# RTB Model Server Go

## 项目简介

RTB Model Server Go 是一个基于 Go 语言开发的实时竞价（Real-Time Bidding）广告模型服务器。该服务器负责处理广告请求，通过多层过滤、预估和决策机制，为广告主提供最优的广告投放策略。系统采用Thrift协议提供高性能的RPC服务，支持广告数据的热更新和实时监控。

## 系统架构

### 核心组件

1. **Model Server** (`internal/model_server/`) - 主服务器，负责接收和处理RTB请求，基于Thrift协议实现
2. **Model Index Manager** (`internal/model_index/`) - 广告索引管理器，协调各个子模块的工作流程
3. **Ad Index Manager** (`internal/model_index/adindex/`) - 广告数据索引管理，支持文件监控和热更新
4. **Filter System** (`internal/model_index/filters/`) - 多层过滤系统，支持各种定向条件和过滤规则
5. **Predict Processor** (`internal/model_index/predict/`) - 预估处理器，通过连接池管理与预估服务的通信，计算点击率和转化率
6. **Bid Decision** (`internal/model_index/predict/`) - 出价决策器，根据预估结果计算最优出价策略
7. **Ads Render** (`internal/model_index/ads_render.go`) - 广告渲染器，生成最终响应数据结构
8. **Context Management** (`internal/context/`) - 请求上下文管理和漏斗统计，贯穿整个请求处理流程
9. **User Profile** (`internal/model_index/user_profile/`) - 用户画像管理，支持IP地址到城市编码的转换
10. **Monitoring** (`internal/promethues/`) - Prometheus监控集成，收集系统运行指标
11. **Logging** (`internal/zaplog/`) - 基于zap的结构化日志管理，支持日志分割和级别控制

### 关键结构体说明

#### ModelIndexManager
```go
// ModelIndexManager 广告索引管理器 - 系统核心协调器
// 负责协调广告索引、过滤、预估、决策和渲染等各个环节
type ModelIndexManager struct {
    config                  *conf.ModelIndexConfig    // 配置信息
    adIndex                 *adindex.AdIndexManager   // 广告索引管理器
    predict                 *predict.PredictProcessor // 预估处理器
    decision                *predict.BidDecision      // 出价决策器
    render                  *AdsRender                // 广告渲染器
    deviceDmpTargetRedisPool *modules.RedisPool       // DMP目标Redis连接池
    frequencyRedisPool       *modules.RedisPool       // 频控Redis连接池
    resourceTargetProcessor  *modules.ResourceTargetProcessor // 资源包目标处理器
    frequencyProcessor       *modules.FrequencyProcessor      // 频控处理器
    funnelProcessor          *modules.FunnelProcessor         // 漏斗处理器
}
```

### FunnelData
```go
// FunnelData 漏斗数据 - 记录请求处理过程中的各个环节信息
// 用于系统监控、调试和性能分析
type FunnelData struct {
    FunnelId      int32           // 命中的漏斗ID
    Date          int32           // 日期
    Hour          int32           // 小时
    RequestId     string          // 请求ID
    SearchId      int64           // 会话ID
    UiName        string          // 用户标识
    Timing        *FunnelTiming   // 时间统计
    ExchangeId    int32           // 交易所ID
    AdxExchangeId int32           // ADX交易所ID
    DmMediaId     int32           // 媒体ID
    AppBundle     string          // 应用包名
    Idfa          string          // iOS广告标识符
    Idfamd5       string          // iOS广告标识符MD5
    Oaidmd5       string          // Android开放广告标识符MD5
    Gaid          string          // Google广告ID
    Platform      int32           // 平台类型
    Os            int32           // 操作系统
    Access        int32           // 接入方式
    City          int32           // 城市
    Carrier       int32           // 运营商
    Request       *FunnelRequest  // 请求信息
    Response      *FunnelResponse // 响应信息
    RuntimeData   *RuntimeData    // 运行时数据
}
```

## 配置说明

系统配置文件位于 `conf/rtb_model_server.yaml`，主要包含以下配置项：

### 服务器配置
```yaml
Server:
  Name: rtb_model_server  # 服务名称
  Port: 3398              # 服务端口
```

### 预测服务器配置
```yaml
PredictServer:
  Addr: "127.0.0.1:3399"  # 预测服务器地址
  ModelName: "default_model"  # 模型名称
  PoolSize: 10        # 连接池最大连接数
  MaxIdleConns: 5     # 最大空闲连接数
  ConnTimeout: 1000   # 连接超时时间(毫秒)
```

### 日志配置
```yaml
LogConfig:
  LogPath: log/rtb_model_sever.log  # 日志文件路径
  LogLevel: debug                   # 日志级别
  MaxAgeDays: 7                     # 日志保留天数
  MaxSize: 100                      # 单个日志文件最大大小(MB)
  MaxBackups: 100                   # 最大备份数量
  Compress: false                   # 是否压缩
  EventPercent: 100                 # 事件日志百分比
```

### 漏斗配置
```yaml
FunnelConfig:
  ConfigFilePath: data/resource/funnel.data  # 设备级别漏斗文件
  Interval: 300                             # 设备级别全量漏斗日志文件刷新时间
  FunnelOpenRatio: 10000                    # 漏斗日志开启比例，单位万分之一
  DataFilePath: data/funnel.data            # 漏斗日志文件路径
```

### 广告索引配置
```yaml
ModelIndex:
  AdIndexPath:                              # 广告索引文件路径
    AdCampaign: data/adindex/ad_campaign.data
    AdStrategy: data/adindex/ad_strategy.data
    AdCreative: data/adindex/ad_creative.data
    AdSponsor: data/adindex/ad_sponsor.data
    AdPromotion: data/adindex/ad_promotion.data
    AdTracking: data/adindex/ad_tracking.data
  ResourceTargetPath: data/resource/resource-target.data  # 资源包目标文件
  IP2RegionDbPath: data/ip2region/ip2region.xdb          # IP地址到区域映射数据库
  DeviceMappingPath: conf/device_mapping.txt             # 设备映射文件
```

### Redis配置
```yaml
DmpTargetRedisPool:                         # DMP目标Redis连接池配置
  Addr: "localhost:6379"                    # Redis地址
  Db: 0                                     # 数据库编号
  AuthInfo: ""                              # 认证信息
  PoolSize: 10                              # 连接池大小
  DialTimeout: 3000                         # 连接超时时间(毫秒)
  ReadTimeout: 3000                         # 读取超时时间(毫秒)
  WriteTimeout: 3000                        # 写入超时时间(毫秒)

FrequencyRedisPool:                         # 频控Redis连接池配置
  Addr: "localhost:6379"                    # Redis地址
  Db: 0                                     # 数据库编号
  AuthInfo: ""                              # 认证信息
  PoolSize: 10                              # 连接池大小
  DialTimeout: 3000                         # 连接超时时间(毫秒)
  ReadTimeout: 3000                         # 读取超时时间(毫秒)
  WriteTimeout: 3000                        # 写入超时时间(毫秒)
```

## 使用指南

### 启动服务

```bash
# 使用默认配置文件启动
$ ./rtb_model_server

# 指定配置文件启动
$ ./rtb_model_server -c conf/custom_config.yaml

# 查看版本信息
$ ./rtb_model_server -v
```

### 测试功能

```bash
# 启动客户端测试模式
$ ./rtb_model_server -client

# 启动预测测试服务器
$ ./rtb_model_server -predict
```

### 构建项目

```bash
# 使用构建脚本
$ ./build.sh
```

## 监控与维护

系统集成了Prometheus监控，可以收集以下指标：

1. 请求处理时间
2. 请求成功/失败率
3. 广告过滤统计
4. 预估服务连接池状态
5. 系统资源使用情况

日志文件位于配置的`LogPath`路径，可以通过调整`LogLevel`来控制日志详细程度。

#### AdIndexManager
```go
// AdIndexManager 广告索引管理器 - 负责广告数据的加载、存储和热更新
// 支持文件监控，当广告数据文件发生变化时自动重新加载
type AdIndexManager struct {
    config            *conf.ModelIndexConfig // 配置信息
    watcher           *fsnotify.Watcher      // 文件监控器
    ctx               context.Context        // 上下文
    cancel            context.CancelFunc     // 取消函数
    fileUpdateTracker map[string]time.Time   // 文件更新追踪器
    mu                sync.RWMutex           // 读写锁
    reloadChan        chan struct{}          // 重载信号通道
}
```

#### RequestContext
```go
// RequestContext 请求上下文 - 贯穿整个请求处理流程的上下文信息
// 包含请求、响应以及当前处理的广告实体信息
type RequestContext struct {
    TimeStart              time.Time                          // 请求开始时间
    AdIndex                *adindex.AdIndexManager            // 广告索引管理器
    BidRequest             *rtb_types.RTBBidRequest           // 广告请求
    BidResponse            *rtb_model_server.RTBModelServerResponse // 广告响应
    CurrentFilterCampaign  *rtb_adinfo_types.RTBCampaign      // 当前过滤的广告计划
    CurrentFilterStrategy  *rtb_adinfo_types.RTBStrategy      // 当前过滤的广告策略
    CurrentFilterCreative  *rtb_adinfo_types.RTBCreative      // 当前过滤的广告创意
    CurrentFilterSponsor   *rtb_adinfo_types.RTBSponsor       // 当前过滤的广告主
    CurrentFilterPromotion *rtb_adinfo_types.RTBPromotion     // 当前过滤的广告推广
    CurrentFilterTracking  *rtb_adinfo_types.AdTrackingInfo   // 当前过滤的追踪信息
    TimeCost               map[string]int64                   // 时间消耗统计
    CreativeFilterMap      map[int32]int32                    // 创意过滤原因映射
    RecallCreatives        []int32                            // 召回的创意ID列表
    DeviceDmpTarget        []int32                            // 设备DMP目标列表
    ResourceTargetWhiteList []int32                           // 资源包白名单
    ResourceTargetBlackList []int32                           // 资源包黑名单
    EnableFunnelLog         bool                              // 是否启用漏斗日志
    ClkFrequencyData        map[string]int                    // 点击频控数据
    ImpFrequencyData        map[string]int                    // 曝光频控数据
}
```

#### PredictResult
```go
// PredictResult 预估结果 - 包含广告创意的各种预估指标
// 用于后续的出价决策计算
type PredictResult struct {
    CreativeId int32   // 创意ID
    PreCtr     int64   // 预估点击率
    PreCvr     int64   // 预估转化率
    PreDeepCvr int64   // 预估深度转化率
}
```

#### BidResult
```go
// BidResult 出价结果 - 包含最终的出价决策信息
// 是整个竞价流程的最终输出
type BidResult struct {
    SearchImpId        int64                    // 搜索展示ID
    CreativeId         int32                    // 创意ID
    FilterType         filters.FilterType       // 过滤类型
    MatchedAdMatchType int64                    // 匹配的广告位类型ID
    PreCtr             int64                    // 预估点击率
    PreAtr             int64                    // 预估广告点击转化率
    WinRate            float64                  // 预估竞胜率
    CtrSource          int32                    // 预估点击率来源
    AtrSource          int32                    // 预估转化率来源
    WinRateSource      int32                    // 预估竞胜率来源
    SelectProb         int64                    // 选择概率
    BidPrice           int64                    // 出价（盟元，百万分之一元）
    BidType            rtb_adinfo_types.BidType // 出价类型：1系统优化出价，2人工出价
    CostType           enums.CostType           // 成本类型
    MediaBidType       int                      // 媒体出价类型：1表示CPM，2表示CPC，4表示CPA
    MediaCostType      int                      // 媒体成本类型：0表示CPM，1表示CPC
}
```

#### FilterProcessor
```go
// FilterProcessor 过滤器处理器 - 管理和执行广告过滤流程
// 支持多种过滤器的组合使用
type FilterProcessor struct {
    filterManager *FilterManager // 过滤器管理器
}
```

## 系统流程图

```mermaid
flowchart TD
    A[RTB请求] --> B[ModelServerHandler.GetAd]
    B --> C[创建RequestContext]
    C --> D[ModelIndexManager.PreFilter]
    
    D --> E[FilterProcessor.ProcessAllAds]
    E --> F[获取所有创意ID]
    F --> G[执行过滤器链]
    
    G --> H{过滤器类型}
    H -->|Creative层级| I[广告位匹配过滤<br/>能力过滤<br/>创意尺寸过滤<br/>视频时长过滤]
    H -->|Strategy层级| J[交易所过滤<br/>已安装应用过滤<br/>资源包过滤<br/>DMP人群过滤<br/>地域/设备过滤]
    H -->|Campaign层级| K[投放时间过滤<br/>预算过滤]
    H -->|流量层级| L[点击频次过滤<br/>展示频次过滤<br/>禁投广告主过滤]
    
    I --> M[过滤结果汇总]
    J --> M
    K --> M
    L --> M
    
    M --> N[ModelIndexManager.Predict]
    N --> O[PredictProcessor.Predict]
    O --> P[生成预估结果<br/>PreCtr, PreCvr, Score]
    
    P --> Q[ModelIndexManager.BidDecision]
    Q --> R[BidDecision.Decision]
    R --> S[计算出价策略]
    
    S --> T{出价类型检查}
    T -->|CPM| U[calculateCpmBid]
    T -->|CPC| V[calculateCpcBid]
    T -->|CPA| W[calculateCpaBid]
    
    U --> X[生成BidResult]
    V --> X
    W --> X
    
    X --> Y[ModelIndexManager.Render]
    Y --> Z[AdsRender.Render]
    Z --> AA[构建RTBModelServerResponse]
    AA --> BB[返回响应]
    
    subgraph "广告索引管理"
        CC[AdIndexManager]
        DD[文件监控]
        EE[数据加载]
        FF[热更新]
        
        DD --> EE
        EE --> FF
        FF --> CC
    end
    
    subgraph "数据存储"
        GG[dictAdCampaign]
        HH[dictAdStrategy]
        II[dictAdCreative]
        JJ[dictAdSponsor]
        KK[dictAdPromotion]
        LL[dictAdTracking]
    end
    
    CC --> GG
    CC --> HH
    CC --> II
    CC --> JJ
    CC --> KK
    CC --> LL
```

## 详细处理流程

### 1. 请求接收阶段
- **入口**: `ModelServerHandler.GetAd()`
- **功能**: 接收RTB请求，创建请求上下文
- **输出**: RequestContext对象

### 2. 预过滤阶段
- **入口**: `ModelIndexManager.PreFilter()`
- **功能**: 执行多层过滤器链，筛选符合条件的广告创意
- **过滤器执行顺序**:
  1. **Creative层级**: 广告位匹配、能力检查、创意尺寸、视频时长
  2. **Strategy层级**: 交易所定向、已安装应用、资源包、DMP人群、地域设备定向
  3. **Campaign层级**: 投放时间、预算检查
  4. **流量层级**: 频次控制、禁投检查
- **输出**: 通过过滤的创意ID列表

### 3. 预估阶段
- **入口**: `ModelIndexManager.Predict()`
- **功能**: 对过滤后的创意进行点击率、转化率等指标预估
- **输出**: 包含预估指标的PredictResult映射

### 4. 出价决策阶段
- **入口**: `ModelIndexManager.BidDecision()`
- **功能**: 根据预估结果和策略配置计算最优出价
- **支持的出价类型**:
  - **CPM**: 按千次展示计费
  - **CPC**: 按点击计费
  - **CPA**: 按转化计费
- **输出**: BidResult对象

### 5. 渲染阶段
- **入口**: `ModelIndexManager.Render()`
- **功能**: 将出价结果渲染为标准的RTB响应格式
- **输出**: RTBModelServerResponse对象

## 广告索引管理

### 数据结构
系统使用sync.Map存储各类广告数据：
- `dictAdCampaign`: 广告计划数据
- `dictAdStrategy`: 广告策略数据
- `dictAdCreative`: 广告创意数据
- `dictAdSponsor`: 广告主数据
- `dictAdPromotion`: 推广数据
- `dictAdTracking`: 追踪数据

### 热更新机制
1. **文件监控**: 使用fsnotify监控广告数据文件变化
2. **批量更新**: 等待所有相关文件更新完成后统一重载
3. **原子操作**: 确保数据更新的一致性
4. **无缝切换**: 更新过程不影响正在处理的请求

## 过滤器系统

### 过滤器类型
系统支持30+种过滤器类型，覆盖广告投放的各个维度：

#### Creative层级过滤器
- `FilterTypeAdMatchTypeTarget`: 广告位匹配过滤
- `FilterTypeAbility`: 能力过滤
- `FilterTypeCreativeDimTarget`: 创意尺寸过滤
- `FilterTypeVideoDurationTarget`: 视频时长过滤

#### Strategy层级过滤器
- `FilterTypeExchangeTarget`: 交易所定向
- `FilterTypeInstalledApps`: 已安装应用过滤
- `FilterTypeResourceTarget`: 资源包过滤
- `FilterTypeDMPTarget`: DMP人群定向
- `FilterTypeCountryTarget`: 国家定向
- `FilterTypePhoneBrandTarget`: 手机品牌定向
- `FilterTypeModelTarget`: 手机型号定向
- `FilterTypePriceTarget`: 手机价格定向
- `FilterTypeOSVersionTarget`: 系统版本定向
- `FilterTypeNetworkTypeTarget`: 网络类型定向
- `FilterTypeCarrierTarget`: 运营商定向
- `FilterTypeGenderTarget`: 性别定向
- `FilterTypeAgeTarget`: 年龄定向
- `FilterTypeInterestTarget`: 兴趣定向

#### Campaign层级过滤器
- `FilterTypeDeliveryTime`: 投放时间过滤
- `FilterTypeBudgetReason`: 预算过滤

#### 流量层级过滤器
- `FilterTypeAdClkFrequency`: 点击频次过滤
- `FilterTypeAdImpFrequency`: 展示频次过滤
- `FilterTypeForbiddenSponsor`: 禁投广告主过滤

### 过滤器架构
- **工厂模式**: 使用FilterFactory创建不同类型的过滤器
- **责任链模式**: 过滤器按顺序执行，任一过滤器失败即停止
- **上下文传递**: 通过RequestContext在过滤器间传递状态

## 配置说明

### 服务器配置
```yaml
server:
  name: "rtb-model-server"
  port: 8080
```

### 日志配置
```yaml
log_config:
  log_level: "info"
  log_path: "/var/log/rtb-model-server.log"
  max_age_days: 7
  max_size: 100
  max_backups: 10
  compress: true
  event_percent: 100
```

### 广告索引配置
```yaml
model_index:
  ad_index_path:
    ad_campaign: "adindex/ad_campaign.data"
    ad_strategy: "adindex/ad_strategy.data"
    ad_creative: "adindex/ad_creative.data"
    ad_sponsor: "adindex/ad_sponsor.data"
    ad_promotion: "adindex/ad_promotion.data"
    ad_tracking: "adindex/ad_tracking.data"
```

## 部署和运行

### 编译
```bash
./build.sh
```

### 运行
```bash
# 使用默认配置
./rtb_model_server

# 指定配置文件
./rtb_model_server -c /path/to/config.yaml

# 查看版本
./rtb_model_server -v

# 客户端测试模式
./rtb_model_server -client

# 预估测试服务器模式
./rtb_model_server -predict
```

### 命令行参数说明
- `-c`: 指定配置文件路径，默认为 `conf/rtb_model_server.yaml`
- `-v`: 打印版本信息并退出
- `-client`: 启动客户端测试模式，用于发送模拟请求
- `-predict`: 启动预估测试服务器，用于测试预估功能

### Docker部署
```bash
# 构建镜像
docker build -t rtb-model-server .

# 运行容器
docker run -p 8080:8080 -v /path/to/config:/app/conf rtb-model-server
```

## 性能特性

1. **高并发**: 基于Go协程，支持高并发请求处理
2. **低延迟**: 内存索引，毫秒级响应
3. **热更新**: 广告数据支持热更新，无需重启服务
4. **容错性**: 完善的错误处理和日志记录
5. **可扩展**: 模块化设计，易于扩展新功能
6. **监控集成**: 支持Prometheus监控指标收集
7. **日志轮转**: 使用Lumberjack实现日志文件自动轮转
8. **上下文管理**: 完整的请求上下文跟踪和漏斗分析
9. **用户画像**: 支持用户画像数据管理和应用
10. **测试友好**: 内置客户端测试和预估测试功能

## 监控和日志

- **结构化日志**: 使用zap日志库，支持JSON格式输出
- **性能监控**: 记录各阶段处理耗时
- **错误追踪**: 详细的错误信息和堆栈跟踪
- **业务指标**: 过滤率、预估准确率、出价成功率等

## 开发指南

### 添加新的过滤器
1. 在`filter_interface.go`中定义新的FilterType
2. 实现对应的过滤器结构体和Filter方法
3. 在`filter_factory.go`中添加创建逻辑
4. 在`GetDefaultFilterChain()`中配置执行顺序

### 扩展预估模型
1. 修改`PredictProcessor.Predict()`方法
2. 集成外部预估服务或机器学习模型
3. 更新`PredictResult`结构体以支持新的预估指标

### 自定义出价策略
1. 在`BidDecision`中添加新的出价计算方法
2. 支持新的成本类型和出价类型
3. 实现动态出价调整逻辑

## 技术栈

- **语言**: Go 1.23+
- **RPC框架**: Apache Thrift
- **日志**: Zap + Lumberjack (日志轮转)
- **配置**: Viper
- **文件监控**: fsnotify
- **并发**: sync.Map, context
- **UUID生成**: Google UUID
- **错误处理**: pkg/errors

## 功能完成状态

### 已完成功能

- [x] 基础服务框架搭建
- [x] Thrift RPC 服务实现
- [x] 广告索引管理器实现
- [x] 文件监控和热更新机制
- [x] 基础过滤器链实现
- [x] 广告位匹配过滤器
- [x] 能力过滤器
- [x] 交易所定向过滤器
- [x] 投放时间过滤器
- [x] 网络类型定向过滤器
- [x] 运营商定向过滤器
- [x] 手机品牌定向过滤器
- [x] 手机型号定向过滤器
- [x] 系统版本定向过滤器
- [x] 预估处理器实现
- [x] 出价决策器实现
- [x] 广告渲染器实现
- [x] 请求上下文管理
- [x] 漏斗统计实现
- [x] IP2Region 城市编码功能
- [x] 日志系统集成
- [x] 监控系统集成
- [x] DMP人群包定向过滤

### 待完成功能

#### 用户定向过滤器 (`internal/model_index/filters/user_filters.go`)
- [ ] **性别定向**: 暂时不支持定向，等新的编码确认后再做支持
- [ ] **年龄定向**: 暂时不支持定向，等新的编码确认后再做支持  
- [ ] **兴趣定向**: 暂时不支持定向，等新的编码确认后再做支持

#### 设备定向过滤器 (`internal/model_index/filters/device_filters.go`)
- [ ] **手机价格定向**: 暂时不支持定向，等新的编码确认后再做支持

#### 其他过滤器
- [x] **已安装应用过滤** (`internal/model_index/filters/install_filter.go`): 需要确认实现细节
- [x] **城市定向过滤** (`internal/model_index/filters/city_filter.go`): 需要实现城市定向过滤逻辑
- [ ] **创意尺寸定向** (`internal/model_index/filters/creative_filters.go`): 需要实现创意尺寸定向过滤逻辑
- [ ] **交易所人群定向** (`internal/model_index/filters/dmp_filter.go`): 推广组里有交易所人群ID无法定向和出价，需要单独实现

#### 出价和预算控制
- [ ] **渲染逻辑** (`internal/model_index/ads_render.go`): 需要完善广告渲染逻辑，涉及不同出价类型返回的价格@利伟
- [ ] **出价排序** (`internal/model_index/predict/bid_decision.go`): 需要对出价进行排序@延玺
- [ ] **预算控制** (`internal/model_index/filters/campaign_filters.go`): 需要对预算进行更精确的控制@延玺

### 优先级说明

**高优先级 (影响核心功能)**:
1. DMP人群定向实现问题
2. 预算控制逻辑完善
3. 出价排序机制

**中优先级 (影响定向精度)**:
1. 用户定向过滤器编码支持
2. 已安装应用过滤逻辑
3. 手机价格定向支持

**低优先级 (功能增强)**:
1. 城市定向过滤
2. 创意尺寸定向过滤

### 解决建议

1. **编码标准化**: 建立统一的定向编码标准，解决各类定向过滤器的编码问题
2. **模块化重构**: 将定向逻辑抽象为通用模块，减少重复代码
3. **测试覆盖**: 为待完善功能添加单元测试和集成测试
4. **文档完善**: 补充各个过滤器的详细实现文档

## 开发计划

### 近期开发计划（1-3个月）

- [ ] 完成DMP人群定向功能实现
- [ ] 优化预算控制逻辑
- [ ] 实现出价排序机制
- [ ] 添加用户定向过滤器的编码支持
- [ ] 实现手机价格定向功能
- [ ] 完善已安装应用过滤逻辑
- [ ] 增加单元测试覆盖率至80%以上

### 长期开发计划（3-6个月）

- [ ] 实现城市定向过滤
- [ ] 实现创意尺寸定向过滤
- [ ] 重构过滤器系统，提高可扩展性
- [ ] 优化预估服务连接池管理
- [ ] 增强监控系统，添加更多业务指标
- [ ] 实现A/B测试框架
- [ ] 支持更多出价策略和算法
- [ ] 完善系统文档和API文档

## IP2Region 城市编码功能

### 功能概述

本项目集成了 [ip2region](https://github.com/lionsoul2014/ip2region) 库，用于根据用户IP地址自动填充城市编码信息。该功能在 `UserProfileProcessor` 中实现，支持高性能的IP地理位置查询。

### 主要特性

- **高性能查询**: 使用内存数据库，查询速度极快（微秒级别）
- **启动时加载**: 服务启动时一次性加载数据库到内存，避免重复IO操作
- **容错处理**: 完善的错误处理机制，IP解析失败时设置默认城市编码
- **城市编码映射**: 支持主要城市的预定义编码，未知城市使用哈希算法生成编码
- **日志记录**: 详细的调试日志，便于问题排查

### 配置说明

#### 1. 配置文件设置

在 `conf/rtb_model_server.yaml` 中添加IP2Region数据库路径配置：

```yaml
ModelIndex:
  # ... 其他配置
  IP2RegionDbPath: data/ip2region/ip2region.xdb
```

#### 2. 数据库文件准备

**方法一：使用脚本自动下载**
```bash
# 执行下载脚本
./scripts/download_ip2region.sh
```

**方法二：手动下载**
```bash
# 创建目录
mkdir -p data/ip2region

# 下载数据库文件
wget -O data/ip2region/ip2region.xdb https://github.com/lionsoul2014/ip2region/raw/master/data/ip2region.xdb
```

### 使用方法

#### 1. 自动初始化

`UserProfileProcessor` 在首次获取实例时会自动初始化IP2Region功能：

```go
// 获取处理器实例（自动初始化）
processor := up.GetUserProfileProcessor()
```

#### 2. 城市编码填充

在处理RTB请求时调用城市编码填充方法：

```go
// 填充城市编码
err := processor.FillCityCodeInfo(req)
if err != nil {
    // 处理错误
}

// 获取填充后的城市编码
cityCode := req.Device.GeoCity
```

### 城市编码映射

#### 预定义城市编码

系统预定义了主要城市的编码映射：

| 城市 | 编码 | 城市 | 编码 |
|------|------|------|------|
| 北京市 | 1 | 天津市 | 11 |
| 上海市 | 2 | 苏州市 | 12 |
| 广州市 | 3 | 长沙市 | 13 |
| 深圳市 | 4 | 郑州市 | 14 |
| 杭州市 | 5 | 青岛市 | 15 |
| 南京市 | 6 | 大连市 | 16 |
| 武汉市 | 7 | 宁波市 | 17 |
| 成都市 | 8 | 厦门市 | 18 |
| 西安市 | 9 | 福州市 | 19 |
| 重庆市 | 10 | 沈阳市 | 20 |

#### 动态编码生成

对于未预定义的城市，系统使用字符串哈希算法生成编码：
- 编码范围：1000-10999
- 算法：城市名称字符串哈希取模
- 确保编码唯一性和一致性

### API 接口

#### UserProfileProcessor 方法

```go
// 获取用户画像处理器实例
func GetUserProfileProcessor() *UserProfileProcessor

// 填充城市编码信息
func (p *UserProfileProcessor) FillCityCodeInfo(req *rtb_types.RTBBidRequest) error

// 初始化IP2Region搜索器（内部方法）
func (p *UserProfileProcessor) initIP2Region() error

// 解析城市编码（内部方法）
func (p *UserProfileProcessor) parseCityCode(region string) int

// 根据城市名称获取编码（内部方法）
func (p *UserProfileProcessor) getCityCodeByName(cityName string) int
```

### 错误处理

系统对各种异常情况进行了完善的处理：

1. **配置缺失**: 数据库路径未配置时记录错误日志
2. **文件不存在**: 数据库文件不存在时记录错误并设置默认编码
3. **IP格式错误**: 无效IP地址时记录警告并设置默认编码
4. **查询失败**: IP查询失败时记录错误并设置默认编码
5. **设备信息缺失**: Device为nil时返回错误

所有错误情况下，城市编码都会被设置为默认值 `0`。

### 性能特性

- **内存查询**: 数据库完全加载到内存，查询速度极快
- **单次加载**: 启动时一次性加载，避免重复IO操作
- **线程安全**: 支持并发查询，无需额外同步
- **低内存占用**: 数据库文件约几MB，内存占用较小

### 测试

项目包含完整的单元测试：

```bash
# 运行用户画像相关测试
go test ./internal/model_index/user_profile/

# 运行特定测试
go test -run TestFillCityCodeInfo ./internal/model_index/user_profile/
go test -run TestParseCityCode ./internal/model_index/user_profile/
```

### 日志示例

```json
{
  "level": "info",
  "ts": "2024-01-01T12:00:00.000Z",
  "msg": "IP2Region initialized successfully",
  "dbPath": "data/ip2region/ip2region.xdb"
}

{
  "level": "debug",
  "ts": "2024-01-01T12:00:01.000Z",
  "msg": "IP region lookup",
  "ip": "***************",
  "region": "中国|0|江苏省|南京市|电信",
  "cityCode": 6
}
```

### 扩展说明

如需扩展城市编码功能，可以：

1. **添加更多预定义城市**: 在 `getCityCodeByName` 方法中添加城市映射
2. **自定义编码算法**: 修改动态编码生成逻辑
3. **支持更多地理信息**: 扩展解析逻辑以支持省份、区域等信息
4. **集成外部服务**: 替换或补充ip2region数据源

## 项目贡献指南

### 贡献流程

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 开发规范

- **代码风格**: 遵循 Go 标准代码风格，使用 `gofmt` 格式化代码
- **提交信息**: 使用清晰的提交信息，格式为 `类型: 描述`，类型包括 feat, fix, docs, style, refactor, test, chore
- **分支管理**: 功能开发使用 feature 分支，修复问题使用 bugfix 分支
- **测试要求**: 新功能必须包含单元测试，测试覆盖率不低于 70%
- **文档更新**: 代码变更需同步更新相关文档

### 任务认领

1. 查看 [功能完成状态](#功能完成状态) 和 [开发计划](#开发计划) 部分
2. 在项目 Issue 中认领未完成的任务
3. 按照优先级顺序进行开发

### 代码审查

所有代码变更都需要经过至少一名核心开发者的审查才能合并到主分支。审查重点包括：

- 代码质量和风格
- 功能完整性
- 测试覆盖率
- 文档完整性
- 性能影响

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。