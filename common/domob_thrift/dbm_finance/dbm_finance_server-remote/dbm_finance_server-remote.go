// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dbm_finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  AgentTransactionResult queryAgentTransaction(RequestHeader header, AgentTransactionSearchParam param, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  SponsorTransactionResult querySponsorTransaction(RequestHeader header, SponsorTransactionSearchParam param, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getAgentAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getSponsorAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  SponsorTransaction changeSponsorBalance(RequestHeader header, i32 agentUid, i32 sponsorId, i64 amount, i64 balance, string note)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dbm_finance.NewDbmFinanceServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryAgentTransaction":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QueryAgentTransaction requires 4 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := dbm_finance.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg36 := flag.Arg(2)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue1 := dbm_finance.NewAgentTransactionSearchParam()
		err41 := argvalue1.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err42 := (strconv.Atoi(flag.Arg(3)))
		if err42 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err43 := (strconv.Atoi(flag.Arg(4)))
		if err43 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QueryAgentTransaction(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "querySponsorTransaction":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QuerySponsorTransaction requires 4 args")
			flag.Usage()
		}
		arg44 := flag.Arg(1)
		mbTrans45 := thrift.NewTMemoryBufferLen(len(arg44))
		defer mbTrans45.Close()
		_, err46 := mbTrans45.WriteString(arg44)
		if err46 != nil {
			Usage()
			return
		}
		factory47 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt48 := factory47.GetProtocol(mbTrans45)
		argvalue0 := dbm_finance.NewRequestHeader()
		err49 := argvalue0.Read(jsProt48)
		if err49 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg50 := flag.Arg(2)
		mbTrans51 := thrift.NewTMemoryBufferLen(len(arg50))
		defer mbTrans51.Close()
		_, err52 := mbTrans51.WriteString(arg50)
		if err52 != nil {
			Usage()
			return
		}
		factory53 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt54 := factory53.GetProtocol(mbTrans51)
		argvalue1 := dbm_finance.NewSponsorTransactionSearchParam()
		err55 := argvalue1.Read(jsProt54)
		if err55 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err56 := (strconv.Atoi(flag.Arg(3)))
		if err56 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err57 := (strconv.Atoi(flag.Arg(4)))
		if err57 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QuerySponsorTransaction(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAgentAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAgentAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg58 := flag.Arg(1)
		mbTrans59 := thrift.NewTMemoryBufferLen(len(arg58))
		defer mbTrans59.Close()
		_, err60 := mbTrans59.WriteString(arg58)
		if err60 != nil {
			Usage()
			return
		}
		factory61 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt62 := factory61.GetProtocol(mbTrans59)
		argvalue0 := dbm_finance.NewRequestHeader()
		err63 := argvalue0.Read(jsProt62)
		if err63 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg64 := flag.Arg(2)
		mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
		defer mbTrans65.Close()
		_, err66 := mbTrans65.WriteString(arg64)
		if err66 != nil {
			Usage()
			return
		}
		factory67 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt68 := factory67.GetProtocol(mbTrans65)
		containerStruct1 := dbm_finance.NewGetAgentAccountsByIdsArgs()
		err69 := containerStruct1.ReadField2(jsProt68)
		if err69 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAgentAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg70 := flag.Arg(1)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		argvalue0 := dbm_finance.NewRequestHeader()
		err75 := argvalue0.Read(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg76 := flag.Arg(2)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		containerStruct1 := dbm_finance.NewGetSponsorAccountsByIdsArgs()
		err81 := containerStruct1.ReadField2(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSponsorAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "changeSponsorBalance":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ChangeSponsorBalance requires 6 args")
			flag.Usage()
		}
		arg82 := flag.Arg(1)
		mbTrans83 := thrift.NewTMemoryBufferLen(len(arg82))
		defer mbTrans83.Close()
		_, err84 := mbTrans83.WriteString(arg82)
		if err84 != nil {
			Usage()
			return
		}
		factory85 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt86 := factory85.GetProtocol(mbTrans83)
		argvalue0 := dbm_finance.NewRequestHeader()
		err87 := argvalue0.Read(jsProt86)
		if err87 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err88 := (strconv.Atoi(flag.Arg(2)))
		if err88 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err89 := (strconv.Atoi(flag.Arg(3)))
		if err89 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err90 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err90 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err91 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err91 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.ChangeSponsorBalance(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
