// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbm_finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dbm_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dbm_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("代理商资金账户操作类型,供查询时使用") *
type AgentTransactionType int64

const (
	AgentTransactionType_ABT_ALL           AgentTransactionType = 0
	AgentTransactionType_ABT_DEPOSIT       AgentTransactionType = 1
	AgentTransactionType_ABT_CHANGE_CREDIT AgentTransactionType = 2
	AgentTransactionType_ABT_WITHDRAW      AgentTransactionType = 3
)

func (p AgentTransactionType) String() string {
	switch p {
	case AgentTransactionType_ABT_ALL:
		return "AgentTransactionType_ABT_ALL"
	case AgentTransactionType_ABT_DEPOSIT:
		return "AgentTransactionType_ABT_DEPOSIT"
	case AgentTransactionType_ABT_CHANGE_CREDIT:
		return "AgentTransactionType_ABT_CHANGE_CREDIT"
	case AgentTransactionType_ABT_WITHDRAW:
		return "AgentTransactionType_ABT_WITHDRAW"
	}
	return "<UNSET>"
}

func AgentTransactionTypeFromString(s string) (AgentTransactionType, error) {
	switch s {
	case "AgentTransactionType_ABT_ALL":
		return AgentTransactionType_ABT_ALL, nil
	case "AgentTransactionType_ABT_DEPOSIT":
		return AgentTransactionType_ABT_DEPOSIT, nil
	case "AgentTransactionType_ABT_CHANGE_CREDIT":
		return AgentTransactionType_ABT_CHANGE_CREDIT, nil
	case "AgentTransactionType_ABT_WITHDRAW":
		return AgentTransactionType_ABT_WITHDRAW, nil
	}
	return AgentTransactionType(math.MinInt32 - 1), fmt.Errorf("not a valid AgentTransactionType string")
}

//@Description("广告主资金账户操作类型,供查询时使用") *
type SponsorTransactionType int64

const (
	SponsorTransactionType_SBT_ALL      SponsorTransactionType = 0
	SponsorTransactionType_SBT_DEPOSIT  SponsorTransactionType = 1
	SponsorTransactionType_SBT_WITHDRAW SponsorTransactionType = 3
)

func (p SponsorTransactionType) String() string {
	switch p {
	case SponsorTransactionType_SBT_ALL:
		return "SponsorTransactionType_SBT_ALL"
	case SponsorTransactionType_SBT_DEPOSIT:
		return "SponsorTransactionType_SBT_DEPOSIT"
	case SponsorTransactionType_SBT_WITHDRAW:
		return "SponsorTransactionType_SBT_WITHDRAW"
	}
	return "<UNSET>"
}

func SponsorTransactionTypeFromString(s string) (SponsorTransactionType, error) {
	switch s {
	case "SponsorTransactionType_SBT_ALL":
		return SponsorTransactionType_SBT_ALL, nil
	case "SponsorTransactionType_SBT_DEPOSIT":
		return SponsorTransactionType_SBT_DEPOSIT, nil
	case "SponsorTransactionType_SBT_WITHDRAW":
		return SponsorTransactionType_SBT_WITHDRAW, nil
	}
	return SponsorTransactionType(math.MinInt32 - 1), fmt.Errorf("not a valid SponsorTransactionType string")
}

type ExceptionCode int64

const (
	ExceptionCode_EC_GENERIC_SYSTEM_ERROR     ExceptionCode = 50000
	ExceptionCode_EC_DATABASE_READ_ERROR      ExceptionCode = 50001
	ExceptionCode_EC_DATABASE_WRITE_ERROR     ExceptionCode = 50002
	ExceptionCode_EC_GENERIC_CLIENT_ERROR     ExceptionCode = 40000
	ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM ExceptionCode = 40001
)

func (p ExceptionCode) String() string {
	switch p {
	case ExceptionCode_EC_GENERIC_SYSTEM_ERROR:
		return "ExceptionCode_EC_GENERIC_SYSTEM_ERROR"
	case ExceptionCode_EC_DATABASE_READ_ERROR:
		return "ExceptionCode_EC_DATABASE_READ_ERROR"
	case ExceptionCode_EC_DATABASE_WRITE_ERROR:
		return "ExceptionCode_EC_DATABASE_WRITE_ERROR"
	case ExceptionCode_EC_GENERIC_CLIENT_ERROR:
		return "ExceptionCode_EC_GENERIC_CLIENT_ERROR"
	case ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM:
		return "ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM"
	}
	return "<UNSET>"
}

func ExceptionCodeFromString(s string) (ExceptionCode, error) {
	switch s {
	case "ExceptionCode_EC_GENERIC_SYSTEM_ERROR":
		return ExceptionCode_EC_GENERIC_SYSTEM_ERROR, nil
	case "ExceptionCode_EC_DATABASE_READ_ERROR":
		return ExceptionCode_EC_DATABASE_READ_ERROR, nil
	case "ExceptionCode_EC_DATABASE_WRITE_ERROR":
		return ExceptionCode_EC_DATABASE_WRITE_ERROR, nil
	case "ExceptionCode_EC_GENERIC_CLIENT_ERROR":
		return ExceptionCode_EC_GENERIC_CLIENT_ERROR, nil
	case "ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM":
		return ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM, nil
	}
	return ExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid ExceptionCode string")
}

type AgentTransaction struct {
	Time           int64                `thrift:"time,1" json:"time"`
	TypeA1         AgentTransactionType `thrift:"type,2" json:"type"`
	Amount         int64                `thrift:"amount,3" json:"amount"`
	OpeningBalance int64                `thrift:"openingBalance,4" json:"openingBalance"`
	ClosingBalance int64                `thrift:"closingBalance,5" json:"closingBalance"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Id       int64 `thrift:"id,10" json:"id"`
	AgentUid int32 `thrift:"agentUid,11" json:"agentUid"`
}

func NewAgentTransaction() *AgentTransaction {
	return &AgentTransaction{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AgentTransaction) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AgentTransaction) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentTransaction) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *AgentTransaction) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = AgentTransactionType(v)
	}
	return nil
}

func (p *AgentTransaction) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *AgentTransaction) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OpeningBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ClosingBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AgentTransaction) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentTransaction) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentTransaction"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentTransaction) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransaction) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingBalance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:openingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningBalance)); err != nil {
		return fmt.Errorf("%T.openingBalance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:openingBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingBalance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:closingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingBalance)); err != nil {
		return fmt.Errorf("%T.closingBalance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:closingBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:id: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentTransaction(%+v)", *p)
}

type AgentTransactionSearchParam struct {
	StartTime int64 `thrift:"startTime,1" json:"startTime"`
	EndTime   int64 `thrift:"endTime,2" json:"endTime"`
	AgentUid  int32 `thrift:"agentUid,3" json:"agentUid"`
	// unused field # 4
	TypeA1 AgentTransactionType `thrift:"type,5" json:"type"`
}

func NewAgentTransactionSearchParam() *AgentTransactionSearchParam {
	return &AgentTransactionSearchParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AgentTransactionSearchParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AgentTransactionSearchParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = AgentTransactionType(v)
	}
	return nil
}

func (p *AgentTransactionSearchParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentTransactionSearchParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransactionSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentTransactionSearchParam(%+v)", *p)
}

type AgentTransactionResult struct {
	TotalCount int32               `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32               `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32               `thrift:"offset,3" json:"offset"`
	Limit      int32               `thrift:"limit,4" json:"limit"`
	Result     []*AgentTransaction `thrift:"result,5" json:"result"`
}

func NewAgentTransactionResult() *AgentTransactionResult {
	return &AgentTransactionResult{}
}

func (p *AgentTransactionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *AgentTransactionResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *AgentTransactionResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AgentTransactionResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AgentTransactionResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*AgentTransaction, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewAgentTransaction()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Result = append(p.Result, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AgentTransactionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentTransactionResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransactionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentTransactionResult(%+v)", *p)
}

type SponsorTransaction struct {
	Time           int64                  `thrift:"time,1" json:"time"`
	TypeA1         SponsorTransactionType `thrift:"type,2" json:"type"`
	Amount         int64                  `thrift:"amount,3" json:"amount"`
	OpeningBalance int64                  `thrift:"openingBalance,4" json:"openingBalance"`
	ClosingBalance int64                  `thrift:"closingBalance,5" json:"closingBalance"`
	Note           string                 `thrift:"note,6" json:"note"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Id        int64 `thrift:"id,10" json:"id"`
	SponsorId int32 `thrift:"sponsorId,11" json:"sponsorId"`
	AgentUid  int32 `thrift:"agentUid,12" json:"agentUid"`
}

func NewSponsorTransaction() *SponsorTransaction {
	return &SponsorTransaction{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SponsorTransaction) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SponsorTransaction) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransaction) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *SponsorTransaction) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = SponsorTransactionType(v)
	}
	return nil
}

func (p *SponsorTransaction) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *SponsorTransaction) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OpeningBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ClosingBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *SponsorTransaction) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SponsorTransaction) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *SponsorTransaction) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorTransaction) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorTransaction"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransaction) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *SponsorTransaction) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingBalance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:openingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningBalance)); err != nil {
		return fmt.Errorf("%T.openingBalance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:openingBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingBalance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:closingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingBalance)); err != nil {
		return fmt.Errorf("%T.closingBalance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:closingBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:note: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:id: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sponsorId: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorTransaction(%+v)", *p)
}

type SponsorTransactionSearchParam struct {
	StartTime int64                  `thrift:"startTime,1" json:"startTime"`
	EndTime   int64                  `thrift:"endTime,2" json:"endTime"`
	AgentUid  int32                  `thrift:"agentUid,3" json:"agentUid"`
	SponsorId int32                  `thrift:"sponsorId,4" json:"sponsorId"`
	TypeA1    SponsorTransactionType `thrift:"type,5" json:"type"`
}

func NewSponsorTransactionSearchParam() *SponsorTransactionSearchParam {
	return &SponsorTransactionSearchParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SponsorTransactionSearchParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SponsorTransactionSearchParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = SponsorTransactionType(v)
	}
	return nil
}

func (p *SponsorTransactionSearchParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorTransactionSearchParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sponsorId: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *SponsorTransactionSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorTransactionSearchParam(%+v)", *p)
}

type SponsorTransactionResult struct {
	TotalCount int32                 `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32                 `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32                 `thrift:"offset,3" json:"offset"`
	Limit      int32                 `thrift:"limit,4" json:"limit"`
	Result     []*SponsorTransaction `thrift:"result,5" json:"result"`
}

func NewSponsorTransactionResult() *SponsorTransactionResult {
	return &SponsorTransactionResult{}
}

func (p *SponsorTransactionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*SponsorTransaction, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewSponsorTransaction()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Result = append(p.Result, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SponsorTransactionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorTransactionResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *SponsorTransactionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorTransactionResult(%+v)", *p)
}

type DbmFinanceServerException struct {
	Code    ExceptionCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewDbmFinanceServerException() *DbmFinanceServerException {
	return &DbmFinanceServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DbmFinanceServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DbmFinanceServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DbmFinanceServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ExceptionCode(v)
	}
	return nil
}

func (p *DbmFinanceServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DbmFinanceServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DbmFinanceServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DbmFinanceServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DbmFinanceServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DbmFinanceServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DbmFinanceServerException(%+v)", *p)
}
