// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package notification_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//通知方式定义
type NotificationDeliveryType int64

const (
	NotificationDeliveryType_NDT_INTERNAL_NOTICE NotificationDeliveryType = 1
	NotificationDeliveryType_NDT_EMAIL           NotificationDeliveryType = 2
	NotificationDeliveryType_NDT_SMS             NotificationDeliveryType = 3
)

func (p NotificationDeliveryType) String() string {
	switch p {
	case NotificationDeliveryType_NDT_INTERNAL_NOTICE:
		return "NotificationDeliveryType_NDT_INTERNAL_NOTICE"
	case NotificationDeliveryType_NDT_EMAIL:
		return "NotificationDeliveryType_NDT_EMAIL"
	case NotificationDeliveryType_NDT_SMS:
		return "NotificationDeliveryType_NDT_SMS"
	}
	return "<UNSET>"
}

func NotificationDeliveryTypeFromString(s string) (NotificationDeliveryType, error) {
	switch s {
	case "NotificationDeliveryType_NDT_INTERNAL_NOTICE":
		return NotificationDeliveryType_NDT_INTERNAL_NOTICE, nil
	case "NotificationDeliveryType_NDT_EMAIL":
		return NotificationDeliveryType_NDT_EMAIL, nil
	case "NotificationDeliveryType_NDT_SMS":
		return NotificationDeliveryType_NDT_SMS, nil
	}
	return NotificationDeliveryType(math.MinInt32 - 1), fmt.Errorf("not a valid NotificationDeliveryType string")
}

//站内通知类型
type NoticeType int64

const (
	NoticeType_NT_UNKNOWN NoticeType = 0
	NoticeType_NT_USER    NoticeType = 1
	NoticeType_NT_AD      NoticeType = 2
	NoticeType_NT_MEDIA   NoticeType = 3
	NoticeType_NT_FINANCE NoticeType = 4
)

func (p NoticeType) String() string {
	switch p {
	case NoticeType_NT_UNKNOWN:
		return "NoticeType_NT_UNKNOWN"
	case NoticeType_NT_USER:
		return "NoticeType_NT_USER"
	case NoticeType_NT_AD:
		return "NoticeType_NT_AD"
	case NoticeType_NT_MEDIA:
		return "NoticeType_NT_MEDIA"
	case NoticeType_NT_FINANCE:
		return "NoticeType_NT_FINANCE"
	}
	return "<UNSET>"
}

func NoticeTypeFromString(s string) (NoticeType, error) {
	switch s {
	case "NoticeType_NT_UNKNOWN":
		return NoticeType_NT_UNKNOWN, nil
	case "NoticeType_NT_USER":
		return NoticeType_NT_USER, nil
	case "NoticeType_NT_AD":
		return NoticeType_NT_AD, nil
	case "NoticeType_NT_MEDIA":
		return NoticeType_NT_MEDIA, nil
	case "NoticeType_NT_FINANCE":
		return NoticeType_NT_FINANCE, nil
	}
	return NoticeType(math.MinInt32 - 1), fmt.Errorf("not a valid NoticeType string")
}

//站内通知状态
type NoticeStatus int64

const (
	NoticeStatus_NS_UNREAD  NoticeStatus = 0
	NoticeStatus_NS_READ    NoticeStatus = 1
	NoticeStatus_NS_DELETED NoticeStatus = 2
)

func (p NoticeStatus) String() string {
	switch p {
	case NoticeStatus_NS_UNREAD:
		return "NoticeStatus_NS_UNREAD"
	case NoticeStatus_NS_READ:
		return "NoticeStatus_NS_READ"
	case NoticeStatus_NS_DELETED:
		return "NoticeStatus_NS_DELETED"
	}
	return "<UNSET>"
}

func NoticeStatusFromString(s string) (NoticeStatus, error) {
	switch s {
	case "NoticeStatus_NS_UNREAD":
		return NoticeStatus_NS_UNREAD, nil
	case "NoticeStatus_NS_READ":
		return NoticeStatus_NS_READ, nil
	case "NoticeStatus_NS_DELETED":
		return NoticeStatus_NS_DELETED, nil
	}
	return NoticeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid NoticeStatus string")
}

//站内广播通知状态
type BroadcastNoticeStatus int64

const (
	BroadcastNoticeStatus_BNS_NORMAL  BroadcastNoticeStatus = 0
	BroadcastNoticeStatus_BNS_DELETED BroadcastNoticeStatus = 1
)

func (p BroadcastNoticeStatus) String() string {
	switch p {
	case BroadcastNoticeStatus_BNS_NORMAL:
		return "BroadcastNoticeStatus_BNS_NORMAL"
	case BroadcastNoticeStatus_BNS_DELETED:
		return "BroadcastNoticeStatus_BNS_DELETED"
	}
	return "<UNSET>"
}

func BroadcastNoticeStatusFromString(s string) (BroadcastNoticeStatus, error) {
	switch s {
	case "BroadcastNoticeStatus_BNS_NORMAL":
		return BroadcastNoticeStatus_BNS_NORMAL, nil
	case "BroadcastNoticeStatus_BNS_DELETED":
		return BroadcastNoticeStatus_BNS_DELETED, nil
	}
	return BroadcastNoticeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid BroadcastNoticeStatus string")
}

type UidInt common.UidInt

type NoticeIdInt common.IdInt

type BroadcastNoticeIdInt common.IdInt

type TimeInt common.TimeInt

type NoticeQueryResult *common.QueryResult

type NoticeQueryInt common.QueryInt

type BroadNoticeQueryResult *common.QueryResult

type BroadNoticeQueryInt common.QueryInt

type RequestHeader *common.RequestHeader

type Notice struct {
	Uid        UidInt       `thrift:"uid,1" json:"uid"`
	NoticeId   NoticeIdInt  `thrift:"noticeId,2" json:"noticeId"`
	Title      string       `thrift:"title,3" json:"title"`
	Body       string       `thrift:"body,4" json:"body"`
	TypeA1     NoticeType   `thrift:"type,5" json:"type"`
	Status     NoticeStatus `thrift:"status,6" json:"status"`
	CreateTime TimeInt      `thrift:"createTime,7" json:"createTime"`
	ReadTime   TimeInt      `thrift:"readTime,8" json:"readTime"`
}

func NewNotice() *Notice {
	return &Notice{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Notice) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Notice) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Notice) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Notice) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *Notice) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.NoticeId = NoticeIdInt(v)
	}
	return nil
}

func (p *Notice) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *Notice) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Body = v
	}
	return nil
}

func (p *Notice) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = NoticeType(v)
	}
	return nil
}

func (p *Notice) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Status = NoticeStatus(v)
	}
	return nil
}

func (p *Notice) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Notice) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ReadTime = TimeInt(v)
	}
	return nil
}

func (p *Notice) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Notice"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Notice) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *Notice) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("noticeId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:noticeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NoticeId)); err != nil {
		return fmt.Errorf("%T.noticeId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:noticeId: %s", p, err)
	}
	return err
}

func (p *Notice) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:title: %s", p, err)
	}
	return err
}

func (p *Notice) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("body", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:body: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Body)); err != nil {
		return fmt.Errorf("%T.body (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:body: %s", p, err)
	}
	return err
}

func (p *Notice) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *Notice) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:status: %s", p, err)
		}
	}
	return err
}

func (p *Notice) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:createTime: %s", p, err)
	}
	return err
}

func (p *Notice) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("readTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:readTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReadTime)); err != nil {
		return fmt.Errorf("%T.readTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:readTime: %s", p, err)
	}
	return err
}

func (p *Notice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Notice(%+v)", *p)
}

type BroadcastNotice struct {
	// unused field # 1
	Id         BroadcastNoticeIdInt  `thrift:"id,2" json:"id"`
	Title      string                `thrift:"title,3" json:"title"`
	Body       string                `thrift:"body,4" json:"body"`
	Status     BroadcastNoticeStatus `thrift:"status,5" json:"status"`
	CreateTime TimeInt               `thrift:"createTime,6" json:"createTime"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	StartTime  TimeInt `thrift:"startTime,20" json:"startTime"`
	ExpireTime TimeInt `thrift:"expireTime,21" json:"expireTime"`
}

func NewBroadcastNotice() *BroadcastNotice {
	return &BroadcastNotice{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BroadcastNotice) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *BroadcastNotice) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BroadcastNotice) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = BroadcastNoticeIdInt(v)
	}
	return nil
}

func (p *BroadcastNotice) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *BroadcastNotice) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Body = v
	}
	return nil
}

func (p *BroadcastNotice) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = BroadcastNoticeStatus(v)
	}
	return nil
}

func (p *BroadcastNotice) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *BroadcastNotice) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *BroadcastNotice) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ExpireTime = TimeInt(v)
	}
	return nil
}

func (p *BroadcastNotice) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BroadcastNotice"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BroadcastNotice) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *BroadcastNotice) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:title: %s", p, err)
	}
	return err
}

func (p *BroadcastNotice) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("body", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:body: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Body)); err != nil {
		return fmt.Errorf("%T.body (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:body: %s", p, err)
	}
	return err
}

func (p *BroadcastNotice) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *BroadcastNotice) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:createTime: %s", p, err)
	}
	return err
}

func (p *BroadcastNotice) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:startTime: %s", p, err)
	}
	return err
}

func (p *BroadcastNotice) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expireTime", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:expireTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExpireTime)); err != nil {
		return fmt.Errorf("%T.expireTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:expireTime: %s", p, err)
	}
	return err
}

func (p *BroadcastNotice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BroadcastNotice(%+v)", *p)
}

type NotificationSetting struct {
	Uid                       UidInt                     `thrift:"uid,1" json:"uid"`
	NotificationDeliveryTypes []NotificationDeliveryType `thrift:"notificationDeliveryTypes,2" json:"notificationDeliveryTypes"`
	Email                     string                     `thrift:"email,3" json:"email"`
	Mobile                    string                     `thrift:"mobile,4" json:"mobile"`
}

func NewNotificationSetting() *NotificationSetting {
	return &NotificationSetting{}
}

func (p *NotificationSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NotificationSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *NotificationSetting) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.NotificationDeliveryTypes = make([]NotificationDeliveryType, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 NotificationDeliveryType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = NotificationDeliveryType(v)
		}
		p.NotificationDeliveryTypes = append(p.NotificationDeliveryTypes, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *NotificationSetting) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *NotificationSetting) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *NotificationSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("NotificationSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NotificationSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *NotificationSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if p.NotificationDeliveryTypes != nil {
		if err := oprot.WriteFieldBegin("notificationDeliveryTypes", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:notificationDeliveryTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.NotificationDeliveryTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.NotificationDeliveryTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:notificationDeliveryTypes: %s", p, err)
		}
	}
	return err
}

func (p *NotificationSetting) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:email: %s", p, err)
	}
	return err
}

func (p *NotificationSetting) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mobile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mobile: %s", p, err)
	}
	return err
}

func (p *NotificationSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NotificationSetting(%+v)", *p)
}
