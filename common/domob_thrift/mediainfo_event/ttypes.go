// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mediainfo_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/mediainfo_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = mediainfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

type UidInt mediainfo_types.UidInt

type MediaIdInt mediainfo_types.MediaIdInt

type PlacementIdInt mediainfo_types.PlacementIdInt

type Media *mediainfo_types.Media

type MediaAppUpdate *mediainfo_types.MediaAppUpdate

type MediaAppRate *mediainfo_types.MediaAppRate

type Placement *mediainfo_types.Placement

type MediaUpdateEvent struct {
	Uid   UidInt                 `thrift:"uid,1" json:"uid"`
	Mid   MediaIdInt             `thrift:"mid,2" json:"mid"`
	Media *mediainfo_types.Media `thrift:"media,3" json:"media"`
}

func NewMediaUpdateEvent() *MediaUpdateEvent {
	return &MediaUpdateEvent{}
}

func (p *MediaUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *MediaUpdateEvent) readField3(iprot thrift.TProtocol) error {
	p.Media = mediainfo_types.NewMedia()
	if err := p.Media.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Media)
	}
	return nil
}

func (p *MediaUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *MediaUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Media != nil {
		if err := oprot.WriteFieldBegin("media", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:media: %s", p, err)
		}
		if err := p.Media.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Media)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:media: %s", p, err)
		}
	}
	return err
}

func (p *MediaUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaUpdateEvent(%+v)", *p)
}

type PlacementUpdateEvent struct {
	Uid       UidInt                     `thrift:"uid,1" json:"uid"`
	Mid       MediaIdInt                 `thrift:"mid,2" json:"mid"`
	Pmid      PlacementIdInt             `thrift:"pmid,3" json:"pmid"`
	Placement *mediainfo_types.Placement `thrift:"placement,4" json:"placement"`
}

func NewPlacementUpdateEvent() *PlacementUpdateEvent {
	return &PlacementUpdateEvent{}
}

func (p *PlacementUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PlacementUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *PlacementUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *PlacementUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pmid = PlacementIdInt(v)
	}
	return nil
}

func (p *PlacementUpdateEvent) readField4(iprot thrift.TProtocol) error {
	p.Placement = mediainfo_types.NewPlacement()
	if err := p.Placement.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Placement)
	}
	return nil
}

func (p *PlacementUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PlacementUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PlacementUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *PlacementUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *PlacementUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pmid: %s", p, err)
	}
	return err
}

func (p *PlacementUpdateEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Placement != nil {
		if err := oprot.WriteFieldBegin("placement", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:placement: %s", p, err)
		}
		if err := p.Placement.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Placement)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:placement: %s", p, err)
		}
	}
	return err
}

func (p *PlacementUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlacementUpdateEvent(%+v)", *p)
}

type MediaAppUpdateEvent struct {
	Uid            UidInt                          `thrift:"uid,1" json:"uid"`
	Mid            MediaIdInt                      `thrift:"mid,2" json:"mid"`
	MediaAppUpdate *mediainfo_types.MediaAppUpdate `thrift:"mediaAppUpdate,3" json:"mediaAppUpdate"`
}

func NewMediaAppUpdateEvent() *MediaAppUpdateEvent {
	return &MediaAppUpdateEvent{}
}

func (p *MediaAppUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaAppUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaAppUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *MediaAppUpdateEvent) readField3(iprot thrift.TProtocol) error {
	p.MediaAppUpdate = mediainfo_types.NewMediaAppUpdate()
	if err := p.MediaAppUpdate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAppUpdate)
	}
	return nil
}

func (p *MediaAppUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaAppUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaAppUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaAppUpdate != nil {
		if err := oprot.WriteFieldBegin("mediaAppUpdate", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaAppUpdate: %s", p, err)
		}
		if err := p.MediaAppUpdate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAppUpdate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaAppUpdate: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaAppUpdateEvent(%+v)", *p)
}

type MediaAppRateEvent struct {
	Uid          UidInt                        `thrift:"uid,1" json:"uid"`
	Mid          MediaIdInt                    `thrift:"mid,2" json:"mid"`
	MediaAppRate *mediainfo_types.MediaAppRate `thrift:"mediaAppRate,3" json:"mediaAppRate"`
}

func NewMediaAppRateEvent() *MediaAppRateEvent {
	return &MediaAppRateEvent{}
}

func (p *MediaAppRateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaAppRateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaAppRateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *MediaAppRateEvent) readField3(iprot thrift.TProtocol) error {
	p.MediaAppRate = mediainfo_types.NewMediaAppRate()
	if err := p.MediaAppRate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAppRate)
	}
	return nil
}

func (p *MediaAppRateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaAppRateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaAppRateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaAppRateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *MediaAppRateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaAppRate != nil {
		if err := oprot.WriteFieldBegin("mediaAppRate", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaAppRate: %s", p, err)
		}
		if err := p.MediaAppRate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAppRate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaAppRate: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppRateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaAppRateEvent(%+v)", *p)
}
