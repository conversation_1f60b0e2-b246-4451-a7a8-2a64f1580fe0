// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbox_stats_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//统计数据时间分组方式
type DateGrouping int64

const (
	DateGrouping_STATS_PER_HOUR  DateGrouping = 0
	DateGrouping_STATS_PER_DAY   DateGrouping = 1
	DateGrouping_STATS_PER_WEEK  DateGrouping = 2
	DateGrouping_STATS_PER_MONTH DateGrouping = 3
	DateGrouping_CUMULATIVE      DateGrouping = 4
)

func (p DateGrouping) String() string {
	switch p {
	case DateGrouping_STATS_PER_HOUR:
		return "DateGrouping_STATS_PER_HOUR"
	case DateGrouping_STATS_PER_DAY:
		return "DateGrouping_STATS_PER_DAY"
	case DateGrouping_STATS_PER_WEEK:
		return "DateGrouping_STATS_PER_WEEK"
	case DateGrouping_STATS_PER_MONTH:
		return "DateGrouping_STATS_PER_MONTH"
	case DateGrouping_CUMULATIVE:
		return "DateGrouping_CUMULATIVE"
	}
	return "<UNSET>"
}

func DateGroupingFromString(s string) (DateGrouping, error) {
	switch s {
	case "DateGrouping_STATS_PER_HOUR":
		return DateGrouping_STATS_PER_HOUR, nil
	case "DateGrouping_STATS_PER_DAY":
		return DateGrouping_STATS_PER_DAY, nil
	case "DateGrouping_STATS_PER_WEEK":
		return DateGrouping_STATS_PER_WEEK, nil
	case "DateGrouping_STATS_PER_MONTH":
		return DateGrouping_STATS_PER_MONTH, nil
	case "DateGrouping_CUMULATIVE":
		return DateGrouping_CUMULATIVE, nil
	}
	return DateGrouping(math.MinInt32 - 1), fmt.Errorf("not a valid DateGrouping string")
}

//广告ID类型
type AderIdType int64

const (
	AderIdType_AD_PLAN_ID     AderIdType = 1
	AderIdType_AD_STRATEGY_ID AderIdType = 2
	AderIdType_AD_CREATIVE_ID AderIdType = 3
)

func (p AderIdType) String() string {
	switch p {
	case AderIdType_AD_PLAN_ID:
		return "AderIdType_AD_PLAN_ID"
	case AderIdType_AD_STRATEGY_ID:
		return "AderIdType_AD_STRATEGY_ID"
	case AderIdType_AD_CREATIVE_ID:
		return "AderIdType_AD_CREATIVE_ID"
	}
	return "<UNSET>"
}

func AderIdTypeFromString(s string) (AderIdType, error) {
	switch s {
	case "AderIdType_AD_PLAN_ID":
		return AderIdType_AD_PLAN_ID, nil
	case "AderIdType_AD_STRATEGY_ID":
		return AderIdType_AD_STRATEGY_ID, nil
	case "AderIdType_AD_CREATIVE_ID":
		return AderIdType_AD_CREATIVE_ID, nil
	}
	return AderIdType(math.MinInt32 - 1), fmt.Errorf("not a valid AderIdType string")
}

//媒体ID类型
type DeverIdType int64

const (
	DeverIdType_MEDIA_ID           DeverIdType = 1
	DeverIdType_MEDIA_PLACEMENT_ID DeverIdType = 2
)

func (p DeverIdType) String() string {
	switch p {
	case DeverIdType_MEDIA_ID:
		return "DeverIdType_MEDIA_ID"
	case DeverIdType_MEDIA_PLACEMENT_ID:
		return "DeverIdType_MEDIA_PLACEMENT_ID"
	}
	return "<UNSET>"
}

func DeverIdTypeFromString(s string) (DeverIdType, error) {
	switch s {
	case "DeverIdType_MEDIA_ID":
		return DeverIdType_MEDIA_ID, nil
	case "DeverIdType_MEDIA_PLACEMENT_ID":
		return DeverIdType_MEDIA_PLACEMENT_ID, nil
	}
	return DeverIdType(math.MinInt32 - 1), fmt.Errorf("not a valid DeverIdType string")
}

//广告统计信息分组方式,用于标识是否按照ID聚合数据
type AderGrouping int64

const (
	AderGrouping_GROUP_BY_ALL         AderGrouping = 0
	AderGrouping_GROUP_BY_AD_PLAN     AderGrouping = 1
	AderGrouping_GROUP_BY_AD_STRATEGY AderGrouping = 2
	AderGrouping_GROUP_BY_AD_CREATIVE AderGrouping = 3
	AderGrouping_GROUP_BY_ID          AderGrouping = 4
)

func (p AderGrouping) String() string {
	switch p {
	case AderGrouping_GROUP_BY_ALL:
		return "AderGrouping_GROUP_BY_ALL"
	case AderGrouping_GROUP_BY_AD_PLAN:
		return "AderGrouping_GROUP_BY_AD_PLAN"
	case AderGrouping_GROUP_BY_AD_STRATEGY:
		return "AderGrouping_GROUP_BY_AD_STRATEGY"
	case AderGrouping_GROUP_BY_AD_CREATIVE:
		return "AderGrouping_GROUP_BY_AD_CREATIVE"
	case AderGrouping_GROUP_BY_ID:
		return "AderGrouping_GROUP_BY_ID"
	}
	return "<UNSET>"
}

func AderGroupingFromString(s string) (AderGrouping, error) {
	switch s {
	case "AderGrouping_GROUP_BY_ALL":
		return AderGrouping_GROUP_BY_ALL, nil
	case "AderGrouping_GROUP_BY_AD_PLAN":
		return AderGrouping_GROUP_BY_AD_PLAN, nil
	case "AderGrouping_GROUP_BY_AD_STRATEGY":
		return AderGrouping_GROUP_BY_AD_STRATEGY, nil
	case "AderGrouping_GROUP_BY_AD_CREATIVE":
		return AderGrouping_GROUP_BY_AD_CREATIVE, nil
	case "AderGrouping_GROUP_BY_ID":
		return AderGrouping_GROUP_BY_ID, nil
	}
	return AderGrouping(math.MinInt32 - 1), fmt.Errorf("not a valid AderGrouping string")
}

type NumberInt int64

type IdInt common.IdInt

type TimeInt common.TimeInt

type Amount common.Amount

type CostType common.CostType

type MediaType common.MediaType

type QueryInt common.QueryInt

type DateParam struct {
	StartDate    TimeInt      `thrift:"startDate,1" json:"startDate"`
	EndDate      TimeInt      `thrift:"endDate,2" json:"endDate"`
	DateGrouping DateGrouping `thrift:"dateGrouping,3" json:"dateGrouping"`
}

func NewDateParam() *DateParam {
	return &DateParam{
		DateGrouping: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DateParam) IsSetDateGrouping() bool {
	return int64(p.DateGrouping) != math.MinInt32-1
}

func (p *DateParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DateParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartDate = TimeInt(v)
	}
	return nil
}

func (p *DateParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndDate = TimeInt(v)
	}
	return nil
}

func (p *DateParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DateGrouping = DateGrouping(v)
	}
	return nil
}

func (p *DateParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DateParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DateParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startDate", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartDate)); err != nil {
		return fmt.Errorf("%T.startDate (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startDate: %s", p, err)
	}
	return err
}

func (p *DateParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endDate", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndDate)); err != nil {
		return fmt.Errorf("%T.endDate (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endDate: %s", p, err)
	}
	return err
}

func (p *DateParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDateGrouping() {
		if err := oprot.WriteFieldBegin("dateGrouping", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:dateGrouping: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DateGrouping)); err != nil {
			return fmt.Errorf("%T.dateGrouping (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:dateGrouping: %s", p, err)
		}
	}
	return err
}

func (p *DateParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DateParam(%+v)", *p)
}

type AderGroupingParam struct {
	AderGrouping AderGrouping `thrift:"aderGrouping,1" json:"aderGrouping"`
	IdList       []IdInt      `thrift:"idList,2" json:"idList"`
	IdType       AderIdType   `thrift:"idType,3" json:"idType"`
}

func NewAderGroupingParam() *AderGroupingParam {
	return &AderGroupingParam{
		AderGrouping: math.MinInt32 - 1, // unset sentinal value

		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AderGroupingParam) IsSetAderGrouping() bool {
	return int64(p.AderGrouping) != math.MinInt32-1
}

func (p *AderGroupingParam) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *AderGroupingParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AderGroupingParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AderGrouping = AderGrouping(v)
	}
	return nil
}

func (p *AderGroupingParam) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = IdInt(v)
		}
		p.IdList = append(p.IdList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AderGroupingParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdType = AderIdType(v)
	}
	return nil
}

func (p *AderGroupingParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AderGroupingParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AderGroupingParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAderGrouping() {
		if err := oprot.WriteFieldBegin("aderGrouping", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:aderGrouping: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AderGrouping)); err != nil {
			return fmt.Errorf("%T.aderGrouping (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:aderGrouping: %s", p, err)
		}
	}
	return err
}

func (p *AderGroupingParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idList: %s", p, err)
		}
	}
	return err
}

func (p *AderGroupingParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idType: %s", p, err)
		}
	}
	return err
}

func (p *AderGroupingParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AderGroupingParam(%+v)", *p)
}

type DeverGroupingParam struct {
	GroupById bool        `thrift:"groupById,1" json:"groupById"`
	IdList    []IdInt     `thrift:"idList,2" json:"idList"`
	IdType    DeverIdType `thrift:"idType,3" json:"idType"`
}

func NewDeverGroupingParam() *DeverGroupingParam {
	return &DeverGroupingParam{
		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeverGroupingParam) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *DeverGroupingParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverGroupingParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.GroupById = v
	}
	return nil
}

func (p *DeverGroupingParam) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = IdInt(v)
		}
		p.IdList = append(p.IdList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeverGroupingParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdType = DeverIdType(v)
	}
	return nil
}

func (p *DeverGroupingParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverGroupingParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverGroupingParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("groupById", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:groupById: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.GroupById)); err != nil {
		return fmt.Errorf("%T.groupById (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:groupById: %s", p, err)
	}
	return err
}

func (p *DeverGroupingParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idList: %s", p, err)
		}
	}
	return err
}

func (p *DeverGroupingParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idType: %s", p, err)
		}
	}
	return err
}

func (p *DeverGroupingParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverGroupingParam(%+v)", *p)
}

type DeverSummaryData struct {
	Total                      NumberInt `thrift:"total,1" json:"total"`
	SummaryEntryClickTotal     NumberInt `thrift:"summaryEntryClickTotal,2" json:"summaryEntryClickTotal"`
	SummaryImpressionTotal     NumberInt `thrift:"summaryImpressionTotal,3" json:"summaryImpressionTotal"`
	SummaryClickTotal          NumberInt `thrift:"summaryClickTotal,4" json:"summaryClickTotal"`
	SummaryEffectiveClickTotal NumberInt `thrift:"summaryEffectiveClickTotal,5" json:"summaryEffectiveClickTotal"`
}

func NewDeverSummaryData() *DeverSummaryData {
	return &DeverSummaryData{}
}

func (p *DeverSummaryData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverSummaryData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Total = NumberInt(v)
	}
	return nil
}

func (p *DeverSummaryData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SummaryEntryClickTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverSummaryData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SummaryImpressionTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverSummaryData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SummaryClickTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverSummaryData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SummaryEffectiveClickTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverSummaryData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverSummaryData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverSummaryData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Total)); err != nil {
		return fmt.Errorf("%T.total (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total: %s", p, err)
	}
	return err
}

func (p *DeverSummaryData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryEntryClickTotal", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:summaryEntryClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryEntryClickTotal)); err != nil {
		return fmt.Errorf("%T.summaryEntryClickTotal (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:summaryEntryClickTotal: %s", p, err)
	}
	return err
}

func (p *DeverSummaryData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryImpressionTotal", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:summaryImpressionTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryImpressionTotal)); err != nil {
		return fmt.Errorf("%T.summaryImpressionTotal (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:summaryImpressionTotal: %s", p, err)
	}
	return err
}

func (p *DeverSummaryData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryClickTotal", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:summaryClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryClickTotal)); err != nil {
		return fmt.Errorf("%T.summaryClickTotal (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:summaryClickTotal: %s", p, err)
	}
	return err
}

func (p *DeverSummaryData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryEffectiveClickTotal", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:summaryEffectiveClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryEffectiveClickTotal)); err != nil {
		return fmt.Errorf("%T.summaryEffectiveClickTotal (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:summaryEffectiveClickTotal: %s", p, err)
	}
	return err
}

func (p *DeverSummaryData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverSummaryData(%+v)", *p)
}

type AderDeverSummaryData struct {
	Total                      NumberInt `thrift:"total,1" json:"total"`
	SummaryImpressionTotal     NumberInt `thrift:"summaryImpressionTotal,2" json:"summaryImpressionTotal"`
	SummaryClickTotal          NumberInt `thrift:"summaryClickTotal,3" json:"summaryClickTotal"`
	SummaryEffectiveClickTotal NumberInt `thrift:"summaryEffectiveClickTotal,4" json:"summaryEffectiveClickTotal"`
}

func NewAderDeverSummaryData() *AderDeverSummaryData {
	return &AderDeverSummaryData{}
}

func (p *AderDeverSummaryData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AderDeverSummaryData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Total = NumberInt(v)
	}
	return nil
}

func (p *AderDeverSummaryData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SummaryImpressionTotal = NumberInt(v)
	}
	return nil
}

func (p *AderDeverSummaryData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SummaryClickTotal = NumberInt(v)
	}
	return nil
}

func (p *AderDeverSummaryData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SummaryEffectiveClickTotal = NumberInt(v)
	}
	return nil
}

func (p *AderDeverSummaryData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AderDeverSummaryData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AderDeverSummaryData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Total)); err != nil {
		return fmt.Errorf("%T.total (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total: %s", p, err)
	}
	return err
}

func (p *AderDeverSummaryData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryImpressionTotal", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:summaryImpressionTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryImpressionTotal)); err != nil {
		return fmt.Errorf("%T.summaryImpressionTotal (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:summaryImpressionTotal: %s", p, err)
	}
	return err
}

func (p *AderDeverSummaryData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryClickTotal", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:summaryClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryClickTotal)); err != nil {
		return fmt.Errorf("%T.summaryClickTotal (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:summaryClickTotal: %s", p, err)
	}
	return err
}

func (p *AderDeverSummaryData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryEffectiveClickTotal", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:summaryEffectiveClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryEffectiveClickTotal)); err != nil {
		return fmt.Errorf("%T.summaryEffectiveClickTotal (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:summaryEffectiveClickTotal: %s", p, err)
	}
	return err
}

func (p *AderDeverSummaryData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AderDeverSummaryData(%+v)", *p)
}

type AderDeverStatsData struct {
	Pid       IdInt      `thrift:"pid,1" json:"pid"`
	Sid       IdInt      `thrift:"sid,2" json:"sid"`
	Cid       IdInt      `thrift:"cid,3" json:"cid"`
	Mid       IdInt      `thrift:"mid,4" json:"mid"`
	DateParam *DateParam `thrift:"dateParam,5" json:"dateParam"`
	Cost      Amount     `thrift:"cost,6" json:"cost"`
	Dt        string     `thrift:"dt,7" json:"dt"`
	MediaType MediaType  `thrift:"mediaType,8" json:"mediaType"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	ImpressionTotal     NumberInt `thrift:"impressionTotal,51" json:"impressionTotal"`
	ImpressionUV        NumberInt `thrift:"impressionUV,52" json:"impressionUV"`
	ClickTotal          NumberInt `thrift:"clickTotal,53" json:"clickTotal"`
	EffectiveClickTotal NumberInt `thrift:"effectiveClickTotal,54" json:"effectiveClickTotal"`
	ClickUV             NumberInt `thrift:"clickUV,55" json:"clickUV"`
}

func NewAderDeverStatsData() *AderDeverStatsData {
	return &AderDeverStatsData{
		MediaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AderDeverStatsData) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *AderDeverStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AderDeverStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pid = IdInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sid = IdInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cid = IdInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mid = IdInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField5(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *AderDeverStatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Cost = Amount(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AderDeverStatsData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.MediaType = MediaType(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.ImpressionTotal = NumberInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.ImpressionUV = NumberInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ClickTotal = NumberInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.EffectiveClickTotal = NumberInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.ClickUV = NumberInt(v)
	}
	return nil
}

func (p *AderDeverStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AderDeverStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AderDeverStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pid: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sid: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cid: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mid: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *AderDeverStatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:cost: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cost)); err != nil {
		return fmt.Errorf("%T.cost (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:cost: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:dt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:dt: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaType", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:mediaType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
		return fmt.Errorf("%T.mediaType (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:mediaType: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionTotal", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:impressionTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionTotal)); err != nil {
		return fmt.Errorf("%T.impressionTotal (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:impressionTotal: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionUV", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:impressionUV: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionUV)); err != nil {
		return fmt.Errorf("%T.impressionUV (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:impressionUV: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTotal", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:clickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickTotal)); err != nil {
		return fmt.Errorf("%T.clickTotal (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:clickTotal: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveClickTotal", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:effectiveClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EffectiveClickTotal)); err != nil {
		return fmt.Errorf("%T.effectiveClickTotal (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:effectiveClickTotal: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickUV", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:clickUV: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickUV)); err != nil {
		return fmt.Errorf("%T.clickUV (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:clickUV: %s", p, err)
	}
	return err
}

func (p *AderDeverStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AderDeverStatsData(%+v)", *p)
}

type DeverStatsData struct {
	Mid       IdInt      `thrift:"mid,1" json:"mid"`
	Pmid      IdInt      `thrift:"pmid,2" json:"pmid"`
	DateParam *DateParam `thrift:"dateParam,3" json:"dateParam"`
	Revenue   Amount     `thrift:"revenue,4" json:"revenue"`
	MediaType MediaType  `thrift:"mediaType,5" json:"mediaType"`
	Dt        string     `thrift:"dt,6" json:"dt"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	EntryClickTotal     NumberInt `thrift:"entryClickTotal,51" json:"entryClickTotal"`
	EntryImpressionUV   NumberInt `thrift:"entryImpressionUV,52" json:"entryImpressionUV"`
	EntrySettledUV      NumberInt `thrift:"entrySettledUV,53" json:"entrySettledUV"`
	ImpressionTotal     NumberInt `thrift:"impressionTotal,54" json:"impressionTotal"`
	ImpressionUV        NumberInt `thrift:"impressionUV,55" json:"impressionUV"`
	ClickTotal          NumberInt `thrift:"clickTotal,56" json:"clickTotal"`
	EffectiveClickTotal NumberInt `thrift:"effectiveClickTotal,57" json:"effectiveClickTotal"`
	ClickUV             NumberInt `thrift:"clickUV,58" json:"clickUV"`
}

func NewDeverStatsData() *DeverStatsData {
	return &DeverStatsData{
		MediaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeverStatsData) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *DeverStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.I64 {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.I64 {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.I64 {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = IdInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pmid = IdInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField3(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *DeverStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Revenue = Amount(v)
	}
	return nil
}

func (p *DeverStatsData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MediaType = MediaType(v)
	}
	return nil
}

func (p *DeverStatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *DeverStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.EntryClickTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.EntryImpressionUV = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.EntrySettledUV = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.ImpressionTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.ImpressionUV = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.ClickTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.EffectiveClickTotal = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.ClickUV = NumberInt(v)
	}
	return nil
}

func (p *DeverStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pmid: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *DeverStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("revenue", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:revenue: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Revenue)); err != nil {
		return fmt.Errorf("%T.revenue (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:revenue: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mediaType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
		return fmt.Errorf("%T.mediaType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mediaType: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:dt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:dt: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entryClickTotal", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:entryClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EntryClickTotal)); err != nil {
		return fmt.Errorf("%T.entryClickTotal (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:entryClickTotal: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entryImpressionUV", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:entryImpressionUV: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EntryImpressionUV)); err != nil {
		return fmt.Errorf("%T.entryImpressionUV (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:entryImpressionUV: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entrySettledUV", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:entrySettledUV: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EntrySettledUV)); err != nil {
		return fmt.Errorf("%T.entrySettledUV (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:entrySettledUV: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionTotal", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:impressionTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionTotal)); err != nil {
		return fmt.Errorf("%T.impressionTotal (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:impressionTotal: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionUV", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:impressionUV: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionUV)); err != nil {
		return fmt.Errorf("%T.impressionUV (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:impressionUV: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTotal", thrift.I64, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:clickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickTotal)); err != nil {
		return fmt.Errorf("%T.clickTotal (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:clickTotal: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveClickTotal", thrift.I64, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:effectiveClickTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EffectiveClickTotal)); err != nil {
		return fmt.Errorf("%T.effectiveClickTotal (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:effectiveClickTotal: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickUV", thrift.I64, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:clickUV: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickUV)); err != nil {
		return fmt.Errorf("%T.clickUV (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:clickUV: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverStatsData(%+v)", *p)
}
