// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_common

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type ZeusServiceCode int64

const (
	ZeusServiceCode_ERROR_PARAM_INVALID     ZeusServiceCode = 401
	ZeusServiceCode_ERROR_SYSTEM_ERROR      ZeusServiceCode = 501
	ZeusServiceCode_ERROR_PERMISSION_DENIED ZeusServiceCode = 601
)

func (p ZeusServiceCode) String() string {
	switch p {
	case ZeusServiceCode_ERROR_PARAM_INVALID:
		return "ZeusServiceCode_ERROR_PARAM_INVALID"
	case ZeusServiceCode_ERROR_SYSTEM_ERROR:
		return "ZeusServiceCode_ERROR_SYSTEM_ERROR"
	case ZeusServiceCode_ERROR_PERMISSION_DENIED:
		return "ZeusServiceCode_ERROR_PERMISSION_DENIED"
	}
	return "<UNSET>"
}

func ZeusServiceCodeFromString(s string) (ZeusServiceCode, error) {
	switch s {
	case "ZeusServiceCode_ERROR_PARAM_INVALID":
		return ZeusServiceCode_ERROR_PARAM_INVALID, nil
	case "ZeusServiceCode_ERROR_SYSTEM_ERROR":
		return ZeusServiceCode_ERROR_SYSTEM_ERROR, nil
	case "ZeusServiceCode_ERROR_PERMISSION_DENIED":
		return ZeusServiceCode_ERROR_PERMISSION_DENIED, nil
	}
	return ZeusServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid ZeusServiceCode string")
}

//RequestHeader 的 operatorUid 字段对应意义，
//暂时应该不会在实际代码中用到，仅用于记录
type ZeusACL int64

const (
	ZeusACL_ACL_UNKNOWN ZeusACL = 0
	ZeusACL_ACL_ADMIN   ZeusACL = 1
	ZeusACL_ACL_USER    ZeusACL = 1000
)

func (p ZeusACL) String() string {
	switch p {
	case ZeusACL_ACL_UNKNOWN:
		return "ZeusACL_ACL_UNKNOWN"
	case ZeusACL_ACL_ADMIN:
		return "ZeusACL_ACL_ADMIN"
	case ZeusACL_ACL_USER:
		return "ZeusACL_ACL_USER"
	}
	return "<UNSET>"
}

func ZeusACLFromString(s string) (ZeusACL, error) {
	switch s {
	case "ZeusACL_ACL_UNKNOWN":
		return ZeusACL_ACL_UNKNOWN, nil
	case "ZeusACL_ACL_ADMIN":
		return ZeusACL_ACL_ADMIN, nil
	case "ZeusACL_ACL_USER":
		return ZeusACL_ACL_USER, nil
	}
	return ZeusACL(math.MinInt32 - 1), fmt.Errorf("not a valid ZeusACL string")
}

type ZeusException struct {
	Code    ZeusServiceCode `thrift:"code,1" json:"code"`
	Message string          `thrift:"message,2" json:"message"`
}

func NewZeusException() *ZeusException {
	return &ZeusException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ZeusException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *ZeusException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ZeusServiceCode(v)
	}
	return nil
}

func (p *ZeusException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *ZeusException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *ZeusException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *ZeusException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusException(%+v)", *p)
}

type Condition struct {
	Name  string `thrift:"name,1" json:"name"`
	Opt   string `thrift:"opt,2" json:"opt"`
	Value int32  `thrift:"value,3" json:"value"`
}

func NewCondition() *Condition {
	return &Condition{}
}

func (p *Condition) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Condition) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Condition) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Opt = v
	}
	return nil
}

func (p *Condition) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *Condition) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Condition"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Condition) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Condition) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("opt", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:opt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Opt)); err != nil {
		return fmt.Errorf("%T.opt (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:opt: %s", p, err)
	}
	return err
}

func (p *Condition) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("value", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:value: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Value)); err != nil {
		return fmt.Errorf("%T.value (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:value: %s", p, err)
	}
	return err
}

func (p *Condition) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Condition(%+v)", *p)
}
