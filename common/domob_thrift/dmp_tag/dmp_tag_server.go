// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dmp_tag

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__

type DmpTagServer interface {
	dm303.DomobService

	GetTag() (r []*CrowdTag, exp *DmpTagServerException, err error)
	GetTagObject() (r *TagObject, exp *DmpTagServerException, err error)
}

type DmpTagServerClient struct {
	*dm303.DomobServiceClient
}

func NewDmpTagServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DmpTagServerClient {
	return &DmpTagServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDmpTagServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DmpTagServerClient {
	return &DmpTagServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

func (p *DmpTagServerClient) GetTag() (r []*CrowdTag, exp *DmpTagServerException, err error) {
	if err = p.sendGetTag(); err != nil {
		return
	}
	return p.recvGetTag()
}

func (p *DmpTagServerClient) sendGetTag() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_tag", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1 := NewGetTagArgs()
	if err = args1.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DmpTagServerClient) recvGetTag() (value []*CrowdTag, exp *DmpTagServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error3 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error4 error
		error4, err = error3.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error4
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result2 := NewGetTagResult()
	if err = result2.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result2.Success
	if result2.Exp != nil {
		exp = result2.Exp
	}
	return
}

func (p *DmpTagServerClient) GetTagObject() (r *TagObject, exp *DmpTagServerException, err error) {
	if err = p.sendGetTagObject(); err != nil {
		return
	}
	return p.recvGetTagObject()
}

func (p *DmpTagServerClient) sendGetTagObject() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_tag_object", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args5 := NewGetTagObjectArgs()
	if err = args5.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DmpTagServerClient) recvGetTagObject() (value *TagObject, exp *DmpTagServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error7 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error8 error
		error8, err = error7.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error8
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result6 := NewGetTagObjectResult()
	if err = result6.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result6.Success
	if result6.Exp != nil {
		exp = result6.Exp
	}
	return
}

type DmpTagServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDmpTagServerProcessor(handler DmpTagServer) *DmpTagServerProcessor {
	self9 := &DmpTagServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self9.AddToProcessorMap("get_tag", &dmpTagServerProcessorGetTag{handler: handler})
	self9.AddToProcessorMap("get_tag_object", &dmpTagServerProcessorGetTagObject{handler: handler})
	return self9
}

type dmpTagServerProcessorGetTag struct {
	handler DmpTagServer
}

func (p *dmpTagServerProcessorGetTag) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTagArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_tag", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTagResult()
	if result.Success, result.Exp, err = p.handler.GetTag(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_tag: "+err.Error())
		oprot.WriteMessageBegin("get_tag", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_tag", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dmpTagServerProcessorGetTagObject struct {
	handler DmpTagServer
}

func (p *dmpTagServerProcessorGetTagObject) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTagObjectArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_tag_object", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTagObjectResult()
	if result.Success, result.Exp, err = p.handler.GetTagObject(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_tag_object: "+err.Error())
		oprot.WriteMessageBegin("get_tag_object", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_tag_object", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetTagArgs struct {
}

func NewGetTagArgs() *GetTagArgs {
	return &GetTagArgs{}
}

func (p *GetTagArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTagArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_tag_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTagArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTagArgs(%+v)", *p)
}

type GetTagResult struct {
	Success []*CrowdTag            `thrift:"success,0" json:"success"`
	Exp     *DmpTagServerException `thrift:"exp,1" json:"exp"`
}

func NewGetTagResult() *GetTagResult {
	return &GetTagResult{}
}

func (p *GetTagResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTagResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*CrowdTag, 0, size)
	for i := 0; i < size; i++ {
		_elem10 := NewCrowdTag()
		if err := _elem10.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem10)
		}
		p.Success = append(p.Success, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTagResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = NewDmpTagServerException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetTagResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_tag_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTagResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTagResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetTagResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTagResult(%+v)", *p)
}

type GetTagObjectArgs struct {
}

func NewGetTagObjectArgs() *GetTagObjectArgs {
	return &GetTagObjectArgs{}
}

func (p *GetTagObjectArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTagObjectArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_tag_object_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTagObjectArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTagObjectArgs(%+v)", *p)
}

type GetTagObjectResult struct {
	Success *TagObject             `thrift:"success,0" json:"success"`
	Exp     *DmpTagServerException `thrift:"exp,1" json:"exp"`
}

func NewGetTagObjectResult() *GetTagObjectResult {
	return &GetTagObjectResult{}
}

func (p *GetTagObjectResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTagObjectResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewTagObject()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTagObjectResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = NewDmpTagServerException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetTagObjectResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_tag_object_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTagObjectResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTagObjectResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetTagObjectResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTagObjectResult(%+v)", *p)
}
