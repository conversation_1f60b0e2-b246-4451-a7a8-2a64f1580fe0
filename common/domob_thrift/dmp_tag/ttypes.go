// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dmp_tag

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type DmpTagErrCode int64

const (
	DmpTagErrCode_SUCC        DmpTagErrCode = 0
	DmpTagErrCode_UNKNOWN_ERR DmpTagErrCode = 1
)

func (p DmpTagErrCode) String() string {
	switch p {
	case DmpTagErrCode_SUCC:
		return "DmpTagErrCode_SUCC"
	case DmpTagErrCode_UNKNOWN_ERR:
		return "DmpTagErrCode_UNKNOWN_ERR"
	}
	return "<UNSET>"
}

func DmpTagErrCodeFromString(s string) (DmpTagErrCode, error) {
	switch s {
	case "DmpTagErrCode_SUCC":
		return DmpTagErrCode_SUCC, nil
	case "DmpTagErrCode_UNKNOWN_ERR":
		return DmpTagErrCode_UNKNOWN_ERR, nil
	}
	return DmpTagErrCode(math.MinInt32 - 1), fmt.Errorf("not a valid DmpTagErrCode string")
}

type CrowdTag struct {
	TagId      int64  `thrift:"tag_id,1" json:"tag_id"`
	TagName    string `thrift:"tag_name,2" json:"tag_name"`
	ParentId   int32  `thrift:"parent_id,3" json:"parent_id"`
	ParentName string `thrift:"parent_name,4" json:"parent_name"`
	TagInfo    string `thrift:"tag_info,5" json:"tag_info"`
}

func NewCrowdTag() *CrowdTag {
	return &CrowdTag{}
}

func (p *CrowdTag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdTag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = v
	}
	return nil
}

func (p *CrowdTag) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TagName = v
	}
	return nil
}

func (p *CrowdTag) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ParentId = v
	}
	return nil
}

func (p *CrowdTag) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ParentName = v
	}
	return nil
}

func (p *CrowdTag) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TagInfo = v
	}
	return nil
}

func (p *CrowdTag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdTag"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tag_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TagId)); err != nil {
		return fmt.Errorf("%T.tag_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tag_id: %s", p, err)
	}
	return err
}

func (p *CrowdTag) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tag_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagName)); err != nil {
		return fmt.Errorf("%T.tag_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tag_name: %s", p, err)
	}
	return err
}

func (p *CrowdTag) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:parent_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ParentId)); err != nil {
		return fmt.Errorf("%T.parent_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:parent_id: %s", p, err)
	}
	return err
}

func (p *CrowdTag) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:parent_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ParentName)); err != nil {
		return fmt.Errorf("%T.parent_name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:parent_name: %s", p, err)
	}
	return err
}

func (p *CrowdTag) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_info", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:tag_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagInfo)); err != nil {
		return fmt.Errorf("%T.tag_info (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:tag_info: %s", p, err)
	}
	return err
}

func (p *CrowdTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdTag(%+v)", *p)
}

type TagObject struct {
	TagId    int64    `thrift:"tag_id,1" json:"tag_id"`
	TagName  string   `thrift:"tag_name,2" json:"tag_name"`
	TotalLen int32    `thrift:"total_len,3" json:"total_len"`
	Offset   int32    `thrift:"offset,4" json:"offset"`
	TagValue []string `thrift:"tag_value,5" json:"tag_value"`
}

func NewTagObject() *TagObject {
	return &TagObject{}
}

func (p *TagObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TagObject) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = v
	}
	return nil
}

func (p *TagObject) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TagName = v
	}
	return nil
}

func (p *TagObject) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalLen = v
	}
	return nil
}

func (p *TagObject) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *TagObject) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TagValue = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.TagValue = append(p.TagValue, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TagObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TagObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TagObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tag_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TagId)); err != nil {
		return fmt.Errorf("%T.tag_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tag_id: %s", p, err)
	}
	return err
}

func (p *TagObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tag_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagName)); err != nil {
		return fmt.Errorf("%T.tag_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tag_name: %s", p, err)
	}
	return err
}

func (p *TagObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_len", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:total_len: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalLen)); err != nil {
		return fmt.Errorf("%T.total_len (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:total_len: %s", p, err)
	}
	return err
}

func (p *TagObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *TagObject) writeField5(oprot thrift.TProtocol) (err error) {
	if p.TagValue != nil {
		if err := oprot.WriteFieldBegin("tag_value", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:tag_value: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TagValue)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TagValue {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:tag_value: %s", p, err)
		}
	}
	return err
}

func (p *TagObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TagObject(%+v)", *p)
}

type DmpTagServerException struct {
	Code    DmpTagErrCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewDmpTagServerException() *DmpTagServerException {
	return &DmpTagServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DmpTagServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DmpTagServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpTagServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DmpTagErrCode(v)
	}
	return nil
}

func (p *DmpTagServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DmpTagServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpTagServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpTagServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DmpTagServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DmpTagServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpTagServerException(%+v)", *p)
}
