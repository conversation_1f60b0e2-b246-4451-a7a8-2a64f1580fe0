// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_adexchange_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

const DSP_UNKNOWN = 0
const DSP_DOMOB = 1
const DSP_ZHONGMENG = 10001
const DSP_JINGDONG = 10002
const DSP_ZHIZIYUN = 10003
const DSP_MUGUA = 10004
const DSP_ALI_LVCHENG = 10005
const DSP_ALI_SHULAN = 10006
const DSP_TOUTIAO_UNION = 10007
const DSP_NETEAST = 10008
const DSP_YEAHMOBI = 10009
const DSP_ZHUOTA = 10010
const DSP_FANWEI = 10011

func init() {
}
