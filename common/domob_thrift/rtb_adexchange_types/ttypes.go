// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_adexchange_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//}}} end of DSP编号常量定义
type DSPResponseCode int64

const (
	DSPResponseCode_DSP_RESP_UNKNOWN   DSPResponseCode = 0
	DSPResponseCode_DSP_RESP_ERROR     DSPResponseCode = 1
	DSPResponseCode_DSP_RESP_TIMEOUT   DSPResponseCode = 2
	DSPResponseCode_DSP_RESP_BID       DSPResponseCode = 3
	DSPResponseCode_DSP_RESP_NOBID     DSPResponseCode = 4
	DSPResponseCode_DSP_RESP_PT_FILTER DSPResponseCode = 5
)

func (p DSPResponseCode) String() string {
	switch p {
	case DSPResponseCode_DSP_RESP_UNKNOWN:
		return "DSPResponseCode_DSP_RESP_UNKNOWN"
	case DSPResponseCode_DSP_RESP_ERROR:
		return "DSPResponseCode_DSP_RESP_ERROR"
	case DSPResponseCode_DSP_RESP_TIMEOUT:
		return "DSPResponseCode_DSP_RESP_TIMEOUT"
	case DSPResponseCode_DSP_RESP_BID:
		return "DSPResponseCode_DSP_RESP_BID"
	case DSPResponseCode_DSP_RESP_NOBID:
		return "DSPResponseCode_DSP_RESP_NOBID"
	case DSPResponseCode_DSP_RESP_PT_FILTER:
		return "DSPResponseCode_DSP_RESP_PT_FILTER"
	}
	return "<UNSET>"
}

func DSPResponseCodeFromString(s string) (DSPResponseCode, error) {
	switch s {
	case "DSPResponseCode_DSP_RESP_UNKNOWN":
		return DSPResponseCode_DSP_RESP_UNKNOWN, nil
	case "DSPResponseCode_DSP_RESP_ERROR":
		return DSPResponseCode_DSP_RESP_ERROR, nil
	case "DSPResponseCode_DSP_RESP_TIMEOUT":
		return DSPResponseCode_DSP_RESP_TIMEOUT, nil
	case "DSPResponseCode_DSP_RESP_BID":
		return DSPResponseCode_DSP_RESP_BID, nil
	case "DSPResponseCode_DSP_RESP_NOBID":
		return DSPResponseCode_DSP_RESP_NOBID, nil
	case "DSPResponseCode_DSP_RESP_PT_FILTER":
		return DSPResponseCode_DSP_RESP_PT_FILTER, nil
	}
	return DSPResponseCode(math.MinInt32 - 1), fmt.Errorf("not a valid DSPResponseCode string")
}

type DSPSenderEventType int64

const (
	DSPSenderEventType_DSP_SENDER_EVENT_UNKNOWN      DSPSenderEventType = 0
	DSPSenderEventType_DSP_SENDER_EVENT_BILLING      DSPSenderEventType = 1
	DSPSenderEventType_DSP_SENDER_EVENT_IMP          DSPSenderEventType = 2
	DSPSenderEventType_DSP_SENDER_EVENT_CLK          DSPSenderEventType = 3
	DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_START  DSPSenderEventType = 4
	DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_FINISH DSPSenderEventType = 5
)

func (p DSPSenderEventType) String() string {
	switch p {
	case DSPSenderEventType_DSP_SENDER_EVENT_UNKNOWN:
		return "DSPSenderEventType_DSP_SENDER_EVENT_UNKNOWN"
	case DSPSenderEventType_DSP_SENDER_EVENT_BILLING:
		return "DSPSenderEventType_DSP_SENDER_EVENT_BILLING"
	case DSPSenderEventType_DSP_SENDER_EVENT_IMP:
		return "DSPSenderEventType_DSP_SENDER_EVENT_IMP"
	case DSPSenderEventType_DSP_SENDER_EVENT_CLK:
		return "DSPSenderEventType_DSP_SENDER_EVENT_CLK"
	case DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_START:
		return "DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_START"
	case DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_FINISH:
		return "DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_FINISH"
	}
	return "<UNSET>"
}

func DSPSenderEventTypeFromString(s string) (DSPSenderEventType, error) {
	switch s {
	case "DSPSenderEventType_DSP_SENDER_EVENT_UNKNOWN":
		return DSPSenderEventType_DSP_SENDER_EVENT_UNKNOWN, nil
	case "DSPSenderEventType_DSP_SENDER_EVENT_BILLING":
		return DSPSenderEventType_DSP_SENDER_EVENT_BILLING, nil
	case "DSPSenderEventType_DSP_SENDER_EVENT_IMP":
		return DSPSenderEventType_DSP_SENDER_EVENT_IMP, nil
	case "DSPSenderEventType_DSP_SENDER_EVENT_CLK":
		return DSPSenderEventType_DSP_SENDER_EVENT_CLK, nil
	case "DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_START":
		return DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_START, nil
	case "DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_FINISH":
		return DSPSenderEventType_DSP_SENDER_EVENT_VIDEO_FINISH, nil
	}
	return DSPSenderEventType(math.MinInt32 - 1), fmt.Errorf("not a valid DSPSenderEventType string")
}

type Adslot struct {
	Width     int32   `thrift:"width,1" json:"width"`
	Height    int32   `thrift:"height,2" json:"height"`
	Templates []int32 `thrift:"templates,3" json:"templates"`
}

func NewAdslot() *Adslot {
	return &Adslot{}
}

func (p *Adslot) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Adslot) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *Adslot) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *Adslot) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Templates = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Templates = append(p.Templates, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Adslot) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Adslot"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Adslot) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:width: %s", p, err)
	}
	return err
}

func (p *Adslot) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:height: %s", p, err)
	}
	return err
}

func (p *Adslot) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Templates != nil {
		if err := oprot.WriteFieldBegin("templates", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:templates: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Templates)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Templates {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:templates: %s", p, err)
		}
	}
	return err
}

func (p *Adslot) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Adslot(%+v)", *p)
}

type Imp struct {
	Id       string    `thrift:"id,1" json:"id"`
	Adslots  []*Adslot `thrift:"adslots,2" json:"adslots"`
	Bidfloor int64     `thrift:"bidfloor,3" json:"bidfloor"`
}

func NewImp() *Imp {
	return &Imp{}
}

func (p *Imp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Imp) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Imp) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Adslots = make([]*Adslot, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewAdslot()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Adslots = append(p.Adslots, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Imp) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Bidfloor = v
	}
	return nil
}

func (p *Imp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Imp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Imp) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Imp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Adslots != nil {
		if err := oprot.WriteFieldBegin("adslots", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:adslots: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Adslots)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Adslots {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:adslots: %s", p, err)
		}
	}
	return err
}

func (p *Imp) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bidfloor", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:bidfloor: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bidfloor)); err != nil {
		return fmt.Errorf("%T.bidfloor (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:bidfloor: %s", p, err)
	}
	return err
}

func (p *Imp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Imp(%+v)", *p)
}

type App struct {
	Id     string `thrift:"id,1" json:"id"`
	Name   string `thrift:"name,2" json:"name"`
	Bundle string `thrift:"bundle,3" json:"bundle"`
	Ver    string `thrift:"ver,4" json:"ver"`
}

func NewApp() *App {
	return &App{}
}

func (p *App) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *App) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *App) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *App) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Bundle = v
	}
	return nil
}

func (p *App) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Ver = v
	}
	return nil
}

func (p *App) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("App"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *App) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *App) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *App) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bundle", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:bundle: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Bundle)); err != nil {
		return fmt.Errorf("%T.bundle (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:bundle: %s", p, err)
	}
	return err
}

func (p *App) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ver", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ver: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ver)); err != nil {
		return fmt.Errorf("%T.ver (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ver: %s", p, err)
	}
	return err
}

func (p *App) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("App(%+v)", *p)
}

type Geo struct {
	Lon float64 `thrift:"lon,1" json:"lon"`
	Lat float64 `thrift:"lat,2" json:"lat"`
}

func NewGeo() *Geo {
	return &Geo{}
}

func (p *Geo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Geo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Lon = v
	}
	return nil
}

func (p *Geo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Lat = v
	}
	return nil
}

func (p *Geo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Geo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Geo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lon", thrift.DOUBLE, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:lon: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Lon)); err != nil {
		return fmt.Errorf("%T.lon (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:lon: %s", p, err)
	}
	return err
}

func (p *Geo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lat", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:lat: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Lat)); err != nil {
		return fmt.Errorf("%T.lat (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:lat: %s", p, err)
	}
	return err
}

func (p *Geo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Geo(%+v)", *p)
}

type Device struct {
	Ua           string `thrift:"ua,1" json:"ua"`
	Geo          *Geo   `thrift:"geo,2" json:"geo"`
	Ip           string `thrift:"ip,3" json:"ip"`
	Make         string `thrift:"make,4" json:"make"`
	Model        string `thrift:"model,5" json:"model"`
	Os           string `thrift:"os,6" json:"os"`
	Osv          string `thrift:"osv,7" json:"osv"`
	Carrier      int32  `thrift:"carrier,8" json:"carrier"`
	Network      int32  `thrift:"network,9" json:"network"`
	Mac          string `thrift:"mac,10" json:"mac"`
	Idfa         string `thrift:"idfa,11" json:"idfa"`
	Idfamd5      string `thrift:"idfamd5,12" json:"idfamd5"`
	Imei         string `thrift:"imei,13" json:"imei"`
	Imeimd5      string `thrift:"imeimd5,14" json:"imeimd5"`
	Androidid    string `thrift:"androidid,15" json:"androidid"`
	Androididmd5 string `thrift:"androididmd5,16" json:"androididmd5"`
}

func NewDevice() *Device {
	return &Device{}
}

func (p *Device) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Device) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Ua = v
	}
	return nil
}

func (p *Device) readField2(iprot thrift.TProtocol) error {
	p.Geo = NewGeo()
	if err := p.Geo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Geo)
	}
	return nil
}

func (p *Device) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *Device) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Make = v
	}
	return nil
}

func (p *Device) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Model = v
	}
	return nil
}

func (p *Device) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Os = v
	}
	return nil
}

func (p *Device) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Osv = v
	}
	return nil
}

func (p *Device) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *Device) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *Device) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *Device) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *Device) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Idfamd5 = v
	}
	return nil
}

func (p *Device) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *Device) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Imeimd5 = v
	}
	return nil
}

func (p *Device) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Androidid = v
	}
	return nil
}

func (p *Device) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Androididmd5 = v
	}
	return nil
}

func (p *Device) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Device"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Device) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ua", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ua)); err != nil {
		return fmt.Errorf("%T.ua (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ua: %s", p, err)
	}
	return err
}

func (p *Device) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Geo != nil {
		if err := oprot.WriteFieldBegin("geo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:geo: %s", p, err)
		}
		if err := p.Geo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Geo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:geo: %s", p, err)
		}
	}
	return err
}

func (p *Device) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ip: %s", p, err)
	}
	return err
}

func (p *Device) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("make", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:make: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Make)); err != nil {
		return fmt.Errorf("%T.make (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:make: %s", p, err)
	}
	return err
}

func (p *Device) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Model)); err != nil {
		return fmt.Errorf("%T.model (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:model: %s", p, err)
	}
	return err
}

func (p *Device) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:os: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Os)); err != nil {
		return fmt.Errorf("%T.os (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:os: %s", p, err)
	}
	return err
}

func (p *Device) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("osv", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:osv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Osv)); err != nil {
		return fmt.Errorf("%T.osv (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:osv: %s", p, err)
	}
	return err
}

func (p *Device) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:carrier: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:carrier: %s", p, err)
	}
	return err
}

func (p *Device) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:network: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Network)); err != nil {
		return fmt.Errorf("%T.network (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:network: %s", p, err)
	}
	return err
}

func (p *Device) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:mac: %s", p, err)
	}
	return err
}

func (p *Device) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:idfa: %s", p, err)
	}
	return err
}

func (p *Device) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfamd5", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:idfamd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfamd5)); err != nil {
		return fmt.Errorf("%T.idfamd5 (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:idfamd5: %s", p, err)
	}
	return err
}

func (p *Device) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:imei: %s", p, err)
	}
	return err
}

func (p *Device) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imeimd5", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:imeimd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imeimd5)); err != nil {
		return fmt.Errorf("%T.imeimd5 (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:imeimd5: %s", p, err)
	}
	return err
}

func (p *Device) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("androidid", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:androidid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Androidid)); err != nil {
		return fmt.Errorf("%T.androidid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:androidid: %s", p, err)
	}
	return err
}

func (p *Device) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("androididmd5", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:androididmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Androididmd5)); err != nil {
		return fmt.Errorf("%T.androididmd5 (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:androididmd5: %s", p, err)
	}
	return err
}

func (p *Device) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Device(%+v)", *p)
}

type User struct {
	Id     string `thrift:"id,1" json:"id"`
	Gender string `thrift:"gender,2" json:"gender"`
	Yob    int32  `thrift:"yob,3" json:"yob"`
}

func NewUser() *User {
	return &User{}
}

func (p *User) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *User) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *User) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Gender = v
	}
	return nil
}

func (p *User) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Yob = v
	}
	return nil
}

func (p *User) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("User"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *User) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *User) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gender", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:gender: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Gender)); err != nil {
		return fmt.Errorf("%T.gender (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:gender: %s", p, err)
	}
	return err
}

func (p *User) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("yob", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:yob: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Yob)); err != nil {
		return fmt.Errorf("%T.yob (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:yob: %s", p, err)
	}
	return err
}

func (p *User) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("User(%+v)", *p)
}

type DSPBidRequest struct {
	Id         string  `thrift:"id,1" json:"id"`
	Imp        []*Imp  `thrift:"imp,2" json:"imp"`
	App        *App    `thrift:"app,3" json:"app"`
	Device     *Device `thrift:"device,4" json:"device"`
	User       *User   `thrift:"user,5" json:"user"`
	At         int32   `thrift:"at,6" json:"at"`
	Secure     int32   `thrift:"secure,7" json:"secure"`
	ExchangeId int32   `thrift:"exchange_id,8" json:"exchange_id"`
	ReqTs      int32   `thrift:"req_ts,9" json:"req_ts"`
}

func NewDSPBidRequest() *DSPBidRequest {
	return &DSPBidRequest{}
}

func (p *DSPBidRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPBidRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DSPBidRequest) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Imp = make([]*Imp, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewImp()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Imp = append(p.Imp, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPBidRequest) readField3(iprot thrift.TProtocol) error {
	p.App = NewApp()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *DSPBidRequest) readField4(iprot thrift.TProtocol) error {
	p.Device = NewDevice()
	if err := p.Device.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Device)
	}
	return nil
}

func (p *DSPBidRequest) readField5(iprot thrift.TProtocol) error {
	p.User = NewUser()
	if err := p.User.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.User)
	}
	return nil
}

func (p *DSPBidRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.At = v
	}
	return nil
}

func (p *DSPBidRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Secure = v
	}
	return nil
}

func (p *DSPBidRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *DSPBidRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ReqTs = v
	}
	return nil
}

func (p *DSPBidRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPBidRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPBidRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DSPBidRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Imp != nil {
		if err := oprot.WriteFieldBegin("imp", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:imp: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Imp)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Imp {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:imp: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:app: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Device != nil {
		if err := oprot.WriteFieldBegin("device", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:device: %s", p, err)
		}
		if err := p.Device.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Device)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:device: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.User != nil {
		if err := oprot.WriteFieldBegin("user", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:user: %s", p, err)
		}
		if err := p.User.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.User)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:user: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("at", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:at: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.At)); err != nil {
		return fmt.Errorf("%T.at (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:at: %s", p, err)
	}
	return err
}

func (p *DSPBidRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("secure", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:secure: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Secure)); err != nil {
		return fmt.Errorf("%T.secure (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:secure: %s", p, err)
	}
	return err
}

func (p *DSPBidRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:exchange_id: %s", p, err)
	}
	return err
}

func (p *DSPBidRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_ts", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:req_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTs)); err != nil {
		return fmt.Errorf("%T.req_ts (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:req_ts: %s", p, err)
	}
	return err
}

func (p *DSPBidRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPBidRequest(%+v)", *p)
}

type DSPBidInfo struct {
	Id             string            `thrift:"id,1" json:"id"`
	ImpId          string            `thrift:"imp_id,2" json:"imp_id"`
	Price          int64             `thrift:"price,3" json:"price"`
	Cid            int32             `thrift:"cid,4" json:"cid"`
	Crid           int32             `thrift:"crid,5" json:"crid"`
	Nurl           string            `thrift:"nurl,6" json:"nurl"`
	TargetUrl      string            `thrift:"target_url,7" json:"target_url"`
	ImpTrackings   []string          `thrift:"imp_trackings,8" json:"imp_trackings"`
	ClkTrackings   []string          `thrift:"clk_trackings,9" json:"clk_trackings"`
	TemplateId     int32             `thrift:"template_id,10" json:"template_id"`
	AdmInfo        map[string]string `thrift:"adm_info,11" json:"adm_info"`
	Ext            map[string]string `thrift:"ext,12" json:"ext"`
	DebugInfo      map[string]string `thrift:"debug_info,13" json:"debug_info"`
	AdType         int32             `thrift:"ad_type,14" json:"ad_type"`
	BrandId        int32             `thrift:"brand_id,15" json:"brand_id"`
	ItunesId       int32             `thrift:"itunes_id,16" json:"itunes_id"`
	PackageName    string            `thrift:"package_name,17" json:"package_name"`
	BidToken       string            `thrift:"bid_token,18" json:"bid_token"`
	RtbCreativeId  int32             `thrift:"rtb_creative_id,19" json:"rtb_creative_id"`
	RtbStrategyId  int32             `thrift:"rtb_strategy_id,20" json:"rtb_strategy_id"`
	RtbCampaignId  int32             `thrift:"rtb_campaign_id,21" json:"rtb_campaign_id"`
	RtbSponsorId   int32             `thrift:"rtb_sponsor_id,22" json:"rtb_sponsor_id"`
	RtbPromotionId int32             `thrift:"rtb_promotion_id,23" json:"rtb_promotion_id"`
	AppIntroUrl    string            `thrift:"app_intro_url,24" json:"app_intro_url"`
}

func NewDSPBidInfo() *DSPBidInfo {
	return &DSPBidInfo{}
}

func (p *DSPBidInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.MAP {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.MAP {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPBidInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DSPBidInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ImpId = v
	}
	return nil
}

func (p *DSPBidInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *DSPBidInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *DSPBidInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Crid = v
	}
	return nil
}

func (p *DSPBidInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Nurl = v
	}
	return nil
}

func (p *DSPBidInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TargetUrl = v
	}
	return nil
}

func (p *DSPBidInfo) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpTrackings = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.ImpTrackings = append(p.ImpTrackings, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPBidInfo) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkTrackings = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.ClkTrackings = append(p.ClkTrackings, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPBidInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *DSPBidInfo) readField11(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdmInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key5 = v
		}
		var _val6 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val6 = v
		}
		p.AdmInfo[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPBidInfo) readField12(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Ext = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key7 = v
		}
		var _val8 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val8 = v
		}
		p.Ext[_key7] = _val8
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPBidInfo) readField13(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.DebugInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key9 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key9 = v
		}
		var _val10 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val10 = v
		}
		p.DebugInfo[_key9] = _val10
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPBidInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AdType = v
	}
	return nil
}

func (p *DSPBidInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.BrandId = v
	}
	return nil
}

func (p *DSPBidInfo) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *DSPBidInfo) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *DSPBidInfo) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.BidToken = v
	}
	return nil
}

func (p *DSPBidInfo) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.RtbCreativeId = v
	}
	return nil
}

func (p *DSPBidInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.RtbStrategyId = v
	}
	return nil
}

func (p *DSPBidInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.RtbCampaignId = v
	}
	return nil
}

func (p *DSPBidInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.RtbSponsorId = v
	}
	return nil
}

func (p *DSPBidInfo) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.RtbPromotionId = v
	}
	return nil
}

func (p *DSPBidInfo) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.AppIntroUrl = v
	}
	return nil
}

func (p *DSPBidInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPBidInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPBidInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_id", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imp_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpId)); err != nil {
		return fmt.Errorf("%T.imp_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imp_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:price: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:crid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Crid)); err != nil {
		return fmt.Errorf("%T.crid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:crid: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nurl", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:nurl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Nurl)); err != nil {
		return fmt.Errorf("%T.nurl (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:nurl: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("target_url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:target_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TargetUrl)); err != nil {
		return fmt.Errorf("%T.target_url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:target_url: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.ImpTrackings != nil {
		if err := oprot.WriteFieldBegin("imp_trackings", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:imp_trackings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ImpTrackings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpTrackings {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:imp_trackings: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.ClkTrackings != nil {
		if err := oprot.WriteFieldBegin("clk_trackings", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:clk_trackings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ClkTrackings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkTrackings {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:clk_trackings: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:template_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:template_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AdmInfo != nil {
		if err := oprot.WriteFieldBegin("adm_info", thrift.MAP, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:adm_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AdmInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdmInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:adm_info: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Ext != nil {
		if err := oprot.WriteFieldBegin("ext", thrift.MAP, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:ext: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Ext {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:ext: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if p.DebugInfo != nil {
		if err := oprot.WriteFieldBegin("debug_info", thrift.MAP, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:debug_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DebugInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.DebugInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:debug_info: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_type", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:ad_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdType)); err != nil {
		return fmt.Errorf("%T.ad_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:ad_type: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brand_id", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:brand_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BrandId)); err != nil {
		return fmt.Errorf("%T.brand_id (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:brand_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunes_id", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:itunes_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunes_id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:itunes_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:package_name: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid_token", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:bid_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BidToken)); err != nil {
		return fmt.Errorf("%T.bid_token (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:bid_token: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_creative_id", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:rtb_creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCreativeId)); err != nil {
		return fmt.Errorf("%T.rtb_creative_id (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:rtb_creative_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_strategy_id", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:rtb_strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbStrategyId)); err != nil {
		return fmt.Errorf("%T.rtb_strategy_id (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:rtb_strategy_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_campaign_id", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:rtb_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCampaignId)); err != nil {
		return fmt.Errorf("%T.rtb_campaign_id (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:rtb_campaign_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_sponsor_id", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:rtb_sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbSponsorId)); err != nil {
		return fmt.Errorf("%T.rtb_sponsor_id (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:rtb_sponsor_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_promotion_id", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:rtb_promotion_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbPromotionId)); err != nil {
		return fmt.Errorf("%T.rtb_promotion_id (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:rtb_promotion_id: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_intro_url", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:app_intro_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppIntroUrl)); err != nil {
		return fmt.Errorf("%T.app_intro_url (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:app_intro_url: %s", p, err)
	}
	return err
}

func (p *DSPBidInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPBidInfo(%+v)", *p)
}

type DSPBidResponseInfo struct {
	Id      string        `thrift:"id,1" json:"id"`
	BidList []*DSPBidInfo `thrift:"bid_list,2" json:"bid_list"`
}

func NewDSPBidResponseInfo() *DSPBidResponseInfo {
	return &DSPBidResponseInfo{}
}

func (p *DSPBidResponseInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPBidResponseInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DSPBidResponseInfo) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BidList = make([]*DSPBidInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem11 := NewDSPBidInfo()
		if err := _elem11.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem11)
		}
		p.BidList = append(p.BidList, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPBidResponseInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPBidResponseInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPBidResponseInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DSPBidResponseInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.BidList != nil {
		if err := oprot.WriteFieldBegin("bid_list", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:bid_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.BidList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BidList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:bid_list: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidResponseInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPBidResponseInfo(%+v)", *p)
}

type DSPBidResponse struct {
	DspId        int32               `thrift:"dsp_id,1" json:"dsp_id"`
	Status       DSPResponseCode     `thrift:"status,2" json:"status"`
	ResponseInfo *DSPBidResponseInfo `thrift:"response_info,3" json:"response_info"`
	IsWinner     bool                `thrift:"is_winner,4" json:"is_winner"`
}

func NewDSPBidResponse() *DSPBidResponse {
	return &DSPBidResponse{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DSPBidResponse) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DSPBidResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPBidResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DspId = v
	}
	return nil
}

func (p *DSPBidResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = DSPResponseCode(v)
	}
	return nil
}

func (p *DSPBidResponse) readField3(iprot thrift.TProtocol) error {
	p.ResponseInfo = NewDSPBidResponseInfo()
	if err := p.ResponseInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ResponseInfo)
	}
	return nil
}

func (p *DSPBidResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IsWinner = v
	}
	return nil
}

func (p *DSPBidResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPBidResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPBidResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dsp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspId)); err != nil {
		return fmt.Errorf("%T.dsp_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dsp_id: %s", p, err)
	}
	return err
}

func (p *DSPBidResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ResponseInfo != nil {
		if err := oprot.WriteFieldBegin("response_info", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:response_info: %s", p, err)
		}
		if err := p.ResponseInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ResponseInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:response_info: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_winner", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:is_winner: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsWinner)); err != nil {
		return fmt.Errorf("%T.is_winner (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:is_winner: %s", p, err)
	}
	return err
}

func (p *DSPBidResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPBidResponse(%+v)", *p)
}

type DSPBidRequestResponse struct {
	DspRequest      *DSPBidRequest    `thrift:"dsp_request,1" json:"dsp_request"`
	DspResponseList []*DSPBidResponse `thrift:"dsp_response_list,2" json:"dsp_response_list"`
}

func NewDSPBidRequestResponse() *DSPBidRequestResponse {
	return &DSPBidRequestResponse{}
}

func (p *DSPBidRequestResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPBidRequestResponse) readField1(iprot thrift.TProtocol) error {
	p.DspRequest = NewDSPBidRequest()
	if err := p.DspRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DspRequest)
	}
	return nil
}

func (p *DSPBidRequestResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DspResponseList = make([]*DSPBidResponse, 0, size)
	for i := 0; i < size; i++ {
		_elem12 := NewDSPBidResponse()
		if err := _elem12.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem12)
		}
		p.DspResponseList = append(p.DspResponseList, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPBidRequestResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPBidRequestResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPBidRequestResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.DspRequest != nil {
		if err := oprot.WriteFieldBegin("dsp_request", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dsp_request: %s", p, err)
		}
		if err := p.DspRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DspRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dsp_request: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidRequestResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DspResponseList != nil {
		if err := oprot.WriteFieldBegin("dsp_response_list", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:dsp_response_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DspResponseList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DspResponseList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:dsp_response_list: %s", p, err)
		}
	}
	return err
}

func (p *DSPBidRequestResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPBidRequestResponse(%+v)", *p)
}

type DSPWinJoinedRequest struct {
	DspId            int32             `thrift:"dsp_id,1" json:"dsp_id"`
	SearchImpId      int64             `thrift:"search_imp_id,2" json:"search_imp_id"`
	CreativeId       int32             `thrift:"creative_id,3" json:"creative_id"`
	TemplateId       int64             `thrift:"template_id,4" json:"template_id"`
	WinUrl           string            `thrift:"win_url,5" json:"win_url"`
	Bid              int64             `thrift:"bid,6" json:"bid"`
	WinPrice         int64             `thrift:"win_price,7" json:"win_price"`
	DspPriceRiseRate int32             `thrift:"dsp_price_rise_rate,8" json:"dsp_price_rise_rate"`
	Ext              map[string]string `thrift:"ext,9" json:"ext"`
	Isspam           bool              `thrift:"isspam,10" json:"isspam"`
	SpamType         string            `thrift:"spam_type,11" json:"spam_type"`
	ReqTs            int32             `thrift:"req_ts,12" json:"req_ts"`
	WinTs            int32             `thrift:"win_ts,13" json:"win_ts"`
	WinJoinTs        int32             `thrift:"win_join_ts,14" json:"win_join_ts"`
	DebugInfo        map[string]string `thrift:"debug_info,15" json:"debug_info"`
	SearchId         int64             `thrift:"search_id,16" json:"search_id"`
	DspInnerWinPrice int64             `thrift:"dsp_inner_win_price,17" json:"dsp_inner_win_price"`
}

func NewDSPWinJoinedRequest() *DSPWinJoinedRequest {
	return &DSPWinJoinedRequest{}
}

func (p *DSPWinJoinedRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.MAP {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.MAP {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DspId = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchImpId = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.WinUrl = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Bid = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.WinPrice = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DspPriceRiseRate = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField9(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Ext = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key13 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key13 = v
		}
		var _val14 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val14 = v
		}
		p.Ext[_key13] = _val14
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ReqTs = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.WinTs = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.WinJoinTs = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField15(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.DebugInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key15 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key15 = v
		}
		var _val16 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val16 = v
		}
		p.DebugInfo[_key15] = _val16
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.DspInnerWinPrice = v
	}
	return nil
}

func (p *DSPWinJoinedRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPWinJoinedRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPWinJoinedRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dsp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspId)); err != nil {
		return fmt.Errorf("%T.dsp_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dsp_id: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_imp_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_imp_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchImpId)); err != nil {
		return fmt.Errorf("%T.search_imp_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_imp_id: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creative_id: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:template_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:template_id: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("win_url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:win_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WinUrl)); err != nil {
		return fmt.Errorf("%T.win_url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:win_url: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:bid: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("win_price", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:win_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.WinPrice)); err != nil {
		return fmt.Errorf("%T.win_price (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:win_price: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_price_rise_rate", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:dsp_price_rise_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspPriceRiseRate)); err != nil {
		return fmt.Errorf("%T.dsp_price_rise_rate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:dsp_price_rise_rate: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.Ext != nil {
		if err := oprot.WriteFieldBegin("ext", thrift.MAP, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:ext: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Ext {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:ext: %s", p, err)
		}
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:isspam: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:spam_type: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_ts", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:req_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTs)); err != nil {
		return fmt.Errorf("%T.req_ts (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:req_ts: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("win_ts", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:win_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WinTs)); err != nil {
		return fmt.Errorf("%T.win_ts (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:win_ts: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("win_join_ts", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:win_join_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WinJoinTs)); err != nil {
		return fmt.Errorf("%T.win_join_ts (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:win_join_ts: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if p.DebugInfo != nil {
		if err := oprot.WriteFieldBegin("debug_info", thrift.MAP, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:debug_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DebugInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.DebugInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:debug_info: %s", p, err)
		}
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:search_id: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_inner_win_price", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:dsp_inner_win_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DspInnerWinPrice)); err != nil {
		return fmt.Errorf("%T.dsp_inner_win_price (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:dsp_inner_win_price: %s", p, err)
	}
	return err
}

func (p *DSPWinJoinedRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPWinJoinedRequest(%+v)", *p)
}

type DSPBidToken struct {
	DspId           int32  `thrift:"dsp_id,1" json:"dsp_id"`
	DspAdvertiserId int32  `thrift:"dsp_advertiser_id,2" json:"dsp_advertiser_id"`
	DspCreativeId   int32  `thrift:"dsp_creative_id,3" json:"dsp_creative_id"`
	Runnable        bool   `thrift:"runnable,4" json:"runnable"`
	ExpireTs        int32  `thrift:"expire_ts,5" json:"expire_ts"`
	RtbCreativeId   int32  `thrift:"rtb_creative_id,6" json:"rtb_creative_id"`
	RtbStrategyId   int32  `thrift:"rtb_strategy_id,7" json:"rtb_strategy_id"`
	RtbCampaignId   int32  `thrift:"rtb_campaign_id,8" json:"rtb_campaign_id"`
	RtbSponsorId    int32  `thrift:"rtb_sponsor_id,9" json:"rtb_sponsor_id"`
	AdmSign         string `thrift:"adm_sign,10" json:"adm_sign"`
	RtbPromotionId  int32  `thrift:"rtb_promotion_id,11" json:"rtb_promotion_id"`
}

func NewDSPBidToken() *DSPBidToken {
	return &DSPBidToken{}
}

func (p *DSPBidToken) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPBidToken) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DspId = v
	}
	return nil
}

func (p *DSPBidToken) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DspAdvertiserId = v
	}
	return nil
}

func (p *DSPBidToken) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DspCreativeId = v
	}
	return nil
}

func (p *DSPBidToken) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *DSPBidToken) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExpireTs = v
	}
	return nil
}

func (p *DSPBidToken) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.RtbCreativeId = v
	}
	return nil
}

func (p *DSPBidToken) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.RtbStrategyId = v
	}
	return nil
}

func (p *DSPBidToken) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.RtbCampaignId = v
	}
	return nil
}

func (p *DSPBidToken) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.RtbSponsorId = v
	}
	return nil
}

func (p *DSPBidToken) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AdmSign = v
	}
	return nil
}

func (p *DSPBidToken) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.RtbPromotionId = v
	}
	return nil
}

func (p *DSPBidToken) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPBidToken"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPBidToken) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dsp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspId)); err != nil {
		return fmt.Errorf("%T.dsp_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dsp_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_advertiser_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dsp_advertiser_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspAdvertiserId)); err != nil {
		return fmt.Errorf("%T.dsp_advertiser_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dsp_advertiser_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:dsp_creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCreativeId)); err != nil {
		return fmt.Errorf("%T.dsp_creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:dsp_creative_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:runnable: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expire_ts", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:expire_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpireTs)); err != nil {
		return fmt.Errorf("%T.expire_ts (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:expire_ts: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_creative_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:rtb_creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCreativeId)); err != nil {
		return fmt.Errorf("%T.rtb_creative_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:rtb_creative_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_strategy_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:rtb_strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbStrategyId)); err != nil {
		return fmt.Errorf("%T.rtb_strategy_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:rtb_strategy_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_campaign_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:rtb_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCampaignId)); err != nil {
		return fmt.Errorf("%T.rtb_campaign_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:rtb_campaign_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_sponsor_id", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:rtb_sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbSponsorId)); err != nil {
		return fmt.Errorf("%T.rtb_sponsor_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:rtb_sponsor_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adm_sign", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:adm_sign: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdmSign)); err != nil {
		return fmt.Errorf("%T.adm_sign (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:adm_sign: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtb_promotion_id", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:rtb_promotion_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbPromotionId)); err != nil {
		return fmt.Errorf("%T.rtb_promotion_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:rtb_promotion_id: %s", p, err)
	}
	return err
}

func (p *DSPBidToken) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPBidToken(%+v)", *p)
}

type DSPClick struct {
	DspId         int32             `thrift:"dsp_id,1" json:"dsp_id"`
	SearchImpId   int64             `thrift:"search_imp_id,2" json:"search_imp_id"`
	CreativeId    int32             `thrift:"creative_id,3" json:"creative_id"`
	TemplateId    int64             `thrift:"template_id,4" json:"template_id"`
	ClickUrl      string            `thrift:"click_url,5" json:"click_url"`
	SearchId      int64             `thrift:"search_id,6" json:"search_id"`
	Ext           map[string]string `thrift:"ext,7" json:"ext"`
	ClickTs       int32             `thrift:"click_ts,8" json:"click_ts"`
	DspCreativeId int32             `thrift:"dsp_creative_id,9" json:"dsp_creative_id"`
}

func NewDSPClick() *DSPClick {
	return &DSPClick{}
}

func (p *DSPClick) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPClick) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DspId = v
	}
	return nil
}

func (p *DSPClick) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchImpId = v
	}
	return nil
}

func (p *DSPClick) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DSPClick) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *DSPClick) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ClickUrl = v
	}
	return nil
}

func (p *DSPClick) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *DSPClick) readField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Ext = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key17 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key17 = v
		}
		var _val18 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val18 = v
		}
		p.Ext[_key17] = _val18
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPClick) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ClickTs = v
	}
	return nil
}

func (p *DSPClick) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.DspCreativeId = v
	}
	return nil
}

func (p *DSPClick) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPClick"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPClick) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dsp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspId)); err != nil {
		return fmt.Errorf("%T.dsp_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dsp_id: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_imp_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_imp_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchImpId)); err != nil {
		return fmt.Errorf("%T.search_imp_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_imp_id: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creative_id: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:template_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:template_id: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:click_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickUrl)); err != nil {
		return fmt.Errorf("%T.click_url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:click_url: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:search_id: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Ext != nil {
		if err := oprot.WriteFieldBegin("ext", thrift.MAP, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:ext: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Ext {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:ext: %s", p, err)
		}
	}
	return err
}

func (p *DSPClick) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_ts", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:click_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClickTs)); err != nil {
		return fmt.Errorf("%T.click_ts (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:click_ts: %s", p, err)
	}
	return err
}

func (p *DSPClick) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_creative_id", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:dsp_creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCreativeId)); err != nil {
		return fmt.Errorf("%T.dsp_creative_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:dsp_creative_id: %s", p, err)
	}
	return err
}

func (p *DSPClick) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPClick(%+v)", *p)
}

type DSPSenderEvent struct {
	DspId            int32              `thrift:"dsp_id,1" json:"dsp_id"`
	SearchId         int64              `thrift:"search_id,2" json:"search_id"`
	SearchImpId      int64              `thrift:"search_imp_id,3" json:"search_imp_id"`
	DspCreativeId    int32              `thrift:"dsp_creative_id,4" json:"dsp_creative_id"`
	TemplateId       int64              `thrift:"template_id,5" json:"template_id"`
	SendingUrl       string             `thrift:"sending_url,6" json:"sending_url"`
	WinPrice         int64              `thrift:"win_price,7" json:"win_price"`
	DspPriceRiseRate int32              `thrift:"dsp_price_rise_rate,8" json:"dsp_price_rise_rate"`
	DspInnerWinPrice int64              `thrift:"dsp_inner_win_price,9" json:"dsp_inner_win_price"`
	FinPrice         int64              `thrift:"fin_price,10" json:"fin_price"`
	Ext              map[string]string  `thrift:"ext,11" json:"ext"`
	Isspam           bool               `thrift:"isspam,12" json:"isspam"`
	SpamType         string             `thrift:"spam_type,13" json:"spam_type"`
	DebugInfo        map[string]string  `thrift:"debug_info,14" json:"debug_info"`
	EventTs          int32              `thrift:"event_ts,15" json:"event_ts"`
	SenderEventType  DSPSenderEventType `thrift:"sender_event_type,16" json:"sender_event_type"`
	ExchangeId       int32              `thrift:"exchange_id,17" json:"exchange_id"`
}

func NewDSPSenderEvent() *DSPSenderEvent {
	return &DSPSenderEvent{
		SenderEventType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DSPSenderEvent) IsSetSenderEventType() bool {
	return int64(p.SenderEventType) != math.MinInt32-1
}

func (p *DSPSenderEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.MAP {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.MAP {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPSenderEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DspId = v
	}
	return nil
}

func (p *DSPSenderEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *DSPSenderEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchImpId = v
	}
	return nil
}

func (p *DSPSenderEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DspCreativeId = v
	}
	return nil
}

func (p *DSPSenderEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *DSPSenderEvent) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SendingUrl = v
	}
	return nil
}

func (p *DSPSenderEvent) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.WinPrice = v
	}
	return nil
}

func (p *DSPSenderEvent) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DspPriceRiseRate = v
	}
	return nil
}

func (p *DSPSenderEvent) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.DspInnerWinPrice = v
	}
	return nil
}

func (p *DSPSenderEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.FinPrice = v
	}
	return nil
}

func (p *DSPSenderEvent) readField11(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Ext = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key19 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key19 = v
		}
		var _val20 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val20 = v
		}
		p.Ext[_key19] = _val20
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPSenderEvent) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *DSPSenderEvent) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *DSPSenderEvent) readField14(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.DebugInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key21 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key21 = v
		}
		var _val22 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val22 = v
		}
		p.DebugInfo[_key21] = _val22
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DSPSenderEvent) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.EventTs = v
	}
	return nil
}

func (p *DSPSenderEvent) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.SenderEventType = DSPSenderEventType(v)
	}
	return nil
}

func (p *DSPSenderEvent) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *DSPSenderEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPSenderEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPSenderEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dsp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspId)); err != nil {
		return fmt.Errorf("%T.dsp_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dsp_id: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_id: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_imp_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:search_imp_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchImpId)); err != nil {
		return fmt.Errorf("%T.search_imp_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:search_imp_id: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_creative_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dsp_creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCreativeId)); err != nil {
		return fmt.Errorf("%T.dsp_creative_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dsp_creative_id: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:template_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:template_id: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sending_url", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:sending_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SendingUrl)); err != nil {
		return fmt.Errorf("%T.sending_url (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:sending_url: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("win_price", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:win_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.WinPrice)); err != nil {
		return fmt.Errorf("%T.win_price (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:win_price: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_price_rise_rate", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:dsp_price_rise_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspPriceRiseRate)); err != nil {
		return fmt.Errorf("%T.dsp_price_rise_rate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:dsp_price_rise_rate: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_inner_win_price", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:dsp_inner_win_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DspInnerWinPrice)); err != nil {
		return fmt.Errorf("%T.dsp_inner_win_price (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:dsp_inner_win_price: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fin_price", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:fin_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FinPrice)); err != nil {
		return fmt.Errorf("%T.fin_price (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:fin_price: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Ext != nil {
		if err := oprot.WriteFieldBegin("ext", thrift.MAP, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:ext: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Ext {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:ext: %s", p, err)
		}
	}
	return err
}

func (p *DSPSenderEvent) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:isspam: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:spam_type: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField14(oprot thrift.TProtocol) (err error) {
	if p.DebugInfo != nil {
		if err := oprot.WriteFieldBegin("debug_info", thrift.MAP, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:debug_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DebugInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.DebugInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:debug_info: %s", p, err)
		}
	}
	return err
}

func (p *DSPSenderEvent) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_ts", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:event_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EventTs)); err != nil {
		return fmt.Errorf("%T.event_ts (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:event_ts: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetSenderEventType() {
		if err := oprot.WriteFieldBegin("sender_event_type", thrift.I32, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:sender_event_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SenderEventType)); err != nil {
			return fmt.Errorf("%T.sender_event_type (16) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:sender_event_type: %s", p, err)
		}
	}
	return err
}

func (p *DSPSenderEvent) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:exchange_id: %s", p, err)
	}
	return err
}

func (p *DSPSenderEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPSenderEvent(%+v)", *p)
}
