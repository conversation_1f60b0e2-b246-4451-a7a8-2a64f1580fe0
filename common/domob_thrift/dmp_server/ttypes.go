// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dmp_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dmp_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dmp_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type DmpServiceExceptionCode int64

const (
	DmpServiceExceptionCode_QUERY_PARAM_ERROR    DmpServiceExceptionCode = 10000
	DmpServiceExceptionCode_NAME_EXISTS          DmpServiceExceptionCode = 10101
	DmpServiceExceptionCode_USER_NOT_EXISTS      DmpServiceExceptionCode = 10102
	DmpServiceExceptionCode_IN_PENDING           DmpServiceExceptionCode = 10103
	DmpServiceExceptionCode_TYPE_REJECT_MODIFIED DmpServiceExceptionCode = 10104
	DmpServiceExceptionCode_FILE_NOT_EXISTS      DmpServiceExceptionCode = 10201
	DmpServiceExceptionCode_FILE_NOT_ACCESS      DmpServiceExceptionCode = 10202
	DmpServiceExceptionCode_FILE_NOT_MATCHING    DmpServiceExceptionCode = 10203
	DmpServiceExceptionCode_SYSTEM_ERROR         DmpServiceExceptionCode = 20000
)

func (p DmpServiceExceptionCode) String() string {
	switch p {
	case DmpServiceExceptionCode_QUERY_PARAM_ERROR:
		return "DmpServiceExceptionCode_QUERY_PARAM_ERROR"
	case DmpServiceExceptionCode_NAME_EXISTS:
		return "DmpServiceExceptionCode_NAME_EXISTS"
	case DmpServiceExceptionCode_USER_NOT_EXISTS:
		return "DmpServiceExceptionCode_USER_NOT_EXISTS"
	case DmpServiceExceptionCode_IN_PENDING:
		return "DmpServiceExceptionCode_IN_PENDING"
	case DmpServiceExceptionCode_TYPE_REJECT_MODIFIED:
		return "DmpServiceExceptionCode_TYPE_REJECT_MODIFIED"
	case DmpServiceExceptionCode_FILE_NOT_EXISTS:
		return "DmpServiceExceptionCode_FILE_NOT_EXISTS"
	case DmpServiceExceptionCode_FILE_NOT_ACCESS:
		return "DmpServiceExceptionCode_FILE_NOT_ACCESS"
	case DmpServiceExceptionCode_FILE_NOT_MATCHING:
		return "DmpServiceExceptionCode_FILE_NOT_MATCHING"
	case DmpServiceExceptionCode_SYSTEM_ERROR:
		return "DmpServiceExceptionCode_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func DmpServiceExceptionCodeFromString(s string) (DmpServiceExceptionCode, error) {
	switch s {
	case "DmpServiceExceptionCode_QUERY_PARAM_ERROR":
		return DmpServiceExceptionCode_QUERY_PARAM_ERROR, nil
	case "DmpServiceExceptionCode_NAME_EXISTS":
		return DmpServiceExceptionCode_NAME_EXISTS, nil
	case "DmpServiceExceptionCode_USER_NOT_EXISTS":
		return DmpServiceExceptionCode_USER_NOT_EXISTS, nil
	case "DmpServiceExceptionCode_IN_PENDING":
		return DmpServiceExceptionCode_IN_PENDING, nil
	case "DmpServiceExceptionCode_TYPE_REJECT_MODIFIED":
		return DmpServiceExceptionCode_TYPE_REJECT_MODIFIED, nil
	case "DmpServiceExceptionCode_FILE_NOT_EXISTS":
		return DmpServiceExceptionCode_FILE_NOT_EXISTS, nil
	case "DmpServiceExceptionCode_FILE_NOT_ACCESS":
		return DmpServiceExceptionCode_FILE_NOT_ACCESS, nil
	case "DmpServiceExceptionCode_FILE_NOT_MATCHING":
		return DmpServiceExceptionCode_FILE_NOT_MATCHING, nil
	case "DmpServiceExceptionCode_SYSTEM_ERROR":
		return DmpServiceExceptionCode_SYSTEM_ERROR, nil
	}
	return DmpServiceExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid DmpServiceExceptionCode string")
}

type DmpServiceException struct {
	Code    DmpServiceExceptionCode `thrift:"code,1" json:"code"`
	Message string                  `thrift:"message,2" json:"message"`
}

func NewDmpServiceException() *DmpServiceException {
	return &DmpServiceException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DmpServiceException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DmpServiceException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpServiceException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DmpServiceExceptionCode(v)
	}
	return nil
}

func (p *DmpServiceException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DmpServiceException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpServiceException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpServiceException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DmpServiceException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DmpServiceException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpServiceException(%+v)", *p)
}
