// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dmp_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addCrowd(RequestHeader header, Crowd crowd)")
	fmt.Fprintln(os.Stderr, "  void deleteCrowdsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.<PERSON>, "  void editCrowd(RequestHeader header, Crowd crowd)")
	fmt.Fprintln(os.Stderr, "  void reloadCrowdsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCrowdsByParams(RequestHeader header, CrowdParams params)")
	fmt.Fprintln(os.Stderr, "   fetchCrowdsByParams(RequestHeader header, CrowdMixParams params)")
	fmt.Fprintln(os.Stderr, "   getCrowdsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteFilesByDids(RequestHeader header, i32 uid,  dids)")
	fmt.Fprintln(os.Stderr, "  i32 addModel(RequestHeader header, Model model)")
	fmt.Fprintln(os.Stderr, "  void deleteModelsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  void editModel(RequestHeader header, Model model)")
	fmt.Fprintln(os.Stderr, "  void pauseModelsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeModelsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchModelsByParams(RequestHeader header, ModelParams params)")
	fmt.Fprintln(os.Stderr, "   getModelsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCrodel(RequestHeader header, Crodel crodel)")
	fmt.Fprintln(os.Stderr, "  void editCrodel(RequestHeader header, Crodel crodel)")
	fmt.Fprintln(os.Stderr, "   getCrodelsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addTask(RequestHeader header, Task task)")
	fmt.Fprintln(os.Stderr, "  void deleteTasksByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  void editTask(RequestHeader header, Task task)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchTasksByParams(RequestHeader header, TaskParams params)")
	fmt.Fprintln(os.Stderr, "   getTasksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  TaskResultQueryResult QueryTaskRetByParams(RequestHeader header, TaskRetQueryParam param, TaskRetOrderParam order)")
	fmt.Fprintln(os.Stderr, "  i32 addPicture(RequestHeader header, Picture picture)")
	fmt.Fprintln(os.Stderr, "  void deletePicturesByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  void editPicture(RequestHeader header, Picture picture)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchPicturesByParams(RequestHeader header, PictureParams params)")
	fmt.Fprintln(os.Stderr, "   getPicturesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchDataSourcesByParams(RequestHeader header, DataSourceParams params)")
	fmt.Fprintln(os.Stderr, "   getDataSourcesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getDataSourcesInfoByIds(RequestHeader header,  ids, bool withTags)")
	fmt.Fprintln(os.Stderr, "   getDataSourcesInfoByFeaIds(RequestHeader header,  ids, bool withTags)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchDataAbilityByParams(RequestHeader header, DataAbilityParams params)")
	fmt.Fprintln(os.Stderr, "   getDataAbilitiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getDataAbilitiesByFeaIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getDmpTagsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchDmpTagByParams(RequestHeader header, DmpTagParams params)")
	fmt.Fprintln(os.Stderr, "   getDmpTagsByFeaIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addSponsor(RequestHeader header, Sponsor sponsor)")
	fmt.Fprintln(os.Stderr, "  void editSponsor(RequestHeader header, Sponsor sponsor)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchSponsorsByParams(RequestHeader header, SponsorParams params)")
	fmt.Fprintln(os.Stderr, "   getSponsorsByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void deleteSponsorsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  void reloadOffLineCrowd(RequestHeader header, i32 crowdId, i32 uid,  fileAttrs, bool additional)")
	fmt.Fprintln(os.Stderr, "   getCrowdProfile(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void estimateCrowd(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dmp_server.NewDmpServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addCrowd":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCrowd requires 2 args")
			flag.Usage()
		}
		arg246 := flag.Arg(1)
		mbTrans247 := thrift.NewTMemoryBufferLen(len(arg246))
		defer mbTrans247.Close()
		_, err248 := mbTrans247.WriteString(arg246)
		if err248 != nil {
			Usage()
			return
		}
		factory249 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt250 := factory249.GetProtocol(mbTrans247)
		argvalue0 := dmp_server.NewRequestHeader()
		err251 := argvalue0.Read(jsProt250)
		if err251 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg252 := flag.Arg(2)
		mbTrans253 := thrift.NewTMemoryBufferLen(len(arg252))
		defer mbTrans253.Close()
		_, err254 := mbTrans253.WriteString(arg252)
		if err254 != nil {
			Usage()
			return
		}
		factory255 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt256 := factory255.GetProtocol(mbTrans253)
		argvalue1 := dmp_server.NewCrowd()
		err257 := argvalue1.Read(jsProt256)
		if err257 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCrowd(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCrowdsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteCrowdsByIds requires 3 args")
			flag.Usage()
		}
		arg258 := flag.Arg(1)
		mbTrans259 := thrift.NewTMemoryBufferLen(len(arg258))
		defer mbTrans259.Close()
		_, err260 := mbTrans259.WriteString(arg258)
		if err260 != nil {
			Usage()
			return
		}
		factory261 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt262 := factory261.GetProtocol(mbTrans259)
		argvalue0 := dmp_server.NewRequestHeader()
		err263 := argvalue0.Read(jsProt262)
		if err263 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err264 := (strconv.Atoi(flag.Arg(2)))
		if err264 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg265 := flag.Arg(3)
		mbTrans266 := thrift.NewTMemoryBufferLen(len(arg265))
		defer mbTrans266.Close()
		_, err267 := mbTrans266.WriteString(arg265)
		if err267 != nil {
			Usage()
			return
		}
		factory268 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt269 := factory268.GetProtocol(mbTrans266)
		containerStruct2 := dmp_server.NewDeleteCrowdsByIdsArgs()
		err270 := containerStruct2.ReadField3(jsProt269)
		if err270 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteCrowdsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editCrowd":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCrowd requires 2 args")
			flag.Usage()
		}
		arg271 := flag.Arg(1)
		mbTrans272 := thrift.NewTMemoryBufferLen(len(arg271))
		defer mbTrans272.Close()
		_, err273 := mbTrans272.WriteString(arg271)
		if err273 != nil {
			Usage()
			return
		}
		factory274 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt275 := factory274.GetProtocol(mbTrans272)
		argvalue0 := dmp_server.NewRequestHeader()
		err276 := argvalue0.Read(jsProt275)
		if err276 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg277 := flag.Arg(2)
		mbTrans278 := thrift.NewTMemoryBufferLen(len(arg277))
		defer mbTrans278.Close()
		_, err279 := mbTrans278.WriteString(arg277)
		if err279 != nil {
			Usage()
			return
		}
		factory280 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt281 := factory280.GetProtocol(mbTrans278)
		argvalue1 := dmp_server.NewCrowd()
		err282 := argvalue1.Read(jsProt281)
		if err282 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCrowd(value0, value1))
		fmt.Print("\n")
		break
	case "reloadCrowdsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ReloadCrowdsByIds requires 3 args")
			flag.Usage()
		}
		arg283 := flag.Arg(1)
		mbTrans284 := thrift.NewTMemoryBufferLen(len(arg283))
		defer mbTrans284.Close()
		_, err285 := mbTrans284.WriteString(arg283)
		if err285 != nil {
			Usage()
			return
		}
		factory286 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt287 := factory286.GetProtocol(mbTrans284)
		argvalue0 := dmp_server.NewRequestHeader()
		err288 := argvalue0.Read(jsProt287)
		if err288 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err289 := (strconv.Atoi(flag.Arg(2)))
		if err289 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg290 := flag.Arg(3)
		mbTrans291 := thrift.NewTMemoryBufferLen(len(arg290))
		defer mbTrans291.Close()
		_, err292 := mbTrans291.WriteString(arg290)
		if err292 != nil {
			Usage()
			return
		}
		factory293 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt294 := factory293.GetProtocol(mbTrans291)
		containerStruct2 := dmp_server.NewReloadCrowdsByIdsArgs()
		err295 := containerStruct2.ReadField3(jsProt294)
		if err295 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ReloadCrowdsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchCrowdsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchCrowdsByParams requires 2 args")
			flag.Usage()
		}
		arg296 := flag.Arg(1)
		mbTrans297 := thrift.NewTMemoryBufferLen(len(arg296))
		defer mbTrans297.Close()
		_, err298 := mbTrans297.WriteString(arg296)
		if err298 != nil {
			Usage()
			return
		}
		factory299 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt300 := factory299.GetProtocol(mbTrans297)
		argvalue0 := dmp_server.NewRequestHeader()
		err301 := argvalue0.Read(jsProt300)
		if err301 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg302 := flag.Arg(2)
		mbTrans303 := thrift.NewTMemoryBufferLen(len(arg302))
		defer mbTrans303.Close()
		_, err304 := mbTrans303.WriteString(arg302)
		if err304 != nil {
			Usage()
			return
		}
		factory305 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt306 := factory305.GetProtocol(mbTrans303)
		argvalue1 := dmp_server.NewCrowdParams()
		err307 := argvalue1.Read(jsProt306)
		if err307 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchCrowdsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "fetchCrowdsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "FetchCrowdsByParams requires 2 args")
			flag.Usage()
		}
		arg308 := flag.Arg(1)
		mbTrans309 := thrift.NewTMemoryBufferLen(len(arg308))
		defer mbTrans309.Close()
		_, err310 := mbTrans309.WriteString(arg308)
		if err310 != nil {
			Usage()
			return
		}
		factory311 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt312 := factory311.GetProtocol(mbTrans309)
		argvalue0 := dmp_server.NewRequestHeader()
		err313 := argvalue0.Read(jsProt312)
		if err313 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg314 := flag.Arg(2)
		mbTrans315 := thrift.NewTMemoryBufferLen(len(arg314))
		defer mbTrans315.Close()
		_, err316 := mbTrans315.WriteString(arg314)
		if err316 != nil {
			Usage()
			return
		}
		factory317 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt318 := factory317.GetProtocol(mbTrans315)
		argvalue1 := dmp_server.NewCrowdMixParams()
		err319 := argvalue1.Read(jsProt318)
		if err319 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.FetchCrowdsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getCrowdsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCrowdsByIds requires 2 args")
			flag.Usage()
		}
		arg320 := flag.Arg(1)
		mbTrans321 := thrift.NewTMemoryBufferLen(len(arg320))
		defer mbTrans321.Close()
		_, err322 := mbTrans321.WriteString(arg320)
		if err322 != nil {
			Usage()
			return
		}
		factory323 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt324 := factory323.GetProtocol(mbTrans321)
		argvalue0 := dmp_server.NewRequestHeader()
		err325 := argvalue0.Read(jsProt324)
		if err325 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg326 := flag.Arg(2)
		mbTrans327 := thrift.NewTMemoryBufferLen(len(arg326))
		defer mbTrans327.Close()
		_, err328 := mbTrans327.WriteString(arg326)
		if err328 != nil {
			Usage()
			return
		}
		factory329 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt330 := factory329.GetProtocol(mbTrans327)
		containerStruct1 := dmp_server.NewGetCrowdsByIdsArgs()
		err331 := containerStruct1.ReadField2(jsProt330)
		if err331 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCrowdsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteFilesByDids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteFilesByDids requires 3 args")
			flag.Usage()
		}
		arg332 := flag.Arg(1)
		mbTrans333 := thrift.NewTMemoryBufferLen(len(arg332))
		defer mbTrans333.Close()
		_, err334 := mbTrans333.WriteString(arg332)
		if err334 != nil {
			Usage()
			return
		}
		factory335 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt336 := factory335.GetProtocol(mbTrans333)
		argvalue0 := dmp_server.NewRequestHeader()
		err337 := argvalue0.Read(jsProt336)
		if err337 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err338 := (strconv.Atoi(flag.Arg(2)))
		if err338 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg339 := flag.Arg(3)
		mbTrans340 := thrift.NewTMemoryBufferLen(len(arg339))
		defer mbTrans340.Close()
		_, err341 := mbTrans340.WriteString(arg339)
		if err341 != nil {
			Usage()
			return
		}
		factory342 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt343 := factory342.GetProtocol(mbTrans340)
		containerStruct2 := dmp_server.NewDeleteFilesByDidsArgs()
		err344 := containerStruct2.ReadField3(jsProt343)
		if err344 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Dids
		value2 := argvalue2
		fmt.Print(client.DeleteFilesByDids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addModel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddModel requires 2 args")
			flag.Usage()
		}
		arg345 := flag.Arg(1)
		mbTrans346 := thrift.NewTMemoryBufferLen(len(arg345))
		defer mbTrans346.Close()
		_, err347 := mbTrans346.WriteString(arg345)
		if err347 != nil {
			Usage()
			return
		}
		factory348 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt349 := factory348.GetProtocol(mbTrans346)
		argvalue0 := dmp_server.NewRequestHeader()
		err350 := argvalue0.Read(jsProt349)
		if err350 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg351 := flag.Arg(2)
		mbTrans352 := thrift.NewTMemoryBufferLen(len(arg351))
		defer mbTrans352.Close()
		_, err353 := mbTrans352.WriteString(arg351)
		if err353 != nil {
			Usage()
			return
		}
		factory354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt355 := factory354.GetProtocol(mbTrans352)
		argvalue1 := dmp_server.NewModel()
		err356 := argvalue1.Read(jsProt355)
		if err356 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddModel(value0, value1))
		fmt.Print("\n")
		break
	case "deleteModelsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteModelsByIds requires 3 args")
			flag.Usage()
		}
		arg357 := flag.Arg(1)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		argvalue0 := dmp_server.NewRequestHeader()
		err362 := argvalue0.Read(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err363 := (strconv.Atoi(flag.Arg(2)))
		if err363 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg364 := flag.Arg(3)
		mbTrans365 := thrift.NewTMemoryBufferLen(len(arg364))
		defer mbTrans365.Close()
		_, err366 := mbTrans365.WriteString(arg364)
		if err366 != nil {
			Usage()
			return
		}
		factory367 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt368 := factory367.GetProtocol(mbTrans365)
		containerStruct2 := dmp_server.NewDeleteModelsByIdsArgs()
		err369 := containerStruct2.ReadField3(jsProt368)
		if err369 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteModelsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editModel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditModel requires 2 args")
			flag.Usage()
		}
		arg370 := flag.Arg(1)
		mbTrans371 := thrift.NewTMemoryBufferLen(len(arg370))
		defer mbTrans371.Close()
		_, err372 := mbTrans371.WriteString(arg370)
		if err372 != nil {
			Usage()
			return
		}
		factory373 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt374 := factory373.GetProtocol(mbTrans371)
		argvalue0 := dmp_server.NewRequestHeader()
		err375 := argvalue0.Read(jsProt374)
		if err375 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg376 := flag.Arg(2)
		mbTrans377 := thrift.NewTMemoryBufferLen(len(arg376))
		defer mbTrans377.Close()
		_, err378 := mbTrans377.WriteString(arg376)
		if err378 != nil {
			Usage()
			return
		}
		factory379 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt380 := factory379.GetProtocol(mbTrans377)
		argvalue1 := dmp_server.NewModel()
		err381 := argvalue1.Read(jsProt380)
		if err381 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditModel(value0, value1))
		fmt.Print("\n")
		break
	case "pauseModelsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseModelsByIds requires 3 args")
			flag.Usage()
		}
		arg382 := flag.Arg(1)
		mbTrans383 := thrift.NewTMemoryBufferLen(len(arg382))
		defer mbTrans383.Close()
		_, err384 := mbTrans383.WriteString(arg382)
		if err384 != nil {
			Usage()
			return
		}
		factory385 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt386 := factory385.GetProtocol(mbTrans383)
		argvalue0 := dmp_server.NewRequestHeader()
		err387 := argvalue0.Read(jsProt386)
		if err387 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err388 := (strconv.Atoi(flag.Arg(2)))
		if err388 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg389 := flag.Arg(3)
		mbTrans390 := thrift.NewTMemoryBufferLen(len(arg389))
		defer mbTrans390.Close()
		_, err391 := mbTrans390.WriteString(arg389)
		if err391 != nil {
			Usage()
			return
		}
		factory392 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt393 := factory392.GetProtocol(mbTrans390)
		containerStruct2 := dmp_server.NewPauseModelsByIdsArgs()
		err394 := containerStruct2.ReadField3(jsProt393)
		if err394 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.PauseModelsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeModelsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeModelsByIds requires 3 args")
			flag.Usage()
		}
		arg395 := flag.Arg(1)
		mbTrans396 := thrift.NewTMemoryBufferLen(len(arg395))
		defer mbTrans396.Close()
		_, err397 := mbTrans396.WriteString(arg395)
		if err397 != nil {
			Usage()
			return
		}
		factory398 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt399 := factory398.GetProtocol(mbTrans396)
		argvalue0 := dmp_server.NewRequestHeader()
		err400 := argvalue0.Read(jsProt399)
		if err400 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err401 := (strconv.Atoi(flag.Arg(2)))
		if err401 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg402 := flag.Arg(3)
		mbTrans403 := thrift.NewTMemoryBufferLen(len(arg402))
		defer mbTrans403.Close()
		_, err404 := mbTrans403.WriteString(arg402)
		if err404 != nil {
			Usage()
			return
		}
		factory405 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt406 := factory405.GetProtocol(mbTrans403)
		containerStruct2 := dmp_server.NewResumeModelsByIdsArgs()
		err407 := containerStruct2.ReadField3(jsProt406)
		if err407 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ResumeModelsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchModelsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchModelsByParams requires 2 args")
			flag.Usage()
		}
		arg408 := flag.Arg(1)
		mbTrans409 := thrift.NewTMemoryBufferLen(len(arg408))
		defer mbTrans409.Close()
		_, err410 := mbTrans409.WriteString(arg408)
		if err410 != nil {
			Usage()
			return
		}
		factory411 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt412 := factory411.GetProtocol(mbTrans409)
		argvalue0 := dmp_server.NewRequestHeader()
		err413 := argvalue0.Read(jsProt412)
		if err413 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg414 := flag.Arg(2)
		mbTrans415 := thrift.NewTMemoryBufferLen(len(arg414))
		defer mbTrans415.Close()
		_, err416 := mbTrans415.WriteString(arg414)
		if err416 != nil {
			Usage()
			return
		}
		factory417 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt418 := factory417.GetProtocol(mbTrans415)
		argvalue1 := dmp_server.NewModelParams()
		err419 := argvalue1.Read(jsProt418)
		if err419 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchModelsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getModelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetModelsByIds requires 2 args")
			flag.Usage()
		}
		arg420 := flag.Arg(1)
		mbTrans421 := thrift.NewTMemoryBufferLen(len(arg420))
		defer mbTrans421.Close()
		_, err422 := mbTrans421.WriteString(arg420)
		if err422 != nil {
			Usage()
			return
		}
		factory423 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt424 := factory423.GetProtocol(mbTrans421)
		argvalue0 := dmp_server.NewRequestHeader()
		err425 := argvalue0.Read(jsProt424)
		if err425 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg426 := flag.Arg(2)
		mbTrans427 := thrift.NewTMemoryBufferLen(len(arg426))
		defer mbTrans427.Close()
		_, err428 := mbTrans427.WriteString(arg426)
		if err428 != nil {
			Usage()
			return
		}
		factory429 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt430 := factory429.GetProtocol(mbTrans427)
		containerStruct1 := dmp_server.NewGetModelsByIdsArgs()
		err431 := containerStruct1.ReadField2(jsProt430)
		if err431 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetModelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCrodel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCrodel requires 2 args")
			flag.Usage()
		}
		arg432 := flag.Arg(1)
		mbTrans433 := thrift.NewTMemoryBufferLen(len(arg432))
		defer mbTrans433.Close()
		_, err434 := mbTrans433.WriteString(arg432)
		if err434 != nil {
			Usage()
			return
		}
		factory435 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt436 := factory435.GetProtocol(mbTrans433)
		argvalue0 := dmp_server.NewRequestHeader()
		err437 := argvalue0.Read(jsProt436)
		if err437 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg438 := flag.Arg(2)
		mbTrans439 := thrift.NewTMemoryBufferLen(len(arg438))
		defer mbTrans439.Close()
		_, err440 := mbTrans439.WriteString(arg438)
		if err440 != nil {
			Usage()
			return
		}
		factory441 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt442 := factory441.GetProtocol(mbTrans439)
		argvalue1 := dmp_server.NewCrodel()
		err443 := argvalue1.Read(jsProt442)
		if err443 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCrodel(value0, value1))
		fmt.Print("\n")
		break
	case "editCrodel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCrodel requires 2 args")
			flag.Usage()
		}
		arg444 := flag.Arg(1)
		mbTrans445 := thrift.NewTMemoryBufferLen(len(arg444))
		defer mbTrans445.Close()
		_, err446 := mbTrans445.WriteString(arg444)
		if err446 != nil {
			Usage()
			return
		}
		factory447 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt448 := factory447.GetProtocol(mbTrans445)
		argvalue0 := dmp_server.NewRequestHeader()
		err449 := argvalue0.Read(jsProt448)
		if err449 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg450 := flag.Arg(2)
		mbTrans451 := thrift.NewTMemoryBufferLen(len(arg450))
		defer mbTrans451.Close()
		_, err452 := mbTrans451.WriteString(arg450)
		if err452 != nil {
			Usage()
			return
		}
		factory453 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt454 := factory453.GetProtocol(mbTrans451)
		argvalue1 := dmp_server.NewCrodel()
		err455 := argvalue1.Read(jsProt454)
		if err455 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCrodel(value0, value1))
		fmt.Print("\n")
		break
	case "getCrodelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCrodelsByIds requires 2 args")
			flag.Usage()
		}
		arg456 := flag.Arg(1)
		mbTrans457 := thrift.NewTMemoryBufferLen(len(arg456))
		defer mbTrans457.Close()
		_, err458 := mbTrans457.WriteString(arg456)
		if err458 != nil {
			Usage()
			return
		}
		factory459 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt460 := factory459.GetProtocol(mbTrans457)
		argvalue0 := dmp_server.NewRequestHeader()
		err461 := argvalue0.Read(jsProt460)
		if err461 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg462 := flag.Arg(2)
		mbTrans463 := thrift.NewTMemoryBufferLen(len(arg462))
		defer mbTrans463.Close()
		_, err464 := mbTrans463.WriteString(arg462)
		if err464 != nil {
			Usage()
			return
		}
		factory465 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt466 := factory465.GetProtocol(mbTrans463)
		containerStruct1 := dmp_server.NewGetCrodelsByIdsArgs()
		err467 := containerStruct1.ReadField2(jsProt466)
		if err467 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCrodelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addTask":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTask requires 2 args")
			flag.Usage()
		}
		arg468 := flag.Arg(1)
		mbTrans469 := thrift.NewTMemoryBufferLen(len(arg468))
		defer mbTrans469.Close()
		_, err470 := mbTrans469.WriteString(arg468)
		if err470 != nil {
			Usage()
			return
		}
		factory471 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt472 := factory471.GetProtocol(mbTrans469)
		argvalue0 := dmp_server.NewRequestHeader()
		err473 := argvalue0.Read(jsProt472)
		if err473 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg474 := flag.Arg(2)
		mbTrans475 := thrift.NewTMemoryBufferLen(len(arg474))
		defer mbTrans475.Close()
		_, err476 := mbTrans475.WriteString(arg474)
		if err476 != nil {
			Usage()
			return
		}
		factory477 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt478 := factory477.GetProtocol(mbTrans475)
		argvalue1 := dmp_server.NewTask()
		err479 := argvalue1.Read(jsProt478)
		if err479 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTask(value0, value1))
		fmt.Print("\n")
		break
	case "deleteTasksByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteTasksByIds requires 3 args")
			flag.Usage()
		}
		arg480 := flag.Arg(1)
		mbTrans481 := thrift.NewTMemoryBufferLen(len(arg480))
		defer mbTrans481.Close()
		_, err482 := mbTrans481.WriteString(arg480)
		if err482 != nil {
			Usage()
			return
		}
		factory483 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt484 := factory483.GetProtocol(mbTrans481)
		argvalue0 := dmp_server.NewRequestHeader()
		err485 := argvalue0.Read(jsProt484)
		if err485 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err486 := (strconv.Atoi(flag.Arg(2)))
		if err486 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg487 := flag.Arg(3)
		mbTrans488 := thrift.NewTMemoryBufferLen(len(arg487))
		defer mbTrans488.Close()
		_, err489 := mbTrans488.WriteString(arg487)
		if err489 != nil {
			Usage()
			return
		}
		factory490 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt491 := factory490.GetProtocol(mbTrans488)
		containerStruct2 := dmp_server.NewDeleteTasksByIdsArgs()
		err492 := containerStruct2.ReadField3(jsProt491)
		if err492 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteTasksByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editTask":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditTask requires 2 args")
			flag.Usage()
		}
		arg493 := flag.Arg(1)
		mbTrans494 := thrift.NewTMemoryBufferLen(len(arg493))
		defer mbTrans494.Close()
		_, err495 := mbTrans494.WriteString(arg493)
		if err495 != nil {
			Usage()
			return
		}
		factory496 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt497 := factory496.GetProtocol(mbTrans494)
		argvalue0 := dmp_server.NewRequestHeader()
		err498 := argvalue0.Read(jsProt497)
		if err498 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg499 := flag.Arg(2)
		mbTrans500 := thrift.NewTMemoryBufferLen(len(arg499))
		defer mbTrans500.Close()
		_, err501 := mbTrans500.WriteString(arg499)
		if err501 != nil {
			Usage()
			return
		}
		factory502 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt503 := factory502.GetProtocol(mbTrans500)
		argvalue1 := dmp_server.NewTask()
		err504 := argvalue1.Read(jsProt503)
		if err504 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditTask(value0, value1))
		fmt.Print("\n")
		break
	case "searchTasksByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchTasksByParams requires 2 args")
			flag.Usage()
		}
		arg505 := flag.Arg(1)
		mbTrans506 := thrift.NewTMemoryBufferLen(len(arg505))
		defer mbTrans506.Close()
		_, err507 := mbTrans506.WriteString(arg505)
		if err507 != nil {
			Usage()
			return
		}
		factory508 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt509 := factory508.GetProtocol(mbTrans506)
		argvalue0 := dmp_server.NewRequestHeader()
		err510 := argvalue0.Read(jsProt509)
		if err510 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg511 := flag.Arg(2)
		mbTrans512 := thrift.NewTMemoryBufferLen(len(arg511))
		defer mbTrans512.Close()
		_, err513 := mbTrans512.WriteString(arg511)
		if err513 != nil {
			Usage()
			return
		}
		factory514 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt515 := factory514.GetProtocol(mbTrans512)
		argvalue1 := dmp_server.NewTaskParams()
		err516 := argvalue1.Read(jsProt515)
		if err516 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchTasksByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getTasksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTasksByIds requires 2 args")
			flag.Usage()
		}
		arg517 := flag.Arg(1)
		mbTrans518 := thrift.NewTMemoryBufferLen(len(arg517))
		defer mbTrans518.Close()
		_, err519 := mbTrans518.WriteString(arg517)
		if err519 != nil {
			Usage()
			return
		}
		factory520 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt521 := factory520.GetProtocol(mbTrans518)
		argvalue0 := dmp_server.NewRequestHeader()
		err522 := argvalue0.Read(jsProt521)
		if err522 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg523 := flag.Arg(2)
		mbTrans524 := thrift.NewTMemoryBufferLen(len(arg523))
		defer mbTrans524.Close()
		_, err525 := mbTrans524.WriteString(arg523)
		if err525 != nil {
			Usage()
			return
		}
		factory526 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt527 := factory526.GetProtocol(mbTrans524)
		containerStruct1 := dmp_server.NewGetTasksByIdsArgs()
		err528 := containerStruct1.ReadField2(jsProt527)
		if err528 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetTasksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "QueryTaskRetByParams":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "QueryTaskRetByParams requires 3 args")
			flag.Usage()
		}
		arg529 := flag.Arg(1)
		mbTrans530 := thrift.NewTMemoryBufferLen(len(arg529))
		defer mbTrans530.Close()
		_, err531 := mbTrans530.WriteString(arg529)
		if err531 != nil {
			Usage()
			return
		}
		factory532 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt533 := factory532.GetProtocol(mbTrans530)
		argvalue0 := dmp_server.NewRequestHeader()
		err534 := argvalue0.Read(jsProt533)
		if err534 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg535 := flag.Arg(2)
		mbTrans536 := thrift.NewTMemoryBufferLen(len(arg535))
		defer mbTrans536.Close()
		_, err537 := mbTrans536.WriteString(arg535)
		if err537 != nil {
			Usage()
			return
		}
		factory538 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt539 := factory538.GetProtocol(mbTrans536)
		argvalue1 := dmp_server.NewTaskRetQueryParam()
		err540 := argvalue1.Read(jsProt539)
		if err540 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg541 := flag.Arg(3)
		mbTrans542 := thrift.NewTMemoryBufferLen(len(arg541))
		defer mbTrans542.Close()
		_, err543 := mbTrans542.WriteString(arg541)
		if err543 != nil {
			Usage()
			return
		}
		factory544 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt545 := factory544.GetProtocol(mbTrans542)
		argvalue2 := dmp_server.NewTaskRetOrderParam()
		err546 := argvalue2.Read(jsProt545)
		if err546 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.QueryTaskRetByParams(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addPicture":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPicture requires 2 args")
			flag.Usage()
		}
		arg547 := flag.Arg(1)
		mbTrans548 := thrift.NewTMemoryBufferLen(len(arg547))
		defer mbTrans548.Close()
		_, err549 := mbTrans548.WriteString(arg547)
		if err549 != nil {
			Usage()
			return
		}
		factory550 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt551 := factory550.GetProtocol(mbTrans548)
		argvalue0 := dmp_server.NewRequestHeader()
		err552 := argvalue0.Read(jsProt551)
		if err552 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg553 := flag.Arg(2)
		mbTrans554 := thrift.NewTMemoryBufferLen(len(arg553))
		defer mbTrans554.Close()
		_, err555 := mbTrans554.WriteString(arg553)
		if err555 != nil {
			Usage()
			return
		}
		factory556 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt557 := factory556.GetProtocol(mbTrans554)
		argvalue1 := dmp_server.NewPicture()
		err558 := argvalue1.Read(jsProt557)
		if err558 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPicture(value0, value1))
		fmt.Print("\n")
		break
	case "deletePicturesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeletePicturesByIds requires 3 args")
			flag.Usage()
		}
		arg559 := flag.Arg(1)
		mbTrans560 := thrift.NewTMemoryBufferLen(len(arg559))
		defer mbTrans560.Close()
		_, err561 := mbTrans560.WriteString(arg559)
		if err561 != nil {
			Usage()
			return
		}
		factory562 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt563 := factory562.GetProtocol(mbTrans560)
		argvalue0 := dmp_server.NewRequestHeader()
		err564 := argvalue0.Read(jsProt563)
		if err564 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err565 := (strconv.Atoi(flag.Arg(2)))
		if err565 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg566 := flag.Arg(3)
		mbTrans567 := thrift.NewTMemoryBufferLen(len(arg566))
		defer mbTrans567.Close()
		_, err568 := mbTrans567.WriteString(arg566)
		if err568 != nil {
			Usage()
			return
		}
		factory569 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt570 := factory569.GetProtocol(mbTrans567)
		containerStruct2 := dmp_server.NewDeletePicturesByIdsArgs()
		err571 := containerStruct2.ReadField3(jsProt570)
		if err571 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeletePicturesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editPicture":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPicture requires 2 args")
			flag.Usage()
		}
		arg572 := flag.Arg(1)
		mbTrans573 := thrift.NewTMemoryBufferLen(len(arg572))
		defer mbTrans573.Close()
		_, err574 := mbTrans573.WriteString(arg572)
		if err574 != nil {
			Usage()
			return
		}
		factory575 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt576 := factory575.GetProtocol(mbTrans573)
		argvalue0 := dmp_server.NewRequestHeader()
		err577 := argvalue0.Read(jsProt576)
		if err577 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg578 := flag.Arg(2)
		mbTrans579 := thrift.NewTMemoryBufferLen(len(arg578))
		defer mbTrans579.Close()
		_, err580 := mbTrans579.WriteString(arg578)
		if err580 != nil {
			Usage()
			return
		}
		factory581 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt582 := factory581.GetProtocol(mbTrans579)
		argvalue1 := dmp_server.NewPicture()
		err583 := argvalue1.Read(jsProt582)
		if err583 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditPicture(value0, value1))
		fmt.Print("\n")
		break
	case "searchPicturesByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchPicturesByParams requires 2 args")
			flag.Usage()
		}
		arg584 := flag.Arg(1)
		mbTrans585 := thrift.NewTMemoryBufferLen(len(arg584))
		defer mbTrans585.Close()
		_, err586 := mbTrans585.WriteString(arg584)
		if err586 != nil {
			Usage()
			return
		}
		factory587 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt588 := factory587.GetProtocol(mbTrans585)
		argvalue0 := dmp_server.NewRequestHeader()
		err589 := argvalue0.Read(jsProt588)
		if err589 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg590 := flag.Arg(2)
		mbTrans591 := thrift.NewTMemoryBufferLen(len(arg590))
		defer mbTrans591.Close()
		_, err592 := mbTrans591.WriteString(arg590)
		if err592 != nil {
			Usage()
			return
		}
		factory593 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt594 := factory593.GetProtocol(mbTrans591)
		argvalue1 := dmp_server.NewPictureParams()
		err595 := argvalue1.Read(jsProt594)
		if err595 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchPicturesByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getPicturesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPicturesByIds requires 2 args")
			flag.Usage()
		}
		arg596 := flag.Arg(1)
		mbTrans597 := thrift.NewTMemoryBufferLen(len(arg596))
		defer mbTrans597.Close()
		_, err598 := mbTrans597.WriteString(arg596)
		if err598 != nil {
			Usage()
			return
		}
		factory599 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt600 := factory599.GetProtocol(mbTrans597)
		argvalue0 := dmp_server.NewRequestHeader()
		err601 := argvalue0.Read(jsProt600)
		if err601 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg602 := flag.Arg(2)
		mbTrans603 := thrift.NewTMemoryBufferLen(len(arg602))
		defer mbTrans603.Close()
		_, err604 := mbTrans603.WriteString(arg602)
		if err604 != nil {
			Usage()
			return
		}
		factory605 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt606 := factory605.GetProtocol(mbTrans603)
		containerStruct1 := dmp_server.NewGetPicturesByIdsArgs()
		err607 := containerStruct1.ReadField2(jsProt606)
		if err607 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPicturesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchDataSourcesByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchDataSourcesByParams requires 2 args")
			flag.Usage()
		}
		arg608 := flag.Arg(1)
		mbTrans609 := thrift.NewTMemoryBufferLen(len(arg608))
		defer mbTrans609.Close()
		_, err610 := mbTrans609.WriteString(arg608)
		if err610 != nil {
			Usage()
			return
		}
		factory611 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt612 := factory611.GetProtocol(mbTrans609)
		argvalue0 := dmp_server.NewRequestHeader()
		err613 := argvalue0.Read(jsProt612)
		if err613 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg614 := flag.Arg(2)
		mbTrans615 := thrift.NewTMemoryBufferLen(len(arg614))
		defer mbTrans615.Close()
		_, err616 := mbTrans615.WriteString(arg614)
		if err616 != nil {
			Usage()
			return
		}
		factory617 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt618 := factory617.GetProtocol(mbTrans615)
		argvalue1 := dmp_server.NewDataSourceParams()
		err619 := argvalue1.Read(jsProt618)
		if err619 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchDataSourcesByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getDataSourcesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDataSourcesByIds requires 2 args")
			flag.Usage()
		}
		arg620 := flag.Arg(1)
		mbTrans621 := thrift.NewTMemoryBufferLen(len(arg620))
		defer mbTrans621.Close()
		_, err622 := mbTrans621.WriteString(arg620)
		if err622 != nil {
			Usage()
			return
		}
		factory623 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt624 := factory623.GetProtocol(mbTrans621)
		argvalue0 := dmp_server.NewRequestHeader()
		err625 := argvalue0.Read(jsProt624)
		if err625 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg626 := flag.Arg(2)
		mbTrans627 := thrift.NewTMemoryBufferLen(len(arg626))
		defer mbTrans627.Close()
		_, err628 := mbTrans627.WriteString(arg626)
		if err628 != nil {
			Usage()
			return
		}
		factory629 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt630 := factory629.GetProtocol(mbTrans627)
		containerStruct1 := dmp_server.NewGetDataSourcesByIdsArgs()
		err631 := containerStruct1.ReadField2(jsProt630)
		if err631 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDataSourcesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getDataSourcesInfoByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetDataSourcesInfoByIds requires 3 args")
			flag.Usage()
		}
		arg632 := flag.Arg(1)
		mbTrans633 := thrift.NewTMemoryBufferLen(len(arg632))
		defer mbTrans633.Close()
		_, err634 := mbTrans633.WriteString(arg632)
		if err634 != nil {
			Usage()
			return
		}
		factory635 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt636 := factory635.GetProtocol(mbTrans633)
		argvalue0 := dmp_server.NewRequestHeader()
		err637 := argvalue0.Read(jsProt636)
		if err637 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg638 := flag.Arg(2)
		mbTrans639 := thrift.NewTMemoryBufferLen(len(arg638))
		defer mbTrans639.Close()
		_, err640 := mbTrans639.WriteString(arg638)
		if err640 != nil {
			Usage()
			return
		}
		factory641 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt642 := factory641.GetProtocol(mbTrans639)
		containerStruct1 := dmp_server.NewGetDataSourcesInfoByIdsArgs()
		err643 := containerStruct1.ReadField2(jsProt642)
		if err643 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetDataSourcesInfoByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getDataSourcesInfoByFeaIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetDataSourcesInfoByFeaIds requires 3 args")
			flag.Usage()
		}
		arg645 := flag.Arg(1)
		mbTrans646 := thrift.NewTMemoryBufferLen(len(arg645))
		defer mbTrans646.Close()
		_, err647 := mbTrans646.WriteString(arg645)
		if err647 != nil {
			Usage()
			return
		}
		factory648 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt649 := factory648.GetProtocol(mbTrans646)
		argvalue0 := dmp_server.NewRequestHeader()
		err650 := argvalue0.Read(jsProt649)
		if err650 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg651 := flag.Arg(2)
		mbTrans652 := thrift.NewTMemoryBufferLen(len(arg651))
		defer mbTrans652.Close()
		_, err653 := mbTrans652.WriteString(arg651)
		if err653 != nil {
			Usage()
			return
		}
		factory654 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt655 := factory654.GetProtocol(mbTrans652)
		containerStruct1 := dmp_server.NewGetDataSourcesInfoByFeaIdsArgs()
		err656 := containerStruct1.ReadField2(jsProt655)
		if err656 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetDataSourcesInfoByFeaIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchDataAbilityByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchDataAbilityByParams requires 2 args")
			flag.Usage()
		}
		arg658 := flag.Arg(1)
		mbTrans659 := thrift.NewTMemoryBufferLen(len(arg658))
		defer mbTrans659.Close()
		_, err660 := mbTrans659.WriteString(arg658)
		if err660 != nil {
			Usage()
			return
		}
		factory661 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt662 := factory661.GetProtocol(mbTrans659)
		argvalue0 := dmp_server.NewRequestHeader()
		err663 := argvalue0.Read(jsProt662)
		if err663 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg664 := flag.Arg(2)
		mbTrans665 := thrift.NewTMemoryBufferLen(len(arg664))
		defer mbTrans665.Close()
		_, err666 := mbTrans665.WriteString(arg664)
		if err666 != nil {
			Usage()
			return
		}
		factory667 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt668 := factory667.GetProtocol(mbTrans665)
		argvalue1 := dmp_server.NewDataAbilityParams()
		err669 := argvalue1.Read(jsProt668)
		if err669 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchDataAbilityByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getDataAbilitiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDataAbilitiesByIds requires 2 args")
			flag.Usage()
		}
		arg670 := flag.Arg(1)
		mbTrans671 := thrift.NewTMemoryBufferLen(len(arg670))
		defer mbTrans671.Close()
		_, err672 := mbTrans671.WriteString(arg670)
		if err672 != nil {
			Usage()
			return
		}
		factory673 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt674 := factory673.GetProtocol(mbTrans671)
		argvalue0 := dmp_server.NewRequestHeader()
		err675 := argvalue0.Read(jsProt674)
		if err675 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg676 := flag.Arg(2)
		mbTrans677 := thrift.NewTMemoryBufferLen(len(arg676))
		defer mbTrans677.Close()
		_, err678 := mbTrans677.WriteString(arg676)
		if err678 != nil {
			Usage()
			return
		}
		factory679 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt680 := factory679.GetProtocol(mbTrans677)
		containerStruct1 := dmp_server.NewGetDataAbilitiesByIdsArgs()
		err681 := containerStruct1.ReadField2(jsProt680)
		if err681 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDataAbilitiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getDataAbilitiesByFeaIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDataAbilitiesByFeaIds requires 2 args")
			flag.Usage()
		}
		arg682 := flag.Arg(1)
		mbTrans683 := thrift.NewTMemoryBufferLen(len(arg682))
		defer mbTrans683.Close()
		_, err684 := mbTrans683.WriteString(arg682)
		if err684 != nil {
			Usage()
			return
		}
		factory685 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt686 := factory685.GetProtocol(mbTrans683)
		argvalue0 := dmp_server.NewRequestHeader()
		err687 := argvalue0.Read(jsProt686)
		if err687 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg688 := flag.Arg(2)
		mbTrans689 := thrift.NewTMemoryBufferLen(len(arg688))
		defer mbTrans689.Close()
		_, err690 := mbTrans689.WriteString(arg688)
		if err690 != nil {
			Usage()
			return
		}
		factory691 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt692 := factory691.GetProtocol(mbTrans689)
		containerStruct1 := dmp_server.NewGetDataAbilitiesByFeaIdsArgs()
		err693 := containerStruct1.ReadField2(jsProt692)
		if err693 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDataAbilitiesByFeaIds(value0, value1))
		fmt.Print("\n")
		break
	case "getDmpTagsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDmpTagsByIds requires 2 args")
			flag.Usage()
		}
		arg694 := flag.Arg(1)
		mbTrans695 := thrift.NewTMemoryBufferLen(len(arg694))
		defer mbTrans695.Close()
		_, err696 := mbTrans695.WriteString(arg694)
		if err696 != nil {
			Usage()
			return
		}
		factory697 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt698 := factory697.GetProtocol(mbTrans695)
		argvalue0 := dmp_server.NewRequestHeader()
		err699 := argvalue0.Read(jsProt698)
		if err699 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg700 := flag.Arg(2)
		mbTrans701 := thrift.NewTMemoryBufferLen(len(arg700))
		defer mbTrans701.Close()
		_, err702 := mbTrans701.WriteString(arg700)
		if err702 != nil {
			Usage()
			return
		}
		factory703 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt704 := factory703.GetProtocol(mbTrans701)
		containerStruct1 := dmp_server.NewGetDmpTagsByIdsArgs()
		err705 := containerStruct1.ReadField2(jsProt704)
		if err705 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDmpTagsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchDmpTagByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchDmpTagByParams requires 2 args")
			flag.Usage()
		}
		arg706 := flag.Arg(1)
		mbTrans707 := thrift.NewTMemoryBufferLen(len(arg706))
		defer mbTrans707.Close()
		_, err708 := mbTrans707.WriteString(arg706)
		if err708 != nil {
			Usage()
			return
		}
		factory709 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt710 := factory709.GetProtocol(mbTrans707)
		argvalue0 := dmp_server.NewRequestHeader()
		err711 := argvalue0.Read(jsProt710)
		if err711 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg712 := flag.Arg(2)
		mbTrans713 := thrift.NewTMemoryBufferLen(len(arg712))
		defer mbTrans713.Close()
		_, err714 := mbTrans713.WriteString(arg712)
		if err714 != nil {
			Usage()
			return
		}
		factory715 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt716 := factory715.GetProtocol(mbTrans713)
		argvalue1 := dmp_server.NewDmpTagParams()
		err717 := argvalue1.Read(jsProt716)
		if err717 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchDmpTagByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getDmpTagsByFeaIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDmpTagsByFeaIds requires 2 args")
			flag.Usage()
		}
		arg718 := flag.Arg(1)
		mbTrans719 := thrift.NewTMemoryBufferLen(len(arg718))
		defer mbTrans719.Close()
		_, err720 := mbTrans719.WriteString(arg718)
		if err720 != nil {
			Usage()
			return
		}
		factory721 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt722 := factory721.GetProtocol(mbTrans719)
		argvalue0 := dmp_server.NewRequestHeader()
		err723 := argvalue0.Read(jsProt722)
		if err723 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg724 := flag.Arg(2)
		mbTrans725 := thrift.NewTMemoryBufferLen(len(arg724))
		defer mbTrans725.Close()
		_, err726 := mbTrans725.WriteString(arg724)
		if err726 != nil {
			Usage()
			return
		}
		factory727 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt728 := factory727.GetProtocol(mbTrans725)
		containerStruct1 := dmp_server.NewGetDmpTagsByFeaIdsArgs()
		err729 := containerStruct1.ReadField2(jsProt728)
		if err729 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDmpTagsByFeaIds(value0, value1))
		fmt.Print("\n")
		break
	case "addSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSponsor requires 2 args")
			flag.Usage()
		}
		arg730 := flag.Arg(1)
		mbTrans731 := thrift.NewTMemoryBufferLen(len(arg730))
		defer mbTrans731.Close()
		_, err732 := mbTrans731.WriteString(arg730)
		if err732 != nil {
			Usage()
			return
		}
		factory733 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt734 := factory733.GetProtocol(mbTrans731)
		argvalue0 := dmp_server.NewRequestHeader()
		err735 := argvalue0.Read(jsProt734)
		if err735 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg736 := flag.Arg(2)
		mbTrans737 := thrift.NewTMemoryBufferLen(len(arg736))
		defer mbTrans737.Close()
		_, err738 := mbTrans737.WriteString(arg736)
		if err738 != nil {
			Usage()
			return
		}
		factory739 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt740 := factory739.GetProtocol(mbTrans737)
		argvalue1 := dmp_server.NewSponsor()
		err741 := argvalue1.Read(jsProt740)
		if err741 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "editSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditSponsor requires 2 args")
			flag.Usage()
		}
		arg742 := flag.Arg(1)
		mbTrans743 := thrift.NewTMemoryBufferLen(len(arg742))
		defer mbTrans743.Close()
		_, err744 := mbTrans743.WriteString(arg742)
		if err744 != nil {
			Usage()
			return
		}
		factory745 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt746 := factory745.GetProtocol(mbTrans743)
		argvalue0 := dmp_server.NewRequestHeader()
		err747 := argvalue0.Read(jsProt746)
		if err747 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg748 := flag.Arg(2)
		mbTrans749 := thrift.NewTMemoryBufferLen(len(arg748))
		defer mbTrans749.Close()
		_, err750 := mbTrans749.WriteString(arg748)
		if err750 != nil {
			Usage()
			return
		}
		factory751 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt752 := factory751.GetProtocol(mbTrans749)
		argvalue1 := dmp_server.NewSponsor()
		err753 := argvalue1.Read(jsProt752)
		if err753 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "searchSponsorsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchSponsorsByParams requires 2 args")
			flag.Usage()
		}
		arg754 := flag.Arg(1)
		mbTrans755 := thrift.NewTMemoryBufferLen(len(arg754))
		defer mbTrans755.Close()
		_, err756 := mbTrans755.WriteString(arg754)
		if err756 != nil {
			Usage()
			return
		}
		factory757 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt758 := factory757.GetProtocol(mbTrans755)
		argvalue0 := dmp_server.NewRequestHeader()
		err759 := argvalue0.Read(jsProt758)
		if err759 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg760 := flag.Arg(2)
		mbTrans761 := thrift.NewTMemoryBufferLen(len(arg760))
		defer mbTrans761.Close()
		_, err762 := mbTrans761.WriteString(arg760)
		if err762 != nil {
			Usage()
			return
		}
		factory763 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt764 := factory763.GetProtocol(mbTrans761)
		argvalue1 := dmp_server.NewSponsorParams()
		err765 := argvalue1.Read(jsProt764)
		if err765 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchSponsorsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorsByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorsByUids requires 2 args")
			flag.Usage()
		}
		arg766 := flag.Arg(1)
		mbTrans767 := thrift.NewTMemoryBufferLen(len(arg766))
		defer mbTrans767.Close()
		_, err768 := mbTrans767.WriteString(arg766)
		if err768 != nil {
			Usage()
			return
		}
		factory769 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt770 := factory769.GetProtocol(mbTrans767)
		argvalue0 := dmp_server.NewRequestHeader()
		err771 := argvalue0.Read(jsProt770)
		if err771 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg772 := flag.Arg(2)
		mbTrans773 := thrift.NewTMemoryBufferLen(len(arg772))
		defer mbTrans773.Close()
		_, err774 := mbTrans773.WriteString(arg772)
		if err774 != nil {
			Usage()
			return
		}
		factory775 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt776 := factory775.GetProtocol(mbTrans773)
		containerStruct1 := dmp_server.NewGetSponsorsByUidsArgs()
		err777 := containerStruct1.ReadField2(jsProt776)
		if err777 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetSponsorsByUids(value0, value1))
		fmt.Print("\n")
		break
	case "deleteSponsorsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteSponsorsByIds requires 3 args")
			flag.Usage()
		}
		arg778 := flag.Arg(1)
		mbTrans779 := thrift.NewTMemoryBufferLen(len(arg778))
		defer mbTrans779.Close()
		_, err780 := mbTrans779.WriteString(arg778)
		if err780 != nil {
			Usage()
			return
		}
		factory781 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt782 := factory781.GetProtocol(mbTrans779)
		argvalue0 := dmp_server.NewRequestHeader()
		err783 := argvalue0.Read(jsProt782)
		if err783 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err784 := (strconv.Atoi(flag.Arg(2)))
		if err784 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg785 := flag.Arg(3)
		mbTrans786 := thrift.NewTMemoryBufferLen(len(arg785))
		defer mbTrans786.Close()
		_, err787 := mbTrans786.WriteString(arg785)
		if err787 != nil {
			Usage()
			return
		}
		factory788 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt789 := factory788.GetProtocol(mbTrans786)
		containerStruct2 := dmp_server.NewDeleteSponsorsByIdsArgs()
		err790 := containerStruct2.ReadField3(jsProt789)
		if err790 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteSponsorsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "reloadOffLineCrowd":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ReloadOffLineCrowd requires 5 args")
			flag.Usage()
		}
		arg791 := flag.Arg(1)
		mbTrans792 := thrift.NewTMemoryBufferLen(len(arg791))
		defer mbTrans792.Close()
		_, err793 := mbTrans792.WriteString(arg791)
		if err793 != nil {
			Usage()
			return
		}
		factory794 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt795 := factory794.GetProtocol(mbTrans792)
		argvalue0 := dmp_server.NewRequestHeader()
		err796 := argvalue0.Read(jsProt795)
		if err796 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err797 := (strconv.Atoi(flag.Arg(2)))
		if err797 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err798 := (strconv.Atoi(flag.Arg(3)))
		if err798 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg799 := flag.Arg(4)
		mbTrans800 := thrift.NewTMemoryBufferLen(len(arg799))
		defer mbTrans800.Close()
		_, err801 := mbTrans800.WriteString(arg799)
		if err801 != nil {
			Usage()
			return
		}
		factory802 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt803 := factory802.GetProtocol(mbTrans800)
		containerStruct3 := dmp_server.NewReloadOffLineCrowdArgs()
		err804 := containerStruct3.ReadField4(jsProt803)
		if err804 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.FileAttrs
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ReloadOffLineCrowd(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getCrowdProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCrowdProfile requires 2 args")
			flag.Usage()
		}
		arg806 := flag.Arg(1)
		mbTrans807 := thrift.NewTMemoryBufferLen(len(arg806))
		defer mbTrans807.Close()
		_, err808 := mbTrans807.WriteString(arg806)
		if err808 != nil {
			Usage()
			return
		}
		factory809 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt810 := factory809.GetProtocol(mbTrans807)
		argvalue0 := dmp_server.NewRequestHeader()
		err811 := argvalue0.Read(jsProt810)
		if err811 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg812 := flag.Arg(2)
		mbTrans813 := thrift.NewTMemoryBufferLen(len(arg812))
		defer mbTrans813.Close()
		_, err814 := mbTrans813.WriteString(arg812)
		if err814 != nil {
			Usage()
			return
		}
		factory815 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt816 := factory815.GetProtocol(mbTrans813)
		containerStruct1 := dmp_server.NewGetCrowdProfileArgs()
		err817 := containerStruct1.ReadField2(jsProt816)
		if err817 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCrowdProfile(value0, value1))
		fmt.Print("\n")
		break
	case "estimateCrowd":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EstimateCrowd requires 2 args")
			flag.Usage()
		}
		arg818 := flag.Arg(1)
		mbTrans819 := thrift.NewTMemoryBufferLen(len(arg818))
		defer mbTrans819.Close()
		_, err820 := mbTrans819.WriteString(arg818)
		if err820 != nil {
			Usage()
			return
		}
		factory821 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt822 := factory821.GetProtocol(mbTrans819)
		argvalue0 := dmp_server.NewRequestHeader()
		err823 := argvalue0.Read(jsProt822)
		if err823 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg824 := flag.Arg(2)
		mbTrans825 := thrift.NewTMemoryBufferLen(len(arg824))
		defer mbTrans825.Close()
		_, err826 := mbTrans825.WriteString(arg824)
		if err826 != nil {
			Usage()
			return
		}
		factory827 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt828 := factory827.GetProtocol(mbTrans825)
		containerStruct1 := dmp_server.NewEstimateCrowdArgs()
		err829 := containerStruct1.ReadField2(jsProt828)
		if err829 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.EstimateCrowd(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
