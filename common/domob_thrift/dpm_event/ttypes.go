// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dpm_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dpm_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dpm_types.GoUnusedProtection__
var GoUnusedProtection__ int

type DPMPromotionUpdateEvent struct {
	Id int32 `thrift:"id,1" json:"id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Promotion *dpm_types.Promotion `thrift:"promotion,10" json:"promotion"`
}

func NewDPMPromotionUpdateEvent() *DPMPromotionUpdateEvent {
	return &DPMPromotionUpdateEvent{}
}

func (p *DPMPromotionUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DPMPromotionUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DPMPromotionUpdateEvent) readField10(iprot thrift.TProtocol) error {
	p.Promotion = dpm_types.NewPromotion()
	if err := p.Promotion.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Promotion)
	}
	return nil
}

func (p *DPMPromotionUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DPMPromotionUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DPMPromotionUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DPMPromotionUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Promotion != nil {
		if err := oprot.WriteFieldBegin("promotion", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:promotion: %s", p, err)
		}
		if err := p.Promotion.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Promotion)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:promotion: %s", p, err)
		}
	}
	return err
}

func (p *DPMPromotionUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DPMPromotionUpdateEvent(%+v)", *p)
}

type DPMPromotionPropertyUpdateEvent struct {
	Id          int32 `thrift:"id,1" json:"id"`
	PromotionId int32 `thrift:"promotionId,2" json:"promotionId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	PromotionProperty *dpm_types.PromotionProperty `thrift:"promotionProperty,10" json:"promotionProperty"`
}

func NewDPMPromotionPropertyUpdateEvent() *DPMPromotionPropertyUpdateEvent {
	return &DPMPromotionPropertyUpdateEvent{}
}

func (p *DPMPromotionPropertyUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DPMPromotionPropertyUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DPMPromotionPropertyUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *DPMPromotionPropertyUpdateEvent) readField10(iprot thrift.TProtocol) error {
	p.PromotionProperty = dpm_types.NewPromotionProperty()
	if err := p.PromotionProperty.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PromotionProperty)
	}
	return nil
}

func (p *DPMPromotionPropertyUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DPMPromotionPropertyUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DPMPromotionPropertyUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DPMPromotionPropertyUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:promotionId: %s", p, err)
	}
	return err
}

func (p *DPMPromotionPropertyUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.PromotionProperty != nil {
		if err := oprot.WriteFieldBegin("promotionProperty", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:promotionProperty: %s", p, err)
		}
		if err := p.PromotionProperty.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PromotionProperty)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:promotionProperty: %s", p, err)
		}
	}
	return err
}

func (p *DPMPromotionPropertyUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DPMPromotionPropertyUpdateEvent(%+v)", *p)
}

type DPMChannelUpdateEvent struct {
	Id          int32 `thrift:"id,1" json:"id"`
	PromotionId int32 `thrift:"promotionId,2" json:"promotionId"`
	CompanyId   int32 `thrift:"companyId,3" json:"companyId"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Channel *dpm_types.Channel `thrift:"channel,10" json:"channel"`
}

func NewDPMChannelUpdateEvent() *DPMChannelUpdateEvent {
	return &DPMChannelUpdateEvent{}
}

func (p *DPMChannelUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DPMChannelUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DPMChannelUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *DPMChannelUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CompanyId = v
	}
	return nil
}

func (p *DPMChannelUpdateEvent) readField10(iprot thrift.TProtocol) error {
	p.Channel = dpm_types.NewChannel()
	if err := p.Channel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Channel)
	}
	return nil
}

func (p *DPMChannelUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DPMChannelUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DPMChannelUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DPMChannelUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:promotionId: %s", p, err)
	}
	return err
}

func (p *DPMChannelUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:companyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CompanyId)); err != nil {
		return fmt.Errorf("%T.companyId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:companyId: %s", p, err)
	}
	return err
}

func (p *DPMChannelUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Channel != nil {
		if err := oprot.WriteFieldBegin("channel", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:channel: %s", p, err)
		}
		if err := p.Channel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Channel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:channel: %s", p, err)
		}
	}
	return err
}

func (p *DPMChannelUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DPMChannelUpdateEvent(%+v)", *p)
}

type DPMTrackingUpdateEvent struct {
	Id        int32 `thrift:"id,1" json:"id"`
	ChannelId int32 `thrift:"channelId,2" json:"channelId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Tracking *dpm_types.Tracking `thrift:"tracking,10" json:"tracking"`
}

func NewDPMTrackingUpdateEvent() *DPMTrackingUpdateEvent {
	return &DPMTrackingUpdateEvent{}
}

func (p *DPMTrackingUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DPMTrackingUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DPMTrackingUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *DPMTrackingUpdateEvent) readField10(iprot thrift.TProtocol) error {
	p.Tracking = dpm_types.NewTracking()
	if err := p.Tracking.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Tracking)
	}
	return nil
}

func (p *DPMTrackingUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DPMTrackingUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DPMTrackingUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DPMTrackingUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:channelId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:channelId: %s", p, err)
	}
	return err
}

func (p *DPMTrackingUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Tracking != nil {
		if err := oprot.WriteFieldBegin("tracking", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:tracking: %s", p, err)
		}
		if err := p.Tracking.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Tracking)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:tracking: %s", p, err)
		}
	}
	return err
}

func (p *DPMTrackingUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DPMTrackingUpdateEvent(%+v)", *p)
}

type DPMCompanyUpdateEvent struct {
	Id int32 `thrift:"id,1" json:"id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Company *dpm_types.Company `thrift:"company,10" json:"company"`
}

func NewDPMCompanyUpdateEvent() *DPMCompanyUpdateEvent {
	return &DPMCompanyUpdateEvent{}
}

func (p *DPMCompanyUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DPMCompanyUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DPMCompanyUpdateEvent) readField10(iprot thrift.TProtocol) error {
	p.Company = dpm_types.NewCompany()
	if err := p.Company.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Company)
	}
	return nil
}

func (p *DPMCompanyUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DPMCompanyUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DPMCompanyUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DPMCompanyUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Company != nil {
		if err := oprot.WriteFieldBegin("company", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:company: %s", p, err)
		}
		if err := p.Company.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Company)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:company: %s", p, err)
		}
	}
	return err
}

func (p *DPMCompanyUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DPMCompanyUpdateEvent(%+v)", *p)
}
