// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//预算控制倾向
type BudgetControllType int64

const (
	BudgetControllType_BCT_UNKNOWN           BudgetControllType = 0
	BudgetControllType_BCT_CONSUME_PREFERED  BudgetControllType = 1
	BudgetControllType_BCT_CONTROLL_PREFERED BudgetControllType = 2
)

func (p BudgetControllType) String() string {
	switch p {
	case BudgetControllType_BCT_UNKNOWN:
		return "BudgetControllType_BCT_UNKNOWN"
	case BudgetControllType_BCT_CONSUME_PREFERED:
		return "BudgetControllType_BCT_CONSUME_PREFERED"
	case BudgetControllType_BCT_CONTROLL_PREFERED:
		return "BudgetControllType_BCT_CONTROLL_PREFERED"
	}
	return "<UNSET>"
}

func BudgetControllTypeFromString(s string) (BudgetControllType, error) {
	switch s {
	case "BudgetControllType_BCT_UNKNOWN":
		return BudgetControllType_BCT_UNKNOWN, nil
	case "BudgetControllType_BCT_CONSUME_PREFERED":
		return BudgetControllType_BCT_CONSUME_PREFERED, nil
	case "BudgetControllType_BCT_CONTROLL_PREFERED":
		return BudgetControllType_BCT_CONTROLL_PREFERED, nil
	}
	return BudgetControllType(math.MinInt32 - 1), fmt.Errorf("not a valid BudgetControllType string")
}

//@Description("预算分配类型, 取值保持和RTB一致") *
type BudgetAllocateType int64

const (
	BudgetAllocateType_BAT_UNKNOWN      BudgetAllocateType = 0
	BudgetAllocateType_BAT_SET_PER_DAY  BudgetAllocateType = 3
	BudgetAllocateType_BAT_SET_SCHEDULE BudgetAllocateType = 4
)

func (p BudgetAllocateType) String() string {
	switch p {
	case BudgetAllocateType_BAT_UNKNOWN:
		return "BudgetAllocateType_BAT_UNKNOWN"
	case BudgetAllocateType_BAT_SET_PER_DAY:
		return "BudgetAllocateType_BAT_SET_PER_DAY"
	case BudgetAllocateType_BAT_SET_SCHEDULE:
		return "BudgetAllocateType_BAT_SET_SCHEDULE"
	}
	return "<UNSET>"
}

func BudgetAllocateTypeFromString(s string) (BudgetAllocateType, error) {
	switch s {
	case "BudgetAllocateType_BAT_UNKNOWN":
		return BudgetAllocateType_BAT_UNKNOWN, nil
	case "BudgetAllocateType_BAT_SET_PER_DAY":
		return BudgetAllocateType_BAT_SET_PER_DAY, nil
	case "BudgetAllocateType_BAT_SET_SCHEDULE":
		return BudgetAllocateType_BAT_SET_SCHEDULE, nil
	}
	return BudgetAllocateType(math.MinInt32 - 1), fmt.Errorf("not a valid BudgetAllocateType string")
}

//@Description("预算快速消耗, 是/否") *
type BudgetRapidConsume int64

const (
	BudgetRapidConsume_BRC_CLOSE BudgetRapidConsume = 0
	BudgetRapidConsume_BRC_OPEN  BudgetRapidConsume = 1
)

func (p BudgetRapidConsume) String() string {
	switch p {
	case BudgetRapidConsume_BRC_CLOSE:
		return "BudgetRapidConsume_BRC_CLOSE"
	case BudgetRapidConsume_BRC_OPEN:
		return "BudgetRapidConsume_BRC_OPEN"
	}
	return "<UNSET>"
}

func BudgetRapidConsumeFromString(s string) (BudgetRapidConsume, error) {
	switch s {
	case "BudgetRapidConsume_BRC_CLOSE":
		return BudgetRapidConsume_BRC_CLOSE, nil
	case "BudgetRapidConsume_BRC_OPEN":
		return BudgetRapidConsume_BRC_OPEN, nil
	}
	return BudgetRapidConsume(math.MinInt32 - 1), fmt.Errorf("not a valid BudgetRapidConsume string")
}

//@Description("暂停开关状态")
type PauseStatus int64

const (
	PauseStatus_PAST_RUNNABLE PauseStatus = 0
	PauseStatus_PAST_PAUSED   PauseStatus = 1
)

func (p PauseStatus) String() string {
	switch p {
	case PauseStatus_PAST_RUNNABLE:
		return "PauseStatus_PAST_RUNNABLE"
	case PauseStatus_PAST_PAUSED:
		return "PauseStatus_PAST_PAUSED"
	}
	return "<UNSET>"
}

func PauseStatusFromString(s string) (PauseStatus, error) {
	switch s {
	case "PauseStatus_PAST_RUNNABLE":
		return PauseStatus_PAST_RUNNABLE, nil
	case "PauseStatus_PAST_PAUSED":
		return PauseStatus_PAST_PAUSED, nil
	}
	return PauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PauseStatus string")
}

//活动状态开头
type CampaignStatus int64

const (
	CampaignStatus_CS_RUNNABLE CampaignStatus = 0
	CampaignStatus_CS_DELETED  CampaignStatus = 1
)

func (p CampaignStatus) String() string {
	switch p {
	case CampaignStatus_CS_RUNNABLE:
		return "CampaignStatus_CS_RUNNABLE"
	case CampaignStatus_CS_DELETED:
		return "CampaignStatus_CS_DELETED"
	}
	return "<UNSET>"
}

func CampaignStatusFromString(s string) (CampaignStatus, error) {
	switch s {
	case "CampaignStatus_CS_RUNNABLE":
		return CampaignStatus_CS_RUNNABLE, nil
	case "CampaignStatus_CS_DELETED":
		return CampaignStatus_CS_DELETED, nil
	}
	return CampaignStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CampaignStatus string")
}

//预估返点比例启用状态
type EstRebRateStatus int64

const (
	EstRebRateStatus_ERRS_CLOSE EstRebRateStatus = 0
	EstRebRateStatus_ERRS_OPEN  EstRebRateStatus = 1
)

func (p EstRebRateStatus) String() string {
	switch p {
	case EstRebRateStatus_ERRS_CLOSE:
		return "EstRebRateStatus_ERRS_CLOSE"
	case EstRebRateStatus_ERRS_OPEN:
		return "EstRebRateStatus_ERRS_OPEN"
	}
	return "<UNSET>"
}

func EstRebRateStatusFromString(s string) (EstRebRateStatus, error) {
	switch s {
	case "EstRebRateStatus_ERRS_CLOSE":
		return EstRebRateStatus_ERRS_CLOSE, nil
	case "EstRebRateStatus_ERRS_OPEN":
		return EstRebRateStatus_ERRS_OPEN, nil
	}
	return EstRebRateStatus(math.MinInt32 - 1), fmt.Errorf("not a valid EstRebRateStatus string")
}

//@Description("投放渠道类型") *
type CampaignChannelType int64

const (
	CampaignChannelType_CCT_UNKNOWN    CampaignChannelType = 0
	CampaignChannelType_CCT_ADN        CampaignChannelType = 1
	CampaignChannelType_CCT_RTB        CampaignChannelType = 2
	CampaignChannelType_CCT_IOW        CampaignChannelType = 3
	CampaignChannelType_CCT_AOW        CampaignChannelType = 4
	CampaignChannelType_CCT_CEO        CampaignChannelType = 5
	CampaignChannelType_CCT_IOW_VIDEO  CampaignChannelType = 6
	CampaignChannelType_CCT_SOS        CampaignChannelType = 7
	CampaignChannelType_CCT_GDT        CampaignChannelType = 8
	CampaignChannelType_CCT_PURCHASING CampaignChannelType = 9
	CampaignChannelType_CCT_DOS        CampaignChannelType = 10
)

func (p CampaignChannelType) String() string {
	switch p {
	case CampaignChannelType_CCT_UNKNOWN:
		return "CampaignChannelType_CCT_UNKNOWN"
	case CampaignChannelType_CCT_ADN:
		return "CampaignChannelType_CCT_ADN"
	case CampaignChannelType_CCT_RTB:
		return "CampaignChannelType_CCT_RTB"
	case CampaignChannelType_CCT_IOW:
		return "CampaignChannelType_CCT_IOW"
	case CampaignChannelType_CCT_AOW:
		return "CampaignChannelType_CCT_AOW"
	case CampaignChannelType_CCT_CEO:
		return "CampaignChannelType_CCT_CEO"
	case CampaignChannelType_CCT_IOW_VIDEO:
		return "CampaignChannelType_CCT_IOW_VIDEO"
	case CampaignChannelType_CCT_SOS:
		return "CampaignChannelType_CCT_SOS"
	case CampaignChannelType_CCT_GDT:
		return "CampaignChannelType_CCT_GDT"
	case CampaignChannelType_CCT_PURCHASING:
		return "CampaignChannelType_CCT_PURCHASING"
	case CampaignChannelType_CCT_DOS:
		return "CampaignChannelType_CCT_DOS"
	}
	return "<UNSET>"
}

func CampaignChannelTypeFromString(s string) (CampaignChannelType, error) {
	switch s {
	case "CampaignChannelType_CCT_UNKNOWN":
		return CampaignChannelType_CCT_UNKNOWN, nil
	case "CampaignChannelType_CCT_ADN":
		return CampaignChannelType_CCT_ADN, nil
	case "CampaignChannelType_CCT_RTB":
		return CampaignChannelType_CCT_RTB, nil
	case "CampaignChannelType_CCT_IOW":
		return CampaignChannelType_CCT_IOW, nil
	case "CampaignChannelType_CCT_AOW":
		return CampaignChannelType_CCT_AOW, nil
	case "CampaignChannelType_CCT_CEO":
		return CampaignChannelType_CCT_CEO, nil
	case "CampaignChannelType_CCT_IOW_VIDEO":
		return CampaignChannelType_CCT_IOW_VIDEO, nil
	case "CampaignChannelType_CCT_SOS":
		return CampaignChannelType_CCT_SOS, nil
	case "CampaignChannelType_CCT_GDT":
		return CampaignChannelType_CCT_GDT, nil
	case "CampaignChannelType_CCT_PURCHASING":
		return CampaignChannelType_CCT_PURCHASING, nil
	case "CampaignChannelType_CCT_DOS":
		return CampaignChannelType_CCT_DOS, nil
	}
	return CampaignChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid CampaignChannelType string")
}

//@Description("活动查询的执行时间状态") *
type DateStatus int64

const (
	DateStatus_DS_ALL       DateStatus = 0
	DateStatus_DS_RUNNING   DateStatus = 1
	DateStatus_DS_NOT_START DateStatus = 2
	DateStatus_DS_ENDED     DateStatus = 3
)

func (p DateStatus) String() string {
	switch p {
	case DateStatus_DS_ALL:
		return "DateStatus_DS_ALL"
	case DateStatus_DS_RUNNING:
		return "DateStatus_DS_RUNNING"
	case DateStatus_DS_NOT_START:
		return "DateStatus_DS_NOT_START"
	case DateStatus_DS_ENDED:
		return "DateStatus_DS_ENDED"
	}
	return "<UNSET>"
}

func DateStatusFromString(s string) (DateStatus, error) {
	switch s {
	case "DateStatus_DS_ALL":
		return DateStatus_DS_ALL, nil
	case "DateStatus_DS_RUNNING":
		return DateStatus_DS_RUNNING, nil
	case "DateStatus_DS_NOT_START":
		return DateStatus_DS_NOT_START, nil
	case "DateStatus_DS_ENDED":
		return DateStatus_DS_ENDED, nil
	}
	return DateStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DateStatus string")
}

//活动类型
type CampaignType int64

const (
	CampaignType_CT_UNKNOWN CampaignType = 0
	CampaignType_CT_DSP     CampaignType = 1
	CampaignType_CT_OW      CampaignType = 2
	CampaignType_CT_AGC     CampaignType = 3
	CampaignType_CT_BID     CampaignType = 4
)

func (p CampaignType) String() string {
	switch p {
	case CampaignType_CT_UNKNOWN:
		return "CampaignType_CT_UNKNOWN"
	case CampaignType_CT_DSP:
		return "CampaignType_CT_DSP"
	case CampaignType_CT_OW:
		return "CampaignType_CT_OW"
	case CampaignType_CT_AGC:
		return "CampaignType_CT_AGC"
	case CampaignType_CT_BID:
		return "CampaignType_CT_BID"
	}
	return "<UNSET>"
}

func CampaignTypeFromString(s string) (CampaignType, error) {
	switch s {
	case "CampaignType_CT_UNKNOWN":
		return CampaignType_CT_UNKNOWN, nil
	case "CampaignType_CT_DSP":
		return CampaignType_CT_DSP, nil
	case "CampaignType_CT_OW":
		return CampaignType_CT_OW, nil
	case "CampaignType_CT_AGC":
		return CampaignType_CT_AGC, nil
	case "CampaignType_CT_BID":
		return CampaignType_CT_BID, nil
	}
	return CampaignType(math.MinInt32 - 1), fmt.Errorf("not a valid CampaignType string")
}

//@Description("推广产品类型") *
type PromotionType int64

const (
	PromotionType_PT_UNKNOWN PromotionType = 0
	PromotionType_PT_IOS     PromotionType = 1
	PromotionType_PT_ANDROID PromotionType = 2
	PromotionType_PT_WEBPAGE PromotionType = 3
)

func (p PromotionType) String() string {
	switch p {
	case PromotionType_PT_UNKNOWN:
		return "PromotionType_PT_UNKNOWN"
	case PromotionType_PT_IOS:
		return "PromotionType_PT_IOS"
	case PromotionType_PT_ANDROID:
		return "PromotionType_PT_ANDROID"
	case PromotionType_PT_WEBPAGE:
		return "PromotionType_PT_WEBPAGE"
	}
	return "<UNSET>"
}

func PromotionTypeFromString(s string) (PromotionType, error) {
	switch s {
	case "PromotionType_PT_UNKNOWN":
		return PromotionType_PT_UNKNOWN, nil
	case "PromotionType_PT_IOS":
		return PromotionType_PT_IOS, nil
	case "PromotionType_PT_ANDROID":
		return PromotionType_PT_ANDROID, nil
	case "PromotionType_PT_WEBPAGE":
		return PromotionType_PT_WEBPAGE, nil
	}
	return PromotionType(math.MinInt32 - 1), fmt.Errorf("not a valid PromotionType string")
}

//活动查询排序字段枚举值
//
type OrderByField int64

const (
	OrderByField_CREATIVE_TIME OrderByField = 0
	OrderByField_DAILY_BUDGET  OrderByField = 1
	OrderByField_TOTAL_BUDGET  OrderByField = 2
)

func (p OrderByField) String() string {
	switch p {
	case OrderByField_CREATIVE_TIME:
		return "OrderByField_CREATIVE_TIME"
	case OrderByField_DAILY_BUDGET:
		return "OrderByField_DAILY_BUDGET"
	case OrderByField_TOTAL_BUDGET:
		return "OrderByField_TOTAL_BUDGET"
	}
	return "<UNSET>"
}

func OrderByFieldFromString(s string) (OrderByField, error) {
	switch s {
	case "OrderByField_CREATIVE_TIME":
		return OrderByField_CREATIVE_TIME, nil
	case "OrderByField_DAILY_BUDGET":
		return OrderByField_DAILY_BUDGET, nil
	case "OrderByField_TOTAL_BUDGET":
		return OrderByField_TOTAL_BUDGET, nil
	}
	return OrderByField(math.MinInt32 - 1), fmt.Errorf("not a valid OrderByField string")
}

type QueryParam struct {
	Status        DateStatus          `thrift:"status,1" json:"status"`
	Consultant    int32               `thrift:"consultant,2" json:"consultant"`
	CampaignName  string              `thrift:"campaignName,3" json:"campaignName"`
	CampaignIds   []int32             `thrift:"campaignIds,4" json:"campaignIds"`
	ProIds        []int32             `thrift:"proIds,5" json:"proIds"`
	SchIds        []int32             `thrift:"schIds,6" json:"schIds"`
	ChannelType   CampaignChannelType `thrift:"channelType,7" json:"channelType"`
	DepartmentId  int32               `thrift:"departmentId,8" json:"departmentId"`
	Sale          int32               `thrift:"sale,9" json:"sale"`
	Offset        int32               `thrift:"offset,10" json:"offset"`
	Limit         int32               `thrift:"limit,11" json:"limit"`
	Ascending     bool                `thrift:"ascending,12" json:"ascending"`
	DepartmentIds []int32             `thrift:"departmentIds,13" json:"departmentIds"`
	CampaignTypes []int32             `thrift:"campaignTypes,14" json:"campaignTypes"`
}

func NewQueryParam() *QueryParam {
	return &QueryParam{
		Status: math.MinInt32 - 1, // unset sentinal value

		ChannelType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryParam) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *QueryParam) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *QueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = DateStatus(v)
	}
	return nil
}

func (p *QueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Consultant = v
	}
	return nil
}

func (p *QueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CampaignName = v
	}
	return nil
}

func (p *QueryParam) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.ProIds = append(p.ProIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SchIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.SchIds = append(p.SchIds, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ChannelType = CampaignChannelType(v)
	}
	return nil
}

func (p *QueryParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DepartmentId = v
	}
	return nil
}

func (p *QueryParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Sale = v
	}
	return nil
}

func (p *QueryParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryParam) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *QueryParam) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DepartmentIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.DepartmentIds = append(p.DepartmentIds, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.CampaignTypes = append(p.CampaignTypes, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consultant", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:consultant: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Consultant)); err != nil {
		return fmt.Errorf("%T.consultant (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:consultant: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:campaignName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CampaignName)); err != nil {
		return fmt.Errorf("%T.campaignName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:campaignName: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.ProIds != nil {
		if err := oprot.WriteFieldBegin("proIds", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:proIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ProIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:proIds: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.SchIds != nil {
		if err := oprot.WriteFieldBegin("schIds", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:schIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SchIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SchIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:schIds: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannelType() {
		if err := oprot.WriteFieldBegin("channelType", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:channelType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
			return fmt.Errorf("%T.channelType (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:channelType: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("departmentId", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:departmentId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DepartmentId)); err != nil {
		return fmt.Errorf("%T.departmentId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:departmentId: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sale", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:sale: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sale)); err != nil {
		return fmt.Errorf("%T.sale (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:sale: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField13(oprot thrift.TProtocol) (err error) {
	if p.DepartmentIds != nil {
		if err := oprot.WriteFieldBegin("departmentIds", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:departmentIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DepartmentIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DepartmentIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:departmentIds: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField14(oprot thrift.TProtocol) (err error) {
	if p.CampaignTypes != nil {
		if err := oprot.WriteFieldBegin("campaignTypes", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:campaignTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:campaignTypes: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryParam(%+v)", *p)
}

type OrderParam struct {
	OrderBy OrderByField `thrift:"orderBy,1" json:"orderBy"`
	IsAsc   bool         `thrift:"isAsc,2" json:"isAsc"`
}

func NewOrderParam() *OrderParam {
	return &OrderParam{
		OrderBy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OrderParam) IsSetOrderBy() bool {
	return int64(p.OrderBy) != math.MinInt32-1
}

func (p *OrderParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OrderParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderBy = OrderByField(v)
	}
	return nil
}

func (p *OrderParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsAsc = v
	}
	return nil
}

func (p *OrderParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OrderParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OrderParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err := oprot.WriteFieldBegin("orderBy", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:orderBy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
			return fmt.Errorf("%T.orderBy (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:orderBy: %s", p, err)
		}
	}
	return err
}

func (p *OrderParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isAsc", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isAsc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAsc)); err != nil {
		return fmt.Errorf("%T.isAsc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isAsc: %s", p, err)
	}
	return err
}

func (p *OrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderParam(%+v)", *p)
}

type DSPCampaignChannel struct {
	Id          int32               `thrift:"id,1" json:"id"`
	CampaignId  int32               `thrift:"campaignId,2" json:"campaignId"`
	ChannelType CampaignChannelType `thrift:"channelType,3" json:"channelType"`
	Name        string              `thrift:"name,4" json:"name"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CreateTime int64 `thrift:"createTime,10" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,11" json:"lastupdate"`
}

func NewDSPCampaignChannel() *DSPCampaignChannel {
	return &DSPCampaignChannel{
		ChannelType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DSPCampaignChannel) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *DSPCampaignChannel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPCampaignChannel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DSPCampaignChannel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *DSPCampaignChannel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChannelType = CampaignChannelType(v)
	}
	return nil
}

func (p *DSPCampaignChannel) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DSPCampaignChannel) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DSPCampaignChannel) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DSPCampaignChannel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPCampaignChannel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPCampaignChannel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DSPCampaignChannel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaignId: %s", p, err)
	}
	return err
}

func (p *DSPCampaignChannel) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannelType() {
		if err := oprot.WriteFieldBegin("channelType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:channelType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
			return fmt.Errorf("%T.channelType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:channelType: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaignChannel) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *DSPCampaignChannel) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *DSPCampaignChannel) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastupdate: %s", p, err)
	}
	return err
}

func (p *DSPCampaignChannel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPCampaignChannel(%+v)", *p)
}

type DSPCampaignSchedule struct {
	Id         int32 `thrift:"id,1" json:"id"`
	CampaignId int32 `thrift:"campaignId,2" json:"campaignId"`
	Budget     int64 `thrift:"budget,3" json:"budget"`
	DateStart  int64 `thrift:"dateStart,4" json:"dateStart"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CreateTime int64 `thrift:"createTime,10" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,11" json:"lastupdate"`
}

func NewDSPCampaignSchedule() *DSPCampaignSchedule {
	return &DSPCampaignSchedule{}
}

func (p *DSPCampaignSchedule) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPCampaignSchedule) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DSPCampaignSchedule) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *DSPCampaignSchedule) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *DSPCampaignSchedule) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DateStart = v
	}
	return nil
}

func (p *DSPCampaignSchedule) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DSPCampaignSchedule) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DSPCampaignSchedule) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPCampaignSchedule"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPCampaignSchedule) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DSPCampaignSchedule) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaignId: %s", p, err)
	}
	return err
}

func (p *DSPCampaignSchedule) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:budget: %s", p, err)
	}
	return err
}

func (p *DSPCampaignSchedule) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dateStart", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dateStart: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DateStart)); err != nil {
		return fmt.Errorf("%T.dateStart (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dateStart: %s", p, err)
	}
	return err
}

func (p *DSPCampaignSchedule) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *DSPCampaignSchedule) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastupdate: %s", p, err)
	}
	return err
}

func (p *DSPCampaignSchedule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPCampaignSchedule(%+v)", *p)
}

type DSPCampaign struct {
	Id    int32 `thrift:"id,1" json:"id"`
	ProId int32 `thrift:"proId,2" json:"proId"`
	SchId int32 `thrift:"schId,3" json:"schId"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name               string                 `thrift:"name,10" json:"name"`
	StartTime          int64                  `thrift:"startTime,11" json:"startTime"`
	EndTime            int64                  `thrift:"endTime,12" json:"endTime"`
	TotalBudget        int64                  `thrift:"totalBudget,13" json:"totalBudget"`
	BudgetAllocateType BudgetAllocateType     `thrift:"budgetAllocateType,14" json:"budgetAllocateType"`
	BudgetType         common.BudgetType      `thrift:"budgetType,15" json:"budgetType"`
	Budget             int64                  `thrift:"budget,16" json:"budget"`
	BudgetRapidConsume BudgetRapidConsume     `thrift:"budgetRapidConsume,17" json:"budgetRapidConsume"`
	Consultant         int32                  `thrift:"consultant,18" json:"consultant"`
	DepartmentId       int32                  `thrift:"departmentId,19" json:"departmentId"`
	Channels           []*DSPCampaignChannel  `thrift:"channels,20" json:"channels"`
	Schedules          []*DSPCampaignSchedule `thrift:"schedules,21" json:"schedules"`
	NextDayBudget      int64                  `thrift:"nextDayBudget,22" json:"nextDayBudget"`
	DailyAutoBudget    bool                   `thrift:"dailyAutoBudget,23" json:"dailyAutoBudget"`
	BudgetControll     BudgetControllType     `thrift:"budgetControll,24" json:"budgetControll"`
	CampaignType       CampaignType           `thrift:"campaignType,25" json:"campaignType"`
	SaleClient         int32                  `thrift:"saleClient,26" json:"saleClient"`
	SaleAgency         int32                  `thrift:"saleAgency,27" json:"saleAgency"`
	PromotionType      PromotionType          `thrift:"promotionType,28" json:"promotionType"`
	// unused field # 29
	Paused              PauseStatus      `thrift:"paused,30" json:"paused"`
	Status              CampaignStatus   `thrift:"status,31" json:"status"`
	HasEstRebRate       EstRebRateStatus `thrift:"hasEstRebRate,32" json:"hasEstRebRate"`
	EstRebRate          int64            `thrift:"estRebRate,33" json:"estRebRate"`
	CampaignBudgetTotal int64            `thrift:"campaignBudgetTotal,34" json:"campaignBudgetTotal"`
	CampaignBudgetDaily int64            `thrift:"campaignBudgetDaily,35" json:"campaignBudgetDaily"`
	CooperateAMs        []int32          `thrift:"cooperateAMs,36" json:"cooperateAMs"`
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	CreateTime int64 `thrift:"createTime,50" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,51" json:"lastupdate"`
}

func NewDSPCampaign() *DSPCampaign {
	return &DSPCampaign{
		BudgetAllocateType: math.MinInt32 - 1, // unset sentinal value

		BudgetType: math.MinInt32 - 1, // unset sentinal value

		BudgetRapidConsume: math.MinInt32 - 1, // unset sentinal value

		BudgetControll: math.MinInt32 - 1, // unset sentinal value

		CampaignType: math.MinInt32 - 1, // unset sentinal value

		PromotionType: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		HasEstRebRate: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DSPCampaign) IsSetBudgetAllocateType() bool {
	return int64(p.BudgetAllocateType) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetBudgetType() bool {
	return int64(p.BudgetType) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetBudgetRapidConsume() bool {
	return int64(p.BudgetRapidConsume) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetBudgetControll() bool {
	return int64(p.BudgetControll) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetCampaignType() bool {
	return int64(p.CampaignType) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DSPCampaign) IsSetHasEstRebRate() bool {
	return int64(p.HasEstRebRate) != math.MinInt32-1
}

func (p *DSPCampaign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.LIST {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I64 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I64 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.LIST {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPCampaign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DSPCampaign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ProId = v
	}
	return nil
}

func (p *DSPCampaign) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SchId = v
	}
	return nil
}

func (p *DSPCampaign) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DSPCampaign) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *DSPCampaign) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *DSPCampaign) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *DSPCampaign) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.BudgetAllocateType = BudgetAllocateType(v)
	}
	return nil
}

func (p *DSPCampaign) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.BudgetType = common.BudgetType(v)
	}
	return nil
}

func (p *DSPCampaign) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *DSPCampaign) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.BudgetRapidConsume = BudgetRapidConsume(v)
	}
	return nil
}

func (p *DSPCampaign) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Consultant = v
	}
	return nil
}

func (p *DSPCampaign) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.DepartmentId = v
	}
	return nil
}

func (p *DSPCampaign) readField20(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Channels = make([]*DSPCampaignChannel, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewDSPCampaignChannel()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Channels = append(p.Channels, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPCampaign) readField21(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Schedules = make([]*DSPCampaignSchedule, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := NewDSPCampaignSchedule()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.Schedules = append(p.Schedules, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPCampaign) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.NextDayBudget = v
	}
	return nil
}

func (p *DSPCampaign) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.DailyAutoBudget = v
	}
	return nil
}

func (p *DSPCampaign) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.BudgetControll = BudgetControllType(v)
	}
	return nil
}

func (p *DSPCampaign) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.CampaignType = CampaignType(v)
	}
	return nil
}

func (p *DSPCampaign) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.SaleClient = v
	}
	return nil
}

func (p *DSPCampaign) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.SaleAgency = v
	}
	return nil
}

func (p *DSPCampaign) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.PromotionType = PromotionType(v)
	}
	return nil
}

func (p *DSPCampaign) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Paused = PauseStatus(v)
	}
	return nil
}

func (p *DSPCampaign) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = CampaignStatus(v)
	}
	return nil
}

func (p *DSPCampaign) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.HasEstRebRate = EstRebRateStatus(v)
	}
	return nil
}

func (p *DSPCampaign) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.EstRebRate = v
	}
	return nil
}

func (p *DSPCampaign) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.CampaignBudgetTotal = v
	}
	return nil
}

func (p *DSPCampaign) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CampaignBudgetDaily = v
	}
	return nil
}

func (p *DSPCampaign) readField36(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CooperateAMs = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.CooperateAMs = append(p.CooperateAMs, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DSPCampaign) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DSPCampaign) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DSPCampaign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPCampaign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPCampaign) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("proId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:proId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProId)); err != nil {
		return fmt.Errorf("%T.proId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:proId: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("schId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:schId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SchId)); err != nil {
		return fmt.Errorf("%T.schId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:schId: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:startTime: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:endTime: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:totalBudget: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetBudgetAllocateType() {
		if err := oprot.WriteFieldBegin("budgetAllocateType", thrift.I32, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:budgetAllocateType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BudgetAllocateType)); err != nil {
			return fmt.Errorf("%T.budgetAllocateType (14) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:budgetAllocateType: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budgetType", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:budgetType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BudgetType)); err != nil {
		return fmt.Errorf("%T.budgetType (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:budgetType: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:budget: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetBudgetRapidConsume() {
		if err := oprot.WriteFieldBegin("budgetRapidConsume", thrift.I32, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:budgetRapidConsume: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BudgetRapidConsume)); err != nil {
			return fmt.Errorf("%T.budgetRapidConsume (17) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:budgetRapidConsume: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consultant", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:consultant: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Consultant)); err != nil {
		return fmt.Errorf("%T.consultant (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:consultant: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("departmentId", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:departmentId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DepartmentId)); err != nil {
		return fmt.Errorf("%T.departmentId (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:departmentId: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Channels != nil {
		if err := oprot.WriteFieldBegin("channels", thrift.LIST, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:channels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Channels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Channels {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:channels: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField21(oprot thrift.TProtocol) (err error) {
	if p.Schedules != nil {
		if err := oprot.WriteFieldBegin("schedules", thrift.LIST, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:schedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Schedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Schedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:schedules: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nextDayBudget", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:nextDayBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.NextDayBudget)); err != nil {
		return fmt.Errorf("%T.nextDayBudget (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:nextDayBudget: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyAutoBudget", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:dailyAutoBudget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.DailyAutoBudget)); err != nil {
		return fmt.Errorf("%T.dailyAutoBudget (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:dailyAutoBudget: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetBudgetControll() {
		if err := oprot.WriteFieldBegin("budgetControll", thrift.I32, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:budgetControll: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BudgetControll)); err != nil {
			return fmt.Errorf("%T.budgetControll (24) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:budgetControll: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField25(oprot thrift.TProtocol) (err error) {
	if p.IsSetCampaignType() {
		if err := oprot.WriteFieldBegin("campaignType", thrift.I32, 25); err != nil {
			return fmt.Errorf("%T write field begin error 25:campaignType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CampaignType)); err != nil {
			return fmt.Errorf("%T.campaignType (25) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 25:campaignType: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("saleClient", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:saleClient: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SaleClient)); err != nil {
		return fmt.Errorf("%T.saleClient (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:saleClient: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("saleAgency", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:saleAgency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SaleAgency)); err != nil {
		return fmt.Errorf("%T.saleAgency (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:saleAgency: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField28(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 28); err != nil {
			return fmt.Errorf("%T write field begin error 28:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (28) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 28:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:paused: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:status: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetHasEstRebRate() {
		if err := oprot.WriteFieldBegin("hasEstRebRate", thrift.I32, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:hasEstRebRate: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.HasEstRebRate)); err != nil {
			return fmt.Errorf("%T.hasEstRebRate (32) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:hasEstRebRate: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("estRebRate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:estRebRate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EstRebRate)); err != nil {
		return fmt.Errorf("%T.estRebRate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:estRebRate: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignBudgetTotal", thrift.I64, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:campaignBudgetTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CampaignBudgetTotal)); err != nil {
		return fmt.Errorf("%T.campaignBudgetTotal (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:campaignBudgetTotal: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignBudgetDaily", thrift.I64, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:campaignBudgetDaily: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CampaignBudgetDaily)); err != nil {
		return fmt.Errorf("%T.campaignBudgetDaily (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:campaignBudgetDaily: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField36(oprot thrift.TProtocol) (err error) {
	if p.CooperateAMs != nil {
		if err := oprot.WriteFieldBegin("cooperateAMs", thrift.LIST, 36); err != nil {
			return fmt.Errorf("%T write field begin error 36:cooperateAMs: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CooperateAMs)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CooperateAMs {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 36:cooperateAMs: %s", p, err)
		}
	}
	return err
}

func (p *DSPCampaign) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:createTime: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:lastupdate: %s", p, err)
	}
	return err
}

func (p *DSPCampaign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPCampaign(%+v)", *p)
}

type DSPAffiliate struct {
	AffId      int32               `thrift:"affId,1" json:"affId"`
	AffType    CampaignChannelType `thrift:"affType,2" json:"affType"`
	ExternalId int32               `thrift:"externalId,3" json:"externalId"`
	Name       string              `thrift:"name,4" json:"name"`
	Createtime int64               `thrift:"createtime,5" json:"createtime"`
	Lastupdate int64               `thrift:"lastupdate,6" json:"lastupdate"`
}

func NewDSPAffiliate() *DSPAffiliate {
	return &DSPAffiliate{
		AffType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DSPAffiliate) IsSetAffType() bool {
	return int64(p.AffType) != math.MinInt32-1
}

func (p *DSPAffiliate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DSPAffiliate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AffId = v
	}
	return nil
}

func (p *DSPAffiliate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AffType = CampaignChannelType(v)
	}
	return nil
}

func (p *DSPAffiliate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExternalId = v
	}
	return nil
}

func (p *DSPAffiliate) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DSPAffiliate) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Createtime = v
	}
	return nil
}

func (p *DSPAffiliate) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DSPAffiliate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DSPAffiliate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DSPAffiliate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("affId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:affId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AffId)); err != nil {
		return fmt.Errorf("%T.affId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:affId: %s", p, err)
	}
	return err
}

func (p *DSPAffiliate) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAffType() {
		if err := oprot.WriteFieldBegin("affType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:affType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AffType)); err != nil {
			return fmt.Errorf("%T.affType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:affType: %s", p, err)
		}
	}
	return err
}

func (p *DSPAffiliate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("externalId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:externalId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExternalId)); err != nil {
		return fmt.Errorf("%T.externalId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:externalId: %s", p, err)
	}
	return err
}

func (p *DSPAffiliate) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *DSPAffiliate) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createtime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:createtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Createtime)); err != nil {
		return fmt.Errorf("%T.createtime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:createtime: %s", p, err)
	}
	return err
}

func (p *DSPAffiliate) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:lastupdate: %s", p, err)
	}
	return err
}

func (p *DSPAffiliate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DSPAffiliate(%+v)", *p)
}

type LinkPayParams struct {
	AppId        int32  `thrift:"appId,1" json:"appId"`
	TrackingUrl  string `thrift:"trackingUrl,2" json:"trackingUrl"`
	TrackingName string `thrift:"trackingName,3" json:"trackingName"`
	StartDate    int64  `thrift:"startDate,4" json:"startDate"`
	EndDate      int64  `thrift:"endDate,5" json:"endDate"`
}

func NewLinkPayParams() *LinkPayParams {
	return &LinkPayParams{}
}

func (p *LinkPayParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LinkPayParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *LinkPayParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TrackingUrl = v
	}
	return nil
}

func (p *LinkPayParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TrackingName = v
	}
	return nil
}

func (p *LinkPayParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartDate = v
	}
	return nil
}

func (p *LinkPayParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndDate = v
	}
	return nil
}

func (p *LinkPayParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LinkPayParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LinkPayParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appId: %s", p, err)
	}
	return err
}

func (p *LinkPayParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingUrl", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:trackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackingUrl)); err != nil {
		return fmt.Errorf("%T.trackingUrl (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:trackingUrl: %s", p, err)
	}
	return err
}

func (p *LinkPayParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:trackingName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackingName)); err != nil {
		return fmt.Errorf("%T.trackingName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:trackingName: %s", p, err)
	}
	return err
}

func (p *LinkPayParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startDate", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartDate)); err != nil {
		return fmt.Errorf("%T.startDate (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startDate: %s", p, err)
	}
	return err
}

func (p *LinkPayParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endDate", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:endDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndDate)); err != nil {
		return fmt.Errorf("%T.endDate (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:endDate: %s", p, err)
	}
	return err
}

func (p *LinkPayParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LinkPayParams(%+v)", *p)
}

type LinkPay struct {
	Id            int32  `thrift:"id,1" json:"id"`
	AppId         int32  `thrift:"appId,2" json:"appId"`
	TrackingUrl   string `thrift:"trackingUrl,3" json:"trackingUrl"`
	TrackingName  string `thrift:"trackingName,4" json:"trackingName"`
	StartDate     int64  `thrift:"startDate,5" json:"startDate"`
	EndDate       int64  `thrift:"endDate,6" json:"endDate"`
	Ltv0          string `thrift:"ltv0,7" json:"ltv0"`
	Ltv7          string `thrift:"ltv7,8" json:"ltv7"`
	IntervalPrice string `thrift:"intervalPrice,9" json:"intervalPrice"`
	YesUsers      string `thrift:"yesUsers,10" json:"yesUsers"`
	YesUserRatio  string `thrift:"yesUserRatio,11" json:"yesUserRatio"`
	WeekUsers     string `thrift:"weekUsers,12" json:"weekUsers"`
	WeekUserRatio string `thrift:"weekUserRatio,13" json:"weekUserRatio"`
	PayUsers      string `thrift:"payUsers,14" json:"payUsers"`
	PayPrice      string `thrift:"payPrice,15" json:"payPrice"`
	PayRatio      string `thrift:"payRatio,16" json:"payRatio"`
	Roi           string `thrift:"roi,17" json:"roi"`
	Arpu          string `thrift:"arpu,18" json:"arpu"`
	CreateTime    int64  `thrift:"createTime,19" json:"createTime"`
	LastUpdate    int64  `thrift:"lastUpdate,20" json:"lastUpdate"`
}

func NewLinkPay() *LinkPay {
	return &LinkPay{}
}

func (p *LinkPay) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LinkPay) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LinkPay) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *LinkPay) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TrackingUrl = v
	}
	return nil
}

func (p *LinkPay) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TrackingName = v
	}
	return nil
}

func (p *LinkPay) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartDate = v
	}
	return nil
}

func (p *LinkPay) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndDate = v
	}
	return nil
}

func (p *LinkPay) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ltv0 = v
	}
	return nil
}

func (p *LinkPay) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Ltv7 = v
	}
	return nil
}

func (p *LinkPay) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.IntervalPrice = v
	}
	return nil
}

func (p *LinkPay) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.YesUsers = v
	}
	return nil
}

func (p *LinkPay) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.YesUserRatio = v
	}
	return nil
}

func (p *LinkPay) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.WeekUsers = v
	}
	return nil
}

func (p *LinkPay) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.WeekUserRatio = v
	}
	return nil
}

func (p *LinkPay) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PayUsers = v
	}
	return nil
}

func (p *LinkPay) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PayPrice = v
	}
	return nil
}

func (p *LinkPay) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.PayRatio = v
	}
	return nil
}

func (p *LinkPay) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Roi = v
	}
	return nil
}

func (p *LinkPay) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Arpu = v
	}
	return nil
}

func (p *LinkPay) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *LinkPay) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *LinkPay) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LinkPay"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LinkPay) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appId: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingUrl", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:trackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackingUrl)); err != nil {
		return fmt.Errorf("%T.trackingUrl (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:trackingUrl: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:trackingName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackingName)); err != nil {
		return fmt.Errorf("%T.trackingName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:trackingName: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startDate", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartDate)); err != nil {
		return fmt.Errorf("%T.startDate (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startDate: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endDate", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndDate)); err != nil {
		return fmt.Errorf("%T.endDate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endDate: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ltv0", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ltv0: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ltv0)); err != nil {
		return fmt.Errorf("%T.ltv0 (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ltv0: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ltv7", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ltv7: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ltv7)); err != nil {
		return fmt.Errorf("%T.ltv7 (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ltv7: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("intervalPrice", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:intervalPrice: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IntervalPrice)); err != nil {
		return fmt.Errorf("%T.intervalPrice (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:intervalPrice: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("yesUsers", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:yesUsers: %s", p, err)
	}
	if err := oprot.WriteString(string(p.YesUsers)); err != nil {
		return fmt.Errorf("%T.yesUsers (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:yesUsers: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("yesUserRatio", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:yesUserRatio: %s", p, err)
	}
	if err := oprot.WriteString(string(p.YesUserRatio)); err != nil {
		return fmt.Errorf("%T.yesUserRatio (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:yesUserRatio: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weekUsers", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:weekUsers: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WeekUsers)); err != nil {
		return fmt.Errorf("%T.weekUsers (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:weekUsers: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weekUserRatio", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:weekUserRatio: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WeekUserRatio)); err != nil {
		return fmt.Errorf("%T.weekUserRatio (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:weekUserRatio: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("payUsers", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:payUsers: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PayUsers)); err != nil {
		return fmt.Errorf("%T.payUsers (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:payUsers: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("payPrice", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:payPrice: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PayPrice)); err != nil {
		return fmt.Errorf("%T.payPrice (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:payPrice: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("payRatio", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:payRatio: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PayRatio)); err != nil {
		return fmt.Errorf("%T.payRatio (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:payRatio: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("roi", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:roi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Roi)); err != nil {
		return fmt.Errorf("%T.roi (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:roi: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("arpu", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:arpu: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Arpu)); err != nil {
		return fmt.Errorf("%T.arpu (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:arpu: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:createTime: %s", p, err)
	}
	return err
}

func (p *LinkPay) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:lastUpdate: %s", p, err)
	}
	return err
}

func (p *LinkPay) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LinkPay(%+v)", *p)
}
