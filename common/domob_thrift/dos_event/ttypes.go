// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dos_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

type DosEventType int64

const (
	DosEventType_DET_UNKNOWN                  DosEventType = 0
	DosEventType_DET_MANUAL_MODIFY_DAILY_DATA DosEventType = 1
	DosEventType_DET_UPLOAD_MODIFY_DAILY_DATA DosEventType = 2
)

func (p DosEventType) String() string {
	switch p {
	case DosEventType_DET_UNKNOWN:
		return "DosEventType_DET_UNKNOWN"
	case DosEventType_DET_MANUAL_MODIFY_DAILY_DATA:
		return "DosEventType_DET_MANUAL_MODIFY_DAILY_DATA"
	case DosEventType_DET_UPLOAD_MODIFY_DAILY_DATA:
		return "DosEventType_DET_UPLOAD_MODIFY_DAILY_DATA"
	}
	return "<UNSET>"
}

func DosEventTypeFromString(s string) (DosEventType, error) {
	switch s {
	case "DosEventType_DET_UNKNOWN":
		return DosEventType_DET_UNKNOWN, nil
	case "DosEventType_DET_MANUAL_MODIFY_DAILY_DATA":
		return DosEventType_DET_MANUAL_MODIFY_DAILY_DATA, nil
	case "DosEventType_DET_UPLOAD_MODIFY_DAILY_DATA":
		return DosEventType_DET_UPLOAD_MODIFY_DAILY_DATA, nil
	}
	return DosEventType(math.MinInt32 - 1), fmt.Errorf("not a valid DosEventType string")
}

type DosEventCategory int64

const (
	DosEventCategory_DEC_UNKNOWN  DosEventCategory = 0
	DosEventCategory_DEC_PLAN     DosEventCategory = 1
	DosEventCategory_DEC_STRATEGY DosEventCategory = 2
	DosEventCategory_DEC_CREATIVE DosEventCategory = 3
)

func (p DosEventCategory) String() string {
	switch p {
	case DosEventCategory_DEC_UNKNOWN:
		return "DosEventCategory_DEC_UNKNOWN"
	case DosEventCategory_DEC_PLAN:
		return "DosEventCategory_DEC_PLAN"
	case DosEventCategory_DEC_STRATEGY:
		return "DosEventCategory_DEC_STRATEGY"
	case DosEventCategory_DEC_CREATIVE:
		return "DosEventCategory_DEC_CREATIVE"
	}
	return "<UNSET>"
}

func DosEventCategoryFromString(s string) (DosEventCategory, error) {
	switch s {
	case "DosEventCategory_DEC_UNKNOWN":
		return DosEventCategory_DEC_UNKNOWN, nil
	case "DosEventCategory_DEC_PLAN":
		return DosEventCategory_DEC_PLAN, nil
	case "DosEventCategory_DEC_STRATEGY":
		return DosEventCategory_DEC_STRATEGY, nil
	case "DosEventCategory_DEC_CREATIVE":
		return DosEventCategory_DEC_CREATIVE, nil
	}
	return DosEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid DosEventCategory string")
}

type DosCommonEvent struct {
	TypeA1   DosEventType     `thrift:"type,1" json:"type"`
	Category DosEventCategory `thrift:"category,2" json:"category"`
	Ids      []int32          `thrift:"ids,3" json:"ids"`
}

func NewDosCommonEvent() *DosCommonEvent {
	return &DosCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DosCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DosCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DosCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DosCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DosEventType(v)
	}
	return nil
}

func (p *DosCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DosEventCategory(v)
	}
	return nil
}

func (p *DosCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DosCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DosCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DosCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DosCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DosCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DosCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DosCommonEvent(%+v)", *p)
}

type DosStatsEvent struct {
	TypeA1     DosEventType     `thrift:"type,1" json:"type"`
	Category   DosEventCategory `thrift:"category,2" json:"category"`
	CampaignId int32            `thrift:"campaignId,3" json:"campaignId"`
	PlanId     int32            `thrift:"planId,4" json:"planId"`
	Dt         int64            `thrift:"dt,5" json:"dt"`
	ChnId      int32            `thrift:"chnId,6" json:"chnId"`
}

func NewDosStatsEvent() *DosStatsEvent {
	return &DosStatsEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DosStatsEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DosStatsEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DosStatsEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DosStatsEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DosEventType(v)
	}
	return nil
}

func (p *DosStatsEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DosEventCategory(v)
	}
	return nil
}

func (p *DosStatsEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *DosStatsEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DosStatsEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *DosStatsEvent) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *DosStatsEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DosStatsEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DosStatsEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DosStatsEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DosStatsEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:campaignId: %s", p, err)
	}
	return err
}

func (p *DosStatsEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:planId: %s", p, err)
	}
	return err
}

func (p *DosStatsEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:dt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:dt: %s", p, err)
	}
	return err
}

func (p *DosStatsEvent) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:chnId: %s", p, err)
	}
	return err
}

func (p *DosStatsEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DosStatsEvent(%+v)", *p)
}
