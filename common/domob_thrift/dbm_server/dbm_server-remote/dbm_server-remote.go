// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dbm_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addSponsor(RequestHeader header, SponsorProfile sponsor)")
	fmt.Fprintln(os.Stderr, "  i32 addSponsorWithPassword(RequestHeader header, SponsorProfile sponsor, string password)")
	fmt.Fprintln(os.<PERSON>der<PERSON>, "  void editSponsor(RequestHeader header, i32 agentUid, SponsorProfile sponsor)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchSponsorsByParams(RequestHeader header, SponsorParams params)")
	fmt.Fprintln(os.Stderr, "   getSponsorsByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void innerApproveSponsor(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  i32 addAdOrder(RequestHeader header, i32 agentUid, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "  void editAdOrder(RequestHeader header, i32 agentUid, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "   getAdOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdOrdersByParams(RequestHeader header, AdOrderParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdOrdersByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdOrdersByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdOrderByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdCampaign(RequestHeader header, i32 agentUid, AdCampaign campaign)")
	fmt.Fprintln(os.Stderr, "  void editAdCampaign(RequestHeader header, i32 agentUid, AdCampaign campaign)")
	fmt.Fprintln(os.Stderr, "   getAdCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdCampaignByParams(RequestHeader header, AdCampaignParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCampaignsByIds(RequestHeader header, i32 sponsorId, i32 orderId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCampaignsByIds(RequestHeader header, i32 sponsorId, i32 orderId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCampaignsByIds(RequestHeader header, i32 sponsorId, i32 orderId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdStrategy(RequestHeader header, i32 agentUid, AdStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editAdStrategy(RequestHeader header, i32 agentUid, AdStrategy strategy)")
	fmt.Fprintln(os.Stderr, "   getAdStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdStrategyByParams(RequestHeader header, AdStrategyParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 orderId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 orderId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 orderId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdCreative(RequestHeader header, i32 agentUid, AdCreative creative)")
	fmt.Fprintln(os.Stderr, "  void editAdCreative(RequestHeader header, i32 agentUid, AdCreative creative)")
	fmt.Fprintln(os.Stderr, "   getAdCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdCreativeByParams(RequestHeader header, AdCreativeParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 orderId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 orderId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 orderId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void innerApproveAdCreatives(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  void editCampaignMinProfitRatio(RequestHeader header,  campaignIds, i32 minProfitRatio)")
	fmt.Fprintln(os.Stderr, "   getOrderIdsByProjectIds(RequestHeader header,  projectIds)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dbm_server.NewDbmServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSponsor requires 2 args")
			flag.Usage()
		}
		arg185 := flag.Arg(1)
		mbTrans186 := thrift.NewTMemoryBufferLen(len(arg185))
		defer mbTrans186.Close()
		_, err187 := mbTrans186.WriteString(arg185)
		if err187 != nil {
			Usage()
			return
		}
		factory188 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt189 := factory188.GetProtocol(mbTrans186)
		argvalue0 := dbm_server.NewRequestHeader()
		err190 := argvalue0.Read(jsProt189)
		if err190 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg191 := flag.Arg(2)
		mbTrans192 := thrift.NewTMemoryBufferLen(len(arg191))
		defer mbTrans192.Close()
		_, err193 := mbTrans192.WriteString(arg191)
		if err193 != nil {
			Usage()
			return
		}
		factory194 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt195 := factory194.GetProtocol(mbTrans192)
		argvalue1 := dbm_server.NewSponsorProfile()
		err196 := argvalue1.Read(jsProt195)
		if err196 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "addSponsorWithPassword":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddSponsorWithPassword requires 3 args")
			flag.Usage()
		}
		arg197 := flag.Arg(1)
		mbTrans198 := thrift.NewTMemoryBufferLen(len(arg197))
		defer mbTrans198.Close()
		_, err199 := mbTrans198.WriteString(arg197)
		if err199 != nil {
			Usage()
			return
		}
		factory200 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt201 := factory200.GetProtocol(mbTrans198)
		argvalue0 := dbm_server.NewRequestHeader()
		err202 := argvalue0.Read(jsProt201)
		if err202 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg203 := flag.Arg(2)
		mbTrans204 := thrift.NewTMemoryBufferLen(len(arg203))
		defer mbTrans204.Close()
		_, err205 := mbTrans204.WriteString(arg203)
		if err205 != nil {
			Usage()
			return
		}
		factory206 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt207 := factory206.GetProtocol(mbTrans204)
		argvalue1 := dbm_server.NewSponsorProfile()
		err208 := argvalue1.Read(jsProt207)
		if err208 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.AddSponsorWithPassword(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editSponsor":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSponsor requires 3 args")
			flag.Usage()
		}
		arg210 := flag.Arg(1)
		mbTrans211 := thrift.NewTMemoryBufferLen(len(arg210))
		defer mbTrans211.Close()
		_, err212 := mbTrans211.WriteString(arg210)
		if err212 != nil {
			Usage()
			return
		}
		factory213 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt214 := factory213.GetProtocol(mbTrans211)
		argvalue0 := dbm_server.NewRequestHeader()
		err215 := argvalue0.Read(jsProt214)
		if err215 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err216 := (strconv.Atoi(flag.Arg(2)))
		if err216 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg217 := flag.Arg(3)
		mbTrans218 := thrift.NewTMemoryBufferLen(len(arg217))
		defer mbTrans218.Close()
		_, err219 := mbTrans218.WriteString(arg217)
		if err219 != nil {
			Usage()
			return
		}
		factory220 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt221 := factory220.GetProtocol(mbTrans218)
		argvalue2 := dbm_server.NewSponsorProfile()
		err222 := argvalue2.Read(jsProt221)
		if err222 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditSponsor(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchSponsorsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchSponsorsByParams requires 2 args")
			flag.Usage()
		}
		arg223 := flag.Arg(1)
		mbTrans224 := thrift.NewTMemoryBufferLen(len(arg223))
		defer mbTrans224.Close()
		_, err225 := mbTrans224.WriteString(arg223)
		if err225 != nil {
			Usage()
			return
		}
		factory226 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt227 := factory226.GetProtocol(mbTrans224)
		argvalue0 := dbm_server.NewRequestHeader()
		err228 := argvalue0.Read(jsProt227)
		if err228 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg229 := flag.Arg(2)
		mbTrans230 := thrift.NewTMemoryBufferLen(len(arg229))
		defer mbTrans230.Close()
		_, err231 := mbTrans230.WriteString(arg229)
		if err231 != nil {
			Usage()
			return
		}
		factory232 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt233 := factory232.GetProtocol(mbTrans230)
		argvalue1 := dbm_server.NewSponsorParams()
		err234 := argvalue1.Read(jsProt233)
		if err234 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchSponsorsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorsByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorsByUids requires 2 args")
			flag.Usage()
		}
		arg235 := flag.Arg(1)
		mbTrans236 := thrift.NewTMemoryBufferLen(len(arg235))
		defer mbTrans236.Close()
		_, err237 := mbTrans236.WriteString(arg235)
		if err237 != nil {
			Usage()
			return
		}
		factory238 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt239 := factory238.GetProtocol(mbTrans236)
		argvalue0 := dbm_server.NewRequestHeader()
		err240 := argvalue0.Read(jsProt239)
		if err240 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg241 := flag.Arg(2)
		mbTrans242 := thrift.NewTMemoryBufferLen(len(arg241))
		defer mbTrans242.Close()
		_, err243 := mbTrans242.WriteString(arg241)
		if err243 != nil {
			Usage()
			return
		}
		factory244 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt245 := factory244.GetProtocol(mbTrans242)
		containerStruct1 := dbm_server.NewGetSponsorsByUidsArgs()
		err246 := containerStruct1.ReadField2(jsProt245)
		if err246 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetSponsorsByUids(value0, value1))
		fmt.Print("\n")
		break
	case "innerApproveSponsor":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveSponsor requires 3 args")
			flag.Usage()
		}
		arg247 := flag.Arg(1)
		mbTrans248 := thrift.NewTMemoryBufferLen(len(arg247))
		defer mbTrans248.Close()
		_, err249 := mbTrans248.WriteString(arg247)
		if err249 != nil {
			Usage()
			return
		}
		factory250 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt251 := factory250.GetProtocol(mbTrans248)
		argvalue0 := dbm_server.NewRequestHeader()
		err252 := argvalue0.Read(jsProt251)
		if err252 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err253 := (strconv.Atoi(flag.Arg(2)))
		if err253 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg254 := flag.Arg(3)
		mbTrans255 := thrift.NewTMemoryBufferLen(len(arg254))
		defer mbTrans255.Close()
		_, err256 := mbTrans255.WriteString(arg254)
		if err256 != nil {
			Usage()
			return
		}
		factory257 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt258 := factory257.GetProtocol(mbTrans255)
		containerStruct2 := dbm_server.NewInnerApproveSponsorArgs()
		err259 := containerStruct2.ReadField3(jsProt258)
		if err259 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveSponsor(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdOrder requires 3 args")
			flag.Usage()
		}
		arg260 := flag.Arg(1)
		mbTrans261 := thrift.NewTMemoryBufferLen(len(arg260))
		defer mbTrans261.Close()
		_, err262 := mbTrans261.WriteString(arg260)
		if err262 != nil {
			Usage()
			return
		}
		factory263 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt264 := factory263.GetProtocol(mbTrans261)
		argvalue0 := dbm_server.NewRequestHeader()
		err265 := argvalue0.Read(jsProt264)
		if err265 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err266 := (strconv.Atoi(flag.Arg(2)))
		if err266 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg267 := flag.Arg(3)
		mbTrans268 := thrift.NewTMemoryBufferLen(len(arg267))
		defer mbTrans268.Close()
		_, err269 := mbTrans268.WriteString(arg267)
		if err269 != nil {
			Usage()
			return
		}
		factory270 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt271 := factory270.GetProtocol(mbTrans268)
		argvalue2 := dbm_server.NewAdOrder()
		err272 := argvalue2.Read(jsProt271)
		if err272 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdOrder requires 3 args")
			flag.Usage()
		}
		arg273 := flag.Arg(1)
		mbTrans274 := thrift.NewTMemoryBufferLen(len(arg273))
		defer mbTrans274.Close()
		_, err275 := mbTrans274.WriteString(arg273)
		if err275 != nil {
			Usage()
			return
		}
		factory276 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt277 := factory276.GetProtocol(mbTrans274)
		argvalue0 := dbm_server.NewRequestHeader()
		err278 := argvalue0.Read(jsProt277)
		if err278 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err279 := (strconv.Atoi(flag.Arg(2)))
		if err279 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg280 := flag.Arg(3)
		mbTrans281 := thrift.NewTMemoryBufferLen(len(arg280))
		defer mbTrans281.Close()
		_, err282 := mbTrans281.WriteString(arg280)
		if err282 != nil {
			Usage()
			return
		}
		factory283 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt284 := factory283.GetProtocol(mbTrans281)
		argvalue2 := dbm_server.NewAdOrder()
		err285 := argvalue2.Read(jsProt284)
		if err285 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg286 := flag.Arg(1)
		mbTrans287 := thrift.NewTMemoryBufferLen(len(arg286))
		defer mbTrans287.Close()
		_, err288 := mbTrans287.WriteString(arg286)
		if err288 != nil {
			Usage()
			return
		}
		factory289 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt290 := factory289.GetProtocol(mbTrans287)
		argvalue0 := dbm_server.NewRequestHeader()
		err291 := argvalue0.Read(jsProt290)
		if err291 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg292 := flag.Arg(2)
		mbTrans293 := thrift.NewTMemoryBufferLen(len(arg292))
		defer mbTrans293.Close()
		_, err294 := mbTrans293.WriteString(arg292)
		if err294 != nil {
			Usage()
			return
		}
		factory295 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt296 := factory295.GetProtocol(mbTrans293)
		containerStruct1 := dbm_server.NewGetAdOrdersByIdsArgs()
		err297 := containerStruct1.ReadField2(jsProt296)
		if err297 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdOrdersByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdOrdersByParams requires 2 args")
			flag.Usage()
		}
		arg298 := flag.Arg(1)
		mbTrans299 := thrift.NewTMemoryBufferLen(len(arg298))
		defer mbTrans299.Close()
		_, err300 := mbTrans299.WriteString(arg298)
		if err300 != nil {
			Usage()
			return
		}
		factory301 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt302 := factory301.GetProtocol(mbTrans299)
		argvalue0 := dbm_server.NewRequestHeader()
		err303 := argvalue0.Read(jsProt302)
		if err303 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg304 := flag.Arg(2)
		mbTrans305 := thrift.NewTMemoryBufferLen(len(arg304))
		defer mbTrans305.Close()
		_, err306 := mbTrans305.WriteString(arg304)
		if err306 != nil {
			Usage()
			return
		}
		factory307 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt308 := factory307.GetProtocol(mbTrans305)
		argvalue1 := dbm_server.NewAdOrderParams()
		err309 := argvalue1.Read(jsProt308)
		if err309 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdOrdersByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdOrdersByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseAdOrdersByIds requires 3 args")
			flag.Usage()
		}
		arg310 := flag.Arg(1)
		mbTrans311 := thrift.NewTMemoryBufferLen(len(arg310))
		defer mbTrans311.Close()
		_, err312 := mbTrans311.WriteString(arg310)
		if err312 != nil {
			Usage()
			return
		}
		factory313 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt314 := factory313.GetProtocol(mbTrans311)
		argvalue0 := dbm_server.NewRequestHeader()
		err315 := argvalue0.Read(jsProt314)
		if err315 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err316 := (strconv.Atoi(flag.Arg(2)))
		if err316 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg317 := flag.Arg(3)
		mbTrans318 := thrift.NewTMemoryBufferLen(len(arg317))
		defer mbTrans318.Close()
		_, err319 := mbTrans318.WriteString(arg317)
		if err319 != nil {
			Usage()
			return
		}
		factory320 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt321 := factory320.GetProtocol(mbTrans318)
		containerStruct2 := dbm_server.NewPauseAdOrdersByIdsArgs()
		err322 := containerStruct2.ReadField3(jsProt321)
		if err322 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.PauseAdOrdersByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeAdOrdersByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeAdOrdersByIds requires 3 args")
			flag.Usage()
		}
		arg323 := flag.Arg(1)
		mbTrans324 := thrift.NewTMemoryBufferLen(len(arg323))
		defer mbTrans324.Close()
		_, err325 := mbTrans324.WriteString(arg323)
		if err325 != nil {
			Usage()
			return
		}
		factory326 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt327 := factory326.GetProtocol(mbTrans324)
		argvalue0 := dbm_server.NewRequestHeader()
		err328 := argvalue0.Read(jsProt327)
		if err328 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err329 := (strconv.Atoi(flag.Arg(2)))
		if err329 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg330 := flag.Arg(3)
		mbTrans331 := thrift.NewTMemoryBufferLen(len(arg330))
		defer mbTrans331.Close()
		_, err332 := mbTrans331.WriteString(arg330)
		if err332 != nil {
			Usage()
			return
		}
		factory333 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt334 := factory333.GetProtocol(mbTrans331)
		containerStruct2 := dbm_server.NewResumeAdOrdersByIdsArgs()
		err335 := containerStruct2.ReadField3(jsProt334)
		if err335 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ResumeAdOrdersByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdOrderByIds requires 3 args")
			flag.Usage()
		}
		arg336 := flag.Arg(1)
		mbTrans337 := thrift.NewTMemoryBufferLen(len(arg336))
		defer mbTrans337.Close()
		_, err338 := mbTrans337.WriteString(arg336)
		if err338 != nil {
			Usage()
			return
		}
		factory339 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt340 := factory339.GetProtocol(mbTrans337)
		argvalue0 := dbm_server.NewRequestHeader()
		err341 := argvalue0.Read(jsProt340)
		if err341 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err342 := (strconv.Atoi(flag.Arg(2)))
		if err342 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg343 := flag.Arg(3)
		mbTrans344 := thrift.NewTMemoryBufferLen(len(arg343))
		defer mbTrans344.Close()
		_, err345 := mbTrans344.WriteString(arg343)
		if err345 != nil {
			Usage()
			return
		}
		factory346 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt347 := factory346.GetProtocol(mbTrans344)
		containerStruct2 := dbm_server.NewDeleteAdOrderByIdsArgs()
		err348 := containerStruct2.ReadField3(jsProt347)
		if err348 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdCampaign":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdCampaign requires 3 args")
			flag.Usage()
		}
		arg349 := flag.Arg(1)
		mbTrans350 := thrift.NewTMemoryBufferLen(len(arg349))
		defer mbTrans350.Close()
		_, err351 := mbTrans350.WriteString(arg349)
		if err351 != nil {
			Usage()
			return
		}
		factory352 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt353 := factory352.GetProtocol(mbTrans350)
		argvalue0 := dbm_server.NewRequestHeader()
		err354 := argvalue0.Read(jsProt353)
		if err354 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err355 := (strconv.Atoi(flag.Arg(2)))
		if err355 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg356 := flag.Arg(3)
		mbTrans357 := thrift.NewTMemoryBufferLen(len(arg356))
		defer mbTrans357.Close()
		_, err358 := mbTrans357.WriteString(arg356)
		if err358 != nil {
			Usage()
			return
		}
		factory359 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt360 := factory359.GetProtocol(mbTrans357)
		argvalue2 := dbm_server.NewAdCampaign()
		err361 := argvalue2.Read(jsProt360)
		if err361 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdCampaign(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdCampaign":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdCampaign requires 3 args")
			flag.Usage()
		}
		arg362 := flag.Arg(1)
		mbTrans363 := thrift.NewTMemoryBufferLen(len(arg362))
		defer mbTrans363.Close()
		_, err364 := mbTrans363.WriteString(arg362)
		if err364 != nil {
			Usage()
			return
		}
		factory365 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt366 := factory365.GetProtocol(mbTrans363)
		argvalue0 := dbm_server.NewRequestHeader()
		err367 := argvalue0.Read(jsProt366)
		if err367 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err368 := (strconv.Atoi(flag.Arg(2)))
		if err368 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg369 := flag.Arg(3)
		mbTrans370 := thrift.NewTMemoryBufferLen(len(arg369))
		defer mbTrans370.Close()
		_, err371 := mbTrans370.WriteString(arg369)
		if err371 != nil {
			Usage()
			return
		}
		factory372 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt373 := factory372.GetProtocol(mbTrans370)
		argvalue2 := dbm_server.NewAdCampaign()
		err374 := argvalue2.Read(jsProt373)
		if err374 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdCampaign(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg375 := flag.Arg(1)
		mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
		defer mbTrans376.Close()
		_, err377 := mbTrans376.WriteString(arg375)
		if err377 != nil {
			Usage()
			return
		}
		factory378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt379 := factory378.GetProtocol(mbTrans376)
		argvalue0 := dbm_server.NewRequestHeader()
		err380 := argvalue0.Read(jsProt379)
		if err380 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg381 := flag.Arg(2)
		mbTrans382 := thrift.NewTMemoryBufferLen(len(arg381))
		defer mbTrans382.Close()
		_, err383 := mbTrans382.WriteString(arg381)
		if err383 != nil {
			Usage()
			return
		}
		factory384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt385 := factory384.GetProtocol(mbTrans382)
		containerStruct1 := dbm_server.NewGetAdCampaignsByIdsArgs()
		err386 := containerStruct1.ReadField2(jsProt385)
		if err386 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdCampaignByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdCampaignByParams requires 2 args")
			flag.Usage()
		}
		arg387 := flag.Arg(1)
		mbTrans388 := thrift.NewTMemoryBufferLen(len(arg387))
		defer mbTrans388.Close()
		_, err389 := mbTrans388.WriteString(arg387)
		if err389 != nil {
			Usage()
			return
		}
		factory390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt391 := factory390.GetProtocol(mbTrans388)
		argvalue0 := dbm_server.NewRequestHeader()
		err392 := argvalue0.Read(jsProt391)
		if err392 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg393 := flag.Arg(2)
		mbTrans394 := thrift.NewTMemoryBufferLen(len(arg393))
		defer mbTrans394.Close()
		_, err395 := mbTrans394.WriteString(arg393)
		if err395 != nil {
			Usage()
			return
		}
		factory396 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt397 := factory396.GetProtocol(mbTrans394)
		argvalue1 := dbm_server.NewAdCampaignParams()
		err398 := argvalue1.Read(jsProt397)
		if err398 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdCampaignByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCampaignsByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PauseAdCampaignsByIds requires 4 args")
			flag.Usage()
		}
		arg399 := flag.Arg(1)
		mbTrans400 := thrift.NewTMemoryBufferLen(len(arg399))
		defer mbTrans400.Close()
		_, err401 := mbTrans400.WriteString(arg399)
		if err401 != nil {
			Usage()
			return
		}
		factory402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt403 := factory402.GetProtocol(mbTrans400)
		argvalue0 := dbm_server.NewRequestHeader()
		err404 := argvalue0.Read(jsProt403)
		if err404 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err405 := (strconv.Atoi(flag.Arg(2)))
		if err405 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err406 := (strconv.Atoi(flag.Arg(3)))
		if err406 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg407 := flag.Arg(4)
		mbTrans408 := thrift.NewTMemoryBufferLen(len(arg407))
		defer mbTrans408.Close()
		_, err409 := mbTrans408.WriteString(arg407)
		if err409 != nil {
			Usage()
			return
		}
		factory410 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt411 := factory410.GetProtocol(mbTrans408)
		containerStruct3 := dbm_server.NewPauseAdCampaignsByIdsArgs()
		err412 := containerStruct3.ReadField4(jsProt411)
		if err412 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.PauseAdCampaignsByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resumeAdCampaignsByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResumeAdCampaignsByIds requires 4 args")
			flag.Usage()
		}
		arg413 := flag.Arg(1)
		mbTrans414 := thrift.NewTMemoryBufferLen(len(arg413))
		defer mbTrans414.Close()
		_, err415 := mbTrans414.WriteString(arg413)
		if err415 != nil {
			Usage()
			return
		}
		factory416 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt417 := factory416.GetProtocol(mbTrans414)
		argvalue0 := dbm_server.NewRequestHeader()
		err418 := argvalue0.Read(jsProt417)
		if err418 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err419 := (strconv.Atoi(flag.Arg(2)))
		if err419 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err420 := (strconv.Atoi(flag.Arg(3)))
		if err420 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg421 := flag.Arg(4)
		mbTrans422 := thrift.NewTMemoryBufferLen(len(arg421))
		defer mbTrans422.Close()
		_, err423 := mbTrans422.WriteString(arg421)
		if err423 != nil {
			Usage()
			return
		}
		factory424 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt425 := factory424.GetProtocol(mbTrans422)
		containerStruct3 := dbm_server.NewResumeAdCampaignsByIdsArgs()
		err426 := containerStruct3.ReadField4(jsProt425)
		if err426 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.ResumeAdCampaignsByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAdCampaignsByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteAdCampaignsByIds requires 4 args")
			flag.Usage()
		}
		arg427 := flag.Arg(1)
		mbTrans428 := thrift.NewTMemoryBufferLen(len(arg427))
		defer mbTrans428.Close()
		_, err429 := mbTrans428.WriteString(arg427)
		if err429 != nil {
			Usage()
			return
		}
		factory430 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt431 := factory430.GetProtocol(mbTrans428)
		argvalue0 := dbm_server.NewRequestHeader()
		err432 := argvalue0.Read(jsProt431)
		if err432 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err433 := (strconv.Atoi(flag.Arg(2)))
		if err433 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err434 := (strconv.Atoi(flag.Arg(3)))
		if err434 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg435 := flag.Arg(4)
		mbTrans436 := thrift.NewTMemoryBufferLen(len(arg435))
		defer mbTrans436.Close()
		_, err437 := mbTrans436.WriteString(arg435)
		if err437 != nil {
			Usage()
			return
		}
		factory438 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt439 := factory438.GetProtocol(mbTrans436)
		containerStruct3 := dbm_server.NewDeleteAdCampaignsByIdsArgs()
		err440 := containerStruct3.ReadField4(jsProt439)
		if err440 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.DeleteAdCampaignsByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addAdStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdStrategy requires 3 args")
			flag.Usage()
		}
		arg441 := flag.Arg(1)
		mbTrans442 := thrift.NewTMemoryBufferLen(len(arg441))
		defer mbTrans442.Close()
		_, err443 := mbTrans442.WriteString(arg441)
		if err443 != nil {
			Usage()
			return
		}
		factory444 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt445 := factory444.GetProtocol(mbTrans442)
		argvalue0 := dbm_server.NewRequestHeader()
		err446 := argvalue0.Read(jsProt445)
		if err446 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err447 := (strconv.Atoi(flag.Arg(2)))
		if err447 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg448 := flag.Arg(3)
		mbTrans449 := thrift.NewTMemoryBufferLen(len(arg448))
		defer mbTrans449.Close()
		_, err450 := mbTrans449.WriteString(arg448)
		if err450 != nil {
			Usage()
			return
		}
		factory451 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt452 := factory451.GetProtocol(mbTrans449)
		argvalue2 := dbm_server.NewAdStrategy()
		err453 := argvalue2.Read(jsProt452)
		if err453 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdStrategy requires 3 args")
			flag.Usage()
		}
		arg454 := flag.Arg(1)
		mbTrans455 := thrift.NewTMemoryBufferLen(len(arg454))
		defer mbTrans455.Close()
		_, err456 := mbTrans455.WriteString(arg454)
		if err456 != nil {
			Usage()
			return
		}
		factory457 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt458 := factory457.GetProtocol(mbTrans455)
		argvalue0 := dbm_server.NewRequestHeader()
		err459 := argvalue0.Read(jsProt458)
		if err459 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err460 := (strconv.Atoi(flag.Arg(2)))
		if err460 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg461 := flag.Arg(3)
		mbTrans462 := thrift.NewTMemoryBufferLen(len(arg461))
		defer mbTrans462.Close()
		_, err463 := mbTrans462.WriteString(arg461)
		if err463 != nil {
			Usage()
			return
		}
		factory464 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt465 := factory464.GetProtocol(mbTrans462)
		argvalue2 := dbm_server.NewAdStrategy()
		err466 := argvalue2.Read(jsProt465)
		if err466 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg467 := flag.Arg(1)
		mbTrans468 := thrift.NewTMemoryBufferLen(len(arg467))
		defer mbTrans468.Close()
		_, err469 := mbTrans468.WriteString(arg467)
		if err469 != nil {
			Usage()
			return
		}
		factory470 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt471 := factory470.GetProtocol(mbTrans468)
		argvalue0 := dbm_server.NewRequestHeader()
		err472 := argvalue0.Read(jsProt471)
		if err472 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg473 := flag.Arg(2)
		mbTrans474 := thrift.NewTMemoryBufferLen(len(arg473))
		defer mbTrans474.Close()
		_, err475 := mbTrans474.WriteString(arg473)
		if err475 != nil {
			Usage()
			return
		}
		factory476 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt477 := factory476.GetProtocol(mbTrans474)
		containerStruct1 := dbm_server.NewGetAdStrategiesByIdsArgs()
		err478 := containerStruct1.ReadField2(jsProt477)
		if err478 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdStrategyByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdStrategyByParams requires 2 args")
			flag.Usage()
		}
		arg479 := flag.Arg(1)
		mbTrans480 := thrift.NewTMemoryBufferLen(len(arg479))
		defer mbTrans480.Close()
		_, err481 := mbTrans480.WriteString(arg479)
		if err481 != nil {
			Usage()
			return
		}
		factory482 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt483 := factory482.GetProtocol(mbTrans480)
		argvalue0 := dbm_server.NewRequestHeader()
		err484 := argvalue0.Read(jsProt483)
		if err484 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg485 := flag.Arg(2)
		mbTrans486 := thrift.NewTMemoryBufferLen(len(arg485))
		defer mbTrans486.Close()
		_, err487 := mbTrans486.WriteString(arg485)
		if err487 != nil {
			Usage()
			return
		}
		factory488 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt489 := factory488.GetProtocol(mbTrans486)
		argvalue1 := dbm_server.NewAdStrategyParams()
		err490 := argvalue1.Read(jsProt489)
		if err490 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdStrategyByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdStrategiesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "PauseAdStrategiesByIds requires 5 args")
			flag.Usage()
		}
		arg491 := flag.Arg(1)
		mbTrans492 := thrift.NewTMemoryBufferLen(len(arg491))
		defer mbTrans492.Close()
		_, err493 := mbTrans492.WriteString(arg491)
		if err493 != nil {
			Usage()
			return
		}
		factory494 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt495 := factory494.GetProtocol(mbTrans492)
		argvalue0 := dbm_server.NewRequestHeader()
		err496 := argvalue0.Read(jsProt495)
		if err496 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err497 := (strconv.Atoi(flag.Arg(2)))
		if err497 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err498 := (strconv.Atoi(flag.Arg(3)))
		if err498 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err499 := (strconv.Atoi(flag.Arg(4)))
		if err499 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg500 := flag.Arg(5)
		mbTrans501 := thrift.NewTMemoryBufferLen(len(arg500))
		defer mbTrans501.Close()
		_, err502 := mbTrans501.WriteString(arg500)
		if err502 != nil {
			Usage()
			return
		}
		factory503 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt504 := factory503.GetProtocol(mbTrans501)
		containerStruct4 := dbm_server.NewPauseAdStrategiesByIdsArgs()
		err505 := containerStruct4.ReadField5(jsProt504)
		if err505 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.PauseAdStrategiesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "resumeAdStrategiesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ResumeAdStrategiesByIds requires 5 args")
			flag.Usage()
		}
		arg506 := flag.Arg(1)
		mbTrans507 := thrift.NewTMemoryBufferLen(len(arg506))
		defer mbTrans507.Close()
		_, err508 := mbTrans507.WriteString(arg506)
		if err508 != nil {
			Usage()
			return
		}
		factory509 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt510 := factory509.GetProtocol(mbTrans507)
		argvalue0 := dbm_server.NewRequestHeader()
		err511 := argvalue0.Read(jsProt510)
		if err511 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err512 := (strconv.Atoi(flag.Arg(2)))
		if err512 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err513 := (strconv.Atoi(flag.Arg(3)))
		if err513 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err514 := (strconv.Atoi(flag.Arg(4)))
		if err514 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg515 := flag.Arg(5)
		mbTrans516 := thrift.NewTMemoryBufferLen(len(arg515))
		defer mbTrans516.Close()
		_, err517 := mbTrans516.WriteString(arg515)
		if err517 != nil {
			Usage()
			return
		}
		factory518 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt519 := factory518.GetProtocol(mbTrans516)
		containerStruct4 := dbm_server.NewResumeAdStrategiesByIdsArgs()
		err520 := containerStruct4.ReadField5(jsProt519)
		if err520 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.ResumeAdStrategiesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteAdStrategiesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteAdStrategiesByIds requires 5 args")
			flag.Usage()
		}
		arg521 := flag.Arg(1)
		mbTrans522 := thrift.NewTMemoryBufferLen(len(arg521))
		defer mbTrans522.Close()
		_, err523 := mbTrans522.WriteString(arg521)
		if err523 != nil {
			Usage()
			return
		}
		factory524 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt525 := factory524.GetProtocol(mbTrans522)
		argvalue0 := dbm_server.NewRequestHeader()
		err526 := argvalue0.Read(jsProt525)
		if err526 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err527 := (strconv.Atoi(flag.Arg(2)))
		if err527 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err528 := (strconv.Atoi(flag.Arg(3)))
		if err528 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err529 := (strconv.Atoi(flag.Arg(4)))
		if err529 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg530 := flag.Arg(5)
		mbTrans531 := thrift.NewTMemoryBufferLen(len(arg530))
		defer mbTrans531.Close()
		_, err532 := mbTrans531.WriteString(arg530)
		if err532 != nil {
			Usage()
			return
		}
		factory533 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt534 := factory533.GetProtocol(mbTrans531)
		containerStruct4 := dbm_server.NewDeleteAdStrategiesByIdsArgs()
		err535 := containerStruct4.ReadField5(jsProt534)
		if err535 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.DeleteAdStrategiesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addAdCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdCreative requires 3 args")
			flag.Usage()
		}
		arg536 := flag.Arg(1)
		mbTrans537 := thrift.NewTMemoryBufferLen(len(arg536))
		defer mbTrans537.Close()
		_, err538 := mbTrans537.WriteString(arg536)
		if err538 != nil {
			Usage()
			return
		}
		factory539 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt540 := factory539.GetProtocol(mbTrans537)
		argvalue0 := dbm_server.NewRequestHeader()
		err541 := argvalue0.Read(jsProt540)
		if err541 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err542 := (strconv.Atoi(flag.Arg(2)))
		if err542 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg543 := flag.Arg(3)
		mbTrans544 := thrift.NewTMemoryBufferLen(len(arg543))
		defer mbTrans544.Close()
		_, err545 := mbTrans544.WriteString(arg543)
		if err545 != nil {
			Usage()
			return
		}
		factory546 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt547 := factory546.GetProtocol(mbTrans544)
		argvalue2 := dbm_server.NewAdCreative()
		err548 := argvalue2.Read(jsProt547)
		if err548 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdCreative requires 3 args")
			flag.Usage()
		}
		arg549 := flag.Arg(1)
		mbTrans550 := thrift.NewTMemoryBufferLen(len(arg549))
		defer mbTrans550.Close()
		_, err551 := mbTrans550.WriteString(arg549)
		if err551 != nil {
			Usage()
			return
		}
		factory552 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt553 := factory552.GetProtocol(mbTrans550)
		argvalue0 := dbm_server.NewRequestHeader()
		err554 := argvalue0.Read(jsProt553)
		if err554 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err555 := (strconv.Atoi(flag.Arg(2)))
		if err555 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg556 := flag.Arg(3)
		mbTrans557 := thrift.NewTMemoryBufferLen(len(arg556))
		defer mbTrans557.Close()
		_, err558 := mbTrans557.WriteString(arg556)
		if err558 != nil {
			Usage()
			return
		}
		factory559 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt560 := factory559.GetProtocol(mbTrans557)
		argvalue2 := dbm_server.NewAdCreative()
		err561 := argvalue2.Read(jsProt560)
		if err561 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg562 := flag.Arg(1)
		mbTrans563 := thrift.NewTMemoryBufferLen(len(arg562))
		defer mbTrans563.Close()
		_, err564 := mbTrans563.WriteString(arg562)
		if err564 != nil {
			Usage()
			return
		}
		factory565 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt566 := factory565.GetProtocol(mbTrans563)
		argvalue0 := dbm_server.NewRequestHeader()
		err567 := argvalue0.Read(jsProt566)
		if err567 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg568 := flag.Arg(2)
		mbTrans569 := thrift.NewTMemoryBufferLen(len(arg568))
		defer mbTrans569.Close()
		_, err570 := mbTrans569.WriteString(arg568)
		if err570 != nil {
			Usage()
			return
		}
		factory571 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt572 := factory571.GetProtocol(mbTrans569)
		containerStruct1 := dbm_server.NewGetAdCreativesByIdsArgs()
		err573 := containerStruct1.ReadField2(jsProt572)
		if err573 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdCreativeByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdCreativeByParams requires 2 args")
			flag.Usage()
		}
		arg574 := flag.Arg(1)
		mbTrans575 := thrift.NewTMemoryBufferLen(len(arg574))
		defer mbTrans575.Close()
		_, err576 := mbTrans575.WriteString(arg574)
		if err576 != nil {
			Usage()
			return
		}
		factory577 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt578 := factory577.GetProtocol(mbTrans575)
		argvalue0 := dbm_server.NewRequestHeader()
		err579 := argvalue0.Read(jsProt578)
		if err579 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg580 := flag.Arg(2)
		mbTrans581 := thrift.NewTMemoryBufferLen(len(arg580))
		defer mbTrans581.Close()
		_, err582 := mbTrans581.WriteString(arg580)
		if err582 != nil {
			Usage()
			return
		}
		factory583 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt584 := factory583.GetProtocol(mbTrans581)
		argvalue1 := dbm_server.NewAdCreativeParams()
		err585 := argvalue1.Read(jsProt584)
		if err585 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdCreativeByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCreativesByIds":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "PauseAdCreativesByIds requires 6 args")
			flag.Usage()
		}
		arg586 := flag.Arg(1)
		mbTrans587 := thrift.NewTMemoryBufferLen(len(arg586))
		defer mbTrans587.Close()
		_, err588 := mbTrans587.WriteString(arg586)
		if err588 != nil {
			Usage()
			return
		}
		factory589 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt590 := factory589.GetProtocol(mbTrans587)
		argvalue0 := dbm_server.NewRequestHeader()
		err591 := argvalue0.Read(jsProt590)
		if err591 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err592 := (strconv.Atoi(flag.Arg(2)))
		if err592 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err593 := (strconv.Atoi(flag.Arg(3)))
		if err593 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err594 := (strconv.Atoi(flag.Arg(4)))
		if err594 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err595 := (strconv.Atoi(flag.Arg(5)))
		if err595 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		arg596 := flag.Arg(6)
		mbTrans597 := thrift.NewTMemoryBufferLen(len(arg596))
		defer mbTrans597.Close()
		_, err598 := mbTrans597.WriteString(arg596)
		if err598 != nil {
			Usage()
			return
		}
		factory599 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt600 := factory599.GetProtocol(mbTrans597)
		containerStruct5 := dbm_server.NewPauseAdCreativesByIdsArgs()
		err601 := containerStruct5.ReadField6(jsProt600)
		if err601 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Ids
		value5 := argvalue5
		fmt.Print(client.PauseAdCreativesByIds(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "resumeAdCreativesByIds":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ResumeAdCreativesByIds requires 6 args")
			flag.Usage()
		}
		arg602 := flag.Arg(1)
		mbTrans603 := thrift.NewTMemoryBufferLen(len(arg602))
		defer mbTrans603.Close()
		_, err604 := mbTrans603.WriteString(arg602)
		if err604 != nil {
			Usage()
			return
		}
		factory605 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt606 := factory605.GetProtocol(mbTrans603)
		argvalue0 := dbm_server.NewRequestHeader()
		err607 := argvalue0.Read(jsProt606)
		if err607 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err608 := (strconv.Atoi(flag.Arg(2)))
		if err608 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err609 := (strconv.Atoi(flag.Arg(3)))
		if err609 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err610 := (strconv.Atoi(flag.Arg(4)))
		if err610 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err611 := (strconv.Atoi(flag.Arg(5)))
		if err611 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		arg612 := flag.Arg(6)
		mbTrans613 := thrift.NewTMemoryBufferLen(len(arg612))
		defer mbTrans613.Close()
		_, err614 := mbTrans613.WriteString(arg612)
		if err614 != nil {
			Usage()
			return
		}
		factory615 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt616 := factory615.GetProtocol(mbTrans613)
		containerStruct5 := dbm_server.NewResumeAdCreativesByIdsArgs()
		err617 := containerStruct5.ReadField6(jsProt616)
		if err617 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Ids
		value5 := argvalue5
		fmt.Print(client.ResumeAdCreativesByIds(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "deleteAdCreativesByIds":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "DeleteAdCreativesByIds requires 6 args")
			flag.Usage()
		}
		arg618 := flag.Arg(1)
		mbTrans619 := thrift.NewTMemoryBufferLen(len(arg618))
		defer mbTrans619.Close()
		_, err620 := mbTrans619.WriteString(arg618)
		if err620 != nil {
			Usage()
			return
		}
		factory621 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt622 := factory621.GetProtocol(mbTrans619)
		argvalue0 := dbm_server.NewRequestHeader()
		err623 := argvalue0.Read(jsProt622)
		if err623 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err624 := (strconv.Atoi(flag.Arg(2)))
		if err624 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err625 := (strconv.Atoi(flag.Arg(3)))
		if err625 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err626 := (strconv.Atoi(flag.Arg(4)))
		if err626 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err627 := (strconv.Atoi(flag.Arg(5)))
		if err627 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		arg628 := flag.Arg(6)
		mbTrans629 := thrift.NewTMemoryBufferLen(len(arg628))
		defer mbTrans629.Close()
		_, err630 := mbTrans629.WriteString(arg628)
		if err630 != nil {
			Usage()
			return
		}
		factory631 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt632 := factory631.GetProtocol(mbTrans629)
		containerStruct5 := dbm_server.NewDeleteAdCreativesByIdsArgs()
		err633 := containerStruct5.ReadField6(jsProt632)
		if err633 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Ids
		value5 := argvalue5
		fmt.Print(client.DeleteAdCreativesByIds(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "innerApproveAdCreatives":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveAdCreatives requires 3 args")
			flag.Usage()
		}
		arg634 := flag.Arg(1)
		mbTrans635 := thrift.NewTMemoryBufferLen(len(arg634))
		defer mbTrans635.Close()
		_, err636 := mbTrans635.WriteString(arg634)
		if err636 != nil {
			Usage()
			return
		}
		factory637 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt638 := factory637.GetProtocol(mbTrans635)
		argvalue0 := dbm_server.NewRequestHeader()
		err639 := argvalue0.Read(jsProt638)
		if err639 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err640 := (strconv.Atoi(flag.Arg(2)))
		if err640 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg641 := flag.Arg(3)
		mbTrans642 := thrift.NewTMemoryBufferLen(len(arg641))
		defer mbTrans642.Close()
		_, err643 := mbTrans642.WriteString(arg641)
		if err643 != nil {
			Usage()
			return
		}
		factory644 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt645 := factory644.GetProtocol(mbTrans642)
		containerStruct2 := dbm_server.NewInnerApproveAdCreativesArgs()
		err646 := containerStruct2.ReadField3(jsProt645)
		if err646 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveAdCreatives(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editCampaignMinProfitRatio":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCampaignMinProfitRatio requires 3 args")
			flag.Usage()
		}
		arg647 := flag.Arg(1)
		mbTrans648 := thrift.NewTMemoryBufferLen(len(arg647))
		defer mbTrans648.Close()
		_, err649 := mbTrans648.WriteString(arg647)
		if err649 != nil {
			Usage()
			return
		}
		factory650 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt651 := factory650.GetProtocol(mbTrans648)
		argvalue0 := dbm_server.NewRequestHeader()
		err652 := argvalue0.Read(jsProt651)
		if err652 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg653 := flag.Arg(2)
		mbTrans654 := thrift.NewTMemoryBufferLen(len(arg653))
		defer mbTrans654.Close()
		_, err655 := mbTrans654.WriteString(arg653)
		if err655 != nil {
			Usage()
			return
		}
		factory656 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt657 := factory656.GetProtocol(mbTrans654)
		containerStruct1 := dbm_server.NewEditCampaignMinProfitRatioArgs()
		err658 := containerStruct1.ReadField2(jsProt657)
		if err658 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIds
		value1 := argvalue1
		tmp2, err659 := (strconv.Atoi(flag.Arg(3)))
		if err659 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.EditCampaignMinProfitRatio(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getOrderIdsByProjectIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOrderIdsByProjectIds requires 2 args")
			flag.Usage()
		}
		arg660 := flag.Arg(1)
		mbTrans661 := thrift.NewTMemoryBufferLen(len(arg660))
		defer mbTrans661.Close()
		_, err662 := mbTrans661.WriteString(arg660)
		if err662 != nil {
			Usage()
			return
		}
		factory663 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt664 := factory663.GetProtocol(mbTrans661)
		argvalue0 := dbm_server.NewRequestHeader()
		err665 := argvalue0.Read(jsProt664)
		if err665 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg666 := flag.Arg(2)
		mbTrans667 := thrift.NewTMemoryBufferLen(len(arg666))
		defer mbTrans667.Close()
		_, err668 := mbTrans667.WriteString(arg666)
		if err668 != nil {
			Usage()
			return
		}
		factory669 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt670 := factory669.GetProtocol(mbTrans667)
		containerStruct1 := dbm_server.NewGetOrderIdsByProjectIdsArgs()
		err671 := containerStruct1.ReadField2(jsProt670)
		if err671 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ProjectIds
		value1 := argvalue1
		fmt.Print(client.GetOrderIdsByProjectIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
