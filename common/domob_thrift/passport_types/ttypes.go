// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package passport_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//注册渠道id
type RegAppId int64

const (
	RegAppId_REG_APP_ID_UNKNOWN         RegAppId = 0
	RegAppId_REG_APP_ID_WEB             RegAppId = 1
	RegAppId_REG_APP_ID_ADMIN           RegAppId = 2
	RegAppId_REG_APP_ID_AGENT           RegAppId = 3
	RegAppId_REG_APP_ID_API_AGENT       RegAppId = 4
	RegAppId_REG_APP_ID_DSP_AGENT       RegAppId = 5
	RegAppId_REG_APP_ID_BIDMASTER_AGENT RegAppId = 6
)

func (p RegAppId) String() string {
	switch p {
	case RegAppId_REG_APP_ID_UNKNOWN:
		return "RegAppId_REG_APP_ID_UNKNOWN"
	case RegAppId_REG_APP_ID_WEB:
		return "RegAppId_REG_APP_ID_WEB"
	case RegAppId_REG_APP_ID_ADMIN:
		return "RegAppId_REG_APP_ID_ADMIN"
	case RegAppId_REG_APP_ID_AGENT:
		return "RegAppId_REG_APP_ID_AGENT"
	case RegAppId_REG_APP_ID_API_AGENT:
		return "RegAppId_REG_APP_ID_API_AGENT"
	case RegAppId_REG_APP_ID_DSP_AGENT:
		return "RegAppId_REG_APP_ID_DSP_AGENT"
	case RegAppId_REG_APP_ID_BIDMASTER_AGENT:
		return "RegAppId_REG_APP_ID_BIDMASTER_AGENT"
	}
	return "<UNSET>"
}

func RegAppIdFromString(s string) (RegAppId, error) {
	switch s {
	case "RegAppId_REG_APP_ID_UNKNOWN":
		return RegAppId_REG_APP_ID_UNKNOWN, nil
	case "RegAppId_REG_APP_ID_WEB":
		return RegAppId_REG_APP_ID_WEB, nil
	case "RegAppId_REG_APP_ID_ADMIN":
		return RegAppId_REG_APP_ID_ADMIN, nil
	case "RegAppId_REG_APP_ID_AGENT":
		return RegAppId_REG_APP_ID_AGENT, nil
	case "RegAppId_REG_APP_ID_API_AGENT":
		return RegAppId_REG_APP_ID_API_AGENT, nil
	case "RegAppId_REG_APP_ID_DSP_AGENT":
		return RegAppId_REG_APP_ID_DSP_AGENT, nil
	case "RegAppId_REG_APP_ID_BIDMASTER_AGENT":
		return RegAppId_REG_APP_ID_BIDMASTER_AGENT, nil
	}
	return RegAppId(math.MinInt32 - 1), fmt.Errorf("not a valid RegAppId string")
}

//用户状态
type UserStatus int64

const (
	UserStatus_USER_STATUS_AWAIT_ACTIVATE UserStatus = 0
	UserStatus_USER_STATUS_ACTIVATED      UserStatus = 1
)

func (p UserStatus) String() string {
	switch p {
	case UserStatus_USER_STATUS_AWAIT_ACTIVATE:
		return "UserStatus_USER_STATUS_AWAIT_ACTIVATE"
	case UserStatus_USER_STATUS_ACTIVATED:
		return "UserStatus_USER_STATUS_ACTIVATED"
	}
	return "<UNSET>"
}

func UserStatusFromString(s string) (UserStatus, error) {
	switch s {
	case "UserStatus_USER_STATUS_AWAIT_ACTIVATE":
		return UserStatus_USER_STATUS_AWAIT_ACTIVATE, nil
	case "UserStatus_USER_STATUS_ACTIVATED":
		return UserStatus_USER_STATUS_ACTIVATED, nil
	}
	return UserStatus(math.MinInt32 - 1), fmt.Errorf("not a valid UserStatus string")
}

//管理后台禁封用户状态
type AdminBanStatus int64

const (
	AdminBanStatus_ADMIN_BAN_STATUS_NONE           AdminBanStatus = 0
	AdminBanStatus_ADMIN_BAN_STATUS_ADER           AdminBanStatus = 1
	AdminBanStatus_ADMIN_BAN_STATUS_DEVER          AdminBanStatus = 2
	AdminBanStatus_ADMIN_BAN_STATUS_ADER_AND_DEVER AdminBanStatus = 3
)

func (p AdminBanStatus) String() string {
	switch p {
	case AdminBanStatus_ADMIN_BAN_STATUS_NONE:
		return "AdminBanStatus_ADMIN_BAN_STATUS_NONE"
	case AdminBanStatus_ADMIN_BAN_STATUS_ADER:
		return "AdminBanStatus_ADMIN_BAN_STATUS_ADER"
	case AdminBanStatus_ADMIN_BAN_STATUS_DEVER:
		return "AdminBanStatus_ADMIN_BAN_STATUS_DEVER"
	case AdminBanStatus_ADMIN_BAN_STATUS_ADER_AND_DEVER:
		return "AdminBanStatus_ADMIN_BAN_STATUS_ADER_AND_DEVER"
	}
	return "<UNSET>"
}

func AdminBanStatusFromString(s string) (AdminBanStatus, error) {
	switch s {
	case "AdminBanStatus_ADMIN_BAN_STATUS_NONE":
		return AdminBanStatus_ADMIN_BAN_STATUS_NONE, nil
	case "AdminBanStatus_ADMIN_BAN_STATUS_ADER":
		return AdminBanStatus_ADMIN_BAN_STATUS_ADER, nil
	case "AdminBanStatus_ADMIN_BAN_STATUS_DEVER":
		return AdminBanStatus_ADMIN_BAN_STATUS_DEVER, nil
	case "AdminBanStatus_ADMIN_BAN_STATUS_ADER_AND_DEVER":
		return AdminBanStatus_ADMIN_BAN_STATUS_ADER_AND_DEVER, nil
	}
	return AdminBanStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdminBanStatus string")
}

//method参数顺序位置
//如方法registerUser(RequestHeader header, UserRegInfo regInfo),
//第一位就是header参数，起始数字为1
type ArgPositions int64

const (
	ArgPositions_ARG_POSITON_ONE   ArgPositions = 1
	ArgPositions_ARG_POSITON_TWO   ArgPositions = 2
	ArgPositions_ARG_POSITON_THREE ArgPositions = 3
	ArgPositions_ARG_POSITON_FOUR  ArgPositions = 4
)

func (p ArgPositions) String() string {
	switch p {
	case ArgPositions_ARG_POSITON_ONE:
		return "ArgPositions_ARG_POSITON_ONE"
	case ArgPositions_ARG_POSITON_TWO:
		return "ArgPositions_ARG_POSITON_TWO"
	case ArgPositions_ARG_POSITON_THREE:
		return "ArgPositions_ARG_POSITON_THREE"
	case ArgPositions_ARG_POSITON_FOUR:
		return "ArgPositions_ARG_POSITON_FOUR"
	}
	return "<UNSET>"
}

func ArgPositionsFromString(s string) (ArgPositions, error) {
	switch s {
	case "ArgPositions_ARG_POSITON_ONE":
		return ArgPositions_ARG_POSITON_ONE, nil
	case "ArgPositions_ARG_POSITON_TWO":
		return ArgPositions_ARG_POSITON_TWO, nil
	case "ArgPositions_ARG_POSITON_THREE":
		return ArgPositions_ARG_POSITON_THREE, nil
	case "ArgPositions_ARG_POSITON_FOUR":
		return ArgPositions_ARG_POSITON_FOUR, nil
	}
	return ArgPositions(math.MinInt32 - 1), fmt.Errorf("not a valid ArgPositions string")
}

//用户扩展信息状态
type UserExtInfoStatus int64

const (
	UserExtInfoStatus_USEREXTINFO_STATUS_VALID  UserExtInfoStatus = 0
	UserExtInfoStatus_USEREXTINFO_STATUS_DELETE UserExtInfoStatus = 1
)

func (p UserExtInfoStatus) String() string {
	switch p {
	case UserExtInfoStatus_USEREXTINFO_STATUS_VALID:
		return "UserExtInfoStatus_USEREXTINFO_STATUS_VALID"
	case UserExtInfoStatus_USEREXTINFO_STATUS_DELETE:
		return "UserExtInfoStatus_USEREXTINFO_STATUS_DELETE"
	}
	return "<UNSET>"
}

func UserExtInfoStatusFromString(s string) (UserExtInfoStatus, error) {
	switch s {
	case "UserExtInfoStatus_USEREXTINFO_STATUS_VALID":
		return UserExtInfoStatus_USEREXTINFO_STATUS_VALID, nil
	case "UserExtInfoStatus_USEREXTINFO_STATUS_DELETE":
		return UserExtInfoStatus_USEREXTINFO_STATUS_DELETE, nil
	}
	return UserExtInfoStatus(math.MinInt32 - 1), fmt.Errorf("not a valid UserExtInfoStatus string")
}

type UserRegInfo struct {
	Email       string            `thrift:"email,1" json:"email"`
	Password    string            `thrift:"password,2" json:"password"`
	RegIp       int64             `thrift:"regIp,3" json:"regIp"`
	RegAppId    RegAppId          `thrift:"regAppId,4" json:"regAppId"`
	UserRole    enums.UserRole    `thrift:"userRole,5" json:"userRole"`
	Remark      string            `thrift:"remark,6" json:"remark"`
	AgentUid    int32             `thrift:"agentUid,7" json:"agentUid"`
	SponsorType enums.SponsorType `thrift:"sponsorType,8" json:"sponsorType"`
	// unused field # 9
	ExtInfo map[string]string `thrift:"extInfo,10" json:"extInfo"`
}

func NewUserRegInfo() *UserRegInfo {
	return &UserRegInfo{
		RegAppId: math.MinInt32 - 1, // unset sentinal value

		UserRole: math.MinInt32 - 1, // unset sentinal value

		SponsorType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UserRegInfo) IsSetRegAppId() bool {
	return int64(p.RegAppId) != math.MinInt32-1
}

func (p *UserRegInfo) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *UserRegInfo) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *UserRegInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserRegInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *UserRegInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *UserRegInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RegIp = v
	}
	return nil
}

func (p *UserRegInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RegAppId = RegAppId(v)
	}
	return nil
}

func (p *UserRegInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UserRole = enums.UserRole(v)
	}
	return nil
}

func (p *UserRegInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *UserRegInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *UserRegInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SponsorType = enums.SponsorType(v)
	}
	return nil
}

func (p *UserRegInfo) readField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.ExtInfo[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UserRegInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserRegInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserRegInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:email: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:password: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regIp", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:regIp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RegIp)); err != nil {
		return fmt.Errorf("%T.regIp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:regIp: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegAppId() {
		if err := oprot.WriteFieldBegin("regAppId", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:regAppId: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RegAppId)); err != nil {
			return fmt.Errorf("%T.regAppId (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:regAppId: %s", p, err)
		}
	}
	return err
}

func (p *UserRegInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:userRole: %s", p, err)
		}
	}
	return err
}

func (p *UserRegInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:remark: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:agentUid: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSponsorType() {
		if err := oprot.WriteFieldBegin("sponsorType", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:sponsorType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
			return fmt.Errorf("%T.sponsorType (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:sponsorType: %s", p, err)
		}
	}
	return err
}

func (p *UserRegInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *UserRegInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserRegInfo(%+v)", *p)
}

type UserBase struct {
	Uid         int32             `thrift:"uid,1" json:"uid"`
	Email       string            `thrift:"email,2" json:"email"`
	UserRole    enums.UserRole    `thrift:"userRole,3" json:"userRole"`
	Status      UserStatus        `thrift:"status,4" json:"status"`
	AdminStatus AdminBanStatus    `thrift:"adminStatus,5" json:"adminStatus"`
	RegTime     int64             `thrift:"regTime,6" json:"regTime"`
	Remark      string            `thrift:"remark,7" json:"remark"`
	AgentUid    int32             `thrift:"agentUid,8" json:"agentUid"`
	SponsorType enums.SponsorType `thrift:"sponsorType,9" json:"sponsorType"`
	ExtInfo     map[string]string `thrift:"extInfo,10" json:"extInfo"`
}

func NewUserBase() *UserBase {
	return &UserBase{
		UserRole: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		AdminStatus: math.MinInt32 - 1, // unset sentinal value

		SponsorType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UserBase) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *UserBase) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *UserBase) IsSetAdminStatus() bool {
	return int64(p.AdminStatus) != math.MinInt32-1
}

func (p *UserBase) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *UserBase) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserBase) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UserBase) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *UserBase) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserRole = enums.UserRole(v)
	}
	return nil
}

func (p *UserBase) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = UserStatus(v)
	}
	return nil
}

func (p *UserBase) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AdminStatus = AdminBanStatus(v)
	}
	return nil
}

func (p *UserBase) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.RegTime = v
	}
	return nil
}

func (p *UserBase) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *UserBase) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *UserBase) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SponsorType = enums.SponsorType(v)
	}
	return nil
}

func (p *UserBase) readField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.ExtInfo[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UserBase) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserBase"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserBase) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *UserBase) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *UserBase) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:userRole: %s", p, err)
		}
	}
	return err
}

func (p *UserBase) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:status: %s", p, err)
		}
	}
	return err
}

func (p *UserBase) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdminStatus() {
		if err := oprot.WriteFieldBegin("adminStatus", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:adminStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdminStatus)); err != nil {
			return fmt.Errorf("%T.adminStatus (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:adminStatus: %s", p, err)
		}
	}
	return err
}

func (p *UserBase) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:regTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RegTime)); err != nil {
		return fmt.Errorf("%T.regTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:regTime: %s", p, err)
	}
	return err
}

func (p *UserBase) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:remark: %s", p, err)
	}
	return err
}

func (p *UserBase) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:agentUid: %s", p, err)
	}
	return err
}

func (p *UserBase) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSponsorType() {
		if err := oprot.WriteFieldBegin("sponsorType", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:sponsorType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
			return fmt.Errorf("%T.sponsorType (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:sponsorType: %s", p, err)
		}
	}
	return err
}

func (p *UserBase) writeField10(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *UserBase) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserBase(%+v)", *p)
}

type UserAderProfile struct {
	Uid int32 `thrift:"uid,1" json:"uid"`
}

func NewUserAderProfile() *UserAderProfile {
	return &UserAderProfile{}
}

func (p *UserAderProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserAderProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UserAderProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserAderProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserAderProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *UserAderProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserAderProfile(%+v)", *p)
}

type UserDeverProfile struct {
	Uid     int32  `thrift:"uid,1" json:"uid"`
	Name    string `thrift:"name,2" json:"name"`
	Qq      string `thrift:"qq,3" json:"qq"`
	Msn     string `thrift:"msn,4" json:"msn"`
	Phone   string `thrift:"phone,5" json:"phone"`
	Mobile  string `thrift:"mobile,6" json:"mobile"`
	Address string `thrift:"address,7" json:"address"`
}

func NewUserDeverProfile() *UserDeverProfile {
	return &UserDeverProfile{}
}

func (p *UserDeverProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserDeverProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UserDeverProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *UserDeverProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Qq = v
	}
	return nil
}

func (p *UserDeverProfile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Msn = v
	}
	return nil
}

func (p *UserDeverProfile) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *UserDeverProfile) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *UserDeverProfile) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Address = v
	}
	return nil
}

func (p *UserDeverProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserDeverProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserDeverProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("qq", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:qq: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Qq)); err != nil {
		return fmt.Errorf("%T.qq (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:qq: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msn", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:msn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msn)); err != nil {
		return fmt.Errorf("%T.msn (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:msn: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:phone: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:mobile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:mobile: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("address", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:address: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Address)); err != nil {
		return fmt.Errorf("%T.address (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:address: %s", p, err)
	}
	return err
}

func (p *UserDeverProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserDeverProfile(%+v)", *p)
}

type UserExtProfile struct {
	Uid     int32  `thrift:"uid,1" json:"uid"`
	Name    string `thrift:"name,2" json:"name"`
	Mobile  string `thrift:"mobile,3" json:"mobile"`
	Address string `thrift:"address,4" json:"address"`
	Phone   string `thrift:"phone,5" json:"phone"`
	Qq      string `thrift:"qq,6" json:"qq"`
	Msn     string `thrift:"msn,7" json:"msn"`
}

func NewUserExtProfile() *UserExtProfile {
	return &UserExtProfile{}
}

func (p *UserExtProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserExtProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UserExtProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *UserExtProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *UserExtProfile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Address = v
	}
	return nil
}

func (p *UserExtProfile) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *UserExtProfile) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Qq = v
	}
	return nil
}

func (p *UserExtProfile) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Msn = v
	}
	return nil
}

func (p *UserExtProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserExtProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserExtProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mobile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mobile: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("address", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:address: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Address)); err != nil {
		return fmt.Errorf("%T.address (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:address: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:phone: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("qq", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:qq: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Qq)); err != nil {
		return fmt.Errorf("%T.qq (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:qq: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msn", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:msn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msn)); err != nil {
		return fmt.Errorf("%T.msn (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:msn: %s", p, err)
	}
	return err
}

func (p *UserExtProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserExtProfile(%+v)", *p)
}

type User struct {
	Base         *UserBase         `thrift:"base,1" json:"base"`
	AderProfile  *UserAderProfile  `thrift:"aderProfile,2" json:"aderProfile"`
	DeverProfile *UserDeverProfile `thrift:"deverProfile,3" json:"deverProfile"`
	ExtProfile   *UserExtProfile   `thrift:"extProfile,4" json:"extProfile"`
}

func NewUser() *User {
	return &User{}
}

func (p *User) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *User) readField1(iprot thrift.TProtocol) error {
	p.Base = NewUserBase()
	if err := p.Base.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Base)
	}
	return nil
}

func (p *User) readField2(iprot thrift.TProtocol) error {
	p.AderProfile = NewUserAderProfile()
	if err := p.AderProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AderProfile)
	}
	return nil
}

func (p *User) readField3(iprot thrift.TProtocol) error {
	p.DeverProfile = NewUserDeverProfile()
	if err := p.DeverProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverProfile)
	}
	return nil
}

func (p *User) readField4(iprot thrift.TProtocol) error {
	p.ExtProfile = NewUserExtProfile()
	if err := p.ExtProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ExtProfile)
	}
	return nil
}

func (p *User) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("User"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *User) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Base != nil {
		if err := oprot.WriteFieldBegin("base", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:base: %s", p, err)
		}
		if err := p.Base.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Base)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:base: %s", p, err)
		}
	}
	return err
}

func (p *User) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AderProfile != nil {
		if err := oprot.WriteFieldBegin("aderProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:aderProfile: %s", p, err)
		}
		if err := p.AderProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AderProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:aderProfile: %s", p, err)
		}
	}
	return err
}

func (p *User) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeverProfile != nil {
		if err := oprot.WriteFieldBegin("deverProfile", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deverProfile: %s", p, err)
		}
		if err := p.DeverProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deverProfile: %s", p, err)
		}
	}
	return err
}

func (p *User) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ExtProfile != nil {
		if err := oprot.WriteFieldBegin("extProfile", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:extProfile: %s", p, err)
		}
		if err := p.ExtProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ExtProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:extProfile: %s", p, err)
		}
	}
	return err
}

func (p *User) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("User(%+v)", *p)
}

type UserMeta struct {
	Uid         int32          `thrift:"uid,1" json:"uid"`
	Email       string         `thrift:"email,2" json:"email"`
	UserRole    enums.UserRole `thrift:"userRole,3" json:"userRole"`
	RegIp       int64          `thrift:"regIp,4" json:"regIp"`
	RegTime     int64          `thrift:"regTime,5" json:"regTime"`
	RegAppId    RegAppId       `thrift:"regAppId,6" json:"regAppId"`
	LastLogin   int64          `thrift:"lastLogin,7" json:"lastLogin"`
	Status      UserStatus     `thrift:"status,8" json:"status"`
	AdminStatus AdminBanStatus `thrift:"adminStatus,9" json:"adminStatus"`
	Remark      string         `thrift:"remark,10" json:"remark"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	AgentUid int32 `thrift:"agentUid,20" json:"agentUid"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	SponsorType enums.SponsorType `thrift:"sponsorType,30" json:"sponsorType"`
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	ExtInfo map[string]string `thrift:"extInfo,40" json:"extInfo"`
}

func NewUserMeta() *UserMeta {
	return &UserMeta{
		UserRole: math.MinInt32 - 1, // unset sentinal value

		RegAppId: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		AdminStatus: math.MinInt32 - 1, // unset sentinal value

		SponsorType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UserMeta) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *UserMeta) IsSetRegAppId() bool {
	return int64(p.RegAppId) != math.MinInt32-1
}

func (p *UserMeta) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *UserMeta) IsSetAdminStatus() bool {
	return int64(p.AdminStatus) != math.MinInt32-1
}

func (p *UserMeta) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *UserMeta) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.MAP {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserMeta) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UserMeta) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *UserMeta) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserRole = enums.UserRole(v)
	}
	return nil
}

func (p *UserMeta) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RegIp = v
	}
	return nil
}

func (p *UserMeta) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RegTime = v
	}
	return nil
}

func (p *UserMeta) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.RegAppId = RegAppId(v)
	}
	return nil
}

func (p *UserMeta) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.LastLogin = v
	}
	return nil
}

func (p *UserMeta) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Status = UserStatus(v)
	}
	return nil
}

func (p *UserMeta) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AdminStatus = AdminBanStatus(v)
	}
	return nil
}

func (p *UserMeta) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *UserMeta) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *UserMeta) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.SponsorType = enums.SponsorType(v)
	}
	return nil
}

func (p *UserMeta) readField40(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key4 = v
		}
		var _val5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val5 = v
		}
		p.ExtInfo[_key4] = _val5
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UserMeta) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserMeta"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:userRole: %s", p, err)
		}
	}
	return err
}

func (p *UserMeta) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regIp", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:regIp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RegIp)); err != nil {
		return fmt.Errorf("%T.regIp (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:regIp: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:regTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RegTime)); err != nil {
		return fmt.Errorf("%T.regTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:regTime: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegAppId() {
		if err := oprot.WriteFieldBegin("regAppId", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:regAppId: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RegAppId)); err != nil {
			return fmt.Errorf("%T.regAppId (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:regAppId: %s", p, err)
		}
	}
	return err
}

func (p *UserMeta) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastLogin", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:lastLogin: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastLogin)); err != nil {
		return fmt.Errorf("%T.lastLogin (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:lastLogin: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:status: %s", p, err)
		}
	}
	return err
}

func (p *UserMeta) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdminStatus() {
		if err := oprot.WriteFieldBegin("adminStatus", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:adminStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdminStatus)); err != nil {
			return fmt.Errorf("%T.adminStatus (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:adminStatus: %s", p, err)
		}
	}
	return err
}

func (p *UserMeta) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:remark: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:agentUid: %s", p, err)
	}
	return err
}

func (p *UserMeta) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetSponsorType() {
		if err := oprot.WriteFieldBegin("sponsorType", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:sponsorType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
			return fmt.Errorf("%T.sponsorType (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:sponsorType: %s", p, err)
		}
	}
	return err
}

func (p *UserMeta) writeField40(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *UserMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserMeta(%+v)", *p)
}
