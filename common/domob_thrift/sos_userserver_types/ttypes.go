// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_userserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type PhoneSighType int64

const (
	PhoneSighType_Register      PhoneSighType = 1
	PhoneSighType_PasswordReset PhoneSighType = 2
)

func (p PhoneSighType) String() string {
	switch p {
	case PhoneSighType_Register:
		return "PhoneSighType_Register"
	case PhoneSighType_PasswordReset:
		return "PhoneSighType_PasswordReset"
	}
	return "<UNSET>"
}

func PhoneSighTypeFromString(s string) (PhoneSighType, error) {
	switch s {
	case "PhoneSighType_Register":
		return PhoneSighType_Register, nil
	case "PhoneSighType_PasswordReset":
		return PhoneSighType_PasswordReset, nil
	}
	return PhoneSighType(math.MinInt32 - 1), fmt.<PERSON><PERSON><PERSON>("not a valid PhoneSighType string")
}

type Sex int64

const (
	Sex_UNKNOWN Sex = 0
	Sex_MAN     Sex = 1
	Sex_WOMAN   Sex = 2
)

func (p Sex) String() string {
	switch p {
	case Sex_UNKNOWN:
		return "Sex_UNKNOWN"
	case Sex_MAN:
		return "Sex_MAN"
	case Sex_WOMAN:
		return "Sex_WOMAN"
	}
	return "<UNSET>"
}

func SexFromString(s string) (Sex, error) {
	switch s {
	case "Sex_UNKNOWN":
		return Sex_UNKNOWN, nil
	case "Sex_MAN":
		return Sex_MAN, nil
	case "Sex_WOMAN":
		return Sex_WOMAN, nil
	}
	return Sex(math.MinInt32 - 1), fmt.Errorf("not a valid Sex string")
}

type StatuCode int64

const (
	StatuCode_OK    StatuCode = 0
	StatuCode_Error StatuCode = 1
)

func (p StatuCode) String() string {
	switch p {
	case StatuCode_OK:
		return "StatuCode_OK"
	case StatuCode_Error:
		return "StatuCode_Error"
	}
	return "<UNSET>"
}

func StatuCodeFromString(s string) (StatuCode, error) {
	switch s {
	case "StatuCode_OK":
		return StatuCode_OK, nil
	case "StatuCode_Error":
		return StatuCode_Error, nil
	}
	return StatuCode(math.MinInt32 - 1), fmt.Errorf("not a valid StatuCode string")
}

type PhoneSighRequest struct {
	PhoneNum string        `thrift:"phone_num,1" json:"phone_num"`
	SendType PhoneSighType `thrift:"send_type,2" json:"send_type"`
}

func NewPhoneSighRequest() *PhoneSighRequest {
	return &PhoneSighRequest{
		SendType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PhoneSighRequest) IsSetSendType() bool {
	return int64(p.SendType) != math.MinInt32-1
}

func (p *PhoneSighRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PhoneSighRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *PhoneSighRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SendType = PhoneSighType(v)
	}
	return nil
}

func (p *PhoneSighRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PhoneSighRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PhoneSighRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone_num: %s", p, err)
	}
	return err
}

func (p *PhoneSighRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSendType() {
		if err := oprot.WriteFieldBegin("send_type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:send_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SendType)); err != nil {
			return fmt.Errorf("%T.send_type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:send_type: %s", p, err)
		}
	}
	return err
}

func (p *PhoneSighRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PhoneSighRequest(%+v)", *p)
}

type SignValidateRequest struct {
	PhoneNum string `thrift:"phone_num,1" json:"phone_num"`
	SignCode string `thrift:"sign_code,2" json:"sign_code"`
}

func NewSignValidateRequest() *SignValidateRequest {
	return &SignValidateRequest{}
}

func (p *SignValidateRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SignValidateRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *SignValidateRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SignCode = v
	}
	return nil
}

func (p *SignValidateRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SignValidateRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SignValidateRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone_num: %s", p, err)
	}
	return err
}

func (p *SignValidateRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign_code", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sign_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SignCode)); err != nil {
		return fmt.Errorf("%T.sign_code (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sign_code: %s", p, err)
	}
	return err
}

func (p *SignValidateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SignValidateRequest(%+v)", *p)
}

type PasswordResetRequest struct {
	PhoneNum    string `thrift:"phone_num,1" json:"phone_num"`
	Newpassword string `thrift:"newpassword,2" json:"newpassword"`
}

func NewPasswordResetRequest() *PasswordResetRequest {
	return &PasswordResetRequest{}
}

func (p *PasswordResetRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PasswordResetRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *PasswordResetRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Newpassword = v
	}
	return nil
}

func (p *PasswordResetRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PasswordResetRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PasswordResetRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone_num: %s", p, err)
	}
	return err
}

func (p *PasswordResetRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newpassword", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:newpassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Newpassword)); err != nil {
		return fmt.Errorf("%T.newpassword (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:newpassword: %s", p, err)
	}
	return err
}

func (p *PasswordResetRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PasswordResetRequest(%+v)", *p)
}

type PasswordUpdateRequest struct {
	Uid         int64  `thrift:"uid,1" json:"uid"`
	OldPassword string `thrift:"old_password,2" json:"old_password"`
	NewPassword string `thrift:"new_password,3" json:"new_password"`
}

func NewPasswordUpdateRequest() *PasswordUpdateRequest {
	return &PasswordUpdateRequest{}
}

func (p *PasswordUpdateRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PasswordUpdateRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *PasswordUpdateRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OldPassword = v
	}
	return nil
}

func (p *PasswordUpdateRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NewPassword = v
	}
	return nil
}

func (p *PasswordUpdateRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PasswordUpdateRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PasswordUpdateRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *PasswordUpdateRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("old_password", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:old_password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OldPassword)); err != nil {
		return fmt.Errorf("%T.old_password (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:old_password: %s", p, err)
	}
	return err
}

func (p *PasswordUpdateRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("new_password", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:new_password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewPassword)); err != nil {
		return fmt.Errorf("%T.new_password (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:new_password: %s", p, err)
	}
	return err
}

func (p *PasswordUpdateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PasswordUpdateRequest(%+v)", *p)
}

type RegisterRequest struct {
	PhoneNum   string `thrift:"phone_num,1" json:"phone_num"`
	Sex        Sex    `thrift:"sex,2" json:"sex"`
	Birthday   int32  `thrift:"birthday,3" json:"birthday"`
	SignCode   string `thrift:"sign_code,4" json:"sign_code"`
	Password   string `thrift:"password,5" json:"password"`
	MasterId   int64  `thrift:"master_id,6" json:"master_id"`
	Channel    string `thrift:"channel,7" json:"channel"`
	RegisterIp string `thrift:"register_ip,8" json:"register_ip"`
	Deviceid   int32  `thrift:"deviceid,9" json:"deviceid"`
	WithSim    bool   `thrift:"with_sim,10" json:"with_sim"`
	Imei       string `thrift:"imei,11" json:"imei"`
	Vc         string `thrift:"vc,12" json:"vc"`
}

func NewRegisterRequest() *RegisterRequest {
	return &RegisterRequest{
		Sex: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RegisterRequest) IsSetSex() bool {
	return int64(p.Sex) != math.MinInt32-1
}

func (p *RegisterRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *RegisterRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sex = Sex(v)
	}
	return nil
}

func (p *RegisterRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Birthday = v
	}
	return nil
}

func (p *RegisterRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SignCode = v
	}
	return nil
}

func (p *RegisterRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *RegisterRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MasterId = v
	}
	return nil
}

func (p *RegisterRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *RegisterRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.RegisterIp = v
	}
	return nil
}

func (p *RegisterRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Deviceid = v
	}
	return nil
}

func (p *RegisterRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.WithSim = v
	}
	return nil
}

func (p *RegisterRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *RegisterRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Vc = v
	}
	return nil
}

func (p *RegisterRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RegisterRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone_num: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSex() {
		if err := oprot.WriteFieldBegin("sex", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:sex: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Sex)); err != nil {
			return fmt.Errorf("%T.sex (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:sex: %s", p, err)
		}
	}
	return err
}

func (p *RegisterRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("birthday", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:birthday: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Birthday)); err != nil {
		return fmt.Errorf("%T.birthday (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:birthday: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign_code", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sign_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SignCode)); err != nil {
		return fmt.Errorf("%T.sign_code (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sign_code: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:password: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("master_id", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:master_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MasterId)); err != nil {
		return fmt.Errorf("%T.master_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:master_id: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:channel: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("register_ip", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:register_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RegisterIp)); err != nil {
		return fmt.Errorf("%T.register_ip (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:register_ip: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceid", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:deviceid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deviceid)); err != nil {
		return fmt.Errorf("%T.deviceid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:deviceid: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_sim", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:with_sim: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithSim)); err != nil {
		return fmt.Errorf("%T.with_sim (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:with_sim: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:imei: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vc", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:vc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vc)); err != nil {
		return fmt.Errorf("%T.vc (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:vc: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterRequest(%+v)", *p)
}

type InvitedRequest struct {
	Uid      int64  `thrift:"uid,1" json:"uid"`
	MasterId int64  `thrift:"master_id,2" json:"master_id"`
	ClientIp string `thrift:"client_ip,3" json:"client_ip"`
	Deviceid int32  `thrift:"deviceid,4" json:"deviceid"`
	WithSim  bool   `thrift:"with_sim,5" json:"with_sim"`
}

func NewInvitedRequest() *InvitedRequest {
	return &InvitedRequest{}
}

func (p *InvitedRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *InvitedRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *InvitedRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MasterId = v
	}
	return nil
}

func (p *InvitedRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *InvitedRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Deviceid = v
	}
	return nil
}

func (p *InvitedRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.WithSim = v
	}
	return nil
}

func (p *InvitedRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("InvitedRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *InvitedRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *InvitedRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("master_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:master_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MasterId)); err != nil {
		return fmt.Errorf("%T.master_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:master_id: %s", p, err)
	}
	return err
}

func (p *InvitedRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:client_ip: %s", p, err)
	}
	return err
}

func (p *InvitedRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:deviceid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deviceid)); err != nil {
		return fmt.Errorf("%T.deviceid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:deviceid: %s", p, err)
	}
	return err
}

func (p *InvitedRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_sim", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:with_sim: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithSim)); err != nil {
		return fmt.Errorf("%T.with_sim (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:with_sim: %s", p, err)
	}
	return err
}

func (p *InvitedRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvitedRequest(%+v)", *p)
}

type LoginRequest struct {
	PhoneNum string `thrift:"phone_num,1" json:"phone_num"`
	Password string `thrift:"password,2" json:"password"`
	Sign     string `thrift:"sign,3" json:"sign"`
	Deviceid int64  `thrift:"deviceid,4" json:"deviceid"`
	Vc       string `thrift:"vc,5" json:"vc"`
}

func NewLoginRequest() *LoginRequest {
	return &LoginRequest{}
}

func (p *LoginRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LoginRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *LoginRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *LoginRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sign = v
	}
	return nil
}

func (p *LoginRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Deviceid = v
	}
	return nil
}

func (p *LoginRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Vc = v
	}
	return nil
}

func (p *LoginRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LoginRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LoginRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone_num: %s", p, err)
	}
	return err
}

func (p *LoginRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:password: %s", p, err)
	}
	return err
}

func (p *LoginRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sign: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sign)); err != nil {
		return fmt.Errorf("%T.sign (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sign: %s", p, err)
	}
	return err
}

func (p *LoginRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceid", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:deviceid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Deviceid)); err != nil {
		return fmt.Errorf("%T.deviceid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:deviceid: %s", p, err)
	}
	return err
}

func (p *LoginRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vc", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:vc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vc)); err != nil {
		return fmt.Errorf("%T.vc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:vc: %s", p, err)
	}
	return err
}

func (p *LoginRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LoginRequest(%+v)", *p)
}

type CommonResponse struct {
	StatusCode StatuCode `thrift:"status_code,1" json:"status_code"`
	Message    string    `thrift:"message,2" json:"message"`
}

func NewCommonResponse() *CommonResponse {
	return &CommonResponse{
		StatusCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CommonResponse) IsSetStatusCode() bool {
	return int64(p.StatusCode) != math.MinInt32-1
}

func (p *CommonResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CommonResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StatusCode = StatuCode(v)
	}
	return nil
}

func (p *CommonResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *CommonResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CommonResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CommonResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusCode() {
		if err := oprot.WriteFieldBegin("status_code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusCode)); err != nil {
			return fmt.Errorf("%T.status_code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status_code: %s", p, err)
		}
	}
	return err
}

func (p *CommonResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *CommonResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonResponse(%+v)", *p)
}

type UserResponse struct {
	StatusCode StatuCode `thrift:"status_code,1" json:"status_code"`
	Message    string    `thrift:"message,2" json:"message"`
	Uid        int64     `thrift:"uid,3" json:"uid"`
	PhoneNum   string    `thrift:"phone_num,4" json:"phone_num"`
}

func NewUserResponse() *UserResponse {
	return &UserResponse{
		StatusCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UserResponse) IsSetStatusCode() bool {
	return int64(p.StatusCode) != math.MinInt32-1
}

func (p *UserResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StatusCode = StatuCode(v)
	}
	return nil
}

func (p *UserResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *UserResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UserResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *UserResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusCode() {
		if err := oprot.WriteFieldBegin("status_code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusCode)); err != nil {
			return fmt.Errorf("%T.status_code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status_code: %s", p, err)
		}
	}
	return err
}

func (p *UserResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *UserResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *UserResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:phone_num: %s", p, err)
	}
	return err
}

func (p *UserResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserResponse(%+v)", *p)
}
