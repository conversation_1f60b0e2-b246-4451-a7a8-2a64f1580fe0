// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/hdy_exception"
	"rtb_model_server/common/domob_thrift/hdy_finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = hdy_exception.GoUnusedProtection__
var _ = hdy_finance_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type FinanceService interface {
	// 获取账户财务信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Uids: 账户ID *
	GetFinanceUserInfoByUids(header *common.RequestHeader, uids []int32) (r map[int32]*hdy_finance_types.FinanceUserInfo, e *hdy_exception.HdyException, err error)
	// 主账户-子账户财务划拨
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - MasterAccountId: 主账号id，资金从这个账号中划分出去 *
	//  - ChildAccountId: 子账号id *
	//  - RechargeAmount: 充值金额划分 *
	//  - RewardAmount: 返点划分 *
	//  - Note: 备注 *
	//  - Time: 时间戳 *
	TransferAccountFinance(header *common.RequestHeader, masterAccountId int32, childAccountId int32, rechargeAmount int64, rewardAmount int64, note string, time int64) (r bool, e *hdy_exception.HdyException, err error)
	// 财务流水记录接口
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Record: 财务流水记录 *
	SaveFinanceAmount(header *common.RequestHeader, record *hdy_finance_types.FinanceAmountRecord) (r int64, e *hdy_exception.HdyException, err error)
	// 查询财务流水
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Params: 查询参数列表 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量限制 *
	SearchFinanceRecordByParams(header *common.RequestHeader, params *hdy_finance_types.FinanceRecordSearchParams, offset int64, limit int64) (r *common.QueryResult, e *hdy_exception.HdyException, err error)
	// 根据ID获取财务记录信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Ids: ids *
	GetFinanceRecordsByIds(header *common.RequestHeader, ids []int32) (r map[int64]*hdy_finance_types.FinanceAmountRecord, e *hdy_exception.HdyException, err error)
	// 保存广告主消耗记录
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Costs
	SaveAdAccountCosts(header *common.RequestHeader, costs []*hdy_finance_types.AdAccountCostInfo) (r bool, e *hdy_exception.HdyException, err error)
}

type FinanceServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewFinanceServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FinanceServiceClient {
	return &FinanceServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewFinanceServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FinanceServiceClient {
	return &FinanceServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 获取账户财务信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Uids: 账户ID *
func (p *FinanceServiceClient) GetFinanceUserInfoByUids(header *common.RequestHeader, uids []int32) (r map[int32]*hdy_finance_types.FinanceUserInfo, e *hdy_exception.HdyException, err error) {
	if err = p.sendGetFinanceUserInfoByUids(header, uids); err != nil {
		return
	}
	return p.recvGetFinanceUserInfoByUids()
}

func (p *FinanceServiceClient) sendGetFinanceUserInfoByUids(header *common.RequestHeader, uids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFinanceUserInfoByUids", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetFinanceUserInfoByUidsArgs()
	args0.Header = header
	args0.Uids = uids
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceServiceClient) recvGetFinanceUserInfoByUids() (value map[int32]*hdy_finance_types.FinanceUserInfo, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetFinanceUserInfoByUidsResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 主账户-子账户财务划拨
//
// Parameters:
//  - Header: 请求头部信息 *
//  - MasterAccountId: 主账号id，资金从这个账号中划分出去 *
//  - ChildAccountId: 子账号id *
//  - RechargeAmount: 充值金额划分 *
//  - RewardAmount: 返点划分 *
//  - Note: 备注 *
//  - Time: 时间戳 *
func (p *FinanceServiceClient) TransferAccountFinance(header *common.RequestHeader, masterAccountId int32, childAccountId int32, rechargeAmount int64, rewardAmount int64, note string, time int64) (r bool, e *hdy_exception.HdyException, err error) {
	if err = p.sendTransferAccountFinance(header, masterAccountId, childAccountId, rechargeAmount, rewardAmount, note, time); err != nil {
		return
	}
	return p.recvTransferAccountFinance()
}

func (p *FinanceServiceClient) sendTransferAccountFinance(header *common.RequestHeader, masterAccountId int32, childAccountId int32, rechargeAmount int64, rewardAmount int64, note string, time int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("transferAccountFinance", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewTransferAccountFinanceArgs()
	args4.Header = header
	args4.MasterAccountId = masterAccountId
	args4.ChildAccountId = childAccountId
	args4.RechargeAmount = rechargeAmount
	args4.RewardAmount = rewardAmount
	args4.Note = note
	args4.Time = time
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceServiceClient) recvTransferAccountFinance() (value bool, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewTransferAccountFinanceResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

// 财务流水记录接口
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Record: 财务流水记录 *
func (p *FinanceServiceClient) SaveFinanceAmount(header *common.RequestHeader, record *hdy_finance_types.FinanceAmountRecord) (r int64, e *hdy_exception.HdyException, err error) {
	if err = p.sendSaveFinanceAmount(header, record); err != nil {
		return
	}
	return p.recvSaveFinanceAmount()
}

func (p *FinanceServiceClient) sendSaveFinanceAmount(header *common.RequestHeader, record *hdy_finance_types.FinanceAmountRecord) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("saveFinanceAmount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewSaveFinanceAmountArgs()
	args8.Header = header
	args8.Record = record
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceServiceClient) recvSaveFinanceAmount() (value int64, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewSaveFinanceAmountResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 查询财务流水
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Params: 查询参数列表 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量限制 *
func (p *FinanceServiceClient) SearchFinanceRecordByParams(header *common.RequestHeader, params *hdy_finance_types.FinanceRecordSearchParams, offset int64, limit int64) (r *common.QueryResult, e *hdy_exception.HdyException, err error) {
	if err = p.sendSearchFinanceRecordByParams(header, params, offset, limit); err != nil {
		return
	}
	return p.recvSearchFinanceRecordByParams()
}

func (p *FinanceServiceClient) sendSearchFinanceRecordByParams(header *common.RequestHeader, params *hdy_finance_types.FinanceRecordSearchParams, offset int64, limit int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchFinanceRecordByParams", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewSearchFinanceRecordByParamsArgs()
	args12.Header = header
	args12.Params = params
	args12.Offset = offset
	args12.Limit = limit
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceServiceClient) recvSearchFinanceRecordByParams() (value *common.QueryResult, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewSearchFinanceRecordByParamsResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.E != nil {
		e = result13.E
	}
	return
}

// 根据ID获取财务记录信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Ids: ids *
func (p *FinanceServiceClient) GetFinanceRecordsByIds(header *common.RequestHeader, ids []int32) (r map[int64]*hdy_finance_types.FinanceAmountRecord, e *hdy_exception.HdyException, err error) {
	if err = p.sendGetFinanceRecordsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetFinanceRecordsByIds()
}

func (p *FinanceServiceClient) sendGetFinanceRecordsByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFinanceRecordsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetFinanceRecordsByIdsArgs()
	args16.Header = header
	args16.Ids = ids
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceServiceClient) recvGetFinanceRecordsByIds() (value map[int64]*hdy_finance_types.FinanceAmountRecord, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetFinanceRecordsByIdsResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.E != nil {
		e = result17.E
	}
	return
}

// 保存广告主消耗记录
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Costs
func (p *FinanceServiceClient) SaveAdAccountCosts(header *common.RequestHeader, costs []*hdy_finance_types.AdAccountCostInfo) (r bool, e *hdy_exception.HdyException, err error) {
	if err = p.sendSaveAdAccountCosts(header, costs); err != nil {
		return
	}
	return p.recvSaveAdAccountCosts()
}

func (p *FinanceServiceClient) sendSaveAdAccountCosts(header *common.RequestHeader, costs []*hdy_finance_types.AdAccountCostInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("saveAdAccountCosts", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewSaveAdAccountCostsArgs()
	args20.Header = header
	args20.Costs = costs
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceServiceClient) recvSaveAdAccountCosts() (value bool, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewSaveAdAccountCostsResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.E != nil {
		e = result21.E
	}
	return
}

type FinanceServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      FinanceService
}

func (p *FinanceServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *FinanceServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *FinanceServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewFinanceServiceProcessor(handler FinanceService) *FinanceServiceProcessor {

	self24 := &FinanceServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self24.processorMap["getFinanceUserInfoByUids"] = &financeServiceProcessorGetFinanceUserInfoByUids{handler: handler}
	self24.processorMap["transferAccountFinance"] = &financeServiceProcessorTransferAccountFinance{handler: handler}
	self24.processorMap["saveFinanceAmount"] = &financeServiceProcessorSaveFinanceAmount{handler: handler}
	self24.processorMap["searchFinanceRecordByParams"] = &financeServiceProcessorSearchFinanceRecordByParams{handler: handler}
	self24.processorMap["getFinanceRecordsByIds"] = &financeServiceProcessorGetFinanceRecordsByIds{handler: handler}
	self24.processorMap["saveAdAccountCosts"] = &financeServiceProcessorSaveAdAccountCosts{handler: handler}
	return self24
}

func (p *FinanceServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x25 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x25.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x25

}

type financeServiceProcessorGetFinanceUserInfoByUids struct {
	handler FinanceService
}

func (p *financeServiceProcessorGetFinanceUserInfoByUids) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFinanceUserInfoByUidsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFinanceUserInfoByUids", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFinanceUserInfoByUidsResult()
	if result.Success, result.E, err = p.handler.GetFinanceUserInfoByUids(args.Header, args.Uids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFinanceUserInfoByUids: "+err.Error())
		oprot.WriteMessageBegin("getFinanceUserInfoByUids", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFinanceUserInfoByUids", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeServiceProcessorTransferAccountFinance struct {
	handler FinanceService
}

func (p *financeServiceProcessorTransferAccountFinance) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewTransferAccountFinanceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("transferAccountFinance", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewTransferAccountFinanceResult()
	if result.Success, result.E, err = p.handler.TransferAccountFinance(args.Header, args.MasterAccountId, args.ChildAccountId, args.RechargeAmount, args.RewardAmount, args.Note, args.Time); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing transferAccountFinance: "+err.Error())
		oprot.WriteMessageBegin("transferAccountFinance", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("transferAccountFinance", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeServiceProcessorSaveFinanceAmount struct {
	handler FinanceService
}

func (p *financeServiceProcessorSaveFinanceAmount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSaveFinanceAmountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("saveFinanceAmount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSaveFinanceAmountResult()
	if result.Success, result.E, err = p.handler.SaveFinanceAmount(args.Header, args.Record); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing saveFinanceAmount: "+err.Error())
		oprot.WriteMessageBegin("saveFinanceAmount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("saveFinanceAmount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeServiceProcessorSearchFinanceRecordByParams struct {
	handler FinanceService
}

func (p *financeServiceProcessorSearchFinanceRecordByParams) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchFinanceRecordByParamsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchFinanceRecordByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchFinanceRecordByParamsResult()
	if result.Success, result.E, err = p.handler.SearchFinanceRecordByParams(args.Header, args.Params, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchFinanceRecordByParams: "+err.Error())
		oprot.WriteMessageBegin("searchFinanceRecordByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchFinanceRecordByParams", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeServiceProcessorGetFinanceRecordsByIds struct {
	handler FinanceService
}

func (p *financeServiceProcessorGetFinanceRecordsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFinanceRecordsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFinanceRecordsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFinanceRecordsByIdsResult()
	if result.Success, result.E, err = p.handler.GetFinanceRecordsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFinanceRecordsByIds: "+err.Error())
		oprot.WriteMessageBegin("getFinanceRecordsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFinanceRecordsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeServiceProcessorSaveAdAccountCosts struct {
	handler FinanceService
}

func (p *financeServiceProcessorSaveAdAccountCosts) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSaveAdAccountCostsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("saveAdAccountCosts", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSaveAdAccountCostsResult()
	if result.Success, result.E, err = p.handler.SaveAdAccountCosts(args.Header, args.Costs); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing saveAdAccountCosts: "+err.Error())
		oprot.WriteMessageBegin("saveAdAccountCosts", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("saveAdAccountCosts", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetFinanceUserInfoByUidsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uids   []int32               `thrift:"uids,2" json:"uids"`
}

func NewGetFinanceUserInfoByUidsArgs() *GetFinanceUserInfoByUidsArgs {
	return &GetFinanceUserInfoByUidsArgs{}
}

func (p *GetFinanceUserInfoByUidsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem26 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem26 = v
		}
		p.Uids = append(p.Uids, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceUserInfoByUids_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceUserInfoByUidsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:uids: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceUserInfoByUidsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceUserInfoByUidsArgs(%+v)", *p)
}

type GetFinanceUserInfoByUidsResult struct {
	Success map[int32]*hdy_finance_types.FinanceUserInfo `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException                  `thrift:"e,1" json:"e"`
}

func NewGetFinanceUserInfoByUidsResult() *GetFinanceUserInfoByUidsResult {
	return &GetFinanceUserInfoByUidsResult{}
}

func (p *GetFinanceUserInfoByUidsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*hdy_finance_types.FinanceUserInfo, size)
	for i := 0; i < size; i++ {
		var _key27 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key27 = v
		}
		_val28 := hdy_finance_types.NewFinanceUserInfo()
		if err := _val28.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val28)
		}
		p.Success[_key27] = _val28
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceUserInfoByUids_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceUserInfoByUidsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceUserInfoByUidsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceUserInfoByUidsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceUserInfoByUidsResult(%+v)", *p)
}

type TransferAccountFinanceArgs struct {
	Header          *common.RequestHeader `thrift:"header,1" json:"header"`
	MasterAccountId int32                 `thrift:"masterAccountId,2" json:"masterAccountId"`
	ChildAccountId  int32                 `thrift:"childAccountId,3" json:"childAccountId"`
	RechargeAmount  int64                 `thrift:"rechargeAmount,4" json:"rechargeAmount"`
	RewardAmount    int64                 `thrift:"rewardAmount,5" json:"rewardAmount"`
	Note            string                `thrift:"note,6" json:"note"`
	Time            int64                 `thrift:"time,7" json:"time"`
}

func NewTransferAccountFinanceArgs() *TransferAccountFinanceArgs {
	return &TransferAccountFinanceArgs{}
}

func (p *TransferAccountFinanceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MasterAccountId = v
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChildAccountId = v
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RechargeAmount = v
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RewardAmount = v
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *TransferAccountFinanceArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *TransferAccountFinanceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("transferAccountFinance_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TransferAccountFinanceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *TransferAccountFinanceArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("masterAccountId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:masterAccountId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MasterAccountId)); err != nil {
		return fmt.Errorf("%T.masterAccountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:masterAccountId: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("childAccountId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:childAccountId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChildAccountId)); err != nil {
		return fmt.Errorf("%T.childAccountId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:childAccountId: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rechargeAmount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:rechargeAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RechargeAmount)); err != nil {
		return fmt.Errorf("%T.rechargeAmount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:rechargeAmount: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rewardAmount", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:rewardAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RewardAmount)); err != nil {
		return fmt.Errorf("%T.rewardAmount (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:rewardAmount: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:note: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:time: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TransferAccountFinanceArgs(%+v)", *p)
}

type TransferAccountFinanceResult struct {
	Success bool                        `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewTransferAccountFinanceResult() *TransferAccountFinanceResult {
	return &TransferAccountFinanceResult{}
}

func (p *TransferAccountFinanceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TransferAccountFinanceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *TransferAccountFinanceResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *TransferAccountFinanceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("transferAccountFinance_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TransferAccountFinanceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *TransferAccountFinanceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *TransferAccountFinanceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TransferAccountFinanceResult(%+v)", *p)
}

type SaveFinanceAmountArgs struct {
	Header *common.RequestHeader                  `thrift:"header,1" json:"header"`
	Record *hdy_finance_types.FinanceAmountRecord `thrift:"record,2" json:"record"`
}

func NewSaveFinanceAmountArgs() *SaveFinanceAmountArgs {
	return &SaveFinanceAmountArgs{}
}

func (p *SaveFinanceAmountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaveFinanceAmountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SaveFinanceAmountArgs) readField2(iprot thrift.TProtocol) error {
	p.Record = hdy_finance_types.NewFinanceAmountRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *SaveFinanceAmountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("saveFinanceAmount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaveFinanceAmountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SaveFinanceAmountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:record: %s", p, err)
		}
	}
	return err
}

func (p *SaveFinanceAmountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveFinanceAmountArgs(%+v)", *p)
}

type SaveFinanceAmountResult struct {
	Success int64                       `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewSaveFinanceAmountResult() *SaveFinanceAmountResult {
	return &SaveFinanceAmountResult{}
}

func (p *SaveFinanceAmountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaveFinanceAmountResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *SaveFinanceAmountResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SaveFinanceAmountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("saveFinanceAmount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaveFinanceAmountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *SaveFinanceAmountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SaveFinanceAmountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveFinanceAmountResult(%+v)", *p)
}

type SearchFinanceRecordByParamsArgs struct {
	Header *common.RequestHeader                        `thrift:"header,1" json:"header"`
	Params *hdy_finance_types.FinanceRecordSearchParams `thrift:"params,2" json:"params"`
	Offset int64                                        `thrift:"offset,3" json:"offset"`
	Limit  int64                                        `thrift:"limit,4" json:"limit"`
}

func NewSearchFinanceRecordByParamsArgs() *SearchFinanceRecordByParamsArgs {
	return &SearchFinanceRecordByParamsArgs{}
}

func (p *SearchFinanceRecordByParamsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = hdy_finance_types.NewFinanceRecordSearchParams()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SearchFinanceRecordByParamsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SearchFinanceRecordByParamsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchFinanceRecordByParams_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchFinanceRecordByParamsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *SearchFinanceRecordByParamsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *SearchFinanceRecordByParamsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *SearchFinanceRecordByParamsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchFinanceRecordByParamsArgs(%+v)", *p)
}

type SearchFinanceRecordByParamsResult struct {
	Success *common.QueryResult         `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewSearchFinanceRecordByParamsResult() *SearchFinanceRecordByParamsResult {
	return &SearchFinanceRecordByParamsResult{}
}

func (p *SearchFinanceRecordByParamsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchFinanceRecordByParams_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchFinanceRecordByParamsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchFinanceRecordByParamsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchFinanceRecordByParamsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchFinanceRecordByParamsResult(%+v)", *p)
}

type GetFinanceRecordsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetFinanceRecordsByIdsArgs() *GetFinanceRecordsByIdsArgs {
	return &GetFinanceRecordsByIdsArgs{}
}

func (p *GetFinanceRecordsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem29 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem29 = v
		}
		p.Ids = append(p.Ids, _elem29)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceRecordsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceRecordsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceRecordsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceRecordsByIdsArgs(%+v)", *p)
}

type GetFinanceRecordsByIdsResult struct {
	Success map[int64]*hdy_finance_types.FinanceAmountRecord `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException                      `thrift:"e,1" json:"e"`
}

func NewGetFinanceRecordsByIdsResult() *GetFinanceRecordsByIdsResult {
	return &GetFinanceRecordsByIdsResult{}
}

func (p *GetFinanceRecordsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int64]*hdy_finance_types.FinanceAmountRecord, size)
	for i := 0; i < size; i++ {
		var _key30 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key30 = v
		}
		_val31 := hdy_finance_types.NewFinanceAmountRecord()
		if err := _val31.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val31)
		}
		p.Success[_key30] = _val31
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceRecordsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceRecordsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI64(int64(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceRecordsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceRecordsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceRecordsByIdsResult(%+v)", *p)
}

type SaveAdAccountCostsArgs struct {
	Header *common.RequestHeader                  `thrift:"header,1" json:"header"`
	Costs  []*hdy_finance_types.AdAccountCostInfo `thrift:"costs,2" json:"costs"`
}

func NewSaveAdAccountCostsArgs() *SaveAdAccountCostsArgs {
	return &SaveAdAccountCostsArgs{}
}

func (p *SaveAdAccountCostsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaveAdAccountCostsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SaveAdAccountCostsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Costs = make([]*hdy_finance_types.AdAccountCostInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem32 := hdy_finance_types.NewAdAccountCostInfo()
		if err := _elem32.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem32)
		}
		p.Costs = append(p.Costs, _elem32)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SaveAdAccountCostsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("saveAdAccountCosts_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaveAdAccountCostsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SaveAdAccountCostsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Costs != nil {
		if err := oprot.WriteFieldBegin("costs", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:costs: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Costs)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Costs {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:costs: %s", p, err)
		}
	}
	return err
}

func (p *SaveAdAccountCostsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveAdAccountCostsArgs(%+v)", *p)
}

type SaveAdAccountCostsResult struct {
	Success bool                        `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewSaveAdAccountCostsResult() *SaveAdAccountCostsResult {
	return &SaveAdAccountCostsResult{}
}

func (p *SaveAdAccountCostsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaveAdAccountCostsResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *SaveAdAccountCostsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SaveAdAccountCostsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("saveAdAccountCosts_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaveAdAccountCostsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *SaveAdAccountCostsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SaveAdAccountCostsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveAdAccountCostsResult(%+v)", *p)
}
