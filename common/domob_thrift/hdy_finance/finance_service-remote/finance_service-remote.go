// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"hdy_finance"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getFinanceUserInfoByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  bool transferAccountFinance(RequestHeader header, i32 masterAccountId, i32 childAccountId, i64 rechargeAmount, i64 rewardAmount, string note, i64 time)")
	fmt.Fprintln(os.Stderr, "  i64 saveFinanceAmount(RequestHeader header, FinanceAmountRecord record)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchFinanceRecordByParams(RequestHeader header, FinanceRecordSearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getFinanceRecordsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  bool saveAdAccountCosts(RequestHeader header,  costs)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := hdy_finance.NewFinanceServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getFinanceUserInfoByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinanceUserInfoByUids requires 2 args")
			flag.Usage()
		}
		arg33 := flag.Arg(1)
		mbTrans34 := thrift.NewTMemoryBufferLen(len(arg33))
		defer mbTrans34.Close()
		_, err35 := mbTrans34.WriteString(arg33)
		if err35 != nil {
			Usage()
			return
		}
		factory36 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt37 := factory36.GetProtocol(mbTrans34)
		argvalue0 := hdy_finance.NewRequestHeader()
		err38 := argvalue0.Read(jsProt37)
		if err38 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg39 := flag.Arg(2)
		mbTrans40 := thrift.NewTMemoryBufferLen(len(arg39))
		defer mbTrans40.Close()
		_, err41 := mbTrans40.WriteString(arg39)
		if err41 != nil {
			Usage()
			return
		}
		factory42 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt43 := factory42.GetProtocol(mbTrans40)
		containerStruct1 := hdy_finance.NewGetFinanceUserInfoByUidsArgs()
		err44 := containerStruct1.ReadField2(jsProt43)
		if err44 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetFinanceUserInfoByUids(value0, value1))
		fmt.Print("\n")
		break
	case "transferAccountFinance":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "TransferAccountFinance requires 7 args")
			flag.Usage()
		}
		arg45 := flag.Arg(1)
		mbTrans46 := thrift.NewTMemoryBufferLen(len(arg45))
		defer mbTrans46.Close()
		_, err47 := mbTrans46.WriteString(arg45)
		if err47 != nil {
			Usage()
			return
		}
		factory48 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt49 := factory48.GetProtocol(mbTrans46)
		argvalue0 := hdy_finance.NewRequestHeader()
		err50 := argvalue0.Read(jsProt49)
		if err50 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err51 := (strconv.Atoi(flag.Arg(2)))
		if err51 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err52 := (strconv.Atoi(flag.Arg(3)))
		if err52 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err53 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err53 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err54 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err54 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6, err56 := (strconv.ParseInt(flag.Arg(7), 10, 64))
		if err56 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		fmt.Print(client.TransferAccountFinance(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "saveFinanceAmount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SaveFinanceAmount requires 2 args")
			flag.Usage()
		}
		arg57 := flag.Arg(1)
		mbTrans58 := thrift.NewTMemoryBufferLen(len(arg57))
		defer mbTrans58.Close()
		_, err59 := mbTrans58.WriteString(arg57)
		if err59 != nil {
			Usage()
			return
		}
		factory60 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt61 := factory60.GetProtocol(mbTrans58)
		argvalue0 := hdy_finance.NewRequestHeader()
		err62 := argvalue0.Read(jsProt61)
		if err62 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg63 := flag.Arg(2)
		mbTrans64 := thrift.NewTMemoryBufferLen(len(arg63))
		defer mbTrans64.Close()
		_, err65 := mbTrans64.WriteString(arg63)
		if err65 != nil {
			Usage()
			return
		}
		factory66 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt67 := factory66.GetProtocol(mbTrans64)
		argvalue1 := hdy_finance.NewFinanceAmountRecord()
		err68 := argvalue1.Read(jsProt67)
		if err68 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SaveFinanceAmount(value0, value1))
		fmt.Print("\n")
		break
	case "searchFinanceRecordByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchFinanceRecordByParams requires 4 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := hdy_finance.NewRequestHeader()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg75 := flag.Arg(2)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue1 := hdy_finance.NewFinanceRecordSearchParams()
		err80 := argvalue1.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err81 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err81 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err82 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err82 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchFinanceRecordByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getFinanceRecordsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinanceRecordsByIds requires 2 args")
			flag.Usage()
		}
		arg83 := flag.Arg(1)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		argvalue0 := hdy_finance.NewRequestHeader()
		err88 := argvalue0.Read(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg89 := flag.Arg(2)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		containerStruct1 := hdy_finance.NewGetFinanceRecordsByIdsArgs()
		err94 := containerStruct1.ReadField2(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetFinanceRecordsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "saveAdAccountCosts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SaveAdAccountCosts requires 2 args")
			flag.Usage()
		}
		arg95 := flag.Arg(1)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		argvalue0 := hdy_finance.NewRequestHeader()
		err100 := argvalue0.Read(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg101 := flag.Arg(2)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		containerStruct1 := hdy_finance.NewSaveAdAccountCostsArgs()
		err106 := containerStruct1.ReadField2(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Costs
		value1 := argvalue1
		fmt.Print(client.SaveAdAccountCosts(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
