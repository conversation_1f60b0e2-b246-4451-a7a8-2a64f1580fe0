// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package compass_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/compass_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = compass_types.GoUnusedProtection__
var GoUnusedProtection__ int

type CompassEventType int64

const (
	CompassEventType_CET_UNKNOWN CompassEventType = 0
	CompassEventType_CET_ADD     CompassEventType = 1
	CompassEventType_CET_UPDATE  CompassEventType = 2
	CompassEventType_CET_DELETE  CompassEventType = 3
	CompassEventType_CET_RELOAD  CompassEventType = 11
	CompassEventType_CET_DETECT  CompassEventType = 21
	CompassEventType_CET_PUSH    CompassEventType = 22
)

func (p CompassEventType) String() string {
	switch p {
	case CompassEventType_CET_UNKNOWN:
		return "CompassEventType_CET_UNKNOWN"
	case CompassEventType_CET_ADD:
		return "CompassEventType_CET_ADD"
	case CompassEventType_CET_UPDATE:
		return "CompassEventType_CET_UPDATE"
	case CompassEventType_CET_DELETE:
		return "CompassEventType_CET_DELETE"
	case CompassEventType_CET_RELOAD:
		return "CompassEventType_CET_RELOAD"
	case CompassEventType_CET_DETECT:
		return "CompassEventType_CET_DETECT"
	case CompassEventType_CET_PUSH:
		return "CompassEventType_CET_PUSH"
	}
	return "<UNSET>"
}

func CompassEventTypeFromString(s string) (CompassEventType, error) {
	switch s {
	case "CompassEventType_CET_UNKNOWN":
		return CompassEventType_CET_UNKNOWN, nil
	case "CompassEventType_CET_ADD":
		return CompassEventType_CET_ADD, nil
	case "CompassEventType_CET_UPDATE":
		return CompassEventType_CET_UPDATE, nil
	case "CompassEventType_CET_DELETE":
		return CompassEventType_CET_DELETE, nil
	case "CompassEventType_CET_RELOAD":
		return CompassEventType_CET_RELOAD, nil
	case "CompassEventType_CET_DETECT":
		return CompassEventType_CET_DETECT, nil
	case "CompassEventType_CET_PUSH":
		return CompassEventType_CET_PUSH, nil
	}
	return CompassEventType(math.MinInt32 - 1), fmt.Errorf("not a valid CompassEventType string")
}

type CompassEventCategory int64

const (
	CompassEventCategory_CEC_UNKNOWN   CompassEventCategory = 0
	CompassEventCategory_CEC_Crowd     CompassEventCategory = 1
	CompassEventCategory_CEC_LifeCycle CompassEventCategory = 2
	CompassEventCategory_CEC_AppLaunch CompassEventCategory = 3
	CompassEventCategory_CEC_AdPush    CompassEventCategory = 4
)

func (p CompassEventCategory) String() string {
	switch p {
	case CompassEventCategory_CEC_UNKNOWN:
		return "CompassEventCategory_CEC_UNKNOWN"
	case CompassEventCategory_CEC_Crowd:
		return "CompassEventCategory_CEC_Crowd"
	case CompassEventCategory_CEC_LifeCycle:
		return "CompassEventCategory_CEC_LifeCycle"
	case CompassEventCategory_CEC_AppLaunch:
		return "CompassEventCategory_CEC_AppLaunch"
	case CompassEventCategory_CEC_AdPush:
		return "CompassEventCategory_CEC_AdPush"
	}
	return "<UNSET>"
}

func CompassEventCategoryFromString(s string) (CompassEventCategory, error) {
	switch s {
	case "CompassEventCategory_CEC_UNKNOWN":
		return CompassEventCategory_CEC_UNKNOWN, nil
	case "CompassEventCategory_CEC_Crowd":
		return CompassEventCategory_CEC_Crowd, nil
	case "CompassEventCategory_CEC_LifeCycle":
		return CompassEventCategory_CEC_LifeCycle, nil
	case "CompassEventCategory_CEC_AppLaunch":
		return CompassEventCategory_CEC_AppLaunch, nil
	case "CompassEventCategory_CEC_AdPush":
		return CompassEventCategory_CEC_AdPush, nil
	}
	return CompassEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid CompassEventCategory string")
}

type CompassCommonEvent struct {
	TypeA1    CompassEventType             `thrift:"type,1" json:"type"`
	Category  CompassEventCategory         `thrift:"category,2" json:"category"`
	Ids       []int32                      `thrift:"ids,3" json:"ids"`
	Platforms []compass_types.PlatformType `thrift:"platforms,4" json:"platforms"`
}

func NewCompassCommonEvent() *CompassCommonEvent {
	return &CompassCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CompassCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *CompassCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *CompassCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompassCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = CompassEventType(v)
	}
	return nil
}

func (p *CompassCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = CompassEventCategory(v)
	}
	return nil
}

func (p *CompassCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CompassCommonEvent) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platforms = make([]compass_types.PlatformType, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 compass_types.PlatformType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = compass_types.PlatformType(v)
		}
		p.Platforms = append(p.Platforms, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CompassCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CompassCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompassCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *CompassCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *CompassCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *CompassCommonEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Platforms != nil {
		if err := oprot.WriteFieldBegin("platforms", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:platforms: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Platforms)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platforms {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:platforms: %s", p, err)
		}
	}
	return err
}

func (p *CompassCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompassCommonEvent(%+v)", *p)
}
