// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbm_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = rtb_adinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("广告主状态,供查询时使用") *
type SponsorParamsStatus int64

const (
	SponsorParamsStatus_SPS_UNKNOWN     SponsorParamsStatus = 0
	SponsorParamsStatus_SPS_AUDIT       SponsorParamsStatus = 1
	SponsorParamsStatus_SPS_PASS        SponsorParamsStatus = 2
	SponsorParamsStatus_SPS_REJECT      SponsorParamsStatus = 9
	SponsorParamsStatus_SPS_BUDGET_OVER SponsorParamsStatus = 10
)

func (p SponsorParamsStatus) String() string {
	switch p {
	case SponsorParamsStatus_SPS_UNKNOWN:
		return "SponsorParamsStatus_SPS_UNKNOWN"
	case SponsorParamsStatus_SPS_AUDIT:
		return "SponsorParamsStatus_SPS_AUDIT"
	case SponsorParamsStatus_SPS_PASS:
		return "SponsorParamsStatus_SPS_PASS"
	case SponsorParamsStatus_SPS_REJECT:
		return "SponsorParamsStatus_SPS_REJECT"
	case SponsorParamsStatus_SPS_BUDGET_OVER:
		return "SponsorParamsStatus_SPS_BUDGET_OVER"
	}
	return "<UNSET>"
}

func SponsorParamsStatusFromString(s string) (SponsorParamsStatus, error) {
	switch s {
	case "SponsorParamsStatus_SPS_UNKNOWN":
		return SponsorParamsStatus_SPS_UNKNOWN, nil
	case "SponsorParamsStatus_SPS_AUDIT":
		return SponsorParamsStatus_SPS_AUDIT, nil
	case "SponsorParamsStatus_SPS_PASS":
		return SponsorParamsStatus_SPS_PASS, nil
	case "SponsorParamsStatus_SPS_REJECT":
		return SponsorParamsStatus_SPS_REJECT, nil
	case "SponsorParamsStatus_SPS_BUDGET_OVER":
		return SponsorParamsStatus_SPS_BUDGET_OVER, nil
	}
	return SponsorParamsStatus(math.MinInt32 - 1), fmt.Errorf("not a valid SponsorParamsStatus string")
}

//@Description("活动查询状态,供查询时使用") *
type AdCampaignParamsStatus int64

const (
	AdCampaignParamsStatus_ACPS_UNKNOWN           AdCampaignParamsStatus = 0
	AdCampaignParamsStatus_ACPS_NOT_STARTED       AdCampaignParamsStatus = 1
	AdCampaignParamsStatus_ACPS_RUNNING           AdCampaignParamsStatus = 2
	AdCampaignParamsStatus_ACPS_TODAY_ENDED       AdCampaignParamsStatus = 3
	AdCampaignParamsStatus_ACPS_ENDED             AdCampaignParamsStatus = 4
	AdCampaignParamsStatus_ACPS_PAUSED            AdCampaignParamsStatus = 5
	AdCampaignParamsStatus_ACPS_IN_PERIOD         AdCampaignParamsStatus = 6
	AdCampaignParamsStatus_ACPS_TOTAL_BUDGET_OVER AdCampaignParamsStatus = 7
)

func (p AdCampaignParamsStatus) String() string {
	switch p {
	case AdCampaignParamsStatus_ACPS_UNKNOWN:
		return "AdCampaignParamsStatus_ACPS_UNKNOWN"
	case AdCampaignParamsStatus_ACPS_NOT_STARTED:
		return "AdCampaignParamsStatus_ACPS_NOT_STARTED"
	case AdCampaignParamsStatus_ACPS_RUNNING:
		return "AdCampaignParamsStatus_ACPS_RUNNING"
	case AdCampaignParamsStatus_ACPS_TODAY_ENDED:
		return "AdCampaignParamsStatus_ACPS_TODAY_ENDED"
	case AdCampaignParamsStatus_ACPS_ENDED:
		return "AdCampaignParamsStatus_ACPS_ENDED"
	case AdCampaignParamsStatus_ACPS_PAUSED:
		return "AdCampaignParamsStatus_ACPS_PAUSED"
	case AdCampaignParamsStatus_ACPS_IN_PERIOD:
		return "AdCampaignParamsStatus_ACPS_IN_PERIOD"
	case AdCampaignParamsStatus_ACPS_TOTAL_BUDGET_OVER:
		return "AdCampaignParamsStatus_ACPS_TOTAL_BUDGET_OVER"
	}
	return "<UNSET>"
}

func AdCampaignParamsStatusFromString(s string) (AdCampaignParamsStatus, error) {
	switch s {
	case "AdCampaignParamsStatus_ACPS_UNKNOWN":
		return AdCampaignParamsStatus_ACPS_UNKNOWN, nil
	case "AdCampaignParamsStatus_ACPS_NOT_STARTED":
		return AdCampaignParamsStatus_ACPS_NOT_STARTED, nil
	case "AdCampaignParamsStatus_ACPS_RUNNING":
		return AdCampaignParamsStatus_ACPS_RUNNING, nil
	case "AdCampaignParamsStatus_ACPS_TODAY_ENDED":
		return AdCampaignParamsStatus_ACPS_TODAY_ENDED, nil
	case "AdCampaignParamsStatus_ACPS_ENDED":
		return AdCampaignParamsStatus_ACPS_ENDED, nil
	case "AdCampaignParamsStatus_ACPS_PAUSED":
		return AdCampaignParamsStatus_ACPS_PAUSED, nil
	case "AdCampaignParamsStatus_ACPS_IN_PERIOD":
		return AdCampaignParamsStatus_ACPS_IN_PERIOD, nil
	case "AdCampaignParamsStatus_ACPS_TOTAL_BUDGET_OVER":
		return AdCampaignParamsStatus_ACPS_TOTAL_BUDGET_OVER, nil
	}
	return AdCampaignParamsStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdCampaignParamsStatus string")
}

//@Description("策略查询状态,供查询时使用") *
type AdStrategyParamsStatus int64

const (
	AdStrategyParamsStatus_ASPS_UNKNOWN     AdStrategyParamsStatus = 0
	AdStrategyParamsStatus_ASPS_RUNNABLE    AdStrategyParamsStatus = 1
	AdStrategyParamsStatus_ASPS_PAUSED      AdStrategyParamsStatus = 2
	AdStrategyParamsStatus_ASPS_TODAY_ENDED AdStrategyParamsStatus = 3
)

func (p AdStrategyParamsStatus) String() string {
	switch p {
	case AdStrategyParamsStatus_ASPS_UNKNOWN:
		return "AdStrategyParamsStatus_ASPS_UNKNOWN"
	case AdStrategyParamsStatus_ASPS_RUNNABLE:
		return "AdStrategyParamsStatus_ASPS_RUNNABLE"
	case AdStrategyParamsStatus_ASPS_PAUSED:
		return "AdStrategyParamsStatus_ASPS_PAUSED"
	case AdStrategyParamsStatus_ASPS_TODAY_ENDED:
		return "AdStrategyParamsStatus_ASPS_TODAY_ENDED"
	}
	return "<UNSET>"
}

func AdStrategyParamsStatusFromString(s string) (AdStrategyParamsStatus, error) {
	switch s {
	case "AdStrategyParamsStatus_ASPS_UNKNOWN":
		return AdStrategyParamsStatus_ASPS_UNKNOWN, nil
	case "AdStrategyParamsStatus_ASPS_RUNNABLE":
		return AdStrategyParamsStatus_ASPS_RUNNABLE, nil
	case "AdStrategyParamsStatus_ASPS_PAUSED":
		return AdStrategyParamsStatus_ASPS_PAUSED, nil
	case "AdStrategyParamsStatus_ASPS_TODAY_ENDED":
		return AdStrategyParamsStatus_ASPS_TODAY_ENDED, nil
	}
	return AdStrategyParamsStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdStrategyParamsStatus string")
}

//@Description("创意查询状态,供查询时使用") *
type AdCreativeParamsStatus int64

const (
	AdCreativeParamsStatus_ASPS_UNKNOWN  AdCreativeParamsStatus = 0
	AdCreativeParamsStatus_ASPS_RUNNABLE AdCreativeParamsStatus = 1
	AdCreativeParamsStatus_ASPS_PAUSED   AdCreativeParamsStatus = 2
	AdCreativeParamsStatus_ASPS_IN_AUDIT AdCreativeParamsStatus = 3
	AdCreativeParamsStatus_ASPS_REJECTED AdCreativeParamsStatus = 4
)

func (p AdCreativeParamsStatus) String() string {
	switch p {
	case AdCreativeParamsStatus_ASPS_UNKNOWN:
		return "AdCreativeParamsStatus_ASPS_UNKNOWN"
	case AdCreativeParamsStatus_ASPS_RUNNABLE:
		return "AdCreativeParamsStatus_ASPS_RUNNABLE"
	case AdCreativeParamsStatus_ASPS_PAUSED:
		return "AdCreativeParamsStatus_ASPS_PAUSED"
	case AdCreativeParamsStatus_ASPS_IN_AUDIT:
		return "AdCreativeParamsStatus_ASPS_IN_AUDIT"
	case AdCreativeParamsStatus_ASPS_REJECTED:
		return "AdCreativeParamsStatus_ASPS_REJECTED"
	}
	return "<UNSET>"
}

func AdCreativeParamsStatusFromString(s string) (AdCreativeParamsStatus, error) {
	switch s {
	case "AdCreativeParamsStatus_ASPS_UNKNOWN":
		return AdCreativeParamsStatus_ASPS_UNKNOWN, nil
	case "AdCreativeParamsStatus_ASPS_RUNNABLE":
		return AdCreativeParamsStatus_ASPS_RUNNABLE, nil
	case "AdCreativeParamsStatus_ASPS_PAUSED":
		return AdCreativeParamsStatus_ASPS_PAUSED, nil
	case "AdCreativeParamsStatus_ASPS_IN_AUDIT":
		return AdCreativeParamsStatus_ASPS_IN_AUDIT, nil
	case "AdCreativeParamsStatus_ASPS_REJECTED":
		return AdCreativeParamsStatus_ASPS_REJECTED, nil
	}
	return AdCreativeParamsStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdCreativeParamsStatus string")
}

//@Description("广告主审核状态") *
type SponsorAuditStatus int64

const (
	SponsorAuditStatus_SAS_UNKNOWN SponsorAuditStatus = 0
	SponsorAuditStatus_SAS_AUDIT   SponsorAuditStatus = 1
	SponsorAuditStatus_SAS_PASS    SponsorAuditStatus = 2
	SponsorAuditStatus_SAS_REJECT  SponsorAuditStatus = 9
)

func (p SponsorAuditStatus) String() string {
	switch p {
	case SponsorAuditStatus_SAS_UNKNOWN:
		return "SponsorAuditStatus_SAS_UNKNOWN"
	case SponsorAuditStatus_SAS_AUDIT:
		return "SponsorAuditStatus_SAS_AUDIT"
	case SponsorAuditStatus_SAS_PASS:
		return "SponsorAuditStatus_SAS_PASS"
	case SponsorAuditStatus_SAS_REJECT:
		return "SponsorAuditStatus_SAS_REJECT"
	}
	return "<UNSET>"
}

func SponsorAuditStatusFromString(s string) (SponsorAuditStatus, error) {
	switch s {
	case "SponsorAuditStatus_SAS_UNKNOWN":
		return SponsorAuditStatus_SAS_UNKNOWN, nil
	case "SponsorAuditStatus_SAS_AUDIT":
		return SponsorAuditStatus_SAS_AUDIT, nil
	case "SponsorAuditStatus_SAS_PASS":
		return SponsorAuditStatus_SAS_PASS, nil
	case "SponsorAuditStatus_SAS_REJECT":
		return SponsorAuditStatus_SAS_REJECT, nil
	}
	return SponsorAuditStatus(math.MinInt32 - 1), fmt.Errorf("not a valid SponsorAuditStatus string")
}

//@Description("创意审核状态") *
type CreativeAuditStatus int64

const (
	CreativeAuditStatus_CAS_UNKNOWN  CreativeAuditStatus = 0
	CreativeAuditStatus_CAS_AUDIT    CreativeAuditStatus = 1
	CreativeAuditStatus_CAS_RUNNABLE CreativeAuditStatus = 2
	CreativeAuditStatus_CAS_RUNNING  CreativeAuditStatus = 3
	CreativeAuditStatus_CAS_REJECT   CreativeAuditStatus = 9
)

func (p CreativeAuditStatus) String() string {
	switch p {
	case CreativeAuditStatus_CAS_UNKNOWN:
		return "CreativeAuditStatus_CAS_UNKNOWN"
	case CreativeAuditStatus_CAS_AUDIT:
		return "CreativeAuditStatus_CAS_AUDIT"
	case CreativeAuditStatus_CAS_RUNNABLE:
		return "CreativeAuditStatus_CAS_RUNNABLE"
	case CreativeAuditStatus_CAS_RUNNING:
		return "CreativeAuditStatus_CAS_RUNNING"
	case CreativeAuditStatus_CAS_REJECT:
		return "CreativeAuditStatus_CAS_REJECT"
	}
	return "<UNSET>"
}

func CreativeAuditStatusFromString(s string) (CreativeAuditStatus, error) {
	switch s {
	case "CreativeAuditStatus_CAS_UNKNOWN":
		return CreativeAuditStatus_CAS_UNKNOWN, nil
	case "CreativeAuditStatus_CAS_AUDIT":
		return CreativeAuditStatus_CAS_AUDIT, nil
	case "CreativeAuditStatus_CAS_RUNNABLE":
		return CreativeAuditStatus_CAS_RUNNABLE, nil
	case "CreativeAuditStatus_CAS_RUNNING":
		return CreativeAuditStatus_CAS_RUNNING, nil
	case "CreativeAuditStatus_CAS_REJECT":
		return CreativeAuditStatus_CAS_REJECT, nil
	}
	return CreativeAuditStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeAuditStatus string")
}

//@Description("dsp service内部定义的广告形式，会和rtb的交易所以及尺寸会有映射关系") *
type DBMPlacementType int64

const (
	DBMPlacementType_DPT_UNKNOWN                     DBMPlacementType = 0
	DBMPlacementType_DPT_BANNER                      DBMPlacementType = 1
	DBMPlacementType_DPT_WX_BANNER                   DBMPlacementType = 2
	DBMPlacementType_DPT_INTERSTITIAL                DBMPlacementType = 3
	DBMPlacementType_DPT_TOUTIAO_NATIVE              DBMPlacementType = 4
	DBMPlacementType_DPT_QQ_NATIVE                   DBMPlacementType = 5
	DBMPlacementType_DPT_POPUP                       DBMPlacementType = 6
	DBMPlacementType_DPT_PATCH                       DBMPlacementType = 7
	DBMPlacementType_DPT_MID_ROLL                    DBMPlacementType = 8
	DBMPlacementType_DPT_POST_ROLL                   DBMPlacementType = 9
	DBMPlacementType_DPT_TIEBA_NATIVE                DBMPlacementType = 10
	DBMPlacementType_DPT_NEIHAN_NATIVE               DBMPlacementType = 11
	DBMPlacementType_DPT_WEIBO_NATIVE                DBMPlacementType = 12
	DBMPlacementType_DPT_GDT_ALLIANCE_NATIVE         DBMPlacementType = 13
	DBMPlacementType_DPT_MOMO_NATIVE                 DBMPlacementType = 14
	DBMPlacementType_DPT_INMOBI_NATIVE               DBMPlacementType = 15
	DBMPlacementType_DPT_YOUKU_NATIVE                DBMPlacementType = 16
	DBMPlacementType_DPT_SHORT_VIDEO                 DBMPlacementType = 17
	DBMPlacementType_DPT_VIDEO                       DBMPlacementType = 20
	DBMPlacementType_DPT_QQ_QC_NATIVE                DBMPlacementType = 21
	DBMPlacementType_DPT_GDT_ALLIANCE_QC_NATIVE      DBMPlacementType = 22
	DBMPlacementType_DPT_AUTOHOME_NATIVE             DBMPlacementType = 23
	DBMPlacementType_DPT_MAX_NATIVE                  DBMPlacementType = 24
	DBMPlacementType_DPT_GDT_ALLIANCE_SHORT_VIDEO    DBMPlacementType = 50
	DBMPlacementType_DPT_GDT_ALLIANCE_QC_SHORT_VIDEO DBMPlacementType = 51
	DBMPlacementType_DPT_QQ_SHORT_VIDEO              DBMPlacementType = 52
	DBMPlacementType_DPT_QQ_QC_SHORT_VIDEO           DBMPlacementType = 53
	DBMPlacementType_DPT_WEIBO_SHORT_VIDEO           DBMPlacementType = 54
	DBMPlacementType_DPT_TOUTIAO_SHORT_VIDEO         DBMPlacementType = 55
	DBMPlacementType_DPT_NEIHAN_SHORT_VIDEO          DBMPlacementType = 56
	DBMPlacementType_DPT_MOMO_SHORT_VIDEO            DBMPlacementType = 57
	DBMPlacementType_DPT_INMOBI_SHORT_VIDEO          DBMPlacementType = 58
)

func (p DBMPlacementType) String() string {
	switch p {
	case DBMPlacementType_DPT_UNKNOWN:
		return "DBMPlacementType_DPT_UNKNOWN"
	case DBMPlacementType_DPT_BANNER:
		return "DBMPlacementType_DPT_BANNER"
	case DBMPlacementType_DPT_WX_BANNER:
		return "DBMPlacementType_DPT_WX_BANNER"
	case DBMPlacementType_DPT_INTERSTITIAL:
		return "DBMPlacementType_DPT_INTERSTITIAL"
	case DBMPlacementType_DPT_TOUTIAO_NATIVE:
		return "DBMPlacementType_DPT_TOUTIAO_NATIVE"
	case DBMPlacementType_DPT_QQ_NATIVE:
		return "DBMPlacementType_DPT_QQ_NATIVE"
	case DBMPlacementType_DPT_POPUP:
		return "DBMPlacementType_DPT_POPUP"
	case DBMPlacementType_DPT_PATCH:
		return "DBMPlacementType_DPT_PATCH"
	case DBMPlacementType_DPT_MID_ROLL:
		return "DBMPlacementType_DPT_MID_ROLL"
	case DBMPlacementType_DPT_POST_ROLL:
		return "DBMPlacementType_DPT_POST_ROLL"
	case DBMPlacementType_DPT_TIEBA_NATIVE:
		return "DBMPlacementType_DPT_TIEBA_NATIVE"
	case DBMPlacementType_DPT_NEIHAN_NATIVE:
		return "DBMPlacementType_DPT_NEIHAN_NATIVE"
	case DBMPlacementType_DPT_WEIBO_NATIVE:
		return "DBMPlacementType_DPT_WEIBO_NATIVE"
	case DBMPlacementType_DPT_GDT_ALLIANCE_NATIVE:
		return "DBMPlacementType_DPT_GDT_ALLIANCE_NATIVE"
	case DBMPlacementType_DPT_MOMO_NATIVE:
		return "DBMPlacementType_DPT_MOMO_NATIVE"
	case DBMPlacementType_DPT_INMOBI_NATIVE:
		return "DBMPlacementType_DPT_INMOBI_NATIVE"
	case DBMPlacementType_DPT_YOUKU_NATIVE:
		return "DBMPlacementType_DPT_YOUKU_NATIVE"
	case DBMPlacementType_DPT_SHORT_VIDEO:
		return "DBMPlacementType_DPT_SHORT_VIDEO"
	case DBMPlacementType_DPT_VIDEO:
		return "DBMPlacementType_DPT_VIDEO"
	case DBMPlacementType_DPT_QQ_QC_NATIVE:
		return "DBMPlacementType_DPT_QQ_QC_NATIVE"
	case DBMPlacementType_DPT_GDT_ALLIANCE_QC_NATIVE:
		return "DBMPlacementType_DPT_GDT_ALLIANCE_QC_NATIVE"
	case DBMPlacementType_DPT_AUTOHOME_NATIVE:
		return "DBMPlacementType_DPT_AUTOHOME_NATIVE"
	case DBMPlacementType_DPT_MAX_NATIVE:
		return "DBMPlacementType_DPT_MAX_NATIVE"
	case DBMPlacementType_DPT_GDT_ALLIANCE_SHORT_VIDEO:
		return "DBMPlacementType_DPT_GDT_ALLIANCE_SHORT_VIDEO"
	case DBMPlacementType_DPT_GDT_ALLIANCE_QC_SHORT_VIDEO:
		return "DBMPlacementType_DPT_GDT_ALLIANCE_QC_SHORT_VIDEO"
	case DBMPlacementType_DPT_QQ_SHORT_VIDEO:
		return "DBMPlacementType_DPT_QQ_SHORT_VIDEO"
	case DBMPlacementType_DPT_QQ_QC_SHORT_VIDEO:
		return "DBMPlacementType_DPT_QQ_QC_SHORT_VIDEO"
	case DBMPlacementType_DPT_WEIBO_SHORT_VIDEO:
		return "DBMPlacementType_DPT_WEIBO_SHORT_VIDEO"
	case DBMPlacementType_DPT_TOUTIAO_SHORT_VIDEO:
		return "DBMPlacementType_DPT_TOUTIAO_SHORT_VIDEO"
	case DBMPlacementType_DPT_NEIHAN_SHORT_VIDEO:
		return "DBMPlacementType_DPT_NEIHAN_SHORT_VIDEO"
	case DBMPlacementType_DPT_MOMO_SHORT_VIDEO:
		return "DBMPlacementType_DPT_MOMO_SHORT_VIDEO"
	case DBMPlacementType_DPT_INMOBI_SHORT_VIDEO:
		return "DBMPlacementType_DPT_INMOBI_SHORT_VIDEO"
	}
	return "<UNSET>"
}

func DBMPlacementTypeFromString(s string) (DBMPlacementType, error) {
	switch s {
	case "DBMPlacementType_DPT_UNKNOWN":
		return DBMPlacementType_DPT_UNKNOWN, nil
	case "DBMPlacementType_DPT_BANNER":
		return DBMPlacementType_DPT_BANNER, nil
	case "DBMPlacementType_DPT_WX_BANNER":
		return DBMPlacementType_DPT_WX_BANNER, nil
	case "DBMPlacementType_DPT_INTERSTITIAL":
		return DBMPlacementType_DPT_INTERSTITIAL, nil
	case "DBMPlacementType_DPT_TOUTIAO_NATIVE":
		return DBMPlacementType_DPT_TOUTIAO_NATIVE, nil
	case "DBMPlacementType_DPT_QQ_NATIVE":
		return DBMPlacementType_DPT_QQ_NATIVE, nil
	case "DBMPlacementType_DPT_POPUP":
		return DBMPlacementType_DPT_POPUP, nil
	case "DBMPlacementType_DPT_PATCH":
		return DBMPlacementType_DPT_PATCH, nil
	case "DBMPlacementType_DPT_MID_ROLL":
		return DBMPlacementType_DPT_MID_ROLL, nil
	case "DBMPlacementType_DPT_POST_ROLL":
		return DBMPlacementType_DPT_POST_ROLL, nil
	case "DBMPlacementType_DPT_TIEBA_NATIVE":
		return DBMPlacementType_DPT_TIEBA_NATIVE, nil
	case "DBMPlacementType_DPT_NEIHAN_NATIVE":
		return DBMPlacementType_DPT_NEIHAN_NATIVE, nil
	case "DBMPlacementType_DPT_WEIBO_NATIVE":
		return DBMPlacementType_DPT_WEIBO_NATIVE, nil
	case "DBMPlacementType_DPT_GDT_ALLIANCE_NATIVE":
		return DBMPlacementType_DPT_GDT_ALLIANCE_NATIVE, nil
	case "DBMPlacementType_DPT_MOMO_NATIVE":
		return DBMPlacementType_DPT_MOMO_NATIVE, nil
	case "DBMPlacementType_DPT_INMOBI_NATIVE":
		return DBMPlacementType_DPT_INMOBI_NATIVE, nil
	case "DBMPlacementType_DPT_YOUKU_NATIVE":
		return DBMPlacementType_DPT_YOUKU_NATIVE, nil
	case "DBMPlacementType_DPT_SHORT_VIDEO":
		return DBMPlacementType_DPT_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_VIDEO":
		return DBMPlacementType_DPT_VIDEO, nil
	case "DBMPlacementType_DPT_QQ_QC_NATIVE":
		return DBMPlacementType_DPT_QQ_QC_NATIVE, nil
	case "DBMPlacementType_DPT_GDT_ALLIANCE_QC_NATIVE":
		return DBMPlacementType_DPT_GDT_ALLIANCE_QC_NATIVE, nil
	case "DBMPlacementType_DPT_AUTOHOME_NATIVE":
		return DBMPlacementType_DPT_AUTOHOME_NATIVE, nil
	case "DBMPlacementType_DPT_MAX_NATIVE":
		return DBMPlacementType_DPT_MAX_NATIVE, nil
	case "DBMPlacementType_DPT_GDT_ALLIANCE_SHORT_VIDEO":
		return DBMPlacementType_DPT_GDT_ALLIANCE_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_GDT_ALLIANCE_QC_SHORT_VIDEO":
		return DBMPlacementType_DPT_GDT_ALLIANCE_QC_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_QQ_SHORT_VIDEO":
		return DBMPlacementType_DPT_QQ_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_QQ_QC_SHORT_VIDEO":
		return DBMPlacementType_DPT_QQ_QC_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_WEIBO_SHORT_VIDEO":
		return DBMPlacementType_DPT_WEIBO_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_TOUTIAO_SHORT_VIDEO":
		return DBMPlacementType_DPT_TOUTIAO_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_NEIHAN_SHORT_VIDEO":
		return DBMPlacementType_DPT_NEIHAN_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_MOMO_SHORT_VIDEO":
		return DBMPlacementType_DPT_MOMO_SHORT_VIDEO, nil
	case "DBMPlacementType_DPT_INMOBI_SHORT_VIDEO":
		return DBMPlacementType_DPT_INMOBI_SHORT_VIDEO, nil
	}
	return DBMPlacementType(math.MinInt32 - 1), fmt.Errorf("not a valid DBMPlacementType string")
}

//dsp service 中各个信息里所显示的状态信息集合,由server端计算得到对应的状态描述 *
type DBMDisplayStatusLamp int64

const (
	DBMDisplayStatusLamp_DSL_UNKNOWN           DBMDisplayStatusLamp = 0
	DBMDisplayStatusLamp_DSL_IN_AUDIT          DBMDisplayStatusLamp = 1
	DBMDisplayStatusLamp_DSL_AUDIT_PASS        DBMDisplayStatusLamp = 2
	DBMDisplayStatusLamp_DSL_AUDIT_REJECT      DBMDisplayStatusLamp = 3
	DBMDisplayStatusLamp_DSL_RUNNING           DBMDisplayStatusLamp = 10
	DBMDisplayStatusLamp_DSL_PAUSED            DBMDisplayStatusLamp = 11
	DBMDisplayStatusLamp_DSL_NOT_START         DBMDisplayStatusLamp = 12
	DBMDisplayStatusLamp_DSL_ENDED             DBMDisplayStatusLamp = 13
	DBMDisplayStatusLamp_DSL_TODAY_ENDED       DBMDisplayStatusLamp = 14
	DBMDisplayStatusLamp_DSL_TOTAL_BUDGET_OVER DBMDisplayStatusLamp = 15
)

func (p DBMDisplayStatusLamp) String() string {
	switch p {
	case DBMDisplayStatusLamp_DSL_UNKNOWN:
		return "DBMDisplayStatusLamp_DSL_UNKNOWN"
	case DBMDisplayStatusLamp_DSL_IN_AUDIT:
		return "DBMDisplayStatusLamp_DSL_IN_AUDIT"
	case DBMDisplayStatusLamp_DSL_AUDIT_PASS:
		return "DBMDisplayStatusLamp_DSL_AUDIT_PASS"
	case DBMDisplayStatusLamp_DSL_AUDIT_REJECT:
		return "DBMDisplayStatusLamp_DSL_AUDIT_REJECT"
	case DBMDisplayStatusLamp_DSL_RUNNING:
		return "DBMDisplayStatusLamp_DSL_RUNNING"
	case DBMDisplayStatusLamp_DSL_PAUSED:
		return "DBMDisplayStatusLamp_DSL_PAUSED"
	case DBMDisplayStatusLamp_DSL_NOT_START:
		return "DBMDisplayStatusLamp_DSL_NOT_START"
	case DBMDisplayStatusLamp_DSL_ENDED:
		return "DBMDisplayStatusLamp_DSL_ENDED"
	case DBMDisplayStatusLamp_DSL_TODAY_ENDED:
		return "DBMDisplayStatusLamp_DSL_TODAY_ENDED"
	case DBMDisplayStatusLamp_DSL_TOTAL_BUDGET_OVER:
		return "DBMDisplayStatusLamp_DSL_TOTAL_BUDGET_OVER"
	}
	return "<UNSET>"
}

func DBMDisplayStatusLampFromString(s string) (DBMDisplayStatusLamp, error) {
	switch s {
	case "DBMDisplayStatusLamp_DSL_UNKNOWN":
		return DBMDisplayStatusLamp_DSL_UNKNOWN, nil
	case "DBMDisplayStatusLamp_DSL_IN_AUDIT":
		return DBMDisplayStatusLamp_DSL_IN_AUDIT, nil
	case "DBMDisplayStatusLamp_DSL_AUDIT_PASS":
		return DBMDisplayStatusLamp_DSL_AUDIT_PASS, nil
	case "DBMDisplayStatusLamp_DSL_AUDIT_REJECT":
		return DBMDisplayStatusLamp_DSL_AUDIT_REJECT, nil
	case "DBMDisplayStatusLamp_DSL_RUNNING":
		return DBMDisplayStatusLamp_DSL_RUNNING, nil
	case "DBMDisplayStatusLamp_DSL_PAUSED":
		return DBMDisplayStatusLamp_DSL_PAUSED, nil
	case "DBMDisplayStatusLamp_DSL_NOT_START":
		return DBMDisplayStatusLamp_DSL_NOT_START, nil
	case "DBMDisplayStatusLamp_DSL_ENDED":
		return DBMDisplayStatusLamp_DSL_ENDED, nil
	case "DBMDisplayStatusLamp_DSL_TODAY_ENDED":
		return DBMDisplayStatusLamp_DSL_TODAY_ENDED, nil
	case "DBMDisplayStatusLamp_DSL_TOTAL_BUDGET_OVER":
		return DBMDisplayStatusLamp_DSL_TOTAL_BUDGET_OVER, nil
	}
	return DBMDisplayStatusLamp(math.MinInt32 - 1), fmt.Errorf("not a valid DBMDisplayStatusLamp string")
}

type SponsorParams struct {
	AgentUid int32               `thrift:"agentUid,1" json:"agentUid"`
	Status   SponsorParamsStatus `thrift:"status,2" json:"status"`
	Name     string              `thrift:"name,3" json:"name"`
	Email    string              `thrift:"email,4" json:"email"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset int32 `thrift:"offset,10" json:"offset"`
	Limit  int32 `thrift:"limit,11" json:"limit"`
}

func NewSponsorParams() *SponsorParams {
	return &SponsorParams{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SponsorParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *SponsorParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = SponsorParamsStatus(v)
	}
	return nil
}

func (p *SponsorParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SponsorParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *SponsorParams) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SponsorParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SponsorParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *SponsorParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *SponsorParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:email: %s", p, err)
	}
	return err
}

func (p *SponsorParams) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *SponsorParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *SponsorParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorParams(%+v)", *p)
}

type AdOrderParams struct {
	SponsorIds []int32 `thrift:"sponsorIds,1" json:"sponsorIds"`
	Status     int32   `thrift:"status,2" json:"status"`
	Name       string  `thrift:"name,3" json:"name"`
	Offset     int32   `thrift:"offset,4" json:"offset"`
	Limit      int32   `thrift:"limit,5" json:"limit"`
}

func NewAdOrderParams() *AdOrderParams {
	return &AdOrderParams{}
}

func (p *AdOrderParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdOrderParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SponsorIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.SponsorIds = append(p.SponsorIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdOrderParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdOrderParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdOrderParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AdOrderParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AdOrderParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdOrderParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdOrderParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SponsorIds != nil {
		if err := oprot.WriteFieldBegin("sponsorIds", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sponsorIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SponsorIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SponsorIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sponsorIds: %s", p, err)
		}
	}
	return err
}

func (p *AdOrderParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:status: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdOrderParams(%+v)", *p)
}

type AdCampaignParams struct {
	SponsorIds   []int32                `thrift:"sponsorIds,1" json:"sponsorIds"`
	OrderIds     []int32                `thrift:"orderIds,2" json:"orderIds"`
	Status       AdCampaignParamsStatus `thrift:"status,3" json:"status"`
	Name         string                 `thrift:"name,4" json:"name"`
	Offset       int32                  `thrift:"offset,5" json:"offset"`
	Limit        int32                  `thrift:"limit,6" json:"limit"`
	PromotionIds []int32                `thrift:"promotionIds,7" json:"promotionIds"`
}

func NewAdCampaignParams() *AdCampaignParams {
	return &AdCampaignParams{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCampaignParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdCampaignParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCampaignParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SponsorIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.SponsorIds = append(p.SponsorIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCampaignParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.OrderIds = append(p.OrderIds, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCampaignParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = AdCampaignParamsStatus(v)
	}
	return nil
}

func (p *AdCampaignParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdCampaignParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AdCampaignParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AdCampaignParams) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PromotionIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.PromotionIds = append(p.PromotionIds, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCampaignParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCampaignParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCampaignParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SponsorIds != nil {
		if err := oprot.WriteFieldBegin("sponsorIds", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sponsorIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SponsorIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SponsorIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sponsorIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaignParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaignParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:status: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaignParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AdCampaignParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *AdCampaignParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *AdCampaignParams) writeField7(oprot thrift.TProtocol) (err error) {
	if p.PromotionIds != nil {
		if err := oprot.WriteFieldBegin("promotionIds", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:promotionIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PromotionIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PromotionIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:promotionIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaignParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCampaignParams(%+v)", *p)
}

type AdStrategyParams struct {
	SponsorIds   []int32                `thrift:"sponsorIds,1" json:"sponsorIds"`
	OrderIds     []int32                `thrift:"orderIds,2" json:"orderIds"`
	CampaignIds  []int32                `thrift:"campaignIds,3" json:"campaignIds"`
	Status       AdStrategyParamsStatus `thrift:"status,4" json:"status"`
	Name         string                 `thrift:"name,5" json:"name"`
	Offset       int32                  `thrift:"offset,6" json:"offset"`
	Limit        int32                  `thrift:"limit,7" json:"limit"`
	PromotionIds []int32                `thrift:"promotionIds,8" json:"promotionIds"`
}

func NewAdStrategyParams() *AdStrategyParams {
	return &AdStrategyParams{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdStrategyParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdStrategyParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStrategyParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SponsorIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.SponsorIds = append(p.SponsorIds, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategyParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.OrderIds = append(p.OrderIds, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategyParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategyParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = AdStrategyParamsStatus(v)
	}
	return nil
}

func (p *AdStrategyParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdStrategyParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AdStrategyParams) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AdStrategyParams) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PromotionIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.PromotionIds = append(p.PromotionIds, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategyParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStrategyParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStrategyParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SponsorIds != nil {
		if err := oprot.WriteFieldBegin("sponsorIds", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sponsorIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SponsorIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SponsorIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sponsorIds: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:status: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *AdStrategyParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:offset: %s", p, err)
	}
	return err
}

func (p *AdStrategyParams) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:limit: %s", p, err)
	}
	return err
}

func (p *AdStrategyParams) writeField8(oprot thrift.TProtocol) (err error) {
	if p.PromotionIds != nil {
		if err := oprot.WriteFieldBegin("promotionIds", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:promotionIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PromotionIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PromotionIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:promotionIds: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStrategyParams(%+v)", *p)
}

type AdCreativeParams struct {
	SponsorIds   []int32                `thrift:"sponsorIds,1" json:"sponsorIds"`
	OrderIds     []int32                `thrift:"orderIds,2" json:"orderIds"`
	CampaignIds  []int32                `thrift:"campaignIds,3" json:"campaignIds"`
	StrategyIds  []int32                `thrift:"strategyIds,4" json:"strategyIds"`
	Status       AdCreativeParamsStatus `thrift:"status,5" json:"status"`
	Name         string                 `thrift:"name,6" json:"name"`
	Offset       int32                  `thrift:"offset,7" json:"offset"`
	Limit        int32                  `thrift:"limit,8" json:"limit"`
	PromotionIds []int32                `thrift:"promotionIds,9" json:"promotionIds"`
}

func NewAdCreativeParams() *AdCreativeParams {
	return &AdCreativeParams{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCreativeParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdCreativeParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SponsorIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.SponsorIds = append(p.SponsorIds, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.OrderIds = append(p.OrderIds, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StrategyIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.StrategyIds = append(p.StrategyIds, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = AdCreativeParamsStatus(v)
	}
	return nil
}

func (p *AdCreativeParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdCreativeParams) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AdCreativeParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AdCreativeParams) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PromotionIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.PromotionIds = append(p.PromotionIds, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCreativeParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SponsorIds != nil {
		if err := oprot.WriteFieldBegin("sponsorIds", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sponsorIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SponsorIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SponsorIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sponsorIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.StrategyIds != nil {
		if err := oprot.WriteFieldBegin("strategyIds", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:strategyIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.StrategyIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StrategyIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:strategyIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeParams) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:name: %s", p, err)
	}
	return err
}

func (p *AdCreativeParams) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:offset: %s", p, err)
	}
	return err
}

func (p *AdCreativeParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:limit: %s", p, err)
	}
	return err
}

func (p *AdCreativeParams) writeField9(oprot thrift.TProtocol) (err error) {
	if p.PromotionIds != nil {
		if err := oprot.WriteFieldBegin("promotionIds", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:promotionIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PromotionIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PromotionIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:promotionIds: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCreativeParams(%+v)", *p)
}

type AuditInfo struct {
	CreativeId  int32                                `thrift:"creativeId,1" json:"creativeId"`
	SponsorId   int32                                `thrift:"sponsorId,2" json:"sponsorId"`
	ExchangeId  int32                                `thrift:"exchangeId,3" json:"exchangeId"`
	AuditType   int32                                `thrift:"auditType,4" json:"auditType"`
	AuditStatus rtb_adinfo_types.ExchangeAuditStatus `thrift:"auditStatus,5" json:"auditStatus"`
	Reason      string                               `thrift:"reason,6" json:"reason"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64 `thrift:"createTime,20" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,21" json:"lastupdate"`
}

func NewAuditInfo() *AuditInfo {
	return &AuditInfo{
		AuditStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AuditInfo) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *AuditInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AuditInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *AuditInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AuditInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *AuditInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AuditType = v
	}
	return nil
}

func (p *AuditInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AuditStatus = rtb_adinfo_types.ExchangeAuditStatus(v)
	}
	return nil
}

func (p *AuditInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Reason = v
	}
	return nil
}

func (p *AuditInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AuditInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AuditInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AuditInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AuditInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creativeId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:creativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creativeId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:creativeId: %s", p, err)
	}
	return err
}

func (p *AuditInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsorId: %s", p, err)
	}
	return err
}

func (p *AuditInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exchangeId: %s", p, err)
	}
	return err
}

func (p *AuditInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auditType", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:auditType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AuditType)); err != nil {
		return fmt.Errorf("%T.auditType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:auditType: %s", p, err)
	}
	return err
}

func (p *AuditInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *AuditInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reason", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:reason: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Reason)); err != nil {
		return fmt.Errorf("%T.reason (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:reason: %s", p, err)
	}
	return err
}

func (p *AuditInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *AuditInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastupdate: %s", p, err)
	}
	return err
}

func (p *AuditInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuditInfo(%+v)", *p)
}

type Qualification struct {
	Name string `thrift:"name,1" json:"name"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	FileUrl string `thrift:"fileUrl,10" json:"fileUrl"`
}

func NewQualification() *Qualification {
	return &Qualification{}
}

func (p *Qualification) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Qualification) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Qualification) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.FileUrl = v
	}
	return nil
}

func (p *Qualification) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Qualification"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Qualification) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Qualification) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileUrl", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:fileUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileUrl)); err != nil {
		return fmt.Errorf("%T.fileUrl (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:fileUrl: %s", p, err)
	}
	return err
}

func (p *Qualification) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Qualification(%+v)", *p)
}

type SponsorProfile struct {
	AgentUid  int32  `thrift:"agentUid,1" json:"agentUid"`
	SponsorId int32  `thrift:"sponsorId,2" json:"sponsorId"`
	Email     string `thrift:"email,3" json:"email"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CompanyName string `thrift:"companyName,10" json:"companyName"`
	Note        string `thrift:"note,11" json:"note"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	SiteName       string               `thrift:"siteName,20" json:"siteName"`
	SiteUrl        string               `thrift:"siteUrl,21" json:"siteUrl"`
	TradeCategory  map[int32]string     `thrift:"tradeCategory,22" json:"tradeCategory"`
	Certificate    string               `thrift:"certificate,23" json:"certificate"`
	IcpRecord      string               `thrift:"icpRecord,24" json:"icpRecord"`
	BrandName      string               `thrift:"brandName,25" json:"brandName"`
	BrandLogo      string               `thrift:"brandLogo,26" json:"brandLogo"`
	Qualifications []*Qualification     `thrift:"qualifications,27" json:"qualifications"`
	Qq             string               `thrift:"qq,28" json:"qq"`
	Attrs          map[string]string    `thrift:"attrs,29" json:"attrs"`
	AuditStatus    SponsorAuditStatus   `thrift:"auditStatus,30" json:"auditStatus"`
	AuditInfo      map[int32]*AuditInfo `thrift:"auditInfo,31" json:"auditInfo"`
	Status         int32                `thrift:"status,32" json:"status"`
	StatusLamp     DBMDisplayStatusLamp `thrift:"statusLamp,33" json:"statusLamp"`
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	CreateTime int64 `thrift:"createTime,40" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,41" json:"lastupdate"`
}

func NewSponsorProfile() *SponsorProfile {
	return &SponsorProfile{
		AuditStatus: math.MinInt32 - 1, // unset sentinal value

		StatusLamp: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SponsorProfile) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *SponsorProfile) IsSetStatusLamp() bool {
	return int64(p.StatusLamp) != math.MinInt32-1
}

func (p *SponsorProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.MAP {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.LIST {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.MAP {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.MAP {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I64 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *SponsorProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *SponsorProfile) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *SponsorProfile) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *SponsorProfile) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.SiteName = v
	}
	return nil
}

func (p *SponsorProfile) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.SiteUrl = v
	}
	return nil
}

func (p *SponsorProfile) readField22(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.TradeCategory = make(map[int32]string, size)
	for i := 0; i < size; i++ {
		var _key13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key13 = v
		}
		var _val14 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val14 = v
		}
		p.TradeCategory[_key13] = _val14
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *SponsorProfile) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Certificate = v
	}
	return nil
}

func (p *SponsorProfile) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.IcpRecord = v
	}
	return nil
}

func (p *SponsorProfile) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.BrandName = v
	}
	return nil
}

func (p *SponsorProfile) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.BrandLogo = v
	}
	return nil
}

func (p *SponsorProfile) readField27(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Qualifications = make([]*Qualification, 0, size)
	for i := 0; i < size; i++ {
		_elem15 := NewQualification()
		if err := _elem15.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem15)
		}
		p.Qualifications = append(p.Qualifications, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SponsorProfile) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Qq = v
	}
	return nil
}

func (p *SponsorProfile) readField29(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key16 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key16 = v
		}
		var _val17 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val17 = v
		}
		p.Attrs[_key16] = _val17
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *SponsorProfile) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.AuditStatus = SponsorAuditStatus(v)
	}
	return nil
}

func (p *SponsorProfile) readField31(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AuditInfo = make(map[int32]*AuditInfo, size)
	for i := 0; i < size; i++ {
		var _key18 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key18 = v
		}
		_val19 := NewAuditInfo()
		if err := _val19.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val19)
		}
		p.AuditInfo[_key18] = _val19
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *SponsorProfile) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *SponsorProfile) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.StatusLamp = DBMDisplayStatusLamp(v)
	}
	return nil
}

func (p *SponsorProfile) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *SponsorProfile) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *SponsorProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsorId: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:email: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyName", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:companyName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CompanyName)); err != nil {
		return fmt.Errorf("%T.companyName (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:companyName: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:note: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("siteName", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:siteName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SiteName)); err != nil {
		return fmt.Errorf("%T.siteName (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:siteName: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("siteUrl", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:siteUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SiteUrl)); err != nil {
		return fmt.Errorf("%T.siteUrl (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:siteUrl: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField22(oprot thrift.TProtocol) (err error) {
	if p.TradeCategory != nil {
		if err := oprot.WriteFieldBegin("tradeCategory", thrift.MAP, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:tradeCategory: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.TradeCategory)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.TradeCategory {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:tradeCategory: %s", p, err)
		}
	}
	return err
}

func (p *SponsorProfile) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("certificate", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:certificate: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Certificate)); err != nil {
		return fmt.Errorf("%T.certificate (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:certificate: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icpRecord", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:icpRecord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IcpRecord)); err != nil {
		return fmt.Errorf("%T.icpRecord (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:icpRecord: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brandName", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:brandName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BrandName)); err != nil {
		return fmt.Errorf("%T.brandName (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:brandName: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brandLogo", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:brandLogo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BrandLogo)); err != nil {
		return fmt.Errorf("%T.brandLogo (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:brandLogo: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField27(oprot thrift.TProtocol) (err error) {
	if p.Qualifications != nil {
		if err := oprot.WriteFieldBegin("qualifications", thrift.LIST, 27); err != nil {
			return fmt.Errorf("%T write field begin error 27:qualifications: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Qualifications)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Qualifications {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 27:qualifications: %s", p, err)
		}
	}
	return err
}

func (p *SponsorProfile) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("qq", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:qq: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Qq)); err != nil {
		return fmt.Errorf("%T.qq (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:qq: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField29(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 29); err != nil {
			return fmt.Errorf("%T write field begin error 29:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 29:attrs: %s", p, err)
		}
	}
	return err
}

func (p *SponsorProfile) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *SponsorProfile) writeField31(oprot thrift.TProtocol) (err error) {
	if p.AuditInfo != nil {
		if err := oprot.WriteFieldBegin("auditInfo", thrift.MAP, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:auditInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.AuditInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AuditInfo {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:auditInfo: %s", p, err)
		}
	}
	return err
}

func (p *SponsorProfile) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:status: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField33(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusLamp() {
		if err := oprot.WriteFieldBegin("statusLamp", thrift.I32, 33); err != nil {
			return fmt.Errorf("%T write field begin error 33:statusLamp: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusLamp)); err != nil {
			return fmt.Errorf("%T.statusLamp (33) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 33:statusLamp: %s", p, err)
		}
	}
	return err
}

func (p *SponsorProfile) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:createTime: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:lastupdate: %s", p, err)
	}
	return err
}

func (p *SponsorProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorProfile(%+v)", *p)
}

type AdOrder struct {
	Id        int32 `thrift:"id,1" json:"id"`
	SponsorId int32 `thrift:"sponsorId,2" json:"sponsorId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name                  string `thrift:"name,10" json:"name"`
	TotalBudget           int64  `thrift:"totalBudget,11" json:"totalBudget"`
	TotalBudgetOverTime   int64  `thrift:"totalBudgetOverTime,12" json:"totalBudgetOverTime"`
	CollectCampaignBudget int32  `thrift:"collectCampaignBudget,13" json:"collectCampaignBudget"`
	Note                  string `thrift:"note,14" json:"note"`
	DisplayTotalBudget    int64  `thrift:"displayTotalBudget,15" json:"displayTotalBudget"`
	ProjectId             int32  `thrift:"projectId,16" json:"projectId"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Paused     int32                `thrift:"paused,20" json:"paused"`
	Status     int32                `thrift:"status,21" json:"status"`
	StatusLamp DBMDisplayStatusLamp `thrift:"statusLamp,22" json:"statusLamp"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,31" json:"lastupdate"`
}

func NewAdOrder() *AdOrder {
	return &AdOrder{
		StatusLamp: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdOrder) IsSetStatusLamp() bool {
	return int64(p.StatusLamp) != math.MinInt32-1
}

func (p *AdOrder) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdOrder) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdOrder) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AdOrder) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdOrder) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *AdOrder) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TotalBudgetOverTime = v
	}
	return nil
}

func (p *AdOrder) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.CollectCampaignBudget = v
	}
	return nil
}

func (p *AdOrder) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AdOrder) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DisplayTotalBudget = v
	}
	return nil
}

func (p *AdOrder) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ProjectId = v
	}
	return nil
}

func (p *AdOrder) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *AdOrder) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdOrder) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.StatusLamp = DBMDisplayStatusLamp(v)
	}
	return nil
}

func (p *AdOrder) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdOrder) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AdOrder) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdOrder"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdOrder) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsorId: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:totalBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudgetOverTime", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:totalBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.totalBudgetOverTime (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:totalBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("collectCampaignBudget", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:collectCampaignBudget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CollectCampaignBudget)); err != nil {
		return fmt.Errorf("%T.collectCampaignBudget (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:collectCampaignBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:note: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayTotalBudget", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:displayTotalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DisplayTotalBudget)); err != nil {
		return fmt.Errorf("%T.displayTotalBudget (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:displayTotalBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("projectId", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:projectId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProjectId)); err != nil {
		return fmt.Errorf("%T.projectId (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:projectId: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:paused: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:status: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusLamp() {
		if err := oprot.WriteFieldBegin("statusLamp", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:statusLamp: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusLamp)); err != nil {
			return fmt.Errorf("%T.statusLamp (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:statusLamp: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastupdate: %s", p, err)
	}
	return err
}

func (p *AdOrder) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdOrder(%+v)", *p)
}

type AdCampaign struct {
	Id            int32 `thrift:"id,1" json:"id"`
	RtbCampaignId int32 `thrift:"rtbCampaignId,2" json:"rtbCampaignId"`
	SponsorId     int32 `thrift:"sponsorId,3" json:"sponsorId"`
	OrderId       int32 `thrift:"orderId,4" json:"orderId"`
	DspCampaignId int32 `thrift:"dspCampaignId,5" json:"dspCampaignId"`
	PromotionId   int32 `thrift:"promotionId,6" json:"promotionId"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name                string         `thrift:"name,10" json:"name"`
	CostType            enums.CostType `thrift:"costType,11" json:"costType"`
	TotalBudget         int64          `thrift:"totalBudget,12" json:"totalBudget"`
	TotalBudgetOverTime int64          `thrift:"totalBudgetOverTime,13" json:"totalBudgetOverTime"`
	DailyBudget         int64          `thrift:"dailyBudget,14" json:"dailyBudget"`
	DailyBudgetOverTime int64          `thrift:"dailyBudgetOverTime,15" json:"dailyBudgetOverTime"`
	StartTime           int64          `thrift:"startTime,16" json:"startTime"`
	EndTime             int64          `thrift:"endTime,17" json:"endTime"`
	MarkupRatio         int32          `thrift:"markupRatio,18" json:"markupRatio"`
	// unused field # 19
	Paused     int32                `thrift:"paused,20" json:"paused"`
	Status     int32                `thrift:"status,21" json:"status"`
	StatusLamp DBMDisplayStatusLamp `thrift:"statusLamp,22" json:"statusLamp"`
	ChnId      int64                `thrift:"chnId,23" json:"chnId"`
	TaskFlag   int32                `thrift:"taskFlag,24" json:"taskFlag"`
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime         int64                          `thrift:"createTime,30" json:"createTime"`
	Lastupdate         int64                          `thrift:"lastupdate,31" json:"lastupdate"`
	PromotionType      rtb_adinfo_types.PromotionType `thrift:"promotionType,32" json:"promotionType"`
	MinProfitRatio     int32                          `thrift:"minProfitRatio,33" json:"minProfitRatio"`
	CustomerAcostLimit int64                          `thrift:"customerAcostLimit,34" json:"customerAcostLimit"`
	TargetCtr          int32                          `thrift:"targetCtr,35" json:"targetCtr"`
	ReserveRatio       int32                          `thrift:"reserveRatio,36" json:"reserveRatio"`
	CreateSource       int32                          `thrift:"createSource,37" json:"createSource"`
}

func NewAdCampaign() *AdCampaign {
	return &AdCampaign{
		CostType: math.MinInt32 - 1, // unset sentinal value

		StatusLamp: math.MinInt32 - 1, // unset sentinal value

		PromotionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCampaign) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *AdCampaign) IsSetStatusLamp() bool {
	return int64(p.StatusLamp) != math.MinInt32-1
}

func (p *AdCampaign) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *AdCampaign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I64 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCampaign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdCampaign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RtbCampaignId = v
	}
	return nil
}

func (p *AdCampaign) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AdCampaign) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *AdCampaign) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *AdCampaign) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *AdCampaign) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdCampaign) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CostType = enums.CostType(v)
	}
	return nil
}

func (p *AdCampaign) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *AdCampaign) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.TotalBudgetOverTime = v
	}
	return nil
}

func (p *AdCampaign) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *AdCampaign) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DailyBudgetOverTime = v
	}
	return nil
}

func (p *AdCampaign) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *AdCampaign) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *AdCampaign) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.MarkupRatio = v
	}
	return nil
}

func (p *AdCampaign) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *AdCampaign) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdCampaign) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.StatusLamp = DBMDisplayStatusLamp(v)
	}
	return nil
}

func (p *AdCampaign) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdCampaign) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.TaskFlag = v
	}
	return nil
}

func (p *AdCampaign) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdCampaign) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AdCampaign) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.PromotionType = rtb_adinfo_types.PromotionType(v)
	}
	return nil
}

func (p *AdCampaign) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.MinProfitRatio = v
	}
	return nil
}

func (p *AdCampaign) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.CustomerAcostLimit = v
	}
	return nil
}

func (p *AdCampaign) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.TargetCtr = v
	}
	return nil
}

func (p *AdCampaign) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.ReserveRatio = v
	}
	return nil
}

func (p *AdCampaign) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.CreateSource = v
	}
	return nil
}

func (p *AdCampaign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCampaign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCampaign) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbCampaignId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rtbCampaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCampaignId)); err != nil {
		return fmt.Errorf("%T.rtbCampaignId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rtbCampaignId: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:orderId: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dspCampaignId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:dspCampaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dspCampaignId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:dspCampaignId: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:promotionId: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCostType() {
		if err := oprot.WriteFieldBegin("costType", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:costType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CostType)); err != nil {
			return fmt.Errorf("%T.costType (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:costType: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaign) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:totalBudget: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudgetOverTime", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:totalBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.totalBudgetOverTime (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:totalBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudget", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:dailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.dailyBudget (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:dailyBudget: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudgetOverTime", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:dailyBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.dailyBudgetOverTime (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:dailyBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:startTime: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:endTime: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("markupRatio", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:markupRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MarkupRatio)); err != nil {
		return fmt.Errorf("%T.markupRatio (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:markupRatio: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:paused: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:status: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusLamp() {
		if err := oprot.WriteFieldBegin("statusLamp", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:statusLamp: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusLamp)); err != nil {
			return fmt.Errorf("%T.statusLamp (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:statusLamp: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaign) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:chnId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:chnId: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskFlag", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:taskFlag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskFlag)); err != nil {
		return fmt.Errorf("%T.taskFlag (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:taskFlag: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastupdate: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (32) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *AdCampaign) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("minProfitRatio", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:minProfitRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinProfitRatio)); err != nil {
		return fmt.Errorf("%T.minProfitRatio (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:minProfitRatio: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("customerAcostLimit", thrift.I64, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:customerAcostLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CustomerAcostLimit)); err != nil {
		return fmt.Errorf("%T.customerAcostLimit (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:customerAcostLimit: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targetCtr", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:targetCtr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetCtr)); err != nil {
		return fmt.Errorf("%T.targetCtr (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:targetCtr: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reserveRatio", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:reserveRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReserveRatio)); err != nil {
		return fmt.Errorf("%T.reserveRatio (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:reserveRatio: %s", p, err)
	}
	return err
}

func (p *AdCampaign) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createSource", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:createSource: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateSource)); err != nil {
		return fmt.Errorf("%T.createSource (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:createSource: %s", p, err)
	}
	return err
}

func (p *AdCampaign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCampaign(%+v)", *p)
}

type AdTrackingInfo struct {
	Id            int32 `thrift:"id,1" json:"id"`
	RtbTrackingId int32 `thrift:"rtbTrackingId,2" json:"rtbTrackingId"`
	PromotionId   int32 `thrift:"promotionId,3" json:"promotionId"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name               string `thrift:"name,10" json:"name"`
	ClickThroughUrl    string `thrift:"clickThroughUrl,11" json:"clickThroughUrl"`
	ImpTrackingUrl     string `thrift:"impTrackingUrl,12" json:"impTrackingUrl"`
	RawUrl             string `thrift:"rawUrl,13" json:"rawUrl"`
	ThirdPartyTracking int32  `thrift:"thirdPartyTracking,14" json:"thirdPartyTracking"`
	ChnId              int64  `thrift:"chnId,15" json:"chnId"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,31" json:"lastupdate"`
}

func NewAdTrackingInfo() *AdTrackingInfo {
	return &AdTrackingInfo{}
}

func (p *AdTrackingInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdTrackingInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdTrackingInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RtbTrackingId = v
	}
	return nil
}

func (p *AdTrackingInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *AdTrackingInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdTrackingInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ClickThroughUrl = v
	}
	return nil
}

func (p *AdTrackingInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ImpTrackingUrl = v
	}
	return nil
}

func (p *AdTrackingInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RawUrl = v
	}
	return nil
}

func (p *AdTrackingInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ThirdPartyTracking = v
	}
	return nil
}

func (p *AdTrackingInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdTrackingInfo) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdTrackingInfo) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AdTrackingInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdTrackingInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdTrackingInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbTrackingId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rtbTrackingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbTrackingId)); err != nil {
		return fmt.Errorf("%T.rtbTrackingId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rtbTrackingId: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:promotionId: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickThroughUrl", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:clickThroughUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickThroughUrl)); err != nil {
		return fmt.Errorf("%T.clickThroughUrl (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:clickThroughUrl: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impTrackingUrl", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:impTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpTrackingUrl)); err != nil {
		return fmt.Errorf("%T.impTrackingUrl (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:impTrackingUrl: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rawUrl", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:rawUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RawUrl)); err != nil {
		return fmt.Errorf("%T.rawUrl (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:rawUrl: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thirdPartyTracking", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:thirdPartyTracking: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ThirdPartyTracking)); err != nil {
		return fmt.Errorf("%T.thirdPartyTracking (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:thirdPartyTracking: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:chnId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:chnId: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastupdate: %s", p, err)
	}
	return err
}

func (p *AdTrackingInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdTrackingInfo(%+v)", *p)
}

type AdStrategy struct {
	Id            int32 `thrift:"id,1" json:"id"`
	RtbStrategyId int32 `thrift:"rtbStrategyId,2" json:"rtbStrategyId"`
	SponsorId     int32 `thrift:"sponsorId,3" json:"sponsorId"`
	OrderId       int32 `thrift:"orderId,4" json:"orderId"`
	CampaignId    int32 `thrift:"campaignId,5" json:"campaignId"`
	PromotionId   int32 `thrift:"promotionId,6" json:"promotionId"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name                string `thrift:"name,10" json:"name"`
	DailyBudget         int64  `thrift:"dailyBudget,11" json:"dailyBudget"`
	DailyBudgetOverTime int64  `thrift:"dailyBudgetOverTime,12" json:"dailyBudgetOverTime"`
	Price               int64  `thrift:"price,13" json:"price"`
	DealId              string `thrift:"dealId,14" json:"dealId"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	PlacementType       DBMPlacementType `thrift:"placementType,20" json:"placementType"`
	HourTarget          []int32          `thrift:"hourTarget,21" json:"hourTarget"`
	WeekdayTarget       []int32          `thrift:"weekdayTarget,22" json:"weekdayTarget"`
	GeoCityTarget       []int64          `thrift:"geoCityTarget,23" json:"geoCityTarget"`
	PlatformTarget      []int32          `thrift:"platformTarget,24" json:"platformTarget"`
	AccessTarget        []int32          `thrift:"accessTarget,25" json:"accessTarget"`
	CarrierTarget       []int32          `thrift:"carrierTarget,26" json:"carrierTarget"`
	MediaCategoryTarget []int32          `thrift:"mediaCategoryTarget,27" json:"mediaCategoryTarget"`
	MinOsversionTarget  map[int32]int32  `thrift:"minOsversionTarget,28" json:"minOsversionTarget"`
	MaxOsversionTarget  map[int32]int32  `thrift:"maxOsversionTarget,29" json:"maxOsversionTarget"`
	ExchangeTarget      []int32          `thrift:"exchangeTarget,30" json:"exchangeTarget"`
	DeviceTarget        []int32          `thrift:"deviceTarget,31" json:"deviceTarget"`
	UserTagTarget       []int32          `thrift:"userTagTarget,32" json:"userTagTarget"`
	InternalAcostLimit  int64            `thrift:"internalAcostLimit,33" json:"internalAcostLimit"`
	ChannelTarget       []int32          `thrift:"channelTarget,34" json:"channelTarget"`
	AcostEvenDays       int32            `thrift:"acostEvenDays,35" json:"acostEvenDays"`
	WeatherTarget       []int32          `thrift:"weatherTarget,36" json:"weatherTarget"`
	RtbTrackingId       int32            `thrift:"rtbTrackingId,37" json:"rtbTrackingId"`
	UserTagListTarget   [][]int32        `thrift:"userTagListTarget,38" json:"userTagListTarget"`
	// unused field # 39
	Paused     int32                `thrift:"paused,40" json:"paused"`
	Status     int32                `thrift:"status,41" json:"status"`
	StatusLamp DBMDisplayStatusLamp `thrift:"statusLamp,42" json:"statusLamp"`
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	CreateTime             int64             `thrift:"createTime,50" json:"createTime"`
	Lastupdate             int64             `thrift:"lastupdate,51" json:"lastupdate"`
	MinProfitRatio         int32             `thrift:"minProfitRatio,52" json:"minProfitRatio"`
	RiskBudget             int64             `thrift:"riskBudget,53" json:"riskBudget"`
	AcostEvenStartTime     int64             `thrift:"acostEvenStartTime,54" json:"acostEvenStartTime"`
	DailyDetectBudget      int64             `thrift:"dailyDetectBudget,55" json:"dailyDetectBudget"`
	DetectMode             int32             `thrift:"detectMode,56" json:"detectMode"`
	ConvergenceMode        int32             `thrift:"convergenceMode,57" json:"convergenceMode"`
	MediaGroupWhiteTarget  []int32           `thrift:"mediaGroupWhiteTarget,58" json:"mediaGroupWhiteTarget"`
	MediaGroupBlackTarget  []int32           `thrift:"mediaGroupBlackTarget,59" json:"mediaGroupBlackTarget"`
	InstalledUserFilter    int32             `thrift:"installedUserFilter,60" json:"installedUserFilter"`
	DeviceGroupWhiteTarget []int32           `thrift:"deviceGroupWhiteTarget,61" json:"deviceGroupWhiteTarget"`
	DeviceGroupBlackTarget []int32           `thrift:"deviceGroupBlackTarget,62" json:"deviceGroupBlackTarget"`
	CreateSource           int32             `thrift:"createSource,63" json:"createSource"`
	Attrs                  map[string]string `thrift:"attrs,64" json:"attrs"`
}

func NewAdStrategy() *AdStrategy {
	return &AdStrategy{
		PlacementType: math.MinInt32 - 1, // unset sentinal value

		StatusLamp: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdStrategy) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *AdStrategy) IsSetStatusLamp() bool {
	return int64(p.StatusLamp) != math.MinInt32-1
}

func (p *AdStrategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.LIST {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.LIST {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.LIST {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.LIST {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.LIST {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.LIST {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.MAP {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.MAP {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.LIST {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.LIST {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.LIST {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.LIST {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.LIST {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.LIST {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.I32 {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.I32 {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.LIST {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.LIST {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I32 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.LIST {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.LIST {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.I32 {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.MAP {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStrategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdStrategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RtbStrategyId = v
	}
	return nil
}

func (p *AdStrategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AdStrategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *AdStrategy) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *AdStrategy) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *AdStrategy) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdStrategy) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *AdStrategy) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.DailyBudgetOverTime = v
	}
	return nil
}

func (p *AdStrategy) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *AdStrategy) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DealId = v
	}
	return nil
}

func (p *AdStrategy) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.PlacementType = DBMPlacementType(v)
	}
	return nil
}

func (p *AdStrategy) readField21(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.HourTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem20 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem20 = v
		}
		p.HourTarget = append(p.HourTarget, _elem20)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField22(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WeekdayTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem21 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem21 = v
		}
		p.WeekdayTarget = append(p.WeekdayTarget, _elem21)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField23(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoCityTarget = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem22 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem22 = v
		}
		p.GeoCityTarget = append(p.GeoCityTarget, _elem22)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField24(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlatformTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem23 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem23 = v
		}
		p.PlatformTarget = append(p.PlatformTarget, _elem23)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField25(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccessTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem24 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem24 = v
		}
		p.AccessTarget = append(p.AccessTarget, _elem24)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField26(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CarrierTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem25 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem25 = v
		}
		p.CarrierTarget = append(p.CarrierTarget, _elem25)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField27(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaCategoryTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem26 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem26 = v
		}
		p.MediaCategoryTarget = append(p.MediaCategoryTarget, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField28(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.MinOsversionTarget = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key27 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key27 = v
		}
		var _val28 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val28 = v
		}
		p.MinOsversionTarget[_key27] = _val28
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField29(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.MaxOsversionTarget = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key29 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key29 = v
		}
		var _val30 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val30 = v
		}
		p.MaxOsversionTarget[_key29] = _val30
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField30(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExchangeTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem31 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem31 = v
		}
		p.ExchangeTarget = append(p.ExchangeTarget, _elem31)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField31(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem32 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem32 = v
		}
		p.DeviceTarget = append(p.DeviceTarget, _elem32)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField32(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.UserTagTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem33 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem33 = v
		}
		p.UserTagTarget = append(p.UserTagTarget, _elem33)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.InternalAcostLimit = v
	}
	return nil
}

func (p *AdStrategy) readField34(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChannelTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem34 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem34 = v
		}
		p.ChannelTarget = append(p.ChannelTarget, _elem34)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.AcostEvenDays = v
	}
	return nil
}

func (p *AdStrategy) readField36(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WeatherTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem35 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem35 = v
		}
		p.WeatherTarget = append(p.WeatherTarget, _elem35)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.RtbTrackingId = v
	}
	return nil
}

func (p *AdStrategy) readField38(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.UserTagListTarget = make([][]int32, 0, size)
	for i := 0; i < size; i++ {
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_elem36 := make([]int32, 0, size)
		for i := 0; i < size; i++ {
			var _elem37 int32
			if v, err := iprot.ReadI32(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem37 = v
			}
			_elem36 = append(_elem36, _elem37)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.UserTagListTarget = append(p.UserTagListTarget, _elem36)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *AdStrategy) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdStrategy) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.StatusLamp = DBMDisplayStatusLamp(v)
	}
	return nil
}

func (p *AdStrategy) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdStrategy) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AdStrategy) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.MinProfitRatio = v
	}
	return nil
}

func (p *AdStrategy) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.RiskBudget = v
	}
	return nil
}

func (p *AdStrategy) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.AcostEvenStartTime = v
	}
	return nil
}

func (p *AdStrategy) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.DailyDetectBudget = v
	}
	return nil
}

func (p *AdStrategy) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.DetectMode = v
	}
	return nil
}

func (p *AdStrategy) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.ConvergenceMode = v
	}
	return nil
}

func (p *AdStrategy) readField58(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaGroupWhiteTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem38 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem38 = v
		}
		p.MediaGroupWhiteTarget = append(p.MediaGroupWhiteTarget, _elem38)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField59(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaGroupBlackTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem39 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem39 = v
		}
		p.MediaGroupBlackTarget = append(p.MediaGroupBlackTarget, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.InstalledUserFilter = v
	}
	return nil
}

func (p *AdStrategy) readField61(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceGroupWhiteTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem40 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem40 = v
		}
		p.DeviceGroupWhiteTarget = append(p.DeviceGroupWhiteTarget, _elem40)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField62(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceGroupBlackTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem41 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem41 = v
		}
		p.DeviceGroupBlackTarget = append(p.DeviceGroupBlackTarget, _elem41)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.CreateSource = v
	}
	return nil
}

func (p *AdStrategy) readField64(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key42 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key42 = v
		}
		var _val43 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val43 = v
		}
		p.Attrs[_key42] = _val43
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStrategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbStrategyId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rtbStrategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbStrategyId)); err != nil {
		return fmt.Errorf("%T.rtbStrategyId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rtbStrategyId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:orderId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:campaignId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:promotionId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudget", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:dailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.dailyBudget (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:dailyBudget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudgetOverTime", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:dailyBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.dailyBudgetOverTime (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:dailyBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:price: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dealId", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:dealId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DealId)); err != nil {
		return fmt.Errorf("%T.dealId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:dealId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlacementType() {
		if err := oprot.WriteFieldBegin("placementType", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:placementType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
			return fmt.Errorf("%T.placementType (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:placementType: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField21(oprot thrift.TProtocol) (err error) {
	if p.HourTarget != nil {
		if err := oprot.WriteFieldBegin("hourTarget", thrift.LIST, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:hourTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.HourTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.HourTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:hourTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField22(oprot thrift.TProtocol) (err error) {
	if p.WeekdayTarget != nil {
		if err := oprot.WriteFieldBegin("weekdayTarget", thrift.LIST, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:weekdayTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.WeekdayTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WeekdayTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:weekdayTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField23(oprot thrift.TProtocol) (err error) {
	if p.GeoCityTarget != nil {
		if err := oprot.WriteFieldBegin("geoCityTarget", thrift.LIST, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:geoCityTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.GeoCityTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoCityTarget {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:geoCityTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField24(oprot thrift.TProtocol) (err error) {
	if p.PlatformTarget != nil {
		if err := oprot.WriteFieldBegin("platformTarget", thrift.LIST, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:platformTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlatformTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlatformTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:platformTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField25(oprot thrift.TProtocol) (err error) {
	if p.AccessTarget != nil {
		if err := oprot.WriteFieldBegin("accessTarget", thrift.LIST, 25); err != nil {
			return fmt.Errorf("%T write field begin error 25:accessTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AccessTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccessTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 25:accessTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField26(oprot thrift.TProtocol) (err error) {
	if p.CarrierTarget != nil {
		if err := oprot.WriteFieldBegin("carrierTarget", thrift.LIST, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:carrierTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CarrierTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CarrierTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:carrierTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField27(oprot thrift.TProtocol) (err error) {
	if p.MediaCategoryTarget != nil {
		if err := oprot.WriteFieldBegin("mediaCategoryTarget", thrift.LIST, 27); err != nil {
			return fmt.Errorf("%T write field begin error 27:mediaCategoryTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaCategoryTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaCategoryTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 27:mediaCategoryTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField28(oprot thrift.TProtocol) (err error) {
	if p.MinOsversionTarget != nil {
		if err := oprot.WriteFieldBegin("minOsversionTarget", thrift.MAP, 28); err != nil {
			return fmt.Errorf("%T write field begin error 28:minOsversionTarget: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.MinOsversionTarget)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.MinOsversionTarget {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 28:minOsversionTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField29(oprot thrift.TProtocol) (err error) {
	if p.MaxOsversionTarget != nil {
		if err := oprot.WriteFieldBegin("maxOsversionTarget", thrift.MAP, 29); err != nil {
			return fmt.Errorf("%T write field begin error 29:maxOsversionTarget: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.MaxOsversionTarget)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.MaxOsversionTarget {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 29:maxOsversionTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField30(oprot thrift.TProtocol) (err error) {
	if p.ExchangeTarget != nil {
		if err := oprot.WriteFieldBegin("exchangeTarget", thrift.LIST, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:exchangeTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExchangeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExchangeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:exchangeTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField31(oprot thrift.TProtocol) (err error) {
	if p.DeviceTarget != nil {
		if err := oprot.WriteFieldBegin("deviceTarget", thrift.LIST, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:deviceTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:deviceTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField32(oprot thrift.TProtocol) (err error) {
	if p.UserTagTarget != nil {
		if err := oprot.WriteFieldBegin("userTagTarget", thrift.LIST, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:userTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.UserTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.UserTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:userTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("internalAcostLimit", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:internalAcostLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.InternalAcostLimit)); err != nil {
		return fmt.Errorf("%T.internalAcostLimit (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:internalAcostLimit: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField34(oprot thrift.TProtocol) (err error) {
	if p.ChannelTarget != nil {
		if err := oprot.WriteFieldBegin("channelTarget", thrift.LIST, 34); err != nil {
			return fmt.Errorf("%T write field begin error 34:channelTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ChannelTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChannelTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 34:channelTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("acostEvenDays", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:acostEvenDays: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AcostEvenDays)); err != nil {
		return fmt.Errorf("%T.acostEvenDays (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:acostEvenDays: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField36(oprot thrift.TProtocol) (err error) {
	if p.WeatherTarget != nil {
		if err := oprot.WriteFieldBegin("weatherTarget", thrift.LIST, 36); err != nil {
			return fmt.Errorf("%T write field begin error 36:weatherTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.WeatherTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WeatherTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 36:weatherTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbTrackingId", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:rtbTrackingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbTrackingId)); err != nil {
		return fmt.Errorf("%T.rtbTrackingId (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:rtbTrackingId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField38(oprot thrift.TProtocol) (err error) {
	if p.UserTagListTarget != nil {
		if err := oprot.WriteFieldBegin("userTagListTarget", thrift.LIST, 38); err != nil {
			return fmt.Errorf("%T write field begin error 38:userTagListTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.LIST, len(p.UserTagListTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.UserTagListTarget {
			if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteI32(int32(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 38:userTagListTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:paused: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:status: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField42(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusLamp() {
		if err := oprot.WriteFieldBegin("statusLamp", thrift.I32, 42); err != nil {
			return fmt.Errorf("%T write field begin error 42:statusLamp: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusLamp)); err != nil {
			return fmt.Errorf("%T.statusLamp (42) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 42:statusLamp: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:createTime: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:lastupdate: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("minProfitRatio", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:minProfitRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinProfitRatio)); err != nil {
		return fmt.Errorf("%T.minProfitRatio (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:minProfitRatio: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("riskBudget", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:riskBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RiskBudget)); err != nil {
		return fmt.Errorf("%T.riskBudget (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:riskBudget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("acostEvenStartTime", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:acostEvenStartTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AcostEvenStartTime)); err != nil {
		return fmt.Errorf("%T.acostEvenStartTime (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:acostEvenStartTime: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyDetectBudget", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:dailyDetectBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyDetectBudget)); err != nil {
		return fmt.Errorf("%T.dailyDetectBudget (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:dailyDetectBudget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detectMode", thrift.I32, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:detectMode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DetectMode)); err != nil {
		return fmt.Errorf("%T.detectMode (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:detectMode: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("convergenceMode", thrift.I32, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:convergenceMode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ConvergenceMode)); err != nil {
		return fmt.Errorf("%T.convergenceMode (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:convergenceMode: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField58(oprot thrift.TProtocol) (err error) {
	if p.MediaGroupWhiteTarget != nil {
		if err := oprot.WriteFieldBegin("mediaGroupWhiteTarget", thrift.LIST, 58); err != nil {
			return fmt.Errorf("%T write field begin error 58:mediaGroupWhiteTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaGroupWhiteTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaGroupWhiteTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 58:mediaGroupWhiteTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField59(oprot thrift.TProtocol) (err error) {
	if p.MediaGroupBlackTarget != nil {
		if err := oprot.WriteFieldBegin("mediaGroupBlackTarget", thrift.LIST, 59); err != nil {
			return fmt.Errorf("%T write field begin error 59:mediaGroupBlackTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaGroupBlackTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaGroupBlackTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 59:mediaGroupBlackTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("installedUserFilter", thrift.I32, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:installedUserFilter: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.InstalledUserFilter)); err != nil {
		return fmt.Errorf("%T.installedUserFilter (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:installedUserFilter: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField61(oprot thrift.TProtocol) (err error) {
	if p.DeviceGroupWhiteTarget != nil {
		if err := oprot.WriteFieldBegin("deviceGroupWhiteTarget", thrift.LIST, 61); err != nil {
			return fmt.Errorf("%T write field begin error 61:deviceGroupWhiteTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceGroupWhiteTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceGroupWhiteTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 61:deviceGroupWhiteTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField62(oprot thrift.TProtocol) (err error) {
	if p.DeviceGroupBlackTarget != nil {
		if err := oprot.WriteFieldBegin("deviceGroupBlackTarget", thrift.LIST, 62); err != nil {
			return fmt.Errorf("%T write field begin error 62:deviceGroupBlackTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceGroupBlackTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceGroupBlackTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 62:deviceGroupBlackTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createSource", thrift.I32, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:createSource: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateSource)); err != nil {
		return fmt.Errorf("%T.createSource (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:createSource: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField64(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 64); err != nil {
			return fmt.Errorf("%T write field begin error 64:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 64:attrs: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStrategy(%+v)", *p)
}

type AdPromotion struct {
	Id             int32 `thrift:"id,1" json:"id"`
	RtbPromotionId int32 `thrift:"rtbPromotionId,2" json:"rtbPromotionId"`
	SponsorId      int32 `thrift:"sponsorId,3" json:"sponsorId"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name          string                         `thrift:"name,10" json:"name"`
	PromotionType rtb_adinfo_types.PromotionType `thrift:"promotionType,11" json:"promotionType"`
	AdTrackings   []*AdTrackingInfo              `thrift:"adTrackings,12" json:"adTrackings"`
	PackageName   string                         `thrift:"packageName,13" json:"packageName"`
	AppId         int64                          `thrift:"appId,14" json:"appId"`
	ChnId         int64                          `thrift:"chnId,15" json:"chnId"`
	Attrs         map[string]string              `thrift:"attrs,16" json:"attrs"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Status int32 `thrift:"status,20" json:"status"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,31" json:"lastupdate"`
}

func NewAdPromotion() *AdPromotion {
	return &AdPromotion{
		PromotionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdPromotion) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *AdPromotion) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.MAP {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPromotion) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdPromotion) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RtbPromotionId = v
	}
	return nil
}

func (p *AdPromotion) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AdPromotion) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdPromotion) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PromotionType = rtb_adinfo_types.PromotionType(v)
	}
	return nil
}

func (p *AdPromotion) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdTrackings = make([]*AdTrackingInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem44 := NewAdTrackingInfo()
		if err := _elem44.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem44)
		}
		p.AdTrackings = append(p.AdTrackings, _elem44)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdPromotion) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AdPromotion) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AdPromotion) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdPromotion) readField16(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key45 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key45 = v
		}
		var _val46 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val46 = v
		}
		p.Attrs[_key45] = _val46
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdPromotion) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdPromotion) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdPromotion) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AdPromotion) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPromotion"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPromotion) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbPromotionId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rtbPromotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbPromotionId)); err != nil {
		return fmt.Errorf("%T.rtbPromotionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rtbPromotionId: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *AdPromotion) writeField12(oprot thrift.TProtocol) (err error) {
	if p.AdTrackings != nil {
		if err := oprot.WriteFieldBegin("adTrackings", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:adTrackings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdTrackings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdTrackings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:adTrackings: %s", p, err)
		}
	}
	return err
}

func (p *AdPromotion) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:packageName: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:appId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:appId: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:chnId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:chnId: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:attrs: %s", p, err)
		}
	}
	return err
}

func (p *AdPromotion) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:status: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *AdPromotion) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastupdate: %s", p, err)
	}
	return err
}

func (p *AdPromotion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPromotion(%+v)", *p)
}

type Image struct {
	Url    string `thrift:"url,1" json:"url"`
	Width  int32  `thrift:"width,2" json:"width"`
	Height int32  `thrift:"height,3" json:"height"`
}

func NewImage() *Image {
	return &Image{}
}

func (p *Image) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Image) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Image) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *Image) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *Image) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Image"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Image) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:url: %s", p, err)
	}
	return err
}

func (p *Image) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:width: %s", p, err)
	}
	return err
}

func (p *Image) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:height: %s", p, err)
	}
	return err
}

func (p *Image) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Image(%+v)", *p)
}

type AdCreative struct {
	Id            int32 `thrift:"id,1" json:"id"`
	RtbCreativeId int32 `thrift:"rtbCreativeId,2" json:"rtbCreativeId"`
	SponsorId     int32 `thrift:"sponsorId,3" json:"sponsorId"`
	OrderId       int32 `thrift:"orderId,4" json:"orderId"`
	CampaignId    int32 `thrift:"campaignId,5" json:"campaignId"`
	StrategyId    int32 `thrift:"strategyId,6" json:"strategyId"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name        string `thrift:"name,10" json:"name"`
	Size        int32  `thrift:"size,11" json:"size"`
	Url         string `thrift:"url,12" json:"url"`
	Title       string `thrift:"title,13" json:"title"`
	InfoSource  string `thrift:"infoSource,14" json:"infoSource"`
	DescText    string `thrift:"descText,15" json:"descText"`
	ImgUrl      string `thrift:"imgUrl,16" json:"imgUrl"`
	AdMatchType int64  `thrift:"adMatchType,17" json:"adMatchType"`
	TagText     string `thrift:"tagText,18" json:"tagText"`
	// unused field # 19
	PromotionId   int32           `thrift:"promotionId,20" json:"promotionId"`
	PromotionInfo *AdPromotion    `thrift:"promotionInfo,21" json:"promotionInfo"`
	AdTrackingId  int32           `thrift:"adTrackingId,22" json:"adTrackingId"`
	TrackingInfo  *AdTrackingInfo `thrift:"trackingInfo,23" json:"trackingInfo"`
	Username      string          `thrift:"username,24" json:"username"`
	LogoUrl       string          `thrift:"logoUrl,25" json:"logoUrl"`
	PopupText     string          `thrift:"popupText,26" json:"popupText"`
	BtnText       string          `thrift:"btnText,27" json:"btnText"`
	BtnUrl        string          `thrift:"btnUrl,28" json:"btnUrl"`
	// unused field # 29
	Paused        int32                `thrift:"paused,30" json:"paused"`
	Status        int32                `thrift:"status,31" json:"status"`
	AuditStatus   CreativeAuditStatus  `thrift:"auditStatus,32" json:"auditStatus"`
	AuditInfo     map[int32]*AuditInfo `thrift:"auditInfo,33" json:"auditInfo"`
	StatusLamp    DBMDisplayStatusLamp `thrift:"statusLamp,34" json:"statusLamp"`
	PmpMediaId    string               `thrift:"pmpMediaId,35" json:"pmpMediaId"`
	VideoDuration int32                `thrift:"videoDuration,36" json:"videoDuration"`
	Bitrate       int32                `thrift:"bitrate,37" json:"bitrate"`
	Images        []*Image             `thrift:"images,38" json:"images"`
	RtbTrackingId int32                `thrift:"rtbTrackingId,39" json:"rtbTrackingId"`
	CreateTime    int64                `thrift:"createTime,40" json:"createTime"`
	Lastupdate    int64                `thrift:"lastupdate,41" json:"lastupdate"`
	CreateSource  int32                `thrift:"createSource,42" json:"createSource"`
	Attrs         map[string]string    `thrift:"attrs,43" json:"attrs"`
}

func NewAdCreative() *AdCreative {
	return &AdCreative{
		AuditStatus: math.MinInt32 - 1, // unset sentinal value

		StatusLamp: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCreative) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *AdCreative) IsSetStatusLamp() bool {
	return int64(p.StatusLamp) != math.MinInt32-1
}

func (p *AdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.MAP {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.LIST {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I64 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.MAP {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RtbCreativeId = v
	}
	return nil
}

func (p *AdCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AdCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *AdCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *AdCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *AdCreative) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdCreative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *AdCreative) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *AdCreative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *AdCreative) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.InfoSource = v
	}
	return nil
}

func (p *AdCreative) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DescText = v
	}
	return nil
}

func (p *AdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *AdCreative) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *AdCreative) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.TagText = v
	}
	return nil
}

func (p *AdCreative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *AdCreative) readField21(iprot thrift.TProtocol) error {
	p.PromotionInfo = NewAdPromotion()
	if err := p.PromotionInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PromotionInfo)
	}
	return nil
}

func (p *AdCreative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AdTrackingId = v
	}
	return nil
}

func (p *AdCreative) readField23(iprot thrift.TProtocol) error {
	p.TrackingInfo = NewAdTrackingInfo()
	if err := p.TrackingInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TrackingInfo)
	}
	return nil
}

func (p *AdCreative) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *AdCreative) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.LogoUrl = v
	}
	return nil
}

func (p *AdCreative) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.PopupText = v
	}
	return nil
}

func (p *AdCreative) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.BtnText = v
	}
	return nil
}

func (p *AdCreative) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.BtnUrl = v
	}
	return nil
}

func (p *AdCreative) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *AdCreative) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdCreative) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.AuditStatus = CreativeAuditStatus(v)
	}
	return nil
}

func (p *AdCreative) readField33(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AuditInfo = make(map[int32]*AuditInfo, size)
	for i := 0; i < size; i++ {
		var _key47 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key47 = v
		}
		_val48 := NewAuditInfo()
		if err := _val48.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val48)
		}
		p.AuditInfo[_key47] = _val48
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdCreative) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.StatusLamp = DBMDisplayStatusLamp(v)
	}
	return nil
}

func (p *AdCreative) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.PmpMediaId = v
	}
	return nil
}

func (p *AdCreative) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.VideoDuration = v
	}
	return nil
}

func (p *AdCreative) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Bitrate = v
	}
	return nil
}

func (p *AdCreative) readField38(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Images = make([]*Image, 0, size)
	for i := 0; i < size; i++ {
		_elem49 := NewImage()
		if err := _elem49.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem49)
		}
		p.Images = append(p.Images, _elem49)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreative) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.RtbTrackingId = v
	}
	return nil
}

func (p *AdCreative) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdCreative) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *AdCreative) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.CreateSource = v
	}
	return nil
}

func (p *AdCreative) readField43(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key50 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key50 = v
		}
		var _val51 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val51 = v
		}
		p.Attrs[_key50] = _val51
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbCreativeId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rtbCreativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCreativeId)); err != nil {
		return fmt.Errorf("%T.rtbCreativeId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rtbCreativeId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:orderId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:campaignId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:strategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:strategyId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:size: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:url: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:title: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("infoSource", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:infoSource: %s", p, err)
	}
	if err := oprot.WriteString(string(p.InfoSource)); err != nil {
		return fmt.Errorf("%T.infoSource (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:infoSource: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("descText", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:descText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DescText)); err != nil {
		return fmt.Errorf("%T.descText (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:descText: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:imgUrl: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adMatchType", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:adMatchType: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.adMatchType (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:adMatchType: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagText", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:tagText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagText)); err != nil {
		return fmt.Errorf("%T.tagText (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:tagText: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:promotionId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField21(oprot thrift.TProtocol) (err error) {
	if p.PromotionInfo != nil {
		if err := oprot.WriteFieldBegin("promotionInfo", thrift.STRUCT, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:promotionInfo: %s", p, err)
		}
		if err := p.PromotionInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PromotionInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:promotionInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adTrackingId", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:adTrackingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdTrackingId)); err != nil {
		return fmt.Errorf("%T.adTrackingId (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:adTrackingId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField23(oprot thrift.TProtocol) (err error) {
	if p.TrackingInfo != nil {
		if err := oprot.WriteFieldBegin("trackingInfo", thrift.STRUCT, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:trackingInfo: %s", p, err)
		}
		if err := p.TrackingInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TrackingInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:trackingInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:username: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logoUrl", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:logoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LogoUrl)); err != nil {
		return fmt.Errorf("%T.logoUrl (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:logoUrl: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("popupText", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:popupText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PopupText)); err != nil {
		return fmt.Errorf("%T.popupText (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:popupText: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("btnText", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:btnText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BtnText)); err != nil {
		return fmt.Errorf("%T.btnText (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:btnText: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("btnUrl", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:btnUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BtnUrl)); err != nil {
		return fmt.Errorf("%T.btnUrl (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:btnUrl: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:paused: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:status: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (32) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField33(oprot thrift.TProtocol) (err error) {
	if p.AuditInfo != nil {
		if err := oprot.WriteFieldBegin("auditInfo", thrift.MAP, 33); err != nil {
			return fmt.Errorf("%T write field begin error 33:auditInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.AuditInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AuditInfo {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 33:auditInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField34(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusLamp() {
		if err := oprot.WriteFieldBegin("statusLamp", thrift.I32, 34); err != nil {
			return fmt.Errorf("%T write field begin error 34:statusLamp: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusLamp)); err != nil {
			return fmt.Errorf("%T.statusLamp (34) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 34:statusLamp: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmpMediaId", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:pmpMediaId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PmpMediaId)); err != nil {
		return fmt.Errorf("%T.pmpMediaId (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:pmpMediaId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoDuration", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:videoDuration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoDuration)); err != nil {
		return fmt.Errorf("%T.videoDuration (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:videoDuration: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bitrate", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:bitrate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bitrate)); err != nil {
		return fmt.Errorf("%T.bitrate (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:bitrate: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField38(oprot thrift.TProtocol) (err error) {
	if p.Images != nil {
		if err := oprot.WriteFieldBegin("images", thrift.LIST, 38); err != nil {
			return fmt.Errorf("%T write field begin error 38:images: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Images)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Images {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 38:images: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbTrackingId", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:rtbTrackingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbTrackingId)); err != nil {
		return fmt.Errorf("%T.rtbTrackingId (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:rtbTrackingId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:createTime: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:lastupdate: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createSource", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:createSource: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateSource)); err != nil {
		return fmt.Errorf("%T.createSource (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:createSource: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField43(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 43); err != nil {
			return fmt.Errorf("%T write field begin error 43:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 43:attrs: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCreative(%+v)", *p)
}

type SponsorAccount struct {
	SponsorId    int32 `thrift:"sponsorId,1" json:"sponsorId"`
	TotalDeposit int64 `thrift:"totalDeposit,2" json:"totalDeposit"`
	TotalConsume int64 `thrift:"totalConsume,3" json:"totalConsume"`
	Balance      int64 `thrift:"balance,4" json:"balance"`
	AgentUid     int32 `thrift:"agentUid,5" json:"agentUid"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	BalanceStatus int32 `thrift:"balanceStatus,10" json:"balanceStatus"`
}

func NewSponsorAccount() *SponsorAccount {
	return &SponsorAccount{}
}

func (p *SponsorAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *SponsorAccount) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TotalDeposit = v
	}
	return nil
}

func (p *SponsorAccount) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalConsume = v
	}
	return nil
}

func (p *SponsorAccount) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Balance = v
	}
	return nil
}

func (p *SponsorAccount) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorAccount) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.BalanceStatus = v
	}
	return nil
}

func (p *SponsorAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsorId: %s", p, err)
	}
	return err
}

func (p *SponsorAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalDeposit", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:totalDeposit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalDeposit)); err != nil {
		return fmt.Errorf("%T.totalDeposit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:totalDeposit: %s", p, err)
	}
	return err
}

func (p *SponsorAccount) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalConsume", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:totalConsume: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalConsume)); err != nil {
		return fmt.Errorf("%T.totalConsume (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:totalConsume: %s", p, err)
	}
	return err
}

func (p *SponsorAccount) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:balance: %s", p, err)
	}
	return err
}

func (p *SponsorAccount) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorAccount) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balanceStatus", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:balanceStatus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BalanceStatus)); err != nil {
		return fmt.Errorf("%T.balanceStatus (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:balanceStatus: %s", p, err)
	}
	return err
}

func (p *SponsorAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorAccount(%+v)", *p)
}

type AgentAccount struct {
	AgentUid     int32 `thrift:"agentUid,1" json:"agentUid"`
	TotalDeposit int64 `thrift:"totalDeposit,2" json:"totalDeposit"`
	TotalConsume int64 `thrift:"totalConsume,3" json:"totalConsume"`
	Credit       int64 `thrift:"credit,4" json:"credit"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	BalanceStatus int32 `thrift:"balanceStatus,10" json:"balanceStatus"`
}

func NewAgentAccount() *AgentAccount {
	return &AgentAccount{}
}

func (p *AgentAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentAccount) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TotalDeposit = v
	}
	return nil
}

func (p *AgentAccount) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalConsume = v
	}
	return nil
}

func (p *AgentAccount) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Credit = v
	}
	return nil
}

func (p *AgentAccount) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.BalanceStatus = v
	}
	return nil
}

func (p *AgentAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalDeposit", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:totalDeposit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalDeposit)); err != nil {
		return fmt.Errorf("%T.totalDeposit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:totalDeposit: %s", p, err)
	}
	return err
}

func (p *AgentAccount) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalConsume", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:totalConsume: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalConsume)); err != nil {
		return fmt.Errorf("%T.totalConsume (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:totalConsume: %s", p, err)
	}
	return err
}

func (p *AgentAccount) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("credit", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:credit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Credit)); err != nil {
		return fmt.Errorf("%T.credit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:credit: %s", p, err)
	}
	return err
}

func (p *AgentAccount) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balanceStatus", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:balanceStatus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BalanceStatus)); err != nil {
		return fmt.Errorf("%T.balanceStatus (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:balanceStatus: %s", p, err)
	}
	return err
}

func (p *AgentAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentAccount(%+v)", *p)
}
