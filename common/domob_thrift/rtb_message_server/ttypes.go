// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_message_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//MessageServer 异常代码
type RTBMessageServerErrorCode int64

const (
	RTBMessageServerErrorCode_ERROR_MSG_PARAM_INVALID RTBMessageServerErrorCode = 10001
	RTBMessageServerErrorCode_ERROR_MSG_NOT_EXIST     RTBMessageServerErrorCode = 10002
	RTBMessageServerErrorCode_ERROR_MSG_SYSTEM_ERROR  RTBMessageServerErrorCode = 10003
)

func (p RTBMessageServerErrorCode) String() string {
	switch p {
	case RTBMessageServerErrorCode_ERROR_MSG_PARAM_INVALID:
		return "RTBMessageServerErrorCode_ERROR_MSG_PARAM_INVALID"
	case RTBMessageServerErrorCode_ERROR_MSG_NOT_EXIST:
		return "RTBMessageServerErrorCode_ERROR_MSG_NOT_EXIST"
	case RTBMessageServerErrorCode_ERROR_MSG_SYSTEM_ERROR:
		return "RTBMessageServerErrorCode_ERROR_MSG_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func RTBMessageServerErrorCodeFromString(s string) (RTBMessageServerErrorCode, error) {
	switch s {
	case "RTBMessageServerErrorCode_ERROR_MSG_PARAM_INVALID":
		return RTBMessageServerErrorCode_ERROR_MSG_PARAM_INVALID, nil
	case "RTBMessageServerErrorCode_ERROR_MSG_NOT_EXIST":
		return RTBMessageServerErrorCode_ERROR_MSG_NOT_EXIST, nil
	case "RTBMessageServerErrorCode_ERROR_MSG_SYSTEM_ERROR":
		return RTBMessageServerErrorCode_ERROR_MSG_SYSTEM_ERROR, nil
	}
	return RTBMessageServerErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid RTBMessageServerErrorCode string")
}

//消息中心-消息类别
type RTBMessageType int64

const (
	RTBMessageType_MSG_UNKNOWN                     RTBMessageType = 0
	RTBMessageType_MSG_CREATIVE_REJECT             RTBMessageType = 1
	RTBMessageType_MSG_YOUKU_STATE                 RTBMessageType = 2
	RTBMessageType_MSG_CREATIVE_WINRATE_REDUCE     RTBMessageType = 3
	RTBMessageType_MSG_SPONSOR_REJECT              RTBMessageType = 4
	RTBMessageType_MSG_STRATEGY_DECAY              RTBMessageType = 5
	RTBMessageType_MSG_LANDING_MONITOR             RTBMessageType = 6
	RTBMessageType_MSG_INTELLIGENT_CREATIVE_SYSTEM RTBMessageType = 7
)

func (p RTBMessageType) String() string {
	switch p {
	case RTBMessageType_MSG_UNKNOWN:
		return "RTBMessageType_MSG_UNKNOWN"
	case RTBMessageType_MSG_CREATIVE_REJECT:
		return "RTBMessageType_MSG_CREATIVE_REJECT"
	case RTBMessageType_MSG_YOUKU_STATE:
		return "RTBMessageType_MSG_YOUKU_STATE"
	case RTBMessageType_MSG_CREATIVE_WINRATE_REDUCE:
		return "RTBMessageType_MSG_CREATIVE_WINRATE_REDUCE"
	case RTBMessageType_MSG_SPONSOR_REJECT:
		return "RTBMessageType_MSG_SPONSOR_REJECT"
	case RTBMessageType_MSG_STRATEGY_DECAY:
		return "RTBMessageType_MSG_STRATEGY_DECAY"
	case RTBMessageType_MSG_LANDING_MONITOR:
		return "RTBMessageType_MSG_LANDING_MONITOR"
	case RTBMessageType_MSG_INTELLIGENT_CREATIVE_SYSTEM:
		return "RTBMessageType_MSG_INTELLIGENT_CREATIVE_SYSTEM"
	}
	return "<UNSET>"
}

func RTBMessageTypeFromString(s string) (RTBMessageType, error) {
	switch s {
	case "RTBMessageType_MSG_UNKNOWN":
		return RTBMessageType_MSG_UNKNOWN, nil
	case "RTBMessageType_MSG_CREATIVE_REJECT":
		return RTBMessageType_MSG_CREATIVE_REJECT, nil
	case "RTBMessageType_MSG_YOUKU_STATE":
		return RTBMessageType_MSG_YOUKU_STATE, nil
	case "RTBMessageType_MSG_CREATIVE_WINRATE_REDUCE":
		return RTBMessageType_MSG_CREATIVE_WINRATE_REDUCE, nil
	case "RTBMessageType_MSG_SPONSOR_REJECT":
		return RTBMessageType_MSG_SPONSOR_REJECT, nil
	case "RTBMessageType_MSG_STRATEGY_DECAY":
		return RTBMessageType_MSG_STRATEGY_DECAY, nil
	case "RTBMessageType_MSG_LANDING_MONITOR":
		return RTBMessageType_MSG_LANDING_MONITOR, nil
	case "RTBMessageType_MSG_INTELLIGENT_CREATIVE_SYSTEM":
		return RTBMessageType_MSG_INTELLIGENT_CREATIVE_SYSTEM, nil
	}
	return RTBMessageType(math.MinInt32 - 1), fmt.Errorf("not a valid RTBMessageType string")
}

type RTBMessage struct {
	Id           int32          `thrift:"id,1" json:"id"`
	Uid          int32          `thrift:"uid,2" json:"uid"`
	Title        string         `thrift:"title,3" json:"title"`
	Content      string         `thrift:"content,4" json:"content"`
	TypeA1       RTBMessageType `thrift:"type,5" json:"type"`
	ReadingState int32          `thrift:"reading_state,6" json:"reading_state"`
	Status       int32          `thrift:"status,7" json:"status"`
	CreateTime   int64          `thrift:"create_time,8" json:"create_time"`
	LastUpdate   int64          `thrift:"last_update,9" json:"last_update"`
}

func NewRTBMessage() *RTBMessage {
	return &RTBMessage{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBMessage) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *RTBMessage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBMessage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *RTBMessage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *RTBMessage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *RTBMessage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *RTBMessage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = RTBMessageType(v)
	}
	return nil
}

func (p *RTBMessage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ReadingState = v
	}
	return nil
}

func (p *RTBMessage) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *RTBMessage) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *RTBMessage) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *RTBMessage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBMessage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBMessage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:title: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:content: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *RTBMessage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reading_state", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:reading_state: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReadingState)); err != nil {
		return fmt.Errorf("%T.reading_state (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:reading_state: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:status: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:create_time: %s", p, err)
	}
	return err
}

func (p *RTBMessage) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_update", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:last_update: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.last_update (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:last_update: %s", p, err)
	}
	return err
}

func (p *RTBMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBMessage(%+v)", *p)
}

type RTBMessageData struct {
	TotalCount int32             `thrift:"total_count,1" json:"total_count"`
	MaxLimit   int32             `thrift:"max_limit,2" json:"max_limit"`
	Offset     int32             `thrift:"offset,3" json:"offset"`
	Limit      int32             `thrift:"limit,4" json:"limit"`
	Result     []*RTBMessage     `thrift:"result,5" json:"result"`
	ExtInfo    map[string]string `thrift:"ext_info,6" json:"ext_info"`
}

func NewRTBMessageData() *RTBMessageData {
	return &RTBMessageData{}
}

func (p *RTBMessageData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBMessageData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *RTBMessageData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *RTBMessageData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *RTBMessageData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *RTBMessageData) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*RTBMessage, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewRTBMessage()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Result = append(p.Result, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RTBMessageData) readField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.ExtInfo[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *RTBMessageData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBMessageData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBMessageData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total_count: %s", p, err)
	}
	return err
}

func (p *RTBMessageData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:max_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.max_limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:max_limit: %s", p, err)
	}
	return err
}

func (p *RTBMessageData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *RTBMessageData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *RTBMessageData) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *RTBMessageData) writeField6(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("ext_info", thrift.MAP, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:ext_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:ext_info: %s", p, err)
		}
	}
	return err
}

func (p *RTBMessageData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBMessageData(%+v)", *p)
}

type RTBMessageServerException struct {
	Code    RTBMessageServerErrorCode `thrift:"code,1" json:"code"`
	Message string                    `thrift:"message,2" json:"message"`
}

func NewRTBMessageServerException() *RTBMessageServerException {
	return &RTBMessageServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBMessageServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *RTBMessageServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBMessageServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = RTBMessageServerErrorCode(v)
	}
	return nil
}

func (p *RTBMessageServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *RTBMessageServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBMessageServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBMessageServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *RTBMessageServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *RTBMessageServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBMessageServerException(%+v)", *p)
}
