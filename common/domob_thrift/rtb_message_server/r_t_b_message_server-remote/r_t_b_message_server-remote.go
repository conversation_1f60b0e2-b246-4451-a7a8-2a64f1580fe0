// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"rtb_message_server"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  RTBMessageData getMessages(RequestHeader header, i32 uid,  ids, i32 offset, i32 limit, RTBMessageType type, i32 reading_state, bool ascending, bool exclude_deleted)")
	fmt.Fprintln(os.<PERSON>, "  RTBMessageData getMessagesByUids(RequestHeader header,  uids, i32 offset, i32 limit, RTBMessageType type, i32 reading_state, bool ascending, bool exclude_deleted)")
	fmt.Fprintln(os.Stderr, "  void updateReadingState(RequestHeader header, i32 uid,  ids, i32 reading_state)")
	fmt.Fprintln(os.Stderr, "  void deleteMessagesByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addMessage(RequestHeader header, RTBMessage rtb_message)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := rtb_message_server.NewRTBMessageServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getMessages":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "GetMessages requires 9 args")
			flag.Usage()
		}
		arg29 := flag.Arg(1)
		mbTrans30 := thrift.NewTMemoryBufferLen(len(arg29))
		defer mbTrans30.Close()
		_, err31 := mbTrans30.WriteString(arg29)
		if err31 != nil {
			Usage()
			return
		}
		factory32 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt33 := factory32.GetProtocol(mbTrans30)
		argvalue0 := rtb_message_server.NewRequestHeader()
		err34 := argvalue0.Read(jsProt33)
		if err34 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err35 := (strconv.Atoi(flag.Arg(2)))
		if err35 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg36 := flag.Arg(3)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		containerStruct2 := rtb_message_server.NewGetMessagesArgs()
		err41 := containerStruct2.ReadField3(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		tmp3, err42 := (strconv.Atoi(flag.Arg(4)))
		if err42 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err43 := (strconv.Atoi(flag.Arg(5)))
		if err43 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := rtb_message_server.RTBMessageType(tmp5)
		value5 := argvalue5
		tmp6, err44 := (strconv.Atoi(flag.Arg(7)))
		if err44 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		argvalue7 := flag.Arg(8) == "true"
		value7 := argvalue7
		argvalue8 := flag.Arg(9) == "true"
		value8 := argvalue8
		fmt.Print(client.GetMessages(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "getMessagesByUids":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "GetMessagesByUids requires 8 args")
			flag.Usage()
		}
		arg47 := flag.Arg(1)
		mbTrans48 := thrift.NewTMemoryBufferLen(len(arg47))
		defer mbTrans48.Close()
		_, err49 := mbTrans48.WriteString(arg47)
		if err49 != nil {
			Usage()
			return
		}
		factory50 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt51 := factory50.GetProtocol(mbTrans48)
		argvalue0 := rtb_message_server.NewRequestHeader()
		err52 := argvalue0.Read(jsProt51)
		if err52 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg53 := flag.Arg(2)
		mbTrans54 := thrift.NewTMemoryBufferLen(len(arg53))
		defer mbTrans54.Close()
		_, err55 := mbTrans54.WriteString(arg53)
		if err55 != nil {
			Usage()
			return
		}
		factory56 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt57 := factory56.GetProtocol(mbTrans54)
		containerStruct1 := rtb_message_server.NewGetMessagesByUidsArgs()
		err58 := containerStruct1.ReadField2(jsProt57)
		if err58 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		tmp2, err59 := (strconv.Atoi(flag.Arg(3)))
		if err59 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err60 := (strconv.Atoi(flag.Arg(4)))
		if err60 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := rtb_message_server.RTBMessageType(tmp4)
		value4 := argvalue4
		tmp5, err61 := (strconv.Atoi(flag.Arg(6)))
		if err61 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		argvalue7 := flag.Arg(8) == "true"
		value7 := argvalue7
		fmt.Print(client.GetMessagesByUids(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "updateReadingState":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateReadingState requires 4 args")
			flag.Usage()
		}
		arg64 := flag.Arg(1)
		mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
		defer mbTrans65.Close()
		_, err66 := mbTrans65.WriteString(arg64)
		if err66 != nil {
			Usage()
			return
		}
		factory67 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt68 := factory67.GetProtocol(mbTrans65)
		argvalue0 := rtb_message_server.NewRequestHeader()
		err69 := argvalue0.Read(jsProt68)
		if err69 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err70 := (strconv.Atoi(flag.Arg(2)))
		if err70 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg71 := flag.Arg(3)
		mbTrans72 := thrift.NewTMemoryBufferLen(len(arg71))
		defer mbTrans72.Close()
		_, err73 := mbTrans72.WriteString(arg71)
		if err73 != nil {
			Usage()
			return
		}
		factory74 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt75 := factory74.GetProtocol(mbTrans72)
		containerStruct2 := rtb_message_server.NewUpdateReadingStateArgs()
		err76 := containerStruct2.ReadField3(jsProt75)
		if err76 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		tmp3, err77 := (strconv.Atoi(flag.Arg(4)))
		if err77 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.UpdateReadingState(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteMessagesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteMessagesByIds requires 3 args")
			flag.Usage()
		}
		arg78 := flag.Arg(1)
		mbTrans79 := thrift.NewTMemoryBufferLen(len(arg78))
		defer mbTrans79.Close()
		_, err80 := mbTrans79.WriteString(arg78)
		if err80 != nil {
			Usage()
			return
		}
		factory81 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt82 := factory81.GetProtocol(mbTrans79)
		argvalue0 := rtb_message_server.NewRequestHeader()
		err83 := argvalue0.Read(jsProt82)
		if err83 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err84 := (strconv.Atoi(flag.Arg(2)))
		if err84 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg85 := flag.Arg(3)
		mbTrans86 := thrift.NewTMemoryBufferLen(len(arg85))
		defer mbTrans86.Close()
		_, err87 := mbTrans86.WriteString(arg85)
		if err87 != nil {
			Usage()
			return
		}
		factory88 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt89 := factory88.GetProtocol(mbTrans86)
		containerStruct2 := rtb_message_server.NewDeleteMessagesByIdsArgs()
		err90 := containerStruct2.ReadField3(jsProt89)
		if err90 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteMessagesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addMessage":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMessage requires 2 args")
			flag.Usage()
		}
		arg91 := flag.Arg(1)
		mbTrans92 := thrift.NewTMemoryBufferLen(len(arg91))
		defer mbTrans92.Close()
		_, err93 := mbTrans92.WriteString(arg91)
		if err93 != nil {
			Usage()
			return
		}
		factory94 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt95 := factory94.GetProtocol(mbTrans92)
		argvalue0 := rtb_message_server.NewRequestHeader()
		err96 := argvalue0.Read(jsProt95)
		if err96 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg97 := flag.Arg(2)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue1 := rtb_message_server.NewRTBMessage()
		err102 := argvalue1.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddMessage(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
