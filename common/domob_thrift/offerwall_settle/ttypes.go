// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_settle

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/offerwall_info_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = offerwall_info_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

//结算状态
type OwSettleStatus int64

const (
	OwSettleStatus_SETTLE_SUCCESS         OwSettleStatus = 0
	OwSettleStatus_REDUPLICATE_VORID      OwSettleStatus = 1
	OwSettleStatus_REQUEST_ERROR          OwSettleStatus = 2
	OwSettleStatus_SERVER_ERROR           OwSettleStatus = 3
	OwSettleStatus_BUDGET_OVER            OwSettleStatus = 4
	OwSettleStatus_DAILY_BUDGET_OVER      OwSettleStatus = 5
	OwSettleStatus_USER_BUDGET_OVER       OwSettleStatus = 6
	OwSettleStatus_USER_DAILY_BUDGET_OVER OwSettleStatus = 7
)

func (p OwSettleStatus) String() string {
	switch p {
	case OwSettleStatus_SETTLE_SUCCESS:
		return "OwSettleStatus_SETTLE_SUCCESS"
	case OwSettleStatus_REDUPLICATE_VORID:
		return "OwSettleStatus_REDUPLICATE_VORID"
	case OwSettleStatus_REQUEST_ERROR:
		return "OwSettleStatus_REQUEST_ERROR"
	case OwSettleStatus_SERVER_ERROR:
		return "OwSettleStatus_SERVER_ERROR"
	case OwSettleStatus_BUDGET_OVER:
		return "OwSettleStatus_BUDGET_OVER"
	case OwSettleStatus_DAILY_BUDGET_OVER:
		return "OwSettleStatus_DAILY_BUDGET_OVER"
	case OwSettleStatus_USER_BUDGET_OVER:
		return "OwSettleStatus_USER_BUDGET_OVER"
	case OwSettleStatus_USER_DAILY_BUDGET_OVER:
		return "OwSettleStatus_USER_DAILY_BUDGET_OVER"
	}
	return "<UNSET>"
}

func OwSettleStatusFromString(s string) (OwSettleStatus, error) {
	switch s {
	case "OwSettleStatus_SETTLE_SUCCESS":
		return OwSettleStatus_SETTLE_SUCCESS, nil
	case "OwSettleStatus_REDUPLICATE_VORID":
		return OwSettleStatus_REDUPLICATE_VORID, nil
	case "OwSettleStatus_REQUEST_ERROR":
		return OwSettleStatus_REQUEST_ERROR, nil
	case "OwSettleStatus_SERVER_ERROR":
		return OwSettleStatus_SERVER_ERROR, nil
	case "OwSettleStatus_BUDGET_OVER":
		return OwSettleStatus_BUDGET_OVER, nil
	case "OwSettleStatus_DAILY_BUDGET_OVER":
		return OwSettleStatus_DAILY_BUDGET_OVER, nil
	case "OwSettleStatus_USER_BUDGET_OVER":
		return OwSettleStatus_USER_BUDGET_OVER, nil
	case "OwSettleStatus_USER_DAILY_BUDGET_OVER":
		return OwSettleStatus_USER_DAILY_BUDGET_OVER, nil
	}
	return OwSettleStatus(math.MinInt32 - 1), fmt.Errorf("not a valid OwSettleStatus string")
}

type Platform offerwall_info_types.Platform

type OwRequestHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
}

func NewOwRequestHeader() *OwRequestHeader {
	return &OwRequestHeader{}
}

func (p *OwRequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwRequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwRequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwRequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwRequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwRequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwRequestHeader(%+v)", *p)
}

type OwDeviceInfo struct {
	Mac       string `thrift:"mac,1" json:"mac"`
	Macmd5    string `thrift:"macmd5,2" json:"macmd5"`
	Idfa      string `thrift:"idfa,3" json:"idfa"`
	Oid       string `thrift:"oid,4" json:"oid"`
	Udid      string `thrift:"udid,5" json:"udid"`
	Imei      string `thrift:"imei,6" json:"imei"`
	AndroidId string `thrift:"androidId,7" json:"androidId"`
	Odin1     string `thrift:"odin1,8" json:"odin1"`
}

func NewOwDeviceInfo() *OwDeviceInfo {
	return &OwDeviceInfo{}
}

func (p *OwDeviceInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwDeviceInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *OwDeviceInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *OwDeviceInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *OwDeviceInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwDeviceInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Udid = v
	}
	return nil
}

func (p *OwDeviceInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *OwDeviceInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *OwDeviceInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwDeviceInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwDeviceInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwDeviceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mac: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:macmd5: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:idfa: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:oid: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("udid", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:udid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Udid)); err != nil {
		return fmt.Errorf("%T.udid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:udid: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:imei: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("androidId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:androidId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.androidId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:androidId: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:odin1: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwDeviceInfo(%+v)", *p)
}

type OwSettleRequest struct {
	Vorid      string        `thrift:"vorid,1" json:"vorid"`
	Platform   Platform      `thrift:"platform,2" json:"platform"`
	DeviceInfo *OwDeviceInfo `thrift:"deviceInfo,3" json:"deviceInfo"`
	UserId     string        `thrift:"userId,4" json:"userId"`
	Mediaid    int32         `thrift:"mediaid,5" json:"mediaid"`
	Deverid    int32         `thrift:"deverid,6" json:"deverid"`
	Sponsorid  int32         `thrift:"sponsorid,7" json:"sponsorid"`
	Planid     int32         `thrift:"planid,8" json:"planid"`
	Cid        int32         `thrift:"cid,9" json:"cid"`
	Appid      string        `thrift:"appid,10" json:"appid"`
	Point      int64         `thrift:"point,11" json:"point"`
	Price      int64         `thrift:"price,12" json:"price"`
	MediaShare int64         `thrift:"mediaShare,13" json:"mediaShare"`
	OfferType  int16         `thrift:"offerType,14" json:"offerType"`
	CostType   int16         `thrift:"costType,15" json:"costType"`
	Action     int16         `thrift:"action,16" json:"action"`
	Unlimit    bool          `thrift:"unlimit,17" json:"unlimit"`
}

func NewOwSettleRequest() *OwSettleRequest {
	return &OwSettleRequest{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwSettleRequest) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *OwSettleRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I16 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I16 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I16 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwSettleRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Vorid = v
	}
	return nil
}

func (p *OwSettleRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *OwSettleRequest) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *OwSettleRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *OwSettleRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mediaid = v
	}
	return nil
}

func (p *OwSettleRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Deverid = v
	}
	return nil
}

func (p *OwSettleRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Sponsorid = v
	}
	return nil
}

func (p *OwSettleRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *OwSettleRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *OwSettleRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *OwSettleRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *OwSettleRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *OwSettleRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.MediaShare = v
	}
	return nil
}

func (p *OwSettleRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwSettleRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwSettleRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *OwSettleRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Unlimit = v
	}
	return nil
}

func (p *OwSettleRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwSettleRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwSettleRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vorid", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:vorid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vorid)); err != nil {
		return fmt.Errorf("%T.vorid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:vorid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *OwSettleRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:userId: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mediaid)); err != nil {
		return fmt.Errorf("%T.mediaid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mediaid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deverid)); err != nil {
		return fmt.Errorf("%T.deverid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:deverid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:sponsorid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sponsorid)); err != nil {
		return fmt.Errorf("%T.sponsorid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:sponsorid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:planid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:cid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:appid: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:point: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:price: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaShare", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:mediaShare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaShare)); err != nil {
		return fmt.Errorf("%T.mediaShare (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:mediaShare: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offerType", thrift.I16, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:offerType: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offerType (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:offerType: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I16, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:costType: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:costType: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I16, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:action: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Action)); err != nil {
		return fmt.Errorf("%T.action (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:action: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unlimit", thrift.BOOL, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:unlimit: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Unlimit)); err != nil {
		return fmt.Errorf("%T.unlimit (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:unlimit: %s", p, err)
	}
	return err
}

func (p *OwSettleRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwSettleRequest(%+v)", *p)
}

type OwSettleResponse struct {
	SearchId     int64          `thrift:"searchId,1" json:"searchId"`
	SettleStatus OwSettleStatus `thrift:"settleStatus,2" json:"settleStatus"`
}

func NewOwSettleResponse() *OwSettleResponse {
	return &OwSettleResponse{
		SettleStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwSettleResponse) IsSetSettleStatus() bool {
	return int64(p.SettleStatus) != math.MinInt32-1
}

func (p *OwSettleResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwSettleResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwSettleResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SettleStatus = OwSettleStatus(v)
	}
	return nil
}

func (p *OwSettleResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwSettleResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwSettleResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwSettleResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSettleStatus() {
		if err := oprot.WriteFieldBegin("settleStatus", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:settleStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SettleStatus)); err != nil {
			return fmt.Errorf("%T.settleStatus (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:settleStatus: %s", p, err)
		}
	}
	return err
}

func (p *OwSettleResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwSettleResponse(%+v)", *p)
}
