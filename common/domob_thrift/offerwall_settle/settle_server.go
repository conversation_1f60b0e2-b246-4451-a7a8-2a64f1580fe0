// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_settle

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/offerwall_info_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = offerwall_info_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type SettleServer interface { //Settle Server服务

	// @Description("video finish settle")
	//
	// Parameters:
	//  - RequestHeader: request header
	//  - OwSettleRequest: 结算的请求结构体
	VideoFinishSettle(requestHeader *OwRequestHeader, owSettleRequest *OwSettleRequest) (r *OwSettleResponse, err error)
}

//Settle Server服务
type SettleServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewSettleServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *SettleServerClient {
	return &SettleServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewSettleServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *SettleServerClient {
	return &SettleServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// @Description("video finish settle")
//
// Parameters:
//  - RequestHeader: request header
//  - OwSettleRequest: 结算的请求结构体
func (p *SettleServerClient) VideoFinishSettle(requestHeader *OwRequestHeader, owSettleRequest *OwSettleRequest) (r *OwSettleResponse, err error) {
	if err = p.sendVideoFinishSettle(requestHeader, owSettleRequest); err != nil {
		return
	}
	return p.recvVideoFinishSettle()
}

func (p *SettleServerClient) sendVideoFinishSettle(requestHeader *OwRequestHeader, owSettleRequest *OwSettleRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("videoFinishSettle", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewVideoFinishSettleArgs()
	args0.RequestHeader = requestHeader
	args0.OwSettleRequest = owSettleRequest
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *SettleServerClient) recvVideoFinishSettle() (value *OwSettleResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewVideoFinishSettleResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

type SettleServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      SettleServer
}

func (p *SettleServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *SettleServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *SettleServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewSettleServerProcessor(handler SettleServer) *SettleServerProcessor {

	self4 := &SettleServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self4.processorMap["videoFinishSettle"] = &settleServerProcessorVideoFinishSettle{handler: handler}
	return self4
}

func (p *SettleServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x5.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x5

}

type settleServerProcessorVideoFinishSettle struct {
	handler SettleServer
}

func (p *settleServerProcessorVideoFinishSettle) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewVideoFinishSettleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("videoFinishSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewVideoFinishSettleResult()
	if result.Success, err = p.handler.VideoFinishSettle(args.RequestHeader, args.OwSettleRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing videoFinishSettle: "+err.Error())
		oprot.WriteMessageBegin("videoFinishSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("videoFinishSettle", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type VideoFinishSettleArgs struct {
	RequestHeader   *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	OwSettleRequest *OwSettleRequest `thrift:"owSettleRequest,2" json:"owSettleRequest"`
}

func NewVideoFinishSettleArgs() *VideoFinishSettleArgs {
	return &VideoFinishSettleArgs{}
}

func (p *VideoFinishSettleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoFinishSettleArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *VideoFinishSettleArgs) readField2(iprot thrift.TProtocol) error {
	p.OwSettleRequest = NewOwSettleRequest()
	if err := p.OwSettleRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.OwSettleRequest)
	}
	return nil
}

func (p *VideoFinishSettleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("videoFinishSettle_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoFinishSettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *VideoFinishSettleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OwSettleRequest != nil {
		if err := oprot.WriteFieldBegin("owSettleRequest", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:owSettleRequest: %s", p, err)
		}
		if err := p.OwSettleRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.OwSettleRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:owSettleRequest: %s", p, err)
		}
	}
	return err
}

func (p *VideoFinishSettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoFinishSettleArgs(%+v)", *p)
}

type VideoFinishSettleResult struct {
	Success *OwSettleResponse `thrift:"success,0" json:"success"`
}

func NewVideoFinishSettleResult() *VideoFinishSettleResult {
	return &VideoFinishSettleResult{}
}

func (p *VideoFinishSettleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoFinishSettleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwSettleResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *VideoFinishSettleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("videoFinishSettle_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoFinishSettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *VideoFinishSettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoFinishSettleResult(%+v)", *p)
}
