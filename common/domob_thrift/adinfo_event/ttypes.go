// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adinfo_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_types"
	"rtb_model_server/common/domob_thrift/finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = adinfo_types.GoUnusedProtection__
var _ = finance_types.GoUnusedProtection__
var GoUnusedProtection__ int

type UidInt adinfo_types.UidInt

type AdPlanIdInt adinfo_types.AdPlanIdInt

type AdStrategyIdInt adinfo_types.AdStrategyIdInt

type AdCreativeIdInt adinfo_types.AdCreativeIdInt

type AdPlan *adinfo_types.AdPlan

type AdStrategy *adinfo_types.AdStrategy

type AdCreative *adinfo_types.AdCreative

type AdPlanBudgetStatus *finance_types.AdPlanBudgetStatus

type AdStrategyBudgetStatus *finance_types.AdStrategyBudgetStatus

type AdPlanUpdateEvent struct {
	Uid                UidInt                            `thrift:"uid,1" json:"uid"`
	Pid                AdPlanIdInt                       `thrift:"pid,2" json:"pid"`
	AdPlan             *adinfo_types.AdPlan              `thrift:"adPlan,3" json:"adPlan"`
	AdPlanBudgetStatus *finance_types.AdPlanBudgetStatus `thrift:"adPlanBudgetStatus,4" json:"adPlanBudgetStatus"`
}

func NewAdPlanUpdateEvent() *AdPlanUpdateEvent {
	return &AdPlanUpdateEvent{}
}

func (p *AdPlanUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlanUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdPlanUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdPlanUpdateEvent) readField3(iprot thrift.TProtocol) error {
	p.AdPlan = adinfo_types.NewAdPlan()
	if err := p.AdPlan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdPlan)
	}
	return nil
}

func (p *AdPlanUpdateEvent) readField4(iprot thrift.TProtocol) error {
	p.AdPlanBudgetStatus = finance_types.NewAdPlanBudgetStatus()
	if err := p.AdPlanBudgetStatus.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdPlanBudgetStatus)
	}
	return nil
}

func (p *AdPlanUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlanUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlanUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdPlanUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AdPlanUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AdPlan != nil {
		if err := oprot.WriteFieldBegin("adPlan", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:adPlan: %s", p, err)
		}
		if err := p.AdPlan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdPlan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:adPlan: %s", p, err)
		}
	}
	return err
}

func (p *AdPlanUpdateEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.AdPlanBudgetStatus != nil {
		if err := oprot.WriteFieldBegin("adPlanBudgetStatus", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:adPlanBudgetStatus: %s", p, err)
		}
		if err := p.AdPlanBudgetStatus.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdPlanBudgetStatus)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:adPlanBudgetStatus: %s", p, err)
		}
	}
	return err
}

func (p *AdPlanUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlanUpdateEvent(%+v)", *p)
}

type AdStrategyUpdateEvent struct {
	Uid                    UidInt                                `thrift:"uid,1" json:"uid"`
	Pid                    AdPlanIdInt                           `thrift:"pid,2" json:"pid"`
	Sid                    AdStrategyIdInt                       `thrift:"sid,3" json:"sid"`
	AdStrategy             *adinfo_types.AdStrategy              `thrift:"adStrategy,4" json:"adStrategy"`
	AdStrategyBudgetStatus *finance_types.AdStrategyBudgetStatus `thrift:"adStrategyBudgetStatus,5" json:"adStrategyBudgetStatus"`
}

func NewAdStrategyUpdateEvent() *AdStrategyUpdateEvent {
	return &AdStrategyUpdateEvent{}
}

func (p *AdStrategyUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sid = AdStrategyIdInt(v)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) readField4(iprot thrift.TProtocol) error {
	p.AdStrategy = adinfo_types.NewAdStrategy()
	if err := p.AdStrategy.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdStrategy)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) readField5(iprot thrift.TProtocol) error {
	p.AdStrategyBudgetStatus = finance_types.NewAdStrategyBudgetStatus()
	if err := p.AdStrategyBudgetStatus.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdStrategyBudgetStatus)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStrategyUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStrategyUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdStrategyUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AdStrategyUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sid: %s", p, err)
	}
	return err
}

func (p *AdStrategyUpdateEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.AdStrategy != nil {
		if err := oprot.WriteFieldBegin("adStrategy", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:adStrategy: %s", p, err)
		}
		if err := p.AdStrategy.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdStrategy)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:adStrategy: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyUpdateEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if p.AdStrategyBudgetStatus != nil {
		if err := oprot.WriteFieldBegin("adStrategyBudgetStatus", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:adStrategyBudgetStatus: %s", p, err)
		}
		if err := p.AdStrategyBudgetStatus.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdStrategyBudgetStatus)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:adStrategyBudgetStatus: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategyUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStrategyUpdateEvent(%+v)", *p)
}

type AdCreativeUpdateEvent struct {
	Uid        UidInt                   `thrift:"uid,1" json:"uid"`
	Pid        AdPlanIdInt              `thrift:"pid,2" json:"pid"`
	Sid        AdStrategyIdInt          `thrift:"sid,3" json:"sid"`
	Cid        AdCreativeIdInt          `thrift:"cid,4" json:"cid"`
	AdCreative *adinfo_types.AdCreative `thrift:"adCreative,5" json:"adCreative"`
}

func NewAdCreativeUpdateEvent() *AdCreativeUpdateEvent {
	return &AdCreativeUpdateEvent{}
}

func (p *AdCreativeUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sid = AdStrategyIdInt(v)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = AdCreativeIdInt(v)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) readField5(iprot thrift.TProtocol) error {
	p.AdCreative = adinfo_types.NewAdCreative()
	if err := p.AdCreative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdCreative)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCreativeUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdCreativeUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AdCreativeUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sid: %s", p, err)
	}
	return err
}

func (p *AdCreativeUpdateEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *AdCreativeUpdateEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if p.AdCreative != nil {
		if err := oprot.WriteFieldBegin("adCreative", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:adCreative: %s", p, err)
		}
		if err := p.AdCreative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdCreative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:adCreative: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCreativeUpdateEvent(%+v)", *p)
}
