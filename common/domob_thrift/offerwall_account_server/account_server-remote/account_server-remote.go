// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"offerwall_account_server"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>r, "  OwAccountCaptchaResp generateCaptcha(OwAccountReqHeader reqHeader, string phone, OwAccountCaptchaType captchaType)")
	fmt.Fprintln(os.<PERSON>, "  OwAccountRegisterResp accountRegister(OwAccountReqHeader reqHeader, OwAccountRegisterReq registerReq)")
	fmt.Fprintln(os.Stderr, "  OwAccountLoginResp accountLogin(OwAccountReqHeader reqHeader, string phone, string passwd)")
	fmt.Fprintln(os.Stderr, "  OwAccountResetPasswdResp resetPasswd(OwAccountReqHeader reqHeader, string phone, string captcha, string passwd)")
	fmt.Fprintln(os.Stderr, "  OwAccountUserProfileResp getUserProfile(OwAccountReqHeader reqHeader, string uid)")
	fmt.Fprintln(os.Stderr, "  OwAccountUserProfileResp pointCheck(OwAccountReqHeader reqHeader, string uid)")
	fmt.Fprintln(os.Stderr, "  OwAccountUserProfileResp increase(OwAccountReqHeader reqHeader, string uid, string orderId, OwAccountIncreaseDto dto)")
	fmt.Fprintln(os.Stderr, "  OwAccountUserProfileResp consume(OwAccountReqHeader reqHeader, string uid, string orderId, i64 mbean, string desc)")
	fmt.Fprintln(os.Stderr, "  OwAccountUserProfileResp consumeRollback(OwAccountReqHeader reqHeader, string uid, string orderId, i64 mbean, string desc)")
	fmt.Fprintln(os.Stderr, "  OwMallGetItemResp getMallItems(OwMallReqHeader reqHeader, string uid)")
	fmt.Fprintln(os.Stderr, "  OwMallExchangeResp exchange(OwMallReqHeader reqHeader, string uid, string userAccount, string itemId, string orderId)")
	fmt.Fprintln(os.Stderr, "  OwMallExchangeRecordResp exchangeRecords(OwMallReqHeader reqHeader, string uid)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := offerwall_account_server.NewAccountServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "generateCaptcha":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GenerateCaptcha requires 3 args")
			flag.Usage()
		}
		arg52 := flag.Arg(1)
		mbTrans53 := thrift.NewTMemoryBufferLen(len(arg52))
		defer mbTrans53.Close()
		_, err54 := mbTrans53.WriteString(arg52)
		if err54 != nil {
			Usage()
			return
		}
		factory55 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt56 := factory55.GetProtocol(mbTrans53)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err57 := argvalue0.Read(jsProt56)
		if err57 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := offerwall_account_server.OwAccountCaptchaType(tmp2)
		value2 := argvalue2
		fmt.Print(client.GenerateCaptcha(value0, value1, value2))
		fmt.Print("\n")
		break
	case "accountRegister":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AccountRegister requires 2 args")
			flag.Usage()
		}
		arg59 := flag.Arg(1)
		mbTrans60 := thrift.NewTMemoryBufferLen(len(arg59))
		defer mbTrans60.Close()
		_, err61 := mbTrans60.WriteString(arg59)
		if err61 != nil {
			Usage()
			return
		}
		factory62 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt63 := factory62.GetProtocol(mbTrans60)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err64 := argvalue0.Read(jsProt63)
		if err64 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg65 := flag.Arg(2)
		mbTrans66 := thrift.NewTMemoryBufferLen(len(arg65))
		defer mbTrans66.Close()
		_, err67 := mbTrans66.WriteString(arg65)
		if err67 != nil {
			Usage()
			return
		}
		factory68 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt69 := factory68.GetProtocol(mbTrans66)
		argvalue1 := offerwall_account_server.NewOwAccountRegisterReq()
		err70 := argvalue1.Read(jsProt69)
		if err70 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AccountRegister(value0, value1))
		fmt.Print("\n")
		break
	case "accountLogin":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AccountLogin requires 3 args")
			flag.Usage()
		}
		arg71 := flag.Arg(1)
		mbTrans72 := thrift.NewTMemoryBufferLen(len(arg71))
		defer mbTrans72.Close()
		_, err73 := mbTrans72.WriteString(arg71)
		if err73 != nil {
			Usage()
			return
		}
		factory74 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt75 := factory74.GetProtocol(mbTrans72)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err76 := argvalue0.Read(jsProt75)
		if err76 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.AccountLogin(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resetPasswd":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResetPasswd requires 4 args")
			flag.Usage()
		}
		arg79 := flag.Arg(1)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err84 := argvalue0.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.ResetPasswd(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getUserProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUserProfile requires 2 args")
			flag.Usage()
		}
		arg88 := flag.Arg(1)
		mbTrans89 := thrift.NewTMemoryBufferLen(len(arg88))
		defer mbTrans89.Close()
		_, err90 := mbTrans89.WriteString(arg88)
		if err90 != nil {
			Usage()
			return
		}
		factory91 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt92 := factory91.GetProtocol(mbTrans89)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err93 := argvalue0.Read(jsProt92)
		if err93 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetUserProfile(value0, value1))
		fmt.Print("\n")
		break
	case "pointCheck":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PointCheck requires 2 args")
			flag.Usage()
		}
		arg95 := flag.Arg(1)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err100 := argvalue0.Read(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.PointCheck(value0, value1))
		fmt.Print("\n")
		break
	case "increase":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "Increase requires 4 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		arg110 := flag.Arg(4)
		mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
		defer mbTrans111.Close()
		_, err112 := mbTrans111.WriteString(arg110)
		if err112 != nil {
			Usage()
			return
		}
		factory113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt114 := factory113.GetProtocol(mbTrans111)
		argvalue3 := offerwall_account_server.NewOwAccountIncreaseDto()
		err115 := argvalue3.Read(jsProt114)
		if err115 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.Increase(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "consume":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "Consume requires 5 args")
			flag.Usage()
		}
		arg116 := flag.Arg(1)
		mbTrans117 := thrift.NewTMemoryBufferLen(len(arg116))
		defer mbTrans117.Close()
		_, err118 := mbTrans117.WriteString(arg116)
		if err118 != nil {
			Usage()
			return
		}
		factory119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt120 := factory119.GetProtocol(mbTrans117)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err121 := argvalue0.Read(jsProt120)
		if err121 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3, err124 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err124 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.Consume(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "consumeRollback":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ConsumeRollback requires 5 args")
			flag.Usage()
		}
		arg126 := flag.Arg(1)
		mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
		defer mbTrans127.Close()
		_, err128 := mbTrans127.WriteString(arg126)
		if err128 != nil {
			Usage()
			return
		}
		factory129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt130 := factory129.GetProtocol(mbTrans127)
		argvalue0 := offerwall_account_server.NewOwAccountReqHeader()
		err131 := argvalue0.Read(jsProt130)
		if err131 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3, err134 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err134 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.ConsumeRollback(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getMallItems":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMallItems requires 2 args")
			flag.Usage()
		}
		arg136 := flag.Arg(1)
		mbTrans137 := thrift.NewTMemoryBufferLen(len(arg136))
		defer mbTrans137.Close()
		_, err138 := mbTrans137.WriteString(arg136)
		if err138 != nil {
			Usage()
			return
		}
		factory139 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt140 := factory139.GetProtocol(mbTrans137)
		argvalue0 := offerwall_account_server.NewOwMallReqHeader()
		err141 := argvalue0.Read(jsProt140)
		if err141 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetMallItems(value0, value1))
		fmt.Print("\n")
		break
	case "exchange":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "Exchange requires 5 args")
			flag.Usage()
		}
		arg143 := flag.Arg(1)
		mbTrans144 := thrift.NewTMemoryBufferLen(len(arg143))
		defer mbTrans144.Close()
		_, err145 := mbTrans144.WriteString(arg143)
		if err145 != nil {
			Usage()
			return
		}
		factory146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt147 := factory146.GetProtocol(mbTrans144)
		argvalue0 := offerwall_account_server.NewOwMallReqHeader()
		err148 := argvalue0.Read(jsProt147)
		if err148 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.Exchange(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "exchangeRecords":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ExchangeRecords requires 2 args")
			flag.Usage()
		}
		arg153 := flag.Arg(1)
		mbTrans154 := thrift.NewTMemoryBufferLen(len(arg153))
		defer mbTrans154.Close()
		_, err155 := mbTrans154.WriteString(arg153)
		if err155 != nil {
			Usage()
			return
		}
		factory156 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt157 := factory156.GetProtocol(mbTrans154)
		argvalue0 := offerwall_account_server.NewOwMallReqHeader()
		err158 := argvalue0.Read(jsProt157)
		if err158 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.ExchangeRecords(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
