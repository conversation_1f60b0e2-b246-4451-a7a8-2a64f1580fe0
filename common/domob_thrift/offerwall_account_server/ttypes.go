// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_account_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//定义一系列的错误码，便于区分异常情况，定位问题
type OwAccountErrorCode int64

const (
	OwAccountErrorCode_ACCOUNT_COMMON_REQ_PARAMS_ERROR      OwAccountErrorCode = 1
	OwAccountErrorCode_ACCOUNT_COMMON_SERVER_ERROR          OwAccountErrorCode = 2
	OwAccountErrorCode_ACCOUNT_COMMON_USER_NOT_FOUND        OwAccountErrorCode = 3
	OwAccountErrorCode_ACCOUNT_COMMON_REDUPLICATE_ORDER     OwAccountErrorCode = 4
	OwAccountErrorCode_ACCOUNT_CAPTCHA_TOO_OFTEN            OwAccountErrorCode = 1001
	OwAccountErrorCode_ACCOUNT_CAPTCHA_PHONE_USED           OwAccountErrorCode = 1002
	OwAccountErrorCode_ACCOUNT_REGISTER_WRONG_CAPTCHA       OwAccountErrorCode = 2001
	OwAccountErrorCode_ACCOUNT_REGISTER_PHONE_USED          OwAccountErrorCode = 2002
	OwAccountErrorCode_ACCOUNT_LOGIN_WRONG_PASSWD           OwAccountErrorCode = 3001
	OwAccountErrorCode_ACCOUNT_RESET_WRONG_CAPTCHA          OwAccountErrorCode = 4001
	OwAccountErrorCode_ACCOUNT_CONSUME_INSUFFICIENT_POINT   OwAccountErrorCode = 5001
	OwAccountErrorCode_ACCOUNT_ROLLBACK_CONSUMED_NOT_ENOUGH OwAccountErrorCode = 6001
)

func (p OwAccountErrorCode) String() string {
	switch p {
	case OwAccountErrorCode_ACCOUNT_COMMON_REQ_PARAMS_ERROR:
		return "OwAccountErrorCode_ACCOUNT_COMMON_REQ_PARAMS_ERROR"
	case OwAccountErrorCode_ACCOUNT_COMMON_SERVER_ERROR:
		return "OwAccountErrorCode_ACCOUNT_COMMON_SERVER_ERROR"
	case OwAccountErrorCode_ACCOUNT_COMMON_USER_NOT_FOUND:
		return "OwAccountErrorCode_ACCOUNT_COMMON_USER_NOT_FOUND"
	case OwAccountErrorCode_ACCOUNT_COMMON_REDUPLICATE_ORDER:
		return "OwAccountErrorCode_ACCOUNT_COMMON_REDUPLICATE_ORDER"
	case OwAccountErrorCode_ACCOUNT_CAPTCHA_TOO_OFTEN:
		return "OwAccountErrorCode_ACCOUNT_CAPTCHA_TOO_OFTEN"
	case OwAccountErrorCode_ACCOUNT_CAPTCHA_PHONE_USED:
		return "OwAccountErrorCode_ACCOUNT_CAPTCHA_PHONE_USED"
	case OwAccountErrorCode_ACCOUNT_REGISTER_WRONG_CAPTCHA:
		return "OwAccountErrorCode_ACCOUNT_REGISTER_WRONG_CAPTCHA"
	case OwAccountErrorCode_ACCOUNT_REGISTER_PHONE_USED:
		return "OwAccountErrorCode_ACCOUNT_REGISTER_PHONE_USED"
	case OwAccountErrorCode_ACCOUNT_LOGIN_WRONG_PASSWD:
		return "OwAccountErrorCode_ACCOUNT_LOGIN_WRONG_PASSWD"
	case OwAccountErrorCode_ACCOUNT_RESET_WRONG_CAPTCHA:
		return "OwAccountErrorCode_ACCOUNT_RESET_WRONG_CAPTCHA"
	case OwAccountErrorCode_ACCOUNT_CONSUME_INSUFFICIENT_POINT:
		return "OwAccountErrorCode_ACCOUNT_CONSUME_INSUFFICIENT_POINT"
	case OwAccountErrorCode_ACCOUNT_ROLLBACK_CONSUMED_NOT_ENOUGH:
		return "OwAccountErrorCode_ACCOUNT_ROLLBACK_CONSUMED_NOT_ENOUGH"
	}
	return "<UNSET>"
}

func OwAccountErrorCodeFromString(s string) (OwAccountErrorCode, error) {
	switch s {
	case "OwAccountErrorCode_ACCOUNT_COMMON_REQ_PARAMS_ERROR":
		return OwAccountErrorCode_ACCOUNT_COMMON_REQ_PARAMS_ERROR, nil
	case "OwAccountErrorCode_ACCOUNT_COMMON_SERVER_ERROR":
		return OwAccountErrorCode_ACCOUNT_COMMON_SERVER_ERROR, nil
	case "OwAccountErrorCode_ACCOUNT_COMMON_USER_NOT_FOUND":
		return OwAccountErrorCode_ACCOUNT_COMMON_USER_NOT_FOUND, nil
	case "OwAccountErrorCode_ACCOUNT_COMMON_REDUPLICATE_ORDER":
		return OwAccountErrorCode_ACCOUNT_COMMON_REDUPLICATE_ORDER, nil
	case "OwAccountErrorCode_ACCOUNT_CAPTCHA_TOO_OFTEN":
		return OwAccountErrorCode_ACCOUNT_CAPTCHA_TOO_OFTEN, nil
	case "OwAccountErrorCode_ACCOUNT_CAPTCHA_PHONE_USED":
		return OwAccountErrorCode_ACCOUNT_CAPTCHA_PHONE_USED, nil
	case "OwAccountErrorCode_ACCOUNT_REGISTER_WRONG_CAPTCHA":
		return OwAccountErrorCode_ACCOUNT_REGISTER_WRONG_CAPTCHA, nil
	case "OwAccountErrorCode_ACCOUNT_REGISTER_PHONE_USED":
		return OwAccountErrorCode_ACCOUNT_REGISTER_PHONE_USED, nil
	case "OwAccountErrorCode_ACCOUNT_LOGIN_WRONG_PASSWD":
		return OwAccountErrorCode_ACCOUNT_LOGIN_WRONG_PASSWD, nil
	case "OwAccountErrorCode_ACCOUNT_RESET_WRONG_CAPTCHA":
		return OwAccountErrorCode_ACCOUNT_RESET_WRONG_CAPTCHA, nil
	case "OwAccountErrorCode_ACCOUNT_CONSUME_INSUFFICIENT_POINT":
		return OwAccountErrorCode_ACCOUNT_CONSUME_INSUFFICIENT_POINT, nil
	case "OwAccountErrorCode_ACCOUNT_ROLLBACK_CONSUMED_NOT_ENOUGH":
		return OwAccountErrorCode_ACCOUNT_ROLLBACK_CONSUMED_NOT_ENOUGH, nil
	}
	return OwAccountErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid OwAccountErrorCode string")
}

//验证码类型
type OwAccountCaptchaType int64

const (
	OwAccountCaptchaType_CAPTCHA_REGISTER     OwAccountCaptchaType = 1
	OwAccountCaptchaType_CAPTCHA_RESET_PASSWD OwAccountCaptchaType = 2
)

func (p OwAccountCaptchaType) String() string {
	switch p {
	case OwAccountCaptchaType_CAPTCHA_REGISTER:
		return "OwAccountCaptchaType_CAPTCHA_REGISTER"
	case OwAccountCaptchaType_CAPTCHA_RESET_PASSWD:
		return "OwAccountCaptchaType_CAPTCHA_RESET_PASSWD"
	}
	return "<UNSET>"
}

func OwAccountCaptchaTypeFromString(s string) (OwAccountCaptchaType, error) {
	switch s {
	case "OwAccountCaptchaType_CAPTCHA_REGISTER":
		return OwAccountCaptchaType_CAPTCHA_REGISTER, nil
	case "OwAccountCaptchaType_CAPTCHA_RESET_PASSWD":
		return OwAccountCaptchaType_CAPTCHA_RESET_PASSWD, nil
	}
	return OwAccountCaptchaType(math.MinInt32 - 1), fmt.Errorf("not a valid OwAccountCaptchaType string")
}

//定义一系列的错误码，便于区分异常情况，定位问题
type OwMallErrorCode int64

const (
	OwMallErrorCode_MALL_COMMON_REQ_PARAMS_ERROR     OwMallErrorCode = 1
	OwMallErrorCode_MALL_COMMON_SERVER_ERROR         OwMallErrorCode = 2
	OwMallErrorCode_MALL_COMMON_USER_NOT_FOUND       OwMallErrorCode = 3
	OwMallErrorCode_MALL_COMMON_REDUPLICATE_ORDER    OwMallErrorCode = 4
	OwMallErrorCode_MALL_EXCHANGE_INSUFFICIENT_POINT OwMallErrorCode = 5001
	OwMallErrorCode_MALL_EXCHANGE_ITEM_NOT_FOUND     OwMallErrorCode = 5002
	OwMallErrorCode_MALL_EXCHANGE_TOO_OFTEN          OwMallErrorCode = 5003
)

func (p OwMallErrorCode) String() string {
	switch p {
	case OwMallErrorCode_MALL_COMMON_REQ_PARAMS_ERROR:
		return "OwMallErrorCode_MALL_COMMON_REQ_PARAMS_ERROR"
	case OwMallErrorCode_MALL_COMMON_SERVER_ERROR:
		return "OwMallErrorCode_MALL_COMMON_SERVER_ERROR"
	case OwMallErrorCode_MALL_COMMON_USER_NOT_FOUND:
		return "OwMallErrorCode_MALL_COMMON_USER_NOT_FOUND"
	case OwMallErrorCode_MALL_COMMON_REDUPLICATE_ORDER:
		return "OwMallErrorCode_MALL_COMMON_REDUPLICATE_ORDER"
	case OwMallErrorCode_MALL_EXCHANGE_INSUFFICIENT_POINT:
		return "OwMallErrorCode_MALL_EXCHANGE_INSUFFICIENT_POINT"
	case OwMallErrorCode_MALL_EXCHANGE_ITEM_NOT_FOUND:
		return "OwMallErrorCode_MALL_EXCHANGE_ITEM_NOT_FOUND"
	case OwMallErrorCode_MALL_EXCHANGE_TOO_OFTEN:
		return "OwMallErrorCode_MALL_EXCHANGE_TOO_OFTEN"
	}
	return "<UNSET>"
}

func OwMallErrorCodeFromString(s string) (OwMallErrorCode, error) {
	switch s {
	case "OwMallErrorCode_MALL_COMMON_REQ_PARAMS_ERROR":
		return OwMallErrorCode_MALL_COMMON_REQ_PARAMS_ERROR, nil
	case "OwMallErrorCode_MALL_COMMON_SERVER_ERROR":
		return OwMallErrorCode_MALL_COMMON_SERVER_ERROR, nil
	case "OwMallErrorCode_MALL_COMMON_USER_NOT_FOUND":
		return OwMallErrorCode_MALL_COMMON_USER_NOT_FOUND, nil
	case "OwMallErrorCode_MALL_COMMON_REDUPLICATE_ORDER":
		return OwMallErrorCode_MALL_COMMON_REDUPLICATE_ORDER, nil
	case "OwMallErrorCode_MALL_EXCHANGE_INSUFFICIENT_POINT":
		return OwMallErrorCode_MALL_EXCHANGE_INSUFFICIENT_POINT, nil
	case "OwMallErrorCode_MALL_EXCHANGE_ITEM_NOT_FOUND":
		return OwMallErrorCode_MALL_EXCHANGE_ITEM_NOT_FOUND, nil
	case "OwMallErrorCode_MALL_EXCHANGE_TOO_OFTEN":
		return OwMallErrorCode_MALL_EXCHANGE_TOO_OFTEN, nil
	}
	return OwMallErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid OwMallErrorCode string")
}

type OwAccountReqHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
}

func NewOwAccountReqHeader() *OwAccountReqHeader {
	return &OwAccountReqHeader{}
}

func (p *OwAccountReqHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountReqHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwAccountReqHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountReqHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountReqHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwAccountReqHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountReqHeader(%+v)", *p)
}

type OwAccountRespHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
	// unused field # 2
	ErrorCode    OwAccountErrorCode `thrift:"errorCode,3" json:"errorCode"`
	ErrorMessage string             `thrift:"errorMessage,4" json:"errorMessage"`
}

func NewOwAccountRespHeader() *OwAccountRespHeader {
	return &OwAccountRespHeader{
		ErrorCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAccountRespHeader) IsSetErrorCode() bool {
	return int64(p.ErrorCode) != math.MinInt32-1
}

func (p *OwAccountRespHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountRespHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwAccountRespHeader) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ErrorCode = OwAccountErrorCode(v)
	}
	return nil
}

func (p *OwAccountRespHeader) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ErrorMessage = v
	}
	return nil
}

func (p *OwAccountRespHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountRespHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountRespHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwAccountRespHeader) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorCode() {
		if err := oprot.WriteFieldBegin("errorCode", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:errorCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ErrorCode)); err != nil {
			return fmt.Errorf("%T.errorCode (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:errorCode: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountRespHeader) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("errorMessage", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:errorMessage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrorMessage)); err != nil {
		return fmt.Errorf("%T.errorMessage (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:errorMessage: %s", p, err)
	}
	return err
}

func (p *OwAccountRespHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountRespHeader(%+v)", *p)
}

type OwAccountCaptchaResp struct {
	RespHeader *OwAccountRespHeader `thrift:"respHeader,1" json:"respHeader"`
	Captcha    string               `thrift:"captcha,2" json:"captcha"`
}

func NewOwAccountCaptchaResp() *OwAccountCaptchaResp {
	return &OwAccountCaptchaResp{}
}

func (p *OwAccountCaptchaResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountCaptchaResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwAccountRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwAccountCaptchaResp) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Captcha = v
	}
	return nil
}

func (p *OwAccountCaptchaResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountCaptchaResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountCaptchaResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountCaptchaResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("captcha", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:captcha: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Captcha)); err != nil {
		return fmt.Errorf("%T.captcha (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:captcha: %s", p, err)
	}
	return err
}

func (p *OwAccountCaptchaResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountCaptchaResp(%+v)", *p)
}

type OwAccountRegisterReq struct {
	Phone    string `thrift:"phone,1" json:"phone"`
	Captcha  string `thrift:"captcha,2" json:"captcha"`
	Passwd   string `thrift:"passwd,3" json:"passwd"`
	Nickname string `thrift:"nickname,4" json:"nickname"`
}

func NewOwAccountRegisterReq() *OwAccountRegisterReq {
	return &OwAccountRegisterReq{}
}

func (p *OwAccountRegisterReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountRegisterReq) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *OwAccountRegisterReq) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Captcha = v
	}
	return nil
}

func (p *OwAccountRegisterReq) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Passwd = v
	}
	return nil
}

func (p *OwAccountRegisterReq) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Nickname = v
	}
	return nil
}

func (p *OwAccountRegisterReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountRegisterReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountRegisterReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone: %s", p, err)
	}
	return err
}

func (p *OwAccountRegisterReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("captcha", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:captcha: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Captcha)); err != nil {
		return fmt.Errorf("%T.captcha (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:captcha: %s", p, err)
	}
	return err
}

func (p *OwAccountRegisterReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("passwd", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:passwd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Passwd)); err != nil {
		return fmt.Errorf("%T.passwd (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:passwd: %s", p, err)
	}
	return err
}

func (p *OwAccountRegisterReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickname", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:nickname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Nickname)); err != nil {
		return fmt.Errorf("%T.nickname (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:nickname: %s", p, err)
	}
	return err
}

func (p *OwAccountRegisterReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountRegisterReq(%+v)", *p)
}

type OwAccountRegisterResp struct {
	RespHeader *OwAccountRespHeader `thrift:"respHeader,1" json:"respHeader"`
	Uid        string               `thrift:"uid,2" json:"uid"`
}

func NewOwAccountRegisterResp() *OwAccountRegisterResp {
	return &OwAccountRegisterResp{}
}

func (p *OwAccountRegisterResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountRegisterResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwAccountRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwAccountRegisterResp) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwAccountRegisterResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountRegisterResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountRegisterResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountRegisterResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *OwAccountRegisterResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountRegisterResp(%+v)", *p)
}

type OwAccountIncreaseDto struct {
	Mbean int64  `thrift:"mbean,1" json:"mbean"`
	Desc  string `thrift:"desc,2" json:"desc"`
	Exp   int64  `thrift:"exp,3" json:"exp"`
	Done  int64  `thrift:"done,4" json:"done"`
}

func NewOwAccountIncreaseDto() *OwAccountIncreaseDto {
	return &OwAccountIncreaseDto{}
}

func (p *OwAccountIncreaseDto) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountIncreaseDto) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mbean = v
	}
	return nil
}

func (p *OwAccountIncreaseDto) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *OwAccountIncreaseDto) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Exp = v
	}
	return nil
}

func (p *OwAccountIncreaseDto) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Done = v
	}
	return nil
}

func (p *OwAccountIncreaseDto) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountIncreaseDto"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountIncreaseDto) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mbean", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mbean: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mbean)); err != nil {
		return fmt.Errorf("%T.mbean (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mbean: %s", p, err)
	}
	return err
}

func (p *OwAccountIncreaseDto) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:desc: %s", p, err)
	}
	return err
}

func (p *OwAccountIncreaseDto) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exp", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Exp)); err != nil {
		return fmt.Errorf("%T.exp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exp: %s", p, err)
	}
	return err
}

func (p *OwAccountIncreaseDto) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("done", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:done: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Done)); err != nil {
		return fmt.Errorf("%T.done (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:done: %s", p, err)
	}
	return err
}

func (p *OwAccountIncreaseDto) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountIncreaseDto(%+v)", *p)
}

type OwAccountUserProfilePoint struct {
	Exp          int64  `thrift:"exp,1" json:"exp"`
	CurrentLevel string `thrift:"currentLevel,2" json:"currentLevel"`
	NextLevel    string `thrift:"nextLevel,3" json:"nextLevel"`
	Done         int32  `thrift:"done,4" json:"done"`
	Total        int64  `thrift:"total,5" json:"total"`
	Consumed     int64  `thrift:"consumed,6" json:"consumed"`
}

func NewOwAccountUserProfilePoint() *OwAccountUserProfilePoint {
	return &OwAccountUserProfilePoint{}
}

func (p *OwAccountUserProfilePoint) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountUserProfilePoint) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Exp = v
	}
	return nil
}

func (p *OwAccountUserProfilePoint) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CurrentLevel = v
	}
	return nil
}

func (p *OwAccountUserProfilePoint) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NextLevel = v
	}
	return nil
}

func (p *OwAccountUserProfilePoint) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Done = v
	}
	return nil
}

func (p *OwAccountUserProfilePoint) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *OwAccountUserProfilePoint) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Consumed = v
	}
	return nil
}

func (p *OwAccountUserProfilePoint) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountUserProfilePoint"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountUserProfilePoint) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exp", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Exp)); err != nil {
		return fmt.Errorf("%T.exp (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfilePoint) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currentLevel", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:currentLevel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrentLevel)); err != nil {
		return fmt.Errorf("%T.currentLevel (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:currentLevel: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfilePoint) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nextLevel", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:nextLevel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NextLevel)); err != nil {
		return fmt.Errorf("%T.nextLevel (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:nextLevel: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfilePoint) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("done", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:done: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Done)); err != nil {
		return fmt.Errorf("%T.done (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:done: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfilePoint) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:total: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Total)); err != nil {
		return fmt.Errorf("%T.total (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:total: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfilePoint) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumed", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consumed)); err != nil {
		return fmt.Errorf("%T.consumed (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:consumed: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfilePoint) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountUserProfilePoint(%+v)", *p)
}

type OwAccountUserProfile struct {
	Uid         string                     `thrift:"uid,1" json:"uid"`
	Phone       string                     `thrift:"phone,2" json:"phone"`
	Nickname    string                     `thrift:"nickname,3" json:"nickname"`
	Status      int16                      `thrift:"status,4" json:"status"`
	RegisterTs  int64                      `thrift:"registerTs,5" json:"registerTs"`
	LastLoginTs int64                      `thrift:"lastLoginTs,6" json:"lastLoginTs"`
	PointInfo   *OwAccountUserProfilePoint `thrift:"pointInfo,7" json:"pointInfo"`
}

func NewOwAccountUserProfile() *OwAccountUserProfile {
	return &OwAccountUserProfile{}
}

func (p *OwAccountUserProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountUserProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwAccountUserProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *OwAccountUserProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Nickname = v
	}
	return nil
}

func (p *OwAccountUserProfile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwAccountUserProfile) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RegisterTs = v
	}
	return nil
}

func (p *OwAccountUserProfile) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.LastLoginTs = v
	}
	return nil
}

func (p *OwAccountUserProfile) readField7(iprot thrift.TProtocol) error {
	p.PointInfo = NewOwAccountUserProfilePoint()
	if err := p.PointInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PointInfo)
	}
	return nil
}

func (p *OwAccountUserProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountUserProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountUserProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:phone: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickname", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:nickname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Nickname)); err != nil {
		return fmt.Errorf("%T.nickname (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:nickname: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:status: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfile) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("registerTs", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:registerTs: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RegisterTs)); err != nil {
		return fmt.Errorf("%T.registerTs (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:registerTs: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfile) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastLoginTs", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:lastLoginTs: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastLoginTs)); err != nil {
		return fmt.Errorf("%T.lastLoginTs (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:lastLoginTs: %s", p, err)
	}
	return err
}

func (p *OwAccountUserProfile) writeField7(oprot thrift.TProtocol) (err error) {
	if p.PointInfo != nil {
		if err := oprot.WriteFieldBegin("pointInfo", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:pointInfo: %s", p, err)
		}
		if err := p.PointInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PointInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:pointInfo: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountUserProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountUserProfile(%+v)", *p)
}

type OwAccountLoginResp struct {
	RespHeader  *OwAccountRespHeader  `thrift:"respHeader,1" json:"respHeader"`
	UserProfile *OwAccountUserProfile `thrift:"userProfile,2" json:"userProfile"`
}

func NewOwAccountLoginResp() *OwAccountLoginResp {
	return &OwAccountLoginResp{}
}

func (p *OwAccountLoginResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountLoginResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwAccountRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwAccountLoginResp) readField2(iprot thrift.TProtocol) error {
	p.UserProfile = NewOwAccountUserProfile()
	if err := p.UserProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserProfile)
	}
	return nil
}

func (p *OwAccountLoginResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountLoginResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountLoginResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountLoginResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UserProfile != nil {
		if err := oprot.WriteFieldBegin("userProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:userProfile: %s", p, err)
		}
		if err := p.UserProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:userProfile: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountLoginResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountLoginResp(%+v)", *p)
}

type OwAccountResetPasswdResp struct {
	RespHeader *OwAccountRespHeader `thrift:"respHeader,1" json:"respHeader"`
	Uid        string               `thrift:"uid,2" json:"uid"`
}

func NewOwAccountResetPasswdResp() *OwAccountResetPasswdResp {
	return &OwAccountResetPasswdResp{}
}

func (p *OwAccountResetPasswdResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountResetPasswdResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwAccountRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwAccountResetPasswdResp) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwAccountResetPasswdResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountResetPasswdResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountResetPasswdResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountResetPasswdResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *OwAccountResetPasswdResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountResetPasswdResp(%+v)", *p)
}

type OwAccountUserProfileResp struct {
	RespHeader  *OwAccountRespHeader  `thrift:"respHeader,1" json:"respHeader"`
	UserProfile *OwAccountUserProfile `thrift:"userProfile,2" json:"userProfile"`
}

func NewOwAccountUserProfileResp() *OwAccountUserProfileResp {
	return &OwAccountUserProfileResp{}
}

func (p *OwAccountUserProfileResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAccountUserProfileResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwAccountRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwAccountUserProfileResp) readField2(iprot thrift.TProtocol) error {
	p.UserProfile = NewOwAccountUserProfile()
	if err := p.UserProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserProfile)
	}
	return nil
}

func (p *OwAccountUserProfileResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAccountUserProfileResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAccountUserProfileResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountUserProfileResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UserProfile != nil {
		if err := oprot.WriteFieldBegin("userProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:userProfile: %s", p, err)
		}
		if err := p.UserProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:userProfile: %s", p, err)
		}
	}
	return err
}

func (p *OwAccountUserProfileResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAccountUserProfileResp(%+v)", *p)
}

type OwMallReqHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
}

func NewOwMallReqHeader() *OwMallReqHeader {
	return &OwMallReqHeader{}
}

func (p *OwMallReqHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallReqHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwMallReqHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallReqHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallReqHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwMallReqHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallReqHeader(%+v)", *p)
}

type OwMallRespHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
	// unused field # 2
	ErrorCode    OwMallErrorCode `thrift:"errorCode,3" json:"errorCode"`
	ErrorMessage string          `thrift:"errorMessage,4" json:"errorMessage"`
}

func NewOwMallRespHeader() *OwMallRespHeader {
	return &OwMallRespHeader{
		ErrorCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwMallRespHeader) IsSetErrorCode() bool {
	return int64(p.ErrorCode) != math.MinInt32-1
}

func (p *OwMallRespHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallRespHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwMallRespHeader) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ErrorCode = OwMallErrorCode(v)
	}
	return nil
}

func (p *OwMallRespHeader) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ErrorMessage = v
	}
	return nil
}

func (p *OwMallRespHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallRespHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallRespHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwMallRespHeader) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorCode() {
		if err := oprot.WriteFieldBegin("errorCode", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:errorCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ErrorCode)); err != nil {
			return fmt.Errorf("%T.errorCode (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:errorCode: %s", p, err)
		}
	}
	return err
}

func (p *OwMallRespHeader) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("errorMessage", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:errorMessage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrorMessage)); err != nil {
		return fmt.Errorf("%T.errorMessage (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:errorMessage: %s", p, err)
	}
	return err
}

func (p *OwMallRespHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallRespHeader(%+v)", *p)
}

type OwMallItem struct {
	Id          string `thrift:"id,1" json:"id"`
	Title       string `thrift:"title,2" json:"title"`
	Desc        string `thrift:"desc,3" json:"desc"`
	Cost        int64  `thrift:"cost,4" json:"cost"`
	Logo        string `thrift:"logo,5" json:"logo"`
	ItemType    int16  `thrift:"item_type,6" json:"item_type"`
	ItemSubType int16  `thrift:"item_sub_type,7" json:"item_sub_type"`
}

func NewOwMallItem() *OwMallItem {
	return &OwMallItem{}
}

func (p *OwMallItem) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I16 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I16 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallItem) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *OwMallItem) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *OwMallItem) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *OwMallItem) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cost = v
	}
	return nil
}

func (p *OwMallItem) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *OwMallItem) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ItemType = v
	}
	return nil
}

func (p *OwMallItem) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ItemSubType = v
	}
	return nil
}

func (p *OwMallItem) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallItem"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *OwMallItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:title: %s", p, err)
	}
	return err
}

func (p *OwMallItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:desc: %s", p, err)
	}
	return err
}

func (p *OwMallItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cost: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cost)); err != nil {
		return fmt.Errorf("%T.cost (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cost: %s", p, err)
	}
	return err
}

func (p *OwMallItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:logo: %s", p, err)
	}
	return err
}

func (p *OwMallItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("item_type", thrift.I16, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:item_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ItemType)); err != nil {
		return fmt.Errorf("%T.item_type (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:item_type: %s", p, err)
	}
	return err
}

func (p *OwMallItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("item_sub_type", thrift.I16, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:item_sub_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ItemSubType)); err != nil {
		return fmt.Errorf("%T.item_sub_type (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:item_sub_type: %s", p, err)
	}
	return err
}

func (p *OwMallItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallItem(%+v)", *p)
}

type OwMallExchangeRecord struct {
	Id          int64  `thrift:"id,1" json:"id"`
	ItemType    int16  `thrift:"item_type,2" json:"item_type"`
	ItemSubType int16  `thrift:"item_sub_type,3" json:"item_sub_type"`
	Title       string `thrift:"title,4" json:"title"`
	Status      int16  `thrift:"status,5" json:"status"`
	Time        int64  `thrift:"time,6" json:"time"`
}

func NewOwMallExchangeRecord() *OwMallExchangeRecord {
	return &OwMallExchangeRecord{}
}

func (p *OwMallExchangeRecord) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I16 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I16 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallExchangeRecord) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *OwMallExchangeRecord) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ItemType = v
	}
	return nil
}

func (p *OwMallExchangeRecord) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ItemSubType = v
	}
	return nil
}

func (p *OwMallExchangeRecord) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *OwMallExchangeRecord) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwMallExchangeRecord) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *OwMallExchangeRecord) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallExchangeRecord"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallExchangeRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *OwMallExchangeRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("item_type", thrift.I16, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:item_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ItemType)); err != nil {
		return fmt.Errorf("%T.item_type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:item_type: %s", p, err)
	}
	return err
}

func (p *OwMallExchangeRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("item_sub_type", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:item_sub_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ItemSubType)); err != nil {
		return fmt.Errorf("%T.item_sub_type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:item_sub_type: %s", p, err)
	}
	return err
}

func (p *OwMallExchangeRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:title: %s", p, err)
	}
	return err
}

func (p *OwMallExchangeRecord) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:status: %s", p, err)
	}
	return err
}

func (p *OwMallExchangeRecord) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:time: %s", p, err)
	}
	return err
}

func (p *OwMallExchangeRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallExchangeRecord(%+v)", *p)
}

type OwMallGetItemResp struct {
	RespHeader *OwMallRespHeader `thrift:"respHeader,1" json:"respHeader"`
	Items      []*OwMallItem     `thrift:"items,2" json:"items"`
}

func NewOwMallGetItemResp() *OwMallGetItemResp {
	return &OwMallGetItemResp{}
}

func (p *OwMallGetItemResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallGetItemResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwMallRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwMallGetItemResp) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Items = make([]*OwMallItem, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewOwMallItem()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Items = append(p.Items, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwMallGetItemResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallGetItemResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallGetItemResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwMallGetItemResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Items != nil {
		if err := oprot.WriteFieldBegin("items", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:items: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Items)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Items {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:items: %s", p, err)
		}
	}
	return err
}

func (p *OwMallGetItemResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallGetItemResp(%+v)", *p)
}

type OwMallExchangeResp struct {
	RespHeader  *OwMallRespHeader     `thrift:"respHeader,1" json:"respHeader"`
	UserProfile *OwAccountUserProfile `thrift:"userProfile,2" json:"userProfile"`
}

func NewOwMallExchangeResp() *OwMallExchangeResp {
	return &OwMallExchangeResp{}
}

func (p *OwMallExchangeResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallExchangeResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwMallRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwMallExchangeResp) readField2(iprot thrift.TProtocol) error {
	p.UserProfile = NewOwAccountUserProfile()
	if err := p.UserProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserProfile)
	}
	return nil
}

func (p *OwMallExchangeResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallExchangeResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallExchangeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwMallExchangeResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UserProfile != nil {
		if err := oprot.WriteFieldBegin("userProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:userProfile: %s", p, err)
		}
		if err := p.UserProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:userProfile: %s", p, err)
		}
	}
	return err
}

func (p *OwMallExchangeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallExchangeResp(%+v)", *p)
}

type OwMallExchangeRecordResp struct {
	RespHeader *OwMallRespHeader       `thrift:"respHeader,1" json:"respHeader"`
	Records    []*OwMallExchangeRecord `thrift:"records,2" json:"records"`
}

func NewOwMallExchangeRecordResp() *OwMallExchangeRecordResp {
	return &OwMallExchangeRecordResp{}
}

func (p *OwMallExchangeRecordResp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMallExchangeRecordResp) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwMallRespHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwMallExchangeRecordResp) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Records = make([]*OwMallExchangeRecord, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewOwMallExchangeRecord()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Records = append(p.Records, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwMallExchangeRecordResp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMallExchangeRecordResp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMallExchangeRecordResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwMallExchangeRecordResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Records != nil {
		if err := oprot.WriteFieldBegin("records", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:records: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Records)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Records {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:records: %s", p, err)
		}
	}
	return err
}

func (p *OwMallExchangeRecordResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMallExchangeRecordResp(%+v)", *p)
}
