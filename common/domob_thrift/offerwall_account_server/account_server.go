// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_account_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

type AccountServer interface { //Account Server服务

	// @Description("生成注册的短信验证码")
	//
	// Parameters:
	//  - ReqHeader
	//  - Phone
	//  - CaptchaType
	GenerateCaptcha(reqHeader *OwAccountReqHeader, phone string, captchaType OwAccountCaptchaType) (r *OwAccountCaptchaResp, err error)
	// @Description("新用户注册")
	//
	// Parameters:
	//  - ReqHeader
	//  - RegisterReq
	AccountRegister(reqHeader *OwAccountReqHeader, registerReq *OwAccountRegisterReq) (r *OwAccountRegisterResp, err error)
	// @Description("登陆")
	//
	// Parameters:
	//  - ReqHeader
	//  - Phone
	//  - Passwd
	AccountLogin(reqHeader *OwAccountReqHeader, phone string, passwd string) (r *OwAccountLoginResp, err error)
	// @Description("重置密码")
	//
	// Parameters:
	//  - ReqHeader
	//  - Phone
	//  - Captcha
	//  - Passwd
	ResetPasswd(reqHeader *OwAccountReqHeader, phone string, captcha string, passwd string) (r *OwAccountResetPasswdResp, err error)
	// @Description("获取账户信息")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	GetUserProfile(reqHeader *OwAccountReqHeader, uid string) (r *OwAccountUserProfileResp, err error)
	// @Description("获取账户盟豆信息")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	PointCheck(reqHeader *OwAccountReqHeader, uid string) (r *OwAccountUserProfileResp, err error)
	// @Description("账户盟豆、经验、完成任务数等项增加。各项可为0，表示不增加")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	//  - OrderId
	//  - Dto
	Increase(reqHeader *OwAccountReqHeader, uid string, orderId string, dto *OwAccountIncreaseDto) (r *OwAccountUserProfileResp, err error)
	// @Description("账户盟豆扣除")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	//  - OrderId
	//  - Mbean
	//  - Desc
	Consume(reqHeader *OwAccountReqHeader, uid string, orderId string, mbean int64, desc string) (r *OwAccountUserProfileResp, err error)
	// @Description("账户盟豆取消扣除（异步消费盟豆失败后的退回）")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	//  - OrderId
	//  - Mbean
	//  - Desc
	ConsumeRollback(reqHeader *OwAccountReqHeader, uid string, orderId string, mbean int64, desc string) (r *OwAccountUserProfileResp, err error)
	// @Description("获取商城商品列表")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	GetMallItems(reqHeader *OwMallReqHeader, uid string) (r *OwMallGetItemResp, err error)
	// @Description("商品兑换")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	//  - UserAccount
	//  - ItemId
	//  - OrderId
	Exchange(reqHeader *OwMallReqHeader, uid string, userAccount string, itemId string, orderId string) (r *OwMallExchangeResp, err error)
	// @Description("获取商品兑换记录")
	//
	// Parameters:
	//  - ReqHeader
	//  - Uid
	ExchangeRecords(reqHeader *OwMallReqHeader, uid string) (r *OwMallExchangeRecordResp, err error)
}

//Account Server服务
type AccountServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAccountServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AccountServerClient {
	return &AccountServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAccountServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AccountServerClient {
	return &AccountServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// @Description("生成注册的短信验证码")
//
// Parameters:
//  - ReqHeader
//  - Phone
//  - CaptchaType
func (p *AccountServerClient) GenerateCaptcha(reqHeader *OwAccountReqHeader, phone string, captchaType OwAccountCaptchaType) (r *OwAccountCaptchaResp, err error) {
	if err = p.sendGenerateCaptcha(reqHeader, phone, captchaType); err != nil {
		return
	}
	return p.recvGenerateCaptcha()
}

func (p *AccountServerClient) sendGenerateCaptcha(reqHeader *OwAccountReqHeader, phone string, captchaType OwAccountCaptchaType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("generateCaptcha", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewGenerateCaptchaArgs()
	args2.ReqHeader = reqHeader
	args2.Phone = phone
	args2.CaptchaType = captchaType
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvGenerateCaptcha() (value *OwAccountCaptchaResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewGenerateCaptchaResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	return
}

// @Description("新用户注册")
//
// Parameters:
//  - ReqHeader
//  - RegisterReq
func (p *AccountServerClient) AccountRegister(reqHeader *OwAccountReqHeader, registerReq *OwAccountRegisterReq) (r *OwAccountRegisterResp, err error) {
	if err = p.sendAccountRegister(reqHeader, registerReq); err != nil {
		return
	}
	return p.recvAccountRegister()
}

func (p *AccountServerClient) sendAccountRegister(reqHeader *OwAccountReqHeader, registerReq *OwAccountRegisterReq) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("accountRegister", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args6 := NewAccountRegisterArgs()
	args6.ReqHeader = reqHeader
	args6.RegisterReq = registerReq
	if err = args6.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvAccountRegister() (value *OwAccountRegisterResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error8 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error9 error
		error9, err = error8.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error9
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result7 := NewAccountRegisterResult()
	if err = result7.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result7.Success
	return
}

// @Description("登陆")
//
// Parameters:
//  - ReqHeader
//  - Phone
//  - Passwd
func (p *AccountServerClient) AccountLogin(reqHeader *OwAccountReqHeader, phone string, passwd string) (r *OwAccountLoginResp, err error) {
	if err = p.sendAccountLogin(reqHeader, phone, passwd); err != nil {
		return
	}
	return p.recvAccountLogin()
}

func (p *AccountServerClient) sendAccountLogin(reqHeader *OwAccountReqHeader, phone string, passwd string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("accountLogin", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args10 := NewAccountLoginArgs()
	args10.ReqHeader = reqHeader
	args10.Phone = phone
	args10.Passwd = passwd
	if err = args10.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvAccountLogin() (value *OwAccountLoginResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error12 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error13 error
		error13, err = error12.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error13
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result11 := NewAccountLoginResult()
	if err = result11.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result11.Success
	return
}

// @Description("重置密码")
//
// Parameters:
//  - ReqHeader
//  - Phone
//  - Captcha
//  - Passwd
func (p *AccountServerClient) ResetPasswd(reqHeader *OwAccountReqHeader, phone string, captcha string, passwd string) (r *OwAccountResetPasswdResp, err error) {
	if err = p.sendResetPasswd(reqHeader, phone, captcha, passwd); err != nil {
		return
	}
	return p.recvResetPasswd()
}

func (p *AccountServerClient) sendResetPasswd(reqHeader *OwAccountReqHeader, phone string, captcha string, passwd string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resetPasswd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args14 := NewResetPasswdArgs()
	args14.ReqHeader = reqHeader
	args14.Phone = phone
	args14.Captcha = captcha
	args14.Passwd = passwd
	if err = args14.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvResetPasswd() (value *OwAccountResetPasswdResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error16 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error17 error
		error17, err = error16.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error17
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result15 := NewResetPasswdResult()
	if err = result15.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result15.Success
	return
}

// @Description("获取账户信息")
//
// Parameters:
//  - ReqHeader
//  - Uid
func (p *AccountServerClient) GetUserProfile(reqHeader *OwAccountReqHeader, uid string) (r *OwAccountUserProfileResp, err error) {
	if err = p.sendGetUserProfile(reqHeader, uid); err != nil {
		return
	}
	return p.recvGetUserProfile()
}

func (p *AccountServerClient) sendGetUserProfile(reqHeader *OwAccountReqHeader, uid string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getUserProfile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args18 := NewGetUserProfileArgs()
	args18.ReqHeader = reqHeader
	args18.Uid = uid
	if err = args18.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvGetUserProfile() (value *OwAccountUserProfileResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error20 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error21 error
		error21, err = error20.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error21
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result19 := NewGetUserProfileResult()
	if err = result19.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result19.Success
	return
}

// @Description("获取账户盟豆信息")
//
// Parameters:
//  - ReqHeader
//  - Uid
func (p *AccountServerClient) PointCheck(reqHeader *OwAccountReqHeader, uid string) (r *OwAccountUserProfileResp, err error) {
	if err = p.sendPointCheck(reqHeader, uid); err != nil {
		return
	}
	return p.recvPointCheck()
}

func (p *AccountServerClient) sendPointCheck(reqHeader *OwAccountReqHeader, uid string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("pointCheck", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args22 := NewPointCheckArgs()
	args22.ReqHeader = reqHeader
	args22.Uid = uid
	if err = args22.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvPointCheck() (value *OwAccountUserProfileResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error24 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error25 error
		error25, err = error24.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error25
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result23 := NewPointCheckResult()
	if err = result23.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result23.Success
	return
}

// @Description("账户盟豆、经验、完成任务数等项增加。各项可为0，表示不增加")
//
// Parameters:
//  - ReqHeader
//  - Uid
//  - OrderId
//  - Dto
func (p *AccountServerClient) Increase(reqHeader *OwAccountReqHeader, uid string, orderId string, dto *OwAccountIncreaseDto) (r *OwAccountUserProfileResp, err error) {
	if err = p.sendIncrease(reqHeader, uid, orderId, dto); err != nil {
		return
	}
	return p.recvIncrease()
}

func (p *AccountServerClient) sendIncrease(reqHeader *OwAccountReqHeader, uid string, orderId string, dto *OwAccountIncreaseDto) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("increase", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args26 := NewIncreaseArgs()
	args26.ReqHeader = reqHeader
	args26.Uid = uid
	args26.OrderId = orderId
	args26.Dto = dto
	if err = args26.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvIncrease() (value *OwAccountUserProfileResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error28 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error29 error
		error29, err = error28.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error29
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result27 := NewIncreaseResult()
	if err = result27.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result27.Success
	return
}

// @Description("账户盟豆扣除")
//
// Parameters:
//  - ReqHeader
//  - Uid
//  - OrderId
//  - Mbean
//  - Desc
func (p *AccountServerClient) Consume(reqHeader *OwAccountReqHeader, uid string, orderId string, mbean int64, desc string) (r *OwAccountUserProfileResp, err error) {
	if err = p.sendConsume(reqHeader, uid, orderId, mbean, desc); err != nil {
		return
	}
	return p.recvConsume()
}

func (p *AccountServerClient) sendConsume(reqHeader *OwAccountReqHeader, uid string, orderId string, mbean int64, desc string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("consume", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args30 := NewConsumeArgs()
	args30.ReqHeader = reqHeader
	args30.Uid = uid
	args30.OrderId = orderId
	args30.Mbean = mbean
	args30.Desc = desc
	if err = args30.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvConsume() (value *OwAccountUserProfileResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error32 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error33 error
		error33, err = error32.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error33
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result31 := NewConsumeResult()
	if err = result31.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result31.Success
	return
}

// @Description("账户盟豆取消扣除（异步消费盟豆失败后的退回）")
//
// Parameters:
//  - ReqHeader
//  - Uid
//  - OrderId
//  - Mbean
//  - Desc
func (p *AccountServerClient) ConsumeRollback(reqHeader *OwAccountReqHeader, uid string, orderId string, mbean int64, desc string) (r *OwAccountUserProfileResp, err error) {
	if err = p.sendConsumeRollback(reqHeader, uid, orderId, mbean, desc); err != nil {
		return
	}
	return p.recvConsumeRollback()
}

func (p *AccountServerClient) sendConsumeRollback(reqHeader *OwAccountReqHeader, uid string, orderId string, mbean int64, desc string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("consumeRollback", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args34 := NewConsumeRollbackArgs()
	args34.ReqHeader = reqHeader
	args34.Uid = uid
	args34.OrderId = orderId
	args34.Mbean = mbean
	args34.Desc = desc
	if err = args34.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvConsumeRollback() (value *OwAccountUserProfileResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error36 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error37 error
		error37, err = error36.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error37
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result35 := NewConsumeRollbackResult()
	if err = result35.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result35.Success
	return
}

// @Description("获取商城商品列表")
//
// Parameters:
//  - ReqHeader
//  - Uid
func (p *AccountServerClient) GetMallItems(reqHeader *OwMallReqHeader, uid string) (r *OwMallGetItemResp, err error) {
	if err = p.sendGetMallItems(reqHeader, uid); err != nil {
		return
	}
	return p.recvGetMallItems()
}

func (p *AccountServerClient) sendGetMallItems(reqHeader *OwMallReqHeader, uid string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMallItems", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args38 := NewGetMallItemsArgs()
	args38.ReqHeader = reqHeader
	args38.Uid = uid
	if err = args38.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvGetMallItems() (value *OwMallGetItemResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error40 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error41 error
		error41, err = error40.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error41
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result39 := NewGetMallItemsResult()
	if err = result39.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result39.Success
	return
}

// @Description("商品兑换")
//
// Parameters:
//  - ReqHeader
//  - Uid
//  - UserAccount
//  - ItemId
//  - OrderId
func (p *AccountServerClient) Exchange(reqHeader *OwMallReqHeader, uid string, userAccount string, itemId string, orderId string) (r *OwMallExchangeResp, err error) {
	if err = p.sendExchange(reqHeader, uid, userAccount, itemId, orderId); err != nil {
		return
	}
	return p.recvExchange()
}

func (p *AccountServerClient) sendExchange(reqHeader *OwMallReqHeader, uid string, userAccount string, itemId string, orderId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exchange", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args42 := NewExchangeArgs()
	args42.ReqHeader = reqHeader
	args42.Uid = uid
	args42.UserAccount = userAccount
	args42.ItemId = itemId
	args42.OrderId = orderId
	if err = args42.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvExchange() (value *OwMallExchangeResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error44 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error45 error
		error45, err = error44.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error45
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result43 := NewExchangeResult()
	if err = result43.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result43.Success
	return
}

// @Description("获取商品兑换记录")
//
// Parameters:
//  - ReqHeader
//  - Uid
func (p *AccountServerClient) ExchangeRecords(reqHeader *OwMallReqHeader, uid string) (r *OwMallExchangeRecordResp, err error) {
	if err = p.sendExchangeRecords(reqHeader, uid); err != nil {
		return
	}
	return p.recvExchangeRecords()
}

func (p *AccountServerClient) sendExchangeRecords(reqHeader *OwMallReqHeader, uid string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exchangeRecords", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args46 := NewExchangeRecordsArgs()
	args46.ReqHeader = reqHeader
	args46.Uid = uid
	if err = args46.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServerClient) recvExchangeRecords() (value *OwMallExchangeRecordResp, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error48 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error49 error
		error49, err = error48.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error49
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result47 := NewExchangeRecordsResult()
	if err = result47.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result47.Success
	return
}

type AccountServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AccountServer
}

func (p *AccountServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AccountServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AccountServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAccountServerProcessor(handler AccountServer) *AccountServerProcessor {

	self50 := &AccountServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self50.processorMap["generateCaptcha"] = &accountServerProcessorGenerateCaptcha{handler: handler}
	self50.processorMap["accountRegister"] = &accountServerProcessorAccountRegister{handler: handler}
	self50.processorMap["accountLogin"] = &accountServerProcessorAccountLogin{handler: handler}
	self50.processorMap["resetPasswd"] = &accountServerProcessorResetPasswd{handler: handler}
	self50.processorMap["getUserProfile"] = &accountServerProcessorGetUserProfile{handler: handler}
	self50.processorMap["pointCheck"] = &accountServerProcessorPointCheck{handler: handler}
	self50.processorMap["increase"] = &accountServerProcessorIncrease{handler: handler}
	self50.processorMap["consume"] = &accountServerProcessorConsume{handler: handler}
	self50.processorMap["consumeRollback"] = &accountServerProcessorConsumeRollback{handler: handler}
	self50.processorMap["getMallItems"] = &accountServerProcessorGetMallItems{handler: handler}
	self50.processorMap["exchange"] = &accountServerProcessorExchange{handler: handler}
	self50.processorMap["exchangeRecords"] = &accountServerProcessorExchangeRecords{handler: handler}
	return self50
}

func (p *AccountServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x51 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x51.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x51

}

type accountServerProcessorGenerateCaptcha struct {
	handler AccountServer
}

func (p *accountServerProcessorGenerateCaptcha) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGenerateCaptchaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("generateCaptcha", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGenerateCaptchaResult()
	if result.Success, err = p.handler.GenerateCaptcha(args.ReqHeader, args.Phone, args.CaptchaType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing generateCaptcha: "+err.Error())
		oprot.WriteMessageBegin("generateCaptcha", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("generateCaptcha", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorAccountRegister struct {
	handler AccountServer
}

func (p *accountServerProcessorAccountRegister) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAccountRegisterArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("accountRegister", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAccountRegisterResult()
	if result.Success, err = p.handler.AccountRegister(args.ReqHeader, args.RegisterReq); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing accountRegister: "+err.Error())
		oprot.WriteMessageBegin("accountRegister", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("accountRegister", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorAccountLogin struct {
	handler AccountServer
}

func (p *accountServerProcessorAccountLogin) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAccountLoginArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("accountLogin", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAccountLoginResult()
	if result.Success, err = p.handler.AccountLogin(args.ReqHeader, args.Phone, args.Passwd); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing accountLogin: "+err.Error())
		oprot.WriteMessageBegin("accountLogin", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("accountLogin", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorResetPasswd struct {
	handler AccountServer
}

func (p *accountServerProcessorResetPasswd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResetPasswdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resetPasswd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResetPasswdResult()
	if result.Success, err = p.handler.ResetPasswd(args.ReqHeader, args.Phone, args.Captcha, args.Passwd); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resetPasswd: "+err.Error())
		oprot.WriteMessageBegin("resetPasswd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resetPasswd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorGetUserProfile struct {
	handler AccountServer
}

func (p *accountServerProcessorGetUserProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUserProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getUserProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUserProfileResult()
	if result.Success, err = p.handler.GetUserProfile(args.ReqHeader, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserProfile: "+err.Error())
		oprot.WriteMessageBegin("getUserProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getUserProfile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorPointCheck struct {
	handler AccountServer
}

func (p *accountServerProcessorPointCheck) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPointCheckArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("pointCheck", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPointCheckResult()
	if result.Success, err = p.handler.PointCheck(args.ReqHeader, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing pointCheck: "+err.Error())
		oprot.WriteMessageBegin("pointCheck", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("pointCheck", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorIncrease struct {
	handler AccountServer
}

func (p *accountServerProcessorIncrease) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewIncreaseArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("increase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewIncreaseResult()
	if result.Success, err = p.handler.Increase(args.ReqHeader, args.Uid, args.OrderId, args.Dto); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing increase: "+err.Error())
		oprot.WriteMessageBegin("increase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("increase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorConsume struct {
	handler AccountServer
}

func (p *accountServerProcessorConsume) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConsumeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("consume", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConsumeResult()
	if result.Success, err = p.handler.Consume(args.ReqHeader, args.Uid, args.OrderId, args.Mbean, args.Desc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing consume: "+err.Error())
		oprot.WriteMessageBegin("consume", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("consume", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorConsumeRollback struct {
	handler AccountServer
}

func (p *accountServerProcessorConsumeRollback) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConsumeRollbackArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("consumeRollback", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConsumeRollbackResult()
	if result.Success, err = p.handler.ConsumeRollback(args.ReqHeader, args.Uid, args.OrderId, args.Mbean, args.Desc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing consumeRollback: "+err.Error())
		oprot.WriteMessageBegin("consumeRollback", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("consumeRollback", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorGetMallItems struct {
	handler AccountServer
}

func (p *accountServerProcessorGetMallItems) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMallItemsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMallItems", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMallItemsResult()
	if result.Success, err = p.handler.GetMallItems(args.ReqHeader, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMallItems: "+err.Error())
		oprot.WriteMessageBegin("getMallItems", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMallItems", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorExchange struct {
	handler AccountServer
}

func (p *accountServerProcessorExchange) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExchangeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExchangeResult()
	if result.Success, err = p.handler.Exchange(args.ReqHeader, args.Uid, args.UserAccount, args.ItemId, args.OrderId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exchange: "+err.Error())
		oprot.WriteMessageBegin("exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exchange", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServerProcessorExchangeRecords struct {
	handler AccountServer
}

func (p *accountServerProcessorExchangeRecords) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExchangeRecordsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exchangeRecords", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExchangeRecordsResult()
	if result.Success, err = p.handler.ExchangeRecords(args.ReqHeader, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exchangeRecords: "+err.Error())
		oprot.WriteMessageBegin("exchangeRecords", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exchangeRecords", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GenerateCaptchaArgs struct {
	ReqHeader   *OwAccountReqHeader  `thrift:"reqHeader,1" json:"reqHeader"`
	Phone       string               `thrift:"phone,2" json:"phone"`
	CaptchaType OwAccountCaptchaType `thrift:"captchaType,3" json:"captchaType"`
}

func NewGenerateCaptchaArgs() *GenerateCaptchaArgs {
	return &GenerateCaptchaArgs{
		CaptchaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GenerateCaptchaArgs) IsSetCaptchaType() bool {
	return int64(p.CaptchaType) != math.MinInt32-1
}

func (p *GenerateCaptchaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GenerateCaptchaArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GenerateCaptchaArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *GenerateCaptchaArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CaptchaType = OwAccountCaptchaType(v)
	}
	return nil
}

func (p *GenerateCaptchaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("generateCaptcha_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GenerateCaptchaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GenerateCaptchaArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:phone: %s", p, err)
	}
	return err
}

func (p *GenerateCaptchaArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaptchaType() {
		if err := oprot.WriteFieldBegin("captchaType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:captchaType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CaptchaType)); err != nil {
			return fmt.Errorf("%T.captchaType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:captchaType: %s", p, err)
		}
	}
	return err
}

func (p *GenerateCaptchaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateCaptchaArgs(%+v)", *p)
}

type GenerateCaptchaResult struct {
	Success *OwAccountCaptchaResp `thrift:"success,0" json:"success"`
}

func NewGenerateCaptchaResult() *GenerateCaptchaResult {
	return &GenerateCaptchaResult{}
}

func (p *GenerateCaptchaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GenerateCaptchaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountCaptchaResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GenerateCaptchaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("generateCaptcha_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GenerateCaptchaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GenerateCaptchaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateCaptchaResult(%+v)", *p)
}

type AccountRegisterArgs struct {
	ReqHeader   *OwAccountReqHeader   `thrift:"reqHeader,1" json:"reqHeader"`
	RegisterReq *OwAccountRegisterReq `thrift:"registerReq,2" json:"registerReq"`
}

func NewAccountRegisterArgs() *AccountRegisterArgs {
	return &AccountRegisterArgs{}
}

func (p *AccountRegisterArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AccountRegisterArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *AccountRegisterArgs) readField2(iprot thrift.TProtocol) error {
	p.RegisterReq = NewOwAccountRegisterReq()
	if err := p.RegisterReq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RegisterReq)
	}
	return nil
}

func (p *AccountRegisterArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("accountRegister_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AccountRegisterArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *AccountRegisterArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RegisterReq != nil {
		if err := oprot.WriteFieldBegin("registerReq", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:registerReq: %s", p, err)
		}
		if err := p.RegisterReq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RegisterReq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:registerReq: %s", p, err)
		}
	}
	return err
}

func (p *AccountRegisterArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountRegisterArgs(%+v)", *p)
}

type AccountRegisterResult struct {
	Success *OwAccountRegisterResp `thrift:"success,0" json:"success"`
}

func NewAccountRegisterResult() *AccountRegisterResult {
	return &AccountRegisterResult{}
}

func (p *AccountRegisterResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AccountRegisterResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountRegisterResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AccountRegisterResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("accountRegister_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AccountRegisterResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AccountRegisterResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountRegisterResult(%+v)", *p)
}

type AccountLoginArgs struct {
	ReqHeader *OwAccountReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Phone     string              `thrift:"phone,2" json:"phone"`
	Passwd    string              `thrift:"passwd,3" json:"passwd"`
}

func NewAccountLoginArgs() *AccountLoginArgs {
	return &AccountLoginArgs{}
}

func (p *AccountLoginArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AccountLoginArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *AccountLoginArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *AccountLoginArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Passwd = v
	}
	return nil
}

func (p *AccountLoginArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("accountLogin_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AccountLoginArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *AccountLoginArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:phone: %s", p, err)
	}
	return err
}

func (p *AccountLoginArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("passwd", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:passwd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Passwd)); err != nil {
		return fmt.Errorf("%T.passwd (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:passwd: %s", p, err)
	}
	return err
}

func (p *AccountLoginArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountLoginArgs(%+v)", *p)
}

type AccountLoginResult struct {
	Success *OwAccountLoginResp `thrift:"success,0" json:"success"`
}

func NewAccountLoginResult() *AccountLoginResult {
	return &AccountLoginResult{}
}

func (p *AccountLoginResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AccountLoginResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountLoginResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AccountLoginResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("accountLogin_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AccountLoginResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AccountLoginResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountLoginResult(%+v)", *p)
}

type ResetPasswdArgs struct {
	ReqHeader *OwAccountReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Phone     string              `thrift:"phone,2" json:"phone"`
	Captcha   string              `thrift:"captcha,3" json:"captcha"`
	Passwd    string              `thrift:"passwd,4" json:"passwd"`
}

func NewResetPasswdArgs() *ResetPasswdArgs {
	return &ResetPasswdArgs{}
}

func (p *ResetPasswdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswdArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *ResetPasswdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Phone = v
	}
	return nil
}

func (p *ResetPasswdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Captcha = v
	}
	return nil
}

func (p *ResetPasswdArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Passwd = v
	}
	return nil
}

func (p *ResetPasswdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetPasswd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResetPasswdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:phone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phone)); err != nil {
		return fmt.Errorf("%T.phone (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:phone: %s", p, err)
	}
	return err
}

func (p *ResetPasswdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("captcha", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:captcha: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Captcha)); err != nil {
		return fmt.Errorf("%T.captcha (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:captcha: %s", p, err)
	}
	return err
}

func (p *ResetPasswdArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("passwd", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:passwd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Passwd)); err != nil {
		return fmt.Errorf("%T.passwd (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:passwd: %s", p, err)
	}
	return err
}

func (p *ResetPasswdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetPasswdArgs(%+v)", *p)
}

type ResetPasswdResult struct {
	Success *OwAccountResetPasswdResp `thrift:"success,0" json:"success"`
}

func NewResetPasswdResult() *ResetPasswdResult {
	return &ResetPasswdResult{}
}

func (p *ResetPasswdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountResetPasswdResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ResetPasswdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetPasswd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ResetPasswdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetPasswdResult(%+v)", *p)
}

type GetUserProfileArgs struct {
	ReqHeader *OwAccountReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string              `thrift:"uid,2" json:"uid"`
}

func NewGetUserProfileArgs() *GetUserProfileArgs {
	return &GetUserProfileArgs{}
}

func (p *GetUserProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GetUserProfileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *GetUserProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserProfile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetUserProfileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *GetUserProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserProfileArgs(%+v)", *p)
}

type GetUserProfileResult struct {
	Success *OwAccountUserProfileResp `thrift:"success,0" json:"success"`
}

func NewGetUserProfileResult() *GetUserProfileResult {
	return &GetUserProfileResult{}
}

func (p *GetUserProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserProfileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountUserProfileResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetUserProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserProfile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUserProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserProfileResult(%+v)", *p)
}

type PointCheckArgs struct {
	ReqHeader *OwAccountReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string              `thrift:"uid,2" json:"uid"`
}

func NewPointCheckArgs() *PointCheckArgs {
	return &PointCheckArgs{}
}

func (p *PointCheckArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PointCheckArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *PointCheckArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *PointCheckArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("pointCheck_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PointCheckArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *PointCheckArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *PointCheckArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PointCheckArgs(%+v)", *p)
}

type PointCheckResult struct {
	Success *OwAccountUserProfileResp `thrift:"success,0" json:"success"`
}

func NewPointCheckResult() *PointCheckResult {
	return &PointCheckResult{}
}

func (p *PointCheckResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PointCheckResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountUserProfileResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PointCheckResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("pointCheck_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PointCheckResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PointCheckResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PointCheckResult(%+v)", *p)
}

type IncreaseArgs struct {
	ReqHeader *OwAccountReqHeader   `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string                `thrift:"uid,2" json:"uid"`
	OrderId   string                `thrift:"orderId,3" json:"orderId"`
	Dto       *OwAccountIncreaseDto `thrift:"dto,4" json:"dto"`
}

func NewIncreaseArgs() *IncreaseArgs {
	return &IncreaseArgs{}
}

func (p *IncreaseArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncreaseArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *IncreaseArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *IncreaseArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *IncreaseArgs) readField4(iprot thrift.TProtocol) error {
	p.Dto = NewOwAccountIncreaseDto()
	if err := p.Dto.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dto)
	}
	return nil
}

func (p *IncreaseArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("increase_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncreaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *IncreaseArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *IncreaseArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:orderId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:orderId: %s", p, err)
	}
	return err
}

func (p *IncreaseArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Dto != nil {
		if err := oprot.WriteFieldBegin("dto", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dto: %s", p, err)
		}
		if err := p.Dto.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dto)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dto: %s", p, err)
		}
	}
	return err
}

func (p *IncreaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncreaseArgs(%+v)", *p)
}

type IncreaseResult struct {
	Success *OwAccountUserProfileResp `thrift:"success,0" json:"success"`
}

func NewIncreaseResult() *IncreaseResult {
	return &IncreaseResult{}
}

func (p *IncreaseResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncreaseResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountUserProfileResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *IncreaseResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("increase_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncreaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *IncreaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncreaseResult(%+v)", *p)
}

type ConsumeArgs struct {
	ReqHeader *OwAccountReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string              `thrift:"uid,2" json:"uid"`
	OrderId   string              `thrift:"orderId,3" json:"orderId"`
	Mbean     int64               `thrift:"mbean,4" json:"mbean"`
	Desc      string              `thrift:"desc,5" json:"desc"`
}

func NewConsumeArgs() *ConsumeArgs {
	return &ConsumeArgs{}
}

func (p *ConsumeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumeArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *ConsumeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ConsumeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *ConsumeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mbean = v
	}
	return nil
}

func (p *ConsumeArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *ConsumeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consume_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *ConsumeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ConsumeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:orderId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:orderId: %s", p, err)
	}
	return err
}

func (p *ConsumeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mbean", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mbean: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mbean)); err != nil {
		return fmt.Errorf("%T.mbean (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mbean: %s", p, err)
	}
	return err
}

func (p *ConsumeArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:desc: %s", p, err)
	}
	return err
}

func (p *ConsumeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeArgs(%+v)", *p)
}

type ConsumeResult struct {
	Success *OwAccountUserProfileResp `thrift:"success,0" json:"success"`
}

func NewConsumeResult() *ConsumeResult {
	return &ConsumeResult{}
}

func (p *ConsumeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountUserProfileResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConsumeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consume_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConsumeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeResult(%+v)", *p)
}

type ConsumeRollbackArgs struct {
	ReqHeader *OwAccountReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string              `thrift:"uid,2" json:"uid"`
	OrderId   string              `thrift:"orderId,3" json:"orderId"`
	Mbean     int64               `thrift:"mbean,4" json:"mbean"`
	Desc      string              `thrift:"desc,5" json:"desc"`
}

func NewConsumeRollbackArgs() *ConsumeRollbackArgs {
	return &ConsumeRollbackArgs{}
}

func (p *ConsumeRollbackArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumeRollbackArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwAccountReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *ConsumeRollbackArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ConsumeRollbackArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *ConsumeRollbackArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mbean = v
	}
	return nil
}

func (p *ConsumeRollbackArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *ConsumeRollbackArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consumeRollback_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumeRollbackArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *ConsumeRollbackArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ConsumeRollbackArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:orderId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:orderId: %s", p, err)
	}
	return err
}

func (p *ConsumeRollbackArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mbean", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mbean: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mbean)); err != nil {
		return fmt.Errorf("%T.mbean (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mbean: %s", p, err)
	}
	return err
}

func (p *ConsumeRollbackArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:desc: %s", p, err)
	}
	return err
}

func (p *ConsumeRollbackArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeRollbackArgs(%+v)", *p)
}

type ConsumeRollbackResult struct {
	Success *OwAccountUserProfileResp `thrift:"success,0" json:"success"`
}

func NewConsumeRollbackResult() *ConsumeRollbackResult {
	return &ConsumeRollbackResult{}
}

func (p *ConsumeRollbackResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumeRollbackResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAccountUserProfileResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConsumeRollbackResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consumeRollback_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumeRollbackResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConsumeRollbackResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeRollbackResult(%+v)", *p)
}

type GetMallItemsArgs struct {
	ReqHeader *OwMallReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string           `thrift:"uid,2" json:"uid"`
}

func NewGetMallItemsArgs() *GetMallItemsArgs {
	return &GetMallItemsArgs{}
}

func (p *GetMallItemsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMallItemsArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwMallReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GetMallItemsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *GetMallItemsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMallItems_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMallItemsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetMallItemsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *GetMallItemsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMallItemsArgs(%+v)", *p)
}

type GetMallItemsResult struct {
	Success *OwMallGetItemResp `thrift:"success,0" json:"success"`
}

func NewGetMallItemsResult() *GetMallItemsResult {
	return &GetMallItemsResult{}
}

func (p *GetMallItemsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMallItemsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwMallGetItemResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetMallItemsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMallItems_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMallItemsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMallItemsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMallItemsResult(%+v)", *p)
}

type ExchangeArgs struct {
	ReqHeader   *OwMallReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid         string           `thrift:"uid,2" json:"uid"`
	UserAccount string           `thrift:"userAccount,3" json:"userAccount"`
	ItemId      string           `thrift:"itemId,4" json:"itemId"`
	OrderId     string           `thrift:"orderId,5" json:"orderId"`
}

func NewExchangeArgs() *ExchangeArgs {
	return &ExchangeArgs{}
}

func (p *ExchangeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExchangeArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwMallReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *ExchangeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ExchangeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserAccount = v
	}
	return nil
}

func (p *ExchangeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ItemId = v
	}
	return nil
}

func (p *ExchangeArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *ExchangeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exchange_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExchangeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *ExchangeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ExchangeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userAccount", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:userAccount: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAccount)); err != nil {
		return fmt.Errorf("%T.userAccount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:userAccount: %s", p, err)
	}
	return err
}

func (p *ExchangeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itemId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:itemId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItemId)); err != nil {
		return fmt.Errorf("%T.itemId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:itemId: %s", p, err)
	}
	return err
}

func (p *ExchangeArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:orderId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:orderId: %s", p, err)
	}
	return err
}

func (p *ExchangeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExchangeArgs(%+v)", *p)
}

type ExchangeResult struct {
	Success *OwMallExchangeResp `thrift:"success,0" json:"success"`
}

func NewExchangeResult() *ExchangeResult {
	return &ExchangeResult{}
}

func (p *ExchangeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExchangeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwMallExchangeResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ExchangeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exchange_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExchangeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ExchangeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExchangeResult(%+v)", *p)
}

type ExchangeRecordsArgs struct {
	ReqHeader *OwMallReqHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Uid       string           `thrift:"uid,2" json:"uid"`
}

func NewExchangeRecordsArgs() *ExchangeRecordsArgs {
	return &ExchangeRecordsArgs{}
}

func (p *ExchangeRecordsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExchangeRecordsArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwMallReqHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *ExchangeRecordsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ExchangeRecordsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exchangeRecords_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExchangeRecordsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *ExchangeRecordsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ExchangeRecordsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExchangeRecordsArgs(%+v)", *p)
}

type ExchangeRecordsResult struct {
	Success *OwMallExchangeRecordResp `thrift:"success,0" json:"success"`
}

func NewExchangeRecordsResult() *ExchangeRecordsResult {
	return &ExchangeRecordsResult{}
}

func (p *ExchangeRecordsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExchangeRecordsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwMallExchangeRecordResp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ExchangeRecordsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exchangeRecords_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExchangeRecordsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ExchangeRecordsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExchangeRecordsResult(%+v)", *p)
}
