// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package predict_model_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = rtb_types.GoUnusedProtection__
var _ = rtb_adinfo_types.GoUnusedProtection__

type PredictModelServer interface {
	dm303.DomobService

	// Parameters:
	//  - MsRequest
	GetPredictValue(ms_request *PredictModelServerRequest) (r *PredictModelServerResponse, err error)
}

type PredictModelServerClient struct {
	*dm303.DomobServiceClient
}

func NewPredictModelServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PredictModelServerClient {
	return &PredictModelServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewPredictModelServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PredictModelServerClient {
	return &PredictModelServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - MsRequest
func (p *PredictModelServerClient) GetPredictValue(ms_request *PredictModelServerRequest) (r *PredictModelServerResponse, err error) {
	if err = p.sendGetPredictValue(ms_request); err != nil {
		return
	}
	return p.recvGetPredictValue()
}

func (p *PredictModelServerClient) sendGetPredictValue(ms_request *PredictModelServerRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPredictValue", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewGetPredictValueArgs()
	args2.MsRequest = ms_request
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PredictModelServerClient) recvGetPredictValue() (value *PredictModelServerResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewGetPredictValueResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	return
}

type PredictModelServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewPredictModelServerProcessor(handler PredictModelServer) *PredictModelServerProcessor {
	self6 := &PredictModelServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self6.AddToProcessorMap("getPredictValue", &predictModelServerProcessorGetPredictValue{handler: handler})
	return self6
}

type predictModelServerProcessorGetPredictValue struct {
	handler PredictModelServer
}

func (p *predictModelServerProcessorGetPredictValue) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPredictValueArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPredictValue", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPredictValueResult()
	if result.Success, err = p.handler.GetPredictValue(args.MsRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPredictValue: "+err.Error())
		oprot.WriteMessageBegin("getPredictValue", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPredictValue", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetPredictValueArgs struct {
	MsRequest *PredictModelServerRequest `thrift:"ms_request,1" json:"ms_request"`
}

func NewGetPredictValueArgs() *GetPredictValueArgs {
	return &GetPredictValueArgs{}
}

func (p *GetPredictValueArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPredictValueArgs) readField1(iprot thrift.TProtocol) error {
	p.MsRequest = NewPredictModelServerRequest()
	if err := p.MsRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MsRequest)
	}
	return nil
}

func (p *GetPredictValueArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPredictValue_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPredictValueArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.MsRequest != nil {
		if err := oprot.WriteFieldBegin("ms_request", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ms_request: %s", p, err)
		}
		if err := p.MsRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MsRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ms_request: %s", p, err)
		}
	}
	return err
}

func (p *GetPredictValueArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPredictValueArgs(%+v)", *p)
}

type GetPredictValueResult struct {
	Success *PredictModelServerResponse `thrift:"success,0" json:"success"`
}

func NewGetPredictValueResult() *GetPredictValueResult {
	return &GetPredictValueResult{}
}

func (p *GetPredictValueResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPredictValueResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewPredictModelServerResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPredictValueResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPredictValue_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPredictValueResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPredictValueResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPredictValueResult(%+v)", *p)
}
