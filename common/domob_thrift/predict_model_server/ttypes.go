// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package predict_model_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = rtb_types.GoUnusedProtection__
var _ = rtb_adinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

type PredictAdInfo struct {
	AdIndex    int32 `thrift:"ad_index,1" json:"ad_index"`
	SponsorId  int32 `thrift:"sponsor_id,2" json:"sponsor_id"`
	ProductId  int32 `thrift:"product_id,3" json:"product_id"`
	CampaignId int32 `thrift:"campaign_id,4" json:"campaign_id"`
	CreativeId int32 `thrift:"creative_id,5" json:"creative_id"`
	StrategyId int32 `thrift:"strategy_id,6" json:"strategy_id"`
	TemplateId int32 `thrift:"template_id,7" json:"template_id"`
	CostType   int32 `thrift:"cost_type,8" json:"cost_type"`
	Bid        int64 `thrift:"bid,9" json:"bid"`
	BidType    int32 `thrift:"bid_type,10" json:"bid_type"`
}

func NewPredictAdInfo() *PredictAdInfo {
	return &PredictAdInfo{}
}

func (p *PredictAdInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictAdInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdIndex = v
	}
	return nil
}

func (p *PredictAdInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *PredictAdInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *PredictAdInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *PredictAdInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *PredictAdInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *PredictAdInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *PredictAdInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *PredictAdInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Bid = v
	}
	return nil
}

func (p *PredictAdInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.BidType = v
	}
	return nil
}

func (p *PredictAdInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictAdInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictAdInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_index", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ad_index: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdIndex)); err != nil {
		return fmt.Errorf("%T.ad_index (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ad_index: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsor_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:product_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductId)); err != nil {
		return fmt.Errorf("%T.product_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:product_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:campaign_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:creative_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:strategy_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:template_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:template_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:cost_type: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:bid: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid_type", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:bid_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BidType)); err != nil {
		return fmt.Errorf("%T.bid_type (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:bid_type: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictAdInfo(%+v)", *p)
}

type PredictResponseItem struct {
	AdIndex int32 `thrift:"ad_index,1" json:"ad_index"`
	Cid     int64 `thrift:"cid,2" json:"cid"`
	Ctr     int64 `thrift:"ctr,3" json:"ctr"`
	Cvr     int64 `thrift:"cvr,4" json:"cvr"`
	DeepCvr int64 `thrift:"deep_cvr,5" json:"deep_cvr"`
}

func NewPredictResponseItem() *PredictResponseItem {
	return &PredictResponseItem{}
}

func (p *PredictResponseItem) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictResponseItem) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdIndex = v
	}
	return nil
}

func (p *PredictResponseItem) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *PredictResponseItem) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *PredictResponseItem) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cvr = v
	}
	return nil
}

func (p *PredictResponseItem) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DeepCvr = v
	}
	return nil
}

func (p *PredictResponseItem) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictResponseItem"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictResponseItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_index", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ad_index: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdIndex)); err != nil {
		return fmt.Errorf("%T.ad_index (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ad_index: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cid: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ctr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ctr: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cvr", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cvr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cvr)); err != nil {
		return fmt.Errorf("%T.cvr (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cvr: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deep_cvr", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:deep_cvr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DeepCvr)); err != nil {
		return fmt.Errorf("%T.deep_cvr (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:deep_cvr: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictResponseItem(%+v)", *p)
}

type PredictModelServerRequest struct {
	ReqId         string                   `thrift:"req_id,1" json:"req_id"`
	ReqTs         int32                    `thrift:"req_ts,2" json:"req_ts"`
	ExchangeId    int32                    `thrift:"exchange_id,3" json:"exchange_id"`
	SearchId      int64                    `thrift:"search_id,4" json:"search_id"`
	User          *rtb_types.RTBUserInfo   `thrift:"user,5" json:"user"`
	Device        *rtb_types.RTBDeviceInfo `thrift:"device,6" json:"device"`
	App           *rtb_types.RTBAppInfo    `thrift:"app,7" json:"app"`
	AdList        []*PredictAdInfo         `thrift:"ad_list,8" json:"ad_list"`
	AdxExchangeId int32                    `thrift:"adx_exchange_id,9" json:"adx_exchange_id"`
	ModelName     string                   `thrift:"model_name,10" json:"model_name"`
}

func NewPredictModelServerRequest() *PredictModelServerRequest {
	return &PredictModelServerRequest{}
}

func (p *PredictModelServerRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ReqId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ReqTs = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField5(iprot thrift.TProtocol) error {
	p.User = rtb_types.NewRTBUserInfo()
	if err := p.User.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.User)
	}
	return nil
}

func (p *PredictModelServerRequest) readField6(iprot thrift.TProtocol) error {
	p.Device = rtb_types.NewRTBDeviceInfo()
	if err := p.Device.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Device)
	}
	return nil
}

func (p *PredictModelServerRequest) readField7(iprot thrift.TProtocol) error {
	p.App = rtb_types.NewRTBAppInfo()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *PredictModelServerRequest) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdList = make([]*PredictAdInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewPredictAdInfo()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.AdList = append(p.AdList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PredictModelServerRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AdxExchangeId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ModelName = v
	}
	return nil
}

func (p *PredictModelServerRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictModelServerRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:req_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReqId)); err != nil {
		return fmt.Errorf("%T.req_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:req_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_ts", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:req_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTs)); err != nil {
		return fmt.Errorf("%T.req_ts (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:req_ts: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exchange_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:search_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.User != nil {
		if err := oprot.WriteFieldBegin("user", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:user: %s", p, err)
		}
		if err := p.User.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.User)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:user: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Device != nil {
		if err := oprot.WriteFieldBegin("device", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:device: %s", p, err)
		}
		if err := p.Device.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Device)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:device: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:app: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if p.AdList != nil {
		if err := oprot.WriteFieldBegin("ad_list", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:ad_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:ad_list: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adx_exchange_id", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:adx_exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdxExchangeId)); err != nil {
		return fmt.Errorf("%T.adx_exchange_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:adx_exchange_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model_name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:model_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ModelName)); err != nil {
		return fmt.Errorf("%T.model_name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:model_name: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictModelServerRequest(%+v)", *p)
}

type PredictModelServerResponse struct {
	Status       int32                  `thrift:"status,1" json:"status"`
	SearchId     int64                  `thrift:"search_id,2" json:"search_id"`
	PredictTs    int64                  `thrift:"predict_ts,3" json:"predict_ts"`
	ResponseList []*PredictResponseItem `thrift:"response_list,4" json:"response_list"`
}

func NewPredictModelServerResponse() *PredictModelServerResponse {
	return &PredictModelServerResponse{}
}

func (p *PredictModelServerResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *PredictModelServerResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *PredictModelServerResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PredictTs = v
	}
	return nil
}

func (p *PredictModelServerResponse) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResponseList = make([]*PredictResponseItem, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewPredictResponseItem()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ResponseList = append(p.ResponseList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PredictModelServerResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictModelServerResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *PredictModelServerResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("predict_ts", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:predict_ts: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PredictTs)); err != nil {
		return fmt.Errorf("%T.predict_ts (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:predict_ts: %s", p, err)
	}
	return err
}

func (p *PredictModelServerResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ResponseList != nil {
		if err := oprot.WriteFieldBegin("response_list", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:response_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResponseList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResponseList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:response_list: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictModelServerResponse(%+v)", *p)
}
