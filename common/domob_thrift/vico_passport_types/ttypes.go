// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_passport_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//账户角色
type VicoAccountRole int64

const (
	VicoAccountRole_VAR_UNKNOWN VicoAccountRole = 0
	VicoAccountRole_VAR_ADMIN   VicoAccountRole = 1
)

func (p VicoAccountRole) String() string {
	switch p {
	case VicoAccountRole_VAR_UNKNOWN:
		return "VicoAccountRole_VAR_UNKNOWN"
	case VicoAccountRole_VAR_ADMIN:
		return "VicoAccountRole_VAR_ADMIN"
	}
	return "<UNSET>"
}

func VicoAccountRoleFromString(s string) (VicoAccountRole, error) {
	switch s {
	case "VicoAccountRole_VAR_UNKNOWN":
		return VicoAccountRole_VAR_UNKNOWN, nil
	case "VicoAccountRole_VAR_ADMIN":
		return VicoAccountRole_VAR_ADMIN, nil
	}
	return VicoAccountRole(math.MinInt32 - 1), fmt.Errorf("not a valid VicoAccountRole string")
}

//账户状态
type VicoAccountStatus int64

const (
	VicoAccountStatus_VAS_RUNNING VicoAccountStatus = 0
	VicoAccountStatus_VAS_BANNED  VicoAccountStatus = 1
	VicoAccountStatus_VAS_DELETED VicoAccountStatus = 2
)

func (p VicoAccountStatus) String() string {
	switch p {
	case VicoAccountStatus_VAS_RUNNING:
		return "VicoAccountStatus_VAS_RUNNING"
	case VicoAccountStatus_VAS_BANNED:
		return "VicoAccountStatus_VAS_BANNED"
	case VicoAccountStatus_VAS_DELETED:
		return "VicoAccountStatus_VAS_DELETED"
	}
	return "<UNSET>"
}

func VicoAccountStatusFromString(s string) (VicoAccountStatus, error) {
	switch s {
	case "VicoAccountStatus_VAS_RUNNING":
		return VicoAccountStatus_VAS_RUNNING, nil
	case "VicoAccountStatus_VAS_BANNED":
		return VicoAccountStatus_VAS_BANNED, nil
	case "VicoAccountStatus_VAS_DELETED":
		return VicoAccountStatus_VAS_DELETED, nil
	}
	return VicoAccountStatus(math.MinInt32 - 1), fmt.Errorf("not a valid VicoAccountStatus string")
}

//账户审核状态
type VicoAccountAuditStatus int64

const (
	VicoAccountAuditStatus_VAAS_UNKNOWN  VicoAccountAuditStatus = 0
	VicoAccountAuditStatus_VAAS_UNSUBMIT VicoAccountAuditStatus = 1
	VicoAccountAuditStatus_VAAS_INAUDIT  VicoAccountAuditStatus = 2
	VicoAccountAuditStatus_VAAS_REJECTED VicoAccountAuditStatus = 3
	VicoAccountAuditStatus_VAAS_PASSED   VicoAccountAuditStatus = 4
)

func (p VicoAccountAuditStatus) String() string {
	switch p {
	case VicoAccountAuditStatus_VAAS_UNKNOWN:
		return "VicoAccountAuditStatus_VAAS_UNKNOWN"
	case VicoAccountAuditStatus_VAAS_UNSUBMIT:
		return "VicoAccountAuditStatus_VAAS_UNSUBMIT"
	case VicoAccountAuditStatus_VAAS_INAUDIT:
		return "VicoAccountAuditStatus_VAAS_INAUDIT"
	case VicoAccountAuditStatus_VAAS_REJECTED:
		return "VicoAccountAuditStatus_VAAS_REJECTED"
	case VicoAccountAuditStatus_VAAS_PASSED:
		return "VicoAccountAuditStatus_VAAS_PASSED"
	}
	return "<UNSET>"
}

func VicoAccountAuditStatusFromString(s string) (VicoAccountAuditStatus, error) {
	switch s {
	case "VicoAccountAuditStatus_VAAS_UNKNOWN":
		return VicoAccountAuditStatus_VAAS_UNKNOWN, nil
	case "VicoAccountAuditStatus_VAAS_UNSUBMIT":
		return VicoAccountAuditStatus_VAAS_UNSUBMIT, nil
	case "VicoAccountAuditStatus_VAAS_INAUDIT":
		return VicoAccountAuditStatus_VAAS_INAUDIT, nil
	case "VicoAccountAuditStatus_VAAS_REJECTED":
		return VicoAccountAuditStatus_VAAS_REJECTED, nil
	case "VicoAccountAuditStatus_VAAS_PASSED":
		return VicoAccountAuditStatus_VAAS_PASSED, nil
	}
	return VicoAccountAuditStatus(math.MinInt32 - 1), fmt.Errorf("not a valid VicoAccountAuditStatus string")
}

//授权第三方账户状态
type VicoAuthorizationAccountStatus int64

const (
	VicoAuthorizationAccountStatus_VAAS_RUNNING VicoAuthorizationAccountStatus = 0
	VicoAuthorizationAccountStatus_VAAS_INVALID VicoAuthorizationAccountStatus = 1
)

func (p VicoAuthorizationAccountStatus) String() string {
	switch p {
	case VicoAuthorizationAccountStatus_VAAS_RUNNING:
		return "VicoAuthorizationAccountStatus_VAAS_RUNNING"
	case VicoAuthorizationAccountStatus_VAAS_INVALID:
		return "VicoAuthorizationAccountStatus_VAAS_INVALID"
	}
	return "<UNSET>"
}

func VicoAuthorizationAccountStatusFromString(s string) (VicoAuthorizationAccountStatus, error) {
	switch s {
	case "VicoAuthorizationAccountStatus_VAAS_RUNNING":
		return VicoAuthorizationAccountStatus_VAAS_RUNNING, nil
	case "VicoAuthorizationAccountStatus_VAAS_INVALID":
		return VicoAuthorizationAccountStatus_VAAS_INVALID, nil
	}
	return VicoAuthorizationAccountStatus(math.MinInt32 - 1), fmt.Errorf("not a valid VicoAuthorizationAccountStatus string")
}

type UserRegisterInfo struct {
	Mobile          int64  `thrift:"mobile,1" json:"mobile"`
	ValidateCode    string `thrift:"validateCode,2" json:"validateCode"`
	Password        string `thrift:"password,3" json:"password"`
	ConfirmPassword string `thrift:"confirmPassword,4" json:"confirmPassword"`
	Source          string `thrift:"source,5" json:"source"`
}

func NewUserRegisterInfo() *UserRegisterInfo {
	return &UserRegisterInfo{}
}

func (p *UserRegisterInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserRegisterInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *UserRegisterInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ValidateCode = v
	}
	return nil
}

func (p *UserRegisterInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *UserRegisterInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ConfirmPassword = v
	}
	return nil
}

func (p *UserRegisterInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *UserRegisterInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserRegisterInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserRegisterInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mobile: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("validateCode", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:validateCode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ValidateCode)); err != nil {
		return fmt.Errorf("%T.validateCode (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:validateCode: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:password: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confirmPassword", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:confirmPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ConfirmPassword)); err != nil {
		return fmt.Errorf("%T.confirmPassword (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:confirmPassword: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:source: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserRegisterInfo(%+v)", *p)
}

type VicoAccountSetting struct {
	AccountId int64 `thrift:"accountId,1" json:"accountId"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Domain        string `thrift:"domain,10" json:"domain"`
	ShopifyToken  string `thrift:"shopifyToken,11" json:"shopifyToken"`
	ShopifyAppKey string `thrift:"shopifyAppKey,12" json:"shopifyAppKey"`
	ShopifyPwd    string `thrift:"shopifyPwd,13" json:"shopifyPwd"`
	UseShopifyBtn bool   `thrift:"useShopifyBtn,14" json:"useShopifyBtn"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	AmazonUnionId  string `thrift:"amazonUnionId,20" json:"amazonUnionId"`
	AwsSecretKey   string `thrift:"awsSecretKey,21" json:"awsSecretKey"`
	AwsAccessKeyId string `thrift:"awsAccessKeyId,22" json:"awsAccessKeyId"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewVicoAccountSetting() *VicoAccountSetting {
	return &VicoAccountSetting{}
}

func (p *VicoAccountSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoAccountSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *VicoAccountSetting) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Domain = v
	}
	return nil
}

func (p *VicoAccountSetting) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ShopifyToken = v
	}
	return nil
}

func (p *VicoAccountSetting) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ShopifyAppKey = v
	}
	return nil
}

func (p *VicoAccountSetting) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ShopifyPwd = v
	}
	return nil
}

func (p *VicoAccountSetting) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.UseShopifyBtn = v
	}
	return nil
}

func (p *VicoAccountSetting) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.AmazonUnionId = v
	}
	return nil
}

func (p *VicoAccountSetting) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.AwsSecretKey = v
	}
	return nil
}

func (p *VicoAccountSetting) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AwsAccessKeyId = v
	}
	return nil
}

func (p *VicoAccountSetting) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *VicoAccountSetting) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *VicoAccountSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoAccountSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoAccountSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:accountId: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("domain", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:domain: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Domain)); err != nil {
		return fmt.Errorf("%T.domain (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:domain: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shopifyToken", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:shopifyToken: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShopifyToken)); err != nil {
		return fmt.Errorf("%T.shopifyToken (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:shopifyToken: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shopifyAppKey", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:shopifyAppKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShopifyAppKey)); err != nil {
		return fmt.Errorf("%T.shopifyAppKey (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:shopifyAppKey: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shopifyPwd", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:shopifyPwd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShopifyPwd)); err != nil {
		return fmt.Errorf("%T.shopifyPwd (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:shopifyPwd: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useShopifyBtn", thrift.BOOL, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:useShopifyBtn: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UseShopifyBtn)); err != nil {
		return fmt.Errorf("%T.useShopifyBtn (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:useShopifyBtn: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amazonUnionId", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:amazonUnionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AmazonUnionId)); err != nil {
		return fmt.Errorf("%T.amazonUnionId (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:amazonUnionId: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("awsSecretKey", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:awsSecretKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AwsSecretKey)); err != nil {
		return fmt.Errorf("%T.awsSecretKey (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:awsSecretKey: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("awsAccessKeyId", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:awsAccessKeyId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AwsAccessKeyId)); err != nil {
		return fmt.Errorf("%T.awsAccessKeyId (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:awsAccessKeyId: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *VicoAccountSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoAccountSetting(%+v)", *p)
}

type VicoAccountFacebookPage struct {
	Id        int64                          `thrift:"id,1" json:"id"`
	AccountId int64                          `thrift:"accountId,2" json:"accountId"`
	PageId    string                         `thrift:"pageId,3" json:"pageId"`
	Name      string                         `thrift:"name,4" json:"name"`
	Token     string                         `thrift:"token,5" json:"token"`
	IsDefault bool                           `thrift:"isDefault,6" json:"isDefault"`
	Status    VicoAuthorizationAccountStatus `thrift:"status,7" json:"status"`
	UserToken string                         `thrift:"userToken,8" json:"userToken"`
	// unused field # 9
	CreateTime int64 `thrift:"createTime,10" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,11" json:"lastUpdate"`
}

func NewVicoAccountFacebookPage() *VicoAccountFacebookPage {
	return &VicoAccountFacebookPage{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *VicoAccountFacebookPage) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *VicoAccountFacebookPage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PageId = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Token = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IsDefault = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = VicoAuthorizationAccountStatus(v)
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.UserToken = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *VicoAccountFacebookPage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoAccountFacebookPage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoAccountFacebookPage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pageId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pageId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PageId)); err != nil {
		return fmt.Errorf("%T.pageId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pageId: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("token", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Token)); err != nil {
		return fmt.Errorf("%T.token (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:token: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isDefault", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:isDefault: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsDefault)); err != nil {
		return fmt.Errorf("%T.isDefault (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:isDefault: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:status: %s", p, err)
		}
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userToken", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:userToken: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserToken)); err != nil {
		return fmt.Errorf("%T.userToken (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:userToken: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastUpdate: %s", p, err)
	}
	return err
}

func (p *VicoAccountFacebookPage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoAccountFacebookPage(%+v)", *p)
}

type VicoAccount struct {
	Id     int64           `thrift:"id,1" json:"id"`
	Mobile int64           `thrift:"mobile,2" json:"mobile"`
	Email  string          `thrift:"email,3" json:"email"`
	Role   VicoAccountRole `thrift:"role,4" json:"role"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Logo      string `thrift:"logo,10" json:"logo"`
	NickName  string `thrift:"nickName,11" json:"nickName"`
	SiteUrl   string `thrift:"siteUrl,12" json:"siteUrl"`
	FbPageUrl string `thrift:"fbPageUrl,13" json:"fbPageUrl"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	RealName    string                     `thrift:"realName,20" json:"realName"`
	Description string                     `thrift:"description,21" json:"description"`
	AuditStatus VicoAccountAuditStatus     `thrift:"auditStatus,22" json:"auditStatus"`
	Status      VicoAccountStatus          `thrift:"status,23" json:"status"`
	Setting     *VicoAccountSetting        `thrift:"setting,24" json:"setting"`
	Channel     string                     `thrift:"channel,25" json:"channel"`
	FbPages     []*VicoAccountFacebookPage `thrift:"fbPages,26" json:"fbPages"`
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewVicoAccount() *VicoAccount {
	return &VicoAccount{
		Role: math.MinInt32 - 1, // unset sentinal value

		AuditStatus: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *VicoAccount) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *VicoAccount) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *VicoAccount) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *VicoAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.LIST {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VicoAccount) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *VicoAccount) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *VicoAccount) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Role = VicoAccountRole(v)
	}
	return nil
}

func (p *VicoAccount) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *VicoAccount) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.NickName = v
	}
	return nil
}

func (p *VicoAccount) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SiteUrl = v
	}
	return nil
}

func (p *VicoAccount) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.FbPageUrl = v
	}
	return nil
}

func (p *VicoAccount) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.RealName = v
	}
	return nil
}

func (p *VicoAccount) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *VicoAccount) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AuditStatus = VicoAccountAuditStatus(v)
	}
	return nil
}

func (p *VicoAccount) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Status = VicoAccountStatus(v)
	}
	return nil
}

func (p *VicoAccount) readField24(iprot thrift.TProtocol) error {
	p.Setting = NewVicoAccountSetting()
	if err := p.Setting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Setting)
	}
	return nil
}

func (p *VicoAccount) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *VicoAccount) readField26(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.FbPages = make([]*VicoAccountFacebookPage, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewVicoAccountFacebookPage()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.FbPages = append(p.FbPages, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VicoAccount) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *VicoAccount) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *VicoAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mobile: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:email: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err := oprot.WriteFieldBegin("role", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:role: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Role)); err != nil {
			return fmt.Errorf("%T.role (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:role: %s", p, err)
		}
	}
	return err
}

func (p *VicoAccount) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:logo: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickName", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:nickName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NickName)); err != nil {
		return fmt.Errorf("%T.nickName (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:nickName: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("siteUrl", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:siteUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SiteUrl)); err != nil {
		return fmt.Errorf("%T.siteUrl (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:siteUrl: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fbPageUrl", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:fbPageUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FbPageUrl)); err != nil {
		return fmt.Errorf("%T.fbPageUrl (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:fbPageUrl: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("realName", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:realName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RealName)); err != nil {
		return fmt.Errorf("%T.realName (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:realName: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:description: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *VicoAccount) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (23) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:status: %s", p, err)
		}
	}
	return err
}

func (p *VicoAccount) writeField24(oprot thrift.TProtocol) (err error) {
	if p.Setting != nil {
		if err := oprot.WriteFieldBegin("setting", thrift.STRUCT, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:setting: %s", p, err)
		}
		if err := p.Setting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Setting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:setting: %s", p, err)
		}
	}
	return err
}

func (p *VicoAccount) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:channel: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField26(oprot thrift.TProtocol) (err error) {
	if p.FbPages != nil {
		if err := oprot.WriteFieldBegin("fbPages", thrift.LIST, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:fbPages: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FbPages)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.FbPages {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:fbPages: %s", p, err)
		}
	}
	return err
}

func (p *VicoAccount) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *VicoAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoAccount(%+v)", *p)
}
