// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dm303

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//Common status reporting mechanism across all services
type DmStatus int64

const (
	DmStatus_DEAD     DmStatus = 0
	DmStatus_STARTING DmStatus = 1
	DmStatus_ALIVE    DmStatus = 2
	DmStatus_STOPPING DmStatus = 3
	DmStatus_STOPPED  DmStatus = 4
	DmStatus_WARNING  DmStatus = 5
)

func (p DmStatus) String() string {
	switch p {
	case DmStatus_DEAD:
		return "DmStatus_DEAD"
	case DmStatus_STARTING:
		return "DmStatus_STARTING"
	case DmStatus_ALIVE:
		return "DmStatus_ALIVE"
	case DmStatus_STOPPING:
		return "DmStatus_STOPPING"
	case DmStatus_STOPPED:
		return "DmStatus_STOPPED"
	case DmStatus_WARNING:
		return "DmStatus_WARNING"
	}
	return "<UNSET>"
}

func DmStatusFromString(s string) (DmStatus, error) {
	switch s {
	case "DmStatus_DEAD":
		return DmStatus_DEAD, nil
	case "DmStatus_STARTING":
		return DmStatus_STARTING, nil
	case "DmStatus_ALIVE":
		return DmStatus_ALIVE, nil
	case "DmStatus_STOPPING":
		return DmStatus_STOPPING, nil
	case "DmStatus_STOPPED":
		return DmStatus_STOPPED, nil
	case "DmStatus_WARNING":
		return DmStatus_WARNING, nil
	}
	return DmStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DmStatus string")
}
