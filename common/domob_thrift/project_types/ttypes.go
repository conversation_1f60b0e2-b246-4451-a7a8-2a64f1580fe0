// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package project_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//业务线类型标示
type ProjectProductType int64

const (
	ProjectProductType_PRODUCT_OW_IOS ProjectProductType = 1
	ProjectProductType_PRODUCT_ADN    ProjectProductType = 2
	ProjectProductType_PRODUCT_RTB    ProjectProductType = 3
)

func (p ProjectProductType) String() string {
	switch p {
	case ProjectProductType_PRODUCT_OW_IOS:
		return "ProjectProductType_PRODUCT_OW_IOS"
	case ProjectProductType_PRODUCT_ADN:
		return "ProjectProductType_PRODUCT_ADN"
	case ProjectProductType_PRODUCT_RTB:
		return "ProjectProductType_PRODUCT_RTB"
	}
	return "<UNSET>"
}

func ProjectProductTypeFromString(s string) (ProjectProductType, error) {
	switch s {
	case "ProjectProductType_PRODUCT_OW_IOS":
		return ProjectProductType_PRODUCT_OW_IOS, nil
	case "ProjectProductType_PRODUCT_ADN":
		return ProjectProductType_PRODUCT_ADN, nil
	case "ProjectProductType_PRODUCT_RTB":
		return ProjectProductType_PRODUCT_RTB, nil
	}
	return ProjectProductType(math.MinInt32 - 1), fmt.Errorf("not a valid ProjectProductType string")
}

type UidInt common.UidInt

type IdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type QueryResult *common.QueryResult

type Schedule struct {
	SdId       IdInt   `thrift:"sd_id,1" json:"sd_id"`
	SdName     string  `thrift:"sd_name,2" json:"sd_name"`
	Platform   int32   `thrift:"platform,3" json:"platform"`
	Price      Amount  `thrift:"price,4" json:"price"`
	Starttime  TimeInt `thrift:"starttime,5" json:"starttime"`
	Endtime    TimeInt `thrift:"endtime,6" json:"endtime"`
	Budget     Amount  `thrift:"budget,7" json:"budget"`
	CostType   int32   `thrift:"cost_type,8" json:"cost_type"`
	Status     int32   `thrift:"status,9" json:"status"`
	PjId       IdInt   `thrift:"pj_id,10" json:"pj_id"`
	Uid        int32   `thrift:"uid,11" json:"uid"`
	AmId       int32   `thrift:"am_id,12" json:"am_id"`
	Line       int32   `thrift:"line,13" json:"line"`
	Currency   int32   `thrift:"currency,14" json:"currency"`
	OurCompany int32   `thrift:"our_company,15" json:"our_company"`
}

func NewSchedule() *Schedule {
	return &Schedule{}
}

func (p *Schedule) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Schedule) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SdId = IdInt(v)
	}
	return nil
}

func (p *Schedule) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SdName = v
	}
	return nil
}

func (p *Schedule) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *Schedule) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *Schedule) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Starttime = TimeInt(v)
	}
	return nil
}

func (p *Schedule) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Endtime = TimeInt(v)
	}
	return nil
}

func (p *Schedule) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Budget = Amount(v)
	}
	return nil
}

func (p *Schedule) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *Schedule) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Schedule) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PjId = IdInt(v)
	}
	return nil
}

func (p *Schedule) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Schedule) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AmId = v
	}
	return nil
}

func (p *Schedule) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Line = v
	}
	return nil
}

func (p *Schedule) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Currency = v
	}
	return nil
}

func (p *Schedule) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.OurCompany = v
	}
	return nil
}

func (p *Schedule) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Schedule"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Schedule) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sd_id: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sd_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdName)); err != nil {
		return fmt.Errorf("%T.sd_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sd_name: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:platform: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:price: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("starttime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:starttime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Starttime)); err != nil {
		return fmt.Errorf("%T.starttime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:starttime: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endtime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Endtime)); err != nil {
		return fmt.Errorf("%T.endtime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endtime: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:budget: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:cost_type: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:status: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:pj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PjId)); err != nil {
		return fmt.Errorf("%T.pj_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:pj_id: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:uid: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("am_id", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:am_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AmId)); err != nil {
		return fmt.Errorf("%T.am_id (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:am_id: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("line", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:line: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Line)); err != nil {
		return fmt.Errorf("%T.line (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:line: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currency", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:currency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Currency)); err != nil {
		return fmt.Errorf("%T.currency (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:currency: %s", p, err)
	}
	return err
}

func (p *Schedule) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("our_company", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:our_company: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OurCompany)); err != nil {
		return fmt.Errorf("%T.our_company (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:our_company: %s", p, err)
	}
	return err
}

func (p *Schedule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Schedule(%+v)", *p)
}

type Project struct {
	PjId            IdInt  `thrift:"pj_id,1" json:"pj_id"`
	CmId            IdInt  `thrift:"cm_id,2" json:"cm_id"`
	PjName          string `thrift:"pj_name,3" json:"pj_name"`
	ProductName     string `thrift:"product_name,4" json:"product_name"`
	SaleDep         IdInt  `thrift:"sale_dep,5" json:"sale_dep"`
	SaleNameBrand   IdInt  `thrift:"sale_name_brand,6" json:"sale_name_brand"`
	SaleNameChannel IdInt  `thrift:"sale_name_channel,7" json:"sale_name_channel"`
	AmId            IdInt  `thrift:"am_id,8" json:"am_id"`
	Status          IdInt  `thrift:"status,9" json:"status"`
	Createtime      IdInt  `thrift:"createtime,10" json:"createtime"`
	Lastupdate      IdInt  `thrift:"lastupdate,11" json:"lastupdate"`
	Note            string `thrift:"note,12" json:"note"`
}

func NewProject() *Project {
	return &Project{}
}

func (p *Project) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Project) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PjId = IdInt(v)
	}
	return nil
}

func (p *Project) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CmId = IdInt(v)
	}
	return nil
}

func (p *Project) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PjName = v
	}
	return nil
}

func (p *Project) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ProductName = v
	}
	return nil
}

func (p *Project) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SaleDep = IdInt(v)
	}
	return nil
}

func (p *Project) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SaleNameBrand = IdInt(v)
	}
	return nil
}

func (p *Project) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SaleNameChannel = IdInt(v)
	}
	return nil
}

func (p *Project) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AmId = IdInt(v)
	}
	return nil
}

func (p *Project) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Status = IdInt(v)
	}
	return nil
}

func (p *Project) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Createtime = IdInt(v)
	}
	return nil
}

func (p *Project) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Lastupdate = IdInt(v)
	}
	return nil
}

func (p *Project) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *Project) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Project"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Project) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PjId)); err != nil {
		return fmt.Errorf("%T.pj_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pj_id: %s", p, err)
	}
	return err
}

func (p *Project) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cm_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cm_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CmId)); err != nil {
		return fmt.Errorf("%T.cm_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cm_id: %s", p, err)
	}
	return err
}

func (p *Project) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pj_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PjName)); err != nil {
		return fmt.Errorf("%T.pj_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pj_name: %s", p, err)
	}
	return err
}

func (p *Project) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:product_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProductName)); err != nil {
		return fmt.Errorf("%T.product_name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:product_name: %s", p, err)
	}
	return err
}

func (p *Project) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sale_dep", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sale_dep: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SaleDep)); err != nil {
		return fmt.Errorf("%T.sale_dep (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sale_dep: %s", p, err)
	}
	return err
}

func (p *Project) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sale_name_brand", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:sale_name_brand: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SaleNameBrand)); err != nil {
		return fmt.Errorf("%T.sale_name_brand (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:sale_name_brand: %s", p, err)
	}
	return err
}

func (p *Project) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sale_name_channel", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:sale_name_channel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SaleNameChannel)); err != nil {
		return fmt.Errorf("%T.sale_name_channel (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:sale_name_channel: %s", p, err)
	}
	return err
}

func (p *Project) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("am_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:am_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AmId)); err != nil {
		return fmt.Errorf("%T.am_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:am_id: %s", p, err)
	}
	return err
}

func (p *Project) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:status: %s", p, err)
	}
	return err
}

func (p *Project) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createtime", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createtime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Createtime)); err != nil {
		return fmt.Errorf("%T.createtime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createtime: %s", p, err)
	}
	return err
}

func (p *Project) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastupdate: %s", p, err)
	}
	return err
}

func (p *Project) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:note: %s", p, err)
	}
	return err
}

func (p *Project) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Project(%+v)", *p)
}

type QueryParam struct {
	AmId      int32  `thrift:"am_id,1" json:"am_id"`
	SdId      IdInt  `thrift:"sd_id,2" json:"sd_id"`
	Uid       int32  `thrift:"uid,3" json:"uid"`
	SdName    string `thrift:"sd_name,4" json:"sd_name"`
	Offset    int32  `thrift:"offset,5" json:"offset"`
	Limit     int32  `thrift:"limit,6" json:"limit"`
	Ascending bool   `thrift:"ascending,7" json:"ascending"`
}

func NewQueryParam() *QueryParam {
	return &QueryParam{}
}

func (p *QueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AmId = v
	}
	return nil
}

func (p *QueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SdId = IdInt(v)
	}
	return nil
}

func (p *QueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *QueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SdName = v
	}
	return nil
}

func (p *QueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *QueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("am_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:am_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AmId)); err != nil {
		return fmt.Errorf("%T.am_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:am_id: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sd_id: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sd_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdName)); err != nil {
		return fmt.Errorf("%T.sd_name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sd_name: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ascending: %s", p, err)
	}
	return err
}

func (p *QueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryParam(%+v)", *p)
}
