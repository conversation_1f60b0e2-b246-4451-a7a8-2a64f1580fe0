// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_passport

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/vico_exception"
	"rtb_model_server/common/domob_thrift/vico_passport_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = vico_passport_types.GoUnusedProtection__
var _ = vico_exception.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type VicoPassportService interface { //vico passport 服务接口定义

	// 生成并发送手机验证码
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Mobile: 手机号 *
	SendMobileVaildateCode(header *common.RequestHeader, mobile int64) (e *vico_exception.VicoException, err error)
	// 注册账户
	// @return 用户uid
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Info: 注册信息 *
	RegisterVicoAccount(header *common.RequestHeader, info *vico_passport_types.UserRegisterInfo) (r int64, e *vico_exception.VicoException, err error)
	// 验证登录手机号和密码是否正确
	// 用于登录
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Mobile: 手机号 *
	//  - Password: 密码 *
	VerifyUserPassword(header *common.RequestHeader, mobile int64, password string) (r bool, e *vico_exception.VicoException, err error)
	// 判断手机号是否可以注册
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Mobile: 手机号 *
	IsUserMobileAvailable(header *common.RequestHeader, mobile int64) (r bool, e *vico_exception.VicoException, err error)
	// 修改用户密码
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户UID *
	//  - OldPassword: 账户原来的旧密码 *
	//  - NewPassword: 要修改的新的密码 *
	ChangeUserPassword(header *common.RequestHeader, accountId int64, oldPassword string, newPassword string) (e *vico_exception.VicoException, err error)
	// 重置用户密码
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Mobile: 手机号 *
	//  - NewPassword: 要修改的新的密码 *
	//  - ValidateCode: 手机验证码 *
	ResetUserPassword(header *common.RequestHeader, mobile int64, newPassword string, validateCode string) (e *vico_exception.VicoException, err error)
	// 完善账户信息
	// 用户注册完之后第一次完善信息使用
	// audit_status = 1的时候可提交
	// 提交之后审核状态置为2
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Info: 账户信息 *
	SubmitAccountAuditInfo(header *common.RequestHeader, info *vico_passport_types.VicoAccount) (e *vico_exception.VicoException, err error)
	// 编辑账户信息
	// 需要用户审核通过之后使用
	// 更新用户信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Info: 账户信息 *
	EditVicoAccount(header *common.RequestHeader, info *vico_passport_types.VicoAccount) (e *vico_exception.VicoException, err error)
	// 批量审核通过账户
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountIds: 账户ID列表 *
	PassVicoAccountByIds(header *common.RequestHeader, accountIds []int64) (e *vico_exception.VicoException, err error)
	// 批量审核拒绝账户
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountIds: 账户ID列表 *
	RejectVicoAccountByIds(header *common.RequestHeader, accountIds []int64) (e *vico_exception.VicoException, err error)
	// 编辑账户设置信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Info: 账户设置信息 *
	EditVicoAccountSetting(header *common.RequestHeader, info *vico_passport_types.VicoAccountSetting) (e *vico_exception.VicoException, err error)
	// 获取账户信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	GetVicoAccountById(header *common.RequestHeader, accountId int64) (r *vico_passport_types.VicoAccount, e *vico_exception.VicoException, err error)
	// 通过手机号获取账户信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Mobile: 手机号 *
	GetVicoAccountByMobile(header *common.RequestHeader, mobile int64) (r *vico_passport_types.VicoAccount, e *vico_exception.VicoException, err error)
	// 添加新的facebook page授权到账户
	// 添加的多个page中如果没有设置默认page, 则取第一个为默认page
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - Info: 关联授权的facebook page信息 *
	AddVicoAccountFacebookPages(header *common.RequestHeader, accountId int64, info []*vico_passport_types.VicoAccountFacebookPage) (e *vico_exception.VicoException, err error)
	// 删除关联的facebook page授权
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - Ids: 对应的内部主键ID *
	DeleteVicoAccountFacebookPages(header *common.RequestHeader, accountId int64, ids []int64) (e *vico_exception.VicoException, err error)
	// 设置默认的facebook page
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - FbPageId: 对应的内部主键ID *
	SetDefaultFacebookPage(header *common.RequestHeader, accountId int64, fbPageId int64) (e *vico_exception.VicoException, err error)
	// 获取账户对应的facebook pages
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	GetFacebookPagesByAccountId(header *common.RequestHeader, accountId int64) (r []*vico_passport_types.VicoAccountFacebookPage, e *vico_exception.VicoException, err error)
}

//vico passport 服务接口定义
type VicoPassportServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewVicoPassportServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *VicoPassportServiceClient {
	return &VicoPassportServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewVicoPassportServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *VicoPassportServiceClient {
	return &VicoPassportServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 生成并发送手机验证码
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Mobile: 手机号 *
func (p *VicoPassportServiceClient) SendMobileVaildateCode(header *common.RequestHeader, mobile int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendSendMobileVaildateCode(header, mobile); err != nil {
		return
	}
	return p.recvSendMobileVaildateCode()
}

func (p *VicoPassportServiceClient) sendSendMobileVaildateCode(header *common.RequestHeader, mobile int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendMobileVaildateCode", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewSendMobileVaildateCodeArgs()
	args0.Header = header
	args0.Mobile = mobile
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvSendMobileVaildateCode() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewSendMobileVaildateCodeResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 注册账户
// @return 用户uid
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Info: 注册信息 *
func (p *VicoPassportServiceClient) RegisterVicoAccount(header *common.RequestHeader, info *vico_passport_types.UserRegisterInfo) (r int64, e *vico_exception.VicoException, err error) {
	if err = p.sendRegisterVicoAccount(header, info); err != nil {
		return
	}
	return p.recvRegisterVicoAccount()
}

func (p *VicoPassportServiceClient) sendRegisterVicoAccount(header *common.RequestHeader, info *vico_passport_types.UserRegisterInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("registerVicoAccount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewRegisterVicoAccountArgs()
	args4.Header = header
	args4.Info = info
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvRegisterVicoAccount() (value int64, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewRegisterVicoAccountResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

// 验证登录手机号和密码是否正确
// 用于登录
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Mobile: 手机号 *
//  - Password: 密码 *
func (p *VicoPassportServiceClient) VerifyUserPassword(header *common.RequestHeader, mobile int64, password string) (r bool, e *vico_exception.VicoException, err error) {
	if err = p.sendVerifyUserPassword(header, mobile, password); err != nil {
		return
	}
	return p.recvVerifyUserPassword()
}

func (p *VicoPassportServiceClient) sendVerifyUserPassword(header *common.RequestHeader, mobile int64, password string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("verifyUserPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewVerifyUserPasswordArgs()
	args8.Header = header
	args8.Mobile = mobile
	args8.Password = password
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvVerifyUserPassword() (value bool, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewVerifyUserPasswordResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 判断手机号是否可以注册
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Mobile: 手机号 *
func (p *VicoPassportServiceClient) IsUserMobileAvailable(header *common.RequestHeader, mobile int64) (r bool, e *vico_exception.VicoException, err error) {
	if err = p.sendIsUserMobileAvailable(header, mobile); err != nil {
		return
	}
	return p.recvIsUserMobileAvailable()
}

func (p *VicoPassportServiceClient) sendIsUserMobileAvailable(header *common.RequestHeader, mobile int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("isUserMobileAvailable", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewIsUserMobileAvailableArgs()
	args12.Header = header
	args12.Mobile = mobile
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvIsUserMobileAvailable() (value bool, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewIsUserMobileAvailableResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.E != nil {
		e = result13.E
	}
	return
}

// 修改用户密码
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户UID *
//  - OldPassword: 账户原来的旧密码 *
//  - NewPassword: 要修改的新的密码 *
func (p *VicoPassportServiceClient) ChangeUserPassword(header *common.RequestHeader, accountId int64, oldPassword string, newPassword string) (e *vico_exception.VicoException, err error) {
	if err = p.sendChangeUserPassword(header, accountId, oldPassword, newPassword); err != nil {
		return
	}
	return p.recvChangeUserPassword()
}

func (p *VicoPassportServiceClient) sendChangeUserPassword(header *common.RequestHeader, accountId int64, oldPassword string, newPassword string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeUserPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewChangeUserPasswordArgs()
	args16.Header = header
	args16.AccountId = accountId
	args16.OldPassword = oldPassword
	args16.NewPassword = newPassword
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvChangeUserPassword() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewChangeUserPasswordResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result17.E != nil {
		e = result17.E
	}
	return
}

// 重置用户密码
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Mobile: 手机号 *
//  - NewPassword: 要修改的新的密码 *
//  - ValidateCode: 手机验证码 *
func (p *VicoPassportServiceClient) ResetUserPassword(header *common.RequestHeader, mobile int64, newPassword string, validateCode string) (e *vico_exception.VicoException, err error) {
	if err = p.sendResetUserPassword(header, mobile, newPassword, validateCode); err != nil {
		return
	}
	return p.recvResetUserPassword()
}

func (p *VicoPassportServiceClient) sendResetUserPassword(header *common.RequestHeader, mobile int64, newPassword string, validateCode string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resetUserPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewResetUserPasswordArgs()
	args20.Header = header
	args20.Mobile = mobile
	args20.NewPassword = newPassword
	args20.ValidateCode = validateCode
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvResetUserPassword() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewResetUserPasswordResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result21.E != nil {
		e = result21.E
	}
	return
}

// 完善账户信息
// 用户注册完之后第一次完善信息使用
// audit_status = 1的时候可提交
// 提交之后审核状态置为2
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Info: 账户信息 *
func (p *VicoPassportServiceClient) SubmitAccountAuditInfo(header *common.RequestHeader, info *vico_passport_types.VicoAccount) (e *vico_exception.VicoException, err error) {
	if err = p.sendSubmitAccountAuditInfo(header, info); err != nil {
		return
	}
	return p.recvSubmitAccountAuditInfo()
}

func (p *VicoPassportServiceClient) sendSubmitAccountAuditInfo(header *common.RequestHeader, info *vico_passport_types.VicoAccount) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitAccountAuditInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewSubmitAccountAuditInfoArgs()
	args24.Header = header
	args24.Info = info
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvSubmitAccountAuditInfo() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewSubmitAccountAuditInfoResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result25.E != nil {
		e = result25.E
	}
	return
}

// 编辑账户信息
// 需要用户审核通过之后使用
// 更新用户信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Info: 账户信息 *
func (p *VicoPassportServiceClient) EditVicoAccount(header *common.RequestHeader, info *vico_passport_types.VicoAccount) (e *vico_exception.VicoException, err error) {
	if err = p.sendEditVicoAccount(header, info); err != nil {
		return
	}
	return p.recvEditVicoAccount()
}

func (p *VicoPassportServiceClient) sendEditVicoAccount(header *common.RequestHeader, info *vico_passport_types.VicoAccount) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editVicoAccount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewEditVicoAccountArgs()
	args28.Header = header
	args28.Info = info
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvEditVicoAccount() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewEditVicoAccountResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result29.E != nil {
		e = result29.E
	}
	return
}

// 批量审核通过账户
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountIds: 账户ID列表 *
func (p *VicoPassportServiceClient) PassVicoAccountByIds(header *common.RequestHeader, accountIds []int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendPassVicoAccountByIds(header, accountIds); err != nil {
		return
	}
	return p.recvPassVicoAccountByIds()
}

func (p *VicoPassportServiceClient) sendPassVicoAccountByIds(header *common.RequestHeader, accountIds []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("passVicoAccountByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewPassVicoAccountByIdsArgs()
	args32.Header = header
	args32.AccountIds = accountIds
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvPassVicoAccountByIds() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewPassVicoAccountByIdsResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result33.E != nil {
		e = result33.E
	}
	return
}

// 批量审核拒绝账户
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountIds: 账户ID列表 *
func (p *VicoPassportServiceClient) RejectVicoAccountByIds(header *common.RequestHeader, accountIds []int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendRejectVicoAccountByIds(header, accountIds); err != nil {
		return
	}
	return p.recvRejectVicoAccountByIds()
}

func (p *VicoPassportServiceClient) sendRejectVicoAccountByIds(header *common.RequestHeader, accountIds []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("rejectVicoAccountByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewRejectVicoAccountByIdsArgs()
	args36.Header = header
	args36.AccountIds = accountIds
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvRejectVicoAccountByIds() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewRejectVicoAccountByIdsResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result37.E != nil {
		e = result37.E
	}
	return
}

// 编辑账户设置信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Info: 账户设置信息 *
func (p *VicoPassportServiceClient) EditVicoAccountSetting(header *common.RequestHeader, info *vico_passport_types.VicoAccountSetting) (e *vico_exception.VicoException, err error) {
	if err = p.sendEditVicoAccountSetting(header, info); err != nil {
		return
	}
	return p.recvEditVicoAccountSetting()
}

func (p *VicoPassportServiceClient) sendEditVicoAccountSetting(header *common.RequestHeader, info *vico_passport_types.VicoAccountSetting) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editVicoAccountSetting", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewEditVicoAccountSettingArgs()
	args40.Header = header
	args40.Info = info
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvEditVicoAccountSetting() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewEditVicoAccountSettingResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result41.E != nil {
		e = result41.E
	}
	return
}

// 获取账户信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
func (p *VicoPassportServiceClient) GetVicoAccountById(header *common.RequestHeader, accountId int64) (r *vico_passport_types.VicoAccount, e *vico_exception.VicoException, err error) {
	if err = p.sendGetVicoAccountById(header, accountId); err != nil {
		return
	}
	return p.recvGetVicoAccountById()
}

func (p *VicoPassportServiceClient) sendGetVicoAccountById(header *common.RequestHeader, accountId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getVicoAccountById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetVicoAccountByIdArgs()
	args44.Header = header
	args44.AccountId = accountId
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvGetVicoAccountById() (value *vico_passport_types.VicoAccount, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetVicoAccountByIdResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.E != nil {
		e = result45.E
	}
	return
}

// 通过手机号获取账户信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Mobile: 手机号 *
func (p *VicoPassportServiceClient) GetVicoAccountByMobile(header *common.RequestHeader, mobile int64) (r *vico_passport_types.VicoAccount, e *vico_exception.VicoException, err error) {
	if err = p.sendGetVicoAccountByMobile(header, mobile); err != nil {
		return
	}
	return p.recvGetVicoAccountByMobile()
}

func (p *VicoPassportServiceClient) sendGetVicoAccountByMobile(header *common.RequestHeader, mobile int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getVicoAccountByMobile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewGetVicoAccountByMobileArgs()
	args48.Header = header
	args48.Mobile = mobile
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvGetVicoAccountByMobile() (value *vico_passport_types.VicoAccount, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewGetVicoAccountByMobileResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.E != nil {
		e = result49.E
	}
	return
}

// 添加新的facebook page授权到账户
// 添加的多个page中如果没有设置默认page, 则取第一个为默认page
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - Info: 关联授权的facebook page信息 *
func (p *VicoPassportServiceClient) AddVicoAccountFacebookPages(header *common.RequestHeader, accountId int64, info []*vico_passport_types.VicoAccountFacebookPage) (e *vico_exception.VicoException, err error) {
	if err = p.sendAddVicoAccountFacebookPages(header, accountId, info); err != nil {
		return
	}
	return p.recvAddVicoAccountFacebookPages()
}

func (p *VicoPassportServiceClient) sendAddVicoAccountFacebookPages(header *common.RequestHeader, accountId int64, info []*vico_passport_types.VicoAccountFacebookPage) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addVicoAccountFacebookPages", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewAddVicoAccountFacebookPagesArgs()
	args52.Header = header
	args52.AccountId = accountId
	args52.Info = info
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvAddVicoAccountFacebookPages() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewAddVicoAccountFacebookPagesResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result53.E != nil {
		e = result53.E
	}
	return
}

// 删除关联的facebook page授权
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - Ids: 对应的内部主键ID *
func (p *VicoPassportServiceClient) DeleteVicoAccountFacebookPages(header *common.RequestHeader, accountId int64, ids []int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendDeleteVicoAccountFacebookPages(header, accountId, ids); err != nil {
		return
	}
	return p.recvDeleteVicoAccountFacebookPages()
}

func (p *VicoPassportServiceClient) sendDeleteVicoAccountFacebookPages(header *common.RequestHeader, accountId int64, ids []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteVicoAccountFacebookPages", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewDeleteVicoAccountFacebookPagesArgs()
	args56.Header = header
	args56.AccountId = accountId
	args56.Ids = ids
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvDeleteVicoAccountFacebookPages() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewDeleteVicoAccountFacebookPagesResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result57.E != nil {
		e = result57.E
	}
	return
}

// 设置默认的facebook page
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - FbPageId: 对应的内部主键ID *
func (p *VicoPassportServiceClient) SetDefaultFacebookPage(header *common.RequestHeader, accountId int64, fbPageId int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendSetDefaultFacebookPage(header, accountId, fbPageId); err != nil {
		return
	}
	return p.recvSetDefaultFacebookPage()
}

func (p *VicoPassportServiceClient) sendSetDefaultFacebookPage(header *common.RequestHeader, accountId int64, fbPageId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("setDefaultFacebookPage", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewSetDefaultFacebookPageArgs()
	args60.Header = header
	args60.AccountId = accountId
	args60.FbPageId = fbPageId
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvSetDefaultFacebookPage() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewSetDefaultFacebookPageResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result61.E != nil {
		e = result61.E
	}
	return
}

// 获取账户对应的facebook pages
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
func (p *VicoPassportServiceClient) GetFacebookPagesByAccountId(header *common.RequestHeader, accountId int64) (r []*vico_passport_types.VicoAccountFacebookPage, e *vico_exception.VicoException, err error) {
	if err = p.sendGetFacebookPagesByAccountId(header, accountId); err != nil {
		return
	}
	return p.recvGetFacebookPagesByAccountId()
}

func (p *VicoPassportServiceClient) sendGetFacebookPagesByAccountId(header *common.RequestHeader, accountId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFacebookPagesByAccountId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewGetFacebookPagesByAccountIdArgs()
	args64.Header = header
	args64.AccountId = accountId
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoPassportServiceClient) recvGetFacebookPagesByAccountId() (value []*vico_passport_types.VicoAccountFacebookPage, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewGetFacebookPagesByAccountIdResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.E != nil {
		e = result65.E
	}
	return
}

type VicoPassportServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      VicoPassportService
}

func (p *VicoPassportServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *VicoPassportServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *VicoPassportServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewVicoPassportServiceProcessor(handler VicoPassportService) *VicoPassportServiceProcessor {

	self68 := &VicoPassportServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self68.processorMap["sendMobileVaildateCode"] = &vicoPassportServiceProcessorSendMobileVaildateCode{handler: handler}
	self68.processorMap["registerVicoAccount"] = &vicoPassportServiceProcessorRegisterVicoAccount{handler: handler}
	self68.processorMap["verifyUserPassword"] = &vicoPassportServiceProcessorVerifyUserPassword{handler: handler}
	self68.processorMap["isUserMobileAvailable"] = &vicoPassportServiceProcessorIsUserMobileAvailable{handler: handler}
	self68.processorMap["changeUserPassword"] = &vicoPassportServiceProcessorChangeUserPassword{handler: handler}
	self68.processorMap["resetUserPassword"] = &vicoPassportServiceProcessorResetUserPassword{handler: handler}
	self68.processorMap["submitAccountAuditInfo"] = &vicoPassportServiceProcessorSubmitAccountAuditInfo{handler: handler}
	self68.processorMap["editVicoAccount"] = &vicoPassportServiceProcessorEditVicoAccount{handler: handler}
	self68.processorMap["passVicoAccountByIds"] = &vicoPassportServiceProcessorPassVicoAccountByIds{handler: handler}
	self68.processorMap["rejectVicoAccountByIds"] = &vicoPassportServiceProcessorRejectVicoAccountByIds{handler: handler}
	self68.processorMap["editVicoAccountSetting"] = &vicoPassportServiceProcessorEditVicoAccountSetting{handler: handler}
	self68.processorMap["getVicoAccountById"] = &vicoPassportServiceProcessorGetVicoAccountById{handler: handler}
	self68.processorMap["getVicoAccountByMobile"] = &vicoPassportServiceProcessorGetVicoAccountByMobile{handler: handler}
	self68.processorMap["addVicoAccountFacebookPages"] = &vicoPassportServiceProcessorAddVicoAccountFacebookPages{handler: handler}
	self68.processorMap["deleteVicoAccountFacebookPages"] = &vicoPassportServiceProcessorDeleteVicoAccountFacebookPages{handler: handler}
	self68.processorMap["setDefaultFacebookPage"] = &vicoPassportServiceProcessorSetDefaultFacebookPage{handler: handler}
	self68.processorMap["getFacebookPagesByAccountId"] = &vicoPassportServiceProcessorGetFacebookPagesByAccountId{handler: handler}
	return self68
}

func (p *VicoPassportServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x69 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x69.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x69

}

type vicoPassportServiceProcessorSendMobileVaildateCode struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorSendMobileVaildateCode) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendMobileVaildateCodeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendMobileVaildateCode", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendMobileVaildateCodeResult()
	if result.E, err = p.handler.SendMobileVaildateCode(args.Header, args.Mobile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendMobileVaildateCode: "+err.Error())
		oprot.WriteMessageBegin("sendMobileVaildateCode", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendMobileVaildateCode", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorRegisterVicoAccount struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorRegisterVicoAccount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRegisterVicoAccountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("registerVicoAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRegisterVicoAccountResult()
	if result.Success, result.E, err = p.handler.RegisterVicoAccount(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing registerVicoAccount: "+err.Error())
		oprot.WriteMessageBegin("registerVicoAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("registerVicoAccount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorVerifyUserPassword struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorVerifyUserPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewVerifyUserPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("verifyUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewVerifyUserPasswordResult()
	if result.Success, result.E, err = p.handler.VerifyUserPassword(args.Header, args.Mobile, args.Password); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing verifyUserPassword: "+err.Error())
		oprot.WriteMessageBegin("verifyUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("verifyUserPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorIsUserMobileAvailable struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorIsUserMobileAvailable) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewIsUserMobileAvailableArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("isUserMobileAvailable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewIsUserMobileAvailableResult()
	if result.Success, result.E, err = p.handler.IsUserMobileAvailable(args.Header, args.Mobile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isUserMobileAvailable: "+err.Error())
		oprot.WriteMessageBegin("isUserMobileAvailable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("isUserMobileAvailable", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorChangeUserPassword struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorChangeUserPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeUserPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeUserPasswordResult()
	if result.E, err = p.handler.ChangeUserPassword(args.Header, args.AccountId, args.OldPassword, args.NewPassword); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeUserPassword: "+err.Error())
		oprot.WriteMessageBegin("changeUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeUserPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorResetUserPassword struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorResetUserPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResetUserPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resetUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResetUserPasswordResult()
	if result.E, err = p.handler.ResetUserPassword(args.Header, args.Mobile, args.NewPassword, args.ValidateCode); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resetUserPassword: "+err.Error())
		oprot.WriteMessageBegin("resetUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resetUserPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorSubmitAccountAuditInfo struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorSubmitAccountAuditInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitAccountAuditInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitAccountAuditInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitAccountAuditInfoResult()
	if result.E, err = p.handler.SubmitAccountAuditInfo(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitAccountAuditInfo: "+err.Error())
		oprot.WriteMessageBegin("submitAccountAuditInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitAccountAuditInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorEditVicoAccount struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorEditVicoAccount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditVicoAccountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editVicoAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditVicoAccountResult()
	if result.E, err = p.handler.EditVicoAccount(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editVicoAccount: "+err.Error())
		oprot.WriteMessageBegin("editVicoAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editVicoAccount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorPassVicoAccountByIds struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorPassVicoAccountByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPassVicoAccountByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("passVicoAccountByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPassVicoAccountByIdsResult()
	if result.E, err = p.handler.PassVicoAccountByIds(args.Header, args.AccountIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing passVicoAccountByIds: "+err.Error())
		oprot.WriteMessageBegin("passVicoAccountByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("passVicoAccountByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorRejectVicoAccountByIds struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorRejectVicoAccountByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRejectVicoAccountByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("rejectVicoAccountByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRejectVicoAccountByIdsResult()
	if result.E, err = p.handler.RejectVicoAccountByIds(args.Header, args.AccountIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing rejectVicoAccountByIds: "+err.Error())
		oprot.WriteMessageBegin("rejectVicoAccountByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("rejectVicoAccountByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorEditVicoAccountSetting struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorEditVicoAccountSetting) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditVicoAccountSettingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editVicoAccountSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditVicoAccountSettingResult()
	if result.E, err = p.handler.EditVicoAccountSetting(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editVicoAccountSetting: "+err.Error())
		oprot.WriteMessageBegin("editVicoAccountSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editVicoAccountSetting", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorGetVicoAccountById struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorGetVicoAccountById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetVicoAccountByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getVicoAccountById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetVicoAccountByIdResult()
	if result.Success, result.E, err = p.handler.GetVicoAccountById(args.Header, args.AccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getVicoAccountById: "+err.Error())
		oprot.WriteMessageBegin("getVicoAccountById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getVicoAccountById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorGetVicoAccountByMobile struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorGetVicoAccountByMobile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetVicoAccountByMobileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getVicoAccountByMobile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetVicoAccountByMobileResult()
	if result.Success, result.E, err = p.handler.GetVicoAccountByMobile(args.Header, args.Mobile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getVicoAccountByMobile: "+err.Error())
		oprot.WriteMessageBegin("getVicoAccountByMobile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getVicoAccountByMobile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorAddVicoAccountFacebookPages struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorAddVicoAccountFacebookPages) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddVicoAccountFacebookPagesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addVicoAccountFacebookPages", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddVicoAccountFacebookPagesResult()
	if result.E, err = p.handler.AddVicoAccountFacebookPages(args.Header, args.AccountId, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addVicoAccountFacebookPages: "+err.Error())
		oprot.WriteMessageBegin("addVicoAccountFacebookPages", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addVicoAccountFacebookPages", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorDeleteVicoAccountFacebookPages struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorDeleteVicoAccountFacebookPages) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteVicoAccountFacebookPagesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteVicoAccountFacebookPages", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteVicoAccountFacebookPagesResult()
	if result.E, err = p.handler.DeleteVicoAccountFacebookPages(args.Header, args.AccountId, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteVicoAccountFacebookPages: "+err.Error())
		oprot.WriteMessageBegin("deleteVicoAccountFacebookPages", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteVicoAccountFacebookPages", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorSetDefaultFacebookPage struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorSetDefaultFacebookPage) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSetDefaultFacebookPageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("setDefaultFacebookPage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSetDefaultFacebookPageResult()
	if result.E, err = p.handler.SetDefaultFacebookPage(args.Header, args.AccountId, args.FbPageId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing setDefaultFacebookPage: "+err.Error())
		oprot.WriteMessageBegin("setDefaultFacebookPage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("setDefaultFacebookPage", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoPassportServiceProcessorGetFacebookPagesByAccountId struct {
	handler VicoPassportService
}

func (p *vicoPassportServiceProcessorGetFacebookPagesByAccountId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFacebookPagesByAccountIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFacebookPagesByAccountId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFacebookPagesByAccountIdResult()
	if result.Success, result.E, err = p.handler.GetFacebookPagesByAccountId(args.Header, args.AccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFacebookPagesByAccountId: "+err.Error())
		oprot.WriteMessageBegin("getFacebookPagesByAccountId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFacebookPagesByAccountId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type SendMobileVaildateCodeArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Mobile int64                 `thrift:"mobile,2" json:"mobile"`
}

func NewSendMobileVaildateCodeArgs() *SendMobileVaildateCodeArgs {
	return &SendMobileVaildateCodeArgs{}
}

func (p *SendMobileVaildateCodeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendMobileVaildateCodeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SendMobileVaildateCodeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *SendMobileVaildateCodeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendMobileVaildateCode_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendMobileVaildateCodeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SendMobileVaildateCodeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mobile: %s", p, err)
	}
	return err
}

func (p *SendMobileVaildateCodeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendMobileVaildateCodeArgs(%+v)", *p)
}

type SendMobileVaildateCodeResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewSendMobileVaildateCodeResult() *SendMobileVaildateCodeResult {
	return &SendMobileVaildateCodeResult{}
}

func (p *SendMobileVaildateCodeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendMobileVaildateCodeResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SendMobileVaildateCodeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendMobileVaildateCode_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendMobileVaildateCodeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SendMobileVaildateCodeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendMobileVaildateCodeResult(%+v)", *p)
}

type RegisterVicoAccountArgs struct {
	Header *common.RequestHeader                 `thrift:"header,1" json:"header"`
	Info   *vico_passport_types.UserRegisterInfo `thrift:"info,2" json:"info"`
}

func NewRegisterVicoAccountArgs() *RegisterVicoAccountArgs {
	return &RegisterVicoAccountArgs{}
}

func (p *RegisterVicoAccountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterVicoAccountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RegisterVicoAccountArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = vico_passport_types.NewUserRegisterInfo()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *RegisterVicoAccountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerVicoAccount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterVicoAccountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RegisterVicoAccountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *RegisterVicoAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterVicoAccountArgs(%+v)", *p)
}

type RegisterVicoAccountResult struct {
	Success int64                         `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewRegisterVicoAccountResult() *RegisterVicoAccountResult {
	return &RegisterVicoAccountResult{}
}

func (p *RegisterVicoAccountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterVicoAccountResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *RegisterVicoAccountResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *RegisterVicoAccountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerVicoAccount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterVicoAccountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *RegisterVicoAccountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *RegisterVicoAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterVicoAccountResult(%+v)", *p)
}

type VerifyUserPasswordArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Mobile   int64                 `thrift:"mobile,2" json:"mobile"`
	Password string                `thrift:"password,3" json:"password"`
}

func NewVerifyUserPasswordArgs() *VerifyUserPasswordArgs {
	return &VerifyUserPasswordArgs{}
}

func (p *VerifyUserPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *VerifyUserPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *VerifyUserPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *VerifyUserPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("verifyUserPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *VerifyUserPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mobile: %s", p, err)
	}
	return err
}

func (p *VerifyUserPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:password: %s", p, err)
	}
	return err
}

func (p *VerifyUserPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VerifyUserPasswordArgs(%+v)", *p)
}

type VerifyUserPasswordResult struct {
	Success bool                          `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewVerifyUserPasswordResult() *VerifyUserPasswordResult {
	return &VerifyUserPasswordResult{}
}

func (p *VerifyUserPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *VerifyUserPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *VerifyUserPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("verifyUserPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *VerifyUserPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *VerifyUserPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VerifyUserPasswordResult(%+v)", *p)
}

type IsUserMobileAvailableArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Mobile int64                 `thrift:"mobile,2" json:"mobile"`
}

func NewIsUserMobileAvailableArgs() *IsUserMobileAvailableArgs {
	return &IsUserMobileAvailableArgs{}
}

func (p *IsUserMobileAvailableArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsUserMobileAvailableArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *IsUserMobileAvailableArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *IsUserMobileAvailableArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isUserMobileAvailable_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsUserMobileAvailableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *IsUserMobileAvailableArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mobile: %s", p, err)
	}
	return err
}

func (p *IsUserMobileAvailableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsUserMobileAvailableArgs(%+v)", *p)
}

type IsUserMobileAvailableResult struct {
	Success bool                          `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewIsUserMobileAvailableResult() *IsUserMobileAvailableResult {
	return &IsUserMobileAvailableResult{}
}

func (p *IsUserMobileAvailableResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsUserMobileAvailableResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *IsUserMobileAvailableResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *IsUserMobileAvailableResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isUserMobileAvailable_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsUserMobileAvailableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *IsUserMobileAvailableResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *IsUserMobileAvailableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsUserMobileAvailableResult(%+v)", *p)
}

type ChangeUserPasswordArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId   int64                 `thrift:"accountId,2" json:"accountId"`
	OldPassword string                `thrift:"oldPassword,3" json:"oldPassword"`
	NewPassword string                `thrift:"newPassword,4" json:"newPassword"`
}

func NewChangeUserPasswordArgs() *ChangeUserPasswordArgs {
	return &ChangeUserPasswordArgs{}
}

func (p *ChangeUserPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OldPassword = v
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.NewPassword = v
	}
	return nil
}

func (p *ChangeUserPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *ChangeUserPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oldPassword", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:oldPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OldPassword)); err != nil {
		return fmt.Errorf("%T.oldPassword (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:oldPassword: %s", p, err)
	}
	return err
}

func (p *ChangeUserPasswordArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newPassword", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:newPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewPassword)); err != nil {
		return fmt.Errorf("%T.newPassword (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:newPassword: %s", p, err)
	}
	return err
}

func (p *ChangeUserPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserPasswordArgs(%+v)", *p)
}

type ChangeUserPasswordResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewChangeUserPasswordResult() *ChangeUserPasswordResult {
	return &ChangeUserPasswordResult{}
}

func (p *ChangeUserPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ChangeUserPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserPasswordResult(%+v)", *p)
}

type ResetUserPasswordArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	Mobile       int64                 `thrift:"mobile,2" json:"mobile"`
	NewPassword  string                `thrift:"newPassword,3" json:"newPassword"`
	ValidateCode string                `thrift:"validateCode,4" json:"validateCode"`
}

func NewResetUserPasswordArgs() *ResetUserPasswordArgs {
	return &ResetUserPasswordArgs{}
}

func (p *ResetUserPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetUserPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ResetUserPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *ResetUserPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NewPassword = v
	}
	return nil
}

func (p *ResetUserPasswordArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ValidateCode = v
	}
	return nil
}

func (p *ResetUserPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetUserPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetUserPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ResetUserPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mobile: %s", p, err)
	}
	return err
}

func (p *ResetUserPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newPassword", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:newPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewPassword)); err != nil {
		return fmt.Errorf("%T.newPassword (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:newPassword: %s", p, err)
	}
	return err
}

func (p *ResetUserPasswordArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("validateCode", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:validateCode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ValidateCode)); err != nil {
		return fmt.Errorf("%T.validateCode (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:validateCode: %s", p, err)
	}
	return err
}

func (p *ResetUserPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetUserPasswordArgs(%+v)", *p)
}

type ResetUserPasswordResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewResetUserPasswordResult() *ResetUserPasswordResult {
	return &ResetUserPasswordResult{}
}

func (p *ResetUserPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetUserPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ResetUserPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetUserPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetUserPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ResetUserPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetUserPasswordResult(%+v)", *p)
}

type SubmitAccountAuditInfoArgs struct {
	Header *common.RequestHeader            `thrift:"header,1" json:"header"`
	Info   *vico_passport_types.VicoAccount `thrift:"info,2" json:"info"`
}

func NewSubmitAccountAuditInfoArgs() *SubmitAccountAuditInfoArgs {
	return &SubmitAccountAuditInfoArgs{}
}

func (p *SubmitAccountAuditInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitAccountAuditInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitAccountAuditInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = vico_passport_types.NewVicoAccount()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *SubmitAccountAuditInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitAccountAuditInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitAccountAuditInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitAccountAuditInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *SubmitAccountAuditInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitAccountAuditInfoArgs(%+v)", *p)
}

type SubmitAccountAuditInfoResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewSubmitAccountAuditInfoResult() *SubmitAccountAuditInfoResult {
	return &SubmitAccountAuditInfoResult{}
}

func (p *SubmitAccountAuditInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitAccountAuditInfoResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SubmitAccountAuditInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitAccountAuditInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitAccountAuditInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SubmitAccountAuditInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitAccountAuditInfoResult(%+v)", *p)
}

type EditVicoAccountArgs struct {
	Header *common.RequestHeader            `thrift:"header,1" json:"header"`
	Info   *vico_passport_types.VicoAccount `thrift:"info,2" json:"info"`
}

func NewEditVicoAccountArgs() *EditVicoAccountArgs {
	return &EditVicoAccountArgs{}
}

func (p *EditVicoAccountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditVicoAccountArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = vico_passport_types.NewVicoAccount()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *EditVicoAccountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editVicoAccount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditVicoAccountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *EditVicoAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditVicoAccountArgs(%+v)", *p)
}

type EditVicoAccountResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewEditVicoAccountResult() *EditVicoAccountResult {
	return &EditVicoAccountResult{}
}

func (p *EditVicoAccountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditVicoAccountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editVicoAccount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditVicoAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditVicoAccountResult(%+v)", *p)
}

type PassVicoAccountByIdsArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountIds []int64               `thrift:"accountIds,2" json:"accountIds"`
}

func NewPassVicoAccountByIdsArgs() *PassVicoAccountByIdsArgs {
	return &PassVicoAccountByIdsArgs{}
}

func (p *PassVicoAccountByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PassVicoAccountByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PassVicoAccountByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccountIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem70 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem70 = v
		}
		p.AccountIds = append(p.AccountIds, _elem70)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PassVicoAccountByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("passVicoAccountByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PassVicoAccountByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PassVicoAccountByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AccountIds != nil {
		if err := oprot.WriteFieldBegin("accountIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:accountIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.AccountIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccountIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:accountIds: %s", p, err)
		}
	}
	return err
}

func (p *PassVicoAccountByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassVicoAccountByIdsArgs(%+v)", *p)
}

type PassVicoAccountByIdsResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewPassVicoAccountByIdsResult() *PassVicoAccountByIdsResult {
	return &PassVicoAccountByIdsResult{}
}

func (p *PassVicoAccountByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PassVicoAccountByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *PassVicoAccountByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("passVicoAccountByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PassVicoAccountByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *PassVicoAccountByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassVicoAccountByIdsResult(%+v)", *p)
}

type RejectVicoAccountByIdsArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountIds []int64               `thrift:"accountIds,2" json:"accountIds"`
}

func NewRejectVicoAccountByIdsArgs() *RejectVicoAccountByIdsArgs {
	return &RejectVicoAccountByIdsArgs{}
}

func (p *RejectVicoAccountByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RejectVicoAccountByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RejectVicoAccountByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccountIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem71 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem71 = v
		}
		p.AccountIds = append(p.AccountIds, _elem71)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RejectVicoAccountByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rejectVicoAccountByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RejectVicoAccountByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RejectVicoAccountByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AccountIds != nil {
		if err := oprot.WriteFieldBegin("accountIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:accountIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.AccountIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccountIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:accountIds: %s", p, err)
		}
	}
	return err
}

func (p *RejectVicoAccountByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RejectVicoAccountByIdsArgs(%+v)", *p)
}

type RejectVicoAccountByIdsResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewRejectVicoAccountByIdsResult() *RejectVicoAccountByIdsResult {
	return &RejectVicoAccountByIdsResult{}
}

func (p *RejectVicoAccountByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RejectVicoAccountByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *RejectVicoAccountByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rejectVicoAccountByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RejectVicoAccountByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *RejectVicoAccountByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RejectVicoAccountByIdsResult(%+v)", *p)
}

type EditVicoAccountSettingArgs struct {
	Header *common.RequestHeader                   `thrift:"header,1" json:"header"`
	Info   *vico_passport_types.VicoAccountSetting `thrift:"info,2" json:"info"`
}

func NewEditVicoAccountSettingArgs() *EditVicoAccountSettingArgs {
	return &EditVicoAccountSettingArgs{}
}

func (p *EditVicoAccountSettingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountSettingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditVicoAccountSettingArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = vico_passport_types.NewVicoAccountSetting()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *EditVicoAccountSettingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editVicoAccountSetting_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountSettingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditVicoAccountSettingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *EditVicoAccountSettingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditVicoAccountSettingArgs(%+v)", *p)
}

type EditVicoAccountSettingResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewEditVicoAccountSettingResult() *EditVicoAccountSettingResult {
	return &EditVicoAccountSettingResult{}
}

func (p *EditVicoAccountSettingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountSettingResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditVicoAccountSettingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editVicoAccountSetting_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditVicoAccountSettingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditVicoAccountSettingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditVicoAccountSettingResult(%+v)", *p)
}

type GetVicoAccountByIdArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
}

func NewGetVicoAccountByIdArgs() *GetVicoAccountByIdArgs {
	return &GetVicoAccountByIdArgs{}
}

func (p *GetVicoAccountByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetVicoAccountByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *GetVicoAccountByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVicoAccountById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetVicoAccountByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *GetVicoAccountByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVicoAccountByIdArgs(%+v)", *p)
}

type GetVicoAccountByIdResult struct {
	Success *vico_passport_types.VicoAccount `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException    `thrift:"e,1" json:"e"`
}

func NewGetVicoAccountByIdResult() *GetVicoAccountByIdResult {
	return &GetVicoAccountByIdResult{}
}

func (p *GetVicoAccountByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = vico_passport_types.NewVicoAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetVicoAccountByIdResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetVicoAccountByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVicoAccountById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetVicoAccountByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetVicoAccountByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVicoAccountByIdResult(%+v)", *p)
}

type GetVicoAccountByMobileArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Mobile int64                 `thrift:"mobile,2" json:"mobile"`
}

func NewGetVicoAccountByMobileArgs() *GetVicoAccountByMobileArgs {
	return &GetVicoAccountByMobileArgs{}
}

func (p *GetVicoAccountByMobileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByMobileArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetVicoAccountByMobileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *GetVicoAccountByMobileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVicoAccountByMobile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByMobileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetVicoAccountByMobileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mobile: %s", p, err)
	}
	return err
}

func (p *GetVicoAccountByMobileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVicoAccountByMobileArgs(%+v)", *p)
}

type GetVicoAccountByMobileResult struct {
	Success *vico_passport_types.VicoAccount `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException    `thrift:"e,1" json:"e"`
}

func NewGetVicoAccountByMobileResult() *GetVicoAccountByMobileResult {
	return &GetVicoAccountByMobileResult{}
}

func (p *GetVicoAccountByMobileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByMobileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = vico_passport_types.NewVicoAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetVicoAccountByMobileResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetVicoAccountByMobileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVicoAccountByMobile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVicoAccountByMobileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetVicoAccountByMobileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetVicoAccountByMobileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVicoAccountByMobileResult(%+v)", *p)
}

type AddVicoAccountFacebookPagesArgs struct {
	Header    *common.RequestHeader                          `thrift:"header,1" json:"header"`
	AccountId int64                                          `thrift:"accountId,2" json:"accountId"`
	Info      []*vico_passport_types.VicoAccountFacebookPage `thrift:"info,3" json:"info"`
}

func NewAddVicoAccountFacebookPagesArgs() *AddVicoAccountFacebookPagesArgs {
	return &AddVicoAccountFacebookPagesArgs{}
}

func (p *AddVicoAccountFacebookPagesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Info = make([]*vico_passport_types.VicoAccountFacebookPage, 0, size)
	for i := 0; i < size; i++ {
		_elem72 := vico_passport_types.NewVicoAccountFacebookPage()
		if err := _elem72.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem72)
		}
		p.Info = append(p.Info, _elem72)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addVicoAccountFacebookPages_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddVicoAccountFacebookPagesArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *AddVicoAccountFacebookPagesArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:info: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Info)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Info {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:info: %s", p, err)
		}
	}
	return err
}

func (p *AddVicoAccountFacebookPagesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddVicoAccountFacebookPagesArgs(%+v)", *p)
}

type AddVicoAccountFacebookPagesResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewAddVicoAccountFacebookPagesResult() *AddVicoAccountFacebookPagesResult {
	return &AddVicoAccountFacebookPagesResult{}
}

func (p *AddVicoAccountFacebookPagesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addVicoAccountFacebookPages_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddVicoAccountFacebookPagesResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddVicoAccountFacebookPagesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddVicoAccountFacebookPagesResult(%+v)", *p)
}

type DeleteVicoAccountFacebookPagesArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
	Ids       []int64               `thrift:"ids,3" json:"ids"`
}

func NewDeleteVicoAccountFacebookPagesArgs() *DeleteVicoAccountFacebookPagesArgs {
	return &DeleteVicoAccountFacebookPagesArgs{}
}

func (p *DeleteVicoAccountFacebookPagesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem73 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem73 = v
		}
		p.Ids = append(p.Ids, _elem73)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteVicoAccountFacebookPages_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeleteVicoAccountFacebookPagesArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *DeleteVicoAccountFacebookPagesArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DeleteVicoAccountFacebookPagesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteVicoAccountFacebookPagesArgs(%+v)", *p)
}

type DeleteVicoAccountFacebookPagesResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewDeleteVicoAccountFacebookPagesResult() *DeleteVicoAccountFacebookPagesResult {
	return &DeleteVicoAccountFacebookPagesResult{}
}

func (p *DeleteVicoAccountFacebookPagesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteVicoAccountFacebookPages_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteVicoAccountFacebookPagesResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *DeleteVicoAccountFacebookPagesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteVicoAccountFacebookPagesResult(%+v)", *p)
}

type SetDefaultFacebookPageArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
	FbPageId  int64                 `thrift:"fbPageId,3" json:"fbPageId"`
}

func NewSetDefaultFacebookPageArgs() *SetDefaultFacebookPageArgs {
	return &SetDefaultFacebookPageArgs{}
}

func (p *SetDefaultFacebookPageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetDefaultFacebookPageArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SetDefaultFacebookPageArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *SetDefaultFacebookPageArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FbPageId = v
	}
	return nil
}

func (p *SetDefaultFacebookPageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setDefaultFacebookPage_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetDefaultFacebookPageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SetDefaultFacebookPageArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *SetDefaultFacebookPageArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fbPageId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fbPageId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbPageId)); err != nil {
		return fmt.Errorf("%T.fbPageId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fbPageId: %s", p, err)
	}
	return err
}

func (p *SetDefaultFacebookPageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetDefaultFacebookPageArgs(%+v)", *p)
}

type SetDefaultFacebookPageResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewSetDefaultFacebookPageResult() *SetDefaultFacebookPageResult {
	return &SetDefaultFacebookPageResult{}
}

func (p *SetDefaultFacebookPageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetDefaultFacebookPageResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SetDefaultFacebookPageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setDefaultFacebookPage_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetDefaultFacebookPageResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SetDefaultFacebookPageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetDefaultFacebookPageResult(%+v)", *p)
}

type GetFacebookPagesByAccountIdArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
}

func NewGetFacebookPagesByAccountIdArgs() *GetFacebookPagesByAccountIdArgs {
	return &GetFacebookPagesByAccountIdArgs{}
}

func (p *GetFacebookPagesByAccountIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFacebookPagesByAccountId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFacebookPagesByAccountIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *GetFacebookPagesByAccountIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFacebookPagesByAccountIdArgs(%+v)", *p)
}

type GetFacebookPagesByAccountIdResult struct {
	Success []*vico_passport_types.VicoAccountFacebookPage `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException                  `thrift:"e,1" json:"e"`
}

func NewGetFacebookPagesByAccountIdResult() *GetFacebookPagesByAccountIdResult {
	return &GetFacebookPagesByAccountIdResult{}
}

func (p *GetFacebookPagesByAccountIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*vico_passport_types.VicoAccountFacebookPage, 0, size)
	for i := 0; i < size; i++ {
		_elem74 := vico_passport_types.NewVicoAccountFacebookPage()
		if err := _elem74.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem74)
		}
		p.Success = append(p.Success, _elem74)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFacebookPagesByAccountId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFacebookPagesByAccountIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFacebookPagesByAccountIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetFacebookPagesByAccountIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFacebookPagesByAccountIdResult(%+v)", *p)
}
