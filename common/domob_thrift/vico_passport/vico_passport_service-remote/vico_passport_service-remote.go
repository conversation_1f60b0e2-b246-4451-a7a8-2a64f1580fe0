// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"vico_passport"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  void sendMobileVaildateCode(RequestHeader header, i64 mobile)")
	fmt.Fprintln(os.Stderr, "  i64 registerVicoAccount(RequestHeader header, UserRegisterInfo info)")
	fmt.Fprintln(os.<PERSON>, "  bool verifyUserPassword(RequestHeader header, i64 mobile, string password)")
	fmt.Fprintln(os.Stderr, "  bool isUserMobileAvailable(RequestHeader header, i64 mobile)")
	fmt.Fprintln(os.Stderr, "  void changeUserPassword(RequestHeader header, i64 accountId, string oldPassword, string newPassword)")
	fmt.Fprintln(os.Stderr, "  void resetUserPassword(RequestHeader header, i64 mobile, string newPassword, string validateCode)")
	fmt.Fprintln(os.Stderr, "  void submitAccountAuditInfo(RequestHeader header, VicoAccount info)")
	fmt.Fprintln(os.Stderr, "  void editVicoAccount(RequestHeader header, VicoAccount info)")
	fmt.Fprintln(os.Stderr, "  void passVicoAccountByIds(RequestHeader header,  accountIds)")
	fmt.Fprintln(os.Stderr, "  void rejectVicoAccountByIds(RequestHeader header,  accountIds)")
	fmt.Fprintln(os.Stderr, "  void editVicoAccountSetting(RequestHeader header, VicoAccountSetting info)")
	fmt.Fprintln(os.Stderr, "  VicoAccount getVicoAccountById(RequestHeader header, i64 accountId)")
	fmt.Fprintln(os.Stderr, "  VicoAccount getVicoAccountByMobile(RequestHeader header, i64 mobile)")
	fmt.Fprintln(os.Stderr, "  void addVicoAccountFacebookPages(RequestHeader header, i64 accountId,  info)")
	fmt.Fprintln(os.Stderr, "  void deleteVicoAccountFacebookPages(RequestHeader header, i64 accountId,  ids)")
	fmt.Fprintln(os.Stderr, "  void setDefaultFacebookPage(RequestHeader header, i64 accountId, i64 fbPageId)")
	fmt.Fprintln(os.Stderr, "   getFacebookPagesByAccountId(RequestHeader header, i64 accountId)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := vico_passport.NewVicoPassportServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "sendMobileVaildateCode":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SendMobileVaildateCode requires 2 args")
			flag.Usage()
		}
		arg75 := flag.Arg(1)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue0 := vico_passport.NewRequestHeader()
		err80 := argvalue0.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err81 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err81 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SendMobileVaildateCode(value0, value1))
		fmt.Print("\n")
		break
	case "registerVicoAccount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RegisterVicoAccount requires 2 args")
			flag.Usage()
		}
		arg82 := flag.Arg(1)
		mbTrans83 := thrift.NewTMemoryBufferLen(len(arg82))
		defer mbTrans83.Close()
		_, err84 := mbTrans83.WriteString(arg82)
		if err84 != nil {
			Usage()
			return
		}
		factory85 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt86 := factory85.GetProtocol(mbTrans83)
		argvalue0 := vico_passport.NewRequestHeader()
		err87 := argvalue0.Read(jsProt86)
		if err87 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg88 := flag.Arg(2)
		mbTrans89 := thrift.NewTMemoryBufferLen(len(arg88))
		defer mbTrans89.Close()
		_, err90 := mbTrans89.WriteString(arg88)
		if err90 != nil {
			Usage()
			return
		}
		factory91 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt92 := factory91.GetProtocol(mbTrans89)
		argvalue1 := vico_passport.NewUserRegisterInfo()
		err93 := argvalue1.Read(jsProt92)
		if err93 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.RegisterVicoAccount(value0, value1))
		fmt.Print("\n")
		break
	case "verifyUserPassword":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "VerifyUserPassword requires 3 args")
			flag.Usage()
		}
		arg94 := flag.Arg(1)
		mbTrans95 := thrift.NewTMemoryBufferLen(len(arg94))
		defer mbTrans95.Close()
		_, err96 := mbTrans95.WriteString(arg94)
		if err96 != nil {
			Usage()
			return
		}
		factory97 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt98 := factory97.GetProtocol(mbTrans95)
		argvalue0 := vico_passport.NewRequestHeader()
		err99 := argvalue0.Read(jsProt98)
		if err99 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err100 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err100 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.VerifyUserPassword(value0, value1, value2))
		fmt.Print("\n")
		break
	case "isUserMobileAvailable":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "IsUserMobileAvailable requires 2 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := vico_passport.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err108 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err108 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.IsUserMobileAvailable(value0, value1))
		fmt.Print("\n")
		break
	case "changeUserPassword":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ChangeUserPassword requires 4 args")
			flag.Usage()
		}
		arg109 := flag.Arg(1)
		mbTrans110 := thrift.NewTMemoryBufferLen(len(arg109))
		defer mbTrans110.Close()
		_, err111 := mbTrans110.WriteString(arg109)
		if err111 != nil {
			Usage()
			return
		}
		factory112 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt113 := factory112.GetProtocol(mbTrans110)
		argvalue0 := vico_passport.NewRequestHeader()
		err114 := argvalue0.Read(jsProt113)
		if err114 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err115 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err115 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.ChangeUserPassword(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resetUserPassword":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResetUserPassword requires 4 args")
			flag.Usage()
		}
		arg118 := flag.Arg(1)
		mbTrans119 := thrift.NewTMemoryBufferLen(len(arg118))
		defer mbTrans119.Close()
		_, err120 := mbTrans119.WriteString(arg118)
		if err120 != nil {
			Usage()
			return
		}
		factory121 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt122 := factory121.GetProtocol(mbTrans119)
		argvalue0 := vico_passport.NewRequestHeader()
		err123 := argvalue0.Read(jsProt122)
		if err123 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err124 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err124 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.ResetUserPassword(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "submitAccountAuditInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SubmitAccountAuditInfo requires 2 args")
			flag.Usage()
		}
		arg127 := flag.Arg(1)
		mbTrans128 := thrift.NewTMemoryBufferLen(len(arg127))
		defer mbTrans128.Close()
		_, err129 := mbTrans128.WriteString(arg127)
		if err129 != nil {
			Usage()
			return
		}
		factory130 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt131 := factory130.GetProtocol(mbTrans128)
		argvalue0 := vico_passport.NewRequestHeader()
		err132 := argvalue0.Read(jsProt131)
		if err132 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg133 := flag.Arg(2)
		mbTrans134 := thrift.NewTMemoryBufferLen(len(arg133))
		defer mbTrans134.Close()
		_, err135 := mbTrans134.WriteString(arg133)
		if err135 != nil {
			Usage()
			return
		}
		factory136 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt137 := factory136.GetProtocol(mbTrans134)
		argvalue1 := vico_passport.NewVicoAccount()
		err138 := argvalue1.Read(jsProt137)
		if err138 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SubmitAccountAuditInfo(value0, value1))
		fmt.Print("\n")
		break
	case "editVicoAccount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditVicoAccount requires 2 args")
			flag.Usage()
		}
		arg139 := flag.Arg(1)
		mbTrans140 := thrift.NewTMemoryBufferLen(len(arg139))
		defer mbTrans140.Close()
		_, err141 := mbTrans140.WriteString(arg139)
		if err141 != nil {
			Usage()
			return
		}
		factory142 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt143 := factory142.GetProtocol(mbTrans140)
		argvalue0 := vico_passport.NewRequestHeader()
		err144 := argvalue0.Read(jsProt143)
		if err144 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg145 := flag.Arg(2)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		argvalue1 := vico_passport.NewVicoAccount()
		err150 := argvalue1.Read(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditVicoAccount(value0, value1))
		fmt.Print("\n")
		break
	case "passVicoAccountByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PassVicoAccountByIds requires 2 args")
			flag.Usage()
		}
		arg151 := flag.Arg(1)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		argvalue0 := vico_passport.NewRequestHeader()
		err156 := argvalue0.Read(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg157 := flag.Arg(2)
		mbTrans158 := thrift.NewTMemoryBufferLen(len(arg157))
		defer mbTrans158.Close()
		_, err159 := mbTrans158.WriteString(arg157)
		if err159 != nil {
			Usage()
			return
		}
		factory160 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt161 := factory160.GetProtocol(mbTrans158)
		containerStruct1 := vico_passport.NewPassVicoAccountByIdsArgs()
		err162 := containerStruct1.ReadField2(jsProt161)
		if err162 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AccountIds
		value1 := argvalue1
		fmt.Print(client.PassVicoAccountByIds(value0, value1))
		fmt.Print("\n")
		break
	case "rejectVicoAccountByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RejectVicoAccountByIds requires 2 args")
			flag.Usage()
		}
		arg163 := flag.Arg(1)
		mbTrans164 := thrift.NewTMemoryBufferLen(len(arg163))
		defer mbTrans164.Close()
		_, err165 := mbTrans164.WriteString(arg163)
		if err165 != nil {
			Usage()
			return
		}
		factory166 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt167 := factory166.GetProtocol(mbTrans164)
		argvalue0 := vico_passport.NewRequestHeader()
		err168 := argvalue0.Read(jsProt167)
		if err168 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg169 := flag.Arg(2)
		mbTrans170 := thrift.NewTMemoryBufferLen(len(arg169))
		defer mbTrans170.Close()
		_, err171 := mbTrans170.WriteString(arg169)
		if err171 != nil {
			Usage()
			return
		}
		factory172 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt173 := factory172.GetProtocol(mbTrans170)
		containerStruct1 := vico_passport.NewRejectVicoAccountByIdsArgs()
		err174 := containerStruct1.ReadField2(jsProt173)
		if err174 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AccountIds
		value1 := argvalue1
		fmt.Print(client.RejectVicoAccountByIds(value0, value1))
		fmt.Print("\n")
		break
	case "editVicoAccountSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditVicoAccountSetting requires 2 args")
			flag.Usage()
		}
		arg175 := flag.Arg(1)
		mbTrans176 := thrift.NewTMemoryBufferLen(len(arg175))
		defer mbTrans176.Close()
		_, err177 := mbTrans176.WriteString(arg175)
		if err177 != nil {
			Usage()
			return
		}
		factory178 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt179 := factory178.GetProtocol(mbTrans176)
		argvalue0 := vico_passport.NewRequestHeader()
		err180 := argvalue0.Read(jsProt179)
		if err180 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg181 := flag.Arg(2)
		mbTrans182 := thrift.NewTMemoryBufferLen(len(arg181))
		defer mbTrans182.Close()
		_, err183 := mbTrans182.WriteString(arg181)
		if err183 != nil {
			Usage()
			return
		}
		factory184 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt185 := factory184.GetProtocol(mbTrans182)
		argvalue1 := vico_passport.NewVicoAccountSetting()
		err186 := argvalue1.Read(jsProt185)
		if err186 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditVicoAccountSetting(value0, value1))
		fmt.Print("\n")
		break
	case "getVicoAccountById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVicoAccountById requires 2 args")
			flag.Usage()
		}
		arg187 := flag.Arg(1)
		mbTrans188 := thrift.NewTMemoryBufferLen(len(arg187))
		defer mbTrans188.Close()
		_, err189 := mbTrans188.WriteString(arg187)
		if err189 != nil {
			Usage()
			return
		}
		factory190 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt191 := factory190.GetProtocol(mbTrans188)
		argvalue0 := vico_passport.NewRequestHeader()
		err192 := argvalue0.Read(jsProt191)
		if err192 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err193 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err193 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetVicoAccountById(value0, value1))
		fmt.Print("\n")
		break
	case "getVicoAccountByMobile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVicoAccountByMobile requires 2 args")
			flag.Usage()
		}
		arg194 := flag.Arg(1)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		argvalue0 := vico_passport.NewRequestHeader()
		err199 := argvalue0.Read(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err200 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err200 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetVicoAccountByMobile(value0, value1))
		fmt.Print("\n")
		break
	case "addVicoAccountFacebookPages":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddVicoAccountFacebookPages requires 3 args")
			flag.Usage()
		}
		arg201 := flag.Arg(1)
		mbTrans202 := thrift.NewTMemoryBufferLen(len(arg201))
		defer mbTrans202.Close()
		_, err203 := mbTrans202.WriteString(arg201)
		if err203 != nil {
			Usage()
			return
		}
		factory204 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt205 := factory204.GetProtocol(mbTrans202)
		argvalue0 := vico_passport.NewRequestHeader()
		err206 := argvalue0.Read(jsProt205)
		if err206 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err207 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err207 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg208 := flag.Arg(3)
		mbTrans209 := thrift.NewTMemoryBufferLen(len(arg208))
		defer mbTrans209.Close()
		_, err210 := mbTrans209.WriteString(arg208)
		if err210 != nil {
			Usage()
			return
		}
		factory211 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt212 := factory211.GetProtocol(mbTrans209)
		containerStruct2 := vico_passport.NewAddVicoAccountFacebookPagesArgs()
		err213 := containerStruct2.ReadField3(jsProt212)
		if err213 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Info
		value2 := argvalue2
		fmt.Print(client.AddVicoAccountFacebookPages(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteVicoAccountFacebookPages":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteVicoAccountFacebookPages requires 3 args")
			flag.Usage()
		}
		arg214 := flag.Arg(1)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		argvalue0 := vico_passport.NewRequestHeader()
		err219 := argvalue0.Read(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err220 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err220 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg221 := flag.Arg(3)
		mbTrans222 := thrift.NewTMemoryBufferLen(len(arg221))
		defer mbTrans222.Close()
		_, err223 := mbTrans222.WriteString(arg221)
		if err223 != nil {
			Usage()
			return
		}
		factory224 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt225 := factory224.GetProtocol(mbTrans222)
		containerStruct2 := vico_passport.NewDeleteVicoAccountFacebookPagesArgs()
		err226 := containerStruct2.ReadField3(jsProt225)
		if err226 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteVicoAccountFacebookPages(value0, value1, value2))
		fmt.Print("\n")
		break
	case "setDefaultFacebookPage":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SetDefaultFacebookPage requires 3 args")
			flag.Usage()
		}
		arg227 := flag.Arg(1)
		mbTrans228 := thrift.NewTMemoryBufferLen(len(arg227))
		defer mbTrans228.Close()
		_, err229 := mbTrans228.WriteString(arg227)
		if err229 != nil {
			Usage()
			return
		}
		factory230 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt231 := factory230.GetProtocol(mbTrans228)
		argvalue0 := vico_passport.NewRequestHeader()
		err232 := argvalue0.Read(jsProt231)
		if err232 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err233 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err233 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err234 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err234 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.SetDefaultFacebookPage(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFacebookPagesByAccountId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFacebookPagesByAccountId requires 2 args")
			flag.Usage()
		}
		arg235 := flag.Arg(1)
		mbTrans236 := thrift.NewTMemoryBufferLen(len(arg235))
		defer mbTrans236.Close()
		_, err237 := mbTrans236.WriteString(arg235)
		if err237 != nil {
			Usage()
			return
		}
		factory238 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt239 := factory238.GetProtocol(mbTrans236)
		argvalue0 := vico_passport.NewRequestHeader()
		err240 := argvalue0.Read(jsProt239)
		if err240 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err241 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err241 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFacebookPagesByAccountId(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
