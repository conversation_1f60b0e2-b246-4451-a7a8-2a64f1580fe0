// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"aow_userserver"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i64 getUserId(RequestHeader header, DeviceInfo device_info, bool create_on_not_exist)")
	fmt.Fprintln(os.Stderr, "   getPackageList(RequestHeader header, i64 uid)")
	fmt.Fprintln(os.<PERSON>, "  TaskQueryResult queryTask(RequestHeader header, i64 uid, AowProduct product, i32 start_dt, i32 end_dt, bool filter_with_date)")
	fmt.Fprintln(os.Stderr, "  TaskQueryResult getTaskList(RequestHeader header, i64 uid, i32 start_dt, i32 end_dt, bool filter_with_date)")
	fmt.Fprintln(os.Stderr, "  bool finishTask(RequestHeader header, TaskInfo task_info)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := aow_userserver.NewAowUserServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getUserId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetUserId requires 3 args")
			flag.Usage()
		}
		arg24 := flag.Arg(1)
		mbTrans25 := thrift.NewTMemoryBufferLen(len(arg24))
		defer mbTrans25.Close()
		_, err26 := mbTrans25.WriteString(arg24)
		if err26 != nil {
			Usage()
			return
		}
		factory27 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt28 := factory27.GetProtocol(mbTrans25)
		argvalue0 := aow_userserver.NewRequestHeader()
		err29 := argvalue0.Read(jsProt28)
		if err29 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg30 := flag.Arg(2)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue1 := aow_userserver.NewDeviceInfo()
		err35 := argvalue1.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetUserId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getPackageList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPackageList requires 2 args")
			flag.Usage()
		}
		arg37 := flag.Arg(1)
		mbTrans38 := thrift.NewTMemoryBufferLen(len(arg37))
		defer mbTrans38.Close()
		_, err39 := mbTrans38.WriteString(arg37)
		if err39 != nil {
			Usage()
			return
		}
		factory40 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt41 := factory40.GetProtocol(mbTrans38)
		argvalue0 := aow_userserver.NewRequestHeader()
		err42 := argvalue0.Read(jsProt41)
		if err42 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err43 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err43 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetPackageList(value0, value1))
		fmt.Print("\n")
		break
	case "queryTask":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryTask requires 6 args")
			flag.Usage()
		}
		arg44 := flag.Arg(1)
		mbTrans45 := thrift.NewTMemoryBufferLen(len(arg44))
		defer mbTrans45.Close()
		_, err46 := mbTrans45.WriteString(arg44)
		if err46 != nil {
			Usage()
			return
		}
		factory47 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt48 := factory47.GetProtocol(mbTrans45)
		argvalue0 := aow_userserver.NewRequestHeader()
		err49 := argvalue0.Read(jsProt48)
		if err49 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err50 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err50 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := aow_userserver.AowProduct(tmp2)
		value2 := argvalue2
		tmp3, err51 := (strconv.Atoi(flag.Arg(4)))
		if err51 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err52 := (strconv.Atoi(flag.Arg(5)))
		if err52 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.QueryTask(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getTaskList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetTaskList requires 5 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := aow_userserver.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err60 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err60 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err61 := (strconv.Atoi(flag.Arg(3)))
		if err61 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err62 := (strconv.Atoi(flag.Arg(4)))
		if err62 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.GetTaskList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "finishTask":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "FinishTask requires 2 args")
			flag.Usage()
		}
		arg64 := flag.Arg(1)
		mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
		defer mbTrans65.Close()
		_, err66 := mbTrans65.WriteString(arg64)
		if err66 != nil {
			Usage()
			return
		}
		factory67 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt68 := factory67.GetProtocol(mbTrans65)
		argvalue0 := aow_userserver.NewRequestHeader()
		err69 := argvalue0.Read(jsProt68)
		if err69 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg70 := flag.Arg(2)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		argvalue1 := aow_userserver.NewTaskInfo()
		err75 := argvalue1.Read(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.FinishTask(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
