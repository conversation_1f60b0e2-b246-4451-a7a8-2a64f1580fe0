// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_userserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/aow_adserver_types"
	"rtb_model_server/common/domob_thrift/aow_ui_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = aow_adserver_types.GoUnusedProtection__
var _ = aow_ui_types.GoUnusedProtection__
var GoUnusedProtection__ int

type AowProduct int64

const (
	AowProduct_ALL       AowProduct = 0
	AowProduct_OFFERWALL AowProduct = 1
	AowProduct_SOS       AowProduct = 2
	AowProduct_WECHAT    AowProduct = 3
)

func (p AowProduct) String() string {
	switch p {
	case AowProduct_ALL:
		return "AowProduct_ALL"
	case AowProduct_OFFERWALL:
		return "AowProduct_OFFERWALL"
	case AowProduct_SOS:
		return "AowProduct_SOS"
	case AowProduct_WECHAT:
		return "AowProduct_WECHAT"
	}
	return "<UNSET>"
}

func AowProductFromString(s string) (AowProduct, error) {
	switch s {
	case "AowProduct_ALL":
		return AowProduct_ALL, nil
	case "AowProduct_OFFERWALL":
		return AowProduct_OFFERWALL, nil
	case "AowProduct_SOS":
		return AowProduct_SOS, nil
	case "AowProduct_WECHAT":
		return AowProduct_WECHAT, nil
	}
	return AowProduct(math.MinInt32 - 1), fmt.Errorf("not a valid AowProduct string")
}

type DeviceInfo struct {
	Id        int64  `thrift:"id,1" json:"id"`
	Imei      string `thrift:"imei,2" json:"imei"`
	AndroidId string `thrift:"android_id,3" json:"android_id"`
	Mac       string `thrift:"mac,4" json:"mac"`
}

func NewDeviceInfo() *DeviceInfo {
	return &DeviceInfo{}
}

func (p *DeviceInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeviceInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DeviceInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *DeviceInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *DeviceInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *DeviceInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeviceInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeviceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imei: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:android_id: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mac: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeviceInfo(%+v)", *p)
}

type TaskInfo struct {
	Id         int64      `thrift:"id,1" json:"id"`
	Uid        int64      `thrift:"uid,2" json:"uid"`
	Pkgid      int32      `thrift:"pkgid,3" json:"pkgid"`
	Cid        int32      `thrift:"cid,4" json:"cid"`
	Mid        int32      `thrift:"mid,5" json:"mid"`
	Planid     int32      `thrift:"planid,6" json:"planid"`
	Sponsorid  int32      `thrift:"sponsorid,7" json:"sponsorid"`
	Action     int32      `thrift:"action,8" json:"action"`
	Price      int32      `thrift:"price,9" json:"price"`
	Point      float64    `thrift:"point,10" json:"point"`
	Product    AowProduct `thrift:"product,11" json:"product"`
	CreateTime int32      `thrift:"create_time,12" json:"create_time"`
	Dt         int32      `thrift:"dt,13" json:"dt"`
	RefId      int64      `thrift:"ref_id,14" json:"ref_id"`
}

func NewTaskInfo() *TaskInfo {
	return &TaskInfo{
		Product: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TaskInfo) IsSetProduct() bool {
	return int64(p.Product) != math.MinInt32-1
}

func (p *TaskInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *TaskInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *TaskInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pkgid = v
	}
	return nil
}

func (p *TaskInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *TaskInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *TaskInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *TaskInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Sponsorid = v
	}
	return nil
}

func (p *TaskInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *TaskInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *TaskInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *TaskInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Product = AowProduct(v)
	}
	return nil
}

func (p *TaskInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *TaskInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *TaskInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.RefId = v
	}
	return nil
}

func (p *TaskInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pkgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pkgid)); err != nil {
		return fmt.Errorf("%T.pkgid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pkgid: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mid: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:planid: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:sponsorid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sponsorid)); err != nil {
		return fmt.Errorf("%T.sponsorid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:sponsorid: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:action: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:price: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Price)); err != nil {
		return fmt.Errorf("%T.price (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:price: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.DOUBLE, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:point: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetProduct() {
		if err := oprot.WriteFieldBegin("product", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:product: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Product)); err != nil {
			return fmt.Errorf("%T.product (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:product: %s", p, err)
		}
	}
	return err
}

func (p *TaskInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:create_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:create_time: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:dt: %s", p, err)
	}
	return err
}

func (p *TaskInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ref_id", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:ref_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RefId)); err != nil {
		return fmt.Errorf("%T.ref_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:ref_id: %s", p, err)
	}
	return err
}

func (p *TaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskInfo(%+v)", *p)
}

type TaskQueryResult struct {
	Total  int32       `thrift:"total,1" json:"total"`
	Offset int32       `thrift:"offset,2" json:"offset"`
	Tasks  []*TaskInfo `thrift:"tasks,3" json:"tasks"`
}

func NewTaskQueryResult() *TaskQueryResult {
	return &TaskQueryResult{}
}

func (p *TaskQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *TaskQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *TaskQueryResult) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tasks = make([]*TaskInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewTaskInfo()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Tasks = append(p.Tasks, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TaskQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:offset: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Tasks != nil {
		if err := oprot.WriteFieldBegin("tasks", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:tasks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tasks {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:tasks: %s", p, err)
		}
	}
	return err
}

func (p *TaskQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskQueryResult(%+v)", *p)
}
