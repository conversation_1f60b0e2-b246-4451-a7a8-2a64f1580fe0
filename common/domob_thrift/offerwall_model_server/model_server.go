// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_model_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/offerwall_info_types"
	"rtb_model_server/common/domob_thrift/offerwall_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = offerwall_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = offerwall_info_types.GoUnusedProtection__

type ModelServer interface {
	dm303.DomobService
	//Model Server服务

	// @Description("request ad rank")
	//
	// Parameters:
	//  - UiReq
	RankAds(uiReq *OwUiRequest) (r *OwUiResponse, err error)
	// @Description("request cache resources")
	//
	// Parameters:
	//  - UiReq
	GetCacheResource(uiReq *OwUiRequest) (r *OwUiCacheResponse, err error)
}

//Model Server服务
type ModelServerClient struct {
	*dm303.DomobServiceClient
}

func NewModelServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ModelServerClient {
	return &ModelServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewModelServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ModelServerClient {
	return &ModelServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// @Description("request ad rank")
//
// Parameters:
//  - UiReq
func (p *ModelServerClient) RankAds(uiReq *OwUiRequest) (r *OwUiResponse, err error) {
	if err = p.sendRankAds(uiReq); err != nil {
		return
	}
	return p.recvRankAds()
}

func (p *ModelServerClient) sendRankAds(uiReq *OwUiRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("rankAds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args10 := NewRankAdsArgs()
	args10.UiReq = uiReq
	if err = args10.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ModelServerClient) recvRankAds() (value *OwUiResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error12 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error13 error
		error13, err = error12.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error13
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result11 := NewRankAdsResult()
	if err = result11.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result11.Success
	return
}

// @Description("request cache resources")
//
// Parameters:
//  - UiReq
func (p *ModelServerClient) GetCacheResource(uiReq *OwUiRequest) (r *OwUiCacheResponse, err error) {
	if err = p.sendGetCacheResource(uiReq); err != nil {
		return
	}
	return p.recvGetCacheResource()
}

func (p *ModelServerClient) sendGetCacheResource(uiReq *OwUiRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCacheResource", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args14 := NewGetCacheResourceArgs()
	args14.UiReq = uiReq
	if err = args14.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ModelServerClient) recvGetCacheResource() (value *OwUiCacheResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error16 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error17 error
		error17, err = error16.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error17
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result15 := NewGetCacheResourceResult()
	if err = result15.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result15.Success
	return
}

type ModelServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewModelServerProcessor(handler ModelServer) *ModelServerProcessor {
	self18 := &ModelServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self18.AddToProcessorMap("rankAds", &modelServerProcessorRankAds{handler: handler})
	self18.AddToProcessorMap("getCacheResource", &modelServerProcessorGetCacheResource{handler: handler})
	return self18
}

type modelServerProcessorRankAds struct {
	handler ModelServer
}

func (p *modelServerProcessorRankAds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRankAdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("rankAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRankAdsResult()
	if result.Success, err = p.handler.RankAds(args.UiReq); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing rankAds: "+err.Error())
		oprot.WriteMessageBegin("rankAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("rankAds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type modelServerProcessorGetCacheResource struct {
	handler ModelServer
}

func (p *modelServerProcessorGetCacheResource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCacheResourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCacheResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCacheResourceResult()
	if result.Success, err = p.handler.GetCacheResource(args.UiReq); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCacheResource: "+err.Error())
		oprot.WriteMessageBegin("getCacheResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCacheResource", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type RankAdsArgs struct {
	UiReq *OwUiRequest `thrift:"uiReq,1" json:"uiReq"`
}

func NewRankAdsArgs() *RankAdsArgs {
	return &RankAdsArgs{}
}

func (p *RankAdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RankAdsArgs) readField1(iprot thrift.TProtocol) error {
	p.UiReq = NewOwUiRequest()
	if err := p.UiReq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiReq)
	}
	return nil
}

func (p *RankAdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rankAds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RankAdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.UiReq != nil {
		if err := oprot.WriteFieldBegin("uiReq", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uiReq: %s", p, err)
		}
		if err := p.UiReq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiReq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uiReq: %s", p, err)
		}
	}
	return err
}

func (p *RankAdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RankAdsArgs(%+v)", *p)
}

type RankAdsResult struct {
	Success *OwUiResponse `thrift:"success,0" json:"success"`
}

func NewRankAdsResult() *RankAdsResult {
	return &RankAdsResult{}
}

func (p *RankAdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RankAdsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwUiResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RankAdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rankAds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RankAdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RankAdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RankAdsResult(%+v)", *p)
}

type GetCacheResourceArgs struct {
	UiReq *OwUiRequest `thrift:"uiReq,1" json:"uiReq"`
}

func NewGetCacheResourceArgs() *GetCacheResourceArgs {
	return &GetCacheResourceArgs{}
}

func (p *GetCacheResourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCacheResourceArgs) readField1(iprot thrift.TProtocol) error {
	p.UiReq = NewOwUiRequest()
	if err := p.UiReq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiReq)
	}
	return nil
}

func (p *GetCacheResourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCacheResource_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCacheResourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.UiReq != nil {
		if err := oprot.WriteFieldBegin("uiReq", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uiReq: %s", p, err)
		}
		if err := p.UiReq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiReq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uiReq: %s", p, err)
		}
	}
	return err
}

func (p *GetCacheResourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCacheResourceArgs(%+v)", *p)
}

type GetCacheResourceResult struct {
	Success *OwUiCacheResponse `thrift:"success,0" json:"success"`
}

func NewGetCacheResourceResult() *GetCacheResourceResult {
	return &GetCacheResourceResult{}
}

func (p *GetCacheResourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCacheResourceResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwUiCacheResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCacheResourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCacheResource_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCacheResourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCacheResourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCacheResourceResult(%+v)", *p)
}
