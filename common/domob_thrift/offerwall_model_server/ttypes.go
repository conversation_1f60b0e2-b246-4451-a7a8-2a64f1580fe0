// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_model_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/offerwall_info_types"
	"rtb_model_server/common/domob_thrift/offerwall_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = offerwall_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = offerwall_info_types.GoUnusedProtection__
var GoUnusedProtection__ int

//表示需要排序的广告类型
//用于ModelServer快速判断排序策略
type OwRankType int64

const (
	OwRankType_RT_IOS_HAS_POINT OwRankType = 1
	OwRankType_RT_IOS_NO_POINT  OwRankType = 2
	OwRankType_RT_ANDROID_TODO  OwRankType = 3
	OwRankType_RT_VIDEO         OwRankType = 4
)

func (p OwRankType) String() string {
	switch p {
	case OwRankType_RT_IOS_HAS_POINT:
		return "OwRankType_RT_IOS_HAS_POINT"
	case OwRankType_RT_IOS_NO_POINT:
		return "OwRankType_RT_IOS_NO_POINT"
	case OwRankType_RT_ANDROID_TODO:
		return "OwRankType_RT_ANDROID_TODO"
	case OwRankType_RT_VIDEO:
		return "OwRankType_RT_VIDEO"
	}
	return "<UNSET>"
}

func OwRankTypeFromString(s string) (OwRankType, error) {
	switch s {
	case "OwRankType_RT_IOS_HAS_POINT":
		return OwRankType_RT_IOS_HAS_POINT, nil
	case "OwRankType_RT_IOS_NO_POINT":
		return OwRankType_RT_IOS_NO_POINT, nil
	case "OwRankType_RT_ANDROID_TODO":
		return OwRankType_RT_ANDROID_TODO, nil
	case "OwRankType_RT_VIDEO":
		return OwRankType_RT_VIDEO, nil
	}
	return OwRankType(math.MinInt32 - 1), fmt.Errorf("not a valid OwRankType string")
}

//频次控制的一个周期定义
type FreqCycle int64

const (
	FreqCycle_FOREVER FreqCycle = 0
	FreqCycle_HOUR    FreqCycle = 1
	FreqCycle_DAY     FreqCycle = 2
	FreqCycle_WEEK    FreqCycle = 3
	FreqCycle_MONTH   FreqCycle = 4
)

func (p FreqCycle) String() string {
	switch p {
	case FreqCycle_FOREVER:
		return "FreqCycle_FOREVER"
	case FreqCycle_HOUR:
		return "FreqCycle_HOUR"
	case FreqCycle_DAY:
		return "FreqCycle_DAY"
	case FreqCycle_WEEK:
		return "FreqCycle_WEEK"
	case FreqCycle_MONTH:
		return "FreqCycle_MONTH"
	}
	return "<UNSET>"
}

func FreqCycleFromString(s string) (FreqCycle, error) {
	switch s {
	case "FreqCycle_FOREVER":
		return FreqCycle_FOREVER, nil
	case "FreqCycle_HOUR":
		return FreqCycle_HOUR, nil
	case "FreqCycle_DAY":
		return FreqCycle_DAY, nil
	case "FreqCycle_WEEK":
		return FreqCycle_WEEK, nil
	case "FreqCycle_MONTH":
		return FreqCycle_MONTH, nil
	}
	return FreqCycle(math.MinInt32 - 1), fmt.Errorf("not a valid FreqCycle string")
}

//
type FreqType int64

const (
	FreqType_IMP FreqType = 0
	FreqType_CLK FreqType = 1
)

func (p FreqType) String() string {
	switch p {
	case FreqType_IMP:
		return "FreqType_IMP"
	case FreqType_CLK:
		return "FreqType_CLK"
	}
	return "<UNSET>"
}

func FreqTypeFromString(s string) (FreqType, error) {
	switch s {
	case "FreqType_IMP":
		return FreqType_IMP, nil
	case "FreqType_CLK":
		return FreqType_CLK, nil
	}
	return FreqType(math.MinInt32 - 1), fmt.Errorf("not a valid FreqType string")
}

//资源物料类型
type ResourceType int64

const (
	ResourceType_STREAMING_VIDEO ResourceType = 1
	ResourceType_MP4_VIDEO       ResourceType = 2
	ResourceType_AUDIO           ResourceType = 3
	ResourceType_IMAGE           ResourceType = 4
	ResourceType_APK             ResourceType = 5
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_STREAMING_VIDEO:
		return "ResourceType_STREAMING_VIDEO"
	case ResourceType_MP4_VIDEO:
		return "ResourceType_MP4_VIDEO"
	case ResourceType_AUDIO:
		return "ResourceType_AUDIO"
	case ResourceType_IMAGE:
		return "ResourceType_IMAGE"
	case ResourceType_APK:
		return "ResourceType_APK"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "ResourceType_STREAMING_VIDEO":
		return ResourceType_STREAMING_VIDEO, nil
	case "ResourceType_MP4_VIDEO":
		return ResourceType_MP4_VIDEO, nil
	case "ResourceType_AUDIO":
		return ResourceType_AUDIO, nil
	case "ResourceType_IMAGE":
		return ResourceType_IMAGE, nil
	case "ResourceType_APK":
		return ResourceType_APK, nil
	}
	return ResourceType(math.MinInt32 - 1), fmt.Errorf("not a valid ResourceType string")
}

type LargeIdInt common.LargeIdInt

type UidInt common.UidInt

type MediaIdInt common.IdInt

type PlanIdInt common.IdInt

type CreativeIdInt common.IdInt

type ImgIdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type JailBreakCode common.JailBreakCode

type IdInt common.IdInt

type AccessTypeCode common.AccessTypeCode

type OwFeedback offerwall_types.OwFeedback

type OwAdActionType offerwall_types.OwAdActionType

type FreqInfo struct {
	FreqId    int32     `thrift:"freqId,1" json:"freqId"`
	Frequency int32     `thrift:"frequency,2" json:"frequency"`
	Cycle     FreqCycle `thrift:"cycle,3" json:"cycle"`
	TypeA1    FreqType  `thrift:"type,4" json:"type"`
}

func NewFreqInfo() *FreqInfo {
	return &FreqInfo{
		Cycle: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FreqInfo) IsSetCycle() bool {
	return int64(p.Cycle) != math.MinInt32-1
}

func (p *FreqInfo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *FreqInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FreqInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FreqId = v
	}
	return nil
}

func (p *FreqInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Frequency = v
	}
	return nil
}

func (p *FreqInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cycle = FreqCycle(v)
	}
	return nil
}

func (p *FreqInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = FreqType(v)
	}
	return nil
}

func (p *FreqInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FreqInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FreqInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("freqId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:freqId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FreqId)); err != nil {
		return fmt.Errorf("%T.freqId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:freqId: %s", p, err)
	}
	return err
}

func (p *FreqInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("frequency", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:frequency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Frequency)); err != nil {
		return fmt.Errorf("%T.frequency (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:frequency: %s", p, err)
	}
	return err
}

func (p *FreqInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCycle() {
		if err := oprot.WriteFieldBegin("cycle", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:cycle: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Cycle)); err != nil {
			return fmt.Errorf("%T.cycle (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:cycle: %s", p, err)
		}
	}
	return err
}

func (p *FreqInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *FreqInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FreqInfo(%+v)", *p)
}

type OwUiResponseExtInfo struct {
	MediaShare Amount `thrift:"media_share,1" json:"media_share"`
	Price      Amount `thrift:"price,2" json:"price"`
}

func NewOwUiResponseExtInfo() *OwUiResponseExtInfo {
	return &OwUiResponseExtInfo{}
}

func (p *OwUiResponseExtInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwUiResponseExtInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaShare = Amount(v)
	}
	return nil
}

func (p *OwUiResponseExtInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *OwUiResponseExtInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwUiResponseExtInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwUiResponseExtInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_share", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:media_share: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaShare)); err != nil {
		return fmt.Errorf("%T.media_share (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:media_share: %s", p, err)
	}
	return err
}

func (p *OwUiResponseExtInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:price: %s", p, err)
	}
	return err
}

func (p *OwUiResponseExtInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwUiResponseExtInfo(%+v)", *p)
}

type Resource struct {
	Rcid      int32        `thrift:"rcid,1" json:"rcid"`
	Url       string       `thrift:"url,2" json:"url"`
	Rctype    ResourceType `thrift:"rctype,3" json:"rctype"`
	Rgid      int32        `thrift:"rgid,4" json:"rgid"`
	PixelSize int32        `thrift:"pixelSize,5" json:"pixelSize"`
}

func NewResource() *Resource {
	return &Resource{
		Rctype: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Resource) IsSetRctype() bool {
	return int64(p.Rctype) != math.MinInt32-1
}

func (p *Resource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Resource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Rcid = v
	}
	return nil
}

func (p *Resource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Resource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rctype = ResourceType(v)
	}
	return nil
}

func (p *Resource) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Rgid = v
	}
	return nil
}

func (p *Resource) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PixelSize = v
	}
	return nil
}

func (p *Resource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Resource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Resource) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rcid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rcid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rcid)); err != nil {
		return fmt.Errorf("%T.rcid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rcid: %s", p, err)
	}
	return err
}

func (p *Resource) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:url: %s", p, err)
	}
	return err
}

func (p *Resource) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRctype() {
		if err := oprot.WriteFieldBegin("rctype", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:rctype: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Rctype)); err != nil {
			return fmt.Errorf("%T.rctype (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:rctype: %s", p, err)
		}
	}
	return err
}

func (p *Resource) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:rgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rgid)); err != nil {
		return fmt.Errorf("%T.rgid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:rgid: %s", p, err)
	}
	return err
}

func (p *Resource) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pixelSize", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pixelSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PixelSize)); err != nil {
		return fmt.Errorf("%T.pixelSize (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pixelSize: %s", p, err)
	}
	return err
}

func (p *Resource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Resource(%+v)", *p)
}

type ResourceGroup struct {
	Rgid         int32       `thrift:"rgid,1" json:"rgid"`
	ResourceList []*Resource `thrift:"resourceList,2" json:"resourceList"`
	RgName       string      `thrift:"rgName,3" json:"rgName"`
	ExpireTime   TimeInt     `thrift:"expireTime,4" json:"expireTime"`
}

func NewResourceGroup() *ResourceGroup {
	return &ResourceGroup{}
}

func (p *ResourceGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResourceGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Rgid = v
	}
	return nil
}

func (p *ResourceGroup) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResourceList = make([]*Resource, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewResource()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.ResourceList = append(p.ResourceList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResourceGroup) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RgName = v
	}
	return nil
}

func (p *ResourceGroup) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExpireTime = TimeInt(v)
	}
	return nil
}

func (p *ResourceGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResourceGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResourceGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rgid)); err != nil {
		return fmt.Errorf("%T.rgid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rgid: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ResourceList != nil {
		if err := oprot.WriteFieldBegin("resourceList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:resourceList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResourceList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:resourceList: %s", p, err)
		}
	}
	return err
}

func (p *ResourceGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RgName)); err != nil {
		return fmt.Errorf("%T.rgName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rgName: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expireTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:expireTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExpireTime)); err != nil {
		return fmt.Errorf("%T.expireTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:expireTime: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceGroup(%+v)", *p)
}

type OwAdPlan struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	Timeslots        string      `thrift:"timeslots,8" json:"timeslots"`
	Gamma            int32       `thrift:"gamma,9" json:"gamma"`
	Starttime        TimeInt     `thrift:"starttime,10" json:"starttime"`
	ClkNum           int32       `thrift:"clk_num,11" json:"clk_num"`
	ImpFreqList      []*FreqInfo `thrift:"impFreqList,12" json:"impFreqList"`
	ClkFreqList      []*FreqInfo `thrift:"clkFreqList,13" json:"clkFreqList"`
	Dailybudget      int32       `thrift:"dailybudget,14" json:"dailybudget"`
	Dailyconsumed    int32       `thrift:"dailyconsumed,15" json:"dailyconsumed"`
	Endtime          TimeInt     `thrift:"endtime,16" json:"endtime"`
	UniformUseBudget bool        `thrift:"uniform_use_budget,17" json:"uniform_use_budget"`
	CostType         int16       `thrift:"cost_type,18" json:"cost_type"`
	OfferType        int16       `thrift:"offer_type,19" json:"offer_type"`
}

func NewOwAdPlan() *OwAdPlan {
	return &OwAdPlan{}
}

func (p *OwAdPlan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I16 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I16 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Timeslots = v
	}
	return nil
}

func (p *OwAdPlan) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Gamma = v
	}
	return nil
}

func (p *OwAdPlan) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Starttime = TimeInt(v)
	}
	return nil
}

func (p *OwAdPlan) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ClkNum = v
	}
	return nil
}

func (p *OwAdPlan) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpFreqList = make([]*FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewFreqInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ImpFreqList = append(p.ImpFreqList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdPlan) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkFreqList = make([]*FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewFreqInfo()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.ClkFreqList = append(p.ClkFreqList, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdPlan) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Dailybudget = v
	}
	return nil
}

func (p *OwAdPlan) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Dailyconsumed = v
	}
	return nil
}

func (p *OwAdPlan) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Endtime = TimeInt(v)
	}
	return nil
}

func (p *OwAdPlan) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.UniformUseBudget = v
	}
	return nil
}

func (p *OwAdPlan) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwAdPlan) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwAdPlan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdPlan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timeslots", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:timeslots: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Timeslots)); err != nil {
		return fmt.Errorf("%T.timeslots (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:timeslots: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gamma", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:gamma: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Gamma)); err != nil {
		return fmt.Errorf("%T.gamma (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:gamma: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("starttime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:starttime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Starttime)); err != nil {
		return fmt.Errorf("%T.starttime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:starttime: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_num", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:clk_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkNum)); err != nil {
		return fmt.Errorf("%T.clk_num (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:clk_num: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField12(oprot thrift.TProtocol) (err error) {
	if p.ImpFreqList != nil {
		if err := oprot.WriteFieldBegin("impFreqList", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:impFreqList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImpFreqList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpFreqList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:impFreqList: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlan) writeField13(oprot thrift.TProtocol) (err error) {
	if p.ClkFreqList != nil {
		if err := oprot.WriteFieldBegin("clkFreqList", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:clkFreqList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ClkFreqList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkFreqList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:clkFreqList: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlan) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailybudget", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:dailybudget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dailybudget)); err != nil {
		return fmt.Errorf("%T.dailybudget (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:dailybudget: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyconsumed", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:dailyconsumed: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dailyconsumed)); err != nil {
		return fmt.Errorf("%T.dailyconsumed (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:dailyconsumed: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endtime", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:endtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Endtime)); err != nil {
		return fmt.Errorf("%T.endtime (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:endtime: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uniform_use_budget", thrift.BOOL, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:uniform_use_budget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UniformUseBudget)); err != nil {
		return fmt.Errorf("%T.uniform_use_budget (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:uniform_use_budget: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I16, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:cost_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:cost_type: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I16, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:offer_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:offer_type: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdPlan(%+v)", *p)
}

type OwAdCreative struct {
	Cid   CreativeIdInt `thrift:"cid,1" json:"cid"`
	Pid   PlanIdInt     `thrift:"pid,2" json:"pid"`
	Uid   UidInt        `thrift:"uid,3" json:"uid"`
	Pkgid IdInt         `thrift:"pkgid,4" json:"pkgid"`
	// unused field # 5
	Logo ImgIdInt `thrift:"logo,6" json:"logo"`
	// unused field # 7
	Price    Amount     `thrift:"price,8" json:"price"`
	Point    Amount     `thrift:"point,9" json:"point"`
	Feedback OwFeedback `thrift:"feedback,10" json:"feedback"`
	// unused field # 11
	// unused field # 12
	Nopoint    bool           `thrift:"nopoint,13" json:"nopoint"`
	ActionType OwAdActionType `thrift:"action_type,14" json:"action_type"`
	// unused field # 15
	SpPrice         Amount         `thrift:"sp_price,16" json:"sp_price"`
	ResourceGroup   *ResourceGroup `thrift:"resourceGroup,17" json:"resourceGroup"`
	VideoCostTarget Amount         `thrift:"video_cost_target,18" json:"video_cost_target"`
}

func NewOwAdCreative() *OwAdCreative {
	return &OwAdCreative{
		Feedback: math.MinInt32 - 1, // unset sentinal value

		ActionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAdCreative) IsSetFeedback() bool {
	return int64(p.Feedback) != math.MinInt32-1
}

func (p *OwAdCreative) IsSetActionType() bool {
	return int64(p.ActionType) != math.MinInt32-1
}

func (p *OwAdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Cid = CreativeIdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = PlanIdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Pkgid = IdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Logo = ImgIdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Point = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Feedback = OwFeedback(v)
	}
	return nil
}

func (p *OwAdCreative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Nopoint = v
	}
	return nil
}

func (p *OwAdCreative) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActionType = OwAdActionType(v)
	}
	return nil
}

func (p *OwAdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.SpPrice = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) readField17(iprot thrift.TProtocol) error {
	p.ResourceGroup = NewResourceGroup()
	if err := p.ResourceGroup.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ResourceGroup)
	}
	return nil
}

func (p *OwAdCreative) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.VideoCostTarget = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pkgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pkgid)); err != nil {
		return fmt.Errorf("%T.pkgid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pkgid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:logo: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:price: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:point: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("feedback", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:feedback: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Feedback)); err != nil {
		return fmt.Errorf("%T.feedback (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:feedback: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nopoint", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:nopoint: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Nopoint)); err != nil {
		return fmt.Errorf("%T.nopoint (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:nopoint: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_type", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.action_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:action_type: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sp_price: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField17(oprot thrift.TProtocol) (err error) {
	if p.ResourceGroup != nil {
		if err := oprot.WriteFieldBegin("resourceGroup", thrift.STRUCT, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:resourceGroup: %s", p, err)
		}
		if err := p.ResourceGroup.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ResourceGroup)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:resourceGroup: %s", p, err)
		}
	}
	return err
}

func (p *OwAdCreative) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_cost_target", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:video_cost_target: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.VideoCostTarget)); err != nil {
		return fmt.Errorf("%T.video_cost_target (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:video_cost_target: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdCreative(%+v)", *p)
}

type OwPackage struct {
	Name    string `thrift:"name,1" json:"name"`
	Version string `thrift:"version,2" json:"version"`
	Appid   string `thrift:"appid,3" json:"appid"`
	Size    int64  `thrift:"size,4" json:"size"`
}

func NewOwPackage() *OwPackage {
	return &OwPackage{}
}

func (p *OwPackage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwPackage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *OwPackage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *OwPackage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *OwPackage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *OwPackage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwPackage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwPackage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:version: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appid: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *OwPackage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwPackage(%+v)", *p)
}

type OwRawAd struct {
	Plan     *OwAdPlan     `thrift:"plan,1" json:"plan"`
	Creative *OwAdCreative `thrift:"creative,2" json:"creative"`
	Pkg      *OwPackage    `thrift:"pkg,3" json:"pkg"`
}

func NewOwRawAd() *OwRawAd {
	return &OwRawAd{}
}

func (p *OwRawAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwRawAd) readField1(iprot thrift.TProtocol) error {
	p.Plan = NewOwAdPlan()
	if err := p.Plan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Plan)
	}
	return nil
}

func (p *OwRawAd) readField2(iprot thrift.TProtocol) error {
	p.Creative = NewOwAdCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *OwRawAd) readField3(iprot thrift.TProtocol) error {
	p.Pkg = NewOwPackage()
	if err := p.Pkg.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pkg)
	}
	return nil
}

func (p *OwRawAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwRawAd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwRawAd) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Plan != nil {
		if err := oprot.WriteFieldBegin("plan", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:plan: %s", p, err)
		}
		if err := p.Plan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Plan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:plan: %s", p, err)
		}
	}
	return err
}

func (p *OwRawAd) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:creative: %s", p, err)
		}
	}
	return err
}

func (p *OwRawAd) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Pkg != nil {
		if err := oprot.WriteFieldBegin("pkg", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:pkg: %s", p, err)
		}
		if err := p.Pkg.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pkg)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:pkg: %s", p, err)
		}
	}
	return err
}

func (p *OwRawAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwRawAd(%+v)", *p)
}

type OwRequestData struct {
	SdkUa          string        `thrift:"sdk_ua,1" json:"sdk_ua"`
	Sdk            string        `thrift:"sdk,2" json:"sdk"`
	Rt             string        `thrift:"rt,3" json:"rt"`
	Ts             TimeInt       `thrift:"ts,4" json:"ts"`
	Ipb            string        `thrift:"ipb,5" json:"ipb"`
	Idv            string        `thrift:"idv,6" json:"idv"`
	V              string        `thrift:"v,7" json:"v"`
	Sv             string        `thrift:"sv,8" json:"sv"`
	L              string        `thrift:"l,9" json:"l"`
	So             string        `thrift:"so,10" json:"so"`
	Sw             string        `thrift:"sw,11" json:"sw"`
	Sh             string        `thrift:"sh,12" json:"sh"`
	Sd             string        `thrift:"sd,13" json:"sd"`
	PbIdentifier   string        `thrift:"pb_identifier,14" json:"pb_identifier"`
	PbVersion      string        `thrift:"pb_version,15" json:"pb_version"`
	PbName         string        `thrift:"pb_name,16" json:"pb_name"`
	CoordTimestamp string        `thrift:"coord_timestamp,17" json:"coord_timestamp"`
	Coord          string        `thrift:"coord,18" json:"coord"`
	Network        string        `thrift:"network,19" json:"network"`
	Jb             JailBreakCode `thrift:"jb,20" json:"jb"`
	Ma             string        `thrift:"ma,21" json:"ma"`
	Oid            string        `thrift:"oid,22" json:"oid"`
	Dma            string        `thrift:"dma,23" json:"dma"`
	Odin1          string        `thrift:"odin1,24" json:"odin1"`
	Ifa            string        `thrift:"ifa,25" json:"ifa"`
	Lat            string        `thrift:"lat,26" json:"lat"`
	Ifv            string        `thrift:"ifv,27" json:"ifv"`
	UserId         string        `thrift:"userId,28" json:"userId"`
	// unused field # 29
	// unused field # 30
	SearchId          LargeIdInt     `thrift:"searchId,31" json:"searchId"`
	Isv               int32          `thrift:"isv,32" json:"isv"`
	Uid               UidInt         `thrift:"uid,33" json:"uid"`
	Mid               MediaIdInt     `thrift:"mid,34" json:"mid"`
	DeviceCode        int32          `thrift:"deviceCode,35" json:"deviceCode"`
	RegionCode        int32          `thrift:"regionCode,36" json:"regionCode"`
	Imei              string         `thrift:"imei,37" json:"imei"`
	Uuid              string         `thrift:"uuid,38" json:"uuid"`
	OsCode            int32          `thrift:"osCode,39" json:"osCode"`
	SdkPlatform       int32          `thrift:"sdkPlatform,40" json:"sdkPlatform"`
	AccessCode        AccessTypeCode `thrift:"accessCode,41" json:"accessCode"`
	Capability        string         `thrift:"capability,42" json:"capability"`
	Duid              int64          `thrift:"duid,43" json:"duid"`
	Iffd              []int32        `thrift:"iffd,44" json:"iffd"`
	Cffd              []int32        `thrift:"cffd,45" json:"cffd"`
	Cd                []int32        `thrift:"cd,46" json:"cd"`
	Num               int16          `thrift:"num,47" json:"num"`
	ExtCapabilityList []int32        `thrift:"extCapabilityList,48" json:"extCapabilityList"`
}

func NewOwRequestData() *OwRequestData {
	return &OwRequestData{
		Jb: math.MinInt32 - 1, // unset sentinal value

		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwRequestData) IsSetJb() bool {
	return int64(p.Jb) != math.MinInt32-1
}

func (p *OwRequestData) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *OwRequestData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I64 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.LIST {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.LIST {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.LIST {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I16 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.LIST {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwRequestData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SdkUa = v
	}
	return nil
}

func (p *OwRequestData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sdk = v
	}
	return nil
}

func (p *OwRequestData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rt = v
	}
	return nil
}

func (p *OwRequestData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Ts = TimeInt(v)
	}
	return nil
}

func (p *OwRequestData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *OwRequestData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *OwRequestData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.V = v
	}
	return nil
}

func (p *OwRequestData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *OwRequestData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.L = v
	}
	return nil
}

func (p *OwRequestData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.So = v
	}
	return nil
}

func (p *OwRequestData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Sw = v
	}
	return nil
}

func (p *OwRequestData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sh = v
	}
	return nil
}

func (p *OwRequestData) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Sd = v
	}
	return nil
}

func (p *OwRequestData) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PbIdentifier = v
	}
	return nil
}

func (p *OwRequestData) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PbVersion = v
	}
	return nil
}

func (p *OwRequestData) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.PbName = v
	}
	return nil
}

func (p *OwRequestData) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.CoordTimestamp = v
	}
	return nil
}

func (p *OwRequestData) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Coord = v
	}
	return nil
}

func (p *OwRequestData) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *OwRequestData) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Jb = JailBreakCode(v)
	}
	return nil
}

func (p *OwRequestData) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Ma = v
	}
	return nil
}

func (p *OwRequestData) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwRequestData) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Dma = v
	}
	return nil
}

func (p *OwRequestData) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwRequestData) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Ifa = v
	}
	return nil
}

func (p *OwRequestData) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Lat = v
	}
	return nil
}

func (p *OwRequestData) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Ifv = v
	}
	return nil
}

func (p *OwRequestData) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *OwRequestData) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *OwRequestData) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Isv = v
	}
	return nil
}

func (p *OwRequestData) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *OwRequestData) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *OwRequestData) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *OwRequestData) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *OwRequestData) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *OwRequestData) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *OwRequestData) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *OwRequestData) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.SdkPlatform = v
	}
	return nil
}

func (p *OwRequestData) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.AccessCode = AccessTypeCode(v)
	}
	return nil
}

func (p *OwRequestData) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Capability = v
	}
	return nil
}

func (p *OwRequestData) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *OwRequestData) readField44(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Iffd = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Iffd = append(p.Iffd, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwRequestData) readField45(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Cffd = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.Cffd = append(p.Cffd, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwRequestData) readField46(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Cd = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.Cd = append(p.Cd, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwRequestData) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.Num = v
	}
	return nil
}

func (p *OwRequestData) readField48(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExtCapabilityList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.ExtCapabilityList = append(p.ExtCapabilityList, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwRequestData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwRequestData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwRequestData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_ua", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sdk_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkUa)); err != nil {
		return fmt.Errorf("%T.sdk_ua (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sdk_ua: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sdk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sdk)); err != nil {
		return fmt.Errorf("%T.sdk (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sdk: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rt", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Rt)); err != nil {
		return fmt.Errorf("%T.rt (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rt: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ts", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ts: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ts)); err != nil {
		return fmt.Errorf("%T.ts (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ts: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ipb: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:idv: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("v", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:v: %s", p, err)
	}
	if err := oprot.WriteString(string(p.V)); err != nil {
		return fmt.Errorf("%T.v (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:v: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sv: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("l", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:l: %s", p, err)
	}
	if err := oprot.WriteString(string(p.L)); err != nil {
		return fmt.Errorf("%T.l (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:l: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("so", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:so: %s", p, err)
	}
	if err := oprot.WriteString(string(p.So)); err != nil {
		return fmt.Errorf("%T.so (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:so: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sw", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sw: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sw)); err != nil {
		return fmt.Errorf("%T.sw (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sw: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sh", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sh: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sh)); err != nil {
		return fmt.Errorf("%T.sh (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sh: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:sd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sd)); err != nil {
		return fmt.Errorf("%T.sd (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:sd: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_identifier", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:pb_identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbIdentifier)); err != nil {
		return fmt.Errorf("%T.pb_identifier (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:pb_identifier: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_version", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:pb_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbVersion)); err != nil {
		return fmt.Errorf("%T.pb_version (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:pb_version: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_name", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:pb_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbName)); err != nil {
		return fmt.Errorf("%T.pb_name (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:pb_name: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_timestamp", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:coord_timestamp: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordTimestamp)); err != nil {
		return fmt.Errorf("%T.coord_timestamp (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:coord_timestamp: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:coord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Coord)); err != nil {
		return fmt.Errorf("%T.coord (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:coord: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:network: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jb", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:jb: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Jb)); err != nil {
		return fmt.Errorf("%T.jb (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:jb: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ma", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:ma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ma)); err != nil {
		return fmt.Errorf("%T.ma (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:ma: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:oid: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dma", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:dma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dma)); err != nil {
		return fmt.Errorf("%T.dma (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:dma: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:odin1: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ifa", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:ifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ifa)); err != nil {
		return fmt.Errorf("%T.ifa (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:ifa: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lat", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:lat: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Lat)); err != nil {
		return fmt.Errorf("%T.lat (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:lat: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ifv", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:ifv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ifv)); err != nil {
		return fmt.Errorf("%T.ifv (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:ifv: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:userId: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:searchId: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isv", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:isv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Isv)); err != nil {
		return fmt.Errorf("%T.isv (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:isv: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:uid: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:mid: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceCode", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:deviceCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.deviceCode (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:deviceCode: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regionCode", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:regionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.regionCode (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:regionCode: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:imei: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:uuid: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("osCode", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:osCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.osCode (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:osCode: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdkPlatform", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:sdkPlatform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkPlatform)); err != nil {
		return fmt.Errorf("%T.sdkPlatform (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:sdkPlatform: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accessCode", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:accessCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.accessCode (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:accessCode: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("capability", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:capability: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Capability)); err != nil {
		return fmt.Errorf("%T.capability (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:capability: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:duid: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField44(oprot thrift.TProtocol) (err error) {
	if p.Iffd != nil {
		if err := oprot.WriteFieldBegin("iffd", thrift.LIST, 44); err != nil {
			return fmt.Errorf("%T write field begin error 44:iffd: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Iffd)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Iffd {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 44:iffd: %s", p, err)
		}
	}
	return err
}

func (p *OwRequestData) writeField45(oprot thrift.TProtocol) (err error) {
	if p.Cffd != nil {
		if err := oprot.WriteFieldBegin("cffd", thrift.LIST, 45); err != nil {
			return fmt.Errorf("%T write field begin error 45:cffd: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Cffd)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Cffd {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 45:cffd: %s", p, err)
		}
	}
	return err
}

func (p *OwRequestData) writeField46(oprot thrift.TProtocol) (err error) {
	if p.Cd != nil {
		if err := oprot.WriteFieldBegin("cd", thrift.LIST, 46); err != nil {
			return fmt.Errorf("%T write field begin error 46:cd: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Cd)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Cd {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 46:cd: %s", p, err)
		}
	}
	return err
}

func (p *OwRequestData) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("num", thrift.I16, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:num: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Num)); err != nil {
		return fmt.Errorf("%T.num (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:num: %s", p, err)
	}
	return err
}

func (p *OwRequestData) writeField48(oprot thrift.TProtocol) (err error) {
	if p.ExtCapabilityList != nil {
		if err := oprot.WriteFieldBegin("extCapabilityList", thrift.LIST, 48); err != nil {
			return fmt.Errorf("%T write field begin error 48:extCapabilityList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExtCapabilityList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExtCapabilityList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 48:extCapabilityList: %s", p, err)
		}
	}
	return err
}

func (p *OwRequestData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwRequestData(%+v)", *p)
}

type OwBriefAd struct {
	Cid             CreativeIdInt        `thrift:"cid,1" json:"cid"`
	ResponseExtinfo *OwUiResponseExtInfo `thrift:"response_extinfo,2" json:"response_extinfo"`
}

func NewOwBriefAd() *OwBriefAd {
	return &OwBriefAd{}
}

func (p *OwBriefAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwBriefAd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Cid = CreativeIdInt(v)
	}
	return nil
}

func (p *OwBriefAd) readField2(iprot thrift.TProtocol) error {
	p.ResponseExtinfo = NewOwUiResponseExtInfo()
	if err := p.ResponseExtinfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ResponseExtinfo)
	}
	return nil
}

func (p *OwBriefAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwBriefAd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwBriefAd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cid: %s", p, err)
	}
	return err
}

func (p *OwBriefAd) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ResponseExtinfo != nil {
		if err := oprot.WriteFieldBegin("response_extinfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:response_extinfo: %s", p, err)
		}
		if err := p.ResponseExtinfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ResponseExtinfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:response_extinfo: %s", p, err)
		}
	}
	return err
}

func (p *OwBriefAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwBriefAd(%+v)", *p)
}

type OwUiRequest struct {
	RequestData *OwRequestData                    `thrift:"requestData,1" json:"requestData"`
	RawAdList   []*OwRawAd                        `thrift:"rawAdList,2" json:"rawAdList"`
	RankType    OwRankType                        `thrift:"rankType,3" json:"rankType"`
	MediaInfo   *offerwall_info_types.OwMediaInfo `thrift:"media_info,4" json:"media_info"`
}

func NewOwUiRequest() *OwUiRequest {
	return &OwUiRequest{
		RankType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwUiRequest) IsSetRankType() bool {
	return int64(p.RankType) != math.MinInt32-1
}

func (p *OwUiRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwUiRequest) readField1(iprot thrift.TProtocol) error {
	p.RequestData = NewOwRequestData()
	if err := p.RequestData.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestData)
	}
	return nil
}

func (p *OwUiRequest) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RawAdList = make([]*OwRawAd, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewOwRawAd()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.RawAdList = append(p.RawAdList, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwUiRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RankType = OwRankType(v)
	}
	return nil
}

func (p *OwUiRequest) readField4(iprot thrift.TProtocol) error {
	p.MediaInfo = offerwall_info_types.NewOwMediaInfo()
	if err := p.MediaInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaInfo)
	}
	return nil
}

func (p *OwUiRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwUiRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwUiRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestData: %s", p, err)
		}
		if err := p.RequestData.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestData)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestData: %s", p, err)
		}
	}
	return err
}

func (p *OwUiRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RawAdList != nil {
		if err := oprot.WriteFieldBegin("rawAdList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:rawAdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RawAdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RawAdList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:rawAdList: %s", p, err)
		}
	}
	return err
}

func (p *OwUiRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRankType() {
		if err := oprot.WriteFieldBegin("rankType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:rankType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RankType)); err != nil {
			return fmt.Errorf("%T.rankType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:rankType: %s", p, err)
		}
	}
	return err
}

func (p *OwUiRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.MediaInfo != nil {
		if err := oprot.WriteFieldBegin("media_info", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:media_info: %s", p, err)
		}
		if err := p.MediaInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:media_info: %s", p, err)
		}
	}
	return err
}

func (p *OwUiRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwUiRequest(%+v)", *p)
}

type OwUiResponse struct {
	Status        int32        `thrift:"status,1" json:"status"`
	OrderedAdList []*OwBriefAd `thrift:"orderedAdList,2" json:"orderedAdList"`
	SearchId      LargeIdInt   `thrift:"searchId,3" json:"searchId"`
	ExpId         string       `thrift:"exp_id,4" json:"exp_id"`
}

func NewOwUiResponse() *OwUiResponse {
	return &OwUiResponse{}
}

func (p *OwUiResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwUiResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwUiResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderedAdList = make([]*OwBriefAd, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewOwBriefAd()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.OrderedAdList = append(p.OrderedAdList, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwUiResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *OwUiResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExpId = v
	}
	return nil
}

func (p *OwUiResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwUiResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwUiResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *OwUiResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OrderedAdList != nil {
		if err := oprot.WriteFieldBegin("orderedAdList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderedAdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OrderedAdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderedAdList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderedAdList: %s", p, err)
		}
	}
	return err
}

func (p *OwUiResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:searchId: %s", p, err)
	}
	return err
}

func (p *OwUiResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exp_id", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:exp_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExpId)); err != nil {
		return fmt.Errorf("%T.exp_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:exp_id: %s", p, err)
	}
	return err
}

func (p *OwUiResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwUiResponse(%+v)", *p)
}

type OwUiCacheResponse struct {
	Status            int32            `thrift:"status,1" json:"status"`
	ResourceGroupList []*ResourceGroup `thrift:"resourceGroupList,2" json:"resourceGroupList"`
	SearchId          LargeIdInt       `thrift:"searchId,3" json:"searchId"`
	ExpId             string           `thrift:"exp_id,4" json:"exp_id"`
}

func NewOwUiCacheResponse() *OwUiCacheResponse {
	return &OwUiCacheResponse{}
}

func (p *OwUiCacheResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwUiCacheResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwUiCacheResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResourceGroupList = make([]*ResourceGroup, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewResourceGroup()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.ResourceGroupList = append(p.ResourceGroupList, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwUiCacheResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *OwUiCacheResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExpId = v
	}
	return nil
}

func (p *OwUiCacheResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwUiCacheResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwUiCacheResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *OwUiCacheResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ResourceGroupList != nil {
		if err := oprot.WriteFieldBegin("resourceGroupList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:resourceGroupList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceGroupList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResourceGroupList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:resourceGroupList: %s", p, err)
		}
	}
	return err
}

func (p *OwUiCacheResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:searchId: %s", p, err)
	}
	return err
}

func (p *OwUiCacheResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exp_id", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:exp_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExpId)); err != nil {
		return fmt.Errorf("%T.exp_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:exp_id: %s", p, err)
	}
	return err
}

func (p *OwUiCacheResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwUiCacheResponse(%+v)", *p)
}
