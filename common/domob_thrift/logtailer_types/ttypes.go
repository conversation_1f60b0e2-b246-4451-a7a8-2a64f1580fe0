// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package logtailer_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type LogTailerThreadStat struct {
	StreamName      string `thrift:"stream_name,1" json:"stream_name"`
	StreamReader    string `thrift:"stream_reader,2" json:"stream_reader"`
	StreamSender    string `thrift:"stream_sender,3" json:"stream_sender"`
	StartTime       int32  `thrift:"start_time,4" json:"start_time"`
	StopTime        int32  `thrift:"stop_time,5" json:"stop_time"`
	Status          string `thrift:"status,6" json:"status"`
	WarningMessage  string `thrift:"warning_message,7" json:"warning_message"`
	ErrorMessage    string `thrift:"error_message,8" json:"error_message"`
	BytesRead       int64  `thrift:"bytes_read,9" json:"bytes_read"`
	MessageSent     int64  `thrift:"message_sent,10" json:"message_sent"`
	ReaderTopicName string `thrift:"reader_topic_name,11" json:"reader_topic_name"`
	ReaderGroup     string `thrift:"reader_group,12" json:"reader_group"`
	// unused field # 13
	// unused field # 14
	CurrentFile    string `thrift:"current_file,15" json:"current_file"`
	Offset         int64  `thrift:"offset,16" json:"offset"`
	FilesProcessed int32  `thrift:"files_processed,17" json:"files_processed"`
	// unused field # 18
	// unused field # 19
	ReadErrorCount  int32  `thrift:"read_error_count,20" json:"read_error_count"`
	SenderTopicName string `thrift:"sender_topic_name,21" json:"sender_topic_name"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	StreamScript     string `thrift:"stream_script,25" json:"stream_script"`
	InlineClass      string `thrift:"inline_class,26" json:"inline_class"`
	ThriftServerPort string `thrift:"thrift_server_port,27" json:"thrift_server_port"`
	// unused field # 28
	// unused field # 29
	SendErrorCount             int64  `thrift:"send_error_count,30" json:"send_error_count"`
	ThreadName                 string `thrift:"thread_name,31" json:"thread_name"`
	SenderKafkaAcks            string `thrift:"sender_kafka_acks,32" json:"sender_kafka_acks"`
	SenderKafkaRetries         int32  `thrift:"sender_kafka_retries,33" json:"sender_kafka_retries"`
	ReaderKafkaAutoOffsetReset string `thrift:"reader_kafka_auto_offset_reset,34" json:"reader_kafka_auto_offset_reset"`
	StreamResponsors           string `thrift:"stream_responsors,35" json:"stream_responsors"`
	StreamWatchers             string `thrift:"stream_watchers,36" json:"stream_watchers"`
	ReaderLowThresholdDaily    int64  `thrift:"reader_low_threshold_daily,37" json:"reader_low_threshold_daily"`
	ReaderLowThresholdHourly   int64  `thrift:"reader_low_threshold_hourly,38" json:"reader_low_threshold_hourly"`
	ReaderLagHour              int32  `thrift:"reader_lag_hour,39" json:"reader_lag_hour"`
	LogLagByte                 int64  `thrift:"log_lag_byte,40" json:"log_lag_byte"`
}

func NewLogTailerThreadStat() *LogTailerThreadStat {
	return &LogTailerThreadStat{}
}

func (p *LogTailerThreadStat) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I64 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I64 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LogTailerThreadStat) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StreamName = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StreamReader = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StreamSender = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StopTime = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.WarningMessage = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ErrorMessage = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.BytesRead = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MessageSent = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ReaderTopicName = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ReaderGroup = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.CurrentFile = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.FilesProcessed = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ReadErrorCount = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.SenderTopicName = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.StreamScript = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.InlineClass = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.ThriftServerPort = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.SendErrorCount = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.ThreadName = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.SenderKafkaAcks = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.SenderKafkaRetries = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.ReaderKafkaAutoOffsetReset = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.StreamResponsors = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.StreamWatchers = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.ReaderLowThresholdDaily = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.ReaderLowThresholdHourly = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.ReaderLagHour = v
	}
	return nil
}

func (p *LogTailerThreadStat) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.LogLagByte = v
	}
	return nil
}

func (p *LogTailerThreadStat) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LogTailerThreadStat"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LogTailerThreadStat) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:stream_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamName)); err != nil {
		return fmt.Errorf("%T.stream_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:stream_name: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_reader", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:stream_reader: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamReader)); err != nil {
		return fmt.Errorf("%T.stream_reader (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:stream_reader: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_sender", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stream_sender: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamSender)); err != nil {
		return fmt.Errorf("%T.stream_sender (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stream_sender: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:start_time: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stop_time", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:stop_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StopTime)); err != nil {
		return fmt.Errorf("%T.stop_time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:stop_time: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Status)); err != nil {
		return fmt.Errorf("%T.status (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:status: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("warning_message", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:warning_message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WarningMessage)); err != nil {
		return fmt.Errorf("%T.warning_message (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:warning_message: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("error_message", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:error_message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrorMessage)); err != nil {
		return fmt.Errorf("%T.error_message (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:error_message: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bytes_read", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:bytes_read: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.BytesRead)); err != nil {
		return fmt.Errorf("%T.bytes_read (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:bytes_read: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message_sent", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:message_sent: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MessageSent)); err != nil {
		return fmt.Errorf("%T.message_sent (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:message_sent: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reader_topic_name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:reader_topic_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReaderTopicName)); err != nil {
		return fmt.Errorf("%T.reader_topic_name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:reader_topic_name: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reader_group", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:reader_group: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReaderGroup)); err != nil {
		return fmt.Errorf("%T.reader_group (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:reader_group: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current_file", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:current_file: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrentFile)); err != nil {
		return fmt.Errorf("%T.current_file (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:current_file: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:offset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:offset: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("files_processed", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:files_processed: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FilesProcessed)); err != nil {
		return fmt.Errorf("%T.files_processed (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:files_processed: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("read_error_count", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:read_error_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReadErrorCount)); err != nil {
		return fmt.Errorf("%T.read_error_count (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:read_error_count: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sender_topic_name", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:sender_topic_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SenderTopicName)); err != nil {
		return fmt.Errorf("%T.sender_topic_name (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:sender_topic_name: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_script", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:stream_script: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamScript)); err != nil {
		return fmt.Errorf("%T.stream_script (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:stream_script: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("inline_class", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:inline_class: %s", p, err)
	}
	if err := oprot.WriteString(string(p.InlineClass)); err != nil {
		return fmt.Errorf("%T.inline_class (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:inline_class: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thrift_server_port", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:thrift_server_port: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ThriftServerPort)); err != nil {
		return fmt.Errorf("%T.thrift_server_port (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:thrift_server_port: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("send_error_count", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:send_error_count: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SendErrorCount)); err != nil {
		return fmt.Errorf("%T.send_error_count (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:send_error_count: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thread_name", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:thread_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ThreadName)); err != nil {
		return fmt.Errorf("%T.thread_name (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:thread_name: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sender_kafka_acks", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:sender_kafka_acks: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SenderKafkaAcks)); err != nil {
		return fmt.Errorf("%T.sender_kafka_acks (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:sender_kafka_acks: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sender_kafka_retries", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:sender_kafka_retries: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SenderKafkaRetries)); err != nil {
		return fmt.Errorf("%T.sender_kafka_retries (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:sender_kafka_retries: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reader_kafka_auto_offset_reset", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:reader_kafka_auto_offset_reset: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReaderKafkaAutoOffsetReset)); err != nil {
		return fmt.Errorf("%T.reader_kafka_auto_offset_reset (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:reader_kafka_auto_offset_reset: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_responsors", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:stream_responsors: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamResponsors)); err != nil {
		return fmt.Errorf("%T.stream_responsors (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:stream_responsors: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_watchers", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:stream_watchers: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamWatchers)); err != nil {
		return fmt.Errorf("%T.stream_watchers (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:stream_watchers: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reader_low_threshold_daily", thrift.I64, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:reader_low_threshold_daily: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReaderLowThresholdDaily)); err != nil {
		return fmt.Errorf("%T.reader_low_threshold_daily (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:reader_low_threshold_daily: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reader_low_threshold_hourly", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:reader_low_threshold_hourly: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReaderLowThresholdHourly)); err != nil {
		return fmt.Errorf("%T.reader_low_threshold_hourly (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:reader_low_threshold_hourly: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reader_lag_hour", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:reader_lag_hour: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReaderLagHour)); err != nil {
		return fmt.Errorf("%T.reader_lag_hour (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:reader_lag_hour: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("log_lag_byte", thrift.I64, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:log_lag_byte: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LogLagByte)); err != nil {
		return fmt.Errorf("%T.log_lag_byte (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:log_lag_byte: %s", p, err)
	}
	return err
}

func (p *LogTailerThreadStat) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogTailerThreadStat(%+v)", *p)
}

type LogTailerAppStat struct {
	Hostname  string `thrift:"hostname,1" json:"hostname"`
	Port      int32  `thrift:"port,2" json:"port"`
	Pid       int32  `thrift:"pid,3" json:"pid"`
	SnapTime  int32  `thrift:"snap_time,4" json:"snap_time"`
	User      string `thrift:"user,5" json:"user"`
	ServiceId string `thrift:"serviceId,6" json:"serviceId"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	TotalThreads         int32 `thrift:"total_threads,10" json:"total_threads"`
	ErrorThreadsNum      int32 `thrift:"error_threads_num,11" json:"error_threads_num"`
	TotalBytesRead       int64 `thrift:"total_bytes_read,12" json:"total_bytes_read"`
	TotalFilesProccessed int32 `thrift:"total_files_proccessed,13" json:"total_files_proccessed"`
	StartTime            int32 `thrift:"start_time,14" json:"start_time"`
	TotalMessagesSend    int64 `thrift:"total_messages_send,15" json:"total_messages_send"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	ThreadStats []*LogTailerThreadStat `thrift:"thread_stats,20" json:"thread_stats"`
}

func NewLogTailerAppStat() *LogTailerAppStat {
	return &LogTailerAppStat{}
}

func (p *LogTailerAppStat) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LogTailerAppStat) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Hostname = v
	}
	return nil
}

func (p *LogTailerAppStat) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Port = v
	}
	return nil
}

func (p *LogTailerAppStat) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *LogTailerAppStat) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SnapTime = v
	}
	return nil
}

func (p *LogTailerAppStat) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.User = v
	}
	return nil
}

func (p *LogTailerAppStat) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ServiceId = v
	}
	return nil
}

func (p *LogTailerAppStat) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TotalThreads = v
	}
	return nil
}

func (p *LogTailerAppStat) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ErrorThreadsNum = v
	}
	return nil
}

func (p *LogTailerAppStat) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TotalBytesRead = v
	}
	return nil
}

func (p *LogTailerAppStat) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.TotalFilesProccessed = v
	}
	return nil
}

func (p *LogTailerAppStat) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *LogTailerAppStat) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.TotalMessagesSend = v
	}
	return nil
}

func (p *LogTailerAppStat) readField20(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ThreadStats = make([]*LogTailerThreadStat, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewLogTailerThreadStat()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.ThreadStats = append(p.ThreadStats, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LogTailerAppStat) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LogTailerAppStat"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LogTailerAppStat) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostname", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:hostname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostname)); err != nil {
		return fmt.Errorf("%T.hostname (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:hostname: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("port", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:port: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Port)); err != nil {
		return fmt.Errorf("%T.port (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:port: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pid: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("snap_time", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:snap_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SnapTime)); err != nil {
		return fmt.Errorf("%T.snap_time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:snap_time: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:user: %s", p, err)
	}
	if err := oprot.WriteString(string(p.User)); err != nil {
		return fmt.Errorf("%T.user (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:user: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("serviceId", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:serviceId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServiceId)); err != nil {
		return fmt.Errorf("%T.serviceId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:serviceId: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_threads", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:total_threads: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalThreads)); err != nil {
		return fmt.Errorf("%T.total_threads (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:total_threads: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("error_threads_num", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:error_threads_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ErrorThreadsNum)); err != nil {
		return fmt.Errorf("%T.error_threads_num (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:error_threads_num: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_bytes_read", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:total_bytes_read: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBytesRead)); err != nil {
		return fmt.Errorf("%T.total_bytes_read (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:total_bytes_read: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_files_proccessed", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:total_files_proccessed: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalFilesProccessed)); err != nil {
		return fmt.Errorf("%T.total_files_proccessed (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:total_files_proccessed: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:start_time: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_messages_send", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:total_messages_send: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalMessagesSend)); err != nil {
		return fmt.Errorf("%T.total_messages_send (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:total_messages_send: %s", p, err)
	}
	return err
}

func (p *LogTailerAppStat) writeField20(oprot thrift.TProtocol) (err error) {
	if p.ThreadStats != nil {
		if err := oprot.WriteFieldBegin("thread_stats", thrift.LIST, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:thread_stats: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ThreadStats)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ThreadStats {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:thread_stats: %s", p, err)
		}
	}
	return err
}

func (p *LogTailerAppStat) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogTailerAppStat(%+v)", *p)
}

type Message struct {
	Message    []byte `thrift:"message,1" json:"message"`
	StreamName string `thrift:"stream_name,2" json:"stream_name"`
}

func NewMessage() *Message {
	return &Message{}
}

func (p *Message) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Message) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *Message) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StreamName = v
	}
	return nil
}

func (p *Message) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Message"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Message) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Message != nil {
		if err := oprot.WriteFieldBegin("message", thrift.STRING, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Message); err != nil {
			return fmt.Errorf("%T.message (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:message: %s", p, err)
		}
	}
	return err
}

func (p *Message) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stream_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:stream_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamName)); err != nil {
		return fmt.Errorf("%T.stream_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:stream_name: %s", p, err)
	}
	return err
}

func (p *Message) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Message(%+v)", *p)
}
