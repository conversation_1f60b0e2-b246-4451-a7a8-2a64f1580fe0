// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"sos_activity_server"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   get_activity_list(RequestHeader header, i64 uid, DisplayType displayType)")
	fmt.Fprintln(os.Stderr, "  CommonResponse active_an_activity(RequestHeader header, i32 activity_id, i64 uid)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := sos_activity_server.NewActivityServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "get_activity_list":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetActivityList requires 3 args")
			flag.Usage()
		}
		arg12 := flag.Arg(1)
		mbTrans13 := thrift.NewTMemoryBufferLen(len(arg12))
		defer mbTrans13.Close()
		_, err14 := mbTrans13.WriteString(arg12)
		if err14 != nil {
			Usage()
			return
		}
		factory15 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt16 := factory15.GetProtocol(mbTrans13)
		argvalue0 := sos_activity_server.NewRequestHeader()
		err17 := argvalue0.Read(jsProt16)
		if err17 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err18 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err18 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := sos_activity_server.DisplayType(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetActivityList(value0, value1, value2))
		fmt.Print("\n")
		break
	case "active_an_activity":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ActiveAnActivity requires 3 args")
			flag.Usage()
		}
		arg19 := flag.Arg(1)
		mbTrans20 := thrift.NewTMemoryBufferLen(len(arg19))
		defer mbTrans20.Close()
		_, err21 := mbTrans20.WriteString(arg19)
		if err21 != nil {
			Usage()
			return
		}
		factory22 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt23 := factory22.GetProtocol(mbTrans20)
		argvalue0 := sos_activity_server.NewRequestHeader()
		err24 := argvalue0.Read(jsProt23)
		if err24 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err25 := (strconv.Atoi(flag.Arg(2)))
		if err25 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err26 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err26 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ActiveAnActivity(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
