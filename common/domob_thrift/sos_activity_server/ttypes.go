// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_activity_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type ActivityType int64

const (
	ActivityType_AddPoint      ActivityType = 1
	ActivityType_UnlockAdjuest ActivityType = 2
)

func (p ActivityType) String() string {
	switch p {
	case ActivityType_AddPoint:
		return "ActivityType_AddPoint"
	case ActivityType_UnlockAdjuest:
		return "ActivityType_UnlockAdjuest"
	}
	return "<UNSET>"
}

func ActivityTypeFromString(s string) (ActivityType, error) {
	switch s {
	case "ActivityType_AddPoint":
		return ActivityType_AddPoint, nil
	case "ActivityType_UnlockAdjuest":
		return ActivityType_UnlockAdjuest, nil
	}
	return ActivityType(math.MinInt32 - 1), fmt.Errorf("not a valid ActivityType string")
}

type DisplayType int64

const (
	DisplayType_Common DisplayType = 0
	DisplayType_InApp  DisplayType = 1
)

func (p DisplayType) String() string {
	switch p {
	case DisplayType_Common:
		return "DisplayType_Common"
	case DisplayType_InApp:
		return "DisplayType_InApp"
	}
	return "<UNSET>"
}

func DisplayTypeFromString(s string) (DisplayType, error) {
	switch s {
	case "DisplayType_Common":
		return DisplayType_Common, nil
	case "DisplayType_InApp":
		return DisplayType_InApp, nil
	}
	return DisplayType(math.MinInt32 - 1), fmt.Errorf("not a valid DisplayType string")
}

type RequiredType int64

const (
	RequiredType_DOWNLOAD_NUM RequiredType = 1
	RequiredType_SIGN_NUM     RequiredType = 2
	RequiredType_INVITE_NUM   RequiredType = 3
)

func (p RequiredType) String() string {
	switch p {
	case RequiredType_DOWNLOAD_NUM:
		return "RequiredType_DOWNLOAD_NUM"
	case RequiredType_SIGN_NUM:
		return "RequiredType_SIGN_NUM"
	case RequiredType_INVITE_NUM:
		return "RequiredType_INVITE_NUM"
	}
	return "<UNSET>"
}

func RequiredTypeFromString(s string) (RequiredType, error) {
	switch s {
	case "RequiredType_DOWNLOAD_NUM":
		return RequiredType_DOWNLOAD_NUM, nil
	case "RequiredType_SIGN_NUM":
		return RequiredType_SIGN_NUM, nil
	case "RequiredType_INVITE_NUM":
		return RequiredType_INVITE_NUM, nil
	}
	return RequiredType(math.MinInt32 - 1), fmt.Errorf("not a valid RequiredType string")
}

type ActivityStatus int64

const (
	ActivityStatus_UnFinish    ActivityStatus = 1
	ActivityStatus_ToBeActived ActivityStatus = 2
	ActivityStatus_Actived     ActivityStatus = 3
)

func (p ActivityStatus) String() string {
	switch p {
	case ActivityStatus_UnFinish:
		return "ActivityStatus_UnFinish"
	case ActivityStatus_ToBeActived:
		return "ActivityStatus_ToBeActived"
	case ActivityStatus_Actived:
		return "ActivityStatus_Actived"
	}
	return "<UNSET>"
}

func ActivityStatusFromString(s string) (ActivityStatus, error) {
	switch s {
	case "ActivityStatus_UnFinish":
		return ActivityStatus_UnFinish, nil
	case "ActivityStatus_ToBeActived":
		return ActivityStatus_ToBeActived, nil
	case "ActivityStatus_Actived":
		return ActivityStatus_Actived, nil
	}
	return ActivityStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ActivityStatus string")
}

type StatusCode int64

const (
	StatusCode_StatusOk    StatusCode = 0
	StatusCode_StatusError StatusCode = 1
)

func (p StatusCode) String() string {
	switch p {
	case StatusCode_StatusOk:
		return "StatusCode_StatusOk"
	case StatusCode_StatusError:
		return "StatusCode_StatusError"
	}
	return "<UNSET>"
}

func StatusCodeFromString(s string) (StatusCode, error) {
	switch s {
	case "StatusCode_StatusOk":
		return StatusCode_StatusOk, nil
	case "StatusCode_StatusError":
		return StatusCode_StatusError, nil
	}
	return StatusCode(math.MinInt32 - 1), fmt.Errorf("not a valid StatusCode string")
}

type CommonResponse struct {
	Status  StatusCode `thrift:"status,1" json:"status"`
	Message string     `thrift:"message,2" json:"message"`
}

func NewCommonResponse() *CommonResponse {
	return &CommonResponse{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CommonResponse) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *CommonResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CommonResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = StatusCode(v)
	}
	return nil
}

func (p *CommonResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *CommonResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CommonResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CommonResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *CommonResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *CommonResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonResponse(%+v)", *p)
}

type ActivityObj struct {
	Id               int32          `thrift:"id,1" json:"id"`
	DisplayName      string         `thrift:"display_name,2" json:"display_name"`
	Desc             []string       `thrift:"desc,3" json:"desc"`
	IconUrl          string         `thrift:"icon_url,4" json:"icon_url"`
	ActivityType     ActivityType   `thrift:"activity_type,5" json:"activity_type"`
	DisplayType      DisplayType    `thrift:"display_type,6" json:"display_type"`
	RequiredNum      int32          `thrift:"required_num,7" json:"required_num"`
	StartTime        int32          `thrift:"start_time,8" json:"start_time"`
	StopTime         int32          `thrift:"stop_time,9" json:"stop_time"`
	RealFinish       int32          `thrift:"real_finish,10" json:"real_finish"`
	Status           ActivityStatus `thrift:"status,11" json:"status"`
	RewardPoint      float64        `thrift:"reward_point,12" json:"reward_point"`
	RewardNumLimit   int32          `thrift:"reward_num_limit,13" json:"reward_num_limit"`
	RewardPointLimit int32          `thrift:"reward_point_limit,14" json:"reward_point_limit"`
	RealIncome       float64        `thrift:"real_income,15" json:"real_income"`
	RealRewardNum    int32          `thrift:"real_reward_num,16" json:"real_reward_num"`
	RequiredDesc     string         `thrift:"required_desc,17" json:"required_desc"`
	Order            int32          `thrift:"order,18" json:"order"`
	H5ActivityUrl    string         `thrift:"h5_activity_url,19" json:"h5_activity_url"`
}

func NewActivityObj() *ActivityObj {
	return &ActivityObj{
		ActivityType: math.MinInt32 - 1, // unset sentinal value

		DisplayType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ActivityObj) IsSetActivityType() bool {
	return int64(p.ActivityType) != math.MinInt32-1
}

func (p *ActivityObj) IsSetDisplayType() bool {
	return int64(p.DisplayType) != math.MinInt32-1
}

func (p *ActivityObj) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ActivityObj) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ActivityObj) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ActivityObj) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *ActivityObj) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Desc = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Desc = append(p.Desc, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ActivityObj) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IconUrl = v
	}
	return nil
}

func (p *ActivityObj) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ActivityType = ActivityType(v)
	}
	return nil
}

func (p *ActivityObj) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DisplayType = DisplayType(v)
	}
	return nil
}

func (p *ActivityObj) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.RequiredNum = v
	}
	return nil
}

func (p *ActivityObj) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *ActivityObj) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.StopTime = v
	}
	return nil
}

func (p *ActivityObj) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RealFinish = v
	}
	return nil
}

func (p *ActivityObj) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Status = ActivityStatus(v)
	}
	return nil
}

func (p *ActivityObj) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.RewardPoint = v
	}
	return nil
}

func (p *ActivityObj) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RewardNumLimit = v
	}
	return nil
}

func (p *ActivityObj) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.RewardPointLimit = v
	}
	return nil
}

func (p *ActivityObj) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.RealIncome = v
	}
	return nil
}

func (p *ActivityObj) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.RealRewardNum = v
	}
	return nil
}

func (p *ActivityObj) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.RequiredDesc = v
	}
	return nil
}

func (p *ActivityObj) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Order = v
	}
	return nil
}

func (p *ActivityObj) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.H5ActivityUrl = v
	}
	return nil
}

func (p *ActivityObj) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ActivityObj"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ActivityObj) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:display_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.display_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:display_name: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Desc != nil {
		if err := oprot.WriteFieldBegin("desc", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:desc: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Desc)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Desc {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:desc: %s", p, err)
		}
	}
	return err
}

func (p *ActivityObj) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon_url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:icon_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IconUrl)); err != nil {
		return fmt.Errorf("%T.icon_url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:icon_url: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetActivityType() {
		if err := oprot.WriteFieldBegin("activity_type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:activity_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ActivityType)); err != nil {
			return fmt.Errorf("%T.activity_type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:activity_type: %s", p, err)
		}
	}
	return err
}

func (p *ActivityObj) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDisplayType() {
		if err := oprot.WriteFieldBegin("display_type", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:display_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
			return fmt.Errorf("%T.display_type (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:display_type: %s", p, err)
		}
	}
	return err
}

func (p *ActivityObj) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("required_num", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:required_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RequiredNum)); err != nil {
		return fmt.Errorf("%T.required_num (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:required_num: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:start_time: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stop_time", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:stop_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StopTime)); err != nil {
		return fmt.Errorf("%T.stop_time (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:stop_time: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("real_finish", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:real_finish: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RealFinish)); err != nil {
		return fmt.Errorf("%T.real_finish (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:real_finish: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:status: %s", p, err)
		}
	}
	return err
}

func (p *ActivityObj) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reward_point", thrift.DOUBLE, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:reward_point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.RewardPoint)); err != nil {
		return fmt.Errorf("%T.reward_point (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:reward_point: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reward_num_limit", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:reward_num_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RewardNumLimit)); err != nil {
		return fmt.Errorf("%T.reward_num_limit (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:reward_num_limit: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reward_point_limit", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:reward_point_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RewardPointLimit)); err != nil {
		return fmt.Errorf("%T.reward_point_limit (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:reward_point_limit: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("real_income", thrift.DOUBLE, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:real_income: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.RealIncome)); err != nil {
		return fmt.Errorf("%T.real_income (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:real_income: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("real_reward_num", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:real_reward_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RealRewardNum)); err != nil {
		return fmt.Errorf("%T.real_reward_num (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:real_reward_num: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("required_desc", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:required_desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RequiredDesc)); err != nil {
		return fmt.Errorf("%T.required_desc (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:required_desc: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("order", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:order: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Order)); err != nil {
		return fmt.Errorf("%T.order (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:order: %s", p, err)
	}
	return err
}

func (p *ActivityObj) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("h5_activity_url", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:h5_activity_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.H5ActivityUrl)); err != nil {
		return fmt.Errorf("%T.h5_activity_url (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:h5_activity_url: %s", p, err)
	}
	return err
}

func (p *ActivityObj) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ActivityObj(%+v)", *p)
}
