// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package image_assembly_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/ugc"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = ugc.GoUnusedProtection__
var GoUnusedProtection__ int

//请求后返回的状态
type RequestStatus int64

const (
	RequestStatus_R_SUCCESS      RequestStatus = 200
	RequestStatus_R_PARAM_ERROR  RequestStatus = 400
	RequestStatus_R_SERVER_ERROR RequestStatus = 500
)

func (p RequestStatus) String() string {
	switch p {
	case RequestStatus_R_SUCCESS:
		return "RequestStatus_R_SUCCESS"
	case RequestStatus_R_PARAM_ERROR:
		return "RequestStatus_R_PARAM_ERROR"
	case RequestStatus_R_SERVER_ERROR:
		return "RequestStatus_R_SERVER_ERROR"
	}
	return "<UNSET>"
}

func RequestStatusFromString(s string) (RequestStatus, error) {
	switch s {
	case "RequestStatus_R_SUCCESS":
		return RequestStatus_R_SUCCESS, nil
	case "RequestStatus_R_PARAM_ERROR":
		return RequestStatus_R_PARAM_ERROR, nil
	case "RequestStatus_R_SERVER_ERROR":
		return RequestStatus_R_SERVER_ERROR, nil
	}
	return RequestStatus(math.MinInt32 - 1), fmt.Errorf("not a valid RequestStatus string")
}

//图片的格式
type ImgFormat int64

const (
	ImgFormat_PNG  ImgFormat = 1
	ImgFormat_JPEG ImgFormat = 2
	ImgFormat_BMP  ImgFormat = 3
	ImgFormat_GIF  ImgFormat = 4
	ImgFormat_MP4  ImgFormat = 5
)

func (p ImgFormat) String() string {
	switch p {
	case ImgFormat_PNG:
		return "ImgFormat_PNG"
	case ImgFormat_JPEG:
		return "ImgFormat_JPEG"
	case ImgFormat_BMP:
		return "ImgFormat_BMP"
	case ImgFormat_GIF:
		return "ImgFormat_GIF"
	case ImgFormat_MP4:
		return "ImgFormat_MP4"
	}
	return "<UNSET>"
}

func ImgFormatFromString(s string) (ImgFormat, error) {
	switch s {
	case "ImgFormat_PNG":
		return ImgFormat_PNG, nil
	case "ImgFormat_JPEG":
		return ImgFormat_JPEG, nil
	case "ImgFormat_BMP":
		return ImgFormat_BMP, nil
	case "ImgFormat_GIF":
		return ImgFormat_GIF, nil
	case "ImgFormat_MP4":
		return ImgFormat_MP4, nil
	}
	return ImgFormat(math.MinInt32 - 1), fmt.Errorf("not a valid ImgFormat string")
}

//返回的图片数据的类型
type ImgResultType int64

const (
	ImgResultType_BIN    ImgResultType = 1
	ImgResultType_UGC    ImgResultType = 2
	ImgResultType_QINIU  ImgResultType = 3
	ImgResultType_BASE64 ImgResultType = 4
)

func (p ImgResultType) String() string {
	switch p {
	case ImgResultType_BIN:
		return "ImgResultType_BIN"
	case ImgResultType_UGC:
		return "ImgResultType_UGC"
	case ImgResultType_QINIU:
		return "ImgResultType_QINIU"
	case ImgResultType_BASE64:
		return "ImgResultType_BASE64"
	}
	return "<UNSET>"
}

func ImgResultTypeFromString(s string) (ImgResultType, error) {
	switch s {
	case "ImgResultType_BIN":
		return ImgResultType_BIN, nil
	case "ImgResultType_UGC":
		return ImgResultType_UGC, nil
	case "ImgResultType_QINIU":
		return ImgResultType_QINIU, nil
	case "ImgResultType_BASE64":
		return ImgResultType_BASE64, nil
	}
	return ImgResultType(math.MinInt32 - 1), fmt.Errorf("not a valid ImgResultType string")
}

type RequestHeader struct {
	Requester string `thrift:"requester,1" json:"requester"`
	SearchId  int64  `thrift:"search_id,2" json:"search_id"`
}

func NewRequestHeader() *RequestHeader {
	return &RequestHeader{}
}

func (p *RequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Requester = v
	}
	return nil
}

func (p *RequestHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *RequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requester", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:requester: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Requester)); err != nil {
		return fmt.Errorf("%T.requester (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:requester: %s", p, err)
	}
	return err
}

func (p *RequestHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_id: %s", p, err)
	}
	return err
}

func (p *RequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RequestHeader(%+v)", *p)
}

type ServerException struct {
	Code RequestStatus `thrift:"code,1" json:"code"`
	Msg  string        `thrift:"msg,2" json:"msg"`
}

func NewServerException() *ServerException {
	return &ServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *ServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = RequestStatus(v)
	}
	return nil
}

func (p *ServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *ServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *ServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *ServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ServerException(%+v)", *p)
}

type Html2ImgRequest struct {
	Html       string        `thrift:"html,1" json:"html"`
	Width      int32         `thrift:"width,2" json:"width"`
	Height     int32         `thrift:"height,3" json:"height"`
	ImgFormat  ImgFormat     `thrift:"imgFormat,4" json:"imgFormat"`
	ResultType ImgResultType `thrift:"resultType,5" json:"resultType"`
	MaxSize    int32         `thrift:"maxSize,6" json:"maxSize"`
}

func NewHtml2ImgRequest() *Html2ImgRequest {
	return &Html2ImgRequest{
		ImgFormat: math.MinInt32 - 1, // unset sentinal value

		ResultType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Html2ImgRequest) IsSetImgFormat() bool {
	return int64(p.ImgFormat) != math.MinInt32-1
}

func (p *Html2ImgRequest) IsSetResultType() bool {
	return int64(p.ResultType) != math.MinInt32-1
}

func (p *Html2ImgRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Html = v
	}
	return nil
}

func (p *Html2ImgRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *Html2ImgRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *Html2ImgRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImgFormat = ImgFormat(v)
	}
	return nil
}

func (p *Html2ImgRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ResultType = ImgResultType(v)
	}
	return nil
}

func (p *Html2ImgRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MaxSize = v
	}
	return nil
}

func (p *Html2ImgRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Html2ImgRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("html", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:html: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Html)); err != nil {
		return fmt.Errorf("%T.html (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:html: %s", p, err)
	}
	return err
}

func (p *Html2ImgRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:width: %s", p, err)
	}
	return err
}

func (p *Html2ImgRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:height: %s", p, err)
	}
	return err
}

func (p *Html2ImgRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetImgFormat() {
		if err := oprot.WriteFieldBegin("imgFormat", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:imgFormat: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ImgFormat)); err != nil {
			return fmt.Errorf("%T.imgFormat (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:imgFormat: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetResultType() {
		if err := oprot.WriteFieldBegin("resultType", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:resultType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResultType)); err != nil {
			return fmt.Errorf("%T.resultType (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:resultType: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxSize", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:maxSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxSize)); err != nil {
		return fmt.Errorf("%T.maxSize (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:maxSize: %s", p, err)
	}
	return err
}

func (p *Html2ImgRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Html2ImgRequest(%+v)", *p)
}

type Html2ImgResult struct {
	Status     RequestStatus `thrift:"status,1" json:"status"`
	Msg        string        `thrift:"msg,2" json:"msg"`
	BinaryCode string        `thrift:"binary_code,3" json:"binary_code"`
	UgcUrl     string        `thrift:"ugc_url,4" json:"ugc_url"`
	SearchId   int64         `thrift:"search_id,5" json:"search_id"`
}

func NewHtml2ImgResult() *Html2ImgResult {
	return &Html2ImgResult{
		Status: math.MinInt32 - 1, // unset sentinal value

		Msg: "",

		BinaryCode: "",

		UgcUrl: "",
	}
}

func (p *Html2ImgResult) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Html2ImgResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = RequestStatus(v)
	}
	return nil
}

func (p *Html2ImgResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *Html2ImgResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.BinaryCode = v
	}
	return nil
}

func (p *Html2ImgResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UgcUrl = v
	}
	return nil
}

func (p *Html2ImgResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *Html2ImgResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Html2ImgResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *Html2ImgResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("binary_code", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:binary_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BinaryCode)); err != nil {
		return fmt.Errorf("%T.binary_code (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:binary_code: %s", p, err)
	}
	return err
}

func (p *Html2ImgResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ugc_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcUrl)); err != nil {
		return fmt.Errorf("%T.ugc_url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ugc_url: %s", p, err)
	}
	return err
}

func (p *Html2ImgResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:search_id: %s", p, err)
	}
	return err
}

func (p *Html2ImgResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Html2ImgResult(%+v)", *p)
}
