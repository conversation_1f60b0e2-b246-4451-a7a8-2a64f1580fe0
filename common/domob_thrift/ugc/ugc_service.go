// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package ugc

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

type UgcService interface { //用户服务接口

	// 函数功能：存储文件数据
	// 输入参数：
	//   fileData：文件数据
	//   fileFormat：文件类型
	// 返回值：
	//   fileId：文件id
	//   fileEtag：文件摘要
	//   fileLastModified：文件最后修改时间
	//     fileEncodeId: 文件加密ID
	//   response：-1函数出现内部错误，调用失败，可直接联系负责人，0函数调用成功
	//
	// Parameters:
	//  - FileData
	//  - FileFormat
	PutFile(fileData string, fileFormat FormatCode) (r *FileStruct, err error)
	// 函数功能：存储文件数据
	// 输入参数：
	//   fileData：文件数据
	//   fileFormat：文件类型
	//     header: 上传侧提供部分上传信息， 方便出行问题后查到问题所在
	// 返回值：
	//   fileId：文件id
	//   fileEtag：文件摘要
	//   fileLastModified：文件最后修改时间
	//     fileEncodeId: 文件加密ID
	//   response：-1函数出现内部错误，调用失败，可直接联系负责人，0函数调用成功
	//
	// Parameters:
	//  - Header
	//  - FileData
	//  - FileFormat
	PutFileWithHeader(header *RequestHeader, fileData string, fileFormat FormatCode) (r *FileStruct, err error)
	// 函数功能：读取文件数据
	// 输入参数：
	//   fileId：文件id
	//   fileLastModified：文件最后修改时间
	//   fileEtag：文件摘要
	// 返回值：
	// 	  如果函数出现内部错误， 调用失败，response置为-1， 请直接联系负责人
	// 	  如果没找到文件数据，response置为404
	// 	  如果fileLastModified和fileEtag与数据库中的记录一致，response置为304
	// 	  如果fileLastModified和fileEtag与数据库中的记录不一致，response置为200，返回fileLastModified和fileEtag的最新值，并传送文件数据fileData
	//
	// Parameters:
	//  - FileId
	//  - FileLastModified
	//  - FileEtag
	GetFile(fileId int32, fileLastModified string, fileEtag string) (r *FileStruct, err error)
	// 函数功能：通过加密ID读取文件数据
	// 输入参数：
	//   fileEncodeId：文件加密id
	//   fileLastModified：文件最后修改时间
	//   fileEtag：文件摘要
	// 返回值：
	// 	  如果函数出现内部错误， 调用失败，response置为-1， 请直接联系负责人
	// 	  如果没找到文件数据，response置为404
	// 	  如果fileLastModified和fileEtag与数据库中的记录一致，response置为304
	// 	  如果fileLastModified和fileEtag与数据库中的记录不一致，response置为200，返回fileLastModified和fileEtag的最新值，并传送文件数据fileData
	//
	// Parameters:
	//  - FileEncodeId
	//  - FileLastModified
	//  - FileEtag
	GetFileWithEId(fileEncodeId string, fileLastModified string, fileEtag string) (r *FileStruct, err error)
	// 函数功能：查询文件是否已经存在
	// 输入参数：
	// 	fileEtag：文件摘要
	// 返回值：
	// 	如果函数出现内部错误， 返回-2， 请联系负责人
	// 	如果没有该文件对应记录， 返回-1
	// 	如果有对应文件记录， 返回文件id
	//
	// Parameters:
	//  - FileEtag
	CheckFile(fileEtag string) (r int32, err error)
	// 函数功能：通过文件MD5，查询库中文件信息
	// 输入参数：
	// 	fileEtag：文件摘要
	//  返回值：
	//  	  如果函数出现内部错误， 调用失败，response置为-1， 请直接联系负责人
	//  	  如果没找到文件数据，response置为404
	//      返回的fileLastModified目前不可用， 暂时填为""
	//      返回的fileEtag与输入一致
	//
	// Parameters:
	//  - FileEtag
	CheckFileInfo(fileEtag string) (r *FileStruct, err error)
	// 函数功能：删除文件数据
	// 输入参数：
	//   fileId：文件id
	// 返回值：
	//   函数调用失败，返回-1
	//   函数调用成功，返回0
	//
	// Parameters:
	//  - FileId
	DeleteFile(fileId int32) (r int32, err error)
	// 函数功能: 获得最大的文件id
	// 返回值: 最大的文件id, 失败返回 -1
	GetLastId() (r int32, err error)
	// 函数功能: 获得文件id所对应的加密Id
	// 输入参数：
	//     fileId: 文件id
	// 返回值: 文件加密id
	//
	// Parameters:
	//  - FileId
	GetEncodeId(fileId int32) (r string, err error)
}

//用户服务接口
type UgcServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewUgcServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UgcServiceClient {
	return &UgcServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewUgcServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UgcServiceClient {
	return &UgcServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 函数功能：存储文件数据
// 输入参数：
//   fileData：文件数据
//   fileFormat：文件类型
// 返回值：
//   fileId：文件id
//   fileEtag：文件摘要
//   fileLastModified：文件最后修改时间
//     fileEncodeId: 文件加密ID
//   response：-1函数出现内部错误，调用失败，可直接联系负责人，0函数调用成功
//
// Parameters:
//  - FileData
//  - FileFormat
func (p *UgcServiceClient) PutFile(fileData string, fileFormat FormatCode) (r *FileStruct, err error) {
	if err = p.sendPutFile(fileData, fileFormat); err != nil {
		return
	}
	return p.recvPutFile()
}

func (p *UgcServiceClient) sendPutFile(fileData string, fileFormat FormatCode) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("putFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewPutFileArgs()
	args0.FileData = fileData
	args0.FileFormat = fileFormat
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvPutFile() (value *FileStruct, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewPutFileResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 函数功能：存储文件数据
// 输入参数：
//   fileData：文件数据
//   fileFormat：文件类型
//     header: 上传侧提供部分上传信息， 方便出行问题后查到问题所在
// 返回值：
//   fileId：文件id
//   fileEtag：文件摘要
//   fileLastModified：文件最后修改时间
//     fileEncodeId: 文件加密ID
//   response：-1函数出现内部错误，调用失败，可直接联系负责人，0函数调用成功
//
// Parameters:
//  - Header
//  - FileData
//  - FileFormat
func (p *UgcServiceClient) PutFileWithHeader(header *RequestHeader, fileData string, fileFormat FormatCode) (r *FileStruct, err error) {
	if err = p.sendPutFileWithHeader(header, fileData, fileFormat); err != nil {
		return
	}
	return p.recvPutFileWithHeader()
}

func (p *UgcServiceClient) sendPutFileWithHeader(header *RequestHeader, fileData string, fileFormat FormatCode) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("putFileWithHeader", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewPutFileWithHeaderArgs()
	args4.Header = header
	args4.FileData = fileData
	args4.FileFormat = fileFormat
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvPutFileWithHeader() (value *FileStruct, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewPutFileWithHeaderResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// 函数功能：读取文件数据
// 输入参数：
//   fileId：文件id
//   fileLastModified：文件最后修改时间
//   fileEtag：文件摘要
// 返回值：
// 	  如果函数出现内部错误， 调用失败，response置为-1， 请直接联系负责人
// 	  如果没找到文件数据，response置为404
// 	  如果fileLastModified和fileEtag与数据库中的记录一致，response置为304
// 	  如果fileLastModified和fileEtag与数据库中的记录不一致，response置为200，返回fileLastModified和fileEtag的最新值，并传送文件数据fileData
//
// Parameters:
//  - FileId
//  - FileLastModified
//  - FileEtag
func (p *UgcServiceClient) GetFile(fileId int32, fileLastModified string, fileEtag string) (r *FileStruct, err error) {
	if err = p.sendGetFile(fileId, fileLastModified, fileEtag); err != nil {
		return
	}
	return p.recvGetFile()
}

func (p *UgcServiceClient) sendGetFile(fileId int32, fileLastModified string, fileEtag string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetFileArgs()
	args8.FileId = fileId
	args8.FileLastModified = fileLastModified
	args8.FileEtag = fileEtag
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvGetFile() (value *FileStruct, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetFileResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 函数功能：通过加密ID读取文件数据
// 输入参数：
//   fileEncodeId：文件加密id
//   fileLastModified：文件最后修改时间
//   fileEtag：文件摘要
// 返回值：
// 	  如果函数出现内部错误， 调用失败，response置为-1， 请直接联系负责人
// 	  如果没找到文件数据，response置为404
// 	  如果fileLastModified和fileEtag与数据库中的记录一致，response置为304
// 	  如果fileLastModified和fileEtag与数据库中的记录不一致，response置为200，返回fileLastModified和fileEtag的最新值，并传送文件数据fileData
//
// Parameters:
//  - FileEncodeId
//  - FileLastModified
//  - FileEtag
func (p *UgcServiceClient) GetFileWithEId(fileEncodeId string, fileLastModified string, fileEtag string) (r *FileStruct, err error) {
	if err = p.sendGetFileWithEId(fileEncodeId, fileLastModified, fileEtag); err != nil {
		return
	}
	return p.recvGetFileWithEId()
}

func (p *UgcServiceClient) sendGetFileWithEId(fileEncodeId string, fileLastModified string, fileEtag string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFileWithEId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetFileWithEIdArgs()
	args12.FileEncodeId = fileEncodeId
	args12.FileLastModified = fileLastModified
	args12.FileEtag = fileEtag
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvGetFileWithEId() (value *FileStruct, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetFileWithEIdResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 函数功能：查询文件是否已经存在
// 输入参数：
// 	fileEtag：文件摘要
// 返回值：
// 	如果函数出现内部错误， 返回-2， 请联系负责人
// 	如果没有该文件对应记录， 返回-1
// 	如果有对应文件记录， 返回文件id
//
// Parameters:
//  - FileEtag
func (p *UgcServiceClient) CheckFile(fileEtag string) (r int32, err error) {
	if err = p.sendCheckFile(fileEtag); err != nil {
		return
	}
	return p.recvCheckFile()
}

func (p *UgcServiceClient) sendCheckFile(fileEtag string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewCheckFileArgs()
	args16.FileEtag = fileEtag
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvCheckFile() (value int32, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewCheckFileResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 函数功能：通过文件MD5，查询库中文件信息
// 输入参数：
// 	fileEtag：文件摘要
//  返回值：
//  	  如果函数出现内部错误， 调用失败，response置为-1， 请直接联系负责人
//  	  如果没找到文件数据，response置为404
//      返回的fileLastModified目前不可用， 暂时填为""
//      返回的fileEtag与输入一致
//
// Parameters:
//  - FileEtag
func (p *UgcServiceClient) CheckFileInfo(fileEtag string) (r *FileStruct, err error) {
	if err = p.sendCheckFileInfo(fileEtag); err != nil {
		return
	}
	return p.recvCheckFileInfo()
}

func (p *UgcServiceClient) sendCheckFileInfo(fileEtag string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkFileInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewCheckFileInfoArgs()
	args20.FileEtag = fileEtag
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvCheckFileInfo() (value *FileStruct, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewCheckFileInfoResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 函数功能：删除文件数据
// 输入参数：
//   fileId：文件id
// 返回值：
//   函数调用失败，返回-1
//   函数调用成功，返回0
//
// Parameters:
//  - FileId
func (p *UgcServiceClient) DeleteFile(fileId int32) (r int32, err error) {
	if err = p.sendDeleteFile(fileId); err != nil {
		return
	}
	return p.recvDeleteFile()
}

func (p *UgcServiceClient) sendDeleteFile(fileId int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewDeleteFileArgs()
	args24.FileId = fileId
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvDeleteFile() (value int32, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewDeleteFileResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

// 函数功能: 获得最大的文件id
// 返回值: 最大的文件id, 失败返回 -1
func (p *UgcServiceClient) GetLastId() (r int32, err error) {
	if err = p.sendGetLastId(); err != nil {
		return
	}
	return p.recvGetLastId()
}

func (p *UgcServiceClient) sendGetLastId() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getLastId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetLastIdArgs()
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvGetLastId() (value int32, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetLastIdResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	return
}

// 函数功能: 获得文件id所对应的加密Id
// 输入参数：
//     fileId: 文件id
// 返回值: 文件加密id
//
// Parameters:
//  - FileId
func (p *UgcServiceClient) GetEncodeId(fileId int32) (r string, err error) {
	if err = p.sendGetEncodeId(fileId); err != nil {
		return
	}
	return p.recvGetEncodeId()
}

func (p *UgcServiceClient) sendGetEncodeId(fileId int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getEncodeId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetEncodeIdArgs()
	args32.FileId = fileId
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UgcServiceClient) recvGetEncodeId() (value string, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetEncodeIdResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	return
}

type UgcServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UgcService
}

func (p *UgcServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UgcServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UgcServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUgcServiceProcessor(handler UgcService) *UgcServiceProcessor {

	self36 := &UgcServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self36.processorMap["putFile"] = &ugcServiceProcessorPutFile{handler: handler}
	self36.processorMap["putFileWithHeader"] = &ugcServiceProcessorPutFileWithHeader{handler: handler}
	self36.processorMap["getFile"] = &ugcServiceProcessorGetFile{handler: handler}
	self36.processorMap["getFileWithEId"] = &ugcServiceProcessorGetFileWithEId{handler: handler}
	self36.processorMap["checkFile"] = &ugcServiceProcessorCheckFile{handler: handler}
	self36.processorMap["checkFileInfo"] = &ugcServiceProcessorCheckFileInfo{handler: handler}
	self36.processorMap["deleteFile"] = &ugcServiceProcessorDeleteFile{handler: handler}
	self36.processorMap["getLastId"] = &ugcServiceProcessorGetLastId{handler: handler}
	self36.processorMap["getEncodeId"] = &ugcServiceProcessorGetEncodeId{handler: handler}
	return self36
}

func (p *UgcServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x37 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x37.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x37

}

type ugcServiceProcessorPutFile struct {
	handler UgcService
}

func (p *ugcServiceProcessorPutFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPutFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("putFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPutFileResult()
	if result.Success, err = p.handler.PutFile(args.FileData, args.FileFormat); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing putFile: "+err.Error())
		oprot.WriteMessageBegin("putFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("putFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorPutFileWithHeader struct {
	handler UgcService
}

func (p *ugcServiceProcessorPutFileWithHeader) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPutFileWithHeaderArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("putFileWithHeader", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPutFileWithHeaderResult()
	if result.Success, err = p.handler.PutFileWithHeader(args.Header, args.FileData, args.FileFormat); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing putFileWithHeader: "+err.Error())
		oprot.WriteMessageBegin("putFileWithHeader", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("putFileWithHeader", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorGetFile struct {
	handler UgcService
}

func (p *ugcServiceProcessorGetFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFileResult()
	if result.Success, err = p.handler.GetFile(args.FileId, args.FileLastModified, args.FileEtag); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFile: "+err.Error())
		oprot.WriteMessageBegin("getFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorGetFileWithEId struct {
	handler UgcService
}

func (p *ugcServiceProcessorGetFileWithEId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFileWithEIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFileWithEId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFileWithEIdResult()
	if result.Success, err = p.handler.GetFileWithEId(args.FileEncodeId, args.FileLastModified, args.FileEtag); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFileWithEId: "+err.Error())
		oprot.WriteMessageBegin("getFileWithEId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFileWithEId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorCheckFile struct {
	handler UgcService
}

func (p *ugcServiceProcessorCheckFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckFileResult()
	if result.Success, err = p.handler.CheckFile(args.FileEtag); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkFile: "+err.Error())
		oprot.WriteMessageBegin("checkFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorCheckFileInfo struct {
	handler UgcService
}

func (p *ugcServiceProcessorCheckFileInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckFileInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkFileInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckFileInfoResult()
	if result.Success, err = p.handler.CheckFileInfo(args.FileEtag); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkFileInfo: "+err.Error())
		oprot.WriteMessageBegin("checkFileInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkFileInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorDeleteFile struct {
	handler UgcService
}

func (p *ugcServiceProcessorDeleteFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteFileResult()
	if result.Success, err = p.handler.DeleteFile(args.FileId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteFile: "+err.Error())
		oprot.WriteMessageBegin("deleteFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorGetLastId struct {
	handler UgcService
}

func (p *ugcServiceProcessorGetLastId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetLastIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getLastId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetLastIdResult()
	if result.Success, err = p.handler.GetLastId(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getLastId: "+err.Error())
		oprot.WriteMessageBegin("getLastId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getLastId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ugcServiceProcessorGetEncodeId struct {
	handler UgcService
}

func (p *ugcServiceProcessorGetEncodeId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetEncodeIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getEncodeId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetEncodeIdResult()
	if result.Success, err = p.handler.GetEncodeId(args.FileId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getEncodeId: "+err.Error())
		oprot.WriteMessageBegin("getEncodeId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getEncodeId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type PutFileArgs struct {
	FileData   string     `thrift:"fileData,1" json:"fileData"`
	FileFormat FormatCode `thrift:"fileFormat,2" json:"fileFormat"`
}

func NewPutFileArgs() *PutFileArgs {
	return &PutFileArgs{
		FileFormat: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PutFileArgs) IsSetFileFormat() bool {
	return int64(p.FileFormat) != math.MinInt32-1
}

func (p *PutFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PutFileArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileData = v
	}
	return nil
}

func (p *PutFileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileFormat = FormatCode(v)
	}
	return nil
}

func (p *PutFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("putFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PutFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileData", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileData: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileData)); err != nil {
		return fmt.Errorf("%T.fileData (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileData: %s", p, err)
	}
	return err
}

func (p *PutFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileFormat() {
		if err := oprot.WriteFieldBegin("fileFormat", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:fileFormat: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileFormat)); err != nil {
			return fmt.Errorf("%T.fileFormat (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:fileFormat: %s", p, err)
		}
	}
	return err
}

func (p *PutFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PutFileArgs(%+v)", *p)
}

type PutFileResult struct {
	Success *FileStruct `thrift:"success,0" json:"success"`
}

func NewPutFileResult() *PutFileResult {
	return &PutFileResult{}
}

func (p *PutFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PutFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewFileStruct()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PutFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("putFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PutFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PutFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PutFileResult(%+v)", *p)
}

type PutFileWithHeaderArgs struct {
	Header     *RequestHeader `thrift:"header,1" json:"header"`
	FileData   string         `thrift:"fileData,2" json:"fileData"`
	FileFormat FormatCode     `thrift:"fileFormat,3" json:"fileFormat"`
}

func NewPutFileWithHeaderArgs() *PutFileWithHeaderArgs {
	return &PutFileWithHeaderArgs{
		FileFormat: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PutFileWithHeaderArgs) IsSetFileFormat() bool {
	return int64(p.FileFormat) != math.MinInt32-1
}

func (p *PutFileWithHeaderArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PutFileWithHeaderArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PutFileWithHeaderArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileData = v
	}
	return nil
}

func (p *PutFileWithHeaderArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FileFormat = FormatCode(v)
	}
	return nil
}

func (p *PutFileWithHeaderArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("putFileWithHeader_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PutFileWithHeaderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PutFileWithHeaderArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileData", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fileData: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileData)); err != nil {
		return fmt.Errorf("%T.fileData (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fileData: %s", p, err)
	}
	return err
}

func (p *PutFileWithHeaderArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileFormat() {
		if err := oprot.WriteFieldBegin("fileFormat", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:fileFormat: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileFormat)); err != nil {
			return fmt.Errorf("%T.fileFormat (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:fileFormat: %s", p, err)
		}
	}
	return err
}

func (p *PutFileWithHeaderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PutFileWithHeaderArgs(%+v)", *p)
}

type PutFileWithHeaderResult struct {
	Success *FileStruct `thrift:"success,0" json:"success"`
}

func NewPutFileWithHeaderResult() *PutFileWithHeaderResult {
	return &PutFileWithHeaderResult{}
}

func (p *PutFileWithHeaderResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PutFileWithHeaderResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewFileStruct()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PutFileWithHeaderResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("putFileWithHeader_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PutFileWithHeaderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PutFileWithHeaderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PutFileWithHeaderResult(%+v)", *p)
}

type GetFileArgs struct {
	FileId           int32  `thrift:"fileId,1" json:"fileId"`
	FileLastModified string `thrift:"fileLastModified,2" json:"fileLastModified"`
	FileEtag         string `thrift:"fileEtag,3" json:"fileEtag"`
}

func NewGetFileArgs() *GetFileArgs {
	return &GetFileArgs{}
}

func (p *GetFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileId = v
	}
	return nil
}

func (p *GetFileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileLastModified = v
	}
	return nil
}

func (p *GetFileArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FileEtag = v
	}
	return nil
}

func (p *GetFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileId)); err != nil {
		return fmt.Errorf("%T.fileId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileId: %s", p, err)
	}
	return err
}

func (p *GetFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileLastModified", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fileLastModified: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileLastModified)); err != nil {
		return fmt.Errorf("%T.fileLastModified (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fileLastModified: %s", p, err)
	}
	return err
}

func (p *GetFileArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEtag", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fileEtag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEtag)); err != nil {
		return fmt.Errorf("%T.fileEtag (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fileEtag: %s", p, err)
	}
	return err
}

func (p *GetFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileArgs(%+v)", *p)
}

type GetFileResult struct {
	Success *FileStruct `thrift:"success,0" json:"success"`
}

func NewGetFileResult() *GetFileResult {
	return &GetFileResult{}
}

func (p *GetFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewFileStruct()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileResult(%+v)", *p)
}

type GetFileWithEIdArgs struct {
	FileEncodeId     string `thrift:"fileEncodeId,1" json:"fileEncodeId"`
	FileLastModified string `thrift:"fileLastModified,2" json:"fileLastModified"`
	FileEtag         string `thrift:"fileEtag,3" json:"fileEtag"`
}

func NewGetFileWithEIdArgs() *GetFileWithEIdArgs {
	return &GetFileWithEIdArgs{}
}

func (p *GetFileWithEIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileWithEIdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileEncodeId = v
	}
	return nil
}

func (p *GetFileWithEIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileLastModified = v
	}
	return nil
}

func (p *GetFileWithEIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FileEtag = v
	}
	return nil
}

func (p *GetFileWithEIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileWithEId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileWithEIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEncodeId", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileEncodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEncodeId)); err != nil {
		return fmt.Errorf("%T.fileEncodeId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileEncodeId: %s", p, err)
	}
	return err
}

func (p *GetFileWithEIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileLastModified", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fileLastModified: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileLastModified)); err != nil {
		return fmt.Errorf("%T.fileLastModified (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fileLastModified: %s", p, err)
	}
	return err
}

func (p *GetFileWithEIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEtag", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fileEtag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEtag)); err != nil {
		return fmt.Errorf("%T.fileEtag (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fileEtag: %s", p, err)
	}
	return err
}

func (p *GetFileWithEIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileWithEIdArgs(%+v)", *p)
}

type GetFileWithEIdResult struct {
	Success *FileStruct `thrift:"success,0" json:"success"`
}

func NewGetFileWithEIdResult() *GetFileWithEIdResult {
	return &GetFileWithEIdResult{}
}

func (p *GetFileWithEIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileWithEIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewFileStruct()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFileWithEIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileWithEId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileWithEIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFileWithEIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileWithEIdResult(%+v)", *p)
}

type CheckFileArgs struct {
	FileEtag string `thrift:"fileEtag,1" json:"fileEtag"`
}

func NewCheckFileArgs() *CheckFileArgs {
	return &CheckFileArgs{}
}

func (p *CheckFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckFileArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileEtag = v
	}
	return nil
}

func (p *CheckFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEtag", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileEtag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEtag)); err != nil {
		return fmt.Errorf("%T.fileEtag (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileEtag: %s", p, err)
	}
	return err
}

func (p *CheckFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckFileArgs(%+v)", *p)
}

type CheckFileResult struct {
	Success int32 `thrift:"success,0" json:"success"`
}

func NewCheckFileResult() *CheckFileResult {
	return &CheckFileResult{}
}

func (p *CheckFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckFileResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *CheckFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *CheckFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckFileResult(%+v)", *p)
}

type CheckFileInfoArgs struct {
	FileEtag string `thrift:"fileEtag,1" json:"fileEtag"`
}

func NewCheckFileInfoArgs() *CheckFileInfoArgs {
	return &CheckFileInfoArgs{}
}

func (p *CheckFileInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckFileInfoArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileEtag = v
	}
	return nil
}

func (p *CheckFileInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkFileInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckFileInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEtag", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileEtag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEtag)); err != nil {
		return fmt.Errorf("%T.fileEtag (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileEtag: %s", p, err)
	}
	return err
}

func (p *CheckFileInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckFileInfoArgs(%+v)", *p)
}

type CheckFileInfoResult struct {
	Success *FileStruct `thrift:"success,0" json:"success"`
}

func NewCheckFileInfoResult() *CheckFileInfoResult {
	return &CheckFileInfoResult{}
}

func (p *CheckFileInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckFileInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewFileStruct()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CheckFileInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkFileInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckFileInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckFileInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckFileInfoResult(%+v)", *p)
}

type DeleteFileArgs struct {
	FileId int32 `thrift:"fileId,1" json:"fileId"`
}

func NewDeleteFileArgs() *DeleteFileArgs {
	return &DeleteFileArgs{}
}

func (p *DeleteFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileId = v
	}
	return nil
}

func (p *DeleteFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileId)); err != nil {
		return fmt.Errorf("%T.fileId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileId: %s", p, err)
	}
	return err
}

func (p *DeleteFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFileArgs(%+v)", *p)
}

type DeleteFileResult struct {
	Success int32 `thrift:"success,0" json:"success"`
}

func NewDeleteFileResult() *DeleteFileResult {
	return &DeleteFileResult{}
}

func (p *DeleteFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *DeleteFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *DeleteFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFileResult(%+v)", *p)
}

type GetLastIdArgs struct {
}

func NewGetLastIdArgs() *GetLastIdArgs {
	return &GetLastIdArgs{}
}

func (p *GetLastIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLastIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLastId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLastIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLastIdArgs(%+v)", *p)
}

type GetLastIdResult struct {
	Success int32 `thrift:"success,0" json:"success"`
}

func NewGetLastIdResult() *GetLastIdResult {
	return &GetLastIdResult{}
}

func (p *GetLastIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLastIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetLastIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLastId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLastIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetLastIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLastIdResult(%+v)", *p)
}

type GetEncodeIdArgs struct {
	FileId int32 `thrift:"fileId,1" json:"fileId"`
}

func NewGetEncodeIdArgs() *GetEncodeIdArgs {
	return &GetEncodeIdArgs{}
}

func (p *GetEncodeIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetEncodeIdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileId = v
	}
	return nil
}

func (p *GetEncodeIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getEncodeId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetEncodeIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileId)); err != nil {
		return fmt.Errorf("%T.fileId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileId: %s", p, err)
	}
	return err
}

func (p *GetEncodeIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetEncodeIdArgs(%+v)", *p)
}

type GetEncodeIdResult struct {
	Success string `thrift:"success,0" json:"success"`
}

func NewGetEncodeIdResult() *GetEncodeIdResult {
	return &GetEncodeIdResult{}
}

func (p *GetEncodeIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetEncodeIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetEncodeIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getEncodeId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetEncodeIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetEncodeIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetEncodeIdResult(%+v)", *p)
}
