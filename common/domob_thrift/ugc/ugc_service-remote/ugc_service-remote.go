// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"ugc"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  FileStruct putFile(string fileData, FormatCode fileFormat)")
	fmt.Fprintln(os.Stderr, "  FileStruct putFileWithHeader(RequestHeader header, string fileData, FormatCode fileFormat)")
	fmt.Fprintln(os.<PERSON>, "  FileStruct getFile(i32 fileId, string fileLastModified, string fileEtag)")
	fmt.Fprintln(os.Stderr, "  FileStruct getFileWithEId(string fileEncodeId, string fileLastModified, string fileEtag)")
	fmt.Fprintln(os.Stderr, "  i32 checkFile(string fileEtag)")
	fmt.Fprintln(os.Stderr, "  FileStruct checkFileInfo(string fileEtag)")
	fmt.Fprintln(os.Stderr, "  i32 deleteFile(i32 fileId)")
	fmt.Fprintln(os.Stderr, "  i32 getLastId()")
	fmt.Fprintln(os.Stderr, "  string getEncodeId(i32 fileId)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := ugc.NewUgcServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "putFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PutFile requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := ugc.FormatCode(tmp1)
		value1 := argvalue1
		fmt.Print(client.PutFile(value0, value1))
		fmt.Print("\n")
		break
	case "putFileWithHeader":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PutFileWithHeader requires 3 args")
			flag.Usage()
		}
		arg39 := flag.Arg(1)
		mbTrans40 := thrift.NewTMemoryBufferLen(len(arg39))
		defer mbTrans40.Close()
		_, err41 := mbTrans40.WriteString(arg39)
		if err41 != nil {
			Usage()
			return
		}
		factory42 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt43 := factory42.GetProtocol(mbTrans40)
		argvalue0 := ugc.NewRequestHeader()
		err44 := argvalue0.Read(jsProt43)
		if err44 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := ugc.FormatCode(tmp2)
		value2 := argvalue2
		fmt.Print(client.PutFileWithHeader(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFile":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFile requires 3 args")
			flag.Usage()
		}
		tmp0, err46 := (strconv.Atoi(flag.Arg(1)))
		if err46 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.GetFile(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFileWithEId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileWithEId requires 3 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.GetFileWithEId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "checkFile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "CheckFile requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.CheckFile(value0))
		fmt.Print("\n")
		break
	case "checkFileInfo":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "CheckFileInfo requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.CheckFileInfo(value0))
		fmt.Print("\n")
		break
	case "deleteFile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DeleteFile requires 1 args")
			flag.Usage()
		}
		tmp0, err54 := (strconv.Atoi(flag.Arg(1)))
		if err54 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.DeleteFile(value0))
		fmt.Print("\n")
		break
	case "getLastId":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetLastId requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetLastId())
		fmt.Print("\n")
		break
	case "getEncodeId":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetEncodeId requires 1 args")
			flag.Usage()
		}
		tmp0, err55 := (strconv.Atoi(flag.Arg(1)))
		if err55 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetEncodeId(value0))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
