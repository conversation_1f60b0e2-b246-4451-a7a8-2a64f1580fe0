// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package ugc

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//文件类型
type FormatCode int64

const (
	FormatCode_TEXT      FormatCode = 1
	FormatCode_VIDEO     FormatCode = 2
	FormatCode_PNG       FormatCode = 3
	FormatCode_JPEG      FormatCode = 4
	FormatCode_BMP       FormatCode = 5
	FormatCode_GIF       FormatCode = 6
	FormatCode_TEXT_HTML FormatCode = 7
	FormatCode_JPG       FormatCode = 8
	FormatCode_PSD       FormatCode = 9
	FormatCode_MP4       FormatCode = 11
	FormatCode_OGG       FormatCode = 12
	FormatCode_WEBM      FormatCode = 13
	FormatCode_MPEG      FormatCode = 14
	FormatCode_IVF       FormatCode = 15
	FormatCode_FLV       FormatCode = 16
	FormatCode_MOV       FormatCode = 17
	FormatCode_SWF       FormatCode = 18
	FormatCode_RAR       FormatCode = 30
	FormatCode_ZIP       FormatCode = 31
	FormatCode_MP3       FormatCode = 20
	FormatCode_WAV       FormatCode = 21
	FormatCode_BIN       FormatCode = 99
)

func (p FormatCode) String() string {
	switch p {
	case FormatCode_TEXT:
		return "FormatCode_TEXT"
	case FormatCode_VIDEO:
		return "FormatCode_VIDEO"
	case FormatCode_PNG:
		return "FormatCode_PNG"
	case FormatCode_JPEG:
		return "FormatCode_JPEG"
	case FormatCode_BMP:
		return "FormatCode_BMP"
	case FormatCode_GIF:
		return "FormatCode_GIF"
	case FormatCode_TEXT_HTML:
		return "FormatCode_TEXT_HTML"
	case FormatCode_JPG:
		return "FormatCode_JPG"
	case FormatCode_PSD:
		return "FormatCode_PSD"
	case FormatCode_MP4:
		return "FormatCode_MP4"
	case FormatCode_OGG:
		return "FormatCode_OGG"
	case FormatCode_WEBM:
		return "FormatCode_WEBM"
	case FormatCode_MPEG:
		return "FormatCode_MPEG"
	case FormatCode_IVF:
		return "FormatCode_IVF"
	case FormatCode_FLV:
		return "FormatCode_FLV"
	case FormatCode_MOV:
		return "FormatCode_MOV"
	case FormatCode_SWF:
		return "FormatCode_SWF"
	case FormatCode_RAR:
		return "FormatCode_RAR"
	case FormatCode_ZIP:
		return "FormatCode_ZIP"
	case FormatCode_MP3:
		return "FormatCode_MP3"
	case FormatCode_WAV:
		return "FormatCode_WAV"
	case FormatCode_BIN:
		return "FormatCode_BIN"
	}
	return "<UNSET>"
}

func FormatCodeFromString(s string) (FormatCode, error) {
	switch s {
	case "FormatCode_TEXT":
		return FormatCode_TEXT, nil
	case "FormatCode_VIDEO":
		return FormatCode_VIDEO, nil
	case "FormatCode_PNG":
		return FormatCode_PNG, nil
	case "FormatCode_JPEG":
		return FormatCode_JPEG, nil
	case "FormatCode_BMP":
		return FormatCode_BMP, nil
	case "FormatCode_GIF":
		return FormatCode_GIF, nil
	case "FormatCode_TEXT_HTML":
		return FormatCode_TEXT_HTML, nil
	case "FormatCode_JPG":
		return FormatCode_JPG, nil
	case "FormatCode_PSD":
		return FormatCode_PSD, nil
	case "FormatCode_MP4":
		return FormatCode_MP4, nil
	case "FormatCode_OGG":
		return FormatCode_OGG, nil
	case "FormatCode_WEBM":
		return FormatCode_WEBM, nil
	case "FormatCode_MPEG":
		return FormatCode_MPEG, nil
	case "FormatCode_IVF":
		return FormatCode_IVF, nil
	case "FormatCode_FLV":
		return FormatCode_FLV, nil
	case "FormatCode_MOV":
		return FormatCode_MOV, nil
	case "FormatCode_SWF":
		return FormatCode_SWF, nil
	case "FormatCode_RAR":
		return FormatCode_RAR, nil
	case "FormatCode_ZIP":
		return FormatCode_ZIP, nil
	case "FormatCode_MP3":
		return FormatCode_MP3, nil
	case "FormatCode_WAV":
		return FormatCode_WAV, nil
	case "FormatCode_BIN":
		return FormatCode_BIN, nil
	}
	return FormatCode(math.MinInt32 - 1), fmt.Errorf("not a valid FormatCode string")
}

type FileStruct struct {
	FileId           int32      `thrift:"fileId,1" json:"fileId"`
	FileData         string     `thrift:"fileData,2" json:"fileData"`
	FileEtag         string     `thrift:"fileEtag,3" json:"fileEtag"`
	FileLastModified string     `thrift:"fileLastModified,4" json:"fileLastModified"`
	Response         int32      `thrift:"response,5" json:"response"`
	FileFormat       FormatCode `thrift:"fileFormat,6" json:"fileFormat"`
	FileEncodeId     string     `thrift:"fileEncodeId,7" json:"fileEncodeId"`
}

func NewFileStruct() *FileStruct {
	return &FileStruct{
		FileFormat: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FileStruct) IsSetFileFormat() bool {
	return int64(p.FileFormat) != math.MinInt32-1
}

func (p *FileStruct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FileStruct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileId = v
	}
	return nil
}

func (p *FileStruct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.FileEncodeId = v
	}
	return nil
}

func (p *FileStruct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileData = v
	}
	return nil
}

func (p *FileStruct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FileEtag = v
	}
	return nil
}

func (p *FileStruct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FileLastModified = v
	}
	return nil
}

func (p *FileStruct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Response = v
	}
	return nil
}

func (p *FileStruct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FileFormat = FormatCode(v)
	}
	return nil
}

func (p *FileStruct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FileStruct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FileStruct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileId)); err != nil {
		return fmt.Errorf("%T.fileId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileId: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileData", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fileData: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileData)); err != nil {
		return fmt.Errorf("%T.fileData (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fileData: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEtag", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fileEtag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEtag)); err != nil {
		return fmt.Errorf("%T.fileEtag (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fileEtag: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileLastModified", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:fileLastModified: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileLastModified)); err != nil {
		return fmt.Errorf("%T.fileLastModified (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:fileLastModified: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("response", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:response: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Response)); err != nil {
		return fmt.Errorf("%T.response (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:response: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileFormat() {
		if err := oprot.WriteFieldBegin("fileFormat", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:fileFormat: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileFormat)); err != nil {
			return fmt.Errorf("%T.fileFormat (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:fileFormat: %s", p, err)
		}
	}
	return err
}

func (p *FileStruct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEncodeId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:fileEncodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEncodeId)); err != nil {
		return fmt.Errorf("%T.fileEncodeId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:fileEncodeId: %s", p, err)
	}
	return err
}

func (p *FileStruct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileStruct(%+v)", *p)
}

type RequestHeader struct {
	ProjectName string `thrift:"projectName,1" json:"projectName"`
	UploadUser  string `thrift:"uploadUser,2" json:"uploadUser"`
}

func NewRequestHeader() *RequestHeader {
	return &RequestHeader{}
}

func (p *RequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ProjectName = v
	}
	return nil
}

func (p *RequestHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UploadUser = v
	}
	return nil
}

func (p *RequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("projectName", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:projectName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProjectName)); err != nil {
		return fmt.Errorf("%T.projectName (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:projectName: %s", p, err)
	}
	return err
}

func (p *RequestHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uploadUser", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uploadUser: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UploadUser)); err != nil {
		return fmt.Errorf("%T.uploadUser (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uploadUser: %s", p, err)
	}
	return err
}

func (p *RequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RequestHeader(%+v)", *p)
}
