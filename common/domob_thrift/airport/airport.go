// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package airport

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/airport_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/p2p_server"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = airport_types.GoUnusedProtection__
var _ = p2p_server.GoUnusedProtection__

type Airport interface {
	p2p_server.P2pServer

	// 通知flight信息
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - FlightFile: 航班文件的详细信息
	//  - DownloadableAirports: 可供下载的airport的host列表
	//  - ReportCollectServer: 初始汇报地址
	NoticeFlight(requestHeader *common.RequestHeader, flightFile *airport_types.FlightFile, downloadableAirports []*airport_types.AirportInfo, reportCollectServer *airport_types.AirportInfo) (r *airport_types.ResultFlightFileStatus, err error)
	// 获取在airport下的文件信息
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Account: 账号
	GetFlightFileList(requestHeader *common.RequestHeader, account string) (r *airport_types.ResultAirportFlightFileList, err error)
	// 汇报状态
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - FlightId: flight ID
	//  - FileStatus: 文件状态
	//  - ExtInfo: 额外信息
	//  - Account: 汇报的账号
	ReportFlightFileStatus(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, flightId int32, fileStatus airport_types.FlightFileStatus, extInfo string, account string) (r *airport_types.ResultBool, err error)
	// 发送通勤记录(通勤机场)
	// 该方法发送单条记录
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - Data: 记录数据
	SendCommuterData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data []byte) (r *airport_types.ResultBool, err error)
}

type AirportClient struct {
	*p2p_server.P2pServerClient
}

func NewAirportClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AirportClient {
	return &AirportClient{P2pServerClient: p2p_server.NewP2pServerClientFactory(t, f)}
}

func NewAirportClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AirportClient {
	return &AirportClient{P2pServerClient: p2p_server.NewP2pServerClientProtocol(t, iprot, oprot)}
}

// 通知flight信息
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - FlightFile: 航班文件的详细信息
//  - DownloadableAirports: 可供下载的airport的host列表
//  - ReportCollectServer: 初始汇报地址
func (p *AirportClient) NoticeFlight(requestHeader *common.RequestHeader, flightFile *airport_types.FlightFile, downloadableAirports []*airport_types.AirportInfo, reportCollectServer *airport_types.AirportInfo) (r *airport_types.ResultFlightFileStatus, err error) {
	if err = p.sendNoticeFlight(requestHeader, flightFile, downloadableAirports, reportCollectServer); err != nil {
		return
	}
	return p.recvNoticeFlight()
}

func (p *AirportClient) sendNoticeFlight(requestHeader *common.RequestHeader, flightFile *airport_types.FlightFile, downloadableAirports []*airport_types.AirportInfo, reportCollectServer *airport_types.AirportInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("noticeFlight", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewNoticeFlightArgs()
	args0.RequestHeader = requestHeader
	args0.FlightFile = flightFile
	args0.DownloadableAirports = downloadableAirports
	args0.ReportCollectServer = reportCollectServer
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportClient) recvNoticeFlight() (value *airport_types.ResultFlightFileStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewNoticeFlightResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 获取在airport下的文件信息
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Account: 账号
func (p *AirportClient) GetFlightFileList(requestHeader *common.RequestHeader, account string) (r *airport_types.ResultAirportFlightFileList, err error) {
	if err = p.sendGetFlightFileList(requestHeader, account); err != nil {
		return
	}
	return p.recvGetFlightFileList()
}

func (p *AirportClient) sendGetFlightFileList(requestHeader *common.RequestHeader, account string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFlightFileList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetFlightFileListArgs()
	args4.RequestHeader = requestHeader
	args4.Account = account
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportClient) recvGetFlightFileList() (value *airport_types.ResultAirportFlightFileList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetFlightFileListResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// 汇报状态
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - FlightId: flight ID
//  - FileStatus: 文件状态
//  - ExtInfo: 额外信息
//  - Account: 汇报的账号
func (p *AirportClient) ReportFlightFileStatus(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, flightId int32, fileStatus airport_types.FlightFileStatus, extInfo string, account string) (r *airport_types.ResultBool, err error) {
	if err = p.sendReportFlightFileStatus(requestHeader, topicInfo, flightId, fileStatus, extInfo, account); err != nil {
		return
	}
	return p.recvReportFlightFileStatus()
}

func (p *AirportClient) sendReportFlightFileStatus(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, flightId int32, fileStatus airport_types.FlightFileStatus, extInfo string, account string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("reportFlightFileStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewReportFlightFileStatusArgs()
	args8.RequestHeader = requestHeader
	args8.TopicInfo = topicInfo
	args8.FlightId = flightId
	args8.FileStatus = fileStatus
	args8.ExtInfo = extInfo
	args8.Account = account
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportClient) recvReportFlightFileStatus() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewReportFlightFileStatusResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 发送通勤记录(通勤机场)
// 该方法发送单条记录
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - Data: 记录数据
func (p *AirportClient) SendCommuterData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data []byte) (r *airport_types.ResultBool, err error) {
	if err = p.sendSendCommuterData(requestHeader, topicInfo, data); err != nil {
		return
	}
	return p.recvSendCommuterData()
}

func (p *AirportClient) sendSendCommuterData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data []byte) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendCommuterData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewSendCommuterDataArgs()
	args12.RequestHeader = requestHeader
	args12.TopicInfo = topicInfo
	args12.Data = data
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportClient) recvSendCommuterData() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewSendCommuterDataResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

type AirportProcessor struct {
	*p2p_server.P2pServerProcessor
}

func NewAirportProcessor(handler Airport) *AirportProcessor {
	self16 := &AirportProcessor{p2p_server.NewP2pServerProcessor(handler)}
	self16.AddToProcessorMap("noticeFlight", &airportProcessorNoticeFlight{handler: handler})
	self16.AddToProcessorMap("getFlightFileList", &airportProcessorGetFlightFileList{handler: handler})
	self16.AddToProcessorMap("reportFlightFileStatus", &airportProcessorReportFlightFileStatus{handler: handler})
	self16.AddToProcessorMap("sendCommuterData", &airportProcessorSendCommuterData{handler: handler})
	return self16
}

type airportProcessorNoticeFlight struct {
	handler Airport
}

func (p *airportProcessorNoticeFlight) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewNoticeFlightArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("noticeFlight", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewNoticeFlightResult()
	if result.Success, err = p.handler.NoticeFlight(args.RequestHeader, args.FlightFile, args.DownloadableAirports, args.ReportCollectServer); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing noticeFlight: "+err.Error())
		oprot.WriteMessageBegin("noticeFlight", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("noticeFlight", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportProcessorGetFlightFileList struct {
	handler Airport
}

func (p *airportProcessorGetFlightFileList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFlightFileListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFlightFileList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFlightFileListResult()
	if result.Success, err = p.handler.GetFlightFileList(args.RequestHeader, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFlightFileList: "+err.Error())
		oprot.WriteMessageBegin("getFlightFileList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFlightFileList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportProcessorReportFlightFileStatus struct {
	handler Airport
}

func (p *airportProcessorReportFlightFileStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewReportFlightFileStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("reportFlightFileStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewReportFlightFileStatusResult()
	if result.Success, err = p.handler.ReportFlightFileStatus(args.RequestHeader, args.TopicInfo, args.FlightId, args.FileStatus, args.ExtInfo, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing reportFlightFileStatus: "+err.Error())
		oprot.WriteMessageBegin("reportFlightFileStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("reportFlightFileStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportProcessorSendCommuterData struct {
	handler Airport
}

func (p *airportProcessorSendCommuterData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendCommuterDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendCommuterData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendCommuterDataResult()
	if result.Success, err = p.handler.SendCommuterData(args.RequestHeader, args.TopicInfo, args.Data); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendCommuterData: "+err.Error())
		oprot.WriteMessageBegin("sendCommuterData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendCommuterData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type NoticeFlightArgs struct {
	RequestHeader        *common.RequestHeader        `thrift:"requestHeader,1" json:"requestHeader"`
	FlightFile           *airport_types.FlightFile    `thrift:"flightFile,2" json:"flightFile"`
	DownloadableAirports []*airport_types.AirportInfo `thrift:"downloadableAirports,3" json:"downloadableAirports"`
	ReportCollectServer  *airport_types.AirportInfo   `thrift:"reportCollectServer,4" json:"reportCollectServer"`
}

func NewNoticeFlightArgs() *NoticeFlightArgs {
	return &NoticeFlightArgs{}
}

func (p *NoticeFlightArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeFlightArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *NoticeFlightArgs) readField2(iprot thrift.TProtocol) error {
	p.FlightFile = airport_types.NewFlightFile()
	if err := p.FlightFile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.FlightFile)
	}
	return nil
}

func (p *NoticeFlightArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DownloadableAirports = make([]*airport_types.AirportInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem17 := airport_types.NewAirportInfo()
		if err := _elem17.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem17)
		}
		p.DownloadableAirports = append(p.DownloadableAirports, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *NoticeFlightArgs) readField4(iprot thrift.TProtocol) error {
	p.ReportCollectServer = airport_types.NewAirportInfo()
	if err := p.ReportCollectServer.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReportCollectServer)
	}
	return nil
}

func (p *NoticeFlightArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeFlight_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeFlightArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFlightArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.FlightFile != nil {
		if err := oprot.WriteFieldBegin("flightFile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:flightFile: %s", p, err)
		}
		if err := p.FlightFile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.FlightFile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:flightFile: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFlightArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DownloadableAirports != nil {
		if err := oprot.WriteFieldBegin("downloadableAirports", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:downloadableAirports: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DownloadableAirports)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DownloadableAirports {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:downloadableAirports: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFlightArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ReportCollectServer != nil {
		if err := oprot.WriteFieldBegin("reportCollectServer", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:reportCollectServer: %s", p, err)
		}
		if err := p.ReportCollectServer.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReportCollectServer)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:reportCollectServer: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFlightArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeFlightArgs(%+v)", *p)
}

type NoticeFlightResult struct {
	Success *airport_types.ResultFlightFileStatus `thrift:"success,0" json:"success"`
}

func NewNoticeFlightResult() *NoticeFlightResult {
	return &NoticeFlightResult{}
}

func (p *NoticeFlightResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeFlightResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultFlightFileStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *NoticeFlightResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeFlight_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeFlightResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFlightResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeFlightResult(%+v)", *p)
}

type GetFlightFileListArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Account       string                `thrift:"account,2" json:"account"`
}

func NewGetFlightFileListArgs() *GetFlightFileListArgs {
	return &GetFlightFileListArgs{}
}

func (p *GetFlightFileListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFlightFileListArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFlightFileListArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *GetFlightFileListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFlightFileList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFlightFileListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFlightFileListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:account: %s", p, err)
	}
	return err
}

func (p *GetFlightFileListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFlightFileListArgs(%+v)", *p)
}

type GetFlightFileListResult struct {
	Success *airport_types.ResultAirportFlightFileList `thrift:"success,0" json:"success"`
}

func NewGetFlightFileListResult() *GetFlightFileListResult {
	return &GetFlightFileListResult{}
}

func (p *GetFlightFileListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFlightFileListResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultAirportFlightFileList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFlightFileListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFlightFileList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFlightFileListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFlightFileListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFlightFileListResult(%+v)", *p)
}

type ReportFlightFileStatusArgs struct {
	RequestHeader *common.RequestHeader          `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo       `thrift:"topicInfo,2" json:"topicInfo"`
	FlightId      int32                          `thrift:"flightId,3" json:"flightId"`
	FileStatus    airport_types.FlightFileStatus `thrift:"fileStatus,4" json:"fileStatus"`
	ExtInfo       string                         `thrift:"extInfo,5" json:"extInfo"`
	Account       string                         `thrift:"account,6" json:"account"`
}

func NewReportFlightFileStatusArgs() *ReportFlightFileStatusArgs {
	return &ReportFlightFileStatusArgs{
		FileStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ReportFlightFileStatusArgs) IsSetFileStatus() bool {
	return int64(p.FileStatus) != math.MinInt32-1
}

func (p *ReportFlightFileStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FileStatus = airport_types.FlightFileStatus(v)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("reportFlightFileStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:flightId: %s", p, err)
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileStatus() {
		if err := oprot.WriteFieldBegin("fileStatus", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:fileStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileStatus)); err != nil {
			return fmt.Errorf("%T.fileStatus (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:fileStatus: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:extInfo: %s", p, err)
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:account: %s", p, err)
	}
	return err
}

func (p *ReportFlightFileStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportFlightFileStatusArgs(%+v)", *p)
}

type ReportFlightFileStatusResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewReportFlightFileStatusResult() *ReportFlightFileStatusResult {
	return &ReportFlightFileStatusResult{}
}

func (p *ReportFlightFileStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ReportFlightFileStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("reportFlightFileStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportFlightFileStatusResult(%+v)", *p)
}

type SendCommuterDataArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	Data          []byte                   `thrift:"data,3" json:"data"`
}

func NewSendCommuterDataArgs() *SendCommuterDataArgs {
	return &SendCommuterDataArgs{}
}

func (p *SendCommuterDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SendCommuterDataArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *SendCommuterDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Data = v
	}
	return nil
}

func (p *SendCommuterDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendCommuterData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.STRING, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:data: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Data); err != nil {
			return fmt.Errorf("%T.data (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:data: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendCommuterDataArgs(%+v)", *p)
}

type SendCommuterDataResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewSendCommuterDataResult() *SendCommuterDataResult {
	return &SendCommuterDataResult{}
}

func (p *SendCommuterDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SendCommuterDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendCommuterData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendCommuterDataResult(%+v)", *p)
}
