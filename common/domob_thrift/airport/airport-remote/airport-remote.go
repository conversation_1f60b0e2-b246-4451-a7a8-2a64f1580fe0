// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"airport"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ResultFlightFileStatus noticeFlight(RequestHeader requestHeader, FlightFile flightFile,  downloadableAirports, AirportInfo reportCollectServer)")
	fmt.Fprintln(os.<PERSON>derr, "  ResultAirportFlightFileList getFlightFileList(RequestHeader requestHeader, string account)")
	fmt.Fprintln(os.Stderr, "  ResultBool reportFlightFileStatus(RequestHeader requestHeader, TopicInfo topicInfo, i32 flightId, FlightFileStatus fileStatus, string extInfo, string account)")
	fmt.Fprintln(os.Stderr, "  ResultBool sendCommuterData(RequestHeader requestHeader, TopicInfo topicInfo, string data)")
	fmt.Fprintln(os.Stderr, "  ResultI32List getFileFinishedBlockIndex(RequestHeader requestHeader, TopicInfo topicInfo,  needBlocks)")
	fmt.Fprintln(os.Stderr, "  ResultData getFileData(RequestHeader requestHeader, TopicInfo topicInfo, i32 blockIndex)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := airport.NewAirportClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "noticeFlight":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "NoticeFlight requires 4 args")
			flag.Usage()
		}
		arg18 := flag.Arg(1)
		mbTrans19 := thrift.NewTMemoryBufferLen(len(arg18))
		defer mbTrans19.Close()
		_, err20 := mbTrans19.WriteString(arg18)
		if err20 != nil {
			Usage()
			return
		}
		factory21 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt22 := factory21.GetProtocol(mbTrans19)
		argvalue0 := airport.NewRequestHeader()
		err23 := argvalue0.Read(jsProt22)
		if err23 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg24 := flag.Arg(2)
		mbTrans25 := thrift.NewTMemoryBufferLen(len(arg24))
		defer mbTrans25.Close()
		_, err26 := mbTrans25.WriteString(arg24)
		if err26 != nil {
			Usage()
			return
		}
		factory27 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt28 := factory27.GetProtocol(mbTrans25)
		argvalue1 := airport.NewFlightFile()
		err29 := argvalue1.Read(jsProt28)
		if err29 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg30 := flag.Arg(3)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		containerStruct2 := airport.NewNoticeFlightArgs()
		err35 := containerStruct2.ReadField3(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.DownloadableAirports
		value2 := argvalue2
		arg36 := flag.Arg(4)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue3 := airport.NewAirportInfo()
		err41 := argvalue3.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.NoticeFlight(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getFlightFileList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFlightFileList requires 2 args")
			flag.Usage()
		}
		arg42 := flag.Arg(1)
		mbTrans43 := thrift.NewTMemoryBufferLen(len(arg42))
		defer mbTrans43.Close()
		_, err44 := mbTrans43.WriteString(arg42)
		if err44 != nil {
			Usage()
			return
		}
		factory45 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt46 := factory45.GetProtocol(mbTrans43)
		argvalue0 := airport.NewRequestHeader()
		err47 := argvalue0.Read(jsProt46)
		if err47 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetFlightFileList(value0, value1))
		fmt.Print("\n")
		break
	case "reportFlightFileStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ReportFlightFileStatus requires 6 args")
			flag.Usage()
		}
		arg49 := flag.Arg(1)
		mbTrans50 := thrift.NewTMemoryBufferLen(len(arg49))
		defer mbTrans50.Close()
		_, err51 := mbTrans50.WriteString(arg49)
		if err51 != nil {
			Usage()
			return
		}
		factory52 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt53 := factory52.GetProtocol(mbTrans50)
		argvalue0 := airport.NewRequestHeader()
		err54 := argvalue0.Read(jsProt53)
		if err54 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg55 := flag.Arg(2)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue1 := airport.NewTopicInfo()
		err60 := argvalue1.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err61 := (strconv.Atoi(flag.Arg(3)))
		if err61 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := airport.FlightFileStatus(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.ReportFlightFileStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "sendCommuterData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SendCommuterData requires 3 args")
			flag.Usage()
		}
		arg64 := flag.Arg(1)
		mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
		defer mbTrans65.Close()
		_, err66 := mbTrans65.WriteString(arg64)
		if err66 != nil {
			Usage()
			return
		}
		factory67 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt68 := factory67.GetProtocol(mbTrans65)
		argvalue0 := airport.NewRequestHeader()
		err69 := argvalue0.Read(jsProt68)
		if err69 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg70 := flag.Arg(2)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		argvalue1 := airport.NewTopicInfo()
		err75 := argvalue1.Read(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.SendCommuterData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFileFinishedBlockIndex":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileFinishedBlockIndex requires 3 args")
			flag.Usage()
		}
		arg77 := flag.Arg(1)
		mbTrans78 := thrift.NewTMemoryBufferLen(len(arg77))
		defer mbTrans78.Close()
		_, err79 := mbTrans78.WriteString(arg77)
		if err79 != nil {
			Usage()
			return
		}
		factory80 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt81 := factory80.GetProtocol(mbTrans78)
		argvalue0 := airport.NewRequestHeader()
		err82 := argvalue0.Read(jsProt81)
		if err82 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg83 := flag.Arg(2)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		argvalue1 := airport.NewTopicInfo()
		err88 := argvalue1.Read(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg89 := flag.Arg(3)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		containerStruct2 := airport.NewGetFileFinishedBlockIndexArgs()
		err94 := containerStruct2.ReadField3(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.NeedBlocks
		value2 := argvalue2
		fmt.Print(client.GetFileFinishedBlockIndex(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFileData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileData requires 3 args")
			flag.Usage()
		}
		arg95 := flag.Arg(1)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		argvalue0 := airport.NewRequestHeader()
		err100 := argvalue0.Read(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg101 := flag.Arg(2)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		argvalue1 := airport.NewTopicInfo()
		err106 := argvalue1.Read(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err107 := (strconv.Atoi(flag.Arg(3)))
		if err107 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetFileData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
