// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_adserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/sos_ui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = sos_ui_types.GoUnusedProtection__
var GoUnusedProtection__ int

type Color int64

const (
	Color_BLACK Color = 1
	Color_WHITE Color = 2
)

func (p Color) String() string {
	switch p {
	case Color_BLACK:
		return "Color_BLACK"
	case Color_WHITE:
		return "Color_WHITE"
	}
	return "<UNSET>"
}

func ColorFromString(s string) (Color, error) {
	switch s {
	case "Color_BLACK":
		return Color_BLACK, nil
	case "Color_WHITE":
		return Color_WHITE, nil
	}
	return Color(math.MinInt32 - 1), fmt.Errorf("not a valid Color string")
}

//消费请求处理状态
type AowConsumeStatus int64

const (
	AowConsumeStatus_NOT_A_CONSUME      AowConsumeStatus = 0
	AowConsumeStatus_CONSUME_SUCCESS    AowConsumeStatus = 1
	AowConsumeStatus_INSUFFICIENT_POINT AowConsumeStatus = 2
	AowConsumeStatus_REDUPLICATE_ORDER  AowConsumeStatus = 3
	AowConsumeStatus_INVALID_USER       AowConsumeStatus = 4
	AowConsumeStatus_REQUEST_ERROR      AowConsumeStatus = 5
	AowConsumeStatus_SERVER_ERROR       AowConsumeStatus = 6
)

func (p AowConsumeStatus) String() string {
	switch p {
	case AowConsumeStatus_NOT_A_CONSUME:
		return "AowConsumeStatus_NOT_A_CONSUME"
	case AowConsumeStatus_CONSUME_SUCCESS:
		return "AowConsumeStatus_CONSUME_SUCCESS"
	case AowConsumeStatus_INSUFFICIENT_POINT:
		return "AowConsumeStatus_INSUFFICIENT_POINT"
	case AowConsumeStatus_REDUPLICATE_ORDER:
		return "AowConsumeStatus_REDUPLICATE_ORDER"
	case AowConsumeStatus_INVALID_USER:
		return "AowConsumeStatus_INVALID_USER"
	case AowConsumeStatus_REQUEST_ERROR:
		return "AowConsumeStatus_REQUEST_ERROR"
	case AowConsumeStatus_SERVER_ERROR:
		return "AowConsumeStatus_SERVER_ERROR"
	}
	return "<UNSET>"
}

func AowConsumeStatusFromString(s string) (AowConsumeStatus, error) {
	switch s {
	case "AowConsumeStatus_NOT_A_CONSUME":
		return AowConsumeStatus_NOT_A_CONSUME, nil
	case "AowConsumeStatus_CONSUME_SUCCESS":
		return AowConsumeStatus_CONSUME_SUCCESS, nil
	case "AowConsumeStatus_INSUFFICIENT_POINT":
		return AowConsumeStatus_INSUFFICIENT_POINT, nil
	case "AowConsumeStatus_REDUPLICATE_ORDER":
		return AowConsumeStatus_REDUPLICATE_ORDER, nil
	case "AowConsumeStatus_INVALID_USER":
		return AowConsumeStatus_INVALID_USER, nil
	case "AowConsumeStatus_REQUEST_ERROR":
		return AowConsumeStatus_REQUEST_ERROR, nil
	case "AowConsumeStatus_SERVER_ERROR":
		return AowConsumeStatus_SERVER_ERROR, nil
	}
	return AowConsumeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AowConsumeStatus string")
}

type SosAdObject struct {
	Id         int32               `thrift:"id,1" json:"id"`
	Name       string              `thrift:"name,2" json:"name"`
	BriefDesc  string              `thrift:"brief_desc,3" json:"brief_desc"`
	AdType     sos_ui_types.AdType `thrift:"ad_type,4" json:"ad_type"`
	Size       int32               `thrift:"size,5" json:"size"`
	Point      float64             `thrift:"point,6" json:"point"`
	ButtonText string              `thrift:"button_text,7" json:"button_text"`
	Logo       string              `thrift:"logo,8" json:"logo"`
	Screenshot string              `thrift:"screenshot,9" json:"screenshot"`
	Background string              `thrift:"background,10" json:"background"`
	TextColor  Color               `thrift:"text_color,11" json:"text_color"`
	Desc       string              `thrift:"desc,12" json:"desc"`
	Tasks      string              `thrift:"tasks,13" json:"tasks"`
	Pkg        string              `thrift:"pkg,14" json:"pkg"`
	// unused field # 15
	Ver          int32  `thrift:"ver,16" json:"ver"`
	ActionUrl    string `thrift:"action_url,17" json:"action_url"`
	TimeInterval int32  `thrift:"time_interval,18" json:"time_interval"`
	TimeFragment int32  `thrift:"time_fragment,19" json:"time_fragment"`
	Filtered     bool   `thrift:"filtered,20" json:"filtered"`
	FilterName   string `thrift:"filterName,21" json:"filterName"`
	Action       int32  `thrift:"action,22" json:"action"`
	LifeCycle    int32  `thrift:"life_cycle,23" json:"life_cycle"`
	TaskDesc     string `thrift:"task_desc,24" json:"task_desc"`
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Tr                     *sos_ui_types.SosTracker `thrift:"tr,30" json:"tr"`
	Weight                 int32                    `thrift:"weight,31" json:"weight"`
	Key                    float64                  `thrift:"key,32" json:"key"`
	IsSuperTask            bool                     `thrift:"is_super_task,33" json:"is_super_task"`
	ExtraPrice             float64                  `thrift:"extra_price,34" json:"extra_price"`
	SuperTaskDisplayWeight int32                    `thrift:"super_task_display_weight,35" json:"super_task_display_weight"`
}

func NewSosAdObject() *SosAdObject {
	return &SosAdObject{
		AdType: math.MinInt32 - 1, // unset sentinal value

		TextColor: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SosAdObject) IsSetAdType() bool {
	return int64(p.AdType) != math.MinInt32-1
}

func (p *SosAdObject) IsSetTextColor() bool {
	return int64(p.TextColor) != math.MinInt32-1
}

func (p *SosAdObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosAdObject) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SosAdObject) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SosAdObject) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.BriefDesc = v
	}
	return nil
}

func (p *SosAdObject) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdType = sos_ui_types.AdType(v)
	}
	return nil
}

func (p *SosAdObject) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *SosAdObject) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *SosAdObject) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ButtonText = v
	}
	return nil
}

func (p *SosAdObject) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *SosAdObject) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Screenshot = v
	}
	return nil
}

func (p *SosAdObject) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Background = v
	}
	return nil
}

func (p *SosAdObject) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TextColor = Color(v)
	}
	return nil
}

func (p *SosAdObject) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *SosAdObject) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Tasks = v
	}
	return nil
}

func (p *SosAdObject) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Pkg = v
	}
	return nil
}

func (p *SosAdObject) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Ver = v
	}
	return nil
}

func (p *SosAdObject) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *SosAdObject) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.TimeInterval = v
	}
	return nil
}

func (p *SosAdObject) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.TimeFragment = v
	}
	return nil
}

func (p *SosAdObject) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Filtered = v
	}
	return nil
}

func (p *SosAdObject) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.FilterName = v
	}
	return nil
}

func (p *SosAdObject) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *SosAdObject) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.LifeCycle = v
	}
	return nil
}

func (p *SosAdObject) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.TaskDesc = v
	}
	return nil
}

func (p *SosAdObject) readField30(iprot thrift.TProtocol) error {
	p.Tr = sos_ui_types.NewSosTracker()
	if err := p.Tr.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Tr)
	}
	return nil
}

func (p *SosAdObject) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Weight = v
	}
	return nil
}

func (p *SosAdObject) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Key = v
	}
	return nil
}

func (p *SosAdObject) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.IsSuperTask = v
	}
	return nil
}

func (p *SosAdObject) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.ExtraPrice = v
	}
	return nil
}

func (p *SosAdObject) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.SuperTaskDisplayWeight = v
	}
	return nil
}

func (p *SosAdObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosAdObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosAdObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief_desc", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:brief_desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BriefDesc)); err != nil {
		return fmt.Errorf("%T.brief_desc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:brief_desc: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdType() {
		if err := oprot.WriteFieldBegin("ad_type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ad_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdType)); err != nil {
			return fmt.Errorf("%T.ad_type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ad_type: %s", p, err)
		}
	}
	return err
}

func (p *SosAdObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:size: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.DOUBLE, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:point: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("button_text", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:button_text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ButtonText)); err != nil {
		return fmt.Errorf("%T.button_text (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:button_text: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:logo: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenshot", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:screenshot: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Screenshot)); err != nil {
		return fmt.Errorf("%T.screenshot (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:screenshot: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("background", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:background: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Background)); err != nil {
		return fmt.Errorf("%T.background (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:background: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetTextColor() {
		if err := oprot.WriteFieldBegin("text_color", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:text_color: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TextColor)); err != nil {
			return fmt.Errorf("%T.text_color (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:text_color: %s", p, err)
		}
	}
	return err
}

func (p *SosAdObject) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:desc: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tasks", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:tasks: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tasks)); err != nil {
		return fmt.Errorf("%T.tasks (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:tasks: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:pkg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pkg)); err != nil {
		return fmt.Errorf("%T.pkg (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:pkg: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ver", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:ver: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ver)); err != nil {
		return fmt.Errorf("%T.ver (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:ver: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_url", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:action_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.action_url (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:action_url: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time_interval", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:time_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TimeInterval)); err != nil {
		return fmt.Errorf("%T.time_interval (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:time_interval: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time_fragment", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:time_fragment: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TimeFragment)); err != nil {
		return fmt.Errorf("%T.time_fragment (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:time_fragment: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filtered", thrift.BOOL, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:filtered: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Filtered)); err != nil {
		return fmt.Errorf("%T.filtered (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:filtered: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filterName", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:filterName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FilterName)); err != nil {
		return fmt.Errorf("%T.filterName (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:filterName: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:action: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("life_cycle", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:life_cycle: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LifeCycle)); err != nil {
		return fmt.Errorf("%T.life_cycle (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:life_cycle: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_desc", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:task_desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskDesc)); err != nil {
		return fmt.Errorf("%T.task_desc (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:task_desc: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField30(oprot thrift.TProtocol) (err error) {
	if p.Tr != nil {
		if err := oprot.WriteFieldBegin("tr", thrift.STRUCT, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:tr: %s", p, err)
		}
		if err := p.Tr.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Tr)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:tr: %s", p, err)
		}
	}
	return err
}

func (p *SosAdObject) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weight", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:weight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Weight)); err != nil {
		return fmt.Errorf("%T.weight (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:weight: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("key", thrift.DOUBLE, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:key: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Key)); err != nil {
		return fmt.Errorf("%T.key (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:key: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_super_task", thrift.BOOL, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:is_super_task: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsSuperTask)); err != nil {
		return fmt.Errorf("%T.is_super_task (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:is_super_task: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extra_price", thrift.DOUBLE, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:extra_price: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.ExtraPrice)); err != nil {
		return fmt.Errorf("%T.extra_price (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:extra_price: %s", p, err)
	}
	return err
}

func (p *SosAdObject) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("super_task_display_weight", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:super_task_display_weight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SuperTaskDisplayWeight)); err != nil {
		return fmt.Errorf("%T.super_task_display_weight (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:super_task_display_weight: %s", p, err)
	}
	return err
}

func (p *SosAdObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosAdObject(%+v)", *p)
}

type AdResponse struct {
	Ads []*SosAdObject `thrift:"ads,1" json:"ads"`
}

func NewAdResponse() *AdResponse {
	return &AdResponse{}
}

func (p *AdResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdResponse) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ads = make([]*SosAdObject, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewSosAdObject()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Ads = append(p.Ads, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ads != nil {
		if err := oprot.WriteFieldBegin("ads", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ads: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ads)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ads {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ads: %s", p, err)
		}
	}
	return err
}

func (p *AdResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdResponse(%+v)", *p)
}

type PointResponse struct {
	TotalPoint    float64          `thrift:"totalPoint,1" json:"totalPoint"`
	ConsumedPoint float64          `thrift:"consumedPoint,2" json:"consumedPoint"`
	ConsumeStatus AowConsumeStatus `thrift:"consumeStatus,3" json:"consumeStatus"`
	ErrMessage    string           `thrift:"err_message,4" json:"err_message"`
}

func NewPointResponse() *PointResponse {
	return &PointResponse{
		ConsumeStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PointResponse) IsSetConsumeStatus() bool {
	return int64(p.ConsumeStatus) != math.MinInt32-1
}

func (p *PointResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PointResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalPoint = v
	}
	return nil
}

func (p *PointResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ConsumedPoint = v
	}
	return nil
}

func (p *PointResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ConsumeStatus = AowConsumeStatus(v)
	}
	return nil
}

func (p *PointResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ErrMessage = v
	}
	return nil
}

func (p *PointResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PointResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PointResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalPoint", thrift.DOUBLE, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalPoint: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.TotalPoint)); err != nil {
		return fmt.Errorf("%T.totalPoint (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalPoint: %s", p, err)
	}
	return err
}

func (p *PointResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumedPoint", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:consumedPoint: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.ConsumedPoint)); err != nil {
		return fmt.Errorf("%T.consumedPoint (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:consumedPoint: %s", p, err)
	}
	return err
}

func (p *PointResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConsumeStatus() {
		if err := oprot.WriteFieldBegin("consumeStatus", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:consumeStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ConsumeStatus)); err != nil {
			return fmt.Errorf("%T.consumeStatus (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:consumeStatus: %s", p, err)
		}
	}
	return err
}

func (p *PointResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("err_message", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:err_message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrMessage)); err != nil {
		return fmt.Errorf("%T.err_message (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:err_message: %s", p, err)
	}
	return err
}

func (p *PointResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PointResponse(%+v)", *p)
}
