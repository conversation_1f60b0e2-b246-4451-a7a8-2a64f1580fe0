// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package app_feature

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type AppFeaErrCode int64

const (
	AppFeaErrCode_SUCC             AppFeaErrCode = 0
	AppFeaErrCode_UNKNOWN_APP_ID   AppFeaErrCode = 1
	AppFeaErrCode_SERVER_INNER_ERR AppFeaErrCode = 2
	AppFeaErrCode_UNKNOWN_ERR      AppFeaErrCode = 15
)

func (p AppFeaErrCode) String() string {
	switch p {
	case AppFeaErrCode_SUCC:
		return "AppFeaErrCode_SUCC"
	case AppFeaErrCode_UNKNOWN_APP_ID:
		return "AppFeaErrCode_UNKNOWN_APP_ID"
	case AppFeaErrCode_SERVER_INNER_ERR:
		return "AppFeaErrCode_SERVER_INNER_ERR"
	case AppFeaErrCode_UNKNOWN_ERR:
		return "AppFeaErrCode_UNKNOWN_ERR"
	}
	return "<UNSET>"
}

func AppFeaErrCodeFromString(s string) (AppFeaErrCode, error) {
	switch s {
	case "AppFeaErrCode_SUCC":
		return AppFeaErrCode_SUCC, nil
	case "AppFeaErrCode_UNKNOWN_APP_ID":
		return AppFeaErrCode_UNKNOWN_APP_ID, nil
	case "AppFeaErrCode_SERVER_INNER_ERR":
		return AppFeaErrCode_SERVER_INNER_ERR, nil
	case "AppFeaErrCode_UNKNOWN_ERR":
		return AppFeaErrCode_UNKNOWN_ERR, nil
	}
	return AppFeaErrCode(math.MinInt32 - 1), fmt.Errorf("not a valid AppFeaErrCode string")
}

type AppFea struct {
	TrackCensoredName       string  `thrift:"trackCensoredName,1" json:"trackCensoredName"`
	CurrentVersionScore     float64 `thrift:"current_version_score,2" json:"current_version_score"`
	TrackContentRating      string  `thrift:"track_content_rating,3" json:"track_content_rating"`
	CurrentVersionUsercount int32   `thrift:"current_version_usercount,4" json:"current_version_usercount"`
	ArtWorkUrl              string  `thrift:"artWorkUrl,5" json:"artWorkUrl"`
}

func NewAppFea() *AppFea {
	return &AppFea{}
}

func (p *AppFea) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppFea) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TrackCensoredName = v
	}
	return nil
}

func (p *AppFea) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CurrentVersionScore = v
	}
	return nil
}

func (p *AppFea) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TrackContentRating = v
	}
	return nil
}

func (p *AppFea) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CurrentVersionUsercount = v
	}
	return nil
}

func (p *AppFea) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ArtWorkUrl = v
	}
	return nil
}

func (p *AppFea) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppFea"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppFea) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackCensoredName", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:trackCensoredName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackCensoredName)); err != nil {
		return fmt.Errorf("%T.trackCensoredName (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:trackCensoredName: %s", p, err)
	}
	return err
}

func (p *AppFea) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current_version_score", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:current_version_score: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.CurrentVersionScore)); err != nil {
		return fmt.Errorf("%T.current_version_score (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:current_version_score: %s", p, err)
	}
	return err
}

func (p *AppFea) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("track_content_rating", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:track_content_rating: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackContentRating)); err != nil {
		return fmt.Errorf("%T.track_content_rating (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:track_content_rating: %s", p, err)
	}
	return err
}

func (p *AppFea) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current_version_usercount", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:current_version_usercount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CurrentVersionUsercount)); err != nil {
		return fmt.Errorf("%T.current_version_usercount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:current_version_usercount: %s", p, err)
	}
	return err
}

func (p *AppFea) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("artWorkUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:artWorkUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ArtWorkUrl)); err != nil {
		return fmt.Errorf("%T.artWorkUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:artWorkUrl: %s", p, err)
	}
	return err
}

func (p *AppFea) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppFea(%+v)", *p)
}

type ErrStatus struct {
	ErrCode AppFeaErrCode `thrift:"err_code,1" json:"err_code"`
	ErrStr  string        `thrift:"err_str,2" json:"err_str"`
	ReqId   int32         `thrift:"req_id,3" json:"req_id"`
}

func NewErrStatus() *ErrStatus {
	return &ErrStatus{
		ErrCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ErrStatus) IsSetErrCode() bool {
	return int64(p.ErrCode) != math.MinInt32-1
}

func (p *ErrStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ErrStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ErrCode = AppFeaErrCode(v)
	}
	return nil
}

func (p *ErrStatus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ErrStr = v
	}
	return nil
}

func (p *ErrStatus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ReqId = v
	}
	return nil
}

func (p *ErrStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ErrStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ErrStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrCode() {
		if err := oprot.WriteFieldBegin("err_code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:err_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ErrCode)); err != nil {
			return fmt.Errorf("%T.err_code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:err_code: %s", p, err)
		}
	}
	return err
}

func (p *ErrStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("err_str", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:err_str: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrStr)); err != nil {
		return fmt.Errorf("%T.err_str (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:err_str: %s", p, err)
	}
	return err
}

func (p *ErrStatus) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:req_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqId)); err != nil {
		return fmt.Errorf("%T.req_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:req_id: %s", p, err)
	}
	return err
}

func (p *ErrStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrStatus(%+v)", *p)
}

type AppFeaReq struct {
	ReqId int32 `thrift:"req_id,1" json:"req_id"`
	AppId int32 `thrift:"app_id,2" json:"app_id"`
}

func NewAppFeaReq() *AppFeaReq {
	return &AppFeaReq{}
}

func (p *AppFeaReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppFeaReq) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ReqId = v
	}
	return nil
}

func (p *AppFeaReq) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AppFeaReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppFeaReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppFeaReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:req_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqId)); err != nil {
		return fmt.Errorf("%T.req_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:req_id: %s", p, err)
	}
	return err
}

func (p *AppFeaReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:app_id: %s", p, err)
	}
	return err
}

func (p *AppFeaReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppFeaReq(%+v)", *p)
}

type AppFeaRes struct {
	Es     *ErrStatus `thrift:"es,1" json:"es"`
	AppFea *AppFea    `thrift:"app_fea,2" json:"app_fea"`
}

func NewAppFeaRes() *AppFeaRes {
	return &AppFeaRes{}
}

func (p *AppFeaRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppFeaRes) readField1(iprot thrift.TProtocol) error {
	p.Es = NewErrStatus()
	if err := p.Es.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Es)
	}
	return nil
}

func (p *AppFeaRes) readField2(iprot thrift.TProtocol) error {
	p.AppFea = NewAppFea()
	if err := p.AppFea.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppFea)
	}
	return nil
}

func (p *AppFeaRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppFeaRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppFeaRes) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Es != nil {
		if err := oprot.WriteFieldBegin("es", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:es: %s", p, err)
		}
		if err := p.Es.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Es)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:es: %s", p, err)
		}
	}
	return err
}

func (p *AppFeaRes) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppFea != nil {
		if err := oprot.WriteFieldBegin("app_fea", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_fea: %s", p, err)
		}
		if err := p.AppFea.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppFea)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_fea: %s", p, err)
		}
	}
	return err
}

func (p *AppFeaRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppFeaRes(%+v)", *p)
}
