// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package app_feature

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__

type GetAppFea interface {
	dm303.DomobService

	// Parameters:
	//  - AppFeaReq
	GetAppFea(app_fea_req *AppFeaReq) (r *AppFeaRes, err error)
}

type GetAppFeaClient struct {
	*dm303.DomobServiceClient
}

func NewGetAppFeaClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *GetAppFeaClient {
	return &GetAppFeaClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewGetAppFeaClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *GetAppFeaClient {
	return &GetAppFeaClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - AppFeaReq
func (p *GetAppFeaClient) GetAppFea(app_fea_req *AppFeaReq) (r *AppFeaRes, err error) {
	if err = p.sendGetAppFea(app_fea_req); err != nil {
		return
	}
	return p.recvGetAppFea()
}

func (p *GetAppFeaClient) sendGetAppFea(app_fea_req *AppFeaReq) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_app_fea", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetAppFeaArgs()
	args0.AppFeaReq = app_fea_req
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *GetAppFeaClient) recvGetAppFea() (value *AppFeaRes, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetAppFeaResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

type GetAppFeaProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewGetAppFeaProcessor(handler GetAppFea) *GetAppFeaProcessor {
	self4 := &GetAppFeaProcessor{dm303.NewDomobServiceProcessor(handler)}
	self4.AddToProcessorMap("get_app_fea", &getAppFeaProcessorGetAppFea{handler: handler})
	return self4
}

type getAppFeaProcessorGetAppFea struct {
	handler GetAppFea
}

func (p *getAppFeaProcessorGetAppFea) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppFeaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_app_fea", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppFeaResult()
	if result.Success, err = p.handler.GetAppFea(args.AppFeaReq); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_app_fea: "+err.Error())
		oprot.WriteMessageBegin("get_app_fea", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_app_fea", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetAppFeaArgs struct {
	AppFeaReq *AppFeaReq `thrift:"app_fea_req,1" json:"app_fea_req"`
}

func NewGetAppFeaArgs() *GetAppFeaArgs {
	return &GetAppFeaArgs{}
}

func (p *GetAppFeaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppFeaArgs) readField1(iprot thrift.TProtocol) error {
	p.AppFeaReq = NewAppFeaReq()
	if err := p.AppFeaReq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppFeaReq)
	}
	return nil
}

func (p *GetAppFeaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_app_fea_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppFeaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.AppFeaReq != nil {
		if err := oprot.WriteFieldBegin("app_fea_req", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:app_fea_req: %s", p, err)
		}
		if err := p.AppFeaReq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppFeaReq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:app_fea_req: %s", p, err)
		}
	}
	return err
}

func (p *GetAppFeaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppFeaArgs(%+v)", *p)
}

type GetAppFeaResult struct {
	Success *AppFeaRes `thrift:"success,0" json:"success"`
}

func NewGetAppFeaResult() *GetAppFeaResult {
	return &GetAppFeaResult{}
}

func (p *GetAppFeaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppFeaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppFeaRes()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppFeaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_app_fea_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppFeaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppFeaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppFeaResult(%+v)", *p)
}
