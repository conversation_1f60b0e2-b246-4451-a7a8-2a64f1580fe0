// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"sos_stserver"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  StResponse settle(RequestHeader header, StObject stobj)")
	fmt.Fprintln(os.Stderr, "  DrawResponse draw(RequestHeader header, DrawObject drawObj)")
	fmt.Fprintln(os.<PERSON>, "  DrawResponse drawv2(RequestHeader header, i32 userid, i32 point)")
	fmt.Fprintln(os.Stderr, "  GiftResponse present(RequestHeader header, GiftObject giftObj)")
	fmt.Fprintln(os.Stderr, "  DrawConfig get_draw_config(RequestHeader header, i32 userid)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := sos_stserver.NewStServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "settle":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "Settle requires 2 args")
			flag.Usage()
		}
		arg22 := flag.Arg(1)
		mbTrans23 := thrift.NewTMemoryBufferLen(len(arg22))
		defer mbTrans23.Close()
		_, err24 := mbTrans23.WriteString(arg22)
		if err24 != nil {
			Usage()
			return
		}
		factory25 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt26 := factory25.GetProtocol(mbTrans23)
		argvalue0 := sos_stserver.NewRequestHeader()
		err27 := argvalue0.Read(jsProt26)
		if err27 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg28 := flag.Arg(2)
		mbTrans29 := thrift.NewTMemoryBufferLen(len(arg28))
		defer mbTrans29.Close()
		_, err30 := mbTrans29.WriteString(arg28)
		if err30 != nil {
			Usage()
			return
		}
		factory31 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt32 := factory31.GetProtocol(mbTrans29)
		argvalue1 := sos_stserver.NewStObject()
		err33 := argvalue1.Read(jsProt32)
		if err33 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.Settle(value0, value1))
		fmt.Print("\n")
		break
	case "draw":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "Draw requires 2 args")
			flag.Usage()
		}
		arg34 := flag.Arg(1)
		mbTrans35 := thrift.NewTMemoryBufferLen(len(arg34))
		defer mbTrans35.Close()
		_, err36 := mbTrans35.WriteString(arg34)
		if err36 != nil {
			Usage()
			return
		}
		factory37 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt38 := factory37.GetProtocol(mbTrans35)
		argvalue0 := sos_stserver.NewRequestHeader()
		err39 := argvalue0.Read(jsProt38)
		if err39 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg40 := flag.Arg(2)
		mbTrans41 := thrift.NewTMemoryBufferLen(len(arg40))
		defer mbTrans41.Close()
		_, err42 := mbTrans41.WriteString(arg40)
		if err42 != nil {
			Usage()
			return
		}
		factory43 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt44 := factory43.GetProtocol(mbTrans41)
		argvalue1 := sos_stserver.NewDrawObject()
		err45 := argvalue1.Read(jsProt44)
		if err45 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.Draw(value0, value1))
		fmt.Print("\n")
		break
	case "drawv2":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "Drawv2 requires 3 args")
			flag.Usage()
		}
		arg46 := flag.Arg(1)
		mbTrans47 := thrift.NewTMemoryBufferLen(len(arg46))
		defer mbTrans47.Close()
		_, err48 := mbTrans47.WriteString(arg46)
		if err48 != nil {
			Usage()
			return
		}
		factory49 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt50 := factory49.GetProtocol(mbTrans47)
		argvalue0 := sos_stserver.NewRequestHeader()
		err51 := argvalue0.Read(jsProt50)
		if err51 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err52 := (strconv.Atoi(flag.Arg(2)))
		if err52 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err53 := (strconv.Atoi(flag.Arg(3)))
		if err53 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.Drawv2(value0, value1, value2))
		fmt.Print("\n")
		break
	case "present":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "Present requires 2 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := sos_stserver.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg60 := flag.Arg(2)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue1 := sos_stserver.NewGiftObject()
		err65 := argvalue1.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.Present(value0, value1))
		fmt.Print("\n")
		break
	case "get_draw_config":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDrawConfig requires 2 args")
			flag.Usage()
		}
		arg66 := flag.Arg(1)
		mbTrans67 := thrift.NewTMemoryBufferLen(len(arg66))
		defer mbTrans67.Close()
		_, err68 := mbTrans67.WriteString(arg66)
		if err68 != nil {
			Usage()
			return
		}
		factory69 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt70 := factory69.GetProtocol(mbTrans67)
		argvalue0 := sos_stserver.NewRequestHeader()
		err71 := argvalue0.Read(jsProt70)
		if err71 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err72 := (strconv.Atoi(flag.Arg(2)))
		if err72 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetDrawConfig(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
