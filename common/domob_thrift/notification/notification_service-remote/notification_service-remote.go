// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"notification"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  void readNoticesByIds(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteNoticesByIds(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.<PERSON>, "  NoticeQueryResult listNoticesByTimeAndTypeAndStatus(RequestHeader header, UidInt uid, TimeInt startTime, TimeInt endTime, NoticeType type, NoticeStatus status, NoticeQueryInt offset, NoticeQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getNoticesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  NotificationSetting getNotificationSetting(RequestHeader header, UidInt uid)")
	fmt.Fprintln(os.Stderr, "  void editNotificationSetting(RequestHeader header, NotificationSetting notificationSetting)")
	fmt.Fprintln(os.Stderr, "  void readBroadcastNoticeById(RequestHeader header, UidInt uid, BroadcastNoticeIdInt noticeId)")
	fmt.Fprintln(os.Stderr, "  BroadNoticeQueryResult listBroadcastNoticesByUidAndReadStatus(RequestHeader header, UidInt uid, NoticeStatus readStatus, BroadNoticeQueryInt offset, BroadNoticeQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getBroadcastNoticesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  BroadcastNoticeIdInt adminAddBroadcastNotice(RequestHeader header, BroadcastNotice broadcastNotice)")
	fmt.Fprintln(os.Stderr, "  void adminEditBroadcastNotice(RequestHeader header, BroadcastNotice broadcastNotice)")
	fmt.Fprintln(os.Stderr, "  void adminDeleteBroadcastNotices(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  BroadNoticeQueryResult adminListBroadcastNoticesByStatus(RequestHeader header, BroadcastNoticeStatus status, bool excludeNotStartedOrExpired, BroadNoticeQueryInt offset, BroadNoticeQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := notification.NewNotificationServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "readNoticesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ReadNoticesByIds requires 3 args")
			flag.Usage()
		}
		arg63 := flag.Arg(1)
		mbTrans64 := thrift.NewTMemoryBufferLen(len(arg63))
		defer mbTrans64.Close()
		_, err65 := mbTrans64.WriteString(arg63)
		if err65 != nil {
			Usage()
			return
		}
		factory66 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt67 := factory66.GetProtocol(mbTrans64)
		argvalue0 := notification.NewRequestHeader()
		err68 := argvalue0.Read(jsProt67)
		if err68 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err69 := (strconv.Atoi(flag.Arg(2)))
		if err69 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := notification.UidInt(argvalue1)
		arg70 := flag.Arg(3)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		containerStruct2 := notification.NewReadNoticesByIdsArgs()
		err75 := containerStruct2.ReadField3(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ReadNoticesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteNoticesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteNoticesByIds requires 3 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := notification.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err82 := (strconv.Atoi(flag.Arg(2)))
		if err82 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := notification.UidInt(argvalue1)
		arg83 := flag.Arg(3)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		containerStruct2 := notification.NewDeleteNoticesByIdsArgs()
		err88 := containerStruct2.ReadField3(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteNoticesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listNoticesByTimeAndTypeAndStatus":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "ListNoticesByTimeAndTypeAndStatus requires 9 args")
			flag.Usage()
		}
		arg89 := flag.Arg(1)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		argvalue0 := notification.NewRequestHeader()
		err94 := argvalue0.Read(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err95 := (strconv.Atoi(flag.Arg(2)))
		if err95 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := notification.UidInt(argvalue1)
		argvalue2, err96 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err96 != nil {
			Usage()
			return
		}
		value2 := notification.TimeInt(argvalue2)
		argvalue3, err97 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err97 != nil {
			Usage()
			return
		}
		value3 := notification.TimeInt(argvalue3)
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := notification.NoticeType(tmp4)
		value4 := notification.NoticeType(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := notification.NoticeStatus(tmp5)
		value5 := notification.NoticeStatus(argvalue5)
		tmp6, err98 := (strconv.Atoi(flag.Arg(7)))
		if err98 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := notification.NoticeQueryInt(argvalue6)
		tmp7, err99 := (strconv.Atoi(flag.Arg(8)))
		if err99 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := notification.NoticeQueryInt(argvalue7)
		argvalue8 := flag.Arg(9) == "true"
		value8 := argvalue8
		fmt.Print(client.ListNoticesByTimeAndTypeAndStatus(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "getNoticesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetNoticesByIds requires 2 args")
			flag.Usage()
		}
		arg101 := flag.Arg(1)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		argvalue0 := notification.NewRequestHeader()
		err106 := argvalue0.Read(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		arg107 := flag.Arg(2)
		mbTrans108 := thrift.NewTMemoryBufferLen(len(arg107))
		defer mbTrans108.Close()
		_, err109 := mbTrans108.WriteString(arg107)
		if err109 != nil {
			Usage()
			return
		}
		factory110 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt111 := factory110.GetProtocol(mbTrans108)
		containerStruct1 := notification.NewGetNoticesByIdsArgs()
		err112 := containerStruct1.ReadField2(jsProt111)
		if err112 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetNoticesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getNotificationSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetNotificationSetting requires 2 args")
			flag.Usage()
		}
		arg113 := flag.Arg(1)
		mbTrans114 := thrift.NewTMemoryBufferLen(len(arg113))
		defer mbTrans114.Close()
		_, err115 := mbTrans114.WriteString(arg113)
		if err115 != nil {
			Usage()
			return
		}
		factory116 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt117 := factory116.GetProtocol(mbTrans114)
		argvalue0 := notification.NewRequestHeader()
		err118 := argvalue0.Read(jsProt117)
		if err118 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err119 := (strconv.Atoi(flag.Arg(2)))
		if err119 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := notification.UidInt(argvalue1)
		fmt.Print(client.GetNotificationSetting(value0, value1))
		fmt.Print("\n")
		break
	case "editNotificationSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditNotificationSetting requires 2 args")
			flag.Usage()
		}
		arg120 := flag.Arg(1)
		mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
		defer mbTrans121.Close()
		_, err122 := mbTrans121.WriteString(arg120)
		if err122 != nil {
			Usage()
			return
		}
		factory123 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt124 := factory123.GetProtocol(mbTrans121)
		argvalue0 := notification.NewRequestHeader()
		err125 := argvalue0.Read(jsProt124)
		if err125 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		arg126 := flag.Arg(2)
		mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
		defer mbTrans127.Close()
		_, err128 := mbTrans127.WriteString(arg126)
		if err128 != nil {
			Usage()
			return
		}
		factory129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt130 := factory129.GetProtocol(mbTrans127)
		argvalue1 := notification.NewNotificationSetting()
		err131 := argvalue1.Read(jsProt130)
		if err131 != nil {
			Usage()
			return
		}
		value1 := notification.NotificationSetting(argvalue1)
		fmt.Print(client.EditNotificationSetting(value0, value1))
		fmt.Print("\n")
		break
	case "readBroadcastNoticeById":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ReadBroadcastNoticeById requires 3 args")
			flag.Usage()
		}
		arg132 := flag.Arg(1)
		mbTrans133 := thrift.NewTMemoryBufferLen(len(arg132))
		defer mbTrans133.Close()
		_, err134 := mbTrans133.WriteString(arg132)
		if err134 != nil {
			Usage()
			return
		}
		factory135 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt136 := factory135.GetProtocol(mbTrans133)
		argvalue0 := notification.NewRequestHeader()
		err137 := argvalue0.Read(jsProt136)
		if err137 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err138 := (strconv.Atoi(flag.Arg(2)))
		if err138 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := notification.UidInt(argvalue1)
		tmp2, err139 := (strconv.Atoi(flag.Arg(3)))
		if err139 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := notification.BroadcastNoticeIdInt(argvalue2)
		fmt.Print(client.ReadBroadcastNoticeById(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listBroadcastNoticesByUidAndReadStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListBroadcastNoticesByUidAndReadStatus requires 6 args")
			flag.Usage()
		}
		arg140 := flag.Arg(1)
		mbTrans141 := thrift.NewTMemoryBufferLen(len(arg140))
		defer mbTrans141.Close()
		_, err142 := mbTrans141.WriteString(arg140)
		if err142 != nil {
			Usage()
			return
		}
		factory143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt144 := factory143.GetProtocol(mbTrans141)
		argvalue0 := notification.NewRequestHeader()
		err145 := argvalue0.Read(jsProt144)
		if err145 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err146 := (strconv.Atoi(flag.Arg(2)))
		if err146 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := notification.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := notification.NoticeStatus(tmp2)
		value2 := notification.NoticeStatus(argvalue2)
		tmp3, err147 := (strconv.Atoi(flag.Arg(4)))
		if err147 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := notification.BroadNoticeQueryInt(argvalue3)
		tmp4, err148 := (strconv.Atoi(flag.Arg(5)))
		if err148 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := notification.BroadNoticeQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListBroadcastNoticesByUidAndReadStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getBroadcastNoticesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetBroadcastNoticesByIds requires 2 args")
			flag.Usage()
		}
		arg150 := flag.Arg(1)
		mbTrans151 := thrift.NewTMemoryBufferLen(len(arg150))
		defer mbTrans151.Close()
		_, err152 := mbTrans151.WriteString(arg150)
		if err152 != nil {
			Usage()
			return
		}
		factory153 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt154 := factory153.GetProtocol(mbTrans151)
		argvalue0 := notification.NewRequestHeader()
		err155 := argvalue0.Read(jsProt154)
		if err155 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		arg156 := flag.Arg(2)
		mbTrans157 := thrift.NewTMemoryBufferLen(len(arg156))
		defer mbTrans157.Close()
		_, err158 := mbTrans157.WriteString(arg156)
		if err158 != nil {
			Usage()
			return
		}
		factory159 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt160 := factory159.GetProtocol(mbTrans157)
		containerStruct1 := notification.NewGetBroadcastNoticesByIdsArgs()
		err161 := containerStruct1.ReadField2(jsProt160)
		if err161 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetBroadcastNoticesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "adminAddBroadcastNotice":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AdminAddBroadcastNotice requires 2 args")
			flag.Usage()
		}
		arg162 := flag.Arg(1)
		mbTrans163 := thrift.NewTMemoryBufferLen(len(arg162))
		defer mbTrans163.Close()
		_, err164 := mbTrans163.WriteString(arg162)
		if err164 != nil {
			Usage()
			return
		}
		factory165 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt166 := factory165.GetProtocol(mbTrans163)
		argvalue0 := notification.NewRequestHeader()
		err167 := argvalue0.Read(jsProt166)
		if err167 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		arg168 := flag.Arg(2)
		mbTrans169 := thrift.NewTMemoryBufferLen(len(arg168))
		defer mbTrans169.Close()
		_, err170 := mbTrans169.WriteString(arg168)
		if err170 != nil {
			Usage()
			return
		}
		factory171 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt172 := factory171.GetProtocol(mbTrans169)
		argvalue1 := notification.NewBroadcastNotice()
		err173 := argvalue1.Read(jsProt172)
		if err173 != nil {
			Usage()
			return
		}
		value1 := notification.BroadcastNotice(argvalue1)
		fmt.Print(client.AdminAddBroadcastNotice(value0, value1))
		fmt.Print("\n")
		break
	case "adminEditBroadcastNotice":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AdminEditBroadcastNotice requires 2 args")
			flag.Usage()
		}
		arg174 := flag.Arg(1)
		mbTrans175 := thrift.NewTMemoryBufferLen(len(arg174))
		defer mbTrans175.Close()
		_, err176 := mbTrans175.WriteString(arg174)
		if err176 != nil {
			Usage()
			return
		}
		factory177 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt178 := factory177.GetProtocol(mbTrans175)
		argvalue0 := notification.NewRequestHeader()
		err179 := argvalue0.Read(jsProt178)
		if err179 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		arg180 := flag.Arg(2)
		mbTrans181 := thrift.NewTMemoryBufferLen(len(arg180))
		defer mbTrans181.Close()
		_, err182 := mbTrans181.WriteString(arg180)
		if err182 != nil {
			Usage()
			return
		}
		factory183 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt184 := factory183.GetProtocol(mbTrans181)
		argvalue1 := notification.NewBroadcastNotice()
		err185 := argvalue1.Read(jsProt184)
		if err185 != nil {
			Usage()
			return
		}
		value1 := notification.BroadcastNotice(argvalue1)
		fmt.Print(client.AdminEditBroadcastNotice(value0, value1))
		fmt.Print("\n")
		break
	case "adminDeleteBroadcastNotices":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AdminDeleteBroadcastNotices requires 2 args")
			flag.Usage()
		}
		arg186 := flag.Arg(1)
		mbTrans187 := thrift.NewTMemoryBufferLen(len(arg186))
		defer mbTrans187.Close()
		_, err188 := mbTrans187.WriteString(arg186)
		if err188 != nil {
			Usage()
			return
		}
		factory189 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt190 := factory189.GetProtocol(mbTrans187)
		argvalue0 := notification.NewRequestHeader()
		err191 := argvalue0.Read(jsProt190)
		if err191 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		arg192 := flag.Arg(2)
		mbTrans193 := thrift.NewTMemoryBufferLen(len(arg192))
		defer mbTrans193.Close()
		_, err194 := mbTrans193.WriteString(arg192)
		if err194 != nil {
			Usage()
			return
		}
		factory195 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt196 := factory195.GetProtocol(mbTrans193)
		containerStruct1 := notification.NewAdminDeleteBroadcastNoticesArgs()
		err197 := containerStruct1.ReadField2(jsProt196)
		if err197 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.AdminDeleteBroadcastNotices(value0, value1))
		fmt.Print("\n")
		break
	case "adminListBroadcastNoticesByStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "AdminListBroadcastNoticesByStatus requires 6 args")
			flag.Usage()
		}
		arg198 := flag.Arg(1)
		mbTrans199 := thrift.NewTMemoryBufferLen(len(arg198))
		defer mbTrans199.Close()
		_, err200 := mbTrans199.WriteString(arg198)
		if err200 != nil {
			Usage()
			return
		}
		factory201 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt202 := factory201.GetProtocol(mbTrans199)
		argvalue0 := notification.NewRequestHeader()
		err203 := argvalue0.Read(jsProt202)
		if err203 != nil {
			Usage()
			return
		}
		value0 := notification.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := notification.BroadcastNoticeStatus(tmp1)
		value1 := notification.BroadcastNoticeStatus(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err205 := (strconv.Atoi(flag.Arg(4)))
		if err205 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := notification.BroadNoticeQueryInt(argvalue3)
		tmp4, err206 := (strconv.Atoi(flag.Arg(5)))
		if err206 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := notification.BroadNoticeQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.AdminListBroadcastNoticesByStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
