// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package notification

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/notification_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = notification_types.GoUnusedProtection__
var GoUnusedProtection__ int

//NotificationException中可能出现的异常代码
type NotificationServiceCode int64

const (
	NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_EXIST             NotificationServiceCode = 24301
	NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_MATCH             NotificationServiceCode = 24302
	NotificationServiceCode_ERROR_NOTIFICATION_NOTICE_NOT_EXIST           NotificationServiceCode = 24303
	NotificationServiceCode_ERROR_NOTIFICATION_BROADCAST_NOTICE_NOT_EXIST NotificationServiceCode = 24304
	NotificationServiceCode_ERROR_NOTIFICATION_USER_BANNED                NotificationServiceCode = 24305
	NotificationServiceCode_ERROR_NOTIFICATION_PARAM_INVALID              NotificationServiceCode = 24401
	NotificationServiceCode_ERROR_NOTIFICATION_SYSTEM_ERROR               NotificationServiceCode = 24501
)

func (p NotificationServiceCode) String() string {
	switch p {
	case NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_EXIST:
		return "NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_EXIST"
	case NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_MATCH:
		return "NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_MATCH"
	case NotificationServiceCode_ERROR_NOTIFICATION_NOTICE_NOT_EXIST:
		return "NotificationServiceCode_ERROR_NOTIFICATION_NOTICE_NOT_EXIST"
	case NotificationServiceCode_ERROR_NOTIFICATION_BROADCAST_NOTICE_NOT_EXIST:
		return "NotificationServiceCode_ERROR_NOTIFICATION_BROADCAST_NOTICE_NOT_EXIST"
	case NotificationServiceCode_ERROR_NOTIFICATION_USER_BANNED:
		return "NotificationServiceCode_ERROR_NOTIFICATION_USER_BANNED"
	case NotificationServiceCode_ERROR_NOTIFICATION_PARAM_INVALID:
		return "NotificationServiceCode_ERROR_NOTIFICATION_PARAM_INVALID"
	case NotificationServiceCode_ERROR_NOTIFICATION_SYSTEM_ERROR:
		return "NotificationServiceCode_ERROR_NOTIFICATION_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func NotificationServiceCodeFromString(s string) (NotificationServiceCode, error) {
	switch s {
	case "NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_EXIST":
		return NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_EXIST, nil
	case "NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_MATCH":
		return NotificationServiceCode_ERROR_NOTIFICATION_USER_NOT_MATCH, nil
	case "NotificationServiceCode_ERROR_NOTIFICATION_NOTICE_NOT_EXIST":
		return NotificationServiceCode_ERROR_NOTIFICATION_NOTICE_NOT_EXIST, nil
	case "NotificationServiceCode_ERROR_NOTIFICATION_BROADCAST_NOTICE_NOT_EXIST":
		return NotificationServiceCode_ERROR_NOTIFICATION_BROADCAST_NOTICE_NOT_EXIST, nil
	case "NotificationServiceCode_ERROR_NOTIFICATION_USER_BANNED":
		return NotificationServiceCode_ERROR_NOTIFICATION_USER_BANNED, nil
	case "NotificationServiceCode_ERROR_NOTIFICATION_PARAM_INVALID":
		return NotificationServiceCode_ERROR_NOTIFICATION_PARAM_INVALID, nil
	case "NotificationServiceCode_ERROR_NOTIFICATION_SYSTEM_ERROR":
		return NotificationServiceCode_ERROR_NOTIFICATION_SYSTEM_ERROR, nil
	}
	return NotificationServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid NotificationServiceCode string")
}

type UidInt notification_types.UidInt

type NoticeIdInt notification_types.NoticeIdInt

type BroadcastNoticeIdInt notification_types.BroadcastNoticeIdInt

type TimeInt notification_types.TimeInt

type NoticeQueryResult *common.QueryResult

type NoticeQueryInt notification_types.NoticeQueryInt

type BroadNoticeQueryResult *common.QueryResult

type BroadNoticeQueryInt notification_types.BroadNoticeQueryInt

type RequestHeader *common.RequestHeader

type Notice *notification_types.Notice

type BroadcastNotice *notification_types.BroadcastNotice

type NoticeType notification_types.NoticeType

type NoticeStatus notification_types.NoticeStatus

type BroadcastNoticeStatus notification_types.BroadcastNoticeStatus

type NotificationSetting *notification_types.NotificationSetting

type NotificationException struct {
	Code    NotificationServiceCode `thrift:"code,1" json:"code"`
	Message string                  `thrift:"message,2" json:"message"`
}

func NewNotificationException() *NotificationException {
	return &NotificationException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *NotificationException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *NotificationException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NotificationException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = NotificationServiceCode(v)
	}
	return nil
}

func (p *NotificationException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *NotificationException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("NotificationException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NotificationException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *NotificationException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *NotificationException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NotificationException(%+v)", *p)
}
