// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package notification

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/notification_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = notification_types.GoUnusedProtection__

type NotificationService interface { //通知服务接口定义

	// 用户阅读站内通知消息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - Ids: 站内通知编号列表
	ReadNoticesByIds(header *common.RequestHeader, uid UidInt, ids []NoticeIdInt) (ne *NotificationException, err error)
	// 用户删除站内通知消息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - Ids: 站内通知编号列表
	DeleteNoticesByIds(header *common.RequestHeader, uid UidInt, ids []NoticeIdInt) (ne *NotificationException, err error)
	// 分页列出站内消息，按创建时间排序。
	// 如果指定了日期区间，则只返回该日期区间内的通知。
	// 如果提供了通知类别参数，则只返回某一类别的通知。
	// 如果提供了通知状态参数，则只返回某一状态的通知。
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - StartTime: 查询开始时间(包含), 为0表示未提供
	//  - EndTime: 查询结束时间(包含), 为0表示未提供
	//  - TypeA1: 站内通知类型
	//  - Status: 站内通知状态
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListNoticesByTimeAndTypeAndStatus(header *common.RequestHeader, uid UidInt, startTime TimeInt, endTime TimeInt, type_a1 NoticeType, status NoticeStatus, offset NoticeQueryInt, limit NoticeQueryInt, ascending bool) (r *common.QueryResult, ne *NotificationException, err error)
	// 根据站内通知编号批量获取站内通知
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 站内通知编号列表
	GetNoticesByIds(header *common.RequestHeader, ids []NoticeIdInt) (r map[NoticeIdInt]*notification_types.Notice, ne *NotificationException, err error)
	// 获取通知接收方式及具体接收邮箱/手机
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	GetNotificationSetting(header *common.RequestHeader, uid UidInt) (r *notification_types.NotificationSetting, ne *NotificationException, err error)
	// 设置通知接收方式及具体接收邮箱/手机
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - NotificationSetting: 通知消息具体设置
	EditNotificationSetting(header *common.RequestHeader, notificationSetting *notification_types.NotificationSetting) (ne *NotificationException, err error)
	// 用户阅读到站内广播通知编号为noticeId的消息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - NoticeId: 用户阅读到的站内广播通知编号
	ReadBroadcastNoticeById(header *common.RequestHeader, uid UidInt, noticeId BroadcastNoticeIdInt) (ne *NotificationException, err error)
	// 分页列出某用户未读的站内广播消息，按创建时间排序。
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - ReadStatus: 通知状态，合法的值为不填、已读、未读
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListBroadcastNoticesByUidAndReadStatus(header *common.RequestHeader, uid UidInt, readStatus NoticeStatus, offset BroadNoticeQueryInt, limit BroadNoticeQueryInt, ascending bool) (r *common.QueryResult, ne *NotificationException, err error)
	// 根据站内广播通知编号批量获取站内广播通知
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 站内广播通知编号列表
	GetBroadcastNoticesByIds(header *common.RequestHeader, ids []BroadcastNoticeIdInt) (r map[BroadcastNoticeIdInt]*notification_types.BroadcastNotice, ne *NotificationException, err error)
	// 管理员发布站内广播通知
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - BroadcastNotice: 站内广播通知结构体
	AdminAddBroadcastNotice(header *common.RequestHeader, broadcastNotice *notification_types.BroadcastNotice) (r BroadcastNoticeIdInt, ne *NotificationException, err error)
	// 管理员修改站内广播通知
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - BroadcastNotice: 站内广播通知结构体
	AdminEditBroadcastNotice(header *common.RequestHeader, broadcastNotice *notification_types.BroadcastNotice) (ne *NotificationException, err error)
	// 管理员删除站内广播通知
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 站内广播通知编号列表
	AdminDeleteBroadcastNotices(header *common.RequestHeader, ids []BroadcastNoticeIdInt) (ne *NotificationException, err error)
	// 管理员分页列出站内广播消息，按创建时间排序。
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Status: 站内广播通知状态，可以不填表示不做限定
	//  - ExcludeNotStartedOrExpired: 是否排除未生效和过期的
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	AdminListBroadcastNoticesByStatus(header *common.RequestHeader, status BroadcastNoticeStatus, excludeNotStartedOrExpired bool, offset BroadNoticeQueryInt, limit BroadNoticeQueryInt, ascending bool) (r *common.QueryResult, ne *NotificationException, err error)
}

//通知服务接口定义
type NotificationServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewNotificationServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *NotificationServiceClient {
	return &NotificationServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewNotificationServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *NotificationServiceClient {
	return &NotificationServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 用户阅读站内通知消息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - Ids: 站内通知编号列表
func (p *NotificationServiceClient) ReadNoticesByIds(header *common.RequestHeader, uid UidInt, ids []NoticeIdInt) (ne *NotificationException, err error) {
	if err = p.sendReadNoticesByIds(header, uid, ids); err != nil {
		return
	}
	return p.recvReadNoticesByIds()
}

func (p *NotificationServiceClient) sendReadNoticesByIds(header *common.RequestHeader, uid UidInt, ids []NoticeIdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("readNoticesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewReadNoticesByIdsArgs()
	args0.Header = header
	args0.Uid = uid
	args0.Ids = ids
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvReadNoticesByIds() (ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewReadNoticesByIdsResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result1.Ne != nil {
		ne = result1.Ne
	}
	return
}

// 用户删除站内通知消息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - Ids: 站内通知编号列表
func (p *NotificationServiceClient) DeleteNoticesByIds(header *common.RequestHeader, uid UidInt, ids []NoticeIdInt) (ne *NotificationException, err error) {
	if err = p.sendDeleteNoticesByIds(header, uid, ids); err != nil {
		return
	}
	return p.recvDeleteNoticesByIds()
}

func (p *NotificationServiceClient) sendDeleteNoticesByIds(header *common.RequestHeader, uid UidInt, ids []NoticeIdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteNoticesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewDeleteNoticesByIdsArgs()
	args4.Header = header
	args4.Uid = uid
	args4.Ids = ids
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvDeleteNoticesByIds() (ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewDeleteNoticesByIdsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result5.Ne != nil {
		ne = result5.Ne
	}
	return
}

// 分页列出站内消息，按创建时间排序。
// 如果指定了日期区间，则只返回该日期区间内的通知。
// 如果提供了通知类别参数，则只返回某一类别的通知。
// 如果提供了通知状态参数，则只返回某一状态的通知。
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - StartTime: 查询开始时间(包含), 为0表示未提供
//  - EndTime: 查询结束时间(包含), 为0表示未提供
//  - TypeA1: 站内通知类型
//  - Status: 站内通知状态
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *NotificationServiceClient) ListNoticesByTimeAndTypeAndStatus(header *common.RequestHeader, uid UidInt, startTime TimeInt, endTime TimeInt, type_a1 NoticeType, status NoticeStatus, offset NoticeQueryInt, limit NoticeQueryInt, ascending bool) (r *common.QueryResult, ne *NotificationException, err error) {
	if err = p.sendListNoticesByTimeAndTypeAndStatus(header, uid, startTime, endTime, type_a1, status, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListNoticesByTimeAndTypeAndStatus()
}

func (p *NotificationServiceClient) sendListNoticesByTimeAndTypeAndStatus(header *common.RequestHeader, uid UidInt, startTime TimeInt, endTime TimeInt, type_a1 NoticeType, status NoticeStatus, offset NoticeQueryInt, limit NoticeQueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listNoticesByTimeAndTypeAndStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewListNoticesByTimeAndTypeAndStatusArgs()
	args8.Header = header
	args8.Uid = uid
	args8.StartTime = startTime
	args8.EndTime = endTime
	args8.TypeA1 = type_a1
	args8.Status = status
	args8.Offset = offset
	args8.Limit = limit
	args8.Ascending = ascending
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvListNoticesByTimeAndTypeAndStatus() (value *common.QueryResult, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewListNoticesByTimeAndTypeAndStatusResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Ne != nil {
		ne = result9.Ne
	}
	return
}

// 根据站内通知编号批量获取站内通知
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 站内通知编号列表
func (p *NotificationServiceClient) GetNoticesByIds(header *common.RequestHeader, ids []NoticeIdInt) (r map[NoticeIdInt]*notification_types.Notice, ne *NotificationException, err error) {
	if err = p.sendGetNoticesByIds(header, ids); err != nil {
		return
	}
	return p.recvGetNoticesByIds()
}

func (p *NotificationServiceClient) sendGetNoticesByIds(header *common.RequestHeader, ids []NoticeIdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getNoticesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetNoticesByIdsArgs()
	args12.Header = header
	args12.Ids = ids
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvGetNoticesByIds() (value map[NoticeIdInt]*notification_types.Notice, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetNoticesByIdsResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Ne != nil {
		ne = result13.Ne
	}
	return
}

// 获取通知接收方式及具体接收邮箱/手机
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
func (p *NotificationServiceClient) GetNotificationSetting(header *common.RequestHeader, uid UidInt) (r *notification_types.NotificationSetting, ne *NotificationException, err error) {
	if err = p.sendGetNotificationSetting(header, uid); err != nil {
		return
	}
	return p.recvGetNotificationSetting()
}

func (p *NotificationServiceClient) sendGetNotificationSetting(header *common.RequestHeader, uid UidInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getNotificationSetting", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetNotificationSettingArgs()
	args16.Header = header
	args16.Uid = uid
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvGetNotificationSetting() (value *notification_types.NotificationSetting, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetNotificationSettingResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Ne != nil {
		ne = result17.Ne
	}
	return
}

// 设置通知接收方式及具体接收邮箱/手机
//
// Parameters:
//  - Header: 请求消息头结构体
//  - NotificationSetting: 通知消息具体设置
func (p *NotificationServiceClient) EditNotificationSetting(header *common.RequestHeader, notificationSetting *notification_types.NotificationSetting) (ne *NotificationException, err error) {
	if err = p.sendEditNotificationSetting(header, notificationSetting); err != nil {
		return
	}
	return p.recvEditNotificationSetting()
}

func (p *NotificationServiceClient) sendEditNotificationSetting(header *common.RequestHeader, notificationSetting *notification_types.NotificationSetting) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editNotificationSetting", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewEditNotificationSettingArgs()
	args20.Header = header
	args20.NotificationSetting = notificationSetting
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvEditNotificationSetting() (ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewEditNotificationSettingResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result21.Ne != nil {
		ne = result21.Ne
	}
	return
}

// 用户阅读到站内广播通知编号为noticeId的消息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - NoticeId: 用户阅读到的站内广播通知编号
func (p *NotificationServiceClient) ReadBroadcastNoticeById(header *common.RequestHeader, uid UidInt, noticeId BroadcastNoticeIdInt) (ne *NotificationException, err error) {
	if err = p.sendReadBroadcastNoticeById(header, uid, noticeId); err != nil {
		return
	}
	return p.recvReadBroadcastNoticeById()
}

func (p *NotificationServiceClient) sendReadBroadcastNoticeById(header *common.RequestHeader, uid UidInt, noticeId BroadcastNoticeIdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("readBroadcastNoticeById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewReadBroadcastNoticeByIdArgs()
	args24.Header = header
	args24.Uid = uid
	args24.NoticeId = noticeId
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvReadBroadcastNoticeById() (ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewReadBroadcastNoticeByIdResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result25.Ne != nil {
		ne = result25.Ne
	}
	return
}

// 分页列出某用户未读的站内广播消息，按创建时间排序。
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - ReadStatus: 通知状态，合法的值为不填、已读、未读
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *NotificationServiceClient) ListBroadcastNoticesByUidAndReadStatus(header *common.RequestHeader, uid UidInt, readStatus NoticeStatus, offset BroadNoticeQueryInt, limit BroadNoticeQueryInt, ascending bool) (r *common.QueryResult, ne *NotificationException, err error) {
	if err = p.sendListBroadcastNoticesByUidAndReadStatus(header, uid, readStatus, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListBroadcastNoticesByUidAndReadStatus()
}

func (p *NotificationServiceClient) sendListBroadcastNoticesByUidAndReadStatus(header *common.RequestHeader, uid UidInt, readStatus NoticeStatus, offset BroadNoticeQueryInt, limit BroadNoticeQueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listBroadcastNoticesByUidAndReadStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewListBroadcastNoticesByUidAndReadStatusArgs()
	args28.Header = header
	args28.Uid = uid
	args28.ReadStatus = readStatus
	args28.Offset = offset
	args28.Limit = limit
	args28.Ascending = ascending
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvListBroadcastNoticesByUidAndReadStatus() (value *common.QueryResult, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewListBroadcastNoticesByUidAndReadStatusResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Ne != nil {
		ne = result29.Ne
	}
	return
}

// 根据站内广播通知编号批量获取站内广播通知
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 站内广播通知编号列表
func (p *NotificationServiceClient) GetBroadcastNoticesByIds(header *common.RequestHeader, ids []BroadcastNoticeIdInt) (r map[BroadcastNoticeIdInt]*notification_types.BroadcastNotice, ne *NotificationException, err error) {
	if err = p.sendGetBroadcastNoticesByIds(header, ids); err != nil {
		return
	}
	return p.recvGetBroadcastNoticesByIds()
}

func (p *NotificationServiceClient) sendGetBroadcastNoticesByIds(header *common.RequestHeader, ids []BroadcastNoticeIdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getBroadcastNoticesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetBroadcastNoticesByIdsArgs()
	args32.Header = header
	args32.Ids = ids
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvGetBroadcastNoticesByIds() (value map[BroadcastNoticeIdInt]*notification_types.BroadcastNotice, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetBroadcastNoticesByIdsResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Ne != nil {
		ne = result33.Ne
	}
	return
}

// 管理员发布站内广播通知
//
// Parameters:
//  - Header: 请求消息头结构体
//  - BroadcastNotice: 站内广播通知结构体
func (p *NotificationServiceClient) AdminAddBroadcastNotice(header *common.RequestHeader, broadcastNotice *notification_types.BroadcastNotice) (r BroadcastNoticeIdInt, ne *NotificationException, err error) {
	if err = p.sendAdminAddBroadcastNotice(header, broadcastNotice); err != nil {
		return
	}
	return p.recvAdminAddBroadcastNotice()
}

func (p *NotificationServiceClient) sendAdminAddBroadcastNotice(header *common.RequestHeader, broadcastNotice *notification_types.BroadcastNotice) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("adminAddBroadcastNotice", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewAdminAddBroadcastNoticeArgs()
	args36.Header = header
	args36.BroadcastNotice = broadcastNotice
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvAdminAddBroadcastNotice() (value BroadcastNoticeIdInt, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewAdminAddBroadcastNoticeResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Ne != nil {
		ne = result37.Ne
	}
	return
}

// 管理员修改站内广播通知
//
// Parameters:
//  - Header: 请求消息头结构体
//  - BroadcastNotice: 站内广播通知结构体
func (p *NotificationServiceClient) AdminEditBroadcastNotice(header *common.RequestHeader, broadcastNotice *notification_types.BroadcastNotice) (ne *NotificationException, err error) {
	if err = p.sendAdminEditBroadcastNotice(header, broadcastNotice); err != nil {
		return
	}
	return p.recvAdminEditBroadcastNotice()
}

func (p *NotificationServiceClient) sendAdminEditBroadcastNotice(header *common.RequestHeader, broadcastNotice *notification_types.BroadcastNotice) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("adminEditBroadcastNotice", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewAdminEditBroadcastNoticeArgs()
	args40.Header = header
	args40.BroadcastNotice = broadcastNotice
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvAdminEditBroadcastNotice() (ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewAdminEditBroadcastNoticeResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result41.Ne != nil {
		ne = result41.Ne
	}
	return
}

// 管理员删除站内广播通知
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 站内广播通知编号列表
func (p *NotificationServiceClient) AdminDeleteBroadcastNotices(header *common.RequestHeader, ids []BroadcastNoticeIdInt) (ne *NotificationException, err error) {
	if err = p.sendAdminDeleteBroadcastNotices(header, ids); err != nil {
		return
	}
	return p.recvAdminDeleteBroadcastNotices()
}

func (p *NotificationServiceClient) sendAdminDeleteBroadcastNotices(header *common.RequestHeader, ids []BroadcastNoticeIdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("adminDeleteBroadcastNotices", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewAdminDeleteBroadcastNoticesArgs()
	args44.Header = header
	args44.Ids = ids
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvAdminDeleteBroadcastNotices() (ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewAdminDeleteBroadcastNoticesResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result45.Ne != nil {
		ne = result45.Ne
	}
	return
}

// 管理员分页列出站内广播消息，按创建时间排序。
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Status: 站内广播通知状态，可以不填表示不做限定
//  - ExcludeNotStartedOrExpired: 是否排除未生效和过期的
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *NotificationServiceClient) AdminListBroadcastNoticesByStatus(header *common.RequestHeader, status BroadcastNoticeStatus, excludeNotStartedOrExpired bool, offset BroadNoticeQueryInt, limit BroadNoticeQueryInt, ascending bool) (r *common.QueryResult, ne *NotificationException, err error) {
	if err = p.sendAdminListBroadcastNoticesByStatus(header, status, excludeNotStartedOrExpired, offset, limit, ascending); err != nil {
		return
	}
	return p.recvAdminListBroadcastNoticesByStatus()
}

func (p *NotificationServiceClient) sendAdminListBroadcastNoticesByStatus(header *common.RequestHeader, status BroadcastNoticeStatus, excludeNotStartedOrExpired bool, offset BroadNoticeQueryInt, limit BroadNoticeQueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("adminListBroadcastNoticesByStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewAdminListBroadcastNoticesByStatusArgs()
	args48.Header = header
	args48.Status = status
	args48.ExcludeNotStartedOrExpired = excludeNotStartedOrExpired
	args48.Offset = offset
	args48.Limit = limit
	args48.Ascending = ascending
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *NotificationServiceClient) recvAdminListBroadcastNoticesByStatus() (value *common.QueryResult, ne *NotificationException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewAdminListBroadcastNoticesByStatusResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Ne != nil {
		ne = result49.Ne
	}
	return
}

type NotificationServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      NotificationService
}

func (p *NotificationServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *NotificationServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *NotificationServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewNotificationServiceProcessor(handler NotificationService) *NotificationServiceProcessor {

	self52 := &NotificationServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self52.processorMap["readNoticesByIds"] = &notificationServiceProcessorReadNoticesByIds{handler: handler}
	self52.processorMap["deleteNoticesByIds"] = &notificationServiceProcessorDeleteNoticesByIds{handler: handler}
	self52.processorMap["listNoticesByTimeAndTypeAndStatus"] = &notificationServiceProcessorListNoticesByTimeAndTypeAndStatus{handler: handler}
	self52.processorMap["getNoticesByIds"] = &notificationServiceProcessorGetNoticesByIds{handler: handler}
	self52.processorMap["getNotificationSetting"] = &notificationServiceProcessorGetNotificationSetting{handler: handler}
	self52.processorMap["editNotificationSetting"] = &notificationServiceProcessorEditNotificationSetting{handler: handler}
	self52.processorMap["readBroadcastNoticeById"] = &notificationServiceProcessorReadBroadcastNoticeById{handler: handler}
	self52.processorMap["listBroadcastNoticesByUidAndReadStatus"] = &notificationServiceProcessorListBroadcastNoticesByUidAndReadStatus{handler: handler}
	self52.processorMap["getBroadcastNoticesByIds"] = &notificationServiceProcessorGetBroadcastNoticesByIds{handler: handler}
	self52.processorMap["adminAddBroadcastNotice"] = &notificationServiceProcessorAdminAddBroadcastNotice{handler: handler}
	self52.processorMap["adminEditBroadcastNotice"] = &notificationServiceProcessorAdminEditBroadcastNotice{handler: handler}
	self52.processorMap["adminDeleteBroadcastNotices"] = &notificationServiceProcessorAdminDeleteBroadcastNotices{handler: handler}
	self52.processorMap["adminListBroadcastNoticesByStatus"] = &notificationServiceProcessorAdminListBroadcastNoticesByStatus{handler: handler}
	return self52
}

func (p *NotificationServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x53 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x53.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x53

}

type notificationServiceProcessorReadNoticesByIds struct {
	handler NotificationService
}

func (p *notificationServiceProcessorReadNoticesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewReadNoticesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("readNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewReadNoticesByIdsResult()
	if result.Ne, err = p.handler.ReadNoticesByIds(args.Header, args.Uid, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing readNoticesByIds: "+err.Error())
		oprot.WriteMessageBegin("readNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("readNoticesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorDeleteNoticesByIds struct {
	handler NotificationService
}

func (p *notificationServiceProcessorDeleteNoticesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteNoticesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteNoticesByIdsResult()
	if result.Ne, err = p.handler.DeleteNoticesByIds(args.Header, args.Uid, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteNoticesByIds: "+err.Error())
		oprot.WriteMessageBegin("deleteNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteNoticesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorListNoticesByTimeAndTypeAndStatus struct {
	handler NotificationService
}

func (p *notificationServiceProcessorListNoticesByTimeAndTypeAndStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListNoticesByTimeAndTypeAndStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listNoticesByTimeAndTypeAndStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListNoticesByTimeAndTypeAndStatusResult()
	if result.Success, result.Ne, err = p.handler.ListNoticesByTimeAndTypeAndStatus(args.Header, args.Uid, args.StartTime, args.EndTime, args.TypeA1, args.Status, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listNoticesByTimeAndTypeAndStatus: "+err.Error())
		oprot.WriteMessageBegin("listNoticesByTimeAndTypeAndStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listNoticesByTimeAndTypeAndStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorGetNoticesByIds struct {
	handler NotificationService
}

func (p *notificationServiceProcessorGetNoticesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetNoticesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetNoticesByIdsResult()
	if result.Success, result.Ne, err = p.handler.GetNoticesByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNoticesByIds: "+err.Error())
		oprot.WriteMessageBegin("getNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getNoticesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorGetNotificationSetting struct {
	handler NotificationService
}

func (p *notificationServiceProcessorGetNotificationSetting) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetNotificationSettingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getNotificationSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetNotificationSettingResult()
	if result.Success, result.Ne, err = p.handler.GetNotificationSetting(args.Header, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNotificationSetting: "+err.Error())
		oprot.WriteMessageBegin("getNotificationSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getNotificationSetting", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorEditNotificationSetting struct {
	handler NotificationService
}

func (p *notificationServiceProcessorEditNotificationSetting) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditNotificationSettingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editNotificationSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditNotificationSettingResult()
	if result.Ne, err = p.handler.EditNotificationSetting(args.Header, args.NotificationSetting); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editNotificationSetting: "+err.Error())
		oprot.WriteMessageBegin("editNotificationSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editNotificationSetting", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorReadBroadcastNoticeById struct {
	handler NotificationService
}

func (p *notificationServiceProcessorReadBroadcastNoticeById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewReadBroadcastNoticeByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("readBroadcastNoticeById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewReadBroadcastNoticeByIdResult()
	if result.Ne, err = p.handler.ReadBroadcastNoticeById(args.Header, args.Uid, args.NoticeId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing readBroadcastNoticeById: "+err.Error())
		oprot.WriteMessageBegin("readBroadcastNoticeById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("readBroadcastNoticeById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorListBroadcastNoticesByUidAndReadStatus struct {
	handler NotificationService
}

func (p *notificationServiceProcessorListBroadcastNoticesByUidAndReadStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListBroadcastNoticesByUidAndReadStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listBroadcastNoticesByUidAndReadStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListBroadcastNoticesByUidAndReadStatusResult()
	if result.Success, result.Ne, err = p.handler.ListBroadcastNoticesByUidAndReadStatus(args.Header, args.Uid, args.ReadStatus, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listBroadcastNoticesByUidAndReadStatus: "+err.Error())
		oprot.WriteMessageBegin("listBroadcastNoticesByUidAndReadStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listBroadcastNoticesByUidAndReadStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorGetBroadcastNoticesByIds struct {
	handler NotificationService
}

func (p *notificationServiceProcessorGetBroadcastNoticesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetBroadcastNoticesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getBroadcastNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetBroadcastNoticesByIdsResult()
	if result.Success, result.Ne, err = p.handler.GetBroadcastNoticesByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getBroadcastNoticesByIds: "+err.Error())
		oprot.WriteMessageBegin("getBroadcastNoticesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getBroadcastNoticesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorAdminAddBroadcastNotice struct {
	handler NotificationService
}

func (p *notificationServiceProcessorAdminAddBroadcastNotice) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAdminAddBroadcastNoticeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("adminAddBroadcastNotice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAdminAddBroadcastNoticeResult()
	if result.Success, result.Ne, err = p.handler.AdminAddBroadcastNotice(args.Header, args.BroadcastNotice); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adminAddBroadcastNotice: "+err.Error())
		oprot.WriteMessageBegin("adminAddBroadcastNotice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("adminAddBroadcastNotice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorAdminEditBroadcastNotice struct {
	handler NotificationService
}

func (p *notificationServiceProcessorAdminEditBroadcastNotice) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAdminEditBroadcastNoticeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("adminEditBroadcastNotice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAdminEditBroadcastNoticeResult()
	if result.Ne, err = p.handler.AdminEditBroadcastNotice(args.Header, args.BroadcastNotice); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adminEditBroadcastNotice: "+err.Error())
		oprot.WriteMessageBegin("adminEditBroadcastNotice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("adminEditBroadcastNotice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorAdminDeleteBroadcastNotices struct {
	handler NotificationService
}

func (p *notificationServiceProcessorAdminDeleteBroadcastNotices) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAdminDeleteBroadcastNoticesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("adminDeleteBroadcastNotices", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAdminDeleteBroadcastNoticesResult()
	if result.Ne, err = p.handler.AdminDeleteBroadcastNotices(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adminDeleteBroadcastNotices: "+err.Error())
		oprot.WriteMessageBegin("adminDeleteBroadcastNotices", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("adminDeleteBroadcastNotices", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type notificationServiceProcessorAdminListBroadcastNoticesByStatus struct {
	handler NotificationService
}

func (p *notificationServiceProcessorAdminListBroadcastNoticesByStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAdminListBroadcastNoticesByStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("adminListBroadcastNoticesByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAdminListBroadcastNoticesByStatusResult()
	if result.Success, result.Ne, err = p.handler.AdminListBroadcastNoticesByStatus(args.Header, args.Status, args.ExcludeNotStartedOrExpired, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adminListBroadcastNoticesByStatus: "+err.Error())
		oprot.WriteMessageBegin("adminListBroadcastNoticesByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("adminListBroadcastNoticesByStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ReadNoticesByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
	Ids    []NoticeIdInt         `thrift:"ids,3" json:"ids"`
}

func NewReadNoticesByIdsArgs() *ReadNoticesByIdsArgs {
	return &ReadNoticesByIdsArgs{}
}

func (p *ReadNoticesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReadNoticesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ReadNoticesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ReadNoticesByIdsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]NoticeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem54 NoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem54 = NoticeIdInt(v)
		}
		p.Ids = append(p.Ids, _elem54)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ReadNoticesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("readNoticesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReadNoticesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ReadNoticesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ReadNoticesByIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *ReadNoticesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReadNoticesByIdsArgs(%+v)", *p)
}

type ReadNoticesByIdsResult struct {
	Ne *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewReadNoticesByIdsResult() *ReadNoticesByIdsResult {
	return &ReadNoticesByIdsResult{}
}

func (p *ReadNoticesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReadNoticesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *ReadNoticesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("readNoticesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReadNoticesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *ReadNoticesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReadNoticesByIdsResult(%+v)", *p)
}

type DeleteNoticesByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
	Ids    []NoticeIdInt         `thrift:"ids,3" json:"ids"`
}

func NewDeleteNoticesByIdsArgs() *DeleteNoticesByIdsArgs {
	return &DeleteNoticesByIdsArgs{}
}

func (p *DeleteNoticesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteNoticesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeleteNoticesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *DeleteNoticesByIdsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]NoticeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem55 NoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem55 = NoticeIdInt(v)
		}
		p.Ids = append(p.Ids, _elem55)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeleteNoticesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteNoticesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteNoticesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeleteNoticesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *DeleteNoticesByIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DeleteNoticesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteNoticesByIdsArgs(%+v)", *p)
}

type DeleteNoticesByIdsResult struct {
	Ne *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewDeleteNoticesByIdsResult() *DeleteNoticesByIdsResult {
	return &DeleteNoticesByIdsResult{}
}

func (p *DeleteNoticesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteNoticesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *DeleteNoticesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteNoticesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteNoticesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *DeleteNoticesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteNoticesByIdsResult(%+v)", *p)
}

type ListNoticesByTimeAndTypeAndStatusArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid       UidInt                `thrift:"uid,2" json:"uid"`
	StartTime TimeInt               `thrift:"startTime,3" json:"startTime"`
	EndTime   TimeInt               `thrift:"endTime,4" json:"endTime"`
	TypeA1    NoticeType            `thrift:"type,5" json:"type"`
	Status    NoticeStatus          `thrift:"status,6" json:"status"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    NoticeQueryInt `thrift:"offset,10" json:"offset"`
	Limit     NoticeQueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool           `thrift:"ascending,12" json:"ascending"`
}

func NewListNoticesByTimeAndTypeAndStatusArgs() *ListNoticesByTimeAndTypeAndStatusArgs {
	return &ListNoticesByTimeAndTypeAndStatusArgs{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EndTime = TimeInt(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = NoticeType(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Status = NoticeStatus(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = NoticeQueryInt(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = NoticeQueryInt(v)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listNoticesByTimeAndTypeAndStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:startTime: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:endTime: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:type: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:status: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListNoticesByTimeAndTypeAndStatusArgs(%+v)", *p)
}

type ListNoticesByTimeAndTypeAndStatusResult struct {
	Success *common.QueryResult    `thrift:"success,0" json:"success"`
	Ne      *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewListNoticesByTimeAndTypeAndStatusResult() *ListNoticesByTimeAndTypeAndStatusResult {
	return &ListNoticesByTimeAndTypeAndStatusResult{}
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listNoticesByTimeAndTypeAndStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *ListNoticesByTimeAndTypeAndStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListNoticesByTimeAndTypeAndStatusResult(%+v)", *p)
}

type GetNoticesByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []NoticeIdInt         `thrift:"ids,2" json:"ids"`
}

func NewGetNoticesByIdsArgs() *GetNoticesByIdsArgs {
	return &GetNoticesByIdsArgs{}
}

func (p *GetNoticesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNoticesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetNoticesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]NoticeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem56 NoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem56 = NoticeIdInt(v)
		}
		p.Ids = append(p.Ids, _elem56)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetNoticesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNoticesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNoticesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetNoticesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetNoticesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNoticesByIdsArgs(%+v)", *p)
}

type GetNoticesByIdsResult struct {
	Success map[NoticeIdInt]*notification_types.Notice `thrift:"success,0" json:"success"`
	Ne      *NotificationException                     `thrift:"ne,1" json:"ne"`
}

func NewGetNoticesByIdsResult() *GetNoticesByIdsResult {
	return &GetNoticesByIdsResult{}
}

func (p *GetNoticesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNoticesByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[NoticeIdInt]*notification_types.Notice, size)
	for i := 0; i < size; i++ {
		var _key57 NoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key57 = NoticeIdInt(v)
		}
		_val58 := notification_types.NewNotice()
		if err := _val58.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val58)
		}
		p.Success[_key57] = _val58
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetNoticesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *GetNoticesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNoticesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNoticesByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetNoticesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *GetNoticesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNoticesByIdsResult(%+v)", *p)
}

type GetNotificationSettingArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
}

func NewGetNotificationSettingArgs() *GetNotificationSettingArgs {
	return &GetNotificationSettingArgs{}
}

func (p *GetNotificationSettingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNotificationSettingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetNotificationSettingArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *GetNotificationSettingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNotificationSetting_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNotificationSettingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetNotificationSettingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *GetNotificationSettingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNotificationSettingArgs(%+v)", *p)
}

type GetNotificationSettingResult struct {
	Success *notification_types.NotificationSetting `thrift:"success,0" json:"success"`
	Ne      *NotificationException                  `thrift:"ne,1" json:"ne"`
}

func NewGetNotificationSettingResult() *GetNotificationSettingResult {
	return &GetNotificationSettingResult{}
}

func (p *GetNotificationSettingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNotificationSettingResult) readField0(iprot thrift.TProtocol) error {
	p.Success = notification_types.NewNotificationSetting()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetNotificationSettingResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *GetNotificationSettingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNotificationSetting_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNotificationSettingResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetNotificationSettingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *GetNotificationSettingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNotificationSettingResult(%+v)", *p)
}

type EditNotificationSettingArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	NotificationSetting *notification_types.NotificationSetting `thrift:"notificationSetting,3" json:"notificationSetting"`
}

func NewEditNotificationSettingArgs() *EditNotificationSettingArgs {
	return &EditNotificationSettingArgs{}
}

func (p *EditNotificationSettingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditNotificationSettingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditNotificationSettingArgs) readField3(iprot thrift.TProtocol) error {
	p.NotificationSetting = notification_types.NewNotificationSetting()
	if err := p.NotificationSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.NotificationSetting)
	}
	return nil
}

func (p *EditNotificationSettingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editNotificationSetting_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditNotificationSettingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditNotificationSettingArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.NotificationSetting != nil {
		if err := oprot.WriteFieldBegin("notificationSetting", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:notificationSetting: %s", p, err)
		}
		if err := p.NotificationSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.NotificationSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:notificationSetting: %s", p, err)
		}
	}
	return err
}

func (p *EditNotificationSettingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditNotificationSettingArgs(%+v)", *p)
}

type EditNotificationSettingResult struct {
	Ne *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewEditNotificationSettingResult() *EditNotificationSettingResult {
	return &EditNotificationSettingResult{}
}

func (p *EditNotificationSettingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditNotificationSettingResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *EditNotificationSettingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editNotificationSetting_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditNotificationSettingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *EditNotificationSettingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditNotificationSettingResult(%+v)", *p)
}

type ReadBroadcastNoticeByIdArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      UidInt                `thrift:"uid,2" json:"uid"`
	NoticeId BroadcastNoticeIdInt  `thrift:"noticeId,3" json:"noticeId"`
}

func NewReadBroadcastNoticeByIdArgs() *ReadBroadcastNoticeByIdArgs {
	return &ReadBroadcastNoticeByIdArgs{}
}

func (p *ReadBroadcastNoticeByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NoticeId = BroadcastNoticeIdInt(v)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("readBroadcastNoticeById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ReadBroadcastNoticeByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ReadBroadcastNoticeByIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("noticeId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:noticeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NoticeId)); err != nil {
		return fmt.Errorf("%T.noticeId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:noticeId: %s", p, err)
	}
	return err
}

func (p *ReadBroadcastNoticeByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReadBroadcastNoticeByIdArgs(%+v)", *p)
}

type ReadBroadcastNoticeByIdResult struct {
	Ne *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewReadBroadcastNoticeByIdResult() *ReadBroadcastNoticeByIdResult {
	return &ReadBroadcastNoticeByIdResult{}
}

func (p *ReadBroadcastNoticeByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("readBroadcastNoticeById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReadBroadcastNoticeByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *ReadBroadcastNoticeByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReadBroadcastNoticeByIdResult(%+v)", *p)
}

type ListBroadcastNoticesByUidAndReadStatusArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid        UidInt                `thrift:"uid,2" json:"uid"`
	ReadStatus NoticeStatus          `thrift:"readStatus,3" json:"readStatus"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    BroadNoticeQueryInt `thrift:"offset,10" json:"offset"`
	Limit     BroadNoticeQueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool                `thrift:"ascending,12" json:"ascending"`
}

func NewListBroadcastNoticesByUidAndReadStatusArgs() *ListBroadcastNoticesByUidAndReadStatusArgs {
	return &ListBroadcastNoticesByUidAndReadStatusArgs{
		ReadStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) IsSetReadStatus() bool {
	return int64(p.ReadStatus) != math.MinInt32-1
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ReadStatus = NoticeStatus(v)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = BroadNoticeQueryInt(v)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = BroadNoticeQueryInt(v)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listBroadcastNoticesByUidAndReadStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("readStatus", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:readStatus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReadStatus)); err != nil {
		return fmt.Errorf("%T.readStatus (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:readStatus: %s", p, err)
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListBroadcastNoticesByUidAndReadStatusArgs(%+v)", *p)
}

type ListBroadcastNoticesByUidAndReadStatusResult struct {
	Success *common.QueryResult    `thrift:"success,0" json:"success"`
	Ne      *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewListBroadcastNoticesByUidAndReadStatusResult() *ListBroadcastNoticesByUidAndReadStatusResult {
	return &ListBroadcastNoticesByUidAndReadStatusResult{}
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listBroadcastNoticesByUidAndReadStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *ListBroadcastNoticesByUidAndReadStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListBroadcastNoticesByUidAndReadStatusResult(%+v)", *p)
}

type GetBroadcastNoticesByIdsArgs struct {
	Header *common.RequestHeader  `thrift:"header,1" json:"header"`
	Ids    []BroadcastNoticeIdInt `thrift:"ids,2" json:"ids"`
}

func NewGetBroadcastNoticesByIdsArgs() *GetBroadcastNoticesByIdsArgs {
	return &GetBroadcastNoticesByIdsArgs{}
}

func (p *GetBroadcastNoticesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]BroadcastNoticeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem59 BroadcastNoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem59 = BroadcastNoticeIdInt(v)
		}
		p.Ids = append(p.Ids, _elem59)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getBroadcastNoticesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetBroadcastNoticesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetBroadcastNoticesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBroadcastNoticesByIdsArgs(%+v)", *p)
}

type GetBroadcastNoticesByIdsResult struct {
	Success map[BroadcastNoticeIdInt]*notification_types.BroadcastNotice `thrift:"success,0" json:"success"`
	Ne      *NotificationException                                       `thrift:"ne,1" json:"ne"`
}

func NewGetBroadcastNoticesByIdsResult() *GetBroadcastNoticesByIdsResult {
	return &GetBroadcastNoticesByIdsResult{}
}

func (p *GetBroadcastNoticesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[BroadcastNoticeIdInt]*notification_types.BroadcastNotice, size)
	for i := 0; i < size; i++ {
		var _key60 BroadcastNoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key60 = BroadcastNoticeIdInt(v)
		}
		_val61 := notification_types.NewBroadcastNotice()
		if err := _val61.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val61)
		}
		p.Success[_key60] = _val61
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getBroadcastNoticesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetBroadcastNoticesByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetBroadcastNoticesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *GetBroadcastNoticesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBroadcastNoticesByIdsResult(%+v)", *p)
}

type AdminAddBroadcastNoticeArgs struct {
	Header          *common.RequestHeader               `thrift:"header,1" json:"header"`
	BroadcastNotice *notification_types.BroadcastNotice `thrift:"broadcastNotice,2" json:"broadcastNotice"`
}

func NewAdminAddBroadcastNoticeArgs() *AdminAddBroadcastNoticeArgs {
	return &AdminAddBroadcastNoticeArgs{}
}

func (p *AdminAddBroadcastNoticeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeArgs) readField2(iprot thrift.TProtocol) error {
	p.BroadcastNotice = notification_types.NewBroadcastNotice()
	if err := p.BroadcastNotice.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.BroadcastNotice)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminAddBroadcastNotice_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AdminAddBroadcastNoticeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.BroadcastNotice != nil {
		if err := oprot.WriteFieldBegin("broadcastNotice", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:broadcastNotice: %s", p, err)
		}
		if err := p.BroadcastNotice.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.BroadcastNotice)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:broadcastNotice: %s", p, err)
		}
	}
	return err
}

func (p *AdminAddBroadcastNoticeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminAddBroadcastNoticeArgs(%+v)", *p)
}

type AdminAddBroadcastNoticeResult struct {
	Success BroadcastNoticeIdInt   `thrift:"success,0" json:"success"`
	Ne      *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewAdminAddBroadcastNoticeResult() *AdminAddBroadcastNoticeResult {
	return &AdminAddBroadcastNoticeResult{}
}

func (p *AdminAddBroadcastNoticeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = BroadcastNoticeIdInt(v)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminAddBroadcastNotice_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminAddBroadcastNoticeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AdminAddBroadcastNoticeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *AdminAddBroadcastNoticeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminAddBroadcastNoticeResult(%+v)", *p)
}

type AdminEditBroadcastNoticeArgs struct {
	Header          *common.RequestHeader               `thrift:"header,1" json:"header"`
	BroadcastNotice *notification_types.BroadcastNotice `thrift:"broadcastNotice,2" json:"broadcastNotice"`
}

func NewAdminEditBroadcastNoticeArgs() *AdminEditBroadcastNoticeArgs {
	return &AdminEditBroadcastNoticeArgs{}
}

func (p *AdminEditBroadcastNoticeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeArgs) readField2(iprot thrift.TProtocol) error {
	p.BroadcastNotice = notification_types.NewBroadcastNotice()
	if err := p.BroadcastNotice.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.BroadcastNotice)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminEditBroadcastNotice_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AdminEditBroadcastNoticeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.BroadcastNotice != nil {
		if err := oprot.WriteFieldBegin("broadcastNotice", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:broadcastNotice: %s", p, err)
		}
		if err := p.BroadcastNotice.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.BroadcastNotice)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:broadcastNotice: %s", p, err)
		}
	}
	return err
}

func (p *AdminEditBroadcastNoticeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminEditBroadcastNoticeArgs(%+v)", *p)
}

type AdminEditBroadcastNoticeResult struct {
	Ne *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewAdminEditBroadcastNoticeResult() *AdminEditBroadcastNoticeResult {
	return &AdminEditBroadcastNoticeResult{}
}

func (p *AdminEditBroadcastNoticeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminEditBroadcastNotice_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminEditBroadcastNoticeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *AdminEditBroadcastNoticeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminEditBroadcastNoticeResult(%+v)", *p)
}

type AdminDeleteBroadcastNoticesArgs struct {
	Header *common.RequestHeader  `thrift:"header,1" json:"header"`
	Ids    []BroadcastNoticeIdInt `thrift:"ids,2" json:"ids"`
}

func NewAdminDeleteBroadcastNoticesArgs() *AdminDeleteBroadcastNoticesArgs {
	return &AdminDeleteBroadcastNoticesArgs{}
}

func (p *AdminDeleteBroadcastNoticesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]BroadcastNoticeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem62 BroadcastNoticeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem62 = BroadcastNoticeIdInt(v)
		}
		p.Ids = append(p.Ids, _elem62)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminDeleteBroadcastNotices_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AdminDeleteBroadcastNoticesArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *AdminDeleteBroadcastNoticesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminDeleteBroadcastNoticesArgs(%+v)", *p)
}

type AdminDeleteBroadcastNoticesResult struct {
	Ne *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewAdminDeleteBroadcastNoticesResult() *AdminDeleteBroadcastNoticesResult {
	return &AdminDeleteBroadcastNoticesResult{}
}

func (p *AdminDeleteBroadcastNoticesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminDeleteBroadcastNotices_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminDeleteBroadcastNoticesResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *AdminDeleteBroadcastNoticesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminDeleteBroadcastNoticesResult(%+v)", *p)
}

type AdminListBroadcastNoticesByStatusArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	Status                     BroadcastNoticeStatus `thrift:"status,3" json:"status"`
	ExcludeNotStartedOrExpired bool                  `thrift:"excludeNotStartedOrExpired,4" json:"excludeNotStartedOrExpired"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    BroadNoticeQueryInt `thrift:"offset,10" json:"offset"`
	Limit     BroadNoticeQueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool                `thrift:"ascending,12" json:"ascending"`
}

func NewAdminListBroadcastNoticesByStatusArgs() *AdminListBroadcastNoticesByStatusArgs {
	return &AdminListBroadcastNoticesByStatusArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdminListBroadcastNoticesByStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdminListBroadcastNoticesByStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = BroadcastNoticeStatus(v)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExcludeNotStartedOrExpired = v
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = BroadNoticeQueryInt(v)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = BroadNoticeQueryInt(v)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminListBroadcastNoticesByStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeNotStartedOrExpired", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:excludeNotStartedOrExpired: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeNotStartedOrExpired)); err != nil {
		return fmt.Errorf("%T.excludeNotStartedOrExpired (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:excludeNotStartedOrExpired: %s", p, err)
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminListBroadcastNoticesByStatusArgs(%+v)", *p)
}

type AdminListBroadcastNoticesByStatusResult struct {
	Success *common.QueryResult    `thrift:"success,0" json:"success"`
	Ne      *NotificationException `thrift:"ne,1" json:"ne"`
}

func NewAdminListBroadcastNoticesByStatusResult() *AdminListBroadcastNoticesByStatusResult {
	return &AdminListBroadcastNoticesByStatusResult{}
}

func (p *AdminListBroadcastNoticesByStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Ne = NewNotificationException()
	if err := p.Ne.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ne)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminListBroadcastNoticesByStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ne != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminListBroadcastNoticesByStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ne != nil {
		if err := oprot.WriteFieldBegin("ne", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ne: %s", p, err)
		}
		if err := p.Ne.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ne)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ne: %s", p, err)
		}
	}
	return err
}

func (p *AdminListBroadcastNoticesByStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminListBroadcastNoticesByStatusResult(%+v)", *p)
}
