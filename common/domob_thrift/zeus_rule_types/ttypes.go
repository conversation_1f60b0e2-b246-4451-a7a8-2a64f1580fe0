// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_rule_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/zeus_common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = zeus_common.GoUnusedProtection__
var GoUnusedProtection__ int

type ExecutionType int64

const (
	ExecutionType_PAUSE         ExecutionType = 1
	ExecutionType_CHANGE_BUDGET ExecutionType = 2
	ExecutionType_CHANGE_BID    ExecutionType = 3
	ExecutionType_START         ExecutionType = 4
)

func (p ExecutionType) String() string {
	switch p {
	case ExecutionType_PAUSE:
		return "ExecutionType_PAUSE"
	case ExecutionType_CHANGE_BUDGET:
		return "ExecutionType_CHANGE_BUDGET"
	case ExecutionType_CHANGE_BID:
		return "ExecutionType_CHANGE_BID"
	case ExecutionType_START:
		return "ExecutionType_START"
	}
	return "<UNSET>"
}

func ExecutionTypeFromString(s string) (ExecutionType, error) {
	switch s {
	case "ExecutionType_PAUSE":
		return ExecutionType_PAUSE, nil
	case "ExecutionType_CHANGE_BUDGET":
		return ExecutionType_CHANGE_BUDGET, nil
	case "ExecutionType_CHANGE_BID":
		return ExecutionType_CHANGE_BID, nil
	case "ExecutionType_START":
		return ExecutionType_START, nil
	}
	return ExecutionType(math.MinInt32 - 1), fmt.Errorf("not a valid ExecutionType string")
}

type RatioType int64

const (
	RatioType_RELATIVE RatioType = 1
	RatioType_ABSOLUTE RatioType = 2
)

func (p RatioType) String() string {
	switch p {
	case RatioType_RELATIVE:
		return "RatioType_RELATIVE"
	case RatioType_ABSOLUTE:
		return "RatioType_ABSOLUTE"
	}
	return "<UNSET>"
}

func RatioTypeFromString(s string) (RatioType, error) {
	switch s {
	case "RatioType_RELATIVE":
		return RatioType_RELATIVE, nil
	case "RatioType_ABSOLUTE":
		return RatioType_ABSOLUTE, nil
	}
	return RatioType(math.MinInt32 - 1), fmt.Errorf("not a valid RatioType string")
}

type Operation struct {
	ExecutionType ExecutionType `thrift:"executionType,1" json:"executionType"`
	RatioType     RatioType     `thrift:"ratioType,2" json:"ratioType"`
	Ratio         int32         `thrift:"ratio,3" json:"ratio"`
}

func NewOperation() *Operation {
	return &Operation{
		ExecutionType: math.MinInt32 - 1, // unset sentinal value

		RatioType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Operation) IsSetExecutionType() bool {
	return int64(p.ExecutionType) != math.MinInt32-1
}

func (p *Operation) IsSetRatioType() bool {
	return int64(p.RatioType) != math.MinInt32-1
}

func (p *Operation) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Operation) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExecutionType = ExecutionType(v)
	}
	return nil
}

func (p *Operation) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RatioType = RatioType(v)
	}
	return nil
}

func (p *Operation) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ratio = v
	}
	return nil
}

func (p *Operation) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Operation"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Operation) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecutionType() {
		if err := oprot.WriteFieldBegin("executionType", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:executionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ExecutionType)); err != nil {
			return fmt.Errorf("%T.executionType (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:executionType: %s", p, err)
		}
	}
	return err
}

func (p *Operation) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRatioType() {
		if err := oprot.WriteFieldBegin("ratioType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ratioType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RatioType)); err != nil {
			return fmt.Errorf("%T.ratioType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ratioType: %s", p, err)
		}
	}
	return err
}

func (p *Operation) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ratio", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ratio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ratio)); err != nil {
		return fmt.Errorf("%T.ratio (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ratio: %s", p, err)
	}
	return err
}

func (p *Operation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Operation(%+v)", *p)
}

type Rule struct {
	RuleId     int32                    `thrift:"rule_id,1" json:"rule_id"`
	CampaignId int32                    `thrift:"campaign_id,2" json:"campaign_id"`
	Conditions []*zeus_common.Condition `thrift:"conditions,3" json:"conditions"`
	Operation  *Operation               `thrift:"operation,4" json:"operation"`
	UserId     int64                    `thrift:"user_id,5" json:"user_id"`
}

func NewRule() *Rule {
	return &Rule{}
}

func (p *Rule) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Rule) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RuleId = v
	}
	return nil
}

func (p *Rule) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *Rule) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Conditions = make([]*zeus_common.Condition, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := zeus_common.NewCondition()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Conditions = append(p.Conditions, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Rule) readField4(iprot thrift.TProtocol) error {
	p.Operation = NewOperation()
	if err := p.Operation.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Operation)
	}
	return nil
}

func (p *Rule) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *Rule) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Rule"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Rule) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rule_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rule_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RuleId)); err != nil {
		return fmt.Errorf("%T.rule_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rule_id: %s", p, err)
	}
	return err
}

func (p *Rule) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *Rule) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Conditions != nil {
		if err := oprot.WriteFieldBegin("conditions", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:conditions: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Conditions)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Conditions {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:conditions: %s", p, err)
		}
	}
	return err
}

func (p *Rule) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Operation != nil {
		if err := oprot.WriteFieldBegin("operation", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:operation: %s", p, err)
		}
		if err := p.Operation.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Operation)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:operation: %s", p, err)
		}
	}
	return err
}

func (p *Rule) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_id", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:user_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UserId)); err != nil {
		return fmt.Errorf("%T.user_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:user_id: %s", p, err)
	}
	return err
}

func (p *Rule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Rule(%+v)", *p)
}
