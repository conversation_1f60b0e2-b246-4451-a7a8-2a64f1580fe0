// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package domino_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type DominoErrorCode int64

const (
	DominoErrorCode_ERROR_PLAN_NAME_EXIST     DominoErrorCode = 10000
	DominoErrorCode_ERROR_PLAN_NAME_NOT_EXIST DominoErrorCode = 10001
	DominoErrorCode_ERROR_INVALID_PARAM       DominoErrorCode = 10002
	DominoErrorCode_ERROR_LIMIT_EXCEEDED      DominoErrorCode = 10003
	DominoErrorCode_ERROR_SYSTEM_ERROR        DominoErrorCode = 50000
	DominoErrorCode_ERROR_PLAN_DEP_EXIST      DominoErrorCode = 10004
	DominoErrorCode_ERROR_DEP_PLAN_EXIST      DominoErrorCode = 10005
	DominoErrorCode_ERROR_JOB_CONFLICT        DominoErrorCode = 10006
)

func (p DominoErrorCode) String() string {
	switch p {
	case DominoErrorCode_ERROR_PLAN_NAME_EXIST:
		return "DominoErrorCode_ERROR_PLAN_NAME_EXIST"
	case DominoErrorCode_ERROR_PLAN_NAME_NOT_EXIST:
		return "DominoErrorCode_ERROR_PLAN_NAME_NOT_EXIST"
	case DominoErrorCode_ERROR_INVALID_PARAM:
		return "DominoErrorCode_ERROR_INVALID_PARAM"
	case DominoErrorCode_ERROR_LIMIT_EXCEEDED:
		return "DominoErrorCode_ERROR_LIMIT_EXCEEDED"
	case DominoErrorCode_ERROR_SYSTEM_ERROR:
		return "DominoErrorCode_ERROR_SYSTEM_ERROR"
	case DominoErrorCode_ERROR_PLAN_DEP_EXIST:
		return "DominoErrorCode_ERROR_PLAN_DEP_EXIST"
	case DominoErrorCode_ERROR_DEP_PLAN_EXIST:
		return "DominoErrorCode_ERROR_DEP_PLAN_EXIST"
	case DominoErrorCode_ERROR_JOB_CONFLICT:
		return "DominoErrorCode_ERROR_JOB_CONFLICT"
	}
	return "<UNSET>"
}

func DominoErrorCodeFromString(s string) (DominoErrorCode, error) {
	switch s {
	case "DominoErrorCode_ERROR_PLAN_NAME_EXIST":
		return DominoErrorCode_ERROR_PLAN_NAME_EXIST, nil
	case "DominoErrorCode_ERROR_PLAN_NAME_NOT_EXIST":
		return DominoErrorCode_ERROR_PLAN_NAME_NOT_EXIST, nil
	case "DominoErrorCode_ERROR_INVALID_PARAM":
		return DominoErrorCode_ERROR_INVALID_PARAM, nil
	case "DominoErrorCode_ERROR_LIMIT_EXCEEDED":
		return DominoErrorCode_ERROR_LIMIT_EXCEEDED, nil
	case "DominoErrorCode_ERROR_SYSTEM_ERROR":
		return DominoErrorCode_ERROR_SYSTEM_ERROR, nil
	case "DominoErrorCode_ERROR_PLAN_DEP_EXIST":
		return DominoErrorCode_ERROR_PLAN_DEP_EXIST, nil
	case "DominoErrorCode_ERROR_DEP_PLAN_EXIST":
		return DominoErrorCode_ERROR_DEP_PLAN_EXIST, nil
	case "DominoErrorCode_ERROR_JOB_CONFLICT":
		return DominoErrorCode_ERROR_JOB_CONFLICT, nil
	}
	return DominoErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid DominoErrorCode string")
}

//任务的调度周期
type PlanCycle int64

const (
	PlanCycle_PLAN_CYCLE_DAILY      PlanCycle = 1
	PlanCycle_PLAN_CYCLE_HOURLY     PlanCycle = 2
	PlanCycle_PLAN_CYCLE_HALFHOURLY PlanCycle = 3
	PlanCycle_PLAN_CYCLE_QUARTERLY  PlanCycle = 4
	PlanCycle_PLAN_CYCLE_5MINUTES   PlanCycle = 5
)

func (p PlanCycle) String() string {
	switch p {
	case PlanCycle_PLAN_CYCLE_DAILY:
		return "PlanCycle_PLAN_CYCLE_DAILY"
	case PlanCycle_PLAN_CYCLE_HOURLY:
		return "PlanCycle_PLAN_CYCLE_HOURLY"
	case PlanCycle_PLAN_CYCLE_HALFHOURLY:
		return "PlanCycle_PLAN_CYCLE_HALFHOURLY"
	case PlanCycle_PLAN_CYCLE_QUARTERLY:
		return "PlanCycle_PLAN_CYCLE_QUARTERLY"
	case PlanCycle_PLAN_CYCLE_5MINUTES:
		return "PlanCycle_PLAN_CYCLE_5MINUTES"
	}
	return "<UNSET>"
}

func PlanCycleFromString(s string) (PlanCycle, error) {
	switch s {
	case "PlanCycle_PLAN_CYCLE_DAILY":
		return PlanCycle_PLAN_CYCLE_DAILY, nil
	case "PlanCycle_PLAN_CYCLE_HOURLY":
		return PlanCycle_PLAN_CYCLE_HOURLY, nil
	case "PlanCycle_PLAN_CYCLE_HALFHOURLY":
		return PlanCycle_PLAN_CYCLE_HALFHOURLY, nil
	case "PlanCycle_PLAN_CYCLE_QUARTERLY":
		return PlanCycle_PLAN_CYCLE_QUARTERLY, nil
	case "PlanCycle_PLAN_CYCLE_5MINUTES":
		return PlanCycle_PLAN_CYCLE_5MINUTES, nil
	}
	return PlanCycle(math.MinInt32 - 1), fmt.Errorf("not a valid PlanCycle string")
}

//任务的状态
type PlanStatus int64

const (
	PlanStatus_PLAN_STATUS_NORMAL  PlanStatus = 1
	PlanStatus_PLAN_STATUS_SUSPEND PlanStatus = 2
	PlanStatus_PLAN_STATUS_STOP    PlanStatus = 3
	PlanStatus_PLAN_STATUS_DELETE  PlanStatus = 4
)

func (p PlanStatus) String() string {
	switch p {
	case PlanStatus_PLAN_STATUS_NORMAL:
		return "PlanStatus_PLAN_STATUS_NORMAL"
	case PlanStatus_PLAN_STATUS_SUSPEND:
		return "PlanStatus_PLAN_STATUS_SUSPEND"
	case PlanStatus_PLAN_STATUS_STOP:
		return "PlanStatus_PLAN_STATUS_STOP"
	case PlanStatus_PLAN_STATUS_DELETE:
		return "PlanStatus_PLAN_STATUS_DELETE"
	}
	return "<UNSET>"
}

func PlanStatusFromString(s string) (PlanStatus, error) {
	switch s {
	case "PlanStatus_PLAN_STATUS_NORMAL":
		return PlanStatus_PLAN_STATUS_NORMAL, nil
	case "PlanStatus_PLAN_STATUS_SUSPEND":
		return PlanStatus_PLAN_STATUS_SUSPEND, nil
	case "PlanStatus_PLAN_STATUS_STOP":
		return PlanStatus_PLAN_STATUS_STOP, nil
	case "PlanStatus_PLAN_STATUS_DELETE":
		return PlanStatus_PLAN_STATUS_DELETE, nil
	}
	return PlanStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PlanStatus string")
}

//任务的优先级
type PlanPriority int64

const (
	PlanPriority_PLAN_PRIORITY_HIGHEST PlanPriority = 1
	PlanPriority_PLAN_PRIORITY_HIGH    PlanPriority = 2
	PlanPriority_PLAN_PRIORITY_MIDDLE  PlanPriority = 3
	PlanPriority_PLAN_PRIORITY_LOW     PlanPriority = 4
	PlanPriority_PLAN_PRIORITY_LOWEST  PlanPriority = 5
)

func (p PlanPriority) String() string {
	switch p {
	case PlanPriority_PLAN_PRIORITY_HIGHEST:
		return "PlanPriority_PLAN_PRIORITY_HIGHEST"
	case PlanPriority_PLAN_PRIORITY_HIGH:
		return "PlanPriority_PLAN_PRIORITY_HIGH"
	case PlanPriority_PLAN_PRIORITY_MIDDLE:
		return "PlanPriority_PLAN_PRIORITY_MIDDLE"
	case PlanPriority_PLAN_PRIORITY_LOW:
		return "PlanPriority_PLAN_PRIORITY_LOW"
	case PlanPriority_PLAN_PRIORITY_LOWEST:
		return "PlanPriority_PLAN_PRIORITY_LOWEST"
	}
	return "<UNSET>"
}

func PlanPriorityFromString(s string) (PlanPriority, error) {
	switch s {
	case "PlanPriority_PLAN_PRIORITY_HIGHEST":
		return PlanPriority_PLAN_PRIORITY_HIGHEST, nil
	case "PlanPriority_PLAN_PRIORITY_HIGH":
		return PlanPriority_PLAN_PRIORITY_HIGH, nil
	case "PlanPriority_PLAN_PRIORITY_MIDDLE":
		return PlanPriority_PLAN_PRIORITY_MIDDLE, nil
	case "PlanPriority_PLAN_PRIORITY_LOW":
		return PlanPriority_PLAN_PRIORITY_LOW, nil
	case "PlanPriority_PLAN_PRIORITY_LOWEST":
		return PlanPriority_PLAN_PRIORITY_LOWEST, nil
	}
	return PlanPriority(math.MinInt32 - 1), fmt.Errorf("not a valid PlanPriority string")
}

//任务执行的状态
type JobStatus int64

const (
	JobStatus_JOB_STATUS_CREATE JobStatus = 0
	JobStatus_JOB_STATUS_NEW    JobStatus = 1
	JobStatus_JOB_STATUS_RUN    JobStatus = 2
	JobStatus_JOB_STATUS_SUCC   JobStatus = 3
	JobStatus_JOB_STATUS_FAIL   JobStatus = 4
	JobStatus_JOB_STATUS_AGAIN  JobStatus = 5
	JobStatus_JOB_STATUS_LOST   JobStatus = 6
)

func (p JobStatus) String() string {
	switch p {
	case JobStatus_JOB_STATUS_CREATE:
		return "JobStatus_JOB_STATUS_CREATE"
	case JobStatus_JOB_STATUS_NEW:
		return "JobStatus_JOB_STATUS_NEW"
	case JobStatus_JOB_STATUS_RUN:
		return "JobStatus_JOB_STATUS_RUN"
	case JobStatus_JOB_STATUS_SUCC:
		return "JobStatus_JOB_STATUS_SUCC"
	case JobStatus_JOB_STATUS_FAIL:
		return "JobStatus_JOB_STATUS_FAIL"
	case JobStatus_JOB_STATUS_AGAIN:
		return "JobStatus_JOB_STATUS_AGAIN"
	case JobStatus_JOB_STATUS_LOST:
		return "JobStatus_JOB_STATUS_LOST"
	}
	return "<UNSET>"
}

func JobStatusFromString(s string) (JobStatus, error) {
	switch s {
	case "JobStatus_JOB_STATUS_CREATE":
		return JobStatus_JOB_STATUS_CREATE, nil
	case "JobStatus_JOB_STATUS_NEW":
		return JobStatus_JOB_STATUS_NEW, nil
	case "JobStatus_JOB_STATUS_RUN":
		return JobStatus_JOB_STATUS_RUN, nil
	case "JobStatus_JOB_STATUS_SUCC":
		return JobStatus_JOB_STATUS_SUCC, nil
	case "JobStatus_JOB_STATUS_FAIL":
		return JobStatus_JOB_STATUS_FAIL, nil
	case "JobStatus_JOB_STATUS_AGAIN":
		return JobStatus_JOB_STATUS_AGAIN, nil
	case "JobStatus_JOB_STATUS_LOST":
		return JobStatus_JOB_STATUS_LOST, nil
	}
	return JobStatus(math.MinInt32 - 1), fmt.Errorf("not a valid JobStatus string")
}

//任务报警方式
type AlarmStyle int64

const (
	AlarmStyle_ALARM_STYLE_NONE            AlarmStyle = 0
	AlarmStyle_ALARM_STYLE_BY_MAIL         AlarmStyle = 1
	AlarmStyle_ALARM_STYLE_BY_MAIL_AND_SMS AlarmStyle = 2
	AlarmStyle_ALARM_STYLE_BY_FAILED_SMS   AlarmStyle = 3
)

func (p AlarmStyle) String() string {
	switch p {
	case AlarmStyle_ALARM_STYLE_NONE:
		return "AlarmStyle_ALARM_STYLE_NONE"
	case AlarmStyle_ALARM_STYLE_BY_MAIL:
		return "AlarmStyle_ALARM_STYLE_BY_MAIL"
	case AlarmStyle_ALARM_STYLE_BY_MAIL_AND_SMS:
		return "AlarmStyle_ALARM_STYLE_BY_MAIL_AND_SMS"
	case AlarmStyle_ALARM_STYLE_BY_FAILED_SMS:
		return "AlarmStyle_ALARM_STYLE_BY_FAILED_SMS"
	}
	return "<UNSET>"
}

func AlarmStyleFromString(s string) (AlarmStyle, error) {
	switch s {
	case "AlarmStyle_ALARM_STYLE_NONE":
		return AlarmStyle_ALARM_STYLE_NONE, nil
	case "AlarmStyle_ALARM_STYLE_BY_MAIL":
		return AlarmStyle_ALARM_STYLE_BY_MAIL, nil
	case "AlarmStyle_ALARM_STYLE_BY_MAIL_AND_SMS":
		return AlarmStyle_ALARM_STYLE_BY_MAIL_AND_SMS, nil
	case "AlarmStyle_ALARM_STYLE_BY_FAILED_SMS":
		return AlarmStyle_ALARM_STYLE_BY_FAILED_SMS, nil
	}
	return AlarmStyle(math.MinInt32 - 1), fmt.Errorf("not a valid AlarmStyle string")
}

type RequestHeader *common.RequestHeader

type IdInt common.IdInt

type TimeInt common.TimeInt

type DominoException struct {
	Code    DominoErrorCode `thrift:"code,1" json:"code"`
	Message string          `thrift:"message,2" json:"message"`
}

func NewDominoException() *DominoException {
	return &DominoException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DominoException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DominoException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DominoException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DominoErrorCode(v)
	}
	return nil
}

func (p *DominoException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DominoException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DominoException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DominoException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DominoException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DominoException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DominoException(%+v)", *p)
}

type JobOutput struct {
	Stdout   string `thrift:"stdout,1" json:"stdout"`
	Stderr   string `thrift:"stderr,2" json:"stderr"`
	ExitCode int32  `thrift:"exitCode,3" json:"exitCode"`
}

func NewJobOutput() *JobOutput {
	return &JobOutput{}
}

func (p *JobOutput) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *JobOutput) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Stdout = v
	}
	return nil
}

func (p *JobOutput) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Stderr = v
	}
	return nil
}

func (p *JobOutput) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExitCode = v
	}
	return nil
}

func (p *JobOutput) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("JobOutput"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *JobOutput) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stdout", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:stdout: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Stdout)); err != nil {
		return fmt.Errorf("%T.stdout (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:stdout: %s", p, err)
	}
	return err
}

func (p *JobOutput) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stderr", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:stderr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Stderr)); err != nil {
		return fmt.Errorf("%T.stderr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:stderr: %s", p, err)
	}
	return err
}

func (p *JobOutput) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exitCode", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exitCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExitCode)); err != nil {
		return fmt.Errorf("%T.exitCode (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exitCode: %s", p, err)
	}
	return err
}

func (p *JobOutput) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("JobOutput(%+v)", *p)
}

type Job struct {
	Id          IdInt     `thrift:"id,1" json:"id"`
	PlanId      IdInt     `thrift:"planId,2" json:"planId"`
	Cmd         string    `thrift:"cmd,3" json:"cmd"`
	Username    string    `thrift:"username,4" json:"username"`
	Hostname    string    `thrift:"hostname,5" json:"hostname"`
	Status      JobStatus `thrift:"status,6" json:"status"`
	SchedTime   TimeInt   `thrift:"schedTime,7" json:"schedTime"`
	StartTime   TimeInt   `thrift:"startTime,8" json:"startTime"`
	FinishTime  TimeInt   `thrift:"finishTime,9" json:"finishTime"`
	CmdExitCode int32     `thrift:"cmdExitCode,10" json:"cmdExitCode"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Output          *JobOutput `thrift:"output,20" json:"output"`
	PlanName        string     `thrift:"planName,21" json:"planName"`
	PlancliThreadId IdInt      `thrift:"plancliThreadId,22" json:"plancliThreadId"`
	LastUpdateTime  TimeInt    `thrift:"lastUpdateTime,23" json:"lastUpdateTime"`
}

func NewJob() *Job {
	return &Job{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Job) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Job) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Job) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *Job) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *Job) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cmd = v
	}
	return nil
}

func (p *Job) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *Job) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Hostname = v
	}
	return nil
}

func (p *Job) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Status = JobStatus(v)
	}
	return nil
}

func (p *Job) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SchedTime = TimeInt(v)
	}
	return nil
}

func (p *Job) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *Job) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.FinishTime = TimeInt(v)
	}
	return nil
}

func (p *Job) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CmdExitCode = v
	}
	return nil
}

func (p *Job) readField20(iprot thrift.TProtocol) error {
	p.Output = NewJobOutput()
	if err := p.Output.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Output)
	}
	return nil
}

func (p *Job) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.PlanName = v
	}
	return nil
}

func (p *Job) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.PlancliThreadId = IdInt(v)
	}
	return nil
}

func (p *Job) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.LastUpdateTime = TimeInt(v)
	}
	return nil
}

func (p *Job) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Job"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Job) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Job) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *Job) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cmd", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cmd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cmd)); err != nil {
		return fmt.Errorf("%T.cmd (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cmd: %s", p, err)
	}
	return err
}

func (p *Job) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:username: %s", p, err)
	}
	return err
}

func (p *Job) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostname", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:hostname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostname)); err != nil {
		return fmt.Errorf("%T.hostname (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:hostname: %s", p, err)
	}
	return err
}

func (p *Job) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:status: %s", p, err)
		}
	}
	return err
}

func (p *Job) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("schedTime", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:schedTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SchedTime)); err != nil {
		return fmt.Errorf("%T.schedTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:schedTime: %s", p, err)
	}
	return err
}

func (p *Job) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:startTime: %s", p, err)
	}
	return err
}

func (p *Job) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finishTime", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:finishTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FinishTime)); err != nil {
		return fmt.Errorf("%T.finishTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:finishTime: %s", p, err)
	}
	return err
}

func (p *Job) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cmdExitCode", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:cmdExitCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CmdExitCode)); err != nil {
		return fmt.Errorf("%T.cmdExitCode (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:cmdExitCode: %s", p, err)
	}
	return err
}

func (p *Job) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Output != nil {
		if err := oprot.WriteFieldBegin("output", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:output: %s", p, err)
		}
		if err := p.Output.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Output)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:output: %s", p, err)
		}
	}
	return err
}

func (p *Job) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planName", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:planName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PlanName)); err != nil {
		return fmt.Errorf("%T.planName (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:planName: %s", p, err)
	}
	return err
}

func (p *Job) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plancliThreadId", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:plancliThreadId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlancliThreadId)); err != nil {
		return fmt.Errorf("%T.plancliThreadId (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:plancliThreadId: %s", p, err)
	}
	return err
}

func (p *Job) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdateTime", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:lastUpdateTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdateTime)); err != nil {
		return fmt.Errorf("%T.lastUpdateTime (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:lastUpdateTime: %s", p, err)
	}
	return err
}

func (p *Job) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Job(%+v)", *p)
}

type Tag struct {
	Id      IdInt  `thrift:"id,1" json:"id"`
	Name    string `thrift:"name,2" json:"name"`
	Comment string `thrift:"comment,3" json:"comment"`
	Urgency int8   `thrift:"urgency,4" json:"urgency"`
}

func NewTag() *Tag {
	return &Tag{}
}

func (p *Tag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Tag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *Tag) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Tag) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *Tag) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Urgency = int8(v)
	}
	return nil
}

func (p *Tag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Tag"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Tag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Tag) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Tag) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:comment: %s", p, err)
	}
	return err
}

func (p *Tag) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("urgency", thrift.BYTE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:urgency: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Urgency)); err != nil {
		return fmt.Errorf("%T.urgency (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:urgency: %s", p, err)
	}
	return err
}

func (p *Tag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tag(%+v)", *p)
}

type Plan struct {
	Id              IdInt        `thrift:"id,1" json:"id"`
	Name            string       `thrift:"name,2" json:"name"`
	Cmd             string       `thrift:"cmd,3" json:"cmd"`
	Cycle           PlanCycle    `thrift:"cycle,4" json:"cycle"`
	MaxCmdDuration  int32        `thrift:"maxCmdDuration,5" json:"maxCmdDuration"`
	MaxShedDuration int32        `thrift:"maxShedDuration,6" json:"maxShedDuration"`
	Description     string       `thrift:"description,7" json:"description"`
	CreateTime      TimeInt      `thrift:"createTime,8" json:"createTime"`
	LastJob         *Job         `thrift:"lastJob,9" json:"lastJob"`
	ContactGroup    []string     `thrift:"contactGroup,10" json:"contactGroup"`
	PlanStatus      PlanStatus   `thrift:"planStatus,11" json:"planStatus"`
	Priority        PlanPriority `thrift:"priority,12" json:"priority"`
	RetryCount      int32        `thrift:"retryCount,13" json:"retryCount"`
	DelayTime       int32        `thrift:"delayTime,14" json:"delayTime"`
	Username        string       `thrift:"username,15" json:"username"`
	Hostname        string       `thrift:"hostname,16" json:"hostname"`
	AlarmStyle      AlarmStyle   `thrift:"alarmStyle,17" json:"alarmStyle"`
	LastStartTime   TimeInt      `thrift:"lastStartTime,18" json:"lastStartTime"`
	Maintainer      string       `thrift:"maintainer,19" json:"maintainer"`
	Creator         string       `thrift:"creator,20" json:"creator"`
	RetryInterval   int32        `thrift:"retryInterval,21" json:"retryInterval"`
}

func NewPlan() *Plan {
	return &Plan{
		Cycle: math.MinInt32 - 1, // unset sentinal value

		PlanStatus: math.MinInt32 - 1, // unset sentinal value

		Priority: math.MinInt32 - 1, // unset sentinal value

		AlarmStyle: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Plan) IsSetCycle() bool {
	return int64(p.Cycle) != math.MinInt32-1
}

func (p *Plan) IsSetPlanStatus() bool {
	return int64(p.PlanStatus) != math.MinInt32-1
}

func (p *Plan) IsSetPriority() bool {
	return int64(p.Priority) != math.MinInt32-1
}

func (p *Plan) IsSetAlarmStyle() bool {
	return int64(p.AlarmStyle) != math.MinInt32-1
}

func (p *Plan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Plan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *Plan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Plan) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cmd = v
	}
	return nil
}

func (p *Plan) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cycle = PlanCycle(v)
	}
	return nil
}

func (p *Plan) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MaxCmdDuration = v
	}
	return nil
}

func (p *Plan) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MaxShedDuration = v
	}
	return nil
}

func (p *Plan) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Plan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Plan) readField9(iprot thrift.TProtocol) error {
	p.LastJob = NewJob()
	if err := p.LastJob.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.LastJob)
	}
	return nil
}

func (p *Plan) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ContactGroup = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ContactGroup = append(p.ContactGroup, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Plan) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PlanStatus = PlanStatus(v)
	}
	return nil
}

func (p *Plan) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Priority = PlanPriority(v)
	}
	return nil
}

func (p *Plan) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RetryCount = v
	}
	return nil
}

func (p *Plan) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DelayTime = v
	}
	return nil
}

func (p *Plan) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *Plan) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Hostname = v
	}
	return nil
}

func (p *Plan) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AlarmStyle = AlarmStyle(v)
	}
	return nil
}

func (p *Plan) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.LastStartTime = TimeInt(v)
	}
	return nil
}

func (p *Plan) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Maintainer = v
	}
	return nil
}

func (p *Plan) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *Plan) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.RetryInterval = v
	}
	return nil
}

func (p *Plan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Plan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Plan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Plan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Plan) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cmd", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cmd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cmd)); err != nil {
		return fmt.Errorf("%T.cmd (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cmd: %s", p, err)
	}
	return err
}

func (p *Plan) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCycle() {
		if err := oprot.WriteFieldBegin("cycle", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:cycle: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Cycle)); err != nil {
			return fmt.Errorf("%T.cycle (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:cycle: %s", p, err)
		}
	}
	return err
}

func (p *Plan) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxCmdDuration", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:maxCmdDuration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxCmdDuration)); err != nil {
		return fmt.Errorf("%T.maxCmdDuration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:maxCmdDuration: %s", p, err)
	}
	return err
}

func (p *Plan) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxShedDuration", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:maxShedDuration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxShedDuration)); err != nil {
		return fmt.Errorf("%T.maxShedDuration (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:maxShedDuration: %s", p, err)
	}
	return err
}

func (p *Plan) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:description: %s", p, err)
	}
	return err
}

func (p *Plan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:createTime: %s", p, err)
	}
	return err
}

func (p *Plan) writeField9(oprot thrift.TProtocol) (err error) {
	if p.LastJob != nil {
		if err := oprot.WriteFieldBegin("lastJob", thrift.STRUCT, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:lastJob: %s", p, err)
		}
		if err := p.LastJob.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.LastJob)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:lastJob: %s", p, err)
		}
	}
	return err
}

func (p *Plan) writeField10(oprot thrift.TProtocol) (err error) {
	if p.ContactGroup != nil {
		if err := oprot.WriteFieldBegin("contactGroup", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:contactGroup: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ContactGroup)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ContactGroup {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:contactGroup: %s", p, err)
		}
	}
	return err
}

func (p *Plan) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlanStatus() {
		if err := oprot.WriteFieldBegin("planStatus", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:planStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PlanStatus)); err != nil {
			return fmt.Errorf("%T.planStatus (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:planStatus: %s", p, err)
		}
	}
	return err
}

func (p *Plan) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetPriority() {
		if err := oprot.WriteFieldBegin("priority", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:priority: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Priority)); err != nil {
			return fmt.Errorf("%T.priority (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:priority: %s", p, err)
		}
	}
	return err
}

func (p *Plan) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("retryCount", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:retryCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RetryCount)); err != nil {
		return fmt.Errorf("%T.retryCount (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:retryCount: %s", p, err)
	}
	return err
}

func (p *Plan) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("delayTime", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:delayTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DelayTime)); err != nil {
		return fmt.Errorf("%T.delayTime (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:delayTime: %s", p, err)
	}
	return err
}

func (p *Plan) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:username: %s", p, err)
	}
	return err
}

func (p *Plan) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostname", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:hostname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostname)); err != nil {
		return fmt.Errorf("%T.hostname (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:hostname: %s", p, err)
	}
	return err
}

func (p *Plan) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetAlarmStyle() {
		if err := oprot.WriteFieldBegin("alarmStyle", thrift.I32, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:alarmStyle: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AlarmStyle)); err != nil {
			return fmt.Errorf("%T.alarmStyle (17) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:alarmStyle: %s", p, err)
		}
	}
	return err
}

func (p *Plan) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastStartTime", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:lastStartTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastStartTime)); err != nil {
		return fmt.Errorf("%T.lastStartTime (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:lastStartTime: %s", p, err)
	}
	return err
}

func (p *Plan) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maintainer", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:maintainer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Maintainer)); err != nil {
		return fmt.Errorf("%T.maintainer (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:maintainer: %s", p, err)
	}
	return err
}

func (p *Plan) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:creator: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:creator: %s", p, err)
	}
	return err
}

func (p *Plan) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("retryInterval", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:retryInterval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RetryInterval)); err != nil {
		return fmt.Errorf("%T.retryInterval (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:retryInterval: %s", p, err)
	}
	return err
}

func (p *Plan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Plan(%+v)", *p)
}

type PlanDep struct {
	Id          IdInt   `thrift:"id,1" json:"id"`
	PlanId      IdInt   `thrift:"planId,2" json:"planId"`
	DepPlan     *Plan   `thrift:"depPlan,3" json:"depPlan"`
	Delta1      TimeInt `thrift:"delta1,4" json:"delta1"`
	Delta2      TimeInt `thrift:"delta2,5" json:"delta2"`
	Description string  `thrift:"description,6" json:"description"`
}

func NewPlanDep() *PlanDep {
	return &PlanDep{}
}

func (p *PlanDep) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PlanDep) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *PlanDep) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *PlanDep) readField3(iprot thrift.TProtocol) error {
	p.DepPlan = NewPlan()
	if err := p.DepPlan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DepPlan)
	}
	return nil
}

func (p *PlanDep) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Delta1 = TimeInt(v)
	}
	return nil
}

func (p *PlanDep) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Delta2 = TimeInt(v)
	}
	return nil
}

func (p *PlanDep) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *PlanDep) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PlanDep"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PlanDep) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PlanDep) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *PlanDep) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DepPlan != nil {
		if err := oprot.WriteFieldBegin("depPlan", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:depPlan: %s", p, err)
		}
		if err := p.DepPlan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DepPlan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:depPlan: %s", p, err)
		}
	}
	return err
}

func (p *PlanDep) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("delta1", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:delta1: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Delta1)); err != nil {
		return fmt.Errorf("%T.delta1 (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:delta1: %s", p, err)
	}
	return err
}

func (p *PlanDep) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("delta2", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:delta2: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Delta2)); err != nil {
		return fmt.Errorf("%T.delta2 (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:delta2: %s", p, err)
	}
	return err
}

func (p *PlanDep) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:description: %s", p, err)
	}
	return err
}

func (p *PlanDep) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlanDep(%+v)", *p)
}

type JobDepDetail struct {
	SrcJob    *Job `thrift:"srcJob,1" json:"srcJob"`
	TargetJob *Job `thrift:"targetJob,2" json:"targetJob"`
}

func NewJobDepDetail() *JobDepDetail {
	return &JobDepDetail{}
}

func (p *JobDepDetail) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *JobDepDetail) readField1(iprot thrift.TProtocol) error {
	p.SrcJob = NewJob()
	if err := p.SrcJob.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcJob)
	}
	return nil
}

func (p *JobDepDetail) readField2(iprot thrift.TProtocol) error {
	p.TargetJob = NewJob()
	if err := p.TargetJob.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TargetJob)
	}
	return nil
}

func (p *JobDepDetail) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("JobDepDetail"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *JobDepDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SrcJob != nil {
		if err := oprot.WriteFieldBegin("srcJob", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:srcJob: %s", p, err)
		}
		if err := p.SrcJob.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcJob)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:srcJob: %s", p, err)
		}
	}
	return err
}

func (p *JobDepDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TargetJob != nil {
		if err := oprot.WriteFieldBegin("targetJob", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:targetJob: %s", p, err)
		}
		if err := p.TargetJob.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TargetJob)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:targetJob: %s", p, err)
		}
	}
	return err
}

func (p *JobDepDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("JobDepDetail(%+v)", *p)
}

type TimeSpan struct {
	Left  TimeInt `thrift:"left,1" json:"left"`
	Right TimeInt `thrift:"right,2" json:"right"`
}

func NewTimeSpan() *TimeSpan {
	return &TimeSpan{}
}

func (p *TimeSpan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TimeSpan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Left = TimeInt(v)
	}
	return nil
}

func (p *TimeSpan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Right = TimeInt(v)
	}
	return nil
}

func (p *TimeSpan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TimeSpan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TimeSpan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("left", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:left: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Left)); err != nil {
		return fmt.Errorf("%T.left (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:left: %s", p, err)
	}
	return err
}

func (p *TimeSpan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("right", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:right: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Right)); err != nil {
		return fmt.Errorf("%T.right (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:right: %s", p, err)
	}
	return err
}

func (p *TimeSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeSpan(%+v)", *p)
}

type Contact struct {
	Name     string `thrift:"name,1" json:"name"`
	TypeA1   string `thrift:"type,2" json:"type"`
	Nickname string `thrift:"nickname,3" json:"nickname"`
	Email    string `thrift:"email,4" json:"email"`
	Pager    string `thrift:"pager,5" json:"pager"`
}

func NewContact() *Contact {
	return &Contact{}
}

func (p *Contact) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Contact) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Contact) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *Contact) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Nickname = v
	}
	return nil
}

func (p *Contact) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *Contact) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Pager = v
	}
	return nil
}

func (p *Contact) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Contact"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Contact) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Contact) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:type: %s", p, err)
	}
	return err
}

func (p *Contact) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickname", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:nickname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Nickname)); err != nil {
		return fmt.Errorf("%T.nickname (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:nickname: %s", p, err)
	}
	return err
}

func (p *Contact) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:email: %s", p, err)
	}
	return err
}

func (p *Contact) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pager", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pager: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pager)); err != nil {
		return fmt.Errorf("%T.pager (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pager: %s", p, err)
	}
	return err
}

func (p *Contact) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Contact(%+v)", *p)
}

type ContactGroup struct {
	Name     string   `thrift:"name,1" json:"name"`
	Nickname string   `thrift:"nickname,2" json:"nickname"`
	Members  []string `thrift:"members,3" json:"members"`
}

func NewContactGroup() *ContactGroup {
	return &ContactGroup{}
}

func (p *ContactGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ContactGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ContactGroup) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Nickname = v
	}
	return nil
}

func (p *ContactGroup) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Members = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Members = append(p.Members, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ContactGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ContactGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ContactGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *ContactGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickname", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:nickname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Nickname)); err != nil {
		return fmt.Errorf("%T.nickname (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:nickname: %s", p, err)
	}
	return err
}

func (p *ContactGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Members != nil {
		if err := oprot.WriteFieldBegin("members", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:members: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Members)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Members {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:members: %s", p, err)
		}
	}
	return err
}

func (p *ContactGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContactGroup(%+v)", *p)
}
