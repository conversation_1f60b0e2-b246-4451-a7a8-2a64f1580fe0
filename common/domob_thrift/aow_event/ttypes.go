// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type Properties struct {
	Imei        string                `thrift:"imei,1" json:"imei"`
	Mac         string                `thrift:"mac,2" json:"mac"`
	Ip          string                `thrift:"ip,3" json:"ip"`
	AndroidId   string                `thrift:"android_id,4" json:"android_id"`
	Amn         string                `thrift:"amn,5" json:"amn"`
	Sv          string                `thrift:"sv,6" json:"sv"`
	SendTime    int64                 `thrift:"send_time,7" json:"send_time"`
	AccessCode  common.AccessTypeCode `thrift:"access_code,8" json:"access_code"`
	CarrierCode common.CarrierCode    `thrift:"carrier_code,9" json:"carrier_code"`
	RegionCode  int32                 `thrift:"region_code,10" json:"region_code"`
	OsCode      int32                 `thrift:"os_code,11" json:"os_code"`
	DeviceCode  int32                 `thrift:"device_code,12" json:"device_code"`
	Latitude    string                `thrift:"latitude,13" json:"latitude"`
	Longitude   string                `thrift:"longitude,14" json:"longitude"`
	Extrainfo   string                `thrift:"extrainfo,15" json:"extrainfo"`
	Isspam      bool                  `thrift:"isspam,16" json:"isspam"`
	SpamType    string                `thrift:"spam_type,17" json:"spam_type"`
	// unused field # 18
	SearchId     int64  `thrift:"search_id,19" json:"search_id"`
	Ipb          string `thrift:"ipb,20" json:"ipb"`
	DevId        int32  `thrift:"dev_id,21" json:"dev_id"`
	MediaId      int32  `thrift:"media_id,22" json:"media_id"`
	MediaPkgName string `thrift:"media_pkg_name,23" json:"media_pkg_name"`
	MedisUserId  string `thrift:"medis_user_id,24" json:"medis_user_id"`
	ImpReqTime   int32  `thrift:"imp_req_time,25" json:"imp_req_time"`
	// unused field # 26
	MediaCharge bool   `thrift:"media_charge,27" json:"media_charge"`
	AdCharge    bool   `thrift:"ad_charge,28" json:"ad_charge"`
	SponsorId   int32  `thrift:"sponsor_id,29" json:"sponsor_id"`
	Planid      int32  `thrift:"planid,30" json:"planid"`
	Cid         int32  `thrift:"cid,31" json:"cid"`
	Pkgid       int32  `thrift:"pkgid,32" json:"pkgid"`
	Appid       string `thrift:"appid,33" json:"appid"`
	Action      int32  `thrift:"action,34" json:"action"`
	SpPrice     int64  `thrift:"sp_price,35" json:"sp_price"`
	Price       int64  `thrift:"price,36" json:"price"`
	Mediashare  int64  `thrift:"mediashare,37" json:"mediashare"`
	Point       int64  `thrift:"point,38" json:"point"`
	Rank        int32  `thrift:"rank,39" json:"rank"`
	AdPkgName   string `thrift:"ad_pkg_name,40" json:"ad_pkg_name"`
}

func NewProperties() *Properties {
	return &Properties{
		AccessCode: math.MinInt32 - 1, // unset sentinal value

		CarrierCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Properties) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *Properties) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *Properties) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I64 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I64 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Properties) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *Properties) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *Properties) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *Properties) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *Properties) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *Properties) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *Properties) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SendTime = v
	}
	return nil
}

func (p *Properties) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AccessCode = common.AccessTypeCode(v)
	}
	return nil
}

func (p *Properties) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CarrierCode = common.CarrierCode(v)
	}
	return nil
}

func (p *Properties) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *Properties) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *Properties) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *Properties) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *Properties) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *Properties) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Extrainfo = v
	}
	return nil
}

func (p *Properties) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *Properties) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *Properties) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *Properties) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *Properties) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.DevId = v
	}
	return nil
}

func (p *Properties) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *Properties) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.MediaPkgName = v
	}
	return nil
}

func (p *Properties) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.MedisUserId = v
	}
	return nil
}

func (p *Properties) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.ImpReqTime = v
	}
	return nil
}

func (p *Properties) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *Properties) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *Properties) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *Properties) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *Properties) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *Properties) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Pkgid = v
	}
	return nil
}

func (p *Properties) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *Properties) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *Properties) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *Properties) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *Properties) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *Properties) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *Properties) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *Properties) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.AdPkgName = v
	}
	return nil
}

func (p *Properties) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Properties"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Properties) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:imei: %s", p, err)
	}
	return err
}

func (p *Properties) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mac: %s", p, err)
	}
	return err
}

func (p *Properties) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ip: %s", p, err)
	}
	return err
}

func (p *Properties) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:android_id: %s", p, err)
	}
	return err
}

func (p *Properties) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:amn: %s", p, err)
	}
	return err
}

func (p *Properties) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:sv: %s", p, err)
	}
	return err
}

func (p *Properties) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("send_time", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:send_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SendTime)); err != nil {
		return fmt.Errorf("%T.send_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:send_time: %s", p, err)
	}
	return err
}

func (p *Properties) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:access_code: %s", p, err)
	}
	return err
}

func (p *Properties) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:carrier_code: %s", p, err)
	}
	return err
}

func (p *Properties) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:region_code: %s", p, err)
	}
	return err
}

func (p *Properties) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:os_code: %s", p, err)
	}
	return err
}

func (p *Properties) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:device_code: %s", p, err)
	}
	return err
}

func (p *Properties) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:latitude: %s", p, err)
	}
	return err
}

func (p *Properties) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:longitude: %s", p, err)
	}
	return err
}

func (p *Properties) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extrainfo", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:extrainfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extrainfo)); err != nil {
		return fmt.Errorf("%T.extrainfo (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:extrainfo: %s", p, err)
	}
	return err
}

func (p *Properties) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:isspam: %s", p, err)
	}
	return err
}

func (p *Properties) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:spam_type: %s", p, err)
	}
	return err
}

func (p *Properties) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:search_id: %s", p, err)
	}
	return err
}

func (p *Properties) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:ipb: %s", p, err)
	}
	return err
}

func (p *Properties) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dev_id", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:dev_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DevId)); err != nil {
		return fmt.Errorf("%T.dev_id (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:dev_id: %s", p, err)
	}
	return err
}

func (p *Properties) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_id", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.media_id (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:media_id: %s", p, err)
	}
	return err
}

func (p *Properties) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_pkg_name", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:media_pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaPkgName)); err != nil {
		return fmt.Errorf("%T.media_pkg_name (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:media_pkg_name: %s", p, err)
	}
	return err
}

func (p *Properties) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("medis_user_id", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:medis_user_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MedisUserId)); err != nil {
		return fmt.Errorf("%T.medis_user_id (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:medis_user_id: %s", p, err)
	}
	return err
}

func (p *Properties) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_req_time", thrift.I32, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:imp_req_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImpReqTime)); err != nil {
		return fmt.Errorf("%T.imp_req_time (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:imp_req_time: %s", p, err)
	}
	return err
}

func (p *Properties) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:media_charge: %s", p, err)
	}
	return err
}

func (p *Properties) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:ad_charge: %s", p, err)
	}
	return err
}

func (p *Properties) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:sponsor_id: %s", p, err)
	}
	return err
}

func (p *Properties) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:planid: %s", p, err)
	}
	return err
}

func (p *Properties) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:cid: %s", p, err)
	}
	return err
}

func (p *Properties) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgid", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:pkgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pkgid)); err != nil {
		return fmt.Errorf("%T.pkgid (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:pkgid: %s", p, err)
	}
	return err
}

func (p *Properties) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:appid: %s", p, err)
	}
	return err
}

func (p *Properties) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:action: %s", p, err)
	}
	return err
}

func (p *Properties) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:sp_price: %s", p, err)
	}
	return err
}

func (p *Properties) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:price: %s", p, err)
	}
	return err
}

func (p *Properties) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:mediashare: %s", p, err)
	}
	return err
}

func (p *Properties) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:point: %s", p, err)
	}
	return err
}

func (p *Properties) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:rank: %s", p, err)
	}
	return err
}

func (p *Properties) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_pkg_name", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:ad_pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdPkgName)); err != nil {
		return fmt.Errorf("%T.ad_pkg_name (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:ad_pkg_name: %s", p, err)
	}
	return err
}

func (p *Properties) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Properties(%+v)", *p)
}

type AowEvent struct {
	Time       int64       `thrift:"time,1" json:"time"`
	DistinctId string      `thrift:"distinct_id,2" json:"distinct_id"`
	Event      string      `thrift:"event,3" json:"event"`
	TypeA1     string      `thrift:"type,4" json:"type"`
	Properties *Properties `thrift:"properties,5" json:"properties"`
}

func NewAowEvent() *AowEvent {
	return &AowEvent{}
}

func (p *AowEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *AowEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DistinctId = v
	}
	return nil
}

func (p *AowEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Event = v
	}
	return nil
}

func (p *AowEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *AowEvent) readField5(iprot thrift.TProtocol) error {
	p.Properties = NewProperties()
	if err := p.Properties.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Properties)
	}
	return nil
}

func (p *AowEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *AowEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("distinct_id", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:distinct_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DistinctId)); err != nil {
		return fmt.Errorf("%T.distinct_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:distinct_id: %s", p, err)
	}
	return err
}

func (p *AowEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:event: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Event)); err != nil {
		return fmt.Errorf("%T.event (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:event: %s", p, err)
	}
	return err
}

func (p *AowEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *AowEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Properties != nil {
		if err := oprot.WriteFieldBegin("properties", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:properties: %s", p, err)
		}
		if err := p.Properties.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Properties)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:properties: %s", p, err)
		}
	}
	return err
}

func (p *AowEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowEvent(%+v)", *p)
}
