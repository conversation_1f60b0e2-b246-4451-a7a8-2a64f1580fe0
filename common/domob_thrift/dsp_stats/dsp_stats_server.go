// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dsp_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dsp_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type DspStatsServer interface { //DSP Stats 统计服务接口定义
	//

	// 查询统计信息
	// 该方法将逐渐废弃，请使用queryStatsDataWithOrder
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (r *StatsQueryResult, e *DspStatsServerException, err error)
	// 查询统计信息，增加排序条件
	// 建议使用queryStatsDataPlus
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Order: 排序条件 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryStatsDataWithOrder(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32) (r *StatsQueryResult, e *DspStatsServerException, err error)
	// 查询活动日统计信息
	// 该方法将逐渐废弃，请使用queryDailyStatsDataPlus
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式，该参数已经废弃 *
	//  - Order: 排序条件 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryDailyStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32) (r *StatsQueryResult, e *DspStatsServerException, err error)
	// 查询活动报表、活动日报表总计数据
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	QueryStatsTotalData(header *common.RequestHeader, condition *StatsConditionParam) (r *DspStatsData, e *DspStatsServerException, err error)
	// 查询统计信息，增加排序条件和显示无统计数据的活动的能力
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Order: 排序条件 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	//  - ExcludeEmpty: 是否输出没有统计数据的活动，默认为false，只输出含有统计数据的活动 *
	QueryStatsDataPlus(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32, excludeEmpty bool) (r *StatsQueryResult, e *DspStatsServerException, err error)
	// 查询活动日统计信息，增加显示无统计数据的活动的能力
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式，该参数已经废弃 *
	//  - Order: 排序条件 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	//  - ExcludeEmpty: 是否输出没有统计数据的活动，默认为false，只输出含有统计数据的活动 *
	QueryDailyStatsDataPlus(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32, excludeEmpty bool) (r *StatsQueryResult, e *DspStatsServerException, err error)
}

//DSP Stats 统计服务接口定义
//
type DspStatsServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDspStatsServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DspStatsServerClient {
	return &DspStatsServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDspStatsServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DspStatsServerClient {
	return &DspStatsServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询统计信息
// 该方法将逐渐废弃，请使用queryStatsDataWithOrder
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DspStatsServerClient) QueryStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (r *StatsQueryResult, e *DspStatsServerException, err error) {
	if err = p.sendQueryStatsData(header, condition, group, offset, limit); err != nil {
		return
	}
	return p.recvQueryStatsData()
}

func (p *DspStatsServerClient) sendQueryStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewQueryStatsDataArgs()
	args8.Header = header
	args8.Condition = condition
	args8.Group = group
	args8.Offset = offset
	args8.Limit = limit
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspStatsServerClient) recvQueryStatsData() (value *StatsQueryResult, e *DspStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewQueryStatsDataResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 查询统计信息，增加排序条件
// 建议使用queryStatsDataPlus
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Order: 排序条件 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DspStatsServerClient) QueryStatsDataWithOrder(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32) (r *StatsQueryResult, e *DspStatsServerException, err error) {
	if err = p.sendQueryStatsDataWithOrder(header, condition, group, order, offset, limit); err != nil {
		return
	}
	return p.recvQueryStatsDataWithOrder()
}

func (p *DspStatsServerClient) sendQueryStatsDataWithOrder(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsDataWithOrder", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewQueryStatsDataWithOrderArgs()
	args12.Header = header
	args12.Condition = condition
	args12.Group = group
	args12.Order = order
	args12.Offset = offset
	args12.Limit = limit
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspStatsServerClient) recvQueryStatsDataWithOrder() (value *StatsQueryResult, e *DspStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewQueryStatsDataWithOrderResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.E != nil {
		e = result13.E
	}
	return
}

// 查询活动日统计信息
// 该方法将逐渐废弃，请使用queryDailyStatsDataPlus
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式，该参数已经废弃 *
//  - Order: 排序条件 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DspStatsServerClient) QueryDailyStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32) (r *StatsQueryResult, e *DspStatsServerException, err error) {
	if err = p.sendQueryDailyStatsData(header, condition, group, order, offset, limit); err != nil {
		return
	}
	return p.recvQueryDailyStatsData()
}

func (p *DspStatsServerClient) sendQueryDailyStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryDailyStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewQueryDailyStatsDataArgs()
	args16.Header = header
	args16.Condition = condition
	args16.Group = group
	args16.Order = order
	args16.Offset = offset
	args16.Limit = limit
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspStatsServerClient) recvQueryDailyStatsData() (value *StatsQueryResult, e *DspStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewQueryDailyStatsDataResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.E != nil {
		e = result17.E
	}
	return
}

// 查询活动报表、活动日报表总计数据
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
func (p *DspStatsServerClient) QueryStatsTotalData(header *common.RequestHeader, condition *StatsConditionParam) (r *DspStatsData, e *DspStatsServerException, err error) {
	if err = p.sendQueryStatsTotalData(header, condition); err != nil {
		return
	}
	return p.recvQueryStatsTotalData()
}

func (p *DspStatsServerClient) sendQueryStatsTotalData(header *common.RequestHeader, condition *StatsConditionParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsTotalData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewQueryStatsTotalDataArgs()
	args20.Header = header
	args20.Condition = condition
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspStatsServerClient) recvQueryStatsTotalData() (value *DspStatsData, e *DspStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewQueryStatsTotalDataResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.E != nil {
		e = result21.E
	}
	return
}

// 查询统计信息，增加排序条件和显示无统计数据的活动的能力
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Order: 排序条件 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
//  - ExcludeEmpty: 是否输出没有统计数据的活动，默认为false，只输出含有统计数据的活动 *
func (p *DspStatsServerClient) QueryStatsDataPlus(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32, excludeEmpty bool) (r *StatsQueryResult, e *DspStatsServerException, err error) {
	if err = p.sendQueryStatsDataPlus(header, condition, group, order, offset, limit, excludeEmpty); err != nil {
		return
	}
	return p.recvQueryStatsDataPlus()
}

func (p *DspStatsServerClient) sendQueryStatsDataPlus(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32, excludeEmpty bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsDataPlus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewQueryStatsDataPlusArgs()
	args24.Header = header
	args24.Condition = condition
	args24.Group = group
	args24.Order = order
	args24.Offset = offset
	args24.Limit = limit
	args24.ExcludeEmpty = excludeEmpty
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspStatsServerClient) recvQueryStatsDataPlus() (value *StatsQueryResult, e *DspStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewQueryStatsDataPlusResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.E != nil {
		e = result25.E
	}
	return
}

// 查询活动日统计信息，增加显示无统计数据的活动的能力
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式，该参数已经废弃 *
//  - Order: 排序条件 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
//  - ExcludeEmpty: 是否输出没有统计数据的活动，默认为false，只输出含有统计数据的活动 *
func (p *DspStatsServerClient) QueryDailyStatsDataPlus(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32, excludeEmpty bool) (r *StatsQueryResult, e *DspStatsServerException, err error) {
	if err = p.sendQueryDailyStatsDataPlus(header, condition, group, order, offset, limit, excludeEmpty); err != nil {
		return
	}
	return p.recvQueryDailyStatsDataPlus()
}

func (p *DspStatsServerClient) sendQueryDailyStatsDataPlus(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, offset int32, limit int32, excludeEmpty bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryDailyStatsDataPlus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewQueryDailyStatsDataPlusArgs()
	args28.Header = header
	args28.Condition = condition
	args28.Group = group
	args28.Order = order
	args28.Offset = offset
	args28.Limit = limit
	args28.ExcludeEmpty = excludeEmpty
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspStatsServerClient) recvQueryDailyStatsDataPlus() (value *StatsQueryResult, e *DspStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewQueryDailyStatsDataPlusResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.E != nil {
		e = result29.E
	}
	return
}

type DspStatsServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DspStatsServer
}

func (p *DspStatsServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DspStatsServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DspStatsServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDspStatsServerProcessor(handler DspStatsServer) *DspStatsServerProcessor {

	self32 := &DspStatsServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self32.processorMap["queryStatsData"] = &dspStatsServerProcessorQueryStatsData{handler: handler}
	self32.processorMap["queryStatsDataWithOrder"] = &dspStatsServerProcessorQueryStatsDataWithOrder{handler: handler}
	self32.processorMap["queryDailyStatsData"] = &dspStatsServerProcessorQueryDailyStatsData{handler: handler}
	self32.processorMap["queryStatsTotalData"] = &dspStatsServerProcessorQueryStatsTotalData{handler: handler}
	self32.processorMap["queryStatsDataPlus"] = &dspStatsServerProcessorQueryStatsDataPlus{handler: handler}
	self32.processorMap["queryDailyStatsDataPlus"] = &dspStatsServerProcessorQueryDailyStatsDataPlus{handler: handler}
	return self32
}

func (p *DspStatsServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x33 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x33.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x33

}

type dspStatsServerProcessorQueryStatsData struct {
	handler DspStatsServer
}

func (p *dspStatsServerProcessorQueryStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsDataResult()
	if result.Success, result.E, err = p.handler.QueryStatsData(args.Header, args.Condition, args.Group, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspStatsServerProcessorQueryStatsDataWithOrder struct {
	handler DspStatsServer
}

func (p *dspStatsServerProcessorQueryStatsDataWithOrder) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsDataWithOrderArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsDataWithOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsDataWithOrderResult()
	if result.Success, result.E, err = p.handler.QueryStatsDataWithOrder(args.Header, args.Condition, args.Group, args.Order, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsDataWithOrder: "+err.Error())
		oprot.WriteMessageBegin("queryStatsDataWithOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsDataWithOrder", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspStatsServerProcessorQueryDailyStatsData struct {
	handler DspStatsServer
}

func (p *dspStatsServerProcessorQueryDailyStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryDailyStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryDailyStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryDailyStatsDataResult()
	if result.Success, result.E, err = p.handler.QueryDailyStatsData(args.Header, args.Condition, args.Group, args.Order, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryDailyStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryDailyStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryDailyStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspStatsServerProcessorQueryStatsTotalData struct {
	handler DspStatsServer
}

func (p *dspStatsServerProcessorQueryStatsTotalData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsTotalDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsTotalData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsTotalDataResult()
	if result.Success, result.E, err = p.handler.QueryStatsTotalData(args.Header, args.Condition); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsTotalData: "+err.Error())
		oprot.WriteMessageBegin("queryStatsTotalData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsTotalData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspStatsServerProcessorQueryStatsDataPlus struct {
	handler DspStatsServer
}

func (p *dspStatsServerProcessorQueryStatsDataPlus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsDataPlusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsDataPlus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsDataPlusResult()
	if result.Success, result.E, err = p.handler.QueryStatsDataPlus(args.Header, args.Condition, args.Group, args.Order, args.Offset, args.Limit, args.ExcludeEmpty); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsDataPlus: "+err.Error())
		oprot.WriteMessageBegin("queryStatsDataPlus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsDataPlus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspStatsServerProcessorQueryDailyStatsDataPlus struct {
	handler DspStatsServer
}

func (p *dspStatsServerProcessorQueryDailyStatsDataPlus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryDailyStatsDataPlusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryDailyStatsDataPlus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryDailyStatsDataPlusResult()
	if result.Success, result.E, err = p.handler.QueryDailyStatsDataPlus(args.Header, args.Condition, args.Group, args.Order, args.Offset, args.Limit, args.ExcludeEmpty); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryDailyStatsDataPlus: "+err.Error())
		oprot.WriteMessageBegin("queryDailyStatsDataPlus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryDailyStatsDataPlus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type QueryStatsDataArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam    `thrift:"group,3" json:"group"`
	// unused field # 4
	Offset int32 `thrift:"offset,5" json:"offset"`
	Limit  int32 `thrift:"limit,6" json:"limit"`
}

func NewQueryStatsDataArgs() *QueryStatsDataArgs {
	return &QueryStatsDataArgs{}
}

func (p *QueryStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataArgs(%+v)", *p)
}

type QueryStatsDataResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DspStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryStatsDataResult() *QueryStatsDataResult {
	return &QueryStatsDataResult{}
}

func (p *QueryStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataResult(%+v)", *p)
}

type QueryStatsDataWithOrderArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam    `thrift:"group,3" json:"group"`
	Order     *StatsOrderByParam    `thrift:"order,4" json:"order"`
	Offset    int32                 `thrift:"offset,5" json:"offset"`
	Limit     int32                 `thrift:"limit,6" json:"limit"`
}

func NewQueryStatsDataWithOrderArgs() *QueryStatsDataWithOrderArgs {
	return &QueryStatsDataWithOrderArgs{}
}

func (p *QueryStatsDataWithOrderArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) readField4(iprot thrift.TProtocol) error {
	p.Order = NewStatsOrderByParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsDataWithOrder_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataWithOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataWithOrderArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataWithOrderArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataWithOrderArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataWithOrderArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataWithOrderArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataWithOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataWithOrderArgs(%+v)", *p)
}

type QueryStatsDataWithOrderResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DspStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryStatsDataWithOrderResult() *QueryStatsDataWithOrderResult {
	return &QueryStatsDataWithOrderResult{}
}

func (p *QueryStatsDataWithOrderResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataWithOrderResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsDataWithOrderResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsDataWithOrderResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsDataWithOrder_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataWithOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataWithOrderResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataWithOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataWithOrderResult(%+v)", *p)
}

type QueryDailyStatsDataArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam    `thrift:"group,3" json:"group"`
	Order     *StatsOrderByParam    `thrift:"order,4" json:"order"`
	Offset    int32                 `thrift:"offset,5" json:"offset"`
	Limit     int32                 `thrift:"limit,6" json:"limit"`
}

func NewQueryDailyStatsDataArgs() *QueryDailyStatsDataArgs {
	return &QueryDailyStatsDataArgs{}
}

func (p *QueryDailyStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.Order = NewStatsOrderByParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDailyStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryDailyStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryDailyStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDailyStatsDataArgs(%+v)", *p)
}

type QueryDailyStatsDataResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DspStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryDailyStatsDataResult() *QueryDailyStatsDataResult {
	return &QueryDailyStatsDataResult{}
}

func (p *QueryDailyStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryDailyStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryDailyStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDailyStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDailyStatsDataResult(%+v)", *p)
}

type QueryStatsTotalDataArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam  `thrift:"condition,2" json:"condition"`
}

func NewQueryStatsTotalDataArgs() *QueryStatsTotalDataArgs {
	return &QueryStatsTotalDataArgs{}
}

func (p *QueryStatsTotalDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsTotalDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsTotalDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsTotalDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsTotalData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsTotalDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsTotalDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsTotalDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsTotalDataArgs(%+v)", *p)
}

type QueryStatsTotalDataResult struct {
	Success *DspStatsData            `thrift:"success,0" json:"success"`
	E       *DspStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryStatsTotalDataResult() *QueryStatsTotalDataResult {
	return &QueryStatsTotalDataResult{}
}

func (p *QueryStatsTotalDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsTotalDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDspStatsData()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsTotalDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsTotalDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsTotalData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsTotalDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsTotalDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsTotalDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsTotalDataResult(%+v)", *p)
}

type QueryStatsDataPlusArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition    *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group        *StatsGroupByParam    `thrift:"group,3" json:"group"`
	Order        *StatsOrderByParam    `thrift:"order,4" json:"order"`
	Offset       int32                 `thrift:"offset,5" json:"offset"`
	Limit        int32                 `thrift:"limit,6" json:"limit"`
	ExcludeEmpty bool                  `thrift:"excludeEmpty,7" json:"excludeEmpty"`
}

func NewQueryStatsDataPlusArgs() *QueryStatsDataPlusArgs {
	return &QueryStatsDataPlusArgs{}
}

func (p *QueryStatsDataPlusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField4(iprot thrift.TProtocol) error {
	p.Order = NewStatsOrderByParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ExcludeEmpty = v
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsDataPlus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataPlusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataPlusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataPlusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataPlusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataPlusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataPlusArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataPlusArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeEmpty", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:excludeEmpty: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeEmpty)); err != nil {
		return fmt.Errorf("%T.excludeEmpty (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:excludeEmpty: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataPlusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataPlusArgs(%+v)", *p)
}

type QueryStatsDataPlusResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DspStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryStatsDataPlusResult() *QueryStatsDataPlusResult {
	return &QueryStatsDataPlusResult{}
}

func (p *QueryStatsDataPlusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataPlusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsDataPlusResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsDataPlusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsDataPlus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataPlusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataPlusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataPlusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataPlusResult(%+v)", *p)
}

type QueryDailyStatsDataPlusArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition    *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group        *StatsGroupByParam    `thrift:"group,3" json:"group"`
	Order        *StatsOrderByParam    `thrift:"order,4" json:"order"`
	Offset       int32                 `thrift:"offset,5" json:"offset"`
	Limit        int32                 `thrift:"limit,6" json:"limit"`
	ExcludeEmpty bool                  `thrift:"excludeEmpty,7" json:"excludeEmpty"`
}

func NewQueryDailyStatsDataPlusArgs() *QueryDailyStatsDataPlusArgs {
	return &QueryDailyStatsDataPlusArgs{}
}

func (p *QueryDailyStatsDataPlusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField4(iprot thrift.TProtocol) error {
	p.Order = NewStatsOrderByParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ExcludeEmpty = v
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDailyStatsDataPlus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeEmpty", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:excludeEmpty: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeEmpty)); err != nil {
		return fmt.Errorf("%T.excludeEmpty (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:excludeEmpty: %s", p, err)
	}
	return err
}

func (p *QueryDailyStatsDataPlusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDailyStatsDataPlusArgs(%+v)", *p)
}

type QueryDailyStatsDataPlusResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DspStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryDailyStatsDataPlusResult() *QueryDailyStatsDataPlusResult {
	return &QueryDailyStatsDataPlusResult{}
}

func (p *QueryDailyStatsDataPlusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDailyStatsDataPlus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDailyStatsDataPlusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataPlusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryDailyStatsDataPlusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDailyStatsDataPlusResult(%+v)", *p)
}
