// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dsp_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dsp_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("支持小时级查询接口starttime endtime精确维度")
type TimeAccuracyType int64

const (
	TimeAccuracyType_TAT_UNKNOWN TimeAccuracyType = 0
	TimeAccuracyType_TAT_DAY     TimeAccuracyType = 1
	TimeAccuracyType_TAT_HOUR    TimeAccuracyType = 2
)

func (p TimeAccuracyType) String() string {
	switch p {
	case TimeAccuracyType_TAT_UNKNOWN:
		return "TimeAccuracyType_TAT_UNKNOWN"
	case TimeAccuracyType_TAT_DAY:
		return "TimeAccuracyType_TAT_DAY"
	case TimeAccuracyType_TAT_HOUR:
		return "TimeAccuracyType_TAT_HOUR"
	}
	return "<UNSET>"
}

func TimeAccuracyTypeFromString(s string) (TimeAccuracyType, error) {
	switch s {
	case "TimeAccuracyType_TAT_UNKNOWN":
		return TimeAccuracyType_TAT_UNKNOWN, nil
	case "TimeAccuracyType_TAT_DAY":
		return TimeAccuracyType_TAT_DAY, nil
	case "TimeAccuracyType_TAT_HOUR":
		return TimeAccuracyType_TAT_HOUR, nil
	}
	return TimeAccuracyType(math.MinInt32 - 1), fmt.Errorf("not a valid TimeAccuracyType string")
}

//统计查询排序字段枚举值
//
type OrderByField int64

const (
	OrderByField_CAMPAIGN_ID                             OrderByField = 0
	OrderByField_AD_PRICE                                OrderByField = 1
	OrderByField_IMP                                     OrderByField = 2
	OrderByField_CLK                                     OrderByField = 3
	OrderByField_ACT                                     OrderByField = 4
	OrderByField_CTR                                     OrderByField = 5
	OrderByField_CVR                                     OrderByField = 6
	OrderByField_MEDIA_PRICE                             OrderByField = 7
	OrderByField_GROSS_MARGIN                            OrderByField = 8
	OrderByField_AD_PRICE_CHAIN_PREV_RATIO               OrderByField = 9
	OrderByField_AD_PRICE_CHAIN_RELATIVE_RATIO           OrderByField = 10
	OrderByField_IMP_CHAIN_PREV_RATIO                    OrderByField = 11
	OrderByField_IMP_CHAIN_RELATIVE_RATIO                OrderByField = 12
	OrderByField_CLK_CHAIN_PREV_RATIO                    OrderByField = 13
	OrderByField_CLK_CHAIN_RELATIVE_RATIO                OrderByField = 14
	OrderByField_ACT_CHAIN_PREV_RATIO                    OrderByField = 15
	OrderByField_ACT_CHAIN_RELATIVE_RATIO                OrderByField = 16
	OrderByField_EFFECTIVE_CREATIVE                      OrderByField = 17
	OrderByField_EFFECTIVE_CREATIVE_CHAIN_PREV_RATIO     OrderByField = 18
	OrderByField_EFFECTIVE_CREATIVE_CHAIN_RELATIVE_RATIO OrderByField = 19
	OrderByField_CONSUME_RATIO                           OrderByField = 20
	OrderByField_CAMPAIGN_CREATIVE_TIME                  OrderByField = 21
	OrderByField_CAMPAIGN_DAILY_BUDGET                   OrderByField = 22
	OrderByField_CAMPAIGN_TOTAL_BUDGET                   OrderByField = 23
)

func (p OrderByField) String() string {
	switch p {
	case OrderByField_CAMPAIGN_ID:
		return "OrderByField_CAMPAIGN_ID"
	case OrderByField_AD_PRICE:
		return "OrderByField_AD_PRICE"
	case OrderByField_IMP:
		return "OrderByField_IMP"
	case OrderByField_CLK:
		return "OrderByField_CLK"
	case OrderByField_ACT:
		return "OrderByField_ACT"
	case OrderByField_CTR:
		return "OrderByField_CTR"
	case OrderByField_CVR:
		return "OrderByField_CVR"
	case OrderByField_MEDIA_PRICE:
		return "OrderByField_MEDIA_PRICE"
	case OrderByField_GROSS_MARGIN:
		return "OrderByField_GROSS_MARGIN"
	case OrderByField_AD_PRICE_CHAIN_PREV_RATIO:
		return "OrderByField_AD_PRICE_CHAIN_PREV_RATIO"
	case OrderByField_AD_PRICE_CHAIN_RELATIVE_RATIO:
		return "OrderByField_AD_PRICE_CHAIN_RELATIVE_RATIO"
	case OrderByField_IMP_CHAIN_PREV_RATIO:
		return "OrderByField_IMP_CHAIN_PREV_RATIO"
	case OrderByField_IMP_CHAIN_RELATIVE_RATIO:
		return "OrderByField_IMP_CHAIN_RELATIVE_RATIO"
	case OrderByField_CLK_CHAIN_PREV_RATIO:
		return "OrderByField_CLK_CHAIN_PREV_RATIO"
	case OrderByField_CLK_CHAIN_RELATIVE_RATIO:
		return "OrderByField_CLK_CHAIN_RELATIVE_RATIO"
	case OrderByField_ACT_CHAIN_PREV_RATIO:
		return "OrderByField_ACT_CHAIN_PREV_RATIO"
	case OrderByField_ACT_CHAIN_RELATIVE_RATIO:
		return "OrderByField_ACT_CHAIN_RELATIVE_RATIO"
	case OrderByField_EFFECTIVE_CREATIVE:
		return "OrderByField_EFFECTIVE_CREATIVE"
	case OrderByField_EFFECTIVE_CREATIVE_CHAIN_PREV_RATIO:
		return "OrderByField_EFFECTIVE_CREATIVE_CHAIN_PREV_RATIO"
	case OrderByField_EFFECTIVE_CREATIVE_CHAIN_RELATIVE_RATIO:
		return "OrderByField_EFFECTIVE_CREATIVE_CHAIN_RELATIVE_RATIO"
	case OrderByField_CONSUME_RATIO:
		return "OrderByField_CONSUME_RATIO"
	case OrderByField_CAMPAIGN_CREATIVE_TIME:
		return "OrderByField_CAMPAIGN_CREATIVE_TIME"
	case OrderByField_CAMPAIGN_DAILY_BUDGET:
		return "OrderByField_CAMPAIGN_DAILY_BUDGET"
	case OrderByField_CAMPAIGN_TOTAL_BUDGET:
		return "OrderByField_CAMPAIGN_TOTAL_BUDGET"
	}
	return "<UNSET>"
}

func OrderByFieldFromString(s string) (OrderByField, error) {
	switch s {
	case "OrderByField_CAMPAIGN_ID":
		return OrderByField_CAMPAIGN_ID, nil
	case "OrderByField_AD_PRICE":
		return OrderByField_AD_PRICE, nil
	case "OrderByField_IMP":
		return OrderByField_IMP, nil
	case "OrderByField_CLK":
		return OrderByField_CLK, nil
	case "OrderByField_ACT":
		return OrderByField_ACT, nil
	case "OrderByField_CTR":
		return OrderByField_CTR, nil
	case "OrderByField_CVR":
		return OrderByField_CVR, nil
	case "OrderByField_MEDIA_PRICE":
		return OrderByField_MEDIA_PRICE, nil
	case "OrderByField_GROSS_MARGIN":
		return OrderByField_GROSS_MARGIN, nil
	case "OrderByField_AD_PRICE_CHAIN_PREV_RATIO":
		return OrderByField_AD_PRICE_CHAIN_PREV_RATIO, nil
	case "OrderByField_AD_PRICE_CHAIN_RELATIVE_RATIO":
		return OrderByField_AD_PRICE_CHAIN_RELATIVE_RATIO, nil
	case "OrderByField_IMP_CHAIN_PREV_RATIO":
		return OrderByField_IMP_CHAIN_PREV_RATIO, nil
	case "OrderByField_IMP_CHAIN_RELATIVE_RATIO":
		return OrderByField_IMP_CHAIN_RELATIVE_RATIO, nil
	case "OrderByField_CLK_CHAIN_PREV_RATIO":
		return OrderByField_CLK_CHAIN_PREV_RATIO, nil
	case "OrderByField_CLK_CHAIN_RELATIVE_RATIO":
		return OrderByField_CLK_CHAIN_RELATIVE_RATIO, nil
	case "OrderByField_ACT_CHAIN_PREV_RATIO":
		return OrderByField_ACT_CHAIN_PREV_RATIO, nil
	case "OrderByField_ACT_CHAIN_RELATIVE_RATIO":
		return OrderByField_ACT_CHAIN_RELATIVE_RATIO, nil
	case "OrderByField_EFFECTIVE_CREATIVE":
		return OrderByField_EFFECTIVE_CREATIVE, nil
	case "OrderByField_EFFECTIVE_CREATIVE_CHAIN_PREV_RATIO":
		return OrderByField_EFFECTIVE_CREATIVE_CHAIN_PREV_RATIO, nil
	case "OrderByField_EFFECTIVE_CREATIVE_CHAIN_RELATIVE_RATIO":
		return OrderByField_EFFECTIVE_CREATIVE_CHAIN_RELATIVE_RATIO, nil
	case "OrderByField_CONSUME_RATIO":
		return OrderByField_CONSUME_RATIO, nil
	case "OrderByField_CAMPAIGN_CREATIVE_TIME":
		return OrderByField_CAMPAIGN_CREATIVE_TIME, nil
	case "OrderByField_CAMPAIGN_DAILY_BUDGET":
		return OrderByField_CAMPAIGN_DAILY_BUDGET, nil
	case "OrderByField_CAMPAIGN_TOTAL_BUDGET":
		return OrderByField_CAMPAIGN_TOTAL_BUDGET, nil
	}
	return OrderByField(math.MinInt32 - 1), fmt.Errorf("not a valid OrderByField string")
}

type ExceptionCode int64

const (
	ExceptionCode_QUERY_PARAM_ERROR    ExceptionCode = 10001
	ExceptionCode_QUERY_DATABASE_ERROR ExceptionCode = 10002
	ExceptionCode_QUERY_SERVER_ERROR   ExceptionCode = 20000
)

func (p ExceptionCode) String() string {
	switch p {
	case ExceptionCode_QUERY_PARAM_ERROR:
		return "ExceptionCode_QUERY_PARAM_ERROR"
	case ExceptionCode_QUERY_DATABASE_ERROR:
		return "ExceptionCode_QUERY_DATABASE_ERROR"
	case ExceptionCode_QUERY_SERVER_ERROR:
		return "ExceptionCode_QUERY_SERVER_ERROR"
	}
	return "<UNSET>"
}

func ExceptionCodeFromString(s string) (ExceptionCode, error) {
	switch s {
	case "ExceptionCode_QUERY_PARAM_ERROR":
		return ExceptionCode_QUERY_PARAM_ERROR, nil
	case "ExceptionCode_QUERY_DATABASE_ERROR":
		return ExceptionCode_QUERY_DATABASE_ERROR, nil
	case "ExceptionCode_QUERY_SERVER_ERROR":
		return ExceptionCode_QUERY_SERVER_ERROR, nil
	}
	return ExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid ExceptionCode string")
}

type StatsConditionParam struct {
	StartTime        int64                  `thrift:"startTime,1" json:"startTime"`
	EndTime          int64                  `thrift:"endTime,2" json:"endTime"`
	CampaignIds      []int32                `thrift:"campaignIds,3" json:"campaignIds"`
	Pids             []int32                `thrift:"pids,4" json:"pids"`
	ChannelTypes     []int32                `thrift:"channelTypes,5" json:"channelTypes"`
	Consultant       int32                  `thrift:"consultant,6" json:"consultant"`
	CampaignName     string                 `thrift:"campaignName,7" json:"campaignName"`
	ProIds           []int32                `thrift:"proIds,8" json:"proIds"`
	SchIds           []int32                `thrift:"schIds,9" json:"schIds"`
	Status           dsp_types.DateStatus   `thrift:"status,10" json:"status"`
	CampaignType     dsp_types.CampaignType `thrift:"campaignType,11" json:"campaignType"`
	DepartmentId     int32                  `thrift:"departmentId,12" json:"departmentId"`
	Sale             int32                  `thrift:"sale,13" json:"sale"`
	CampaignNameOrId string                 `thrift:"campaignNameOrId,14" json:"campaignNameOrId"`
	TimeAccuracy     TimeAccuracyType       `thrift:"timeAccuracy,15" json:"timeAccuracy"`
	DepartmentIds    []int32                `thrift:"departmentIds,16" json:"departmentIds"`
	CampaignTypes    []int32                `thrift:"campaignTypes,17" json:"campaignTypes"`
}

func NewStatsConditionParam() *StatsConditionParam {
	return &StatsConditionParam{
		Status: math.MinInt32 - 1, // unset sentinal value

		CampaignType: math.MinInt32 - 1, // unset sentinal value

		TimeAccuracy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *StatsConditionParam) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *StatsConditionParam) IsSetCampaignType() bool {
	return int64(p.CampaignType) != math.MinInt32-1
}

func (p *StatsConditionParam) IsSetTimeAccuracy() bool {
	return int64(p.TimeAccuracy) != math.MinInt32-1
}

func (p *StatsConditionParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Pids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Pids = append(p.Pids, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChannelTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.ChannelTypes = append(p.ChannelTypes, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Consultant = v
	}
	return nil
}

func (p *StatsConditionParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CampaignName = v
	}
	return nil
}

func (p *StatsConditionParam) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.ProIds = append(p.ProIds, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SchIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.SchIds = append(p.SchIds, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = dsp_types.DateStatus(v)
	}
	return nil
}

func (p *StatsConditionParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CampaignType = dsp_types.CampaignType(v)
	}
	return nil
}

func (p *StatsConditionParam) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.DepartmentId = v
	}
	return nil
}

func (p *StatsConditionParam) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Sale = v
	}
	return nil
}

func (p *StatsConditionParam) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CampaignNameOrId = v
	}
	return nil
}

func (p *StatsConditionParam) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.TimeAccuracy = TimeAccuracyType(v)
	}
	return nil
}

func (p *StatsConditionParam) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DepartmentIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.DepartmentIds = append(p.DepartmentIds, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.CampaignTypes = append(p.CampaignTypes, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsConditionParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Pids != nil {
		if err := oprot.WriteFieldBegin("pids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:pids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Pids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Pids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:pids: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.ChannelTypes != nil {
		if err := oprot.WriteFieldBegin("channelTypes", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:channelTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ChannelTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChannelTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:channelTypes: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consultant", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:consultant: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Consultant)); err != nil {
		return fmt.Errorf("%T.consultant (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:consultant: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignName", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:campaignName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CampaignName)); err != nil {
		return fmt.Errorf("%T.campaignName (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:campaignName: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.ProIds != nil {
		if err := oprot.WriteFieldBegin("proIds", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:proIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ProIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:proIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.SchIds != nil {
		if err := oprot.WriteFieldBegin("schIds", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:schIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SchIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SchIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:schIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:status: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCampaignType() {
		if err := oprot.WriteFieldBegin("campaignType", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:campaignType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CampaignType)); err != nil {
			return fmt.Errorf("%T.campaignType (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:campaignType: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("departmentId", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:departmentId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DepartmentId)); err != nil {
		return fmt.Errorf("%T.departmentId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:departmentId: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sale", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:sale: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sale)); err != nil {
		return fmt.Errorf("%T.sale (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:sale: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignNameOrId", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:campaignNameOrId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CampaignNameOrId)); err != nil {
		return fmt.Errorf("%T.campaignNameOrId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:campaignNameOrId: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeAccuracy() {
		if err := oprot.WriteFieldBegin("timeAccuracy", thrift.I32, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:timeAccuracy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TimeAccuracy)); err != nil {
			return fmt.Errorf("%T.timeAccuracy (15) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:timeAccuracy: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField16(oprot thrift.TProtocol) (err error) {
	if p.DepartmentIds != nil {
		if err := oprot.WriteFieldBegin("departmentIds", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:departmentIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DepartmentIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DepartmentIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:departmentIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField17(oprot thrift.TProtocol) (err error) {
	if p.CampaignTypes != nil {
		if err := oprot.WriteFieldBegin("campaignTypes", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:campaignTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:campaignTypes: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsConditionParam(%+v)", *p)
}

type StatsGroupByParam struct {
	Dt          bool `thrift:"dt,1" json:"dt"`
	Hr          bool `thrift:"hr,2" json:"hr"`
	CampaignId  bool `thrift:"campaignId,3" json:"campaignId"`
	ChannelType bool `thrift:"channelType,4" json:"channelType"`
	Pid         bool `thrift:"pid,5" json:"pid"`
}

func NewStatsGroupByParam() *StatsGroupByParam {
	return &StatsGroupByParam{}
}

func (p *StatsGroupByParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *StatsGroupByParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *StatsGroupByParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ChannelType = v
	}
	return nil
}

func (p *StatsGroupByParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *StatsGroupByParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsGroupByParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:campaignId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:campaignId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelType", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:channelType: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ChannelType)); err != nil {
		return fmt.Errorf("%T.channelType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:channelType: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pid: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pid: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsGroupByParam(%+v)", *p)
}

type DspStatsData struct {
	Dt          int64 `thrift:"dt,1" json:"dt"`
	Hr          int32 `thrift:"hr,2" json:"hr"`
	CampaignId  int32 `thrift:"campaignId,3" json:"campaignId"`
	ChannelType int32 `thrift:"channelType,4" json:"channelType"`
	Pid         int32 `thrift:"pid,5" json:"pid"`
	Pt          int32 `thrift:"pt,6" json:"pt"`
	Ct          int32 `thrift:"ct,7" json:"ct"`
	// unused field # 8
	// unused field # 9
	Imp     int64 `thrift:"imp,10" json:"imp"`
	Clk     int64 `thrift:"clk,11" json:"clk"`
	Act     int64 `thrift:"act,12" json:"act"`
	Settled int64 `thrift:"settled,13" json:"settled"`
	AdPrice int64 `thrift:"adPrice,14" json:"adPrice"`
	// unused field # 15
	MediaPrice  int64 `thrift:"mediaPrice,16" json:"mediaPrice"`
	Consume     int64 `thrift:"consume,17" json:"consume"`
	Ctr         int32 `thrift:"ctr,18" json:"ctr"`
	Cvr         int32 `thrift:"cvr,19" json:"cvr"`
	GrossMargin int32 `thrift:"grossMargin,20" json:"grossMargin"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	CampaignDailyBudget                 int64 `thrift:"campaignDailyBudget,37" json:"campaignDailyBudget"`
	EffectiveCreative                   int32 `thrift:"effectiveCreative,38" json:"effectiveCreative"`
	ConsumeRatio                        int32 `thrift:"consumeRatio,39" json:"consumeRatio"`
	AdPriceChainPrevRatio               int32 `thrift:"adPriceChainPrevRatio,40" json:"adPriceChainPrevRatio"`
	AdPriceChainRelativeRatio           int32 `thrift:"adPriceChainRelativeRatio,41" json:"adPriceChainRelativeRatio"`
	ImpChainPrevRatio                   int32 `thrift:"impChainPrevRatio,42" json:"impChainPrevRatio"`
	ImpChainRelativeRatio               int32 `thrift:"impChainRelativeRatio,43" json:"impChainRelativeRatio"`
	ClkChainPrevRatio                   int32 `thrift:"clkChainPrevRatio,44" json:"clkChainPrevRatio"`
	ClkChainRelativeRatio               int32 `thrift:"clkChainRelativeRatio,45" json:"clkChainRelativeRatio"`
	ActChainPrevRatio                   int32 `thrift:"actChainPrevRatio,46" json:"actChainPrevRatio"`
	ActChainRelativeRatio               int32 `thrift:"actChainRelativeRatio,47" json:"actChainRelativeRatio"`
	EffectiveCreativeChainPrevRatio     int32 `thrift:"effectiveCreativeChainPrevRatio,48" json:"effectiveCreativeChainPrevRatio"`
	EffectiveCreativeChainRelativeRatio int32 `thrift:"effectiveCreativeChainRelativeRatio,49" json:"effectiveCreativeChainRelativeRatio"`
	SettleImp                           int64 `thrift:"settleImp,50" json:"settleImp"`
	SettleClk                           int64 `thrift:"settleClk,51" json:"settleClk"`
	SettleAct                           int64 `thrift:"settleAct,52" json:"settleAct"`
	SettleAdPrice                       int64 `thrift:"settleAdPrice,53" json:"settleAdPrice"`
	SettleMediaPrice                    int64 `thrift:"settleMediaPrice,54" json:"settleMediaPrice"`
	IsSendClick                         int64 `thrift:"isSendClick,55" json:"isSendClick"`
}

func NewDspStatsData() *DspStatsData {
	return &DspStatsData{}
}

func (p *DspStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I64 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I32 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I32 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.I32 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.I32 {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.I32 {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.I32 {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *DspStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *DspStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *DspStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ChannelType = v
	}
	return nil
}

func (p *DspStatsData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *DspStatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Pt = v
	}
	return nil
}

func (p *DspStatsData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ct = v
	}
	return nil
}

func (p *DspStatsData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Imp = v
	}
	return nil
}

func (p *DspStatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Clk = v
	}
	return nil
}

func (p *DspStatsData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Act = v
	}
	return nil
}

func (p *DspStatsData) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Settled = v
	}
	return nil
}

func (p *DspStatsData) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AdPrice = v
	}
	return nil
}

func (p *DspStatsData) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.MediaPrice = v
	}
	return nil
}

func (p *DspStatsData) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Consume = v
	}
	return nil
}

func (p *DspStatsData) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *DspStatsData) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Cvr = v
	}
	return nil
}

func (p *DspStatsData) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.GrossMargin = v
	}
	return nil
}

func (p *DspStatsData) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.CampaignDailyBudget = v
	}
	return nil
}

func (p *DspStatsData) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.EffectiveCreative = v
	}
	return nil
}

func (p *DspStatsData) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.ConsumeRatio = v
	}
	return nil
}

func (p *DspStatsData) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.AdPriceChainPrevRatio = v
	}
	return nil
}

func (p *DspStatsData) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.AdPriceChainRelativeRatio = v
	}
	return nil
}

func (p *DspStatsData) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.ImpChainPrevRatio = v
	}
	return nil
}

func (p *DspStatsData) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.ImpChainRelativeRatio = v
	}
	return nil
}

func (p *DspStatsData) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.ClkChainPrevRatio = v
	}
	return nil
}

func (p *DspStatsData) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.ClkChainRelativeRatio = v
	}
	return nil
}

func (p *DspStatsData) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.ActChainPrevRatio = v
	}
	return nil
}

func (p *DspStatsData) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.ActChainRelativeRatio = v
	}
	return nil
}

func (p *DspStatsData) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.EffectiveCreativeChainPrevRatio = v
	}
	return nil
}

func (p *DspStatsData) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.EffectiveCreativeChainRelativeRatio = v
	}
	return nil
}

func (p *DspStatsData) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.SettleImp = v
	}
	return nil
}

func (p *DspStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.SettleClk = v
	}
	return nil
}

func (p *DspStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.SettleAct = v
	}
	return nil
}

func (p *DspStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.SettleAdPrice = v
	}
	return nil
}

func (p *DspStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.SettleMediaPrice = v
	}
	return nil
}

func (p *DspStatsData) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.IsSendClick = v
	}
	return nil
}

func (p *DspStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:campaignId: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelType", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:channelType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
		return fmt.Errorf("%T.channelType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:channelType: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pid: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pt", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pt)); err != nil {
		return fmt.Errorf("%T.pt (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pt: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ct", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ct: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ct)); err != nil {
		return fmt.Errorf("%T.ct (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ct: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:imp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imp)); err != nil {
		return fmt.Errorf("%T.imp (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:imp: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:clk: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clk)); err != nil {
		return fmt.Errorf("%T.clk (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:clk: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:act: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Act)); err != nil {
		return fmt.Errorf("%T.act (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:act: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settled", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:settled: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Settled)); err != nil {
		return fmt.Errorf("%T.settled (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:settled: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPrice", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:adPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPrice)); err != nil {
		return fmt.Errorf("%T.adPrice (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:adPrice: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaPrice", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:mediaPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.mediaPrice (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:mediaPrice: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consume", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:consume: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consume)); err != nil {
		return fmt.Errorf("%T.consume (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:consume: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:ctr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:ctr: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cvr", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:cvr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cvr)); err != nil {
		return fmt.Errorf("%T.cvr (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:cvr: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("grossMargin", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:grossMargin: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GrossMargin)); err != nil {
		return fmt.Errorf("%T.grossMargin (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:grossMargin: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignDailyBudget", thrift.I64, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:campaignDailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CampaignDailyBudget)); err != nil {
		return fmt.Errorf("%T.campaignDailyBudget (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:campaignDailyBudget: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveCreative", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:effectiveCreative: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EffectiveCreative)); err != nil {
		return fmt.Errorf("%T.effectiveCreative (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:effectiveCreative: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumeRatio", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:consumeRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ConsumeRatio)); err != nil {
		return fmt.Errorf("%T.consumeRatio (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:consumeRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPriceChainPrevRatio", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:adPriceChainPrevRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPriceChainPrevRatio)); err != nil {
		return fmt.Errorf("%T.adPriceChainPrevRatio (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:adPriceChainPrevRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPriceChainRelativeRatio", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:adPriceChainRelativeRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPriceChainRelativeRatio)); err != nil {
		return fmt.Errorf("%T.adPriceChainRelativeRatio (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:adPriceChainRelativeRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impChainPrevRatio", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:impChainPrevRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImpChainPrevRatio)); err != nil {
		return fmt.Errorf("%T.impChainPrevRatio (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:impChainPrevRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impChainRelativeRatio", thrift.I32, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:impChainRelativeRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImpChainRelativeRatio)); err != nil {
		return fmt.Errorf("%T.impChainRelativeRatio (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:impChainRelativeRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkChainPrevRatio", thrift.I32, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:clkChainPrevRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkChainPrevRatio)); err != nil {
		return fmt.Errorf("%T.clkChainPrevRatio (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:clkChainPrevRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkChainRelativeRatio", thrift.I32, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:clkChainRelativeRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkChainRelativeRatio)); err != nil {
		return fmt.Errorf("%T.clkChainRelativeRatio (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:clkChainRelativeRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actChainPrevRatio", thrift.I32, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:actChainPrevRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActChainPrevRatio)); err != nil {
		return fmt.Errorf("%T.actChainPrevRatio (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:actChainPrevRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actChainRelativeRatio", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:actChainRelativeRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActChainRelativeRatio)); err != nil {
		return fmt.Errorf("%T.actChainRelativeRatio (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:actChainRelativeRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveCreativeChainPrevRatio", thrift.I32, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:effectiveCreativeChainPrevRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EffectiveCreativeChainPrevRatio)); err != nil {
		return fmt.Errorf("%T.effectiveCreativeChainPrevRatio (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:effectiveCreativeChainPrevRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveCreativeChainRelativeRatio", thrift.I32, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:effectiveCreativeChainRelativeRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EffectiveCreativeChainRelativeRatio)); err != nil {
		return fmt.Errorf("%T.effectiveCreativeChainRelativeRatio (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:effectiveCreativeChainRelativeRatio: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settleImp", thrift.I64, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:settleImp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleImp)); err != nil {
		return fmt.Errorf("%T.settleImp (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:settleImp: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settleClk", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:settleClk: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleClk)); err != nil {
		return fmt.Errorf("%T.settleClk (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:settleClk: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settleAct", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:settleAct: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleAct)); err != nil {
		return fmt.Errorf("%T.settleAct (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:settleAct: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settleAdPrice", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:settleAdPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleAdPrice)); err != nil {
		return fmt.Errorf("%T.settleAdPrice (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:settleAdPrice: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settleMediaPrice", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:settleMediaPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleMediaPrice)); err != nil {
		return fmt.Errorf("%T.settleMediaPrice (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:settleMediaPrice: %s", p, err)
	}
	return err
}

func (p *DspStatsData) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isSendClick", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:isSendClick: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.IsSendClick)); err != nil {
		return fmt.Errorf("%T.isSendClick (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:isSendClick: %s", p, err)
	}
	return err
}

func (p *DspStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspStatsData(%+v)", *p)
}

type StatsQueryResult struct {
	TotalCount int32           `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32           `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32           `thrift:"offset,3" json:"offset"`
	Limit      int32           `thrift:"limit,4" json:"limit"`
	Result     []*DspStatsData `thrift:"result,5" json:"result"`
}

func NewStatsQueryResult() *StatsQueryResult {
	return &StatsQueryResult{}
}

func (p *StatsQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *StatsQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *StatsQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *StatsQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *StatsQueryResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*DspStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewDspStatsData()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.Result = append(p.Result, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *StatsQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsQueryResult(%+v)", *p)
}

type StatsOrderByParam struct {
	OrderBy OrderByField `thrift:"orderBy,1" json:"orderBy"`
	IsAsc   bool         `thrift:"isAsc,2" json:"isAsc"`
}

func NewStatsOrderByParam() *StatsOrderByParam {
	return &StatsOrderByParam{
		OrderBy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *StatsOrderByParam) IsSetOrderBy() bool {
	return int64(p.OrderBy) != math.MinInt32-1
}

func (p *StatsOrderByParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsOrderByParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderBy = OrderByField(v)
	}
	return nil
}

func (p *StatsOrderByParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsAsc = v
	}
	return nil
}

func (p *StatsOrderByParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsOrderByParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsOrderByParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err := oprot.WriteFieldBegin("orderBy", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:orderBy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
			return fmt.Errorf("%T.orderBy (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:orderBy: %s", p, err)
		}
	}
	return err
}

func (p *StatsOrderByParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isAsc", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isAsc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAsc)); err != nil {
		return fmt.Errorf("%T.isAsc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isAsc: %s", p, err)
	}
	return err
}

func (p *StatsOrderByParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsOrderByParam(%+v)", *p)
}

type DspStatsServerException struct {
	Code    ExceptionCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewDspStatsServerException() *DspStatsServerException {
	return &DspStatsServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DspStatsServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DspStatsServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspStatsServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ExceptionCode(v)
	}
	return nil
}

func (p *DspStatsServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DspStatsServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspStatsServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspStatsServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DspStatsServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DspStatsServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspStatsServerException(%+v)", *p)
}
