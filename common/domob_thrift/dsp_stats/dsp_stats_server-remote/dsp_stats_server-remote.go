// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dsp_stats"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  StatsQueryResult queryStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, i32 offset, i32 limit)")
	fmt.Fprintln(os.<PERSON>der<PERSON>, "  StatsQueryResult queryStatsDataWithOrder(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryDailyStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  DspStatsData queryStatsTotalData(RequestHeader header, StatsConditionParam condition)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryStatsDataPlus(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, i32 offset, i32 limit, bool excludeEmpty)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryDailyStatsDataPlus(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, i32 offset, i32 limit, bool excludeEmpty)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dsp_stats.NewDspStatsServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryStatsData requires 5 args")
			flag.Usage()
		}
		arg34 := flag.Arg(1)
		mbTrans35 := thrift.NewTMemoryBufferLen(len(arg34))
		defer mbTrans35.Close()
		_, err36 := mbTrans35.WriteString(arg34)
		if err36 != nil {
			Usage()
			return
		}
		factory37 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt38 := factory37.GetProtocol(mbTrans35)
		argvalue0 := dsp_stats.NewRequestHeader()
		err39 := argvalue0.Read(jsProt38)
		if err39 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg40 := flag.Arg(2)
		mbTrans41 := thrift.NewTMemoryBufferLen(len(arg40))
		defer mbTrans41.Close()
		_, err42 := mbTrans41.WriteString(arg40)
		if err42 != nil {
			Usage()
			return
		}
		factory43 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt44 := factory43.GetProtocol(mbTrans41)
		argvalue1 := dsp_stats.NewStatsConditionParam()
		err45 := argvalue1.Read(jsProt44)
		if err45 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg46 := flag.Arg(3)
		mbTrans47 := thrift.NewTMemoryBufferLen(len(arg46))
		defer mbTrans47.Close()
		_, err48 := mbTrans47.WriteString(arg46)
		if err48 != nil {
			Usage()
			return
		}
		factory49 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt50 := factory49.GetProtocol(mbTrans47)
		argvalue2 := dsp_stats.NewStatsGroupByParam()
		err51 := argvalue2.Read(jsProt50)
		if err51 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err52 := (strconv.Atoi(flag.Arg(4)))
		if err52 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err53 := (strconv.Atoi(flag.Arg(5)))
		if err53 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryStatsDataWithOrder":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryStatsDataWithOrder requires 6 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := dsp_stats.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg60 := flag.Arg(2)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue1 := dsp_stats.NewStatsConditionParam()
		err65 := argvalue1.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg66 := flag.Arg(3)
		mbTrans67 := thrift.NewTMemoryBufferLen(len(arg66))
		defer mbTrans67.Close()
		_, err68 := mbTrans67.WriteString(arg66)
		if err68 != nil {
			Usage()
			return
		}
		factory69 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt70 := factory69.GetProtocol(mbTrans67)
		argvalue2 := dsp_stats.NewStatsGroupByParam()
		err71 := argvalue2.Read(jsProt70)
		if err71 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg72 := flag.Arg(4)
		mbTrans73 := thrift.NewTMemoryBufferLen(len(arg72))
		defer mbTrans73.Close()
		_, err74 := mbTrans73.WriteString(arg72)
		if err74 != nil {
			Usage()
			return
		}
		factory75 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt76 := factory75.GetProtocol(mbTrans73)
		argvalue3 := dsp_stats.NewStatsOrderByParam()
		err77 := argvalue3.Read(jsProt76)
		if err77 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err78 := (strconv.Atoi(flag.Arg(5)))
		if err78 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err79 := (strconv.Atoi(flag.Arg(6)))
		if err79 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.QueryStatsDataWithOrder(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "queryDailyStatsData":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryDailyStatsData requires 6 args")
			flag.Usage()
		}
		arg80 := flag.Arg(1)
		mbTrans81 := thrift.NewTMemoryBufferLen(len(arg80))
		defer mbTrans81.Close()
		_, err82 := mbTrans81.WriteString(arg80)
		if err82 != nil {
			Usage()
			return
		}
		factory83 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt84 := factory83.GetProtocol(mbTrans81)
		argvalue0 := dsp_stats.NewRequestHeader()
		err85 := argvalue0.Read(jsProt84)
		if err85 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg86 := flag.Arg(2)
		mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
		defer mbTrans87.Close()
		_, err88 := mbTrans87.WriteString(arg86)
		if err88 != nil {
			Usage()
			return
		}
		factory89 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt90 := factory89.GetProtocol(mbTrans87)
		argvalue1 := dsp_stats.NewStatsConditionParam()
		err91 := argvalue1.Read(jsProt90)
		if err91 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg92 := flag.Arg(3)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		argvalue2 := dsp_stats.NewStatsGroupByParam()
		err97 := argvalue2.Read(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg98 := flag.Arg(4)
		mbTrans99 := thrift.NewTMemoryBufferLen(len(arg98))
		defer mbTrans99.Close()
		_, err100 := mbTrans99.WriteString(arg98)
		if err100 != nil {
			Usage()
			return
		}
		factory101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt102 := factory101.GetProtocol(mbTrans99)
		argvalue3 := dsp_stats.NewStatsOrderByParam()
		err103 := argvalue3.Read(jsProt102)
		if err103 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err104 := (strconv.Atoi(flag.Arg(5)))
		if err104 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err105 := (strconv.Atoi(flag.Arg(6)))
		if err105 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.QueryDailyStatsData(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "queryStatsTotalData":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "QueryStatsTotalData requires 2 args")
			flag.Usage()
		}
		arg106 := flag.Arg(1)
		mbTrans107 := thrift.NewTMemoryBufferLen(len(arg106))
		defer mbTrans107.Close()
		_, err108 := mbTrans107.WriteString(arg106)
		if err108 != nil {
			Usage()
			return
		}
		factory109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt110 := factory109.GetProtocol(mbTrans107)
		argvalue0 := dsp_stats.NewRequestHeader()
		err111 := argvalue0.Read(jsProt110)
		if err111 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg112 := flag.Arg(2)
		mbTrans113 := thrift.NewTMemoryBufferLen(len(arg112))
		defer mbTrans113.Close()
		_, err114 := mbTrans113.WriteString(arg112)
		if err114 != nil {
			Usage()
			return
		}
		factory115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt116 := factory115.GetProtocol(mbTrans113)
		argvalue1 := dsp_stats.NewStatsConditionParam()
		err117 := argvalue1.Read(jsProt116)
		if err117 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.QueryStatsTotalData(value0, value1))
		fmt.Print("\n")
		break
	case "queryStatsDataPlus":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryStatsDataPlus requires 7 args")
			flag.Usage()
		}
		arg118 := flag.Arg(1)
		mbTrans119 := thrift.NewTMemoryBufferLen(len(arg118))
		defer mbTrans119.Close()
		_, err120 := mbTrans119.WriteString(arg118)
		if err120 != nil {
			Usage()
			return
		}
		factory121 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt122 := factory121.GetProtocol(mbTrans119)
		argvalue0 := dsp_stats.NewRequestHeader()
		err123 := argvalue0.Read(jsProt122)
		if err123 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg124 := flag.Arg(2)
		mbTrans125 := thrift.NewTMemoryBufferLen(len(arg124))
		defer mbTrans125.Close()
		_, err126 := mbTrans125.WriteString(arg124)
		if err126 != nil {
			Usage()
			return
		}
		factory127 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt128 := factory127.GetProtocol(mbTrans125)
		argvalue1 := dsp_stats.NewStatsConditionParam()
		err129 := argvalue1.Read(jsProt128)
		if err129 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg130 := flag.Arg(3)
		mbTrans131 := thrift.NewTMemoryBufferLen(len(arg130))
		defer mbTrans131.Close()
		_, err132 := mbTrans131.WriteString(arg130)
		if err132 != nil {
			Usage()
			return
		}
		factory133 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt134 := factory133.GetProtocol(mbTrans131)
		argvalue2 := dsp_stats.NewStatsGroupByParam()
		err135 := argvalue2.Read(jsProt134)
		if err135 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg136 := flag.Arg(4)
		mbTrans137 := thrift.NewTMemoryBufferLen(len(arg136))
		defer mbTrans137.Close()
		_, err138 := mbTrans137.WriteString(arg136)
		if err138 != nil {
			Usage()
			return
		}
		factory139 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt140 := factory139.GetProtocol(mbTrans137)
		argvalue3 := dsp_stats.NewStatsOrderByParam()
		err141 := argvalue3.Read(jsProt140)
		if err141 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err142 := (strconv.Atoi(flag.Arg(5)))
		if err142 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err143 := (strconv.Atoi(flag.Arg(6)))
		if err143 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.QueryStatsDataPlus(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "queryDailyStatsDataPlus":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryDailyStatsDataPlus requires 7 args")
			flag.Usage()
		}
		arg145 := flag.Arg(1)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		argvalue0 := dsp_stats.NewRequestHeader()
		err150 := argvalue0.Read(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg151 := flag.Arg(2)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		argvalue1 := dsp_stats.NewStatsConditionParam()
		err156 := argvalue1.Read(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg157 := flag.Arg(3)
		mbTrans158 := thrift.NewTMemoryBufferLen(len(arg157))
		defer mbTrans158.Close()
		_, err159 := mbTrans158.WriteString(arg157)
		if err159 != nil {
			Usage()
			return
		}
		factory160 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt161 := factory160.GetProtocol(mbTrans158)
		argvalue2 := dsp_stats.NewStatsGroupByParam()
		err162 := argvalue2.Read(jsProt161)
		if err162 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg163 := flag.Arg(4)
		mbTrans164 := thrift.NewTMemoryBufferLen(len(arg163))
		defer mbTrans164.Close()
		_, err165 := mbTrans164.WriteString(arg163)
		if err165 != nil {
			Usage()
			return
		}
		factory166 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt167 := factory166.GetProtocol(mbTrans164)
		argvalue3 := dsp_stats.NewStatsOrderByParam()
		err168 := argvalue3.Read(jsProt167)
		if err168 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err169 := (strconv.Atoi(flag.Arg(5)))
		if err169 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err170 := (strconv.Atoi(flag.Arg(6)))
		if err170 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.QueryDailyStatsDataPlus(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
