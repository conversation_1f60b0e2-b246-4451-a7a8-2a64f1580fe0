// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package quake

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type QuakeService interface {
	dm303.DomobService
	//发送报警的服务
	//
	//两个函数是异步的，在接到请求后，先将请求放到本地队列中就返回，实际的发送
	//时间，可能有延后，取决于系统的负载情况
	//
	//请注意各个字段的最大长度限制，超过这个限制将发送失败

	// 发送短信报警，可以指定多个接收人
	//
	// Parameters:
	//  - Header
	//  - Sender: 发送报警的服务，如nagios，domino，sahara等
	// 最长64个字节
	//  - ServiceName: 出现错误的服务，对于nagios是服务名，对于sahara是数据流名，对于domino是任务名
	// 最长64个字节
	//  - Numbers: 手机号码列表
	//  - Content: 短信内容，不宜过长，最长1024个字节
	//  - Channel: 发送通道，默认是SC_DEFAULT，
	// 通常你不需要选择，让系统决定就好。
	// 除非你知道这个参数的效果
	SmsAlarm(header *common.RequestHeader, sender string, service_name string, numbers []string, content string, channel SmsChannel) (r bool, e *QuakeException, err error)
	// 发送email报警，可以指定多个接收人
	//
	// Parameters:
	//  - Header
	//  - Sender: 发送报警的服务，如nagios，domino，sahara等
	// 最长64个字节
	//  - ServiceName: 出现错误的服务，对于nagios是服务名，对于sahara是数据流名，对于domino是任务名
	// 最长64个字节
	//  - Emails
	//  - Subject: 最长256字节
	//  - Content: 最大64K
	EmailAlarm(header *common.RequestHeader, sender string, service_name string, emails []string, subject string, content string) (r bool, e *QuakeException, err error)
}

//发送报警的服务
//
//两个函数是异步的，在接到请求后，先将请求放到本地队列中就返回，实际的发送
//时间，可能有延后，取决于系统的负载情况
//
//请注意各个字段的最大长度限制，超过这个限制将发送失败
type QuakeServiceClient struct {
	*dm303.DomobServiceClient
}

func NewQuakeServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *QuakeServiceClient {
	return &QuakeServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewQuakeServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *QuakeServiceClient {
	return &QuakeServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 发送短信报警，可以指定多个接收人
//
// Parameters:
//  - Header
//  - Sender: 发送报警的服务，如nagios，domino，sahara等
// 最长64个字节
//  - ServiceName: 出现错误的服务，对于nagios是服务名，对于sahara是数据流名，对于domino是任务名
// 最长64个字节
//  - Numbers: 手机号码列表
//  - Content: 短信内容，不宜过长，最长1024个字节
//  - Channel: 发送通道，默认是SC_DEFAULT，
// 通常你不需要选择，让系统决定就好。
// 除非你知道这个参数的效果
func (p *QuakeServiceClient) SmsAlarm(header *common.RequestHeader, sender string, service_name string, numbers []string, content string, channel SmsChannel) (r bool, e *QuakeException, err error) {
	if err = p.sendSmsAlarm(header, sender, service_name, numbers, content, channel); err != nil {
		return
	}
	return p.recvSmsAlarm()
}

func (p *QuakeServiceClient) sendSmsAlarm(header *common.RequestHeader, sender string, service_name string, numbers []string, content string, channel SmsChannel) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("smsAlarm", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewSmsAlarmArgs()
	args0.Header = header
	args0.Sender = sender
	args0.ServiceName = service_name
	args0.Numbers = numbers
	args0.Content = content
	args0.Channel = channel
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QuakeServiceClient) recvSmsAlarm() (value bool, e *QuakeException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewSmsAlarmResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 发送email报警，可以指定多个接收人
//
// Parameters:
//  - Header
//  - Sender: 发送报警的服务，如nagios，domino，sahara等
// 最长64个字节
//  - ServiceName: 出现错误的服务，对于nagios是服务名，对于sahara是数据流名，对于domino是任务名
// 最长64个字节
//  - Emails
//  - Subject: 最长256字节
//  - Content: 最大64K
func (p *QuakeServiceClient) EmailAlarm(header *common.RequestHeader, sender string, service_name string, emails []string, subject string, content string) (r bool, e *QuakeException, err error) {
	if err = p.sendEmailAlarm(header, sender, service_name, emails, subject, content); err != nil {
		return
	}
	return p.recvEmailAlarm()
}

func (p *QuakeServiceClient) sendEmailAlarm(header *common.RequestHeader, sender string, service_name string, emails []string, subject string, content string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("emailAlarm", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewEmailAlarmArgs()
	args4.Header = header
	args4.Sender = sender
	args4.ServiceName = service_name
	args4.Emails = emails
	args4.Subject = subject
	args4.Content = content
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QuakeServiceClient) recvEmailAlarm() (value bool, e *QuakeException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewEmailAlarmResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

type QuakeServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewQuakeServiceProcessor(handler QuakeService) *QuakeServiceProcessor {
	self8 := &QuakeServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self8.AddToProcessorMap("smsAlarm", &quakeServiceProcessorSmsAlarm{handler: handler})
	self8.AddToProcessorMap("emailAlarm", &quakeServiceProcessorEmailAlarm{handler: handler})
	return self8
}

type quakeServiceProcessorSmsAlarm struct {
	handler QuakeService
}

func (p *quakeServiceProcessorSmsAlarm) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSmsAlarmArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("smsAlarm", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSmsAlarmResult()
	if result.Success, result.E, err = p.handler.SmsAlarm(args.Header, args.Sender, args.ServiceName, args.Numbers, args.Content, args.Channel); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing smsAlarm: "+err.Error())
		oprot.WriteMessageBegin("smsAlarm", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("smsAlarm", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type quakeServiceProcessorEmailAlarm struct {
	handler QuakeService
}

func (p *quakeServiceProcessorEmailAlarm) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEmailAlarmArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("emailAlarm", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEmailAlarmResult()
	if result.Success, result.E, err = p.handler.EmailAlarm(args.Header, args.Sender, args.ServiceName, args.Emails, args.Subject, args.Content); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing emailAlarm: "+err.Error())
		oprot.WriteMessageBegin("emailAlarm", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("emailAlarm", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type SmsAlarmArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Sender      string                `thrift:"sender,2" json:"sender"`
	ServiceName string                `thrift:"service_name,3" json:"service_name"`
	Numbers     []string              `thrift:"numbers,4" json:"numbers"`
	Content     string                `thrift:"content,5" json:"content"`
	Channel     SmsChannel            `thrift:"channel,6" json:"channel"`
}

func NewSmsAlarmArgs() *SmsAlarmArgs {
	return &SmsAlarmArgs{
		Channel: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SmsAlarmArgs) IsSetChannel() bool {
	return int64(p.Channel) != math.MinInt32-1
}

func (p *SmsAlarmArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SmsAlarmArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SmsAlarmArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sender = v
	}
	return nil
}

func (p *SmsAlarmArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *SmsAlarmArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Numbers = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.Numbers = append(p.Numbers, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SmsAlarmArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *SmsAlarmArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Channel = SmsChannel(v)
	}
	return nil
}

func (p *SmsAlarmArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("smsAlarm_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SmsAlarmArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SmsAlarmArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sender", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sender: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sender)); err != nil {
		return fmt.Errorf("%T.sender (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sender: %s", p, err)
	}
	return err
}

func (p *SmsAlarmArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("service_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:service_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServiceName)); err != nil {
		return fmt.Errorf("%T.service_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:service_name: %s", p, err)
	}
	return err
}

func (p *SmsAlarmArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Numbers != nil {
		if err := oprot.WriteFieldBegin("numbers", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:numbers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Numbers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Numbers {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:numbers: %s", p, err)
		}
	}
	return err
}

func (p *SmsAlarmArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:content: %s", p, err)
	}
	return err
}

func (p *SmsAlarmArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannel() {
		if err := oprot.WriteFieldBegin("channel", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:channel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Channel)); err != nil {
			return fmt.Errorf("%T.channel (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:channel: %s", p, err)
		}
	}
	return err
}

func (p *SmsAlarmArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SmsAlarmArgs(%+v)", *p)
}

type SmsAlarmResult struct {
	Success bool            `thrift:"success,0" json:"success"`
	E       *QuakeException `thrift:"e,1" json:"e"`
}

func NewSmsAlarmResult() *SmsAlarmResult {
	return &SmsAlarmResult{}
}

func (p *SmsAlarmResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SmsAlarmResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *SmsAlarmResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewQuakeException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SmsAlarmResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("smsAlarm_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SmsAlarmResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *SmsAlarmResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SmsAlarmResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SmsAlarmResult(%+v)", *p)
}

type EmailAlarmArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Sender      string                `thrift:"sender,2" json:"sender"`
	ServiceName string                `thrift:"service_name,3" json:"service_name"`
	Emails      []string              `thrift:"emails,4" json:"emails"`
	Subject     string                `thrift:"subject,5" json:"subject"`
	Content     string                `thrift:"content,6" json:"content"`
}

func NewEmailAlarmArgs() *EmailAlarmArgs {
	return &EmailAlarmArgs{}
}

func (p *EmailAlarmArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EmailAlarmArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EmailAlarmArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sender = v
	}
	return nil
}

func (p *EmailAlarmArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *EmailAlarmArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Emails = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.Emails = append(p.Emails, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *EmailAlarmArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Subject = v
	}
	return nil
}

func (p *EmailAlarmArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *EmailAlarmArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("emailAlarm_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EmailAlarmArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EmailAlarmArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sender", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sender: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sender)); err != nil {
		return fmt.Errorf("%T.sender (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sender: %s", p, err)
	}
	return err
}

func (p *EmailAlarmArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("service_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:service_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServiceName)); err != nil {
		return fmt.Errorf("%T.service_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:service_name: %s", p, err)
	}
	return err
}

func (p *EmailAlarmArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Emails != nil {
		if err := oprot.WriteFieldBegin("emails", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:emails: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Emails)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Emails {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:emails: %s", p, err)
		}
	}
	return err
}

func (p *EmailAlarmArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subject", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:subject: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Subject)); err != nil {
		return fmt.Errorf("%T.subject (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:subject: %s", p, err)
	}
	return err
}

func (p *EmailAlarmArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:content: %s", p, err)
	}
	return err
}

func (p *EmailAlarmArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EmailAlarmArgs(%+v)", *p)
}

type EmailAlarmResult struct {
	Success bool            `thrift:"success,0" json:"success"`
	E       *QuakeException `thrift:"e,1" json:"e"`
}

func NewEmailAlarmResult() *EmailAlarmResult {
	return &EmailAlarmResult{}
}

func (p *EmailAlarmResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EmailAlarmResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *EmailAlarmResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewQuakeException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EmailAlarmResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("emailAlarm_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EmailAlarmResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *EmailAlarmResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EmailAlarmResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EmailAlarmResult(%+v)", *p)
}
