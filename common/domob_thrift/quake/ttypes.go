// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package quake

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type SmsChannel int64

const (
	SmsChannel_SC_DEFAULT SmsChannel = 0
	SmsChannel_SC_SMS_ISP SmsChannel = 1
	SmsChannel_SC_FETION  SmsChannel = 2
)

func (p SmsChannel) String() string {
	switch p {
	case SmsChannel_SC_DEFAULT:
		return "SmsChannel_SC_DEFAULT"
	case SmsChannel_SC_SMS_ISP:
		return "SmsChannel_SC_SMS_ISP"
	case SmsChannel_SC_FETION:
		return "SmsChannel_SC_FETION"
	}
	return "<UNSET>"
}

func SmsChannelFromString(s string) (SmsChannel, error) {
	switch s {
	case "SmsChannel_SC_DEFAULT":
		return SmsChannel_SC_DEFAULT, nil
	case "SmsChannel_SC_SMS_ISP":
		return SmsChannel_SC_SMS_ISP, nil
	case "SmsChannel_SC_FETION":
		return SmsChannel_SC_FETION, nil
	}
	return SmsChannel(math.MinInt32 - 1), fmt.Errorf("not a valid SmsChannel string")
}

type RequestHeader *common.RequestHeader

type QuakeException struct {
	Message string `thrift:"message,1" json:"message"`
}

func NewQuakeException() *QuakeException {
	return &QuakeException{}
}

func (p *QuakeException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QuakeException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *QuakeException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QuakeException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QuakeException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:message: %s", p, err)
	}
	return err
}

func (p *QuakeException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QuakeException(%+v)", *p)
}
