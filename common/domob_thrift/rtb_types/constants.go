// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

const EX_MOCK = 100
const EX_AMAX = 101
const EX_YOUKU = 102
const EX_GOOGLE = 103
const EX_TANX = 104
const EX_BES = 105
const EX_TOUTIAO = 106
const EX_GDT = 107
const EX_GDT_QC = 1107
const EX_SOHU = 108
const EX_WAX = 109
const EX_BAIDU_CBG = 110
const EX_DVX = 111
const EX_ZPLAY = 112
const EX_IQIYI = 113
const EX_MOMO = 114
const EX_PPTV = 115
const EX_TENCENT = 116
const EX_TENCENT_PMP = 117
const EX_SAX = 118
const EX_PCAUTO = 119
const EX_AUTOHOME = 120
const EX_MAX = 121
const EX_LETV = 122
const EX_YDZX = 123
const EX_YICHE = 124
const EX_SUNING = 125
const EX_XIAOMI = 126
const EX_BAOFENG = 127
const EX_ZYKQ = 128
const EX_YOMOB = 129
const EX_BAIDU_ADX = 130
const EX_ADXING = 131
const EX_KUAISHOU = 132
const EX_ZUIYOU = 133
const EX_BILIBILI = 134
const EX_QUTOUTIAO = 135
const EX_OPPO = 136
const EX_DMADX = 137
const EX_VIVO = 138
const EX_UC = 139
const EX_MEISHU = 140
const EX_MOJI = 141
const EX_XUNFEI = 142
const EX_MANGGUOTV = 143
const EX_SIGMOB = 144
const EX_DM_BID_ADX = 145
const EX_FENGDU = 146
const EX_2345 = 147
const EX_WANGYI = 148
const EX_QINGTING = 149
const EX_SMAATO = 201
const EX_INMOBI = 202
const EX_MOPUB = 203
const EX_NEXAGE = 204
const EX_LEADBOLT = 205
const EX_VUNGLE = 206
const EX_MINTEGRAL = 207
const EX_DM_SAILING_ADX = 208

func init() {
}
