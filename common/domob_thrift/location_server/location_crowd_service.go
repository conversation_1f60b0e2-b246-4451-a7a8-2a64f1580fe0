// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package location_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__

type LocationCrowdService interface {
	// 通过关键词查询获得匹配的POI点列表以及在其附近经常出现的人群的单日PV,CLK,UV
	//
	//
	// Parameters:
	//  - Req
	SearchLocation(req *QueryReq) (r *LocationQueryRes, se *SearchException, err error)
	// 将指定的的POI点列表附近经常出现的人群打上Tag用于之后投放
	//
	//
	// Parameters:
	//  - Req
	AddLocationTag(req *AddTagReq) (r ResCode, se *SearchException, err error)
}

type LocationCrowdServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewLocationCrowdServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *LocationCrowdServiceClient {
	return &LocationCrowdServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewLocationCrowdServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *LocationCrowdServiceClient {
	return &LocationCrowdServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 通过关键词查询获得匹配的POI点列表以及在其附近经常出现的人群的单日PV,CLK,UV
//
//
// Parameters:
//  - Req
func (p *LocationCrowdServiceClient) SearchLocation(req *QueryReq) (r *LocationQueryRes, se *SearchException, err error) {
	if err = p.sendSearchLocation(req); err != nil {
		return
	}
	return p.recvSearchLocation()
}

func (p *LocationCrowdServiceClient) sendSearchLocation(req *QueryReq) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchLocation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args3 := NewSearchLocationArgs()
	args3.Req = req
	if err = args3.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *LocationCrowdServiceClient) recvSearchLocation() (value *LocationQueryRes, se *SearchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error5 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error6 error
		error6, err = error5.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error6
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result4 := NewSearchLocationResult()
	if err = result4.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result4.Success
	if result4.Se != nil {
		se = result4.Se
	}
	return
}

// 将指定的的POI点列表附近经常出现的人群打上Tag用于之后投放
//
//
// Parameters:
//  - Req
func (p *LocationCrowdServiceClient) AddLocationTag(req *AddTagReq) (r ResCode, se *SearchException, err error) {
	if err = p.sendAddLocationTag(req); err != nil {
		return
	}
	return p.recvAddLocationTag()
}

func (p *LocationCrowdServiceClient) sendAddLocationTag(req *AddTagReq) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addLocationTag", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args7 := NewAddLocationTagArgs()
	args7.Req = req
	if err = args7.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *LocationCrowdServiceClient) recvAddLocationTag() (value ResCode, se *SearchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error9 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error10 error
		error10, err = error9.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error10
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result8 := NewAddLocationTagResult()
	if err = result8.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result8.Success
	if result8.Se != nil {
		se = result8.Se
	}
	return
}

type LocationCrowdServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      LocationCrowdService
}

func (p *LocationCrowdServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *LocationCrowdServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *LocationCrowdServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewLocationCrowdServiceProcessor(handler LocationCrowdService) *LocationCrowdServiceProcessor {

	self11 := &LocationCrowdServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self11.processorMap["searchLocation"] = &locationCrowdServiceProcessorSearchLocation{handler: handler}
	self11.processorMap["addLocationTag"] = &locationCrowdServiceProcessorAddLocationTag{handler: handler}
	return self11
}

func (p *LocationCrowdServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x12 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x12.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x12

}

type locationCrowdServiceProcessorSearchLocation struct {
	handler LocationCrowdService
}

func (p *locationCrowdServiceProcessorSearchLocation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchLocationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchLocation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchLocationResult()
	if result.Success, result.Se, err = p.handler.SearchLocation(args.Req); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchLocation: "+err.Error())
		oprot.WriteMessageBegin("searchLocation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchLocation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type locationCrowdServiceProcessorAddLocationTag struct {
	handler LocationCrowdService
}

func (p *locationCrowdServiceProcessorAddLocationTag) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddLocationTagArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addLocationTag", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddLocationTagResult()
	if result.Success, result.Se, err = p.handler.AddLocationTag(args.Req); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addLocationTag: "+err.Error())
		oprot.WriteMessageBegin("addLocationTag", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addLocationTag", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type SearchLocationArgs struct {
	Req *QueryReq `thrift:"req,1" json:"req"`
}

func NewSearchLocationArgs() *SearchLocationArgs {
	return &SearchLocationArgs{}
}

func (p *SearchLocationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchLocationArgs) readField1(iprot thrift.TProtocol) error {
	p.Req = NewQueryReq()
	if err := p.Req.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Req)
	}
	return nil
}

func (p *SearchLocationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchLocation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchLocationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Req != nil {
		if err := oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:req: %s", p, err)
		}
		if err := p.Req.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Req)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:req: %s", p, err)
		}
	}
	return err
}

func (p *SearchLocationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchLocationArgs(%+v)", *p)
}

type SearchLocationResult struct {
	Success *LocationQueryRes `thrift:"success,0" json:"success"`
	Se      *SearchException  `thrift:"se,1" json:"se"`
}

func NewSearchLocationResult() *SearchLocationResult {
	return &SearchLocationResult{}
}

func (p *SearchLocationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchLocationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewLocationQueryRes()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchLocationResult) readField1(iprot thrift.TProtocol) error {
	p.Se = NewSearchException()
	if err := p.Se.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Se)
	}
	return nil
}

func (p *SearchLocationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchLocation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Se != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchLocationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchLocationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Se != nil {
		if err := oprot.WriteFieldBegin("se", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:se: %s", p, err)
		}
		if err := p.Se.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Se)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:se: %s", p, err)
		}
	}
	return err
}

func (p *SearchLocationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchLocationResult(%+v)", *p)
}

type AddLocationTagArgs struct {
	Req *AddTagReq `thrift:"req,1" json:"req"`
}

func NewAddLocationTagArgs() *AddLocationTagArgs {
	return &AddLocationTagArgs{}
}

func (p *AddLocationTagArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddLocationTagArgs) readField1(iprot thrift.TProtocol) error {
	p.Req = NewAddTagReq()
	if err := p.Req.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Req)
	}
	return nil
}

func (p *AddLocationTagArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addLocationTag_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddLocationTagArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Req != nil {
		if err := oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:req: %s", p, err)
		}
		if err := p.Req.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Req)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:req: %s", p, err)
		}
	}
	return err
}

func (p *AddLocationTagArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddLocationTagArgs(%+v)", *p)
}

type AddLocationTagResult struct {
	Success ResCode          `thrift:"success,0" json:"success"`
	Se      *SearchException `thrift:"se,1" json:"se"`
}

func NewAddLocationTagResult() *AddLocationTagResult {
	return &AddLocationTagResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddLocationTagResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddLocationTagResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddLocationTagResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = ResCode(v)
	}
	return nil
}

func (p *AddLocationTagResult) readField1(iprot thrift.TProtocol) error {
	p.Se = NewSearchException()
	if err := p.Se.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Se)
	}
	return nil
}

func (p *AddLocationTagResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addLocationTag_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Se != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddLocationTagResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddLocationTagResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Se != nil {
		if err := oprot.WriteFieldBegin("se", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:se: %s", p, err)
		}
		if err := p.Se.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Se)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:se: %s", p, err)
		}
	}
	return err
}

func (p *AddLocationTagResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddLocationTagResult(%+v)", *p)
}
