// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package location_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type ResCode int64

const (
	ResCode_REQ_OK                 ResCode = 200
	ResCode_REQ_HEAD_ERROR         ResCode = 400
	ResCode_REQ_QUERY_NULL_ERROR   ResCode = 401
	ResCode_REQ_PRI_CATEGORY_ERROR ResCode = 402
	ResCode_REQ_INT_CATEGORY_ERROR ResCode = 403
	ResCode_REQ_ADV_CATEGORY_ERROR ResCode = 404
	ResCode_REQ_MATCH_TYPE_ERROR   ResCode = 405
	ResCode_REQ_TAG_POI_NULL_ERROR ResCode = 406
	ResCode_REQ_ADD_TAG_ERROR      ResCode = 500
)

func (p ResCode) String() string {
	switch p {
	case ResCode_REQ_OK:
		return "ResCode_REQ_OK"
	case ResCode_REQ_HEAD_ERROR:
		return "ResCode_REQ_HEAD_ERROR"
	case ResCode_REQ_QUERY_NULL_ERROR:
		return "ResCode_REQ_QUERY_NULL_ERROR"
	case ResCode_REQ_PRI_CATEGORY_ERROR:
		return "ResCode_REQ_PRI_CATEGORY_ERROR"
	case ResCode_REQ_INT_CATEGORY_ERROR:
		return "ResCode_REQ_INT_CATEGORY_ERROR"
	case ResCode_REQ_ADV_CATEGORY_ERROR:
		return "ResCode_REQ_ADV_CATEGORY_ERROR"
	case ResCode_REQ_MATCH_TYPE_ERROR:
		return "ResCode_REQ_MATCH_TYPE_ERROR"
	case ResCode_REQ_TAG_POI_NULL_ERROR:
		return "ResCode_REQ_TAG_POI_NULL_ERROR"
	case ResCode_REQ_ADD_TAG_ERROR:
		return "ResCode_REQ_ADD_TAG_ERROR"
	}
	return "<UNSET>"
}

func ResCodeFromString(s string) (ResCode, error) {
	switch s {
	case "ResCode_REQ_OK":
		return ResCode_REQ_OK, nil
	case "ResCode_REQ_HEAD_ERROR":
		return ResCode_REQ_HEAD_ERROR, nil
	case "ResCode_REQ_QUERY_NULL_ERROR":
		return ResCode_REQ_QUERY_NULL_ERROR, nil
	case "ResCode_REQ_PRI_CATEGORY_ERROR":
		return ResCode_REQ_PRI_CATEGORY_ERROR, nil
	case "ResCode_REQ_INT_CATEGORY_ERROR":
		return ResCode_REQ_INT_CATEGORY_ERROR, nil
	case "ResCode_REQ_ADV_CATEGORY_ERROR":
		return ResCode_REQ_ADV_CATEGORY_ERROR, nil
	case "ResCode_REQ_MATCH_TYPE_ERROR":
		return ResCode_REQ_MATCH_TYPE_ERROR, nil
	case "ResCode_REQ_TAG_POI_NULL_ERROR":
		return ResCode_REQ_TAG_POI_NULL_ERROR, nil
	case "ResCode_REQ_ADD_TAG_ERROR":
		return ResCode_REQ_ADD_TAG_ERROR, nil
	}
	return ResCode(math.MinInt32 - 1), fmt.Errorf("not a valid ResCode string")
}

type MatchType int64

const (
	MatchType_ROUGH_MATCH    MatchType = 1
	MatchType_ACCURATE_MATCH MatchType = 2
)

func (p MatchType) String() string {
	switch p {
	case MatchType_ROUGH_MATCH:
		return "MatchType_ROUGH_MATCH"
	case MatchType_ACCURATE_MATCH:
		return "MatchType_ACCURATE_MATCH"
	}
	return "<UNSET>"
}

func MatchTypeFromString(s string) (MatchType, error) {
	switch s {
	case "MatchType_ROUGH_MATCH":
		return MatchType_ROUGH_MATCH, nil
	case "MatchType_ACCURATE_MATCH":
		return MatchType_ACCURATE_MATCH, nil
	}
	return MatchType(math.MinInt32 - 1), fmt.Errorf("not a valid MatchType string")
}

type SortType int64

const (
	SortType_ST_DEFAULT  SortType = 0
	SortType_ST_UV_ASC   SortType = 1
	SortType_ST_UV_DESC  SortType = 2
	SortType_ST_PV_ASC   SortType = 3
	SortType_ST_PV_DESC  SortType = 4
	SortType_ST_CLK_ASC  SortType = 5
	SortType_ST_CLK_DESC SortType = 6
)

func (p SortType) String() string {
	switch p {
	case SortType_ST_DEFAULT:
		return "SortType_ST_DEFAULT"
	case SortType_ST_UV_ASC:
		return "SortType_ST_UV_ASC"
	case SortType_ST_UV_DESC:
		return "SortType_ST_UV_DESC"
	case SortType_ST_PV_ASC:
		return "SortType_ST_PV_ASC"
	case SortType_ST_PV_DESC:
		return "SortType_ST_PV_DESC"
	case SortType_ST_CLK_ASC:
		return "SortType_ST_CLK_ASC"
	case SortType_ST_CLK_DESC:
		return "SortType_ST_CLK_DESC"
	}
	return "<UNSET>"
}

func SortTypeFromString(s string) (SortType, error) {
	switch s {
	case "SortType_ST_DEFAULT":
		return SortType_ST_DEFAULT, nil
	case "SortType_ST_UV_ASC":
		return SortType_ST_UV_ASC, nil
	case "SortType_ST_UV_DESC":
		return SortType_ST_UV_DESC, nil
	case "SortType_ST_PV_ASC":
		return SortType_ST_PV_ASC, nil
	case "SortType_ST_PV_DESC":
		return SortType_ST_PV_DESC, nil
	case "SortType_ST_CLK_ASC":
		return SortType_ST_CLK_ASC, nil
	case "SortType_ST_CLK_DESC":
		return SortType_ST_CLK_DESC, nil
	}
	return SortType(math.MinInt32 - 1), fmt.Errorf("not a valid SortType string")
}

type RequestHeader *common.RequestHeader

type IdInt common.IdInt

type QueryReq struct {
	Header        *common.RequestHeader `thrift:"header,1" json:"header"`
	SearchQuery   string                `thrift:"search_query,2" json:"search_query"`
	BannedQuery   string                `thrift:"banned_query,3" json:"banned_query"`
	PriCategory   string                `thrift:"pri_category,4" json:"pri_category"`
	IntCategory   string                `thrift:"int_category,5" json:"int_category"`
	AdvCategory   string                `thrift:"adv_category,6" json:"adv_category"`
	MatchType     MatchType             `thrift:"match_type,7" json:"match_type"`
	RegionList    []IdInt               `thrift:"region_list,8" json:"region_list"`
	ResListLength IdInt                 `thrift:"res_list_length,9" json:"res_list_length"`
	Offset        IdInt                 `thrift:"offset,10" json:"offset"`
	Limit         IdInt                 `thrift:"limit,11" json:"limit"`
	SortType      SortType              `thrift:"sort_type,12" json:"sort_type"`
}

func NewQueryReq() *QueryReq {
	return &QueryReq{
		MatchType: math.MinInt32 - 1, // unset sentinal value

		SortType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryReq) IsSetMatchType() bool {
	return int64(p.MatchType) != math.MinInt32-1
}

func (p *QueryReq) IsSetSortType() bool {
	return int64(p.SortType) != math.MinInt32-1
}

func (p *QueryReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryReq) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryReq) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchQuery = v
	}
	return nil
}

func (p *QueryReq) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.BannedQuery = v
	}
	return nil
}

func (p *QueryReq) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PriCategory = v
	}
	return nil
}

func (p *QueryReq) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IntCategory = v
	}
	return nil
}

func (p *QueryReq) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AdvCategory = v
	}
	return nil
}

func (p *QueryReq) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.MatchType = MatchType(v)
	}
	return nil
}

func (p *QueryReq) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RegionList = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = IdInt(v)
		}
		p.RegionList = append(p.RegionList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryReq) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ResListLength = IdInt(v)
	}
	return nil
}

func (p *QueryReq) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = IdInt(v)
	}
	return nil
}

func (p *QueryReq) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = IdInt(v)
	}
	return nil
}

func (p *QueryReq) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SortType = SortType(v)
	}
	return nil
}

func (p *QueryReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_query", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_query: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SearchQuery)); err != nil {
		return fmt.Errorf("%T.search_query (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_query: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("banned_query", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:banned_query: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BannedQuery)); err != nil {
		return fmt.Errorf("%T.banned_query (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:banned_query: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pri_category", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pri_category: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PriCategory)); err != nil {
		return fmt.Errorf("%T.pri_category (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pri_category: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("int_category", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:int_category: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IntCategory)); err != nil {
		return fmt.Errorf("%T.int_category (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:int_category: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adv_category", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:adv_category: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdvCategory)); err != nil {
		return fmt.Errorf("%T.adv_category (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:adv_category: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchType() {
		if err := oprot.WriteFieldBegin("match_type", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:match_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MatchType)); err != nil {
			return fmt.Errorf("%T.match_type (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:match_type: %s", p, err)
		}
	}
	return err
}

func (p *QueryReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.RegionList != nil {
		if err := oprot.WriteFieldBegin("region_list", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:region_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RegionList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RegionList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:region_list: %s", p, err)
		}
	}
	return err
}

func (p *QueryReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("res_list_length", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:res_list_length: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ResListLength)); err != nil {
		return fmt.Errorf("%T.res_list_length (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:res_list_length: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *QueryReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortType() {
		if err := oprot.WriteFieldBegin("sort_type", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:sort_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SortType)); err != nil {
			return fmt.Errorf("%T.sort_type (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:sort_type: %s", p, err)
		}
	}
	return err
}

func (p *QueryReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryReq(%+v)", *p)
}

type LocationInfo struct {
	LocationId   IdInt   `thrift:"location_id,1" json:"location_id"`
	Name         string  `thrift:"name,2" json:"name"`
	Address      string  `thrift:"address,3" json:"address"`
	ProvinceCode IdInt   `thrift:"province_code,4" json:"province_code"`
	CityCode     IdInt   `thrift:"city_code,5" json:"city_code"`
	Latitude     float64 `thrift:"latitude,6" json:"latitude"`
	Longitude    float64 `thrift:"longitude,7" json:"longitude"`
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	ImpressionNum IdInt `thrift:"impression_num,20" json:"impression_num"`
	ClickNum      IdInt `thrift:"click_num,21" json:"click_num"`
	UserNum       IdInt `thrift:"user_num,22" json:"user_num"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Score IdInt `thrift:"score,30" json:"score"`
}

func NewLocationInfo() *LocationInfo {
	return &LocationInfo{}
}

func (p *LocationInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LocationInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.LocationId = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *LocationInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Address = v
	}
	return nil
}

func (p *LocationInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ProvinceCode = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CityCode = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *LocationInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *LocationInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ImpressionNum = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ClickNum = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.UserNum = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Score = IdInt(v)
	}
	return nil
}

func (p *LocationInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LocationInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LocationInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("location_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:location_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LocationId)); err != nil {
		return fmt.Errorf("%T.location_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:location_id: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("address", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:address: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Address)); err != nil {
		return fmt.Errorf("%T.address (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:address: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("province_code", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:province_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProvinceCode)); err != nil {
		return fmt.Errorf("%T.province_code (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:province_code: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:city_code: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:latitude: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:longitude: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impression_num", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:impression_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImpressionNum)); err != nil {
		return fmt.Errorf("%T.impression_num (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:impression_num: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_num", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:click_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClickNum)); err != nil {
		return fmt.Errorf("%T.click_num (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:click_num: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_num", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:user_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserNum)); err != nil {
		return fmt.Errorf("%T.user_num (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:user_num: %s", p, err)
	}
	return err
}

func (p *LocationInfo) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("score", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:score: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Score)); err != nil {
		return fmt.Errorf("%T.score (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:score: %s", p, err)
	}
	return err
}

func (p *LocationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LocationInfo(%+v)", *p)
}

type LocationQueryRes struct {
	ResCode      ResCode               `thrift:"res_code,1" json:"res_code"`
	QueryHeader  *common.RequestHeader `thrift:"query_header,2" json:"query_header"`
	LocationList []*LocationInfo       `thrift:"location_list,3" json:"location_list"`
	TotalNum     IdInt                 `thrift:"total_num,4" json:"total_num"`
}

func NewLocationQueryRes() *LocationQueryRes {
	return &LocationQueryRes{
		ResCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *LocationQueryRes) IsSetResCode() bool {
	return int64(p.ResCode) != math.MinInt32-1
}

func (p *LocationQueryRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LocationQueryRes) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ResCode = ResCode(v)
	}
	return nil
}

func (p *LocationQueryRes) readField2(iprot thrift.TProtocol) error {
	p.QueryHeader = common.NewRequestHeader()
	if err := p.QueryHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.QueryHeader)
	}
	return nil
}

func (p *LocationQueryRes) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.LocationList = make([]*LocationInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewLocationInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.LocationList = append(p.LocationList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LocationQueryRes) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TotalNum = IdInt(v)
	}
	return nil
}

func (p *LocationQueryRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LocationQueryRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LocationQueryRes) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResCode() {
		if err := oprot.WriteFieldBegin("res_code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:res_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResCode)); err != nil {
			return fmt.Errorf("%T.res_code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:res_code: %s", p, err)
		}
	}
	return err
}

func (p *LocationQueryRes) writeField2(oprot thrift.TProtocol) (err error) {
	if p.QueryHeader != nil {
		if err := oprot.WriteFieldBegin("query_header", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:query_header: %s", p, err)
		}
		if err := p.QueryHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.QueryHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:query_header: %s", p, err)
		}
	}
	return err
}

func (p *LocationQueryRes) writeField3(oprot thrift.TProtocol) (err error) {
	if p.LocationList != nil {
		if err := oprot.WriteFieldBegin("location_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:location_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.LocationList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.LocationList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:location_list: %s", p, err)
		}
	}
	return err
}

func (p *LocationQueryRes) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_num", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:total_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalNum)); err != nil {
		return fmt.Errorf("%T.total_num (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:total_num: %s", p, err)
	}
	return err
}

func (p *LocationQueryRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LocationQueryRes(%+v)", *p)
}

type AddTagReq struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	LocationList []*LocationInfo `thrift:"location_list,3" json:"location_list"`
}

func NewAddTagReq() *AddTagReq {
	return &AddTagReq{}
}

func (p *AddTagReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTagReq) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTagReq) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.LocationList = make([]*LocationInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewLocationInfo()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.LocationList = append(p.LocationList, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddTagReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AddTagReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTagReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTagReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.LocationList != nil {
		if err := oprot.WriteFieldBegin("location_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:location_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.LocationList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.LocationList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:location_list: %s", p, err)
		}
	}
	return err
}

func (p *AddTagReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTagReq(%+v)", *p)
}

type SearchException struct {
	Code    ResCode `thrift:"code,1" json:"code"`
	Message string  `thrift:"message,2" json:"message"`
}

func NewSearchException() *SearchException {
	return &SearchException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SearchException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *SearchException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ResCode(v)
	}
	return nil
}

func (p *SearchException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *SearchException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SearchException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *SearchException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *SearchException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchException(%+v)", *p)
}
