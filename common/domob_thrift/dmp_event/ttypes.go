// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dmp_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dmp_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dmp_types.GoUnusedProtection__
var GoUnusedProtection__ int

type DmpEventType int64

const (
	DmpEventType_DET_UNKNOWN  DmpEventType = 0
	DmpEventType_DET_ADD      DmpEventType = 1
	DmpEventType_DET_UPDATE   DmpEventType = 2
	DmpEventType_DET_DELETE   DmpEventType = 3
	DmpEventType_DET_PAUSE    DmpEventType = 4
	DmpEventType_DET_RESUME   DmpEventType = 5
	DmpEventType_DET_UPLOAD   DmpEventType = 6
	DmpEventType_DET_RELOAD   DmpEventType = 7
	DmpEventType_DET_ESTIMATE DmpEventType = 8
)

func (p DmpEventType) String() string {
	switch p {
	case DmpEventType_DET_UNKNOWN:
		return "DmpEventType_DET_UNKNOWN"
	case DmpEventType_DET_ADD:
		return "DmpEventType_DET_ADD"
	case DmpEventType_DET_UPDATE:
		return "DmpEventType_DET_UPDATE"
	case DmpEventType_DET_DELETE:
		return "DmpEventType_DET_DELETE"
	case DmpEventType_DET_PAUSE:
		return "DmpEventType_DET_PAUSE"
	case DmpEventType_DET_RESUME:
		return "DmpEventType_DET_RESUME"
	case DmpEventType_DET_UPLOAD:
		return "DmpEventType_DET_UPLOAD"
	case DmpEventType_DET_RELOAD:
		return "DmpEventType_DET_RELOAD"
	case DmpEventType_DET_ESTIMATE:
		return "DmpEventType_DET_ESTIMATE"
	}
	return "<UNSET>"
}

func DmpEventTypeFromString(s string) (DmpEventType, error) {
	switch s {
	case "DmpEventType_DET_UNKNOWN":
		return DmpEventType_DET_UNKNOWN, nil
	case "DmpEventType_DET_ADD":
		return DmpEventType_DET_ADD, nil
	case "DmpEventType_DET_UPDATE":
		return DmpEventType_DET_UPDATE, nil
	case "DmpEventType_DET_DELETE":
		return DmpEventType_DET_DELETE, nil
	case "DmpEventType_DET_PAUSE":
		return DmpEventType_DET_PAUSE, nil
	case "DmpEventType_DET_RESUME":
		return DmpEventType_DET_RESUME, nil
	case "DmpEventType_DET_UPLOAD":
		return DmpEventType_DET_UPLOAD, nil
	case "DmpEventType_DET_RELOAD":
		return DmpEventType_DET_RELOAD, nil
	case "DmpEventType_DET_ESTIMATE":
		return DmpEventType_DET_ESTIMATE, nil
	}
	return DmpEventType(math.MinInt32 - 1), fmt.Errorf("not a valid DmpEventType string")
}

type DmpEventCategory int64

const (
	DmpEventCategory_DEC_UNKNOWN     DmpEventCategory = 0
	DmpEventCategory_DEC_CROWD       DmpEventCategory = 1
	DmpEventCategory_DEC_MODEL       DmpEventCategory = 2
	DmpEventCategory_DEC_DATA_SOURCE DmpEventCategory = 3
	DmpEventCategory_DEC_SPONSOR     DmpEventCategory = 4
	DmpEventCategory_DEC_FILE        DmpEventCategory = 5
	DmpEventCategory_DEC_TASK        DmpEventCategory = 6
	DmpEventCategory_DEC_PICTURE     DmpEventCategory = 7
)

func (p DmpEventCategory) String() string {
	switch p {
	case DmpEventCategory_DEC_UNKNOWN:
		return "DmpEventCategory_DEC_UNKNOWN"
	case DmpEventCategory_DEC_CROWD:
		return "DmpEventCategory_DEC_CROWD"
	case DmpEventCategory_DEC_MODEL:
		return "DmpEventCategory_DEC_MODEL"
	case DmpEventCategory_DEC_DATA_SOURCE:
		return "DmpEventCategory_DEC_DATA_SOURCE"
	case DmpEventCategory_DEC_SPONSOR:
		return "DmpEventCategory_DEC_SPONSOR"
	case DmpEventCategory_DEC_FILE:
		return "DmpEventCategory_DEC_FILE"
	case DmpEventCategory_DEC_TASK:
		return "DmpEventCategory_DEC_TASK"
	case DmpEventCategory_DEC_PICTURE:
		return "DmpEventCategory_DEC_PICTURE"
	}
	return "<UNSET>"
}

func DmpEventCategoryFromString(s string) (DmpEventCategory, error) {
	switch s {
	case "DmpEventCategory_DEC_UNKNOWN":
		return DmpEventCategory_DEC_UNKNOWN, nil
	case "DmpEventCategory_DEC_CROWD":
		return DmpEventCategory_DEC_CROWD, nil
	case "DmpEventCategory_DEC_MODEL":
		return DmpEventCategory_DEC_MODEL, nil
	case "DmpEventCategory_DEC_DATA_SOURCE":
		return DmpEventCategory_DEC_DATA_SOURCE, nil
	case "DmpEventCategory_DEC_SPONSOR":
		return DmpEventCategory_DEC_SPONSOR, nil
	case "DmpEventCategory_DEC_FILE":
		return DmpEventCategory_DEC_FILE, nil
	case "DmpEventCategory_DEC_TASK":
		return DmpEventCategory_DEC_TASK, nil
	case "DmpEventCategory_DEC_PICTURE":
		return DmpEventCategory_DEC_PICTURE, nil
	}
	return DmpEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid DmpEventCategory string")
}

type DmpCommonEvent struct {
	TypeA1   DmpEventType     `thrift:"type,1" json:"type"`
	Category DmpEventCategory `thrift:"category,2" json:"category"`
	Ids      []int32          `thrift:"ids,3" json:"ids"`
}

func NewDmpCommonEvent() *DmpCommonEvent {
	return &DmpCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DmpCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DmpCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DmpCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DmpEventType(v)
	}
	return nil
}

func (p *DmpCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DmpEventCategory(v)
	}
	return nil
}

func (p *DmpCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DmpCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DmpCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DmpCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DmpCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpCommonEvent(%+v)", *p)
}

type DmpOperationEvent struct {
	TypeA1      DmpEventType     `thrift:"type,1" json:"type"`
	Category    DmpEventCategory `thrift:"category,2" json:"category"`
	OperateTime int64            `thrift:"operateTime,3" json:"operateTime"`
	ServiceName string           `thrift:"serviceName,4" json:"serviceName"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Crowd      *dmp_types.Crowd      `thrift:"crowd,11" json:"crowd"`
	Model      *dmp_types.Model      `thrift:"model,12" json:"model"`
	DataSource *dmp_types.DataSource `thrift:"dataSource,13" json:"dataSource"`
	Task       *dmp_types.Task       `thrift:"task,14" json:"task"`
	Picture    *dmp_types.Picture    `thrift:"picture,15" json:"picture"`
}

func NewDmpOperationEvent() *DmpOperationEvent {
	return &DmpOperationEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DmpOperationEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DmpOperationEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DmpOperationEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpOperationEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DmpEventType(v)
	}
	return nil
}

func (p *DmpOperationEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DmpEventCategory(v)
	}
	return nil
}

func (p *DmpOperationEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OperateTime = v
	}
	return nil
}

func (p *DmpOperationEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *DmpOperationEvent) readField11(iprot thrift.TProtocol) error {
	p.Crowd = dmp_types.NewCrowd()
	if err := p.Crowd.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Crowd)
	}
	return nil
}

func (p *DmpOperationEvent) readField12(iprot thrift.TProtocol) error {
	p.Model = dmp_types.NewModel()
	if err := p.Model.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Model)
	}
	return nil
}

func (p *DmpOperationEvent) readField13(iprot thrift.TProtocol) error {
	p.DataSource = dmp_types.NewDataSource()
	if err := p.DataSource.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DataSource)
	}
	return nil
}

func (p *DmpOperationEvent) readField14(iprot thrift.TProtocol) error {
	p.Task = dmp_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *DmpOperationEvent) readField15(iprot thrift.TProtocol) error {
	p.Picture = dmp_types.NewPicture()
	if err := p.Picture.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Picture)
	}
	return nil
}

func (p *DmpOperationEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpOperationEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpOperationEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("operateTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:operateTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OperateTime)); err != nil {
		return fmt.Errorf("%T.operateTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:operateTime: %s", p, err)
	}
	return err
}

func (p *DmpOperationEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("serviceName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:serviceName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServiceName)); err != nil {
		return fmt.Errorf("%T.serviceName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:serviceName: %s", p, err)
	}
	return err
}

func (p *DmpOperationEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Crowd != nil {
		if err := oprot.WriteFieldBegin("crowd", thrift.STRUCT, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:crowd: %s", p, err)
		}
		if err := p.Crowd.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Crowd)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:crowd: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Model != nil {
		if err := oprot.WriteFieldBegin("model", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:model: %s", p, err)
		}
		if err := p.Model.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Model)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:model: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) writeField13(oprot thrift.TProtocol) (err error) {
	if p.DataSource != nil {
		if err := oprot.WriteFieldBegin("dataSource", thrift.STRUCT, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:dataSource: %s", p, err)
		}
		if err := p.DataSource.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DataSource)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:dataSource: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:task: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) writeField15(oprot thrift.TProtocol) (err error) {
	if p.Picture != nil {
		if err := oprot.WriteFieldBegin("picture", thrift.STRUCT, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:picture: %s", p, err)
		}
		if err := p.Picture.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Picture)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:picture: %s", p, err)
		}
	}
	return err
}

func (p *DmpOperationEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpOperationEvent(%+v)", *p)
}
