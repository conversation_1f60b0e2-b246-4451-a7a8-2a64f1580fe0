// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_compensate_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type LargeIdInt common.LargeIdInt

type UidInt common.UidInt

type OwCSCompensateNode struct {
	ActId LargeIdInt `thrift:"actId,1" json:"actId"`
}

func NewOwCSCompensateNode() *OwCSCompensateNode {
	return &OwCSCompensateNode{}
}

func (p *OwCSCompensateNode) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwCSCompensateNode) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ActId = LargeIdInt(v)
	}
	return nil
}

func (p *OwCSCompensateNode) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwCSCompensateNode"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwCSCompensateNode) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:actId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ActId)); err != nil {
		return fmt.Errorf("%T.actId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:actId: %s", p, err)
	}
	return err
}

func (p *OwCSCompensateNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwCSCompensateNode(%+v)", *p)
}

type OwCSWaitingChargeNode struct {
	ActId    LargeIdInt `thrift:"actId,1" json:"actId"`
	ClkId    LargeIdInt `thrift:"clkId,2" json:"clkId"`
	AdCharge int32      `thrift:"adCharge,3" json:"adCharge"`
}

func NewOwCSWaitingChargeNode() *OwCSWaitingChargeNode {
	return &OwCSWaitingChargeNode{}
}

func (p *OwCSWaitingChargeNode) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwCSWaitingChargeNode) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ActId = LargeIdInt(v)
	}
	return nil
}

func (p *OwCSWaitingChargeNode) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ClkId = LargeIdInt(v)
	}
	return nil
}

func (p *OwCSWaitingChargeNode) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *OwCSWaitingChargeNode) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwCSWaitingChargeNode"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwCSWaitingChargeNode) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:actId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ActId)); err != nil {
		return fmt.Errorf("%T.actId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:actId: %s", p, err)
	}
	return err
}

func (p *OwCSWaitingChargeNode) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:clkId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClkId)); err != nil {
		return fmt.Errorf("%T.clkId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:clkId: %s", p, err)
	}
	return err
}

func (p *OwCSWaitingChargeNode) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adCharge", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:adCharge: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.adCharge (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:adCharge: %s", p, err)
	}
	return err
}

func (p *OwCSWaitingChargeNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwCSWaitingChargeNode(%+v)", *p)
}

type OwCSRequest struct {
	ActIdList []LargeIdInt `thrift:"actIdList,1" json:"actIdList"`
}

func NewOwCSRequest() *OwCSRequest {
	return &OwCSRequest{}
}

func (p *OwCSRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwCSRequest) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ActIdList = make([]LargeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 LargeIdInt
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = LargeIdInt(v)
		}
		p.ActIdList = append(p.ActIdList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwCSRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwCSRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwCSRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ActIdList != nil {
		if err := oprot.WriteFieldBegin("actIdList", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:actIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.ActIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ActIdList {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:actIdList: %s", p, err)
		}
	}
	return err
}

func (p *OwCSRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwCSRequest(%+v)", *p)
}

type OwCSResponse struct {
	Status int32 `thrift:"status,1" json:"status"`
}

func NewOwCSResponse() *OwCSResponse {
	return &OwCSResponse{}
}

func (p *OwCSResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwCSResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwCSResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwCSResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwCSResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *OwCSResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwCSResponse(%+v)", *p)
}
