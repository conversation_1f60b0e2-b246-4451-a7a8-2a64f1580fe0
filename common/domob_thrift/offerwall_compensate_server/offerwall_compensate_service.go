// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_compensate_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type OfferwallCompensateService interface {
	dm303.DomobService
	//Offerwall Compensate Server服务

	// @Description("返积分/补偿")
	//
	// Parameters:
	//  - Req
	Compensate(req *OwCSRequest) (r *OwCSResponse, err error)
}

//Offerwall Compensate Server服务
type OfferwallCompensateServiceClient struct {
	*dm303.DomobServiceClient
}

func NewOfferwallCompensateServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *OfferwallCompensateServiceClient {
	return &OfferwallCompensateServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewOfferwallCompensateServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *OfferwallCompensateServiceClient {
	return &OfferwallCompensateServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// @Description("返积分/补偿")
//
// Parameters:
//  - Req
func (p *OfferwallCompensateServiceClient) Compensate(req *OwCSRequest) (r *OwCSResponse, err error) {
	if err = p.sendCompensate(req); err != nil {
		return
	}
	return p.recvCompensate()
}

func (p *OfferwallCompensateServiceClient) sendCompensate(req *OwCSRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("compensate", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1 := NewCompensateArgs()
	args1.Req = req
	if err = args1.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *OfferwallCompensateServiceClient) recvCompensate() (value *OwCSResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error3 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error4 error
		error4, err = error3.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error4
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result2 := NewCompensateResult()
	if err = result2.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result2.Success
	return
}

type OfferwallCompensateServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewOfferwallCompensateServiceProcessor(handler OfferwallCompensateService) *OfferwallCompensateServiceProcessor {
	self5 := &OfferwallCompensateServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self5.AddToProcessorMap("compensate", &offerwallCompensateServiceProcessorCompensate{handler: handler})
	return self5
}

type offerwallCompensateServiceProcessorCompensate struct {
	handler OfferwallCompensateService
}

func (p *offerwallCompensateServiceProcessorCompensate) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCompensateArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("compensate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCompensateResult()
	if result.Success, err = p.handler.Compensate(args.Req); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing compensate: "+err.Error())
		oprot.WriteMessageBegin("compensate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("compensate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type CompensateArgs struct {
	Req *OwCSRequest `thrift:"req,1" json:"req"`
}

func NewCompensateArgs() *CompensateArgs {
	return &CompensateArgs{}
}

func (p *CompensateArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompensateArgs) readField1(iprot thrift.TProtocol) error {
	p.Req = NewOwCSRequest()
	if err := p.Req.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Req)
	}
	return nil
}

func (p *CompensateArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("compensate_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompensateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Req != nil {
		if err := oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:req: %s", p, err)
		}
		if err := p.Req.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Req)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:req: %s", p, err)
		}
	}
	return err
}

func (p *CompensateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompensateArgs(%+v)", *p)
}

type CompensateResult struct {
	Success *OwCSResponse `thrift:"success,0" json:"success"`
}

func NewCompensateResult() *CompensateResult {
	return &CompensateResult{}
}

func (p *CompensateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompensateResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwCSResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CompensateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("compensate_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompensateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CompensateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompensateResult(%+v)", *p)
}
