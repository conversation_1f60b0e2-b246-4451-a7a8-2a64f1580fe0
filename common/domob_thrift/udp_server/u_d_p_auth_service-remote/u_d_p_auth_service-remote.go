// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"udp_server"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  AuthInfo authByCode(RequestHeader header, UDPMediaPlatform platform, string auth_code, string custom_state)")
	fmt.Fprintln(os.Stderr, "  AuthInfo authByPassword(RequestHeader header, UDPMediaPlatform platform, string username, string password, string token, string account_role)")
	fmt.Fprintln(os.Stderr, "  TokenInfo getAccessToken(RequestHeader header, UDPMediaPlatform platform, i32 account_id, string account_id_string)")
	fmt.Fprintln(os.Stderr, "   getAuthInfoList(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "  void refreshToken(RequestHeader header, i32 auth_id)")
	fmt.Fprintln(os.Stderr, "   getResourceAccountByAuthId(RequestHeader header, i32 auth_id)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := udp_server.NewUDPAuthServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "authByCode":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "AuthByCode requires 4 args")
			flag.Usage()
		}
		arg28 := flag.Arg(1)
		mbTrans29 := thrift.NewTMemoryBufferLen(len(arg28))
		defer mbTrans29.Close()
		_, err30 := mbTrans29.WriteString(arg28)
		if err30 != nil {
			Usage()
			return
		}
		factory31 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt32 := factory31.GetProtocol(mbTrans29)
		argvalue0 := udp_server.NewRequestHeader()
		err33 := argvalue0.Read(jsProt32)
		if err33 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := udp_server.UDPMediaPlatform(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.AuthByCode(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "authByPassword":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "AuthByPassword requires 6 args")
			flag.Usage()
		}
		arg36 := flag.Arg(1)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue0 := udp_server.NewRequestHeader()
		err41 := argvalue0.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := udp_server.UDPMediaPlatform(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.AuthByPassword(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getAccessToken":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetAccessToken requires 4 args")
			flag.Usage()
		}
		arg46 := flag.Arg(1)
		mbTrans47 := thrift.NewTMemoryBufferLen(len(arg46))
		defer mbTrans47.Close()
		_, err48 := mbTrans47.WriteString(arg46)
		if err48 != nil {
			Usage()
			return
		}
		factory49 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt50 := factory49.GetProtocol(mbTrans47)
		argvalue0 := udp_server.NewRequestHeader()
		err51 := argvalue0.Read(jsProt50)
		if err51 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := udp_server.UDPMediaPlatform(tmp1)
		value1 := argvalue1
		tmp2, err52 := (strconv.Atoi(flag.Arg(3)))
		if err52 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.GetAccessToken(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAuthInfoList":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAuthInfoList requires 1 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := udp_server.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetAuthInfoList(value0))
		fmt.Print("\n")
		break
	case "refreshToken":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RefreshToken requires 2 args")
			flag.Usage()
		}
		arg60 := flag.Arg(1)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue0 := udp_server.NewRequestHeader()
		err65 := argvalue0.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err66 := (strconv.Atoi(flag.Arg(2)))
		if err66 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.RefreshToken(value0, value1))
		fmt.Print("\n")
		break
	case "getResourceAccountByAuthId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetResourceAccountByAuthId requires 2 args")
			flag.Usage()
		}
		arg67 := flag.Arg(1)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue0 := udp_server.NewRequestHeader()
		err72 := argvalue0.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err73 := (strconv.Atoi(flag.Arg(2)))
		if err73 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetResourceAccountByAuthId(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
