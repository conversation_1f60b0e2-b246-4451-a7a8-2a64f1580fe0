// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package udp_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/udp_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = udp_types.GoUnusedProtection__

type UDPAuthService interface {
	// 根据授权码获取授权
	//
	// Parameters:
	//  - Header
	//  - Platform
	//  - AuthCode
	//  - CustomState
	AuthByCode(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, auth_code string, custom_state string) (r *udp_types.AuthInfo, ue *UDPException, err error)
	// 根据用户名和密码获取授权
	//
	// Parameters:
	//  - Header
	//  - Platform
	//  - Username
	//  - Password
	//  - Token
	//  - AccountRole
	AuthByPassword(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, username string, password string, token string, account_role string) (r *udp_types.AuthInfo, ue *UDPException, err error)
	// 获取指定账号的AccessToken
	//
	//
	// Parameters:
	//  - Header
	//  - Platform
	//  - AccountId
	//  - AccountIdString
	GetAccessToken(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, account_id int32, account_id_string string) (r *udp_types.TokenInfo, ue *UDPException, err error)
	// 获取授权账户列表
	//
	// Parameters:
	//  - Header
	GetAuthInfoList(header *common.RequestHeader) (r []*udp_types.AuthInfo, ue *UDPException, err error)
	// 刷新指定授权账号Token
	// 目前仅限内部调用
	//
	// Parameters:
	//  - Header
	//  - AuthId
	RefreshToken(header *common.RequestHeader, auth_id int32) (ue *UDPException, err error)
	// Parameters:
	//  - Header
	//  - AuthId
	GetResourceAccountByAuthId(header *common.RequestHeader, auth_id int32) (r []*udp_types.ResourceAccount, ue *UDPException, err error)
}

type UDPAuthServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewUDPAuthServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UDPAuthServiceClient {
	return &UDPAuthServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewUDPAuthServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UDPAuthServiceClient {
	return &UDPAuthServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 根据授权码获取授权
//
// Parameters:
//  - Header
//  - Platform
//  - AuthCode
//  - CustomState
func (p *UDPAuthServiceClient) AuthByCode(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, auth_code string, custom_state string) (r *udp_types.AuthInfo, ue *UDPException, err error) {
	if err = p.sendAuthByCode(header, platform, auth_code, custom_state); err != nil {
		return
	}
	return p.recvAuthByCode()
}

func (p *UDPAuthServiceClient) sendAuthByCode(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, auth_code string, custom_state string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("authByCode", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAuthByCodeArgs()
	args0.Header = header
	args0.Platform = platform
	args0.AuthCode = auth_code
	args0.CustomState = custom_state
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPAuthServiceClient) recvAuthByCode() (value *udp_types.AuthInfo, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAuthByCodeResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Ue != nil {
		ue = result1.Ue
	}
	return
}

// 根据用户名和密码获取授权
//
// Parameters:
//  - Header
//  - Platform
//  - Username
//  - Password
//  - Token
//  - AccountRole
func (p *UDPAuthServiceClient) AuthByPassword(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, username string, password string, token string, account_role string) (r *udp_types.AuthInfo, ue *UDPException, err error) {
	if err = p.sendAuthByPassword(header, platform, username, password, token, account_role); err != nil {
		return
	}
	return p.recvAuthByPassword()
}

func (p *UDPAuthServiceClient) sendAuthByPassword(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, username string, password string, token string, account_role string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("authByPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewAuthByPasswordArgs()
	args4.Header = header
	args4.Platform = platform
	args4.Username = username
	args4.Password = password
	args4.Token = token
	args4.AccountRole = account_role
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPAuthServiceClient) recvAuthByPassword() (value *udp_types.AuthInfo, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewAuthByPasswordResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Ue != nil {
		ue = result5.Ue
	}
	return
}

// 获取指定账号的AccessToken
//
//
// Parameters:
//  - Header
//  - Platform
//  - AccountId
//  - AccountIdString
func (p *UDPAuthServiceClient) GetAccessToken(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, account_id int32, account_id_string string) (r *udp_types.TokenInfo, ue *UDPException, err error) {
	if err = p.sendGetAccessToken(header, platform, account_id, account_id_string); err != nil {
		return
	}
	return p.recvGetAccessToken()
}

func (p *UDPAuthServiceClient) sendGetAccessToken(header *common.RequestHeader, platform udp_types.UDPMediaPlatform, account_id int32, account_id_string string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAccessToken", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetAccessTokenArgs()
	args8.Header = header
	args8.Platform = platform
	args8.AccountId = account_id
	args8.AccountIdString = account_id_string
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPAuthServiceClient) recvGetAccessToken() (value *udp_types.TokenInfo, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetAccessTokenResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Ue != nil {
		ue = result9.Ue
	}
	return
}

// 获取授权账户列表
//
// Parameters:
//  - Header
func (p *UDPAuthServiceClient) GetAuthInfoList(header *common.RequestHeader) (r []*udp_types.AuthInfo, ue *UDPException, err error) {
	if err = p.sendGetAuthInfoList(header); err != nil {
		return
	}
	return p.recvGetAuthInfoList()
}

func (p *UDPAuthServiceClient) sendGetAuthInfoList(header *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAuthInfoList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetAuthInfoListArgs()
	args12.Header = header
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPAuthServiceClient) recvGetAuthInfoList() (value []*udp_types.AuthInfo, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetAuthInfoListResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Ue != nil {
		ue = result13.Ue
	}
	return
}

// 刷新指定授权账号Token
// 目前仅限内部调用
//
// Parameters:
//  - Header
//  - AuthId
func (p *UDPAuthServiceClient) RefreshToken(header *common.RequestHeader, auth_id int32) (ue *UDPException, err error) {
	if err = p.sendRefreshToken(header, auth_id); err != nil {
		return
	}
	return p.recvRefreshToken()
}

func (p *UDPAuthServiceClient) sendRefreshToken(header *common.RequestHeader, auth_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("refreshToken", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewRefreshTokenArgs()
	args16.Header = header
	args16.AuthId = auth_id
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPAuthServiceClient) recvRefreshToken() (ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewRefreshTokenResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result17.Ue != nil {
		ue = result17.Ue
	}
	return
}

// Parameters:
//  - Header
//  - AuthId
func (p *UDPAuthServiceClient) GetResourceAccountByAuthId(header *common.RequestHeader, auth_id int32) (r []*udp_types.ResourceAccount, ue *UDPException, err error) {
	if err = p.sendGetResourceAccountByAuthId(header, auth_id); err != nil {
		return
	}
	return p.recvGetResourceAccountByAuthId()
}

func (p *UDPAuthServiceClient) sendGetResourceAccountByAuthId(header *common.RequestHeader, auth_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getResourceAccountByAuthId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetResourceAccountByAuthIdArgs()
	args20.Header = header
	args20.AuthId = auth_id
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPAuthServiceClient) recvGetResourceAccountByAuthId() (value []*udp_types.ResourceAccount, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetResourceAccountByAuthIdResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Ue != nil {
		ue = result21.Ue
	}
	return
}

type UDPAuthServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UDPAuthService
}

func (p *UDPAuthServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UDPAuthServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UDPAuthServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUDPAuthServiceProcessor(handler UDPAuthService) *UDPAuthServiceProcessor {

	self24 := &UDPAuthServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self24.processorMap["authByCode"] = &uDPAuthServiceProcessorAuthByCode{handler: handler}
	self24.processorMap["authByPassword"] = &uDPAuthServiceProcessorAuthByPassword{handler: handler}
	self24.processorMap["getAccessToken"] = &uDPAuthServiceProcessorGetAccessToken{handler: handler}
	self24.processorMap["getAuthInfoList"] = &uDPAuthServiceProcessorGetAuthInfoList{handler: handler}
	self24.processorMap["refreshToken"] = &uDPAuthServiceProcessorRefreshToken{handler: handler}
	self24.processorMap["getResourceAccountByAuthId"] = &uDPAuthServiceProcessorGetResourceAccountByAuthId{handler: handler}
	return self24
}

func (p *UDPAuthServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x25 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x25.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x25

}

type uDPAuthServiceProcessorAuthByCode struct {
	handler UDPAuthService
}

func (p *uDPAuthServiceProcessorAuthByCode) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAuthByCodeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("authByCode", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAuthByCodeResult()
	if result.Success, result.Ue, err = p.handler.AuthByCode(args.Header, args.Platform, args.AuthCode, args.CustomState); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing authByCode: "+err.Error())
		oprot.WriteMessageBegin("authByCode", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("authByCode", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uDPAuthServiceProcessorAuthByPassword struct {
	handler UDPAuthService
}

func (p *uDPAuthServiceProcessorAuthByPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAuthByPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("authByPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAuthByPasswordResult()
	if result.Success, result.Ue, err = p.handler.AuthByPassword(args.Header, args.Platform, args.Username, args.Password, args.Token, args.AccountRole); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing authByPassword: "+err.Error())
		oprot.WriteMessageBegin("authByPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("authByPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uDPAuthServiceProcessorGetAccessToken struct {
	handler UDPAuthService
}

func (p *uDPAuthServiceProcessorGetAccessToken) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAccessTokenArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAccessToken", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAccessTokenResult()
	if result.Success, result.Ue, err = p.handler.GetAccessToken(args.Header, args.Platform, args.AccountId, args.AccountIdString); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAccessToken: "+err.Error())
		oprot.WriteMessageBegin("getAccessToken", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAccessToken", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uDPAuthServiceProcessorGetAuthInfoList struct {
	handler UDPAuthService
}

func (p *uDPAuthServiceProcessorGetAuthInfoList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAuthInfoListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAuthInfoList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAuthInfoListResult()
	if result.Success, result.Ue, err = p.handler.GetAuthInfoList(args.Header); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAuthInfoList: "+err.Error())
		oprot.WriteMessageBegin("getAuthInfoList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAuthInfoList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uDPAuthServiceProcessorRefreshToken struct {
	handler UDPAuthService
}

func (p *uDPAuthServiceProcessorRefreshToken) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRefreshTokenArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("refreshToken", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRefreshTokenResult()
	if result.Ue, err = p.handler.RefreshToken(args.Header, args.AuthId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing refreshToken: "+err.Error())
		oprot.WriteMessageBegin("refreshToken", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("refreshToken", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uDPAuthServiceProcessorGetResourceAccountByAuthId struct {
	handler UDPAuthService
}

func (p *uDPAuthServiceProcessorGetResourceAccountByAuthId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetResourceAccountByAuthIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getResourceAccountByAuthId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetResourceAccountByAuthIdResult()
	if result.Success, result.Ue, err = p.handler.GetResourceAccountByAuthId(args.Header, args.AuthId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getResourceAccountByAuthId: "+err.Error())
		oprot.WriteMessageBegin("getResourceAccountByAuthId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getResourceAccountByAuthId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AuthByCodeArgs struct {
	Header      *common.RequestHeader      `thrift:"header,1" json:"header"`
	Platform    udp_types.UDPMediaPlatform `thrift:"platform,2" json:"platform"`
	AuthCode    string                     `thrift:"auth_code,3" json:"auth_code"`
	CustomState string                     `thrift:"custom_state,4" json:"custom_state"`
}

func NewAuthByCodeArgs() *AuthByCodeArgs {
	return &AuthByCodeArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AuthByCodeArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *AuthByCodeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AuthByCodeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AuthByCodeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = udp_types.UDPMediaPlatform(v)
	}
	return nil
}

func (p *AuthByCodeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AuthCode = v
	}
	return nil
}

func (p *AuthByCodeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CustomState = v
	}
	return nil
}

func (p *AuthByCodeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("authByCode_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AuthByCodeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AuthByCodeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *AuthByCodeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth_code", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:auth_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthCode)); err != nil {
		return fmt.Errorf("%T.auth_code (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:auth_code: %s", p, err)
	}
	return err
}

func (p *AuthByCodeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("custom_state", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:custom_state: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CustomState)); err != nil {
		return fmt.Errorf("%T.custom_state (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:custom_state: %s", p, err)
	}
	return err
}

func (p *AuthByCodeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthByCodeArgs(%+v)", *p)
}

type AuthByCodeResult struct {
	Success *udp_types.AuthInfo `thrift:"success,0" json:"success"`
	Ue      *UDPException       `thrift:"ue,1" json:"ue"`
}

func NewAuthByCodeResult() *AuthByCodeResult {
	return &AuthByCodeResult{}
}

func (p *AuthByCodeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AuthByCodeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = udp_types.NewAuthInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AuthByCodeResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *AuthByCodeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("authByCode_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AuthByCodeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AuthByCodeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *AuthByCodeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthByCodeResult(%+v)", *p)
}

type AuthByPasswordArgs struct {
	Header      *common.RequestHeader      `thrift:"header,1" json:"header"`
	Platform    udp_types.UDPMediaPlatform `thrift:"platform,2" json:"platform"`
	Username    string                     `thrift:"username,3" json:"username"`
	Password    string                     `thrift:"password,4" json:"password"`
	Token       string                     `thrift:"token,5" json:"token"`
	AccountRole string                     `thrift:"account_role,6" json:"account_role"`
}

func NewAuthByPasswordArgs() *AuthByPasswordArgs {
	return &AuthByPasswordArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AuthByPasswordArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *AuthByPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AuthByPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AuthByPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = udp_types.UDPMediaPlatform(v)
	}
	return nil
}

func (p *AuthByPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *AuthByPasswordArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *AuthByPasswordArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Token = v
	}
	return nil
}

func (p *AuthByPasswordArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AccountRole = v
	}
	return nil
}

func (p *AuthByPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("authByPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AuthByPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AuthByPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *AuthByPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:username: %s", p, err)
	}
	return err
}

func (p *AuthByPasswordArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:password: %s", p, err)
	}
	return err
}

func (p *AuthByPasswordArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("token", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Token)); err != nil {
		return fmt.Errorf("%T.token (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:token: %s", p, err)
	}
	return err
}

func (p *AuthByPasswordArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_role", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:account_role: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountRole)); err != nil {
		return fmt.Errorf("%T.account_role (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:account_role: %s", p, err)
	}
	return err
}

func (p *AuthByPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthByPasswordArgs(%+v)", *p)
}

type AuthByPasswordResult struct {
	Success *udp_types.AuthInfo `thrift:"success,0" json:"success"`
	Ue      *UDPException       `thrift:"ue,1" json:"ue"`
}

func NewAuthByPasswordResult() *AuthByPasswordResult {
	return &AuthByPasswordResult{}
}

func (p *AuthByPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AuthByPasswordResult) readField0(iprot thrift.TProtocol) error {
	p.Success = udp_types.NewAuthInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AuthByPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *AuthByPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("authByPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AuthByPasswordResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AuthByPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *AuthByPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthByPasswordResult(%+v)", *p)
}

type GetAccessTokenArgs struct {
	Header          *common.RequestHeader      `thrift:"header,1" json:"header"`
	Platform        udp_types.UDPMediaPlatform `thrift:"platform,2" json:"platform"`
	AccountId       int32                      `thrift:"account_id,3" json:"account_id"`
	AccountIdString string                     `thrift:"account_id_string,4" json:"account_id_string"`
}

func NewGetAccessTokenArgs() *GetAccessTokenArgs {
	return &GetAccessTokenArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAccessTokenArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetAccessTokenArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAccessTokenArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAccessTokenArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = udp_types.UDPMediaPlatform(v)
	}
	return nil
}

func (p *GetAccessTokenArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *GetAccessTokenArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccountIdString = v
	}
	return nil
}

func (p *GetAccessTokenArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAccessToken_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAccessTokenArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAccessTokenArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *GetAccessTokenArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccountId)); err != nil {
		return fmt.Errorf("%T.account_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account_id: %s", p, err)
	}
	return err
}

func (p *GetAccessTokenArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id_string", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:account_id_string: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountIdString)); err != nil {
		return fmt.Errorf("%T.account_id_string (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:account_id_string: %s", p, err)
	}
	return err
}

func (p *GetAccessTokenArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAccessTokenArgs(%+v)", *p)
}

type GetAccessTokenResult struct {
	Success *udp_types.TokenInfo `thrift:"success,0" json:"success"`
	Ue      *UDPException        `thrift:"ue,1" json:"ue"`
}

func NewGetAccessTokenResult() *GetAccessTokenResult {
	return &GetAccessTokenResult{}
}

func (p *GetAccessTokenResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAccessTokenResult) readField0(iprot thrift.TProtocol) error {
	p.Success = udp_types.NewTokenInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAccessTokenResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetAccessTokenResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAccessToken_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAccessTokenResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAccessTokenResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetAccessTokenResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAccessTokenResult(%+v)", *p)
}

type GetAuthInfoListArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
}

func NewGetAuthInfoListArgs() *GetAuthInfoListArgs {
	return &GetAuthInfoListArgs{}
}

func (p *GetAuthInfoListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAuthInfoListArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAuthInfoListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAuthInfoList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAuthInfoListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAuthInfoListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAuthInfoListArgs(%+v)", *p)
}

type GetAuthInfoListResult struct {
	Success []*udp_types.AuthInfo `thrift:"success,0" json:"success"`
	Ue      *UDPException         `thrift:"ue,1" json:"ue"`
}

func NewGetAuthInfoListResult() *GetAuthInfoListResult {
	return &GetAuthInfoListResult{}
}

func (p *GetAuthInfoListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAuthInfoListResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*udp_types.AuthInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem26 := udp_types.NewAuthInfo()
		if err := _elem26.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem26)
		}
		p.Success = append(p.Success, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAuthInfoListResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetAuthInfoListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAuthInfoList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAuthInfoListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAuthInfoListResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetAuthInfoListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAuthInfoListResult(%+v)", *p)
}

type RefreshTokenArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	AuthId int32                 `thrift:"auth_id,2" json:"auth_id"`
}

func NewRefreshTokenArgs() *RefreshTokenArgs {
	return &RefreshTokenArgs{}
}

func (p *RefreshTokenArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RefreshTokenArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RefreshTokenArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AuthId = v
	}
	return nil
}

func (p *RefreshTokenArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("refreshToken_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RefreshTokenArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RefreshTokenArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:auth_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AuthId)); err != nil {
		return fmt.Errorf("%T.auth_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:auth_id: %s", p, err)
	}
	return err
}

func (p *RefreshTokenArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefreshTokenArgs(%+v)", *p)
}

type RefreshTokenResult struct {
	Ue *UDPException `thrift:"ue,1" json:"ue"`
}

func NewRefreshTokenResult() *RefreshTokenResult {
	return &RefreshTokenResult{}
}

func (p *RefreshTokenResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RefreshTokenResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *RefreshTokenResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("refreshToken_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RefreshTokenResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *RefreshTokenResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefreshTokenResult(%+v)", *p)
}

type GetResourceAccountByAuthIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	AuthId int32                 `thrift:"auth_id,2" json:"auth_id"`
}

func NewGetResourceAccountByAuthIdArgs() *GetResourceAccountByAuthIdArgs {
	return &GetResourceAccountByAuthIdArgs{}
}

func (p *GetResourceAccountByAuthIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AuthId = v
	}
	return nil
}

func (p *GetResourceAccountByAuthIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getResourceAccountByAuthId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetResourceAccountByAuthIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:auth_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AuthId)); err != nil {
		return fmt.Errorf("%T.auth_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:auth_id: %s", p, err)
	}
	return err
}

func (p *GetResourceAccountByAuthIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetResourceAccountByAuthIdArgs(%+v)", *p)
}

type GetResourceAccountByAuthIdResult struct {
	Success []*udp_types.ResourceAccount `thrift:"success,0" json:"success"`
	Ue      *UDPException                `thrift:"ue,1" json:"ue"`
}

func NewGetResourceAccountByAuthIdResult() *GetResourceAccountByAuthIdResult {
	return &GetResourceAccountByAuthIdResult{}
}

func (p *GetResourceAccountByAuthIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*udp_types.ResourceAccount, 0, size)
	for i := 0; i < size; i++ {
		_elem27 := udp_types.NewResourceAccount()
		if err := _elem27.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem27)
		}
		p.Success = append(p.Success, _elem27)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getResourceAccountByAuthId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetResourceAccountByAuthIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetResourceAccountByAuthIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetResourceAccountByAuthIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetResourceAccountByAuthIdResult(%+v)", *p)
}
