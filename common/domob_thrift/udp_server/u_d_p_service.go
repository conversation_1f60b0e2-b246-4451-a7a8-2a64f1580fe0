// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package udp_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/udp_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = udp_types.GoUnusedProtection__

type UDPService interface {
	// 提交数据抓取/同步任务
	//
	// Parameters:
	//  - Header
	//  - Task
	SubmitSyncTask(header *common.RequestHeader, task *udp_types.SyncTask) (r int32, ue *UDPException, err error)
	// 查询任务信息
	// 包括任务执行状态
	//
	// Parameters:
	//  - Header
	//  - Tid
	GetSyncTaskById(header *common.RequestHeader, tid int32) (r *udp_types.SyncTask, ue *UDPException, err error)
}

type UDPServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewUDPServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UDPServiceClient {
	return &UDPServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewUDPServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UDPServiceClient {
	return &UDPServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 提交数据抓取/同步任务
//
// Parameters:
//  - Header
//  - Task
func (p *UDPServiceClient) SubmitSyncTask(header *common.RequestHeader, task *udp_types.SyncTask) (r int32, ue *UDPException, err error) {
	if err = p.sendSubmitSyncTask(header, task); err != nil {
		return
	}
	return p.recvSubmitSyncTask()
}

func (p *UDPServiceClient) sendSubmitSyncTask(header *common.RequestHeader, task *udp_types.SyncTask) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitSyncTask", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args74 := NewSubmitSyncTaskArgs()
	args74.Header = header
	args74.Task = task
	if err = args74.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPServiceClient) recvSubmitSyncTask() (value int32, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error76 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error77 error
		error77, err = error76.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error77
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result75 := NewSubmitSyncTaskResult()
	if err = result75.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result75.Success
	if result75.Ue != nil {
		ue = result75.Ue
	}
	return
}

// 查询任务信息
// 包括任务执行状态
//
// Parameters:
//  - Header
//  - Tid
func (p *UDPServiceClient) GetSyncTaskById(header *common.RequestHeader, tid int32) (r *udp_types.SyncTask, ue *UDPException, err error) {
	if err = p.sendGetSyncTaskById(header, tid); err != nil {
		return
	}
	return p.recvGetSyncTaskById()
}

func (p *UDPServiceClient) sendGetSyncTaskById(header *common.RequestHeader, tid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSyncTaskById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args78 := NewGetSyncTaskByIdArgs()
	args78.Header = header
	args78.Tid = tid
	if err = args78.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UDPServiceClient) recvGetSyncTaskById() (value *udp_types.SyncTask, ue *UDPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error80 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error81 error
		error81, err = error80.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error81
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result79 := NewGetSyncTaskByIdResult()
	if err = result79.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result79.Success
	if result79.Ue != nil {
		ue = result79.Ue
	}
	return
}

type UDPServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UDPService
}

func (p *UDPServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UDPServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UDPServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUDPServiceProcessor(handler UDPService) *UDPServiceProcessor {

	self82 := &UDPServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self82.processorMap["submitSyncTask"] = &uDPServiceProcessorSubmitSyncTask{handler: handler}
	self82.processorMap["getSyncTaskById"] = &uDPServiceProcessorGetSyncTaskById{handler: handler}
	return self82
}

func (p *UDPServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x83 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x83.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x83

}

type uDPServiceProcessorSubmitSyncTask struct {
	handler UDPService
}

func (p *uDPServiceProcessorSubmitSyncTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitSyncTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitSyncTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitSyncTaskResult()
	if result.Success, result.Ue, err = p.handler.SubmitSyncTask(args.Header, args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitSyncTask: "+err.Error())
		oprot.WriteMessageBegin("submitSyncTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitSyncTask", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uDPServiceProcessorGetSyncTaskById struct {
	handler UDPService
}

func (p *uDPServiceProcessorGetSyncTaskById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSyncTaskByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSyncTaskById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSyncTaskByIdResult()
	if result.Success, result.Ue, err = p.handler.GetSyncTaskById(args.Header, args.Tid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSyncTaskById: "+err.Error())
		oprot.WriteMessageBegin("getSyncTaskById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSyncTaskById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type SubmitSyncTaskArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Task   *udp_types.SyncTask   `thrift:"task,2" json:"task"`
}

func NewSubmitSyncTaskArgs() *SubmitSyncTaskArgs {
	return &SubmitSyncTaskArgs{}
}

func (p *SubmitSyncTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitSyncTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitSyncTaskArgs) readField2(iprot thrift.TProtocol) error {
	p.Task = udp_types.NewSyncTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *SubmitSyncTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitSyncTask_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitSyncTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitSyncTaskArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:task: %s", p, err)
		}
	}
	return err
}

func (p *SubmitSyncTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitSyncTaskArgs(%+v)", *p)
}

type SubmitSyncTaskResult struct {
	Success int32         `thrift:"success,0" json:"success"`
	Ue      *UDPException `thrift:"ue,1" json:"ue"`
}

func NewSubmitSyncTaskResult() *SubmitSyncTaskResult {
	return &SubmitSyncTaskResult{}
}

func (p *SubmitSyncTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitSyncTaskResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *SubmitSyncTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *SubmitSyncTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitSyncTask_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitSyncTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *SubmitSyncTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *SubmitSyncTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitSyncTaskResult(%+v)", *p)
}

type GetSyncTaskByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Tid    int32                 `thrift:"tid,2" json:"tid"`
}

func NewGetSyncTaskByIdArgs() *GetSyncTaskByIdArgs {
	return &GetSyncTaskByIdArgs{}
}

func (p *GetSyncTaskByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSyncTaskByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSyncTaskByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Tid = v
	}
	return nil
}

func (p *GetSyncTaskByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSyncTaskById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSyncTaskByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSyncTaskByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Tid)); err != nil {
		return fmt.Errorf("%T.tid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tid: %s", p, err)
	}
	return err
}

func (p *GetSyncTaskByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSyncTaskByIdArgs(%+v)", *p)
}

type GetSyncTaskByIdResult struct {
	Success *udp_types.SyncTask `thrift:"success,0" json:"success"`
	Ue      *UDPException       `thrift:"ue,1" json:"ue"`
}

func NewGetSyncTaskByIdResult() *GetSyncTaskByIdResult {
	return &GetSyncTaskByIdResult{}
}

func (p *GetSyncTaskByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSyncTaskByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = udp_types.NewSyncTask()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSyncTaskByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUDPException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetSyncTaskByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSyncTaskById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSyncTaskByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSyncTaskByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetSyncTaskByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSyncTaskByIdResult(%+v)", *p)
}
