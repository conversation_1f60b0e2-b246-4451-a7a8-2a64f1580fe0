// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package media_features_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__

type MediaFeatureServer interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - MediaType
	//  - SourceUrl
	GetMediaFingerprintInfo(header *common.RequestHeader, media_type MFMediaType, source_url string) (r *MediaFeatureInfo, rae *MFServerException, err error)
}

type MediaFeatureServerClient struct {
	*dm303.DomobServiceClient
}

func NewMediaFeatureServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *MediaFeatureServerClient {
	return &MediaFeatureServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewMediaFeatureServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *MediaFeatureServerClient {
	return &MediaFeatureServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - MediaType
//  - SourceUrl
func (p *MediaFeatureServerClient) GetMediaFingerprintInfo(header *common.RequestHeader, media_type MFMediaType, source_url string) (r *MediaFeatureInfo, rae *MFServerException, err error) {
	if err = p.sendGetMediaFingerprintInfo(header, media_type, source_url); err != nil {
		return
	}
	return p.recvGetMediaFingerprintInfo()
}

func (p *MediaFeatureServerClient) sendGetMediaFingerprintInfo(header *common.RequestHeader, media_type MFMediaType, source_url string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMediaFingerprintInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewGetMediaFingerprintInfoArgs()
	args2.Header = header
	args2.MediaType = media_type
	args2.SourceUrl = source_url
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *MediaFeatureServerClient) recvGetMediaFingerprintInfo() (value *MediaFeatureInfo, rae *MFServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewGetMediaFingerprintInfoResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	if result3.Rae != nil {
		rae = result3.Rae
	}
	return
}

type MediaFeatureServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewMediaFeatureServerProcessor(handler MediaFeatureServer) *MediaFeatureServerProcessor {
	self6 := &MediaFeatureServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self6.AddToProcessorMap("getMediaFingerprintInfo", &mediaFeatureServerProcessorGetMediaFingerprintInfo{handler: handler})
	return self6
}

type mediaFeatureServerProcessorGetMediaFingerprintInfo struct {
	handler MediaFeatureServer
}

func (p *mediaFeatureServerProcessorGetMediaFingerprintInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMediaFingerprintInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMediaFingerprintInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMediaFingerprintInfoResult()
	if result.Success, result.Rae, err = p.handler.GetMediaFingerprintInfo(args.Header, args.MediaType, args.SourceUrl); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMediaFingerprintInfo: "+err.Error())
		oprot.WriteMessageBegin("getMediaFingerprintInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMediaFingerprintInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetMediaFingerprintInfoArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	MediaType MFMediaType           `thrift:"media_type,2" json:"media_type"`
	SourceUrl string                `thrift:"source_url,3" json:"source_url"`
}

func NewGetMediaFingerprintInfoArgs() *GetMediaFingerprintInfoArgs {
	return &GetMediaFingerprintInfoArgs{
		MediaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetMediaFingerprintInfoArgs) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *GetMediaFingerprintInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaFingerprintInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetMediaFingerprintInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaType = MFMediaType(v)
	}
	return nil
}

func (p *GetMediaFingerprintInfoArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SourceUrl = v
	}
	return nil
}

func (p *GetMediaFingerprintInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaFingerprintInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaFingerprintInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaFingerprintInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaType() {
		if err := oprot.WriteFieldBegin("media_type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:media_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
			return fmt.Errorf("%T.media_type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:media_type: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaFingerprintInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_url", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:source_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceUrl)); err != nil {
		return fmt.Errorf("%T.source_url (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:source_url: %s", p, err)
	}
	return err
}

func (p *GetMediaFingerprintInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaFingerprintInfoArgs(%+v)", *p)
}

type GetMediaFingerprintInfoResult struct {
	Success *MediaFeatureInfo  `thrift:"success,0" json:"success"`
	Rae     *MFServerException `thrift:"rae,1" json:"rae"`
}

func NewGetMediaFingerprintInfoResult() *GetMediaFingerprintInfoResult {
	return &GetMediaFingerprintInfoResult{}
}

func (p *GetMediaFingerprintInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaFingerprintInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMediaFeatureInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetMediaFingerprintInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = NewMFServerException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetMediaFingerprintInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaFingerprintInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaFingerprintInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaFingerprintInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaFingerprintInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaFingerprintInfoResult(%+v)", *p)
}
