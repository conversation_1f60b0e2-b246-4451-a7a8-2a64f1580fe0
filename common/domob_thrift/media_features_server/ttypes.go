// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package media_features_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__
var GoUnusedProtection__ int

type MFServerStatus int64

const (
	MFServerStatus_SUCCESS MFServerStatus = 0
)

func (p MFServerStatus) String() string {
	switch p {
	case MFServerStatus_SUCCESS:
		return "MFServerStatus_SUCCESS"
	}
	return "<UNSET>"
}

func MFServerStatusFromString(s string) (MFServerStatus, error) {
	switch s {
	case "MFServerStatus_SUCCESS":
		return MFServerStatus_SUCCESS, nil
	}
	return MFServerStatus(math.MinInt32 - 1), fmt.Errorf("not a valid MFServerStatus string")
}

type MFMediaType int64

const (
	MFMediaType_STATIC_IMAGE    MFMediaType = 1
	MFMediaType_ANIMATION_IMAGE MFMediaType = 2
	MFMediaType_VIDEO           MFMediaType = 3
)

func (p MFMediaType) String() string {
	switch p {
	case MFMediaType_STATIC_IMAGE:
		return "MFMediaType_STATIC_IMAGE"
	case MFMediaType_ANIMATION_IMAGE:
		return "MFMediaType_ANIMATION_IMAGE"
	case MFMediaType_VIDEO:
		return "MFMediaType_VIDEO"
	}
	return "<UNSET>"
}

func MFMediaTypeFromString(s string) (MFMediaType, error) {
	switch s {
	case "MFMediaType_STATIC_IMAGE":
		return MFMediaType_STATIC_IMAGE, nil
	case "MFMediaType_ANIMATION_IMAGE":
		return MFMediaType_ANIMATION_IMAGE, nil
	case "MFMediaType_VIDEO":
		return MFMediaType_VIDEO, nil
	}
	return MFMediaType(math.MinInt32 - 1), fmt.Errorf("not a valid MFMediaType string")
}

type MFServerException struct {
	Code    MFServerStatus `thrift:"code,1" json:"code"`
	Message string         `thrift:"message,2" json:"message"`
}

func NewMFServerException() *MFServerException {
	return &MFServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MFServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *MFServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MFServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = MFServerStatus(v)
	}
	return nil
}

func (p *MFServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *MFServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MFServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MFServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *MFServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *MFServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MFServerException(%+v)", *p)
}

type MediaFeatureInfo struct {
	SourceUrl string      `thrift:"source_url,1" json:"source_url"`
	MediaType MFMediaType `thrift:"media_type,2" json:"media_type"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	HexMediaFingerprint []string `thrift:"hex_media_fingerprint,10" json:"hex_media_fingerprint"`
	DecMediaFingerprint []int64  `thrift:"dec_media_fingerprint,11" json:"dec_media_fingerprint"`
}

func NewMediaFeatureInfo() *MediaFeatureInfo {
	return &MediaFeatureInfo{
		MediaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaFeatureInfo) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *MediaFeatureInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaFeatureInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SourceUrl = v
	}
	return nil
}

func (p *MediaFeatureInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaType = MFMediaType(v)
	}
	return nil
}

func (p *MediaFeatureInfo) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.HexMediaFingerprint = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.HexMediaFingerprint = append(p.HexMediaFingerprint, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaFeatureInfo) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DecMediaFingerprint = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.DecMediaFingerprint = append(p.DecMediaFingerprint, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaFeatureInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaFeatureInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaFeatureInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:source_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceUrl)); err != nil {
		return fmt.Errorf("%T.source_url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:source_url: %s", p, err)
	}
	return err
}

func (p *MediaFeatureInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaType() {
		if err := oprot.WriteFieldBegin("media_type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:media_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
			return fmt.Errorf("%T.media_type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:media_type: %s", p, err)
		}
	}
	return err
}

func (p *MediaFeatureInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.HexMediaFingerprint != nil {
		if err := oprot.WriteFieldBegin("hex_media_fingerprint", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:hex_media_fingerprint: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.HexMediaFingerprint)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.HexMediaFingerprint {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:hex_media_fingerprint: %s", p, err)
		}
	}
	return err
}

func (p *MediaFeatureInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.DecMediaFingerprint != nil {
		if err := oprot.WriteFieldBegin("dec_media_fingerprint", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:dec_media_fingerprint: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.DecMediaFingerprint)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DecMediaFingerprint {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:dec_media_fingerprint: %s", p, err)
		}
	}
	return err
}

func (p *MediaFeatureInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaFeatureInfo(%+v)", *p)
}
