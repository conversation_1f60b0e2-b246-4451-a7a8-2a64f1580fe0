// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_finance_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//财务记录类型定义
type FinanceAmountRecordType int64

const (
	FinanceAmountRecordType_FART_UNKNOWN                  FinanceAmountRecordType = 0
	FinanceAmountRecordType_FART_RECHARGE                 FinanceAmountRecordType = 1
	FinanceAmountRecordType_FART_AD_EXPEND                FinanceAmountRecordType = 2
	FinanceAmountRecordType_FART_ACCOUNT_FINANCE_TRANSFER FinanceAmountRecordType = 3
)

func (p FinanceAmountRecordType) String() string {
	switch p {
	case FinanceAmountRecordType_FART_UNKNOWN:
		return "FinanceAmountRecordType_FART_UNKNOWN"
	case FinanceAmountRecordType_FART_RECHARGE:
		return "FinanceAmountRecordType_FART_RECHARGE"
	case FinanceAmountRecordType_FART_AD_EXPEND:
		return "FinanceAmountRecordType_FART_AD_EXPEND"
	case FinanceAmountRecordType_FART_ACCOUNT_FINANCE_TRANSFER:
		return "FinanceAmountRecordType_FART_ACCOUNT_FINANCE_TRANSFER"
	}
	return "<UNSET>"
}

func FinanceAmountRecordTypeFromString(s string) (FinanceAmountRecordType, error) {
	switch s {
	case "FinanceAmountRecordType_FART_UNKNOWN":
		return FinanceAmountRecordType_FART_UNKNOWN, nil
	case "FinanceAmountRecordType_FART_RECHARGE":
		return FinanceAmountRecordType_FART_RECHARGE, nil
	case "FinanceAmountRecordType_FART_AD_EXPEND":
		return FinanceAmountRecordType_FART_AD_EXPEND, nil
	case "FinanceAmountRecordType_FART_ACCOUNT_FINANCE_TRANSFER":
		return FinanceAmountRecordType_FART_ACCOUNT_FINANCE_TRANSFER, nil
	}
	return FinanceAmountRecordType(math.MinInt32 - 1), fmt.Errorf("not a valid FinanceAmountRecordType string")
}

type FinanceRecordSearchParams struct {
	Keyword   string `thrift:"keyword,1" json:"keyword"`
	Uid       int32  `thrift:"uid,2" json:"uid"`
	StartTime int64  `thrift:"startTime,3" json:"startTime"`
	EndTime   int64  `thrift:"endTime,4" json:"endTime"`
}

func NewFinanceRecordSearchParams() *FinanceRecordSearchParams {
	return &FinanceRecordSearchParams{}
}

func (p *FinanceRecordSearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceRecordSearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *FinanceRecordSearchParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *FinanceRecordSearchParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *FinanceRecordSearchParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *FinanceRecordSearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceRecordSearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceRecordSearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *FinanceRecordSearchParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *FinanceRecordSearchParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:startTime: %s", p, err)
	}
	return err
}

func (p *FinanceRecordSearchParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:endTime: %s", p, err)
	}
	return err
}

func (p *FinanceRecordSearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceRecordSearchParams(%+v)", *p)
}

type FinanceUserInfo struct {
	Uid             int32 `thrift:"uid,1" json:"uid"`
	HistoryRecharge int64 `thrift:"historyRecharge,2" json:"historyRecharge"`
	Recharge        int64 `thrift:"recharge,3" json:"recharge"`
	HistoryReward   int64 `thrift:"historyReward,4" json:"historyReward"`
	Reward          int64 `thrift:"reward,5" json:"reward"`
	Balance         int64 `thrift:"balance,6" json:"balance"`
	TodayExpend     int64 `thrift:"todayExpend,7" json:"todayExpend"`
	TotalExpend     int64 `thrift:"totalExpend,8" json:"totalExpend"`
}

func NewFinanceUserInfo() *FinanceUserInfo {
	return &FinanceUserInfo{}
}

func (p *FinanceUserInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceUserInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *FinanceUserInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.HistoryRecharge = v
	}
	return nil
}

func (p *FinanceUserInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Recharge = v
	}
	return nil
}

func (p *FinanceUserInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.HistoryReward = v
	}
	return nil
}

func (p *FinanceUserInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Reward = v
	}
	return nil
}

func (p *FinanceUserInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Balance = v
	}
	return nil
}

func (p *FinanceUserInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TodayExpend = v
	}
	return nil
}

func (p *FinanceUserInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.TotalExpend = v
	}
	return nil
}

func (p *FinanceUserInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceUserInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceUserInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("historyRecharge", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:historyRecharge: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.HistoryRecharge)); err != nil {
		return fmt.Errorf("%T.historyRecharge (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:historyRecharge: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("recharge", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:recharge: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Recharge)); err != nil {
		return fmt.Errorf("%T.recharge (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:recharge: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("historyReward", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:historyReward: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.HistoryReward)); err != nil {
		return fmt.Errorf("%T.historyReward (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:historyReward: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reward", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:reward: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Reward)); err != nil {
		return fmt.Errorf("%T.reward (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:reward: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:balance: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("todayExpend", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:todayExpend: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TodayExpend)); err != nil {
		return fmt.Errorf("%T.todayExpend (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:todayExpend: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalExpend", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:totalExpend: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalExpend)); err != nil {
		return fmt.Errorf("%T.totalExpend (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:totalExpend: %s", p, err)
	}
	return err
}

func (p *FinanceUserInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceUserInfo(%+v)", *p)
}

type FinanceAmountRecord struct {
	Id     int64                   `thrift:"id,1" json:"id"`
	Uid    int32                   `thrift:"uid,2" json:"uid"`
	Time   int64                   `thrift:"time,3" json:"time"`
	TypeA1 FinanceAmountRecordType `thrift:"type,4" json:"type"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	RechargeIncome int64  `thrift:"rechargeIncome,10" json:"rechargeIncome"`
	RewardIncome   int64  `thrift:"rewardIncome,11" json:"rewardIncome"`
	RechargeExpend int64  `thrift:"rechargeExpend,12" json:"rechargeExpend"`
	RewardExpend   int64  `thrift:"rewardExpend,13" json:"rewardExpend"`
	Note           string `thrift:"note,14" json:"note"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewFinanceAmountRecord() *FinanceAmountRecord {
	return &FinanceAmountRecord{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceAmountRecord) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *FinanceAmountRecord) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FinanceAmountRecord) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceAmountRecord) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = FinanceAmountRecordType(v)
	}
	return nil
}

func (p *FinanceAmountRecord) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RechargeIncome = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.RewardIncome = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.RechargeExpend = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RewardExpend = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *FinanceAmountRecord) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *FinanceAmountRecord) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceAmountRecord"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceAmountRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:time: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *FinanceAmountRecord) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rechargeIncome", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:rechargeIncome: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RechargeIncome)); err != nil {
		return fmt.Errorf("%T.rechargeIncome (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:rechargeIncome: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rewardIncome", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:rewardIncome: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RewardIncome)); err != nil {
		return fmt.Errorf("%T.rewardIncome (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:rewardIncome: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rechargeExpend", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:rechargeExpend: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RechargeExpend)); err != nil {
		return fmt.Errorf("%T.rechargeExpend (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:rechargeExpend: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rewardExpend", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:rewardExpend: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RewardExpend)); err != nil {
		return fmt.Errorf("%T.rewardExpend (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:rewardExpend: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:note: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *FinanceAmountRecord) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *FinanceAmountRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceAmountRecord(%+v)", *p)
}

type AdAccountCostInfo struct {
	Id                    int64 `thrift:"id,1" json:"id"`
	Uid                   int32 `thrift:"uid,2" json:"uid"`
	OrderId               int32 `thrift:"orderId,3" json:"orderId"`
	IsMediaBuy            bool  `thrift:"isMediaBuy,4" json:"isMediaBuy"`
	MediaCost             int64 `thrift:"mediaCost,5" json:"mediaCost"`
	AgencyCost            int64 `thrift:"agencyCost,6" json:"agencyCost"`
	Expend                int64 `thrift:"expend,7" json:"expend"`
	Imp                   int64 `thrift:"imp,8" json:"imp"`
	Clk                   int64 `thrift:"clk,9" json:"clk"`
	Dt                    int32 `thrift:"dt,10" json:"dt"`
	FinanceAmountRecordId int64 `thrift:"financeAmountRecordId,11" json:"financeAmountRecordId"`
}

func NewAdAccountCostInfo() *AdAccountCostInfo {
	return &AdAccountCostInfo{}
}

func (p *AdAccountCostInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdAccountCostInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IsMediaBuy = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MediaCost = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AgencyCost = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Expend = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Imp = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Clk = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AdAccountCostInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.FinanceAmountRecordId = v
	}
	return nil
}

func (p *AdAccountCostInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdAccountCostInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdAccountCostInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:orderId: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isMediaBuy", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:isMediaBuy: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsMediaBuy)); err != nil {
		return fmt.Errorf("%T.isMediaBuy (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:isMediaBuy: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaCost", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mediaCost: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaCost)); err != nil {
		return fmt.Errorf("%T.mediaCost (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mediaCost: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agencyCost", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:agencyCost: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AgencyCost)); err != nil {
		return fmt.Errorf("%T.agencyCost (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:agencyCost: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expend", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:expend: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Expend)); err != nil {
		return fmt.Errorf("%T.expend (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:expend: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:imp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imp)); err != nil {
		return fmt.Errorf("%T.imp (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:imp: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:clk: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clk)); err != nil {
		return fmt.Errorf("%T.clk (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:clk: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:dt: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("financeAmountRecordId", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:financeAmountRecordId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FinanceAmountRecordId)); err != nil {
		return fmt.Errorf("%T.financeAmountRecordId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:financeAmountRecordId: %s", p, err)
	}
	return err
}

func (p *AdAccountCostInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdAccountCostInfo(%+v)", *p)
}
