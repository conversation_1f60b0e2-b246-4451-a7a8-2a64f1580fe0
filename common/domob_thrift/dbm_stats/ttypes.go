// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbm_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dbm_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dbm_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//统计查询排序字段枚举值
//
type OrderByField int64

const (
	OrderByField_CLK           OrderByField = 0
	OrderByField_IMP           OrderByField = 1
	OrderByField_ACT           OrderByField = 2
	OrderByField_SETTLED_PRICE OrderByField = 3
	OrderByField_CLK_RATIO     OrderByField = 4
	OrderByField_CPC           OrderByField = 5
	OrderByField_CPM           OrderByField = 6
	OrderByField_CPA           OrderByField = 7
	OrderByField_CVR           OrderByField = 8
)

func (p OrderByField) String() string {
	switch p {
	case OrderByField_CLK:
		return "OrderByField_CLK"
	case OrderByField_IMP:
		return "OrderByField_IMP"
	case OrderByField_ACT:
		return "OrderByField_ACT"
	case OrderByField_SETTLED_PRICE:
		return "OrderByField_SETTLED_PRICE"
	case OrderByField_CLK_RATIO:
		return "OrderByField_CLK_RATIO"
	case OrderByField_CPC:
		return "OrderByField_CPC"
	case OrderByField_CPM:
		return "OrderByField_CPM"
	case OrderByField_CPA:
		return "OrderByField_CPA"
	case OrderByField_CVR:
		return "OrderByField_CVR"
	}
	return "<UNSET>"
}

func OrderByFieldFromString(s string) (OrderByField, error) {
	switch s {
	case "OrderByField_CLK":
		return OrderByField_CLK, nil
	case "OrderByField_IMP":
		return OrderByField_IMP, nil
	case "OrderByField_ACT":
		return OrderByField_ACT, nil
	case "OrderByField_SETTLED_PRICE":
		return OrderByField_SETTLED_PRICE, nil
	case "OrderByField_CLK_RATIO":
		return OrderByField_CLK_RATIO, nil
	case "OrderByField_CPC":
		return OrderByField_CPC, nil
	case "OrderByField_CPM":
		return OrderByField_CPM, nil
	case "OrderByField_CPA":
		return OrderByField_CPA, nil
	case "OrderByField_CVR":
		return OrderByField_CVR, nil
	}
	return OrderByField(math.MinInt32 - 1), fmt.Errorf("not a valid OrderByField string")
}

//统计分析接口分析维度枚举值
//用于定向维度数据统计
//
type StatsAnalysisDimension int64

const (
	StatsAnalysisDimension_REGION         StatsAnalysisDimension = 10
	StatsAnalysisDimension_REGION_CITY    StatsAnalysisDimension = 11
	StatsAnalysisDimension_OS             StatsAnalysisDimension = 20
	StatsAnalysisDimension_OS_VERSION     StatsAnalysisDimension = 21
	StatsAnalysisDimension_USER_TAG       StatsAnalysisDimension = 30
	StatsAnalysisDimension_USER_TAG_SUB   StatsAnalysisDimension = 31
	StatsAnalysisDimension_MEDIA_CATEGORY StatsAnalysisDimension = 40
	StatsAnalysisDimension_ACCESS_TYPE    StatsAnalysisDimension = 50
)

func (p StatsAnalysisDimension) String() string {
	switch p {
	case StatsAnalysisDimension_REGION:
		return "StatsAnalysisDimension_REGION"
	case StatsAnalysisDimension_REGION_CITY:
		return "StatsAnalysisDimension_REGION_CITY"
	case StatsAnalysisDimension_OS:
		return "StatsAnalysisDimension_OS"
	case StatsAnalysisDimension_OS_VERSION:
		return "StatsAnalysisDimension_OS_VERSION"
	case StatsAnalysisDimension_USER_TAG:
		return "StatsAnalysisDimension_USER_TAG"
	case StatsAnalysisDimension_USER_TAG_SUB:
		return "StatsAnalysisDimension_USER_TAG_SUB"
	case StatsAnalysisDimension_MEDIA_CATEGORY:
		return "StatsAnalysisDimension_MEDIA_CATEGORY"
	case StatsAnalysisDimension_ACCESS_TYPE:
		return "StatsAnalysisDimension_ACCESS_TYPE"
	}
	return "<UNSET>"
}

func StatsAnalysisDimensionFromString(s string) (StatsAnalysisDimension, error) {
	switch s {
	case "StatsAnalysisDimension_REGION":
		return StatsAnalysisDimension_REGION, nil
	case "StatsAnalysisDimension_REGION_CITY":
		return StatsAnalysisDimension_REGION_CITY, nil
	case "StatsAnalysisDimension_OS":
		return StatsAnalysisDimension_OS, nil
	case "StatsAnalysisDimension_OS_VERSION":
		return StatsAnalysisDimension_OS_VERSION, nil
	case "StatsAnalysisDimension_USER_TAG":
		return StatsAnalysisDimension_USER_TAG, nil
	case "StatsAnalysisDimension_USER_TAG_SUB":
		return StatsAnalysisDimension_USER_TAG_SUB, nil
	case "StatsAnalysisDimension_MEDIA_CATEGORY":
		return StatsAnalysisDimension_MEDIA_CATEGORY, nil
	case "StatsAnalysisDimension_ACCESS_TYPE":
		return StatsAnalysisDimension_ACCESS_TYPE, nil
	}
	return StatsAnalysisDimension(math.MinInt32 - 1), fmt.Errorf("not a valid StatsAnalysisDimension string")
}

type ExceptionCode int64

const (
	ExceptionCode_QUERY_PARAM_ERROR    ExceptionCode = 10001
	ExceptionCode_QUERY_DATABASE_ERROR ExceptionCode = 10002
	ExceptionCode_QUERY_SERVER_ERROR   ExceptionCode = 20000
)

func (p ExceptionCode) String() string {
	switch p {
	case ExceptionCode_QUERY_PARAM_ERROR:
		return "ExceptionCode_QUERY_PARAM_ERROR"
	case ExceptionCode_QUERY_DATABASE_ERROR:
		return "ExceptionCode_QUERY_DATABASE_ERROR"
	case ExceptionCode_QUERY_SERVER_ERROR:
		return "ExceptionCode_QUERY_SERVER_ERROR"
	}
	return "<UNSET>"
}

func ExceptionCodeFromString(s string) (ExceptionCode, error) {
	switch s {
	case "ExceptionCode_QUERY_PARAM_ERROR":
		return ExceptionCode_QUERY_PARAM_ERROR, nil
	case "ExceptionCode_QUERY_DATABASE_ERROR":
		return ExceptionCode_QUERY_DATABASE_ERROR, nil
	case "ExceptionCode_QUERY_SERVER_ERROR":
		return ExceptionCode_QUERY_SERVER_ERROR, nil
	}
	return ExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid ExceptionCode string")
}

type StatsConditionParam struct {
	StartTime      int64   `thrift:"startTime,1" json:"startTime"`
	EndTime        int64   `thrift:"endTime,2" json:"endTime"`
	SponsorIds     []int32 `thrift:"sponsorIds,3" json:"sponsorIds"`
	OrderIds       []int32 `thrift:"orderIds,4" json:"orderIds"`
	CampaignIds    []int32 `thrift:"campaignIds,5" json:"campaignIds"`
	StrategyIds    []int32 `thrift:"strategyIds,6" json:"strategyIds"`
	CreativeIds    []int32 `thrift:"creativeIds,7" json:"creativeIds"`
	AgentUids      []int32 `thrift:"agentUids,8" json:"agentUids"`
	ExchangeIds    []int32 `thrift:"exchangeIds,9" json:"exchangeIds"`
	PlacementTypes []int32 `thrift:"placementTypes,10" json:"placementTypes"`
	RegionList     []int32 `thrift:"regionList,11" json:"regionList"`
	OsList         []int32 `thrift:"osList,12" json:"osList"`
	UserTagList    []int32 `thrift:"userTagList,13" json:"userTagList"`
	PromotionIds   []int32 `thrift:"promotionIds,14" json:"promotionIds"`
}

func NewStatsConditionParam() *StatsConditionParam {
	return &StatsConditionParam{}
}

func (p *StatsConditionParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SponsorIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.SponsorIds = append(p.SponsorIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.OrderIds = append(p.OrderIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StrategyIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.StrategyIds = append(p.StrategyIds, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.CreativeIds = append(p.CreativeIds, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AgentUids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.AgentUids = append(p.AgentUids, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExchangeIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.ExchangeIds = append(p.ExchangeIds, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlacementTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.PlacementTypes = append(p.PlacementTypes, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RegionList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.RegionList = append(p.RegionList, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OsList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.OsList = append(p.OsList, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.UserTagList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.UserTagList = append(p.UserTagList, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PromotionIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.PromotionIds = append(p.PromotionIds, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsConditionParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SponsorIds != nil {
		if err := oprot.WriteFieldBegin("sponsorIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sponsorIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SponsorIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SponsorIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sponsorIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.StrategyIds != nil {
		if err := oprot.WriteFieldBegin("strategyIds", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:strategyIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.StrategyIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StrategyIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:strategyIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.CreativeIds != nil {
		if err := oprot.WriteFieldBegin("creativeIds", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:creativeIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CreativeIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:creativeIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.AgentUids != nil {
		if err := oprot.WriteFieldBegin("agentUids", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:agentUids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AgentUids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AgentUids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:agentUids: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.ExchangeIds != nil {
		if err := oprot.WriteFieldBegin("exchangeIds", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:exchangeIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExchangeIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExchangeIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:exchangeIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.PlacementTypes != nil {
		if err := oprot.WriteFieldBegin("placementTypes", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:placementTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlacementTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlacementTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:placementTypes: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.RegionList != nil {
		if err := oprot.WriteFieldBegin("regionList", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:regionList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RegionList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RegionList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:regionList: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField12(oprot thrift.TProtocol) (err error) {
	if p.OsList != nil {
		if err := oprot.WriteFieldBegin("osList", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:osList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OsList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OsList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:osList: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField13(oprot thrift.TProtocol) (err error) {
	if p.UserTagList != nil {
		if err := oprot.WriteFieldBegin("userTagList", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:userTagList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.UserTagList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.UserTagList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:userTagList: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField14(oprot thrift.TProtocol) (err error) {
	if p.PromotionIds != nil {
		if err := oprot.WriteFieldBegin("promotionIds", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:promotionIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PromotionIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PromotionIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:promotionIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsConditionParam(%+v)", *p)
}

type StatsGroupByParam struct {
	Dt            bool `thrift:"dt,1" json:"dt"`
	Hr            bool `thrift:"hr,2" json:"hr"`
	SponsorId     bool `thrift:"sponsorId,3" json:"sponsorId"`
	OrderId       bool `thrift:"orderId,4" json:"orderId"`
	CampaignId    bool `thrift:"campaignId,5" json:"campaignId"`
	StrategyId    bool `thrift:"strategyId,6" json:"strategyId"`
	CreativeId    bool `thrift:"creativeId,7" json:"creativeId"`
	AgentUid      bool `thrift:"agentUid,8" json:"agentUid"`
	ExchangeId    bool `thrift:"exchangeId,9" json:"exchangeId"`
	PlacementType bool `thrift:"placementType,10" json:"placementType"`
	PromotionId   bool `thrift:"promotionId,11" json:"promotionId"`
}

func NewStatsGroupByParam() *StatsGroupByParam {
	return &StatsGroupByParam{}
}

func (p *StatsGroupByParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *StatsGroupByParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *StatsGroupByParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *StatsGroupByParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PlacementType = v
	}
	return nil
}

func (p *StatsGroupByParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *StatsGroupByParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsGroupByParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:orderId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:orderId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:campaignId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:campaignId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:strategyId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:strategyId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creativeId", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:creativeId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creativeId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:creativeId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.BOOL, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:agentUid: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:agentUid: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.BOOL, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:exchangeId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:exchangeId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:placementType: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:placementType: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.BOOL, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:promotionId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:promotionId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsGroupByParam(%+v)", *p)
}

type StatsOrderByParam struct {
	OrderBy OrderByField `thrift:"orderBy,1" json:"orderBy"`
	IsAsc   bool         `thrift:"isAsc,2" json:"isAsc"`
}

func NewStatsOrderByParam() *StatsOrderByParam {
	return &StatsOrderByParam{
		OrderBy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *StatsOrderByParam) IsSetOrderBy() bool {
	return int64(p.OrderBy) != math.MinInt32-1
}

func (p *StatsOrderByParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsOrderByParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderBy = OrderByField(v)
	}
	return nil
}

func (p *StatsOrderByParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsAsc = v
	}
	return nil
}

func (p *StatsOrderByParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsOrderByParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsOrderByParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err := oprot.WriteFieldBegin("orderBy", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:orderBy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
			return fmt.Errorf("%T.orderBy (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:orderBy: %s", p, err)
		}
	}
	return err
}

func (p *StatsOrderByParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isAsc", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isAsc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAsc)); err != nil {
		return fmt.Errorf("%T.isAsc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isAsc: %s", p, err)
	}
	return err
}

func (p *StatsOrderByParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsOrderByParam(%+v)", *p)
}

type DbmStatsData struct {
	Dt            int64 `thrift:"dt,1" json:"dt"`
	Hr            int32 `thrift:"hr,2" json:"hr"`
	SponsorId     int32 `thrift:"sponsorId,3" json:"sponsorId"`
	OrderId       int32 `thrift:"orderId,4" json:"orderId"`
	CampaignId    int32 `thrift:"campaignId,5" json:"campaignId"`
	StrategyId    int32 `thrift:"strategyId,6" json:"strategyId"`
	CreativeId    int32 `thrift:"creativeId,7" json:"creativeId"`
	RtbCampaignId int32 `thrift:"rtbCampaignId,8" json:"rtbCampaignId"`
	RtbStrategyId int32 `thrift:"rtbStrategyId,9" json:"rtbStrategyId"`
	RtbCreativeId int32 `thrift:"rtbCreativeId,10" json:"rtbCreativeId"`
	PromotionId   int32 `thrift:"promotionId,11" json:"promotionId"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Imp           int64 `thrift:"imp,20" json:"imp"`
	Clk           int32 `thrift:"clk,21" json:"clk"`
	Act           int32 `thrift:"act,22" json:"act"`
	Consume       int64 `thrift:"consume,23" json:"consume"`
	Price         int64 `thrift:"price,24" json:"price"`
	SettledPrice  int64 `thrift:"settledPrice,25" json:"settledPrice"`
	AgentUid      int32 `thrift:"agentUid,26" json:"agentUid"`
	ExchangeId    int32 `thrift:"exchangeId,27" json:"exchangeId"`
	PlacementType int32 `thrift:"placementType,28" json:"placementType"`
	// unused field # 29
	Region        int32 `thrift:"region,30" json:"region"`
	City          int32 `thrift:"city,31" json:"city"`
	Os            int32 `thrift:"os,32" json:"os"`
	Oisv          int32 `thrift:"oisv,33" json:"oisv"`
	UserTag       int32 `thrift:"userTag,34" json:"userTag"`
	UserTagSub    int32 `thrift:"userTagSub,35" json:"userTagSub"`
	MediaCategory int32 `thrift:"mediaCategory,36" json:"mediaCategory"`
	AccessType    int32 `thrift:"accessType,37" json:"accessType"`
	ClkRatio      int32 `thrift:"clkRatio,38" json:"clkRatio"`
	Cpc           int32 `thrift:"cpc,39" json:"cpc"`
	Cpm           int32 `thrift:"cpm,40" json:"cpm"`
	Cpa           int32 `thrift:"cpa,41" json:"cpa"`
	Cvr           int32 `thrift:"cvr,42" json:"cvr"`
}

func NewDbmStatsData() *DbmStatsData {
	return &DbmStatsData{}
}

func (p *DbmStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I64 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DbmStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *DbmStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *DbmStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *DbmStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *DbmStatsData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *DbmStatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *DbmStatsData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DbmStatsData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.RtbCampaignId = v
	}
	return nil
}

func (p *DbmStatsData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.RtbStrategyId = v
	}
	return nil
}

func (p *DbmStatsData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RtbCreativeId = v
	}
	return nil
}

func (p *DbmStatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *DbmStatsData) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Imp = v
	}
	return nil
}

func (p *DbmStatsData) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Clk = v
	}
	return nil
}

func (p *DbmStatsData) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Act = v
	}
	return nil
}

func (p *DbmStatsData) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Consume = v
	}
	return nil
}

func (p *DbmStatsData) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *DbmStatsData) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.SettledPrice = v
	}
	return nil
}

func (p *DbmStatsData) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *DbmStatsData) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *DbmStatsData) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.PlacementType = v
	}
	return nil
}

func (p *DbmStatsData) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Region = v
	}
	return nil
}

func (p *DbmStatsData) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.City = v
	}
	return nil
}

func (p *DbmStatsData) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Os = v
	}
	return nil
}

func (p *DbmStatsData) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Oisv = v
	}
	return nil
}

func (p *DbmStatsData) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.UserTag = v
	}
	return nil
}

func (p *DbmStatsData) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.UserTagSub = v
	}
	return nil
}

func (p *DbmStatsData) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.MediaCategory = v
	}
	return nil
}

func (p *DbmStatsData) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AccessType = v
	}
	return nil
}

func (p *DbmStatsData) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.ClkRatio = v
	}
	return nil
}

func (p *DbmStatsData) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Cpc = v
	}
	return nil
}

func (p *DbmStatsData) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Cpm = v
	}
	return nil
}

func (p *DbmStatsData) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Cpa = v
	}
	return nil
}

func (p *DbmStatsData) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Cvr = v
	}
	return nil
}

func (p *DbmStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DbmStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DbmStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:orderId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:campaignId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:strategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:strategyId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creativeId", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:creativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creativeId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:creativeId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbCampaignId", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:rtbCampaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCampaignId)); err != nil {
		return fmt.Errorf("%T.rtbCampaignId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:rtbCampaignId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbStrategyId", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:rtbStrategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbStrategyId)); err != nil {
		return fmt.Errorf("%T.rtbStrategyId (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:rtbStrategyId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rtbCreativeId", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:rtbCreativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RtbCreativeId)); err != nil {
		return fmt.Errorf("%T.rtbCreativeId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:rtbCreativeId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:promotionId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:imp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imp)); err != nil {
		return fmt.Errorf("%T.imp (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:imp: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:clk: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Clk)); err != nil {
		return fmt.Errorf("%T.clk (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:clk: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:act: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Act)); err != nil {
		return fmt.Errorf("%T.act (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:act: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consume", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:consume: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consume)); err != nil {
		return fmt.Errorf("%T.consume (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:consume: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:price: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settledPrice", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:settledPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettledPrice)); err != nil {
		return fmt.Errorf("%T.settledPrice (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:settledPrice: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:agentUid: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:exchangeId: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:placementType: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:region: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:city: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.City)); err != nil {
		return fmt.Errorf("%T.city (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:city: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:os: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oisv", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:oisv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Oisv)); err != nil {
		return fmt.Errorf("%T.oisv (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:oisv: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userTag", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:userTag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserTag)); err != nil {
		return fmt.Errorf("%T.userTag (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:userTag: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userTagSub", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:userTagSub: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserTagSub)); err != nil {
		return fmt.Errorf("%T.userTagSub (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:userTagSub: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaCategory", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:mediaCategory: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaCategory)); err != nil {
		return fmt.Errorf("%T.mediaCategory (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:mediaCategory: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accessType", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:accessType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessType)); err != nil {
		return fmt.Errorf("%T.accessType (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:accessType: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkRatio", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:clkRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkRatio)); err != nil {
		return fmt.Errorf("%T.clkRatio (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:clkRatio: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpc", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:cpc: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cpc)); err != nil {
		return fmt.Errorf("%T.cpc (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:cpc: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpm", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:cpm: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cpm)); err != nil {
		return fmt.Errorf("%T.cpm (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:cpm: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpa", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:cpa: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cpa)); err != nil {
		return fmt.Errorf("%T.cpa (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:cpa: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cvr", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:cvr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cvr)); err != nil {
		return fmt.Errorf("%T.cvr (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:cvr: %s", p, err)
	}
	return err
}

func (p *DbmStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DbmStatsData(%+v)", *p)
}

type StatsQueryResult struct {
	TotalCount int32           `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32           `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32           `thrift:"offset,3" json:"offset"`
	Limit      int32           `thrift:"limit,4" json:"limit"`
	Result     []*DbmStatsData `thrift:"result,5" json:"result"`
}

func NewStatsQueryResult() *StatsQueryResult {
	return &StatsQueryResult{}
}

func (p *StatsQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *StatsQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *StatsQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *StatsQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *StatsQueryResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*DbmStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem12 := NewDbmStatsData()
		if err := _elem12.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem12)
		}
		p.Result = append(p.Result, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *StatsQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsQueryResult(%+v)", *p)
}

type DbmStatsServerException struct {
	Code    ExceptionCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewDbmStatsServerException() *DbmStatsServerException {
	return &DbmStatsServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DbmStatsServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DbmStatsServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DbmStatsServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ExceptionCode(v)
	}
	return nil
}

func (p *DbmStatsServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DbmStatsServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DbmStatsServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DbmStatsServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DbmStatsServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DbmStatsServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DbmStatsServerException(%+v)", *p)
}
