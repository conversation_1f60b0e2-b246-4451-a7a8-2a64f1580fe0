// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dbm_stats"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  StatsQueryResult queryStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, i32 offset, i32 limit)")
	fmt.Fprintln(os.<PERSON>, "  StatsQueryResult queryMarkupStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryStatsAnalysisData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, StatsAnalysisDimension dimension, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryMarkupStatsAnalysisData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, StatsAnalysisDimension dimension, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dbm_stats.NewDbmStatsServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryStatsData requires 5 args")
			flag.Usage()
		}
		arg31 := flag.Arg(1)
		mbTrans32 := thrift.NewTMemoryBufferLen(len(arg31))
		defer mbTrans32.Close()
		_, err33 := mbTrans32.WriteString(arg31)
		if err33 != nil {
			Usage()
			return
		}
		factory34 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt35 := factory34.GetProtocol(mbTrans32)
		argvalue0 := dbm_stats.NewRequestHeader()
		err36 := argvalue0.Read(jsProt35)
		if err36 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg37 := flag.Arg(2)
		mbTrans38 := thrift.NewTMemoryBufferLen(len(arg37))
		defer mbTrans38.Close()
		_, err39 := mbTrans38.WriteString(arg37)
		if err39 != nil {
			Usage()
			return
		}
		factory40 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt41 := factory40.GetProtocol(mbTrans38)
		argvalue1 := dbm_stats.NewStatsConditionParam()
		err42 := argvalue1.Read(jsProt41)
		if err42 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg43 := flag.Arg(3)
		mbTrans44 := thrift.NewTMemoryBufferLen(len(arg43))
		defer mbTrans44.Close()
		_, err45 := mbTrans44.WriteString(arg43)
		if err45 != nil {
			Usage()
			return
		}
		factory46 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt47 := factory46.GetProtocol(mbTrans44)
		argvalue2 := dbm_stats.NewStatsGroupByParam()
		err48 := argvalue2.Read(jsProt47)
		if err48 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err49 := (strconv.Atoi(flag.Arg(4)))
		if err49 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err50 := (strconv.Atoi(flag.Arg(5)))
		if err50 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryMarkupStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryMarkupStatsData requires 5 args")
			flag.Usage()
		}
		arg51 := flag.Arg(1)
		mbTrans52 := thrift.NewTMemoryBufferLen(len(arg51))
		defer mbTrans52.Close()
		_, err53 := mbTrans52.WriteString(arg51)
		if err53 != nil {
			Usage()
			return
		}
		factory54 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt55 := factory54.GetProtocol(mbTrans52)
		argvalue0 := dbm_stats.NewRequestHeader()
		err56 := argvalue0.Read(jsProt55)
		if err56 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg57 := flag.Arg(2)
		mbTrans58 := thrift.NewTMemoryBufferLen(len(arg57))
		defer mbTrans58.Close()
		_, err59 := mbTrans58.WriteString(arg57)
		if err59 != nil {
			Usage()
			return
		}
		factory60 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt61 := factory60.GetProtocol(mbTrans58)
		argvalue1 := dbm_stats.NewStatsConditionParam()
		err62 := argvalue1.Read(jsProt61)
		if err62 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg63 := flag.Arg(3)
		mbTrans64 := thrift.NewTMemoryBufferLen(len(arg63))
		defer mbTrans64.Close()
		_, err65 := mbTrans64.WriteString(arg63)
		if err65 != nil {
			Usage()
			return
		}
		factory66 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt67 := factory66.GetProtocol(mbTrans64)
		argvalue2 := dbm_stats.NewStatsGroupByParam()
		err68 := argvalue2.Read(jsProt67)
		if err68 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err69 := (strconv.Atoi(flag.Arg(4)))
		if err69 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err70 := (strconv.Atoi(flag.Arg(5)))
		if err70 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryMarkupStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryStatsAnalysisData":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryStatsAnalysisData requires 7 args")
			flag.Usage()
		}
		arg71 := flag.Arg(1)
		mbTrans72 := thrift.NewTMemoryBufferLen(len(arg71))
		defer mbTrans72.Close()
		_, err73 := mbTrans72.WriteString(arg71)
		if err73 != nil {
			Usage()
			return
		}
		factory74 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt75 := factory74.GetProtocol(mbTrans72)
		argvalue0 := dbm_stats.NewRequestHeader()
		err76 := argvalue0.Read(jsProt75)
		if err76 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg77 := flag.Arg(2)
		mbTrans78 := thrift.NewTMemoryBufferLen(len(arg77))
		defer mbTrans78.Close()
		_, err79 := mbTrans78.WriteString(arg77)
		if err79 != nil {
			Usage()
			return
		}
		factory80 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt81 := factory80.GetProtocol(mbTrans78)
		argvalue1 := dbm_stats.NewStatsConditionParam()
		err82 := argvalue1.Read(jsProt81)
		if err82 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg83 := flag.Arg(3)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		argvalue2 := dbm_stats.NewStatsGroupByParam()
		err88 := argvalue2.Read(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg89 := flag.Arg(4)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		argvalue3 := dbm_stats.NewStatsOrderByParam()
		err94 := argvalue3.Read(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := dbm_stats.StatsAnalysisDimension(tmp4)
		value4 := argvalue4
		tmp5, err95 := (strconv.Atoi(flag.Arg(6)))
		if err95 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err96 := (strconv.Atoi(flag.Arg(7)))
		if err96 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		fmt.Print(client.QueryStatsAnalysisData(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "queryMarkupStatsAnalysisData":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryMarkupStatsAnalysisData requires 7 args")
			flag.Usage()
		}
		arg97 := flag.Arg(1)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue0 := dbm_stats.NewRequestHeader()
		err102 := argvalue0.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg103 := flag.Arg(2)
		mbTrans104 := thrift.NewTMemoryBufferLen(len(arg103))
		defer mbTrans104.Close()
		_, err105 := mbTrans104.WriteString(arg103)
		if err105 != nil {
			Usage()
			return
		}
		factory106 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt107 := factory106.GetProtocol(mbTrans104)
		argvalue1 := dbm_stats.NewStatsConditionParam()
		err108 := argvalue1.Read(jsProt107)
		if err108 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg109 := flag.Arg(3)
		mbTrans110 := thrift.NewTMemoryBufferLen(len(arg109))
		defer mbTrans110.Close()
		_, err111 := mbTrans110.WriteString(arg109)
		if err111 != nil {
			Usage()
			return
		}
		factory112 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt113 := factory112.GetProtocol(mbTrans110)
		argvalue2 := dbm_stats.NewStatsGroupByParam()
		err114 := argvalue2.Read(jsProt113)
		if err114 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg115 := flag.Arg(4)
		mbTrans116 := thrift.NewTMemoryBufferLen(len(arg115))
		defer mbTrans116.Close()
		_, err117 := mbTrans116.WriteString(arg115)
		if err117 != nil {
			Usage()
			return
		}
		factory118 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt119 := factory118.GetProtocol(mbTrans116)
		argvalue3 := dbm_stats.NewStatsOrderByParam()
		err120 := argvalue3.Read(jsProt119)
		if err120 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := dbm_stats.StatsAnalysisDimension(tmp4)
		value4 := argvalue4
		tmp5, err121 := (strconv.Atoi(flag.Arg(6)))
		if err121 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err122 := (strconv.Atoi(flag.Arg(7)))
		if err122 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		fmt.Print(client.QueryMarkupStatsAnalysisData(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
