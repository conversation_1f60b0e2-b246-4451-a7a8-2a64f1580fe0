// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbm_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dbm_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dbm_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type DbmStatsServer interface { //DBM Stats 统计服务接口定义
	//

	// 查询统计信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error)
	// 查询markup统计信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryMarkupStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error)
	// 查询定向维度统计分析信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Order: 排序条件 *
	//  - Dimension: 统计分析维度
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryStatsAnalysisData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, dimension StatsAnalysisDimension, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error)
	// 查询定向维度markup统计分析信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Order: 排序条件 *
	//  - Dimension: 统计分析维度
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryMarkupStatsAnalysisData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, dimension StatsAnalysisDimension, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error)
}

//DBM Stats 统计服务接口定义
//
type DbmStatsServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDbmStatsServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DbmStatsServerClient {
	return &DbmStatsServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDbmStatsServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DbmStatsServerClient {
	return &DbmStatsServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询统计信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DbmStatsServerClient) QueryStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error) {
	if err = p.sendQueryStatsData(header, condition, group, offset, limit); err != nil {
		return
	}
	return p.recvQueryStatsData()
}

func (p *DbmStatsServerClient) sendQueryStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args13 := NewQueryStatsDataArgs()
	args13.Header = header
	args13.Condition = condition
	args13.Group = group
	args13.Offset = offset
	args13.Limit = limit
	if err = args13.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DbmStatsServerClient) recvQueryStatsData() (value *StatsQueryResult, e *DbmStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error15 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error16 error
		error16, err = error15.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error16
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result14 := NewQueryStatsDataResult()
	if err = result14.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result14.Success
	if result14.E != nil {
		e = result14.E
	}
	return
}

// 查询markup统计信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DbmStatsServerClient) QueryMarkupStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error) {
	if err = p.sendQueryMarkupStatsData(header, condition, group, offset, limit); err != nil {
		return
	}
	return p.recvQueryMarkupStatsData()
}

func (p *DbmStatsServerClient) sendQueryMarkupStatsData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryMarkupStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args17 := NewQueryMarkupStatsDataArgs()
	args17.Header = header
	args17.Condition = condition
	args17.Group = group
	args17.Offset = offset
	args17.Limit = limit
	if err = args17.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DbmStatsServerClient) recvQueryMarkupStatsData() (value *StatsQueryResult, e *DbmStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error19 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error20 error
		error20, err = error19.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error20
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result18 := NewQueryMarkupStatsDataResult()
	if err = result18.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result18.Success
	if result18.E != nil {
		e = result18.E
	}
	return
}

// 查询定向维度统计分析信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Order: 排序条件 *
//  - Dimension: 统计分析维度
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DbmStatsServerClient) QueryStatsAnalysisData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, dimension StatsAnalysisDimension, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error) {
	if err = p.sendQueryStatsAnalysisData(header, condition, group, order, dimension, offset, limit); err != nil {
		return
	}
	return p.recvQueryStatsAnalysisData()
}

func (p *DbmStatsServerClient) sendQueryStatsAnalysisData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, dimension StatsAnalysisDimension, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsAnalysisData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args21 := NewQueryStatsAnalysisDataArgs()
	args21.Header = header
	args21.Condition = condition
	args21.Group = group
	args21.Order = order
	args21.Dimension = dimension
	args21.Offset = offset
	args21.Limit = limit
	if err = args21.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DbmStatsServerClient) recvQueryStatsAnalysisData() (value *StatsQueryResult, e *DbmStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error23 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error24 error
		error24, err = error23.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error24
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result22 := NewQueryStatsAnalysisDataResult()
	if err = result22.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result22.Success
	if result22.E != nil {
		e = result22.E
	}
	return
}

// 查询定向维度markup统计分析信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Order: 排序条件 *
//  - Dimension: 统计分析维度
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *DbmStatsServerClient) QueryMarkupStatsAnalysisData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, dimension StatsAnalysisDimension, offset int32, limit int32) (r *StatsQueryResult, e *DbmStatsServerException, err error) {
	if err = p.sendQueryMarkupStatsAnalysisData(header, condition, group, order, dimension, offset, limit); err != nil {
		return
	}
	return p.recvQueryMarkupStatsAnalysisData()
}

func (p *DbmStatsServerClient) sendQueryMarkupStatsAnalysisData(header *common.RequestHeader, condition *StatsConditionParam, group *StatsGroupByParam, order *StatsOrderByParam, dimension StatsAnalysisDimension, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryMarkupStatsAnalysisData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args25 := NewQueryMarkupStatsAnalysisDataArgs()
	args25.Header = header
	args25.Condition = condition
	args25.Group = group
	args25.Order = order
	args25.Dimension = dimension
	args25.Offset = offset
	args25.Limit = limit
	if err = args25.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DbmStatsServerClient) recvQueryMarkupStatsAnalysisData() (value *StatsQueryResult, e *DbmStatsServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error27 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error28 error
		error28, err = error27.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error28
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result26 := NewQueryMarkupStatsAnalysisDataResult()
	if err = result26.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result26.Success
	if result26.E != nil {
		e = result26.E
	}
	return
}

type DbmStatsServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DbmStatsServer
}

func (p *DbmStatsServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DbmStatsServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DbmStatsServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDbmStatsServerProcessor(handler DbmStatsServer) *DbmStatsServerProcessor {

	self29 := &DbmStatsServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self29.processorMap["queryStatsData"] = &dbmStatsServerProcessorQueryStatsData{handler: handler}
	self29.processorMap["queryMarkupStatsData"] = &dbmStatsServerProcessorQueryMarkupStatsData{handler: handler}
	self29.processorMap["queryStatsAnalysisData"] = &dbmStatsServerProcessorQueryStatsAnalysisData{handler: handler}
	self29.processorMap["queryMarkupStatsAnalysisData"] = &dbmStatsServerProcessorQueryMarkupStatsAnalysisData{handler: handler}
	return self29
}

func (p *DbmStatsServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x30 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x30.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x30

}

type dbmStatsServerProcessorQueryStatsData struct {
	handler DbmStatsServer
}

func (p *dbmStatsServerProcessorQueryStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsDataResult()
	if result.Success, result.E, err = p.handler.QueryStatsData(args.Header, args.Condition, args.Group, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dbmStatsServerProcessorQueryMarkupStatsData struct {
	handler DbmStatsServer
}

func (p *dbmStatsServerProcessorQueryMarkupStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryMarkupStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryMarkupStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryMarkupStatsDataResult()
	if result.Success, result.E, err = p.handler.QueryMarkupStatsData(args.Header, args.Condition, args.Group, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryMarkupStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryMarkupStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryMarkupStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dbmStatsServerProcessorQueryStatsAnalysisData struct {
	handler DbmStatsServer
}

func (p *dbmStatsServerProcessorQueryStatsAnalysisData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsAnalysisDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsAnalysisData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsAnalysisDataResult()
	if result.Success, result.E, err = p.handler.QueryStatsAnalysisData(args.Header, args.Condition, args.Group, args.Order, args.Dimension, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsAnalysisData: "+err.Error())
		oprot.WriteMessageBegin("queryStatsAnalysisData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsAnalysisData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dbmStatsServerProcessorQueryMarkupStatsAnalysisData struct {
	handler DbmStatsServer
}

func (p *dbmStatsServerProcessorQueryMarkupStatsAnalysisData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryMarkupStatsAnalysisDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryMarkupStatsAnalysisData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryMarkupStatsAnalysisDataResult()
	if result.Success, result.E, err = p.handler.QueryMarkupStatsAnalysisData(args.Header, args.Condition, args.Group, args.Order, args.Dimension, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryMarkupStatsAnalysisData: "+err.Error())
		oprot.WriteMessageBegin("queryMarkupStatsAnalysisData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryMarkupStatsAnalysisData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type QueryStatsDataArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam    `thrift:"group,3" json:"group"`
	// unused field # 4
	Offset int32 `thrift:"offset,5" json:"offset"`
	Limit  int32 `thrift:"limit,6" json:"limit"`
}

func NewQueryStatsDataArgs() *QueryStatsDataArgs {
	return &QueryStatsDataArgs{}
}

func (p *QueryStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataArgs(%+v)", *p)
}

type QueryStatsDataResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DbmStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryStatsDataResult() *QueryStatsDataResult {
	return &QueryStatsDataResult{}
}

func (p *QueryStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDbmStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataResult(%+v)", *p)
}

type QueryMarkupStatsDataArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam  `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam    `thrift:"group,3" json:"group"`
	// unused field # 4
	Offset int32 `thrift:"offset,5" json:"offset"`
	Limit  int32 `thrift:"limit,6" json:"limit"`
}

func NewQueryMarkupStatsDataArgs() *QueryMarkupStatsDataArgs {
	return &QueryMarkupStatsDataArgs{}
}

func (p *QueryMarkupStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryMarkupStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryMarkupStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryMarkupStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryMarkupStatsDataArgs(%+v)", *p)
}

type QueryMarkupStatsDataResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DbmStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryMarkupStatsDataResult() *QueryMarkupStatsDataResult {
	return &QueryMarkupStatsDataResult{}
}

func (p *QueryMarkupStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryMarkupStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDbmStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryMarkupStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryMarkupStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryMarkupStatsDataResult(%+v)", *p)
}

type QueryStatsAnalysisDataArgs struct {
	Header    *common.RequestHeader  `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam   `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam     `thrift:"group,3" json:"group"`
	Order     *StatsOrderByParam     `thrift:"order,4" json:"order"`
	Dimension StatsAnalysisDimension `thrift:"dimension,5" json:"dimension"`
	Offset    int32                  `thrift:"offset,6" json:"offset"`
	Limit     int32                  `thrift:"limit,7" json:"limit"`
}

func NewQueryStatsAnalysisDataArgs() *QueryStatsAnalysisDataArgs {
	return &QueryStatsAnalysisDataArgs{
		Dimension: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryStatsAnalysisDataArgs) IsSetDimension() bool {
	return int64(p.Dimension) != math.MinInt32-1
}

func (p *QueryStatsAnalysisDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField4(iprot thrift.TProtocol) error {
	p.Order = NewStatsOrderByParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Dimension = StatsAnalysisDimension(v)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsAnalysisData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsAnalysisDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDimension() {
		if err := oprot.WriteFieldBegin("dimension", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:dimension: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Dimension)); err != nil {
			return fmt.Errorf("%T.dimension (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:dimension: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:offset: %s", p, err)
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:limit: %s", p, err)
	}
	return err
}

func (p *QueryStatsAnalysisDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsAnalysisDataArgs(%+v)", *p)
}

type QueryStatsAnalysisDataResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DbmStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryStatsAnalysisDataResult() *QueryStatsAnalysisDataResult {
	return &QueryStatsAnalysisDataResult{}
}

func (p *QueryStatsAnalysisDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsAnalysisDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsAnalysisDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDbmStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsAnalysisDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsAnalysisData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsAnalysisDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsAnalysisDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsAnalysisDataResult(%+v)", *p)
}

type QueryMarkupStatsAnalysisDataArgs struct {
	Header    *common.RequestHeader  `thrift:"header,1" json:"header"`
	Condition *StatsConditionParam   `thrift:"condition,2" json:"condition"`
	Group     *StatsGroupByParam     `thrift:"group,3" json:"group"`
	Order     *StatsOrderByParam     `thrift:"order,4" json:"order"`
	Dimension StatsAnalysisDimension `thrift:"dimension,5" json:"dimension"`
	Offset    int32                  `thrift:"offset,6" json:"offset"`
	Limit     int32                  `thrift:"limit,7" json:"limit"`
}

func NewQueryMarkupStatsAnalysisDataArgs() *QueryMarkupStatsAnalysisDataArgs {
	return &QueryMarkupStatsAnalysisDataArgs{
		Dimension: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryMarkupStatsAnalysisDataArgs) IsSetDimension() bool {
	return int64(p.Dimension) != math.MinInt32-1
}

func (p *QueryMarkupStatsAnalysisDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField4(iprot thrift.TProtocol) error {
	p.Order = NewStatsOrderByParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Dimension = StatsAnalysisDimension(v)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryMarkupStatsAnalysisData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDimension() {
		if err := oprot.WriteFieldBegin("dimension", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:dimension: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Dimension)); err != nil {
			return fmt.Errorf("%T.dimension (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:dimension: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:offset: %s", p, err)
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:limit: %s", p, err)
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryMarkupStatsAnalysisDataArgs(%+v)", *p)
}

type QueryMarkupStatsAnalysisDataResult struct {
	Success *StatsQueryResult        `thrift:"success,0" json:"success"`
	E       *DbmStatsServerException `thrift:"e,1" json:"e"`
}

func NewQueryMarkupStatsAnalysisDataResult() *QueryMarkupStatsAnalysisDataResult {
	return &QueryMarkupStatsAnalysisDataResult{}
}

func (p *QueryMarkupStatsAnalysisDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDbmStatsServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryMarkupStatsAnalysisData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryMarkupStatsAnalysisDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryMarkupStatsAnalysisDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryMarkupStatsAnalysisDataResult(%+v)", *p)
}
