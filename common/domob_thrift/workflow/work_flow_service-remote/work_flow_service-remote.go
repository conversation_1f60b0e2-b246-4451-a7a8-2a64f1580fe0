// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"workflow"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>der<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ProcessDefinitionQueryResult listProcessDefinitions(RequestHeader header, string category, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  ProcessDefinitionThrift getProcessDefinitionById(RequestHeader header, string processDefinitionId)")
	fmt.Fprintln(os.Stderr, "  ProcessInstanceQueryResult listProcessInstancesByStatus(RequestHeader header, QueryConditionInstanceStatus status, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  ProcessInstanceQueryResult listProcessInstancesByBusinessKey(RequestHeader header, string businessKey, QueryConditionInstanceStatus status, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  ProcessInstanceQueryResult listProcessInstancesByStartUserId(RequestHeader header, string startUserId, QueryConditionInstanceStatus status, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  TaskSummaryThrift getTaskSummaryByUserId(RequestHeader header, string userId,  groupIds,  taskDefinitionKeys)")
	fmt.Fprintln(os.Stderr, "  TaskQueryResult listTasksByRole(RequestHeader header, QueryConditionRoleType roleType, string role, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  TaskQueryResult listTasksByTaskDefinitionKey(RequestHeader header, string taskDefinitionKey, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  TaskQueryResult listHistoricTaskByAssigneeAndStatus(RequestHeader header, string assignee, QueryConditionInstanceStatus status, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  TaskThrift getTaskById(RequestHeader header, string taskId)")
	fmt.Fprintln(os.Stderr, "   getTaskFormPropertiesByTaskId(RequestHeader header, string taskId)")
	fmt.Fprintln(os.Stderr, "   getProcessFormPropertiesByDefinitionId(RequestHeader header, string processDefinitionId)")
	fmt.Fprintln(os.Stderr, "  ProcessInstanceThrift startProcessInstanceByProcessDefinitionId(RequestHeader header, string processDefinitionId,  variables, string userId)")
	fmt.Fprintln(os.Stderr, "  ProcessInstanceThrift startProcessInstanceByProcessDefinitionKey(RequestHeader header, string processDefinitionKey,  variables, string userId)")
	fmt.Fprintln(os.Stderr, "  void claimTask(RequestHeader header, string taskId, string userId)")
	fmt.Fprintln(os.Stderr, "  void completeTask(RequestHeader header, string taskId,  variables)")
	fmt.Fprintln(os.Stderr, "  TaskQueryResult listTasksByScript(RequestHeader header, string script, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  TaskQueryResult listHistoricTasksByScript(RequestHeader header, string script, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  ProcessInstanceQueryResult listProcessInstanceByScript(RequestHeader header, string script, NumberInt start, NumberInt size, string sort, bool isasc)")
	fmt.Fprintln(os.Stderr, "  void processDefinitionDeploy(RequestHeader header, string resouceName, string resouceContent)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := workflow.NewWorkFlowServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "listProcessDefinitions":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListProcessDefinitions requires 6 args")
			flag.Usage()
		}
		arg92 := flag.Arg(1)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		argvalue0 := workflow.NewRequestHeader()
		err97 := argvalue0.Read(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err99 := (strconv.Atoi(flag.Arg(3)))
		if err99 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := workflow.NumberInt(argvalue2)
		tmp3, err100 := (strconv.Atoi(flag.Arg(4)))
		if err100 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListProcessDefinitions(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getProcessDefinitionById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProcessDefinitionById requires 2 args")
			flag.Usage()
		}
		arg103 := flag.Arg(1)
		mbTrans104 := thrift.NewTMemoryBufferLen(len(arg103))
		defer mbTrans104.Close()
		_, err105 := mbTrans104.WriteString(arg103)
		if err105 != nil {
			Usage()
			return
		}
		factory106 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt107 := factory106.GetProtocol(mbTrans104)
		argvalue0 := workflow.NewRequestHeader()
		err108 := argvalue0.Read(jsProt107)
		if err108 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetProcessDefinitionById(value0, value1))
		fmt.Print("\n")
		break
	case "listProcessInstancesByStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListProcessInstancesByStatus requires 6 args")
			flag.Usage()
		}
		arg110 := flag.Arg(1)
		mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
		defer mbTrans111.Close()
		_, err112 := mbTrans111.WriteString(arg110)
		if err112 != nil {
			Usage()
			return
		}
		factory113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt114 := factory113.GetProtocol(mbTrans111)
		argvalue0 := workflow.NewRequestHeader()
		err115 := argvalue0.Read(jsProt114)
		if err115 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := workflow.QueryConditionInstanceStatus(tmp1)
		value1 := workflow.QueryConditionInstanceStatus(argvalue1)
		tmp2, err116 := (strconv.Atoi(flag.Arg(3)))
		if err116 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := workflow.NumberInt(argvalue2)
		tmp3, err117 := (strconv.Atoi(flag.Arg(4)))
		if err117 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListProcessInstancesByStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listProcessInstancesByBusinessKey":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListProcessInstancesByBusinessKey requires 7 args")
			flag.Usage()
		}
		arg120 := flag.Arg(1)
		mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
		defer mbTrans121.Close()
		_, err122 := mbTrans121.WriteString(arg120)
		if err122 != nil {
			Usage()
			return
		}
		factory123 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt124 := factory123.GetProtocol(mbTrans121)
		argvalue0 := workflow.NewRequestHeader()
		err125 := argvalue0.Read(jsProt124)
		if err125 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := workflow.QueryConditionInstanceStatus(tmp2)
		value2 := workflow.QueryConditionInstanceStatus(argvalue2)
		tmp3, err127 := (strconv.Atoi(flag.Arg(4)))
		if err127 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		tmp4, err128 := (strconv.Atoi(flag.Arg(5)))
		if err128 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := workflow.NumberInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListProcessInstancesByBusinessKey(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listProcessInstancesByStartUserId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListProcessInstancesByStartUserId requires 7 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := workflow.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := workflow.QueryConditionInstanceStatus(tmp2)
		value2 := workflow.QueryConditionInstanceStatus(argvalue2)
		tmp3, err138 := (strconv.Atoi(flag.Arg(4)))
		if err138 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		tmp4, err139 := (strconv.Atoi(flag.Arg(5)))
		if err139 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := workflow.NumberInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListProcessInstancesByStartUserId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getTaskSummaryByUserId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetTaskSummaryByUserId requires 4 args")
			flag.Usage()
		}
		arg142 := flag.Arg(1)
		mbTrans143 := thrift.NewTMemoryBufferLen(len(arg142))
		defer mbTrans143.Close()
		_, err144 := mbTrans143.WriteString(arg142)
		if err144 != nil {
			Usage()
			return
		}
		factory145 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt146 := factory145.GetProtocol(mbTrans143)
		argvalue0 := workflow.NewRequestHeader()
		err147 := argvalue0.Read(jsProt146)
		if err147 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg149 := flag.Arg(3)
		mbTrans150 := thrift.NewTMemoryBufferLen(len(arg149))
		defer mbTrans150.Close()
		_, err151 := mbTrans150.WriteString(arg149)
		if err151 != nil {
			Usage()
			return
		}
		factory152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt153 := factory152.GetProtocol(mbTrans150)
		containerStruct2 := workflow.NewGetTaskSummaryByUserIdArgs()
		err154 := containerStruct2.ReadField3(jsProt153)
		if err154 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.GroupIds
		value2 := argvalue2
		arg155 := flag.Arg(4)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		containerStruct3 := workflow.NewGetTaskSummaryByUserIdArgs()
		err160 := containerStruct3.ReadField4(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.TaskDefinitionKeys
		value3 := argvalue3
		fmt.Print(client.GetTaskSummaryByUserId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listTasksByRole":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListTasksByRole requires 7 args")
			flag.Usage()
		}
		arg161 := flag.Arg(1)
		mbTrans162 := thrift.NewTMemoryBufferLen(len(arg161))
		defer mbTrans162.Close()
		_, err163 := mbTrans162.WriteString(arg161)
		if err163 != nil {
			Usage()
			return
		}
		factory164 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt165 := factory164.GetProtocol(mbTrans162)
		argvalue0 := workflow.NewRequestHeader()
		err166 := argvalue0.Read(jsProt165)
		if err166 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := workflow.QueryConditionRoleType(tmp1)
		value1 := workflow.QueryConditionRoleType(argvalue1)
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		tmp3, err168 := (strconv.Atoi(flag.Arg(4)))
		if err168 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		tmp4, err169 := (strconv.Atoi(flag.Arg(5)))
		if err169 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := workflow.NumberInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListTasksByRole(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listTasksByTaskDefinitionKey":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListTasksByTaskDefinitionKey requires 6 args")
			flag.Usage()
		}
		arg172 := flag.Arg(1)
		mbTrans173 := thrift.NewTMemoryBufferLen(len(arg172))
		defer mbTrans173.Close()
		_, err174 := mbTrans173.WriteString(arg172)
		if err174 != nil {
			Usage()
			return
		}
		factory175 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt176 := factory175.GetProtocol(mbTrans173)
		argvalue0 := workflow.NewRequestHeader()
		err177 := argvalue0.Read(jsProt176)
		if err177 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err179 := (strconv.Atoi(flag.Arg(3)))
		if err179 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := workflow.NumberInt(argvalue2)
		tmp3, err180 := (strconv.Atoi(flag.Arg(4)))
		if err180 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListTasksByTaskDefinitionKey(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listHistoricTaskByAssigneeAndStatus":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListHistoricTaskByAssigneeAndStatus requires 7 args")
			flag.Usage()
		}
		arg183 := flag.Arg(1)
		mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
		defer mbTrans184.Close()
		_, err185 := mbTrans184.WriteString(arg183)
		if err185 != nil {
			Usage()
			return
		}
		factory186 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt187 := factory186.GetProtocol(mbTrans184)
		argvalue0 := workflow.NewRequestHeader()
		err188 := argvalue0.Read(jsProt187)
		if err188 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := workflow.QueryConditionInstanceStatus(tmp2)
		value2 := workflow.QueryConditionInstanceStatus(argvalue2)
		tmp3, err190 := (strconv.Atoi(flag.Arg(4)))
		if err190 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		tmp4, err191 := (strconv.Atoi(flag.Arg(5)))
		if err191 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := workflow.NumberInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListHistoricTaskByAssigneeAndStatus(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getTaskById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTaskById requires 2 args")
			flag.Usage()
		}
		arg194 := flag.Arg(1)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		argvalue0 := workflow.NewRequestHeader()
		err199 := argvalue0.Read(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetTaskById(value0, value1))
		fmt.Print("\n")
		break
	case "getTaskFormPropertiesByTaskId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTaskFormPropertiesByTaskId requires 2 args")
			flag.Usage()
		}
		arg201 := flag.Arg(1)
		mbTrans202 := thrift.NewTMemoryBufferLen(len(arg201))
		defer mbTrans202.Close()
		_, err203 := mbTrans202.WriteString(arg201)
		if err203 != nil {
			Usage()
			return
		}
		factory204 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt205 := factory204.GetProtocol(mbTrans202)
		argvalue0 := workflow.NewRequestHeader()
		err206 := argvalue0.Read(jsProt205)
		if err206 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetTaskFormPropertiesByTaskId(value0, value1))
		fmt.Print("\n")
		break
	case "getProcessFormPropertiesByDefinitionId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProcessFormPropertiesByDefinitionId requires 2 args")
			flag.Usage()
		}
		arg208 := flag.Arg(1)
		mbTrans209 := thrift.NewTMemoryBufferLen(len(arg208))
		defer mbTrans209.Close()
		_, err210 := mbTrans209.WriteString(arg208)
		if err210 != nil {
			Usage()
			return
		}
		factory211 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt212 := factory211.GetProtocol(mbTrans209)
		argvalue0 := workflow.NewRequestHeader()
		err213 := argvalue0.Read(jsProt212)
		if err213 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetProcessFormPropertiesByDefinitionId(value0, value1))
		fmt.Print("\n")
		break
	case "startProcessInstanceByProcessDefinitionId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "StartProcessInstanceByProcessDefinitionId requires 4 args")
			flag.Usage()
		}
		arg215 := flag.Arg(1)
		mbTrans216 := thrift.NewTMemoryBufferLen(len(arg215))
		defer mbTrans216.Close()
		_, err217 := mbTrans216.WriteString(arg215)
		if err217 != nil {
			Usage()
			return
		}
		factory218 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt219 := factory218.GetProtocol(mbTrans216)
		argvalue0 := workflow.NewRequestHeader()
		err220 := argvalue0.Read(jsProt219)
		if err220 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg222 := flag.Arg(3)
		mbTrans223 := thrift.NewTMemoryBufferLen(len(arg222))
		defer mbTrans223.Close()
		_, err224 := mbTrans223.WriteString(arg222)
		if err224 != nil {
			Usage()
			return
		}
		factory225 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt226 := factory225.GetProtocol(mbTrans223)
		containerStruct2 := workflow.NewStartProcessInstanceByProcessDefinitionIdArgs()
		err227 := containerStruct2.ReadField3(jsProt226)
		if err227 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Variables
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.StartProcessInstanceByProcessDefinitionId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "startProcessInstanceByProcessDefinitionKey":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "StartProcessInstanceByProcessDefinitionKey requires 4 args")
			flag.Usage()
		}
		arg229 := flag.Arg(1)
		mbTrans230 := thrift.NewTMemoryBufferLen(len(arg229))
		defer mbTrans230.Close()
		_, err231 := mbTrans230.WriteString(arg229)
		if err231 != nil {
			Usage()
			return
		}
		factory232 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt233 := factory232.GetProtocol(mbTrans230)
		argvalue0 := workflow.NewRequestHeader()
		err234 := argvalue0.Read(jsProt233)
		if err234 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg236 := flag.Arg(3)
		mbTrans237 := thrift.NewTMemoryBufferLen(len(arg236))
		defer mbTrans237.Close()
		_, err238 := mbTrans237.WriteString(arg236)
		if err238 != nil {
			Usage()
			return
		}
		factory239 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt240 := factory239.GetProtocol(mbTrans237)
		containerStruct2 := workflow.NewStartProcessInstanceByProcessDefinitionKeyArgs()
		err241 := containerStruct2.ReadField3(jsProt240)
		if err241 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Variables
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.StartProcessInstanceByProcessDefinitionKey(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "claimTask":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ClaimTask requires 3 args")
			flag.Usage()
		}
		arg243 := flag.Arg(1)
		mbTrans244 := thrift.NewTMemoryBufferLen(len(arg243))
		defer mbTrans244.Close()
		_, err245 := mbTrans244.WriteString(arg243)
		if err245 != nil {
			Usage()
			return
		}
		factory246 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt247 := factory246.GetProtocol(mbTrans244)
		argvalue0 := workflow.NewRequestHeader()
		err248 := argvalue0.Read(jsProt247)
		if err248 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.ClaimTask(value0, value1, value2))
		fmt.Print("\n")
		break
	case "completeTask":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CompleteTask requires 3 args")
			flag.Usage()
		}
		arg251 := flag.Arg(1)
		mbTrans252 := thrift.NewTMemoryBufferLen(len(arg251))
		defer mbTrans252.Close()
		_, err253 := mbTrans252.WriteString(arg251)
		if err253 != nil {
			Usage()
			return
		}
		factory254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt255 := factory254.GetProtocol(mbTrans252)
		argvalue0 := workflow.NewRequestHeader()
		err256 := argvalue0.Read(jsProt255)
		if err256 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg258 := flag.Arg(3)
		mbTrans259 := thrift.NewTMemoryBufferLen(len(arg258))
		defer mbTrans259.Close()
		_, err260 := mbTrans259.WriteString(arg258)
		if err260 != nil {
			Usage()
			return
		}
		factory261 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt262 := factory261.GetProtocol(mbTrans259)
		containerStruct2 := workflow.NewCompleteTaskArgs()
		err263 := containerStruct2.ReadField3(jsProt262)
		if err263 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Variables
		value2 := argvalue2
		fmt.Print(client.CompleteTask(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listTasksByScript":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListTasksByScript requires 6 args")
			flag.Usage()
		}
		arg264 := flag.Arg(1)
		mbTrans265 := thrift.NewTMemoryBufferLen(len(arg264))
		defer mbTrans265.Close()
		_, err266 := mbTrans265.WriteString(arg264)
		if err266 != nil {
			Usage()
			return
		}
		factory267 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt268 := factory267.GetProtocol(mbTrans265)
		argvalue0 := workflow.NewRequestHeader()
		err269 := argvalue0.Read(jsProt268)
		if err269 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err271 := (strconv.Atoi(flag.Arg(3)))
		if err271 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := workflow.NumberInt(argvalue2)
		tmp3, err272 := (strconv.Atoi(flag.Arg(4)))
		if err272 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListTasksByScript(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listHistoricTasksByScript":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListHistoricTasksByScript requires 6 args")
			flag.Usage()
		}
		arg275 := flag.Arg(1)
		mbTrans276 := thrift.NewTMemoryBufferLen(len(arg275))
		defer mbTrans276.Close()
		_, err277 := mbTrans276.WriteString(arg275)
		if err277 != nil {
			Usage()
			return
		}
		factory278 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt279 := factory278.GetProtocol(mbTrans276)
		argvalue0 := workflow.NewRequestHeader()
		err280 := argvalue0.Read(jsProt279)
		if err280 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err282 := (strconv.Atoi(flag.Arg(3)))
		if err282 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := workflow.NumberInt(argvalue2)
		tmp3, err283 := (strconv.Atoi(flag.Arg(4)))
		if err283 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListHistoricTasksByScript(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listProcessInstanceByScript":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListProcessInstanceByScript requires 6 args")
			flag.Usage()
		}
		arg286 := flag.Arg(1)
		mbTrans287 := thrift.NewTMemoryBufferLen(len(arg286))
		defer mbTrans287.Close()
		_, err288 := mbTrans287.WriteString(arg286)
		if err288 != nil {
			Usage()
			return
		}
		factory289 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt290 := factory289.GetProtocol(mbTrans287)
		argvalue0 := workflow.NewRequestHeader()
		err291 := argvalue0.Read(jsProt290)
		if err291 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err293 := (strconv.Atoi(flag.Arg(3)))
		if err293 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := workflow.NumberInt(argvalue2)
		tmp3, err294 := (strconv.Atoi(flag.Arg(4)))
		if err294 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := workflow.NumberInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListProcessInstanceByScript(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "processDefinitionDeploy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ProcessDefinitionDeploy requires 3 args")
			flag.Usage()
		}
		arg297 := flag.Arg(1)
		mbTrans298 := thrift.NewTMemoryBufferLen(len(arg297))
		defer mbTrans298.Close()
		_, err299 := mbTrans298.WriteString(arg297)
		if err299 != nil {
			Usage()
			return
		}
		factory300 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt301 := factory300.GetProtocol(mbTrans298)
		argvalue0 := workflow.NewRequestHeader()
		err302 := argvalue0.Read(jsProt301)
		if err302 != nil {
			Usage()
			return
		}
		value0 := workflow.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.ProcessDefinitionDeploy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
