// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package workflow

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/workflow_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = workflow_types.GoUnusedProtection__
var GoUnusedProtection__ int

//WorkFlowException中可能出现的异常代码
type WorkFlowServiceCode int64

const (
	WorkFlowServiceCode_ERROR_WF_TASK_USER_NOT_EXIST WorkFlowServiceCode = 25301
	WorkFlowServiceCode_ERROR_WF_PARAM_INVALID       WorkFlowServiceCode = 25401
	WorkFlowServiceCode_ERROR_WF_SYSTEM_ERROR        WorkFlowServiceCode = 25501
	WorkFlowServiceCode_ERROR_WF_SCRIPT_ERROR        WorkFlowServiceCode = 25502
)

func (p WorkFlowServiceCode) String() string {
	switch p {
	case WorkFlowServiceCode_ERROR_WF_TASK_USER_NOT_EXIST:
		return "WorkFlowServiceCode_ERROR_WF_TASK_USER_NOT_EXIST"
	case WorkFlowServiceCode_ERROR_WF_PARAM_INVALID:
		return "WorkFlowServiceCode_ERROR_WF_PARAM_INVALID"
	case WorkFlowServiceCode_ERROR_WF_SYSTEM_ERROR:
		return "WorkFlowServiceCode_ERROR_WF_SYSTEM_ERROR"
	case WorkFlowServiceCode_ERROR_WF_SCRIPT_ERROR:
		return "WorkFlowServiceCode_ERROR_WF_SCRIPT_ERROR"
	}
	return "<UNSET>"
}

func WorkFlowServiceCodeFromString(s string) (WorkFlowServiceCode, error) {
	switch s {
	case "WorkFlowServiceCode_ERROR_WF_TASK_USER_NOT_EXIST":
		return WorkFlowServiceCode_ERROR_WF_TASK_USER_NOT_EXIST, nil
	case "WorkFlowServiceCode_ERROR_WF_PARAM_INVALID":
		return WorkFlowServiceCode_ERROR_WF_PARAM_INVALID, nil
	case "WorkFlowServiceCode_ERROR_WF_SYSTEM_ERROR":
		return WorkFlowServiceCode_ERROR_WF_SYSTEM_ERROR, nil
	case "WorkFlowServiceCode_ERROR_WF_SCRIPT_ERROR":
		return WorkFlowServiceCode_ERROR_WF_SCRIPT_ERROR, nil
	}
	return WorkFlowServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid WorkFlowServiceCode string")
}

type TimeInt workflow_types.TimeInt

type NumberInt workflow_types.NumberInt

type QueryConditionInstanceStatus workflow_types.QueryConditionInstanceStatus

type QueryConditionRoleType workflow_types.QueryConditionRoleType

type RequestHeader *common.RequestHeader

type ProcessDefinitionThrift *workflow_types.ProcessDefinitionThrift

type ProcessInstanceThrift *workflow_types.ProcessInstanceThrift

type TaskSummaryThrift *workflow_types.TaskSummaryThrift

type FormPropertyThrift *workflow_types.FormPropertyThrift

type TaskThrift *workflow_types.TaskThrift

type ProcessDefinitionQueryResult *workflow_types.ProcessDefinitionQueryResult

type ProcessInstanceQueryResult *workflow_types.ProcessInstanceQueryResult

type TaskQueryResult *workflow_types.TaskQueryResult

type WorkFlowException struct {
	Code    WorkFlowServiceCode `thrift:"code,1" json:"code"`
	Message string              `thrift:"message,2" json:"message"`
}

func NewWorkFlowException() *WorkFlowException {
	return &WorkFlowException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *WorkFlowException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *WorkFlowException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WorkFlowException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = WorkFlowServiceCode(v)
	}
	return nil
}

func (p *WorkFlowException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *WorkFlowException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("WorkFlowException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WorkFlowException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *WorkFlowException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *WorkFlowException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WorkFlowException(%+v)", *p)
}
