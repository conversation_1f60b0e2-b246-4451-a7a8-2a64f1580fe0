// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package workflow

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/workflow_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = workflow_types.GoUnusedProtection__

type WorkFlowService interface { //工作流服务接口定义

	// 列出流程定义
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Category: 流程类型，目前支持cn.domob.workflow.adcrm。如果为null或为空数组，表示不限类型
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, key, name, version
	//  - Isasc: 是否升序
	ListProcessDefinitions(header *common.RequestHeader, category string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessDefinitionQueryResult, wfe *WorkFlowException, err error)
	// 根据Id获取流程定义
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ProcessDefinitionId: processDefinitionId
	GetProcessDefinitionById(header *common.RequestHeader, processDefinitionId string) (r *workflow_types.ProcessDefinitionThrift, wfe *WorkFlowException, err error)
	// 按流程实例状态列出流程实例
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Status: 实例完成情况, 必须提供
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, starttime
	//  - Isasc: 是否升序
	ListProcessInstancesByStatus(header *common.RequestHeader, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error)
	// 按BusinessKey列出流程实例
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - BusinessKey: 流程实例的businessKey
	//  - Status: 实例完成情况, 必须提供
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, starttime
	//  - Isasc: 是否升序
	ListProcessInstancesByBusinessKey(header *common.RequestHeader, businessKey string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error)
	// 按发起人列出流程实例
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - StartUserId: 发起流程的UserId，必须提供，不能为null或空串
	//  - Status: 实例完成情况, 必须提供
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, starttime
	//  - Isasc: 是否升序
	ListProcessInstancesByStartUserId(header *common.RequestHeader, startUserId string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error)
	// 获取某个用户的任务概要信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - UserId: 用户编号
	//  - GroupIds: 要求返回的用户组任务概要信息中需要包含的用户组，如果不提供，则返回的概要信息不包含分用户组的信息
	//  - TaskDefinitionKeys
	GetTaskSummaryByUserId(header *common.RequestHeader, userId string, groupIds map[string]bool, taskDefinitionKeys map[string]bool) (r *workflow_types.TaskSummaryThrift, wfe *WorkFlowException, err error)
	// 获取未完成的任务列表
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - RoleType: 用户任务的筛选条件, 不允许为null
	//  - Role: 角色的值，可以是userId或者groupId，取决于roleType是什么
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
	//  - Isasc: 是否升序
	ListTasksByRole(header *common.RequestHeader, roleType QueryConditionRoleType, role string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error)
	// 获取各任务未完成的任务
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskDefinitionKey: task definition key, 不允许为null
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
	//  - Isasc: 是否升序
	ListTasksByTaskDefinitionKey(header *common.RequestHeader, taskDefinitionKey string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error)
	// 根据Assignee和Status获取任务历史列表
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Assignee: 认领者, 为null或空串时表示不限制
	//  - Status: 任务完成情况, 必须提供
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, starttime, endtime, duration
	//  - Isasc: 是否升序
	ListHistoricTaskByAssigneeAndStatus(header *common.RequestHeader, assignee string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 用户任务编号
	GetTaskById(header *common.RequestHeader, taskId string) (r *workflow_types.TaskThrift, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 用户任务编号
	GetTaskFormPropertiesByTaskId(header *common.RequestHeader, taskId string) (r []*workflow_types.FormPropertyThrift, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ProcessDefinitionId: 流程编号
	GetProcessFormPropertiesByDefinitionId(header *common.RequestHeader, processDefinitionId string) (r []*workflow_types.FormPropertyThrift, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ProcessDefinitionId: 流程编号
	//  - Variables: 表单变量, 见startProcessInstanceByProcessDefinitionId描述
	//  - UserId: 发起流程的用户编号
	StartProcessInstanceByProcessDefinitionId(header *common.RequestHeader, processDefinitionId string, variables map[string]string, userId string) (r *workflow_types.ProcessInstanceThrift, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ProcessDefinitionKey: 流程标识
	//  - Variables: 表单变量, 见startProcessInstanceByProcessDefinitionId描述
	//  - UserId: 发起流程的用户编号
	StartProcessInstanceByProcessDefinitionKey(header *common.RequestHeader, processDefinitionKey string, variables map[string]string, userId string) (r *workflow_types.ProcessInstanceThrift, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 任务编号
	//  - UserId: 用户编号
	ClaimTask(header *common.RequestHeader, taskId string, userId string) (wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 任务编号
	//  - Variables: 表单变量, 见startProcessInstanceByProcessDefinitionId描述
	CompleteTask(header *common.RequestHeader, taskId string, variables map[string]string) (wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Script: script 脚本
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
	//  - Isasc: 是否升序
	ListTasksByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Script: script 脚本
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
	//  - Isasc: 是否升序
	ListHistoricTasksByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Script: script 脚本
	//  - Start: 起始位置，从0开始算
	//  - Size: 最多返回多少条
	//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
	//  - Isasc: 是否升序
	ListProcessInstanceByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error)
	// 通过thrift部署新的流程
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ResouceName: resouce 名称
	//  - ResouceContent: resouce .bpmn20.xml内容
	ProcessDefinitionDeploy(header *common.RequestHeader, resouceName string, resouceContent string) (wfe *WorkFlowException, err error)
}

//工作流服务接口定义
type WorkFlowServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewWorkFlowServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *WorkFlowServiceClient {
	return &WorkFlowServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewWorkFlowServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *WorkFlowServiceClient {
	return &WorkFlowServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 列出流程定义
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Category: 流程类型，目前支持cn.domob.workflow.adcrm。如果为null或为空数组，表示不限类型
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, key, name, version
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListProcessDefinitions(header *common.RequestHeader, category string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessDefinitionQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListProcessDefinitions(header, category, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListProcessDefinitions()
}

func (p *WorkFlowServiceClient) sendListProcessDefinitions(header *common.RequestHeader, category string, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listProcessDefinitions", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewListProcessDefinitionsArgs()
	args0.Header = header
	args0.Category = category
	args0.Start = start
	args0.Size = size
	args0.Sort = sort
	args0.Isasc = isasc
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListProcessDefinitions() (value *workflow_types.ProcessDefinitionQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewListProcessDefinitionsResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Wfe != nil {
		wfe = result1.Wfe
	}
	return
}

// 根据Id获取流程定义
//
// Parameters:
//  - Header: 请求消息头结构体
//  - ProcessDefinitionId: processDefinitionId
func (p *WorkFlowServiceClient) GetProcessDefinitionById(header *common.RequestHeader, processDefinitionId string) (r *workflow_types.ProcessDefinitionThrift, wfe *WorkFlowException, err error) {
	if err = p.sendGetProcessDefinitionById(header, processDefinitionId); err != nil {
		return
	}
	return p.recvGetProcessDefinitionById()
}

func (p *WorkFlowServiceClient) sendGetProcessDefinitionById(header *common.RequestHeader, processDefinitionId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getProcessDefinitionById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetProcessDefinitionByIdArgs()
	args4.Header = header
	args4.ProcessDefinitionId = processDefinitionId
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvGetProcessDefinitionById() (value *workflow_types.ProcessDefinitionThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetProcessDefinitionByIdResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Wfe != nil {
		wfe = result5.Wfe
	}
	return
}

// 按流程实例状态列出流程实例
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Status: 实例完成情况, 必须提供
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, starttime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListProcessInstancesByStatus(header *common.RequestHeader, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListProcessInstancesByStatus(header, status, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListProcessInstancesByStatus()
}

func (p *WorkFlowServiceClient) sendListProcessInstancesByStatus(header *common.RequestHeader, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listProcessInstancesByStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewListProcessInstancesByStatusArgs()
	args8.Header = header
	args8.Status = status
	args8.Start = start
	args8.Size = size
	args8.Sort = sort
	args8.Isasc = isasc
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListProcessInstancesByStatus() (value *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewListProcessInstancesByStatusResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Wfe != nil {
		wfe = result9.Wfe
	}
	return
}

// 按BusinessKey列出流程实例
//
// Parameters:
//  - Header: 请求消息头结构体
//  - BusinessKey: 流程实例的businessKey
//  - Status: 实例完成情况, 必须提供
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, starttime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListProcessInstancesByBusinessKey(header *common.RequestHeader, businessKey string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListProcessInstancesByBusinessKey(header, businessKey, status, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListProcessInstancesByBusinessKey()
}

func (p *WorkFlowServiceClient) sendListProcessInstancesByBusinessKey(header *common.RequestHeader, businessKey string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listProcessInstancesByBusinessKey", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewListProcessInstancesByBusinessKeyArgs()
	args12.Header = header
	args12.BusinessKey = businessKey
	args12.Status = status
	args12.Start = start
	args12.Size = size
	args12.Sort = sort
	args12.Isasc = isasc
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListProcessInstancesByBusinessKey() (value *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewListProcessInstancesByBusinessKeyResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Wfe != nil {
		wfe = result13.Wfe
	}
	return
}

// 按发起人列出流程实例
//
// Parameters:
//  - Header: 请求消息头结构体
//  - StartUserId: 发起流程的UserId，必须提供，不能为null或空串
//  - Status: 实例完成情况, 必须提供
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, starttime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListProcessInstancesByStartUserId(header *common.RequestHeader, startUserId string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListProcessInstancesByStartUserId(header, startUserId, status, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListProcessInstancesByStartUserId()
}

func (p *WorkFlowServiceClient) sendListProcessInstancesByStartUserId(header *common.RequestHeader, startUserId string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listProcessInstancesByStartUserId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewListProcessInstancesByStartUserIdArgs()
	args16.Header = header
	args16.StartUserId = startUserId
	args16.Status = status
	args16.Start = start
	args16.Size = size
	args16.Sort = sort
	args16.Isasc = isasc
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListProcessInstancesByStartUserId() (value *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewListProcessInstancesByStartUserIdResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Wfe != nil {
		wfe = result17.Wfe
	}
	return
}

// 获取某个用户的任务概要信息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - UserId: 用户编号
//  - GroupIds: 要求返回的用户组任务概要信息中需要包含的用户组，如果不提供，则返回的概要信息不包含分用户组的信息
//  - TaskDefinitionKeys
func (p *WorkFlowServiceClient) GetTaskSummaryByUserId(header *common.RequestHeader, userId string, groupIds map[string]bool, taskDefinitionKeys map[string]bool) (r *workflow_types.TaskSummaryThrift, wfe *WorkFlowException, err error) {
	if err = p.sendGetTaskSummaryByUserId(header, userId, groupIds, taskDefinitionKeys); err != nil {
		return
	}
	return p.recvGetTaskSummaryByUserId()
}

func (p *WorkFlowServiceClient) sendGetTaskSummaryByUserId(header *common.RequestHeader, userId string, groupIds map[string]bool, taskDefinitionKeys map[string]bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTaskSummaryByUserId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetTaskSummaryByUserIdArgs()
	args20.Header = header
	args20.UserId = userId
	args20.GroupIds = groupIds
	args20.TaskDefinitionKeys = taskDefinitionKeys
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvGetTaskSummaryByUserId() (value *workflow_types.TaskSummaryThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetTaskSummaryByUserIdResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Wfe != nil {
		wfe = result21.Wfe
	}
	return
}

// 获取未完成的任务列表
//
// Parameters:
//  - Header: 请求消息头结构体
//  - RoleType: 用户任务的筛选条件, 不允许为null
//  - Role: 角色的值，可以是userId或者groupId，取决于roleType是什么
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListTasksByRole(header *common.RequestHeader, roleType QueryConditionRoleType, role string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListTasksByRole(header, roleType, role, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListTasksByRole()
}

func (p *WorkFlowServiceClient) sendListTasksByRole(header *common.RequestHeader, roleType QueryConditionRoleType, role string, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTasksByRole", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewListTasksByRoleArgs()
	args24.Header = header
	args24.RoleType = roleType
	args24.Role = role
	args24.Start = start
	args24.Size = size
	args24.Sort = sort
	args24.Isasc = isasc
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListTasksByRole() (value *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewListTasksByRoleResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Wfe != nil {
		wfe = result25.Wfe
	}
	return
}

// 获取各任务未完成的任务
//
// Parameters:
//  - Header: 请求消息头结构体
//  - TaskDefinitionKey: task definition key, 不允许为null
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListTasksByTaskDefinitionKey(header *common.RequestHeader, taskDefinitionKey string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListTasksByTaskDefinitionKey(header, taskDefinitionKey, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListTasksByTaskDefinitionKey()
}

func (p *WorkFlowServiceClient) sendListTasksByTaskDefinitionKey(header *common.RequestHeader, taskDefinitionKey string, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTasksByTaskDefinitionKey", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewListTasksByTaskDefinitionKeyArgs()
	args28.Header = header
	args28.TaskDefinitionKey = taskDefinitionKey
	args28.Start = start
	args28.Size = size
	args28.Sort = sort
	args28.Isasc = isasc
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListTasksByTaskDefinitionKey() (value *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewListTasksByTaskDefinitionKeyResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Wfe != nil {
		wfe = result29.Wfe
	}
	return
}

// 根据Assignee和Status获取任务历史列表
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Assignee: 认领者, 为null或空串时表示不限制
//  - Status: 任务完成情况, 必须提供
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, starttime, endtime, duration
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListHistoricTaskByAssigneeAndStatus(header *common.RequestHeader, assignee string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListHistoricTaskByAssigneeAndStatus(header, assignee, status, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListHistoricTaskByAssigneeAndStatus()
}

func (p *WorkFlowServiceClient) sendListHistoricTaskByAssigneeAndStatus(header *common.RequestHeader, assignee string, status QueryConditionInstanceStatus, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listHistoricTaskByAssigneeAndStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewListHistoricTaskByAssigneeAndStatusArgs()
	args32.Header = header
	args32.Assignee = assignee
	args32.Status = status
	args32.Start = start
	args32.Size = size
	args32.Sort = sort
	args32.Isasc = isasc
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListHistoricTaskByAssigneeAndStatus() (value *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewListHistoricTaskByAssigneeAndStatusResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Wfe != nil {
		wfe = result33.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 用户任务编号
func (p *WorkFlowServiceClient) GetTaskById(header *common.RequestHeader, taskId string) (r *workflow_types.TaskThrift, wfe *WorkFlowException, err error) {
	if err = p.sendGetTaskById(header, taskId); err != nil {
		return
	}
	return p.recvGetTaskById()
}

func (p *WorkFlowServiceClient) sendGetTaskById(header *common.RequestHeader, taskId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTaskById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetTaskByIdArgs()
	args36.Header = header
	args36.TaskId = taskId
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvGetTaskById() (value *workflow_types.TaskThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetTaskByIdResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Wfe != nil {
		wfe = result37.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 用户任务编号
func (p *WorkFlowServiceClient) GetTaskFormPropertiesByTaskId(header *common.RequestHeader, taskId string) (r []*workflow_types.FormPropertyThrift, wfe *WorkFlowException, err error) {
	if err = p.sendGetTaskFormPropertiesByTaskId(header, taskId); err != nil {
		return
	}
	return p.recvGetTaskFormPropertiesByTaskId()
}

func (p *WorkFlowServiceClient) sendGetTaskFormPropertiesByTaskId(header *common.RequestHeader, taskId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTaskFormPropertiesByTaskId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewGetTaskFormPropertiesByTaskIdArgs()
	args40.Header = header
	args40.TaskId = taskId
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvGetTaskFormPropertiesByTaskId() (value []*workflow_types.FormPropertyThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewGetTaskFormPropertiesByTaskIdResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Wfe != nil {
		wfe = result41.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - ProcessDefinitionId: 流程编号
func (p *WorkFlowServiceClient) GetProcessFormPropertiesByDefinitionId(header *common.RequestHeader, processDefinitionId string) (r []*workflow_types.FormPropertyThrift, wfe *WorkFlowException, err error) {
	if err = p.sendGetProcessFormPropertiesByDefinitionId(header, processDefinitionId); err != nil {
		return
	}
	return p.recvGetProcessFormPropertiesByDefinitionId()
}

func (p *WorkFlowServiceClient) sendGetProcessFormPropertiesByDefinitionId(header *common.RequestHeader, processDefinitionId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getProcessFormPropertiesByDefinitionId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetProcessFormPropertiesByDefinitionIdArgs()
	args44.Header = header
	args44.ProcessDefinitionId = processDefinitionId
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvGetProcessFormPropertiesByDefinitionId() (value []*workflow_types.FormPropertyThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetProcessFormPropertiesByDefinitionIdResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Wfe != nil {
		wfe = result45.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - ProcessDefinitionId: 流程编号
//  - Variables: 表单变量, 见startProcessInstanceByProcessDefinitionId描述
//  - UserId: 发起流程的用户编号
func (p *WorkFlowServiceClient) StartProcessInstanceByProcessDefinitionId(header *common.RequestHeader, processDefinitionId string, variables map[string]string, userId string) (r *workflow_types.ProcessInstanceThrift, wfe *WorkFlowException, err error) {
	if err = p.sendStartProcessInstanceByProcessDefinitionId(header, processDefinitionId, variables, userId); err != nil {
		return
	}
	return p.recvStartProcessInstanceByProcessDefinitionId()
}

func (p *WorkFlowServiceClient) sendStartProcessInstanceByProcessDefinitionId(header *common.RequestHeader, processDefinitionId string, variables map[string]string, userId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewStartProcessInstanceByProcessDefinitionIdArgs()
	args48.Header = header
	args48.ProcessDefinitionId = processDefinitionId
	args48.Variables = variables
	args48.UserId = userId
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvStartProcessInstanceByProcessDefinitionId() (value *workflow_types.ProcessInstanceThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewStartProcessInstanceByProcessDefinitionIdResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Wfe != nil {
		wfe = result49.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - ProcessDefinitionKey: 流程标识
//  - Variables: 表单变量, 见startProcessInstanceByProcessDefinitionId描述
//  - UserId: 发起流程的用户编号
func (p *WorkFlowServiceClient) StartProcessInstanceByProcessDefinitionKey(header *common.RequestHeader, processDefinitionKey string, variables map[string]string, userId string) (r *workflow_types.ProcessInstanceThrift, wfe *WorkFlowException, err error) {
	if err = p.sendStartProcessInstanceByProcessDefinitionKey(header, processDefinitionKey, variables, userId); err != nil {
		return
	}
	return p.recvStartProcessInstanceByProcessDefinitionKey()
}

func (p *WorkFlowServiceClient) sendStartProcessInstanceByProcessDefinitionKey(header *common.RequestHeader, processDefinitionKey string, variables map[string]string, userId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionKey", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewStartProcessInstanceByProcessDefinitionKeyArgs()
	args52.Header = header
	args52.ProcessDefinitionKey = processDefinitionKey
	args52.Variables = variables
	args52.UserId = userId
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvStartProcessInstanceByProcessDefinitionKey() (value *workflow_types.ProcessInstanceThrift, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewStartProcessInstanceByProcessDefinitionKeyResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Wfe != nil {
		wfe = result53.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 任务编号
//  - UserId: 用户编号
func (p *WorkFlowServiceClient) ClaimTask(header *common.RequestHeader, taskId string, userId string) (wfe *WorkFlowException, err error) {
	if err = p.sendClaimTask(header, taskId, userId); err != nil {
		return
	}
	return p.recvClaimTask()
}

func (p *WorkFlowServiceClient) sendClaimTask(header *common.RequestHeader, taskId string, userId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("claimTask", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewClaimTaskArgs()
	args56.Header = header
	args56.TaskId = taskId
	args56.UserId = userId
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvClaimTask() (wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewClaimTaskResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result57.Wfe != nil {
		wfe = result57.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 任务编号
//  - Variables: 表单变量, 见startProcessInstanceByProcessDefinitionId描述
func (p *WorkFlowServiceClient) CompleteTask(header *common.RequestHeader, taskId string, variables map[string]string) (wfe *WorkFlowException, err error) {
	if err = p.sendCompleteTask(header, taskId, variables); err != nil {
		return
	}
	return p.recvCompleteTask()
}

func (p *WorkFlowServiceClient) sendCompleteTask(header *common.RequestHeader, taskId string, variables map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("completeTask", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewCompleteTaskArgs()
	args60.Header = header
	args60.TaskId = taskId
	args60.Variables = variables
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvCompleteTask() (wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewCompleteTaskResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result61.Wfe != nil {
		wfe = result61.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Script: script 脚本
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListTasksByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListTasksByScript(header, script, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListTasksByScript()
}

func (p *WorkFlowServiceClient) sendListTasksByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTasksByScript", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewListTasksByScriptArgs()
	args64.Header = header
	args64.Script = script
	args64.Start = start
	args64.Size = size
	args64.Sort = sort
	args64.Isasc = isasc
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListTasksByScript() (value *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewListTasksByScriptResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.Wfe != nil {
		wfe = result65.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Script: script 脚本
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListHistoricTasksByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListHistoricTasksByScript(header, script, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListHistoricTasksByScript()
}

func (p *WorkFlowServiceClient) sendListHistoricTasksByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listHistoricTasksByScript", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewListHistoricTasksByScriptArgs()
	args68.Header = header
	args68.Script = script
	args68.Start = start
	args68.Size = size
	args68.Sort = sort
	args68.Isasc = isasc
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListHistoricTasksByScript() (value *workflow_types.TaskQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewListHistoricTasksByScriptResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result69.Success
	if result69.Wfe != nil {
		wfe = result69.Wfe
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Script: script 脚本
//  - Start: 起始位置，从0开始算
//  - Size: 最多返回多少条
//  - Sort: 按什么字段排序，目前支持id, name, description, assignee, executionid, processinstanceid, createtime
//  - Isasc: 是否升序
func (p *WorkFlowServiceClient) ListProcessInstanceByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (r *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	if err = p.sendListProcessInstanceByScript(header, script, start, size, sort, isasc); err != nil {
		return
	}
	return p.recvListProcessInstanceByScript()
}

func (p *WorkFlowServiceClient) sendListProcessInstanceByScript(header *common.RequestHeader, script string, start NumberInt, size NumberInt, sort string, isasc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listProcessInstanceByScript", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args72 := NewListProcessInstanceByScriptArgs()
	args72.Header = header
	args72.Script = script
	args72.Start = start
	args72.Size = size
	args72.Sort = sort
	args72.Isasc = isasc
	if err = args72.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvListProcessInstanceByScript() (value *workflow_types.ProcessInstanceQueryResult, wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error74 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error75 error
		error75, err = error74.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error75
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result73 := NewListProcessInstanceByScriptResult()
	if err = result73.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result73.Success
	if result73.Wfe != nil {
		wfe = result73.Wfe
	}
	return
}

// 通过thrift部署新的流程
//
// Parameters:
//  - Header: 请求消息头结构体
//  - ResouceName: resouce 名称
//  - ResouceContent: resouce .bpmn20.xml内容
func (p *WorkFlowServiceClient) ProcessDefinitionDeploy(header *common.RequestHeader, resouceName string, resouceContent string) (wfe *WorkFlowException, err error) {
	if err = p.sendProcessDefinitionDeploy(header, resouceName, resouceContent); err != nil {
		return
	}
	return p.recvProcessDefinitionDeploy()
}

func (p *WorkFlowServiceClient) sendProcessDefinitionDeploy(header *common.RequestHeader, resouceName string, resouceContent string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("processDefinitionDeploy", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args76 := NewProcessDefinitionDeployArgs()
	args76.Header = header
	args76.ResouceName = resouceName
	args76.ResouceContent = resouceContent
	if err = args76.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *WorkFlowServiceClient) recvProcessDefinitionDeploy() (wfe *WorkFlowException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error78 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error79 error
		error79, err = error78.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error79
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result77 := NewProcessDefinitionDeployResult()
	if err = result77.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result77.Wfe != nil {
		wfe = result77.Wfe
	}
	return
}

type WorkFlowServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      WorkFlowService
}

func (p *WorkFlowServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *WorkFlowServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *WorkFlowServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewWorkFlowServiceProcessor(handler WorkFlowService) *WorkFlowServiceProcessor {

	self80 := &WorkFlowServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self80.processorMap["listProcessDefinitions"] = &workFlowServiceProcessorListProcessDefinitions{handler: handler}
	self80.processorMap["getProcessDefinitionById"] = &workFlowServiceProcessorGetProcessDefinitionById{handler: handler}
	self80.processorMap["listProcessInstancesByStatus"] = &workFlowServiceProcessorListProcessInstancesByStatus{handler: handler}
	self80.processorMap["listProcessInstancesByBusinessKey"] = &workFlowServiceProcessorListProcessInstancesByBusinessKey{handler: handler}
	self80.processorMap["listProcessInstancesByStartUserId"] = &workFlowServiceProcessorListProcessInstancesByStartUserId{handler: handler}
	self80.processorMap["getTaskSummaryByUserId"] = &workFlowServiceProcessorGetTaskSummaryByUserId{handler: handler}
	self80.processorMap["listTasksByRole"] = &workFlowServiceProcessorListTasksByRole{handler: handler}
	self80.processorMap["listTasksByTaskDefinitionKey"] = &workFlowServiceProcessorListTasksByTaskDefinitionKey{handler: handler}
	self80.processorMap["listHistoricTaskByAssigneeAndStatus"] = &workFlowServiceProcessorListHistoricTaskByAssigneeAndStatus{handler: handler}
	self80.processorMap["getTaskById"] = &workFlowServiceProcessorGetTaskById{handler: handler}
	self80.processorMap["getTaskFormPropertiesByTaskId"] = &workFlowServiceProcessorGetTaskFormPropertiesByTaskId{handler: handler}
	self80.processorMap["getProcessFormPropertiesByDefinitionId"] = &workFlowServiceProcessorGetProcessFormPropertiesByDefinitionId{handler: handler}
	self80.processorMap["startProcessInstanceByProcessDefinitionId"] = &workFlowServiceProcessorStartProcessInstanceByProcessDefinitionId{handler: handler}
	self80.processorMap["startProcessInstanceByProcessDefinitionKey"] = &workFlowServiceProcessorStartProcessInstanceByProcessDefinitionKey{handler: handler}
	self80.processorMap["claimTask"] = &workFlowServiceProcessorClaimTask{handler: handler}
	self80.processorMap["completeTask"] = &workFlowServiceProcessorCompleteTask{handler: handler}
	self80.processorMap["listTasksByScript"] = &workFlowServiceProcessorListTasksByScript{handler: handler}
	self80.processorMap["listHistoricTasksByScript"] = &workFlowServiceProcessorListHistoricTasksByScript{handler: handler}
	self80.processorMap["listProcessInstanceByScript"] = &workFlowServiceProcessorListProcessInstanceByScript{handler: handler}
	self80.processorMap["processDefinitionDeploy"] = &workFlowServiceProcessorProcessDefinitionDeploy{handler: handler}
	return self80
}

func (p *WorkFlowServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x81 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x81.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x81

}

type workFlowServiceProcessorListProcessDefinitions struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListProcessDefinitions) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListProcessDefinitionsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listProcessDefinitions", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListProcessDefinitionsResult()
	if result.Success, result.Wfe, err = p.handler.ListProcessDefinitions(args.Header, args.Category, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listProcessDefinitions: "+err.Error())
		oprot.WriteMessageBegin("listProcessDefinitions", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listProcessDefinitions", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorGetProcessDefinitionById struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorGetProcessDefinitionById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetProcessDefinitionByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getProcessDefinitionById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetProcessDefinitionByIdResult()
	if result.Success, result.Wfe, err = p.handler.GetProcessDefinitionById(args.Header, args.ProcessDefinitionId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getProcessDefinitionById: "+err.Error())
		oprot.WriteMessageBegin("getProcessDefinitionById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getProcessDefinitionById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListProcessInstancesByStatus struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListProcessInstancesByStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListProcessInstancesByStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listProcessInstancesByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListProcessInstancesByStatusResult()
	if result.Success, result.Wfe, err = p.handler.ListProcessInstancesByStatus(args.Header, args.Status, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listProcessInstancesByStatus: "+err.Error())
		oprot.WriteMessageBegin("listProcessInstancesByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listProcessInstancesByStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListProcessInstancesByBusinessKey struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListProcessInstancesByBusinessKey) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListProcessInstancesByBusinessKeyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listProcessInstancesByBusinessKey", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListProcessInstancesByBusinessKeyResult()
	if result.Success, result.Wfe, err = p.handler.ListProcessInstancesByBusinessKey(args.Header, args.BusinessKey, args.Status, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listProcessInstancesByBusinessKey: "+err.Error())
		oprot.WriteMessageBegin("listProcessInstancesByBusinessKey", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listProcessInstancesByBusinessKey", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListProcessInstancesByStartUserId struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListProcessInstancesByStartUserId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListProcessInstancesByStartUserIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listProcessInstancesByStartUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListProcessInstancesByStartUserIdResult()
	if result.Success, result.Wfe, err = p.handler.ListProcessInstancesByStartUserId(args.Header, args.StartUserId, args.Status, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listProcessInstancesByStartUserId: "+err.Error())
		oprot.WriteMessageBegin("listProcessInstancesByStartUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listProcessInstancesByStartUserId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorGetTaskSummaryByUserId struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorGetTaskSummaryByUserId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTaskSummaryByUserIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTaskSummaryByUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTaskSummaryByUserIdResult()
	if result.Success, result.Wfe, err = p.handler.GetTaskSummaryByUserId(args.Header, args.UserId, args.GroupIds, args.TaskDefinitionKeys); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTaskSummaryByUserId: "+err.Error())
		oprot.WriteMessageBegin("getTaskSummaryByUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTaskSummaryByUserId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListTasksByRole struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListTasksByRole) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTasksByRoleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTasksByRole", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTasksByRoleResult()
	if result.Success, result.Wfe, err = p.handler.ListTasksByRole(args.Header, args.RoleType, args.Role, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTasksByRole: "+err.Error())
		oprot.WriteMessageBegin("listTasksByRole", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTasksByRole", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListTasksByTaskDefinitionKey struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListTasksByTaskDefinitionKey) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTasksByTaskDefinitionKeyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTasksByTaskDefinitionKey", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTasksByTaskDefinitionKeyResult()
	if result.Success, result.Wfe, err = p.handler.ListTasksByTaskDefinitionKey(args.Header, args.TaskDefinitionKey, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTasksByTaskDefinitionKey: "+err.Error())
		oprot.WriteMessageBegin("listTasksByTaskDefinitionKey", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTasksByTaskDefinitionKey", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListHistoricTaskByAssigneeAndStatus struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListHistoricTaskByAssigneeAndStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListHistoricTaskByAssigneeAndStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listHistoricTaskByAssigneeAndStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListHistoricTaskByAssigneeAndStatusResult()
	if result.Success, result.Wfe, err = p.handler.ListHistoricTaskByAssigneeAndStatus(args.Header, args.Assignee, args.Status, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listHistoricTaskByAssigneeAndStatus: "+err.Error())
		oprot.WriteMessageBegin("listHistoricTaskByAssigneeAndStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listHistoricTaskByAssigneeAndStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorGetTaskById struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorGetTaskById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTaskByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTaskById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTaskByIdResult()
	if result.Success, result.Wfe, err = p.handler.GetTaskById(args.Header, args.TaskId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTaskById: "+err.Error())
		oprot.WriteMessageBegin("getTaskById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTaskById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorGetTaskFormPropertiesByTaskId struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorGetTaskFormPropertiesByTaskId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTaskFormPropertiesByTaskIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTaskFormPropertiesByTaskId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTaskFormPropertiesByTaskIdResult()
	if result.Success, result.Wfe, err = p.handler.GetTaskFormPropertiesByTaskId(args.Header, args.TaskId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTaskFormPropertiesByTaskId: "+err.Error())
		oprot.WriteMessageBegin("getTaskFormPropertiesByTaskId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTaskFormPropertiesByTaskId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorGetProcessFormPropertiesByDefinitionId struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorGetProcessFormPropertiesByDefinitionId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetProcessFormPropertiesByDefinitionIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getProcessFormPropertiesByDefinitionId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetProcessFormPropertiesByDefinitionIdResult()
	if result.Success, result.Wfe, err = p.handler.GetProcessFormPropertiesByDefinitionId(args.Header, args.ProcessDefinitionId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getProcessFormPropertiesByDefinitionId: "+err.Error())
		oprot.WriteMessageBegin("getProcessFormPropertiesByDefinitionId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getProcessFormPropertiesByDefinitionId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorStartProcessInstanceByProcessDefinitionId struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorStartProcessInstanceByProcessDefinitionId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewStartProcessInstanceByProcessDefinitionIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewStartProcessInstanceByProcessDefinitionIdResult()
	if result.Success, result.Wfe, err = p.handler.StartProcessInstanceByProcessDefinitionId(args.Header, args.ProcessDefinitionId, args.Variables, args.UserId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing startProcessInstanceByProcessDefinitionId: "+err.Error())
		oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorStartProcessInstanceByProcessDefinitionKey struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorStartProcessInstanceByProcessDefinitionKey) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewStartProcessInstanceByProcessDefinitionKeyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionKey", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewStartProcessInstanceByProcessDefinitionKeyResult()
	if result.Success, result.Wfe, err = p.handler.StartProcessInstanceByProcessDefinitionKey(args.Header, args.ProcessDefinitionKey, args.Variables, args.UserId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing startProcessInstanceByProcessDefinitionKey: "+err.Error())
		oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionKey", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("startProcessInstanceByProcessDefinitionKey", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorClaimTask struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorClaimTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewClaimTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("claimTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewClaimTaskResult()
	if result.Wfe, err = p.handler.ClaimTask(args.Header, args.TaskId, args.UserId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing claimTask: "+err.Error())
		oprot.WriteMessageBegin("claimTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("claimTask", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorCompleteTask struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorCompleteTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCompleteTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("completeTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCompleteTaskResult()
	if result.Wfe, err = p.handler.CompleteTask(args.Header, args.TaskId, args.Variables); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing completeTask: "+err.Error())
		oprot.WriteMessageBegin("completeTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("completeTask", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListTasksByScript struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListTasksByScript) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTasksByScriptArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTasksByScript", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTasksByScriptResult()
	if result.Success, result.Wfe, err = p.handler.ListTasksByScript(args.Header, args.Script, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTasksByScript: "+err.Error())
		oprot.WriteMessageBegin("listTasksByScript", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTasksByScript", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListHistoricTasksByScript struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListHistoricTasksByScript) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListHistoricTasksByScriptArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listHistoricTasksByScript", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListHistoricTasksByScriptResult()
	if result.Success, result.Wfe, err = p.handler.ListHistoricTasksByScript(args.Header, args.Script, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listHistoricTasksByScript: "+err.Error())
		oprot.WriteMessageBegin("listHistoricTasksByScript", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listHistoricTasksByScript", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorListProcessInstanceByScript struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorListProcessInstanceByScript) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListProcessInstanceByScriptArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listProcessInstanceByScript", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListProcessInstanceByScriptResult()
	if result.Success, result.Wfe, err = p.handler.ListProcessInstanceByScript(args.Header, args.Script, args.Start, args.Size, args.Sort, args.Isasc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listProcessInstanceByScript: "+err.Error())
		oprot.WriteMessageBegin("listProcessInstanceByScript", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listProcessInstanceByScript", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type workFlowServiceProcessorProcessDefinitionDeploy struct {
	handler WorkFlowService
}

func (p *workFlowServiceProcessorProcessDefinitionDeploy) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewProcessDefinitionDeployArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("processDefinitionDeploy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewProcessDefinitionDeployResult()
	if result.Wfe, err = p.handler.ProcessDefinitionDeploy(args.Header, args.ResouceName, args.ResouceContent); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing processDefinitionDeploy: "+err.Error())
		oprot.WriteMessageBegin("processDefinitionDeploy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("processDefinitionDeploy", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ListProcessDefinitionsArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Category string                `thrift:"category,2" json:"category"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Start NumberInt `thrift:"start,10" json:"start"`
	Size  NumberInt `thrift:"size,11" json:"size"`
	Sort  string    `thrift:"sort,12" json:"sort"`
	Isasc bool      `thrift:"isasc,13" json:"isasc"`
}

func NewListProcessDefinitionsArgs() *ListProcessDefinitionsArgs {
	return &ListProcessDefinitionsArgs{}
}

func (p *ListProcessDefinitionsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = v
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessDefinitions_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessDefinitionsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessDefinitionsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Category)); err != nil {
		return fmt.Errorf("%T.category (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:category: %s", p, err)
	}
	return err
}

func (p *ListProcessDefinitionsArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:start: %s", p, err)
	}
	return err
}

func (p *ListProcessDefinitionsArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:size: %s", p, err)
	}
	return err
}

func (p *ListProcessDefinitionsArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sort: %s", p, err)
	}
	return err
}

func (p *ListProcessDefinitionsArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListProcessDefinitionsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessDefinitionsArgs(%+v)", *p)
}

type ListProcessDefinitionsResult struct {
	Success *workflow_types.ProcessDefinitionQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                           `thrift:"wfe,1" json:"wfe"`
}

func NewListProcessDefinitionsResult() *ListProcessDefinitionsResult {
	return &ListProcessDefinitionsResult{}
}

func (p *ListProcessDefinitionsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessDefinitionsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessDefinitionQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListProcessDefinitionsResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListProcessDefinitionsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessDefinitions_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessDefinitionsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessDefinitionsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessDefinitionsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessDefinitionsResult(%+v)", *p)
}

type GetProcessDefinitionByIdArgs struct {
	Header              *common.RequestHeader `thrift:"header,1" json:"header"`
	ProcessDefinitionId string                `thrift:"processDefinitionId,2" json:"processDefinitionId"`
}

func NewGetProcessDefinitionByIdArgs() *GetProcessDefinitionByIdArgs {
	return &GetProcessDefinitionByIdArgs{}
}

func (p *GetProcessDefinitionByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProcessDefinitionByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetProcessDefinitionByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ProcessDefinitionId = v
	}
	return nil
}

func (p *GetProcessDefinitionByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProcessDefinitionById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProcessDefinitionByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetProcessDefinitionByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processDefinitionId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:processDefinitionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessDefinitionId)); err != nil {
		return fmt.Errorf("%T.processDefinitionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:processDefinitionId: %s", p, err)
	}
	return err
}

func (p *GetProcessDefinitionByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProcessDefinitionByIdArgs(%+v)", *p)
}

type GetProcessDefinitionByIdResult struct {
	Success *workflow_types.ProcessDefinitionThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                      `thrift:"wfe,1" json:"wfe"`
}

func NewGetProcessDefinitionByIdResult() *GetProcessDefinitionByIdResult {
	return &GetProcessDefinitionByIdResult{}
}

func (p *GetProcessDefinitionByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProcessDefinitionByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessDefinitionThrift()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetProcessDefinitionByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *GetProcessDefinitionByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProcessDefinitionById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProcessDefinitionByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetProcessDefinitionByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *GetProcessDefinitionByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProcessDefinitionByIdResult(%+v)", *p)
}

type ListProcessInstancesByStatusArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	Status QueryConditionInstanceStatus `thrift:"status,3" json:"status"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Start NumberInt `thrift:"start,10" json:"start"`
	Size  NumberInt `thrift:"size,11" json:"size"`
	Sort  string    `thrift:"sort,12" json:"sort"`
	Isasc bool      `thrift:"isasc,13" json:"isasc"`
}

func NewListProcessInstancesByStatusArgs() *ListProcessInstancesByStatusArgs {
	return &ListProcessInstancesByStatusArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListProcessInstancesByStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ListProcessInstancesByStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = QueryConditionInstanceStatus(v)
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstancesByStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStatusArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:start: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStatusArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:size: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStatusArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sort: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStatusArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstancesByStatusArgs(%+v)", *p)
}

type ListProcessInstancesByStatusResult struct {
	Success *workflow_types.ProcessInstanceQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                         `thrift:"wfe,1" json:"wfe"`
}

func NewListProcessInstancesByStatusResult() *ListProcessInstancesByStatusResult {
	return &ListProcessInstancesByStatusResult{}
}

func (p *ListProcessInstancesByStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessInstanceQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListProcessInstancesByStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListProcessInstancesByStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstancesByStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstancesByStatusResult(%+v)", *p)
}

type ListProcessInstancesByBusinessKeyArgs struct {
	Header      *common.RequestHeader        `thrift:"header,1" json:"header"`
	BusinessKey string                       `thrift:"businessKey,2" json:"businessKey"`
	Status      QueryConditionInstanceStatus `thrift:"status,3" json:"status"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Start NumberInt `thrift:"start,10" json:"start"`
	Size  NumberInt `thrift:"size,11" json:"size"`
	Sort  string    `thrift:"sort,12" json:"sort"`
	Isasc bool      `thrift:"isasc,13" json:"isasc"`
}

func NewListProcessInstancesByBusinessKeyArgs() *ListProcessInstancesByBusinessKeyArgs {
	return &ListProcessInstancesByBusinessKeyArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListProcessInstancesByBusinessKeyArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ListProcessInstancesByBusinessKeyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BusinessKey = v
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = QueryConditionInstanceStatus(v)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstancesByBusinessKey_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("businessKey", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:businessKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BusinessKey)); err != nil {
		return fmt.Errorf("%T.businessKey (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:businessKey: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:start: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:size: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sort: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstancesByBusinessKeyArgs(%+v)", *p)
}

type ListProcessInstancesByBusinessKeyResult struct {
	Success *workflow_types.ProcessInstanceQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                         `thrift:"wfe,1" json:"wfe"`
}

func NewListProcessInstancesByBusinessKeyResult() *ListProcessInstancesByBusinessKeyResult {
	return &ListProcessInstancesByBusinessKeyResult{}
}

func (p *ListProcessInstancesByBusinessKeyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessInstanceQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstancesByBusinessKey_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByBusinessKeyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByBusinessKeyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstancesByBusinessKeyResult(%+v)", *p)
}

type ListProcessInstancesByStartUserIdArgs struct {
	Header      *common.RequestHeader        `thrift:"header,1" json:"header"`
	StartUserId string                       `thrift:"startUserId,2" json:"startUserId"`
	Status      QueryConditionInstanceStatus `thrift:"status,3" json:"status"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Start NumberInt `thrift:"start,10" json:"start"`
	Size  NumberInt `thrift:"size,11" json:"size"`
	Sort  string    `thrift:"sort,12" json:"sort"`
	Isasc bool      `thrift:"isasc,13" json:"isasc"`
}

func NewListProcessInstancesByStartUserIdArgs() *ListProcessInstancesByStartUserIdArgs {
	return &ListProcessInstancesByStartUserIdArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListProcessInstancesByStartUserIdArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ListProcessInstancesByStartUserIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StartUserId = v
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = QueryConditionInstanceStatus(v)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstancesByStartUserId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startUserId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:startUserId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StartUserId)); err != nil {
		return fmt.Errorf("%T.startUserId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:startUserId: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:start: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:size: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sort: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstancesByStartUserIdArgs(%+v)", *p)
}

type ListProcessInstancesByStartUserIdResult struct {
	Success *workflow_types.ProcessInstanceQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                         `thrift:"wfe,1" json:"wfe"`
}

func NewListProcessInstancesByStartUserIdResult() *ListProcessInstancesByStartUserIdResult {
	return &ListProcessInstancesByStartUserIdResult{}
}

func (p *ListProcessInstancesByStartUserIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessInstanceQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstancesByStartUserId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstancesByStartUserIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstancesByStartUserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstancesByStartUserIdResult(%+v)", *p)
}

type GetTaskSummaryByUserIdArgs struct {
	Header             *common.RequestHeader `thrift:"header,1" json:"header"`
	UserId             string                `thrift:"userId,2" json:"userId"`
	GroupIds           map[string]bool       `thrift:"groupIds,3" json:"groupIds"`
	TaskDefinitionKeys map[string]bool       `thrift:"taskDefinitionKeys,4" json:"taskDefinitionKeys"`
}

func NewGetTaskSummaryByUserIdArgs() *GetTaskSummaryByUserIdArgs {
	return &GetTaskSummaryByUserIdArgs{}
}

func (p *GetTaskSummaryByUserIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.SET {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.SET {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *GetTaskSummaryByUserIdArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadSetBegin()
	if err != nil {
		return fmt.Errorf("error reading set being: %s")
	}
	p.GroupIds = make(map[string]bool, size)
	for i := 0; i < size; i++ {
		var _elem82 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem82 = v
		}
		p.GroupIds[_elem82] = true
	}
	if err := iprot.ReadSetEnd(); err != nil {
		return fmt.Errorf("error reading set end: %s", err)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadSetBegin()
	if err != nil {
		return fmt.Errorf("error reading set being: %s")
	}
	p.TaskDefinitionKeys = make(map[string]bool, size)
	for i := 0; i < size; i++ {
		var _elem83 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem83 = v
		}
		p.TaskDefinitionKeys[_elem83] = true
	}
	if err := iprot.ReadSetEnd(); err != nil {
		return fmt.Errorf("error reading set end: %s", err)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskSummaryByUserId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskSummaryByUserIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:userId: %s", p, err)
	}
	return err
}

func (p *GetTaskSummaryByUserIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.GroupIds != nil {
		if err := oprot.WriteFieldBegin("groupIds", thrift.SET, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:groupIds: %s", p, err)
		}
		if err := oprot.WriteSetBegin(thrift.STRING, len(p.GroupIds)); err != nil {
			return fmt.Errorf("error writing set begin: %s")
		}
		for v, _ := range p.GroupIds {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteSetEnd(); err != nil {
			return fmt.Errorf("error writing set end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:groupIds: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskSummaryByUserIdArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.TaskDefinitionKeys != nil {
		if err := oprot.WriteFieldBegin("taskDefinitionKeys", thrift.SET, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:taskDefinitionKeys: %s", p, err)
		}
		if err := oprot.WriteSetBegin(thrift.STRING, len(p.TaskDefinitionKeys)); err != nil {
			return fmt.Errorf("error writing set begin: %s")
		}
		for v, _ := range p.TaskDefinitionKeys {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteSetEnd(); err != nil {
			return fmt.Errorf("error writing set end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:taskDefinitionKeys: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskSummaryByUserIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskSummaryByUserIdArgs(%+v)", *p)
}

type GetTaskSummaryByUserIdResult struct {
	Success *workflow_types.TaskSummaryThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                `thrift:"wfe,1" json:"wfe"`
}

func NewGetTaskSummaryByUserIdResult() *GetTaskSummaryByUserIdResult {
	return &GetTaskSummaryByUserIdResult{}
}

func (p *GetTaskSummaryByUserIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskSummaryThrift()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskSummaryByUserId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskSummaryByUserIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskSummaryByUserIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskSummaryByUserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskSummaryByUserIdResult(%+v)", *p)
}

type ListTasksByRoleArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	RoleType QueryConditionRoleType `thrift:"roleType,2" json:"roleType"`
	Role     string                 `thrift:"role,3" json:"role"`
	Start    NumberInt              `thrift:"start,4" json:"start"`
	Size     NumberInt              `thrift:"size,5" json:"size"`
	Sort     string                 `thrift:"sort,6" json:"sort"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	Isasc bool `thrift:"isasc,13" json:"isasc"`
}

func NewListTasksByRoleArgs() *ListTasksByRoleArgs {
	return &ListTasksByRoleArgs{
		RoleType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListTasksByRoleArgs) IsSetRoleType() bool {
	return int64(p.RoleType) != math.MinInt32-1
}

func (p *ListTasksByRoleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RoleType = QueryConditionRoleType(v)
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Role = v
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListTasksByRoleArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListTasksByRoleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTasksByRole_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByRoleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByRoleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("roleType", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:roleType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RoleType)); err != nil {
		return fmt.Errorf("%T.roleType (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:roleType: %s", p, err)
	}
	return err
}

func (p *ListTasksByRoleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("role", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:role: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Role)); err != nil {
		return fmt.Errorf("%T.role (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:role: %s", p, err)
	}
	return err
}

func (p *ListTasksByRoleArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:start: %s", p, err)
	}
	return err
}

func (p *ListTasksByRoleArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:size: %s", p, err)
	}
	return err
}

func (p *ListTasksByRoleArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:sort: %s", p, err)
	}
	return err
}

func (p *ListTasksByRoleArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListTasksByRoleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTasksByRoleArgs(%+v)", *p)
}

type ListTasksByRoleResult struct {
	Success *workflow_types.TaskQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException              `thrift:"wfe,1" json:"wfe"`
}

func NewListTasksByRoleResult() *ListTasksByRoleResult {
	return &ListTasksByRoleResult{}
}

func (p *ListTasksByRoleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByRoleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListTasksByRoleResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListTasksByRoleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTasksByRole_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByRoleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByRoleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByRoleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTasksByRoleResult(%+v)", *p)
}

type ListTasksByTaskDefinitionKeyArgs struct {
	Header            *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskDefinitionKey string                `thrift:"taskDefinitionKey,2" json:"taskDefinitionKey"`
	Start             NumberInt             `thrift:"start,3" json:"start"`
	Size              NumberInt             `thrift:"size,4" json:"size"`
	Sort              string                `thrift:"sort,5" json:"sort"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	Isasc bool `thrift:"isasc,13" json:"isasc"`
}

func NewListTasksByTaskDefinitionKeyArgs() *ListTasksByTaskDefinitionKeyArgs {
	return &ListTasksByTaskDefinitionKeyArgs{}
}

func (p *ListTasksByTaskDefinitionKeyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskDefinitionKey = v
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTasksByTaskDefinitionKey_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskDefinitionKey", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:taskDefinitionKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskDefinitionKey)); err != nil {
		return fmt.Errorf("%T.taskDefinitionKey (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:taskDefinitionKey: %s", p, err)
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sort: %s", p, err)
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTasksByTaskDefinitionKeyArgs(%+v)", *p)
}

type ListTasksByTaskDefinitionKeyResult struct {
	Success *workflow_types.TaskQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException              `thrift:"wfe,1" json:"wfe"`
}

func NewListTasksByTaskDefinitionKeyResult() *ListTasksByTaskDefinitionKeyResult {
	return &ListTasksByTaskDefinitionKeyResult{}
}

func (p *ListTasksByTaskDefinitionKeyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTasksByTaskDefinitionKey_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByTaskDefinitionKeyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByTaskDefinitionKeyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTasksByTaskDefinitionKeyResult(%+v)", *p)
}

type ListHistoricTaskByAssigneeAndStatusArgs struct {
	Header   *common.RequestHeader        `thrift:"header,1" json:"header"`
	Assignee string                       `thrift:"assignee,2" json:"assignee"`
	Status   QueryConditionInstanceStatus `thrift:"status,3" json:"status"`
	Start    NumberInt                    `thrift:"start,4" json:"start"`
	Size     NumberInt                    `thrift:"size,5" json:"size"`
	Sort     string                       `thrift:"sort,6" json:"sort"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	Isasc bool `thrift:"isasc,13" json:"isasc"`
}

func NewListHistoricTaskByAssigneeAndStatusArgs() *ListHistoricTaskByAssigneeAndStatusArgs {
	return &ListHistoricTaskByAssigneeAndStatusArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Assignee = v
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = QueryConditionInstanceStatus(v)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listHistoricTaskByAssigneeAndStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assignee", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:assignee: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Assignee)); err != nil {
		return fmt.Errorf("%T.assignee (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:assignee: %s", p, err)
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:start: %s", p, err)
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:size: %s", p, err)
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:sort: %s", p, err)
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHistoricTaskByAssigneeAndStatusArgs(%+v)", *p)
}

type ListHistoricTaskByAssigneeAndStatusResult struct {
	Success *workflow_types.TaskQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException              `thrift:"wfe,1" json:"wfe"`
}

func NewListHistoricTaskByAssigneeAndStatusResult() *ListHistoricTaskByAssigneeAndStatusResult {
	return &ListHistoricTaskByAssigneeAndStatusResult{}
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listHistoricTaskByAssigneeAndStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListHistoricTaskByAssigneeAndStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHistoricTaskByAssigneeAndStatusResult(%+v)", *p)
}

type GetTaskByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId string                `thrift:"taskId,2" json:"taskId"`
}

func NewGetTaskByIdArgs() *GetTaskByIdArgs {
	return &GetTaskByIdArgs{}
}

func (p *GetTaskByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTaskByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *GetTaskByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:taskId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskId)); err != nil {
		return fmt.Errorf("%T.taskId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:taskId: %s", p, err)
	}
	return err
}

func (p *GetTaskByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskByIdArgs(%+v)", *p)
}

type GetTaskByIdResult struct {
	Success *workflow_types.TaskThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException         `thrift:"wfe,1" json:"wfe"`
}

func NewGetTaskByIdResult() *GetTaskByIdResult {
	return &GetTaskByIdResult{}
}

func (p *GetTaskByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskThrift()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTaskByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *GetTaskByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskByIdResult(%+v)", *p)
}

type GetTaskFormPropertiesByTaskIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId string                `thrift:"taskId,2" json:"taskId"`
}

func NewGetTaskFormPropertiesByTaskIdArgs() *GetTaskFormPropertiesByTaskIdArgs {
	return &GetTaskFormPropertiesByTaskIdArgs{}
}

func (p *GetTaskFormPropertiesByTaskIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskFormPropertiesByTaskId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskFormPropertiesByTaskIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:taskId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskId)); err != nil {
		return fmt.Errorf("%T.taskId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:taskId: %s", p, err)
	}
	return err
}

func (p *GetTaskFormPropertiesByTaskIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskFormPropertiesByTaskIdArgs(%+v)", *p)
}

type GetTaskFormPropertiesByTaskIdResult struct {
	Success []*workflow_types.FormPropertyThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                   `thrift:"wfe,1" json:"wfe"`
}

func NewGetTaskFormPropertiesByTaskIdResult() *GetTaskFormPropertiesByTaskIdResult {
	return &GetTaskFormPropertiesByTaskIdResult{}
}

func (p *GetTaskFormPropertiesByTaskIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*workflow_types.FormPropertyThrift, 0, size)
	for i := 0; i < size; i++ {
		_elem84 := workflow_types.NewFormPropertyThrift()
		if err := _elem84.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem84)
		}
		p.Success = append(p.Success, _elem84)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskFormPropertiesByTaskId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskFormPropertiesByTaskIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskFormPropertiesByTaskIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskFormPropertiesByTaskIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskFormPropertiesByTaskIdResult(%+v)", *p)
}

type GetProcessFormPropertiesByDefinitionIdArgs struct {
	Header              *common.RequestHeader `thrift:"header,1" json:"header"`
	ProcessDefinitionId string                `thrift:"processDefinitionId,2" json:"processDefinitionId"`
}

func NewGetProcessFormPropertiesByDefinitionIdArgs() *GetProcessFormPropertiesByDefinitionIdArgs {
	return &GetProcessFormPropertiesByDefinitionIdArgs{}
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ProcessDefinitionId = v
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProcessFormPropertiesByDefinitionId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processDefinitionId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:processDefinitionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessDefinitionId)); err != nil {
		return fmt.Errorf("%T.processDefinitionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:processDefinitionId: %s", p, err)
	}
	return err
}

func (p *GetProcessFormPropertiesByDefinitionIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProcessFormPropertiesByDefinitionIdArgs(%+v)", *p)
}

type GetProcessFormPropertiesByDefinitionIdResult struct {
	Success []*workflow_types.FormPropertyThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                   `thrift:"wfe,1" json:"wfe"`
}

func NewGetProcessFormPropertiesByDefinitionIdResult() *GetProcessFormPropertiesByDefinitionIdResult {
	return &GetProcessFormPropertiesByDefinitionIdResult{}
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*workflow_types.FormPropertyThrift, 0, size)
	for i := 0; i < size; i++ {
		_elem85 := workflow_types.NewFormPropertyThrift()
		if err := _elem85.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem85)
		}
		p.Success = append(p.Success, _elem85)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProcessFormPropertiesByDefinitionId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *GetProcessFormPropertiesByDefinitionIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProcessFormPropertiesByDefinitionIdResult(%+v)", *p)
}

type StartProcessInstanceByProcessDefinitionIdArgs struct {
	Header              *common.RequestHeader `thrift:"header,1" json:"header"`
	ProcessDefinitionId string                `thrift:"processDefinitionId,2" json:"processDefinitionId"`
	Variables           map[string]string     `thrift:"variables,3" json:"variables"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	UserId string `thrift:"userId,10" json:"userId"`
}

func NewStartProcessInstanceByProcessDefinitionIdArgs() *StartProcessInstanceByProcessDefinitionIdArgs {
	return &StartProcessInstanceByProcessDefinitionIdArgs{}
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ProcessDefinitionId = v
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Variables = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key86 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key86 = v
		}
		var _val87 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val87 = v
		}
		p.Variables[_key86] = _val87
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("startProcessInstanceByProcessDefinitionId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processDefinitionId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:processDefinitionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessDefinitionId)); err != nil {
		return fmt.Errorf("%T.processDefinitionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:processDefinitionId: %s", p, err)
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Variables != nil {
		if err := oprot.WriteFieldBegin("variables", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:variables: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Variables)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Variables {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:variables: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:userId: %s", p, err)
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StartProcessInstanceByProcessDefinitionIdArgs(%+v)", *p)
}

type StartProcessInstanceByProcessDefinitionIdResult struct {
	Success *workflow_types.ProcessInstanceThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                    `thrift:"wfe,1" json:"wfe"`
}

func NewStartProcessInstanceByProcessDefinitionIdResult() *StartProcessInstanceByProcessDefinitionIdResult {
	return &StartProcessInstanceByProcessDefinitionIdResult{}
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessInstanceThrift()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("startProcessInstanceByProcessDefinitionId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StartProcessInstanceByProcessDefinitionIdResult(%+v)", *p)
}

type StartProcessInstanceByProcessDefinitionKeyArgs struct {
	Header               *common.RequestHeader `thrift:"header,1" json:"header"`
	ProcessDefinitionKey string                `thrift:"processDefinitionKey,2" json:"processDefinitionKey"`
	Variables            map[string]string     `thrift:"variables,3" json:"variables"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	UserId string `thrift:"userId,10" json:"userId"`
}

func NewStartProcessInstanceByProcessDefinitionKeyArgs() *StartProcessInstanceByProcessDefinitionKeyArgs {
	return &StartProcessInstanceByProcessDefinitionKeyArgs{}
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ProcessDefinitionKey = v
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Variables = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key88 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key88 = v
		}
		var _val89 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val89 = v
		}
		p.Variables[_key88] = _val89
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("startProcessInstanceByProcessDefinitionKey_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processDefinitionKey", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:processDefinitionKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessDefinitionKey)); err != nil {
		return fmt.Errorf("%T.processDefinitionKey (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:processDefinitionKey: %s", p, err)
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Variables != nil {
		if err := oprot.WriteFieldBegin("variables", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:variables: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Variables)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Variables {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:variables: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:userId: %s", p, err)
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionKeyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StartProcessInstanceByProcessDefinitionKeyArgs(%+v)", *p)
}

type StartProcessInstanceByProcessDefinitionKeyResult struct {
	Success *workflow_types.ProcessInstanceThrift `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                    `thrift:"wfe,1" json:"wfe"`
}

func NewStartProcessInstanceByProcessDefinitionKeyResult() *StartProcessInstanceByProcessDefinitionKeyResult {
	return &StartProcessInstanceByProcessDefinitionKeyResult{}
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessInstanceThrift()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("startProcessInstanceByProcessDefinitionKey_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *StartProcessInstanceByProcessDefinitionKeyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StartProcessInstanceByProcessDefinitionKeyResult(%+v)", *p)
}

type ClaimTaskArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId string                `thrift:"taskId,2" json:"taskId"`
	UserId string                `thrift:"userId,3" json:"userId"`
}

func NewClaimTaskArgs() *ClaimTaskArgs {
	return &ClaimTaskArgs{}
}

func (p *ClaimTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ClaimTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ClaimTaskArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *ClaimTaskArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *ClaimTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("claimTask_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ClaimTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ClaimTaskArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:taskId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskId)); err != nil {
		return fmt.Errorf("%T.taskId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:taskId: %s", p, err)
	}
	return err
}

func (p *ClaimTaskArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:userId: %s", p, err)
	}
	return err
}

func (p *ClaimTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClaimTaskArgs(%+v)", *p)
}

type ClaimTaskResult struct {
	Wfe *WorkFlowException `thrift:"wfe,1" json:"wfe"`
}

func NewClaimTaskResult() *ClaimTaskResult {
	return &ClaimTaskResult{}
}

func (p *ClaimTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ClaimTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ClaimTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("claimTask_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ClaimTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ClaimTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClaimTaskResult(%+v)", *p)
}

type CompleteTaskArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId    string                `thrift:"taskId,2" json:"taskId"`
	Variables map[string]string     `thrift:"variables,3" json:"variables"`
}

func NewCompleteTaskArgs() *CompleteTaskArgs {
	return &CompleteTaskArgs{}
}

func (p *CompleteTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompleteTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CompleteTaskArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *CompleteTaskArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Variables = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key90 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key90 = v
		}
		var _val91 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val91 = v
		}
		p.Variables[_key90] = _val91
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CompleteTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("completeTask_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompleteTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CompleteTaskArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:taskId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskId)); err != nil {
		return fmt.Errorf("%T.taskId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:taskId: %s", p, err)
	}
	return err
}

func (p *CompleteTaskArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Variables != nil {
		if err := oprot.WriteFieldBegin("variables", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:variables: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Variables)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Variables {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:variables: %s", p, err)
		}
	}
	return err
}

func (p *CompleteTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompleteTaskArgs(%+v)", *p)
}

type CompleteTaskResult struct {
	Wfe *WorkFlowException `thrift:"wfe,1" json:"wfe"`
}

func NewCompleteTaskResult() *CompleteTaskResult {
	return &CompleteTaskResult{}
}

func (p *CompleteTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompleteTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *CompleteTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("completeTask_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompleteTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *CompleteTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompleteTaskResult(%+v)", *p)
}

type ListTasksByScriptArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Script string                `thrift:"script,2" json:"script"`
	Start  NumberInt             `thrift:"start,3" json:"start"`
	Size   NumberInt             `thrift:"size,4" json:"size"`
	Sort   string                `thrift:"sort,5" json:"sort"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	Isasc bool `thrift:"isasc,13" json:"isasc"`
}

func NewListTasksByScriptArgs() *ListTasksByScriptArgs {
	return &ListTasksByScriptArgs{}
}

func (p *ListTasksByScriptArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByScriptArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTasksByScriptArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Script = v
	}
	return nil
}

func (p *ListTasksByScriptArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListTasksByScriptArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListTasksByScriptArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListTasksByScriptArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListTasksByScriptArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTasksByScript_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByScriptArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByScriptArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("script", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:script: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Script)); err != nil {
		return fmt.Errorf("%T.script (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:script: %s", p, err)
	}
	return err
}

func (p *ListTasksByScriptArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *ListTasksByScriptArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *ListTasksByScriptArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sort: %s", p, err)
	}
	return err
}

func (p *ListTasksByScriptArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListTasksByScriptArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTasksByScriptArgs(%+v)", *p)
}

type ListTasksByScriptResult struct {
	Success *workflow_types.TaskQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException              `thrift:"wfe,1" json:"wfe"`
}

func NewListTasksByScriptResult() *ListTasksByScriptResult {
	return &ListTasksByScriptResult{}
}

func (p *ListTasksByScriptResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByScriptResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListTasksByScriptResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListTasksByScriptResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTasksByScript_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTasksByScriptResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByScriptResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListTasksByScriptResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTasksByScriptResult(%+v)", *p)
}

type ListHistoricTasksByScriptArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Script string                `thrift:"script,2" json:"script"`
	Start  NumberInt             `thrift:"start,3" json:"start"`
	Size   NumberInt             `thrift:"size,4" json:"size"`
	Sort   string                `thrift:"sort,5" json:"sort"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	Isasc bool `thrift:"isasc,13" json:"isasc"`
}

func NewListHistoricTasksByScriptArgs() *ListHistoricTasksByScriptArgs {
	return &ListHistoricTasksByScriptArgs{}
}

func (p *ListHistoricTasksByScriptArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Script = v
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listHistoricTasksByScript_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTasksByScriptArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListHistoricTasksByScriptArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("script", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:script: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Script)); err != nil {
		return fmt.Errorf("%T.script (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:script: %s", p, err)
	}
	return err
}

func (p *ListHistoricTasksByScriptArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *ListHistoricTasksByScriptArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *ListHistoricTasksByScriptArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sort: %s", p, err)
	}
	return err
}

func (p *ListHistoricTasksByScriptArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListHistoricTasksByScriptArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHistoricTasksByScriptArgs(%+v)", *p)
}

type ListHistoricTasksByScriptResult struct {
	Success *workflow_types.TaskQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException              `thrift:"wfe,1" json:"wfe"`
}

func NewListHistoricTasksByScriptResult() *ListHistoricTasksByScriptResult {
	return &ListHistoricTasksByScriptResult{}
}

func (p *ListHistoricTasksByScriptResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTasksByScriptResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewTaskQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListHistoricTasksByScriptResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListHistoricTasksByScriptResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listHistoricTasksByScript_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListHistoricTasksByScriptResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListHistoricTasksByScriptResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListHistoricTasksByScriptResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListHistoricTasksByScriptResult(%+v)", *p)
}

type ListProcessInstanceByScriptArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Script string                `thrift:"script,2" json:"script"`
	Start  NumberInt             `thrift:"start,3" json:"start"`
	Size   NumberInt             `thrift:"size,4" json:"size"`
	Sort   string                `thrift:"sort,5" json:"sort"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	Isasc bool `thrift:"isasc,13" json:"isasc"`
}

func NewListProcessInstanceByScriptArgs() *ListProcessInstanceByScriptArgs {
	return &ListProcessInstanceByScriptArgs{}
}

func (p *ListProcessInstanceByScriptArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Script = v
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstanceByScript_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstanceByScriptArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstanceByScriptArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("script", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:script: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Script)); err != nil {
		return fmt.Errorf("%T.script (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:script: %s", p, err)
	}
	return err
}

func (p *ListProcessInstanceByScriptArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *ListProcessInstanceByScriptArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *ListProcessInstanceByScriptArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sort: %s", p, err)
	}
	return err
}

func (p *ListProcessInstanceByScriptArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isasc: %s", p, err)
	}
	return err
}

func (p *ListProcessInstanceByScriptArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstanceByScriptArgs(%+v)", *p)
}

type ListProcessInstanceByScriptResult struct {
	Success *workflow_types.ProcessInstanceQueryResult `thrift:"success,0" json:"success"`
	Wfe     *WorkFlowException                         `thrift:"wfe,1" json:"wfe"`
}

func NewListProcessInstanceByScriptResult() *ListProcessInstanceByScriptResult {
	return &ListProcessInstanceByScriptResult{}
}

func (p *ListProcessInstanceByScriptResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstanceByScriptResult) readField0(iprot thrift.TProtocol) error {
	p.Success = workflow_types.NewProcessInstanceQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListProcessInstanceByScriptResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ListProcessInstanceByScriptResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listProcessInstanceByScript_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListProcessInstanceByScriptResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstanceByScriptResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ListProcessInstanceByScriptResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListProcessInstanceByScriptResult(%+v)", *p)
}

type ProcessDefinitionDeployArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	ResouceName    string                `thrift:"resouceName,2" json:"resouceName"`
	ResouceContent string                `thrift:"resouceContent,3" json:"resouceContent"`
}

func NewProcessDefinitionDeployArgs() *ProcessDefinitionDeployArgs {
	return &ProcessDefinitionDeployArgs{}
}

func (p *ProcessDefinitionDeployArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionDeployArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ProcessDefinitionDeployArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ResouceName = v
	}
	return nil
}

func (p *ProcessDefinitionDeployArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ResouceContent = v
	}
	return nil
}

func (p *ProcessDefinitionDeployArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processDefinitionDeploy_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionDeployArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ProcessDefinitionDeployArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("resouceName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:resouceName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResouceName)); err != nil {
		return fmt.Errorf("%T.resouceName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:resouceName: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionDeployArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("resouceContent", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:resouceContent: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResouceContent)); err != nil {
		return fmt.Errorf("%T.resouceContent (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:resouceContent: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionDeployArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessDefinitionDeployArgs(%+v)", *p)
}

type ProcessDefinitionDeployResult struct {
	Wfe *WorkFlowException `thrift:"wfe,1" json:"wfe"`
}

func NewProcessDefinitionDeployResult() *ProcessDefinitionDeployResult {
	return &ProcessDefinitionDeployResult{}
}

func (p *ProcessDefinitionDeployResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionDeployResult) readField1(iprot thrift.TProtocol) error {
	p.Wfe = NewWorkFlowException()
	if err := p.Wfe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Wfe)
	}
	return nil
}

func (p *ProcessDefinitionDeployResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processDefinitionDeploy_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Wfe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionDeployResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Wfe != nil {
		if err := oprot.WriteFieldBegin("wfe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:wfe: %s", p, err)
		}
		if err := p.Wfe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Wfe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:wfe: %s", p, err)
		}
	}
	return err
}

func (p *ProcessDefinitionDeployResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessDefinitionDeployResult(%+v)", *p)
}
