// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_plus_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type PauseStatus int64

const (
	PauseStatus_PS_RUNNABLE PauseStatus = 0
	PauseStatus_PS_PAUSED   PauseStatus = 1
)

func (p PauseStatus) String() string {
	switch p {
	case PauseStatus_PS_RUNNABLE:
		return "PauseStatus_PS_RUNNABLE"
	case PauseStatus_PS_PAUSED:
		return "PauseStatus_PS_PAUSED"
	}
	return "<UNSET>"
}

func PauseStatusFromString(s string) (PauseStatus, error) {
	switch s {
	case "PauseStatus_PS_RUNNABLE":
		return PauseStatus_PS_RUNNABLE, nil
	case "PauseStatus_PS_PAUSED":
		return PauseStatus_PS_PAUSED, nil
	}
	return PauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PauseStatus string")
}

//删除状态
type DeleteStatus int64

const (
	DeleteStatus_DS_RUNNABLE DeleteStatus = 0
	DeleteStatus_DS_DELETED  DeleteStatus = 1
)

func (p DeleteStatus) String() string {
	switch p {
	case DeleteStatus_DS_RUNNABLE:
		return "DeleteStatus_DS_RUNNABLE"
	case DeleteStatus_DS_DELETED:
		return "DeleteStatus_DS_DELETED"
	}
	return "<UNSET>"
}

func DeleteStatusFromString(s string) (DeleteStatus, error) {
	switch s {
	case "DeleteStatus_DS_RUNNABLE":
		return DeleteStatus_DS_RUNNABLE, nil
	case "DeleteStatus_DS_DELETED":
		return DeleteStatus_DS_DELETED, nil
	}
	return DeleteStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DeleteStatus string")
}

type ProcessStatus int64

const (
	ProcessStatus_PS_NEW      ProcessStatus = 1
	ProcessStatus_PS_COMMITED ProcessStatus = 2
	ProcessStatus_PS_RUNNING  ProcessStatus = 3
	ProcessStatus_PS_SUCESS   ProcessStatus = 4
	ProcessStatus_PS_FAILURE  ProcessStatus = 5
	ProcessStatus_PS_STOPED   ProcessStatus = 101
)

func (p ProcessStatus) String() string {
	switch p {
	case ProcessStatus_PS_NEW:
		return "ProcessStatus_PS_NEW"
	case ProcessStatus_PS_COMMITED:
		return "ProcessStatus_PS_COMMITED"
	case ProcessStatus_PS_RUNNING:
		return "ProcessStatus_PS_RUNNING"
	case ProcessStatus_PS_SUCESS:
		return "ProcessStatus_PS_SUCESS"
	case ProcessStatus_PS_FAILURE:
		return "ProcessStatus_PS_FAILURE"
	case ProcessStatus_PS_STOPED:
		return "ProcessStatus_PS_STOPED"
	}
	return "<UNSET>"
}

func ProcessStatusFromString(s string) (ProcessStatus, error) {
	switch s {
	case "ProcessStatus_PS_NEW":
		return ProcessStatus_PS_NEW, nil
	case "ProcessStatus_PS_COMMITED":
		return ProcessStatus_PS_COMMITED, nil
	case "ProcessStatus_PS_RUNNING":
		return ProcessStatus_PS_RUNNING, nil
	case "ProcessStatus_PS_SUCESS":
		return ProcessStatus_PS_SUCESS, nil
	case "ProcessStatus_PS_FAILURE":
		return ProcessStatus_PS_FAILURE, nil
	case "ProcessStatus_PS_STOPED":
		return ProcessStatus_PS_STOPED, nil
	}
	return ProcessStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ProcessStatus string")
}

type AudienceBuildType int64

const (
	AudienceBuildType_ABT_UNKNOW AudienceBuildType = 0
	AudienceBuildType_ABT_LABEL  AudienceBuildType = 1
	AudienceBuildType_ABT_FILE   AudienceBuildType = 2
)

func (p AudienceBuildType) String() string {
	switch p {
	case AudienceBuildType_ABT_UNKNOW:
		return "AudienceBuildType_ABT_UNKNOW"
	case AudienceBuildType_ABT_LABEL:
		return "AudienceBuildType_ABT_LABEL"
	case AudienceBuildType_ABT_FILE:
		return "AudienceBuildType_ABT_FILE"
	}
	return "<UNSET>"
}

func AudienceBuildTypeFromString(s string) (AudienceBuildType, error) {
	switch s {
	case "AudienceBuildType_ABT_UNKNOW":
		return AudienceBuildType_ABT_UNKNOW, nil
	case "AudienceBuildType_ABT_LABEL":
		return AudienceBuildType_ABT_LABEL, nil
	case "AudienceBuildType_ABT_FILE":
		return AudienceBuildType_ABT_FILE, nil
	}
	return AudienceBuildType(math.MinInt32 - 1), fmt.Errorf("not a valid AudienceBuildType string")
}

type AudienceType int64

const (
	AudienceType_AT_UNKNOW AudienceType = 0
	AudienceType_AT_LABEL  AudienceType = 1
	AudienceType_AT_FILE   AudienceType = 2
)

func (p AudienceType) String() string {
	switch p {
	case AudienceType_AT_UNKNOW:
		return "AudienceType_AT_UNKNOW"
	case AudienceType_AT_LABEL:
		return "AudienceType_AT_LABEL"
	case AudienceType_AT_FILE:
		return "AudienceType_AT_FILE"
	}
	return "<UNSET>"
}

func AudienceTypeFromString(s string) (AudienceType, error) {
	switch s {
	case "AudienceType_AT_UNKNOW":
		return AudienceType_AT_UNKNOW, nil
	case "AudienceType_AT_LABEL":
		return AudienceType_AT_LABEL, nil
	case "AudienceType_AT_FILE":
		return AudienceType_AT_FILE, nil
	}
	return AudienceType(math.MinInt32 - 1), fmt.Errorf("not a valid AudienceType string")
}

type PictureStatus int64

const (
	PictureStatus_PS_data              PictureStatus = 0
	PictureStatus_PS_NEW               PictureStatus = 1
	PictureStatus_PS_RUNNING           PictureStatus = 2
	PictureStatus_PS_SUCESS            PictureStatus = 3
	PictureStatus_PS_FAILURE           PictureStatus = 4
	PictureStatus_PS_WAIT_FILE_SCANNED PictureStatus = 5
)

func (p PictureStatus) String() string {
	switch p {
	case PictureStatus_PS_data:
		return "PictureStatus_PS_data"
	case PictureStatus_PS_NEW:
		return "PictureStatus_PS_NEW"
	case PictureStatus_PS_RUNNING:
		return "PictureStatus_PS_RUNNING"
	case PictureStatus_PS_SUCESS:
		return "PictureStatus_PS_SUCESS"
	case PictureStatus_PS_FAILURE:
		return "PictureStatus_PS_FAILURE"
	case PictureStatus_PS_WAIT_FILE_SCANNED:
		return "PictureStatus_PS_WAIT_FILE_SCANNED"
	}
	return "<UNSET>"
}

func PictureStatusFromString(s string) (PictureStatus, error) {
	switch s {
	case "PictureStatus_PS_data":
		return PictureStatus_PS_data, nil
	case "PictureStatus_PS_NEW":
		return PictureStatus_PS_NEW, nil
	case "PictureStatus_PS_RUNNING":
		return PictureStatus_PS_RUNNING, nil
	case "PictureStatus_PS_SUCESS":
		return PictureStatus_PS_SUCESS, nil
	case "PictureStatus_PS_FAILURE":
		return PictureStatus_PS_FAILURE, nil
	case "PictureStatus_PS_WAIT_FILE_SCANNED":
		return PictureStatus_PS_WAIT_FILE_SCANNED, nil
	}
	return PictureStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PictureStatus string")
}

type DataType int64

const (
	DataType_DT_UNKNOWN  DataType = 0
	DataType_DT_IMEI     DataType = 1
	DataType_DT_IDFA     DataType = 2
	DataType_DT_IMEI_MD5 DataType = 3
	DataType_DT_IDFA_MD5 DataType = 4
)

func (p DataType) String() string {
	switch p {
	case DataType_DT_UNKNOWN:
		return "DataType_DT_UNKNOWN"
	case DataType_DT_IMEI:
		return "DataType_DT_IMEI"
	case DataType_DT_IDFA:
		return "DataType_DT_IDFA"
	case DataType_DT_IMEI_MD5:
		return "DataType_DT_IMEI_MD5"
	case DataType_DT_IDFA_MD5:
		return "DataType_DT_IDFA_MD5"
	}
	return "<UNSET>"
}

func DataTypeFromString(s string) (DataType, error) {
	switch s {
	case "DataType_DT_UNKNOWN":
		return DataType_DT_UNKNOWN, nil
	case "DataType_DT_IMEI":
		return DataType_DT_IMEI, nil
	case "DataType_DT_IDFA":
		return DataType_DT_IDFA, nil
	case "DataType_DT_IMEI_MD5":
		return DataType_DT_IMEI_MD5, nil
	case "DataType_DT_IDFA_MD5":
		return DataType_DT_IDFA_MD5, nil
	}
	return DataType(math.MinInt32 - 1), fmt.Errorf("not a valid DataType string")
}

type FileStatus int64

const (
	FileStatus_FS_NEW       FileStatus = 1
	FileStatus_FS_SCANNED   FileStatus = 2
	FileStatus_FS_COMMITTED FileStatus = 3
)

func (p FileStatus) String() string {
	switch p {
	case FileStatus_FS_NEW:
		return "FileStatus_FS_NEW"
	case FileStatus_FS_SCANNED:
		return "FileStatus_FS_SCANNED"
	case FileStatus_FS_COMMITTED:
		return "FileStatus_FS_COMMITTED"
	}
	return "<UNSET>"
}

func FileStatusFromString(s string) (FileStatus, error) {
	switch s {
	case "FileStatus_FS_NEW":
		return FileStatus_FS_NEW, nil
	case "FileStatus_FS_SCANNED":
		return FileStatus_FS_SCANNED, nil
	case "FileStatus_FS_COMMITTED":
		return FileStatus_FS_COMMITTED, nil
	}
	return FileStatus(math.MinInt32 - 1), fmt.Errorf("not a valid FileStatus string")
}

type AudienceParams struct {
	Uids        []int32 `thrift:"uids,1" json:"uids"`
	Name        string  `thrift:"name,2" json:"name"`
	ProjectName string  `thrift:"projectName,3" json:"projectName"`
	Comment     string  `thrift:"comment,4" json:"comment"`
	Ids         []int32 `thrift:"ids,5" json:"ids"`
	Status      []int32 `thrift:"status,6" json:"status"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Offset    int32 `thrift:"offset,11" json:"offset"`
	Limit     int32 `thrift:"limit,12" json:"limit"`
	Ascending bool  `thrift:"ascending,13" json:"ascending"`
}

func NewAudienceParams() *AudienceParams {
	return &AudienceParams{}
}

func (p *AudienceParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AudienceParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Uids = append(p.Uids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AudienceParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AudienceParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProjectName = v
	}
	return nil
}

func (p *AudienceParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *AudienceParams) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Ids = append(p.Ids, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AudienceParams) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Status = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Status = append(p.Status, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AudienceParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AudienceParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AudienceParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *AudienceParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AudienceParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AudienceParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *AudienceParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AudienceParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("projectName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:projectName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProjectName)); err != nil {
		return fmt.Errorf("%T.projectName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:projectName: %s", p, err)
	}
	return err
}

func (p *AudienceParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:comment: %s", p, err)
	}
	return err
}

func (p *AudienceParams) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:ids: %s", p, err)
		}
	}
	return err
}

func (p *AudienceParams) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Status != nil {
		if err := oprot.WriteFieldBegin("status", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Status)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Status {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:status: %s", p, err)
		}
	}
	return err
}

func (p *AudienceParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:offset: %s", p, err)
	}
	return err
}

func (p *AudienceParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:limit: %s", p, err)
	}
	return err
}

func (p *AudienceParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ascending: %s", p, err)
	}
	return err
}

func (p *AudienceParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AudienceParams(%+v)", *p)
}

type DeviceFile struct {
	Id     int32    `thrift:"id,1" json:"id"`
	Name   string   `thrift:"name,2" json:"name"`
	TypeA1 DataType `thrift:"type,3" json:"type"`
	Path   string   `thrift:"path,4" json:"path"`
	Md5    string   `thrift:"md5,5" json:"md5"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Size   int64 `thrift:"size,11" json:"size"`
	Record int64 `thrift:"record,12" json:"record"`
	Valid  int64 `thrift:"valid,13" json:"valid"`
	Ignore int64 `thrift:"ignore,14" json:"ignore"`
	Unique int64 `thrift:"unique,15" json:"unique"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	CommittedFileMd5 string `thrift:"committedFileMd5,21" json:"committedFileMd5"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Status  FileStatus   `thrift:"status,31" json:"status"`
	Deleted DeleteStatus `thrift:"deleted,32" json:"deleted"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	CreateTime int64 `thrift:"createTime,41" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,42" json:"lastUpdate"`
}

func NewDeviceFile() *DeviceFile {
	return &DeviceFile{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Deleted: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeviceFile) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DeviceFile) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DeviceFile) IsSetDeleted() bool {
	return int64(p.Deleted) != math.MinInt32-1
}

func (p *DeviceFile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeviceFile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DeviceFile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DeviceFile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = DataType(v)
	}
	return nil
}

func (p *DeviceFile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Path = v
	}
	return nil
}

func (p *DeviceFile) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Md5 = v
	}
	return nil
}

func (p *DeviceFile) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *DeviceFile) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Record = v
	}
	return nil
}

func (p *DeviceFile) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Valid = v
	}
	return nil
}

func (p *DeviceFile) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Ignore = v
	}
	return nil
}

func (p *DeviceFile) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Unique = v
	}
	return nil
}

func (p *DeviceFile) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CommittedFileMd5 = v
	}
	return nil
}

func (p *DeviceFile) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = FileStatus(v)
	}
	return nil
}

func (p *DeviceFile) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Deleted = DeleteStatus(v)
	}
	return nil
}

func (p *DeviceFile) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DeviceFile) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *DeviceFile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeviceFile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeviceFile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *DeviceFile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("path", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Path)); err != nil {
		return fmt.Errorf("%T.path (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:path: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("md5", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Md5)); err != nil {
		return fmt.Errorf("%T.md5 (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:md5: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:size: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Size)); err != nil {
		return fmt.Errorf("%T.size (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:size: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("record", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:record: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Record)); err != nil {
		return fmt.Errorf("%T.record (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:record: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("valid", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:valid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Valid)); err != nil {
		return fmt.Errorf("%T.valid (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:valid: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ignore", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:ignore: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ignore)); err != nil {
		return fmt.Errorf("%T.ignore (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:ignore: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unique", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:unique: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Unique)); err != nil {
		return fmt.Errorf("%T.unique (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:unique: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("committedFileMd5", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:committedFileMd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CommittedFileMd5)); err != nil {
		return fmt.Errorf("%T.committedFileMd5 (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:committedFileMd5: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:status: %s", p, err)
		}
	}
	return err
}

func (p *DeviceFile) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeleted() {
		if err := oprot.WriteFieldBegin("deleted", thrift.I32, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:deleted: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
			return fmt.Errorf("%T.deleted (32) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:deleted: %s", p, err)
		}
	}
	return err
}

func (p *DeviceFile) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:createTime: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:lastUpdate: %s", p, err)
	}
	return err
}

func (p *DeviceFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeviceFile(%+v)", *p)
}

type DeviceFilePackage struct {
	Id         int32  `thrift:"id,1" json:"id"`
	CategoryId int32  `thrift:"categoryId,2" json:"categoryId"`
	Category   string `thrift:"category,3" json:"category"`
	Path       string `thrift:"path,4" json:"path"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Files []*DeviceFile `thrift:"files,11" json:"files"`
}

func NewDeviceFilePackage() *DeviceFilePackage {
	return &DeviceFilePackage{}
}

func (p *DeviceFilePackage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeviceFilePackage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DeviceFilePackage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CategoryId = v
	}
	return nil
}

func (p *DeviceFilePackage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Category = v
	}
	return nil
}

func (p *DeviceFilePackage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Path = v
	}
	return nil
}

func (p *DeviceFilePackage) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Files = make([]*DeviceFile, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewDeviceFile()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Files = append(p.Files, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeviceFilePackage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeviceFilePackage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeviceFilePackage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DeviceFilePackage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("categoryId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:categoryId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CategoryId)); err != nil {
		return fmt.Errorf("%T.categoryId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:categoryId: %s", p, err)
	}
	return err
}

func (p *DeviceFilePackage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:category: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Category)); err != nil {
		return fmt.Errorf("%T.category (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:category: %s", p, err)
	}
	return err
}

func (p *DeviceFilePackage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("path", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Path)); err != nil {
		return fmt.Errorf("%T.path (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:path: %s", p, err)
	}
	return err
}

func (p *DeviceFilePackage) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Files != nil {
		if err := oprot.WriteFieldBegin("files", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:files: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Files)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Files {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:files: %s", p, err)
		}
	}
	return err
}

func (p *DeviceFilePackage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeviceFilePackage(%+v)", *p)
}

type Audience struct {
	Id            int32  `thrift:"id,1" json:"id"`
	Uid           int32  `thrift:"uid,2" json:"uid"`
	FeaAudienceId int64  `thrift:"feaAudienceId,3" json:"feaAudienceId"`
	Name          string `thrift:"name,4" json:"name"`
	ProjectName   string `thrift:"projectName,5" json:"projectName"`
	Source        string `thrift:"source,6" json:"source"`
	Comment       string `thrift:"comment,7" json:"comment"`
	// unused field # 8
	// unused field # 9
	// unused field # 10
	BuildType AudienceBuildType `thrift:"buildType,11" json:"buildType"`
	Status    ProcessStatus     `thrift:"status,12" json:"status"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	FileSession string  `thrift:"fileSession,21" json:"fileSession"`
	FileTarget  []int32 `thrift:"fileTarget,22" json:"fileTarget"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	SexTarget    []int32 `thrift:"sexTarget,31" json:"sexTarget"`
	AgeTarget    []int32 `thrift:"ageTarget,32" json:"ageTarget"`
	RegionTarget []int32 `thrift:"regionTarget,33" json:"regionTarget"`
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	ShoppingActionTarget  [][][]int32 `thrift:"shoppingActionTarget,41" json:"shoppingActionTarget"`
	ShoppingCountryTarget []int32     `thrift:"shoppingCountryTarget,42" json:"shoppingCountryTarget"`
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	VideoActionTarget [][][]int32 `thrift:"videoActionTarget,51" json:"videoActionTarget"`
	VideoAreaTarget   []int32     `thrift:"videoAreaTarget,52" json:"videoAreaTarget"`
	VideoPeriodTarget []int32     `thrift:"videoPeriodTarget,53" json:"videoPeriodTarget"`
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	Deleted    DeleteStatus `thrift:"deleted,61" json:"deleted"`
	CreateTime int64        `thrift:"createTime,62" json:"createTime"`
	CommitTime int64        `thrift:"commitTime,63" json:"commitTime"`
	FinishTime int64        `thrift:"finishTime,64" json:"finishTime"`
	LastUpdate int64        `thrift:"lastUpdate,65" json:"lastUpdate"`
}

func NewAudience() *Audience {
	return &Audience{
		BuildType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Deleted: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Audience) IsSetBuildType() bool {
	return int64(p.BuildType) != math.MinInt32-1
}

func (p *Audience) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Audience) IsSetDeleted() bool {
	return int64(p.Deleted) != math.MinInt32-1
}

func (p *Audience) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.LIST {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.LIST {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.LIST {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.LIST {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.LIST {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.LIST {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.LIST {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.LIST {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.LIST {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I64 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.I64 {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.I64 {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 65:
			if fieldTypeId == thrift.I64 {
				if err := p.readField65(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Audience) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Audience) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Audience) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FeaAudienceId = v
	}
	return nil
}

func (p *Audience) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Audience) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ProjectName = v
	}
	return nil
}

func (p *Audience) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *Audience) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *Audience) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.BuildType = AudienceBuildType(v)
	}
	return nil
}

func (p *Audience) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Status = ProcessStatus(v)
	}
	return nil
}

func (p *Audience) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.FileSession = v
	}
	return nil
}

func (p *Audience) readField22(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.FileTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.FileTarget = append(p.FileTarget, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField31(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SexTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.SexTarget = append(p.SexTarget, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField32(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AgeTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.AgeTarget = append(p.AgeTarget, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField33(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RegionTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.RegionTarget = append(p.RegionTarget, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField41(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ShoppingActionTarget = make([][][]int32, 0, size)
	for i := 0; i < size; i++ {
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_elem8 := make([][]int32, 0, size)
		for i := 0; i < size; i++ {
			_, size, err := iprot.ReadListBegin()
			if err != nil {
				return fmt.Errorf("error reading list being: %s", err)
			}
			_elem9 := make([]int32, 0, size)
			for i := 0; i < size; i++ {
				var _elem10 int32
				if v, err := iprot.ReadI32(); err != nil {
					return fmt.Errorf("error reading field 0: %s", err)
				} else {
					_elem10 = v
				}
				_elem9 = append(_elem9, _elem10)
			}
			if err := iprot.ReadListEnd(); err != nil {
				return fmt.Errorf("error reading list end: %s", err)
			}
			_elem8 = append(_elem8, _elem9)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.ShoppingActionTarget = append(p.ShoppingActionTarget, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField42(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ShoppingCountryTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.ShoppingCountryTarget = append(p.ShoppingCountryTarget, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField51(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoActionTarget = make([][][]int32, 0, size)
	for i := 0; i < size; i++ {
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_elem12 := make([][]int32, 0, size)
		for i := 0; i < size; i++ {
			_, size, err := iprot.ReadListBegin()
			if err != nil {
				return fmt.Errorf("error reading list being: %s", err)
			}
			_elem13 := make([]int32, 0, size)
			for i := 0; i < size; i++ {
				var _elem14 int32
				if v, err := iprot.ReadI32(); err != nil {
					return fmt.Errorf("error reading field 0: %s", err)
				} else {
					_elem14 = v
				}
				_elem13 = append(_elem13, _elem14)
			}
			if err := iprot.ReadListEnd(); err != nil {
				return fmt.Errorf("error reading list end: %s", err)
			}
			_elem12 = append(_elem12, _elem13)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.VideoActionTarget = append(p.VideoActionTarget, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField52(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoAreaTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = v
		}
		p.VideoAreaTarget = append(p.VideoAreaTarget, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField53(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoPeriodTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = v
		}
		p.VideoPeriodTarget = append(p.VideoPeriodTarget, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Audience) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Deleted = DeleteStatus(v)
	}
	return nil
}

func (p *Audience) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Audience) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.CommitTime = v
	}
	return nil
}

func (p *Audience) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.FinishTime = v
	}
	return nil
}

func (p *Audience) readField65(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 65: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Audience) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Audience"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField65(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Audience) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Audience) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Audience) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("feaAudienceId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:feaAudienceId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FeaAudienceId)); err != nil {
		return fmt.Errorf("%T.feaAudienceId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:feaAudienceId: %s", p, err)
	}
	return err
}

func (p *Audience) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Audience) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("projectName", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:projectName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProjectName)); err != nil {
		return fmt.Errorf("%T.projectName (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:projectName: %s", p, err)
	}
	return err
}

func (p *Audience) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:source: %s", p, err)
	}
	return err
}

func (p *Audience) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:comment: %s", p, err)
	}
	return err
}

func (p *Audience) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetBuildType() {
		if err := oprot.WriteFieldBegin("buildType", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:buildType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BuildType)); err != nil {
			return fmt.Errorf("%T.buildType (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:buildType: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:status: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileSession", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:fileSession: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileSession)); err != nil {
		return fmt.Errorf("%T.fileSession (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:fileSession: %s", p, err)
	}
	return err
}

func (p *Audience) writeField22(oprot thrift.TProtocol) (err error) {
	if p.FileTarget != nil {
		if err := oprot.WriteFieldBegin("fileTarget", thrift.LIST, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:fileTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.FileTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.FileTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:fileTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField31(oprot thrift.TProtocol) (err error) {
	if p.SexTarget != nil {
		if err := oprot.WriteFieldBegin("sexTarget", thrift.LIST, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:sexTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SexTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SexTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:sexTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField32(oprot thrift.TProtocol) (err error) {
	if p.AgeTarget != nil {
		if err := oprot.WriteFieldBegin("ageTarget", thrift.LIST, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:ageTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AgeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AgeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:ageTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField33(oprot thrift.TProtocol) (err error) {
	if p.RegionTarget != nil {
		if err := oprot.WriteFieldBegin("regionTarget", thrift.LIST, 33); err != nil {
			return fmt.Errorf("%T write field begin error 33:regionTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RegionTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RegionTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 33:regionTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField41(oprot thrift.TProtocol) (err error) {
	if p.ShoppingActionTarget != nil {
		if err := oprot.WriteFieldBegin("shoppingActionTarget", thrift.LIST, 41); err != nil {
			return fmt.Errorf("%T write field begin error 41:shoppingActionTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.LIST, len(p.ShoppingActionTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ShoppingActionTarget {
			if err := oprot.WriteListBegin(thrift.LIST, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
					return fmt.Errorf("error writing list begin: %s")
				}
				for _, v := range v {
					if err := oprot.WriteI32(int32(v)); err != nil {
						return fmt.Errorf("%T. (0) field write error: %s", p, err)
					}
				}
				if err := oprot.WriteListEnd(); err != nil {
					return fmt.Errorf("error writing list end: %s")
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 41:shoppingActionTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField42(oprot thrift.TProtocol) (err error) {
	if p.ShoppingCountryTarget != nil {
		if err := oprot.WriteFieldBegin("shoppingCountryTarget", thrift.LIST, 42); err != nil {
			return fmt.Errorf("%T write field begin error 42:shoppingCountryTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ShoppingCountryTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ShoppingCountryTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 42:shoppingCountryTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField51(oprot thrift.TProtocol) (err error) {
	if p.VideoActionTarget != nil {
		if err := oprot.WriteFieldBegin("videoActionTarget", thrift.LIST, 51); err != nil {
			return fmt.Errorf("%T write field begin error 51:videoActionTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.LIST, len(p.VideoActionTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoActionTarget {
			if err := oprot.WriteListBegin(thrift.LIST, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
					return fmt.Errorf("error writing list begin: %s")
				}
				for _, v := range v {
					if err := oprot.WriteI32(int32(v)); err != nil {
						return fmt.Errorf("%T. (0) field write error: %s", p, err)
					}
				}
				if err := oprot.WriteListEnd(); err != nil {
					return fmt.Errorf("error writing list end: %s")
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 51:videoActionTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField52(oprot thrift.TProtocol) (err error) {
	if p.VideoAreaTarget != nil {
		if err := oprot.WriteFieldBegin("videoAreaTarget", thrift.LIST, 52); err != nil {
			return fmt.Errorf("%T write field begin error 52:videoAreaTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VideoAreaTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoAreaTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 52:videoAreaTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField53(oprot thrift.TProtocol) (err error) {
	if p.VideoPeriodTarget != nil {
		if err := oprot.WriteFieldBegin("videoPeriodTarget", thrift.LIST, 53); err != nil {
			return fmt.Errorf("%T write field begin error 53:videoPeriodTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VideoPeriodTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoPeriodTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 53:videoPeriodTarget: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField61(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeleted() {
		if err := oprot.WriteFieldBegin("deleted", thrift.I32, 61); err != nil {
			return fmt.Errorf("%T write field begin error 61:deleted: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
			return fmt.Errorf("%T.deleted (61) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 61:deleted: %s", p, err)
		}
	}
	return err
}

func (p *Audience) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:createTime: %s", p, err)
	}
	return err
}

func (p *Audience) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("commitTime", thrift.I64, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:commitTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CommitTime)); err != nil {
		return fmt.Errorf("%T.commitTime (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:commitTime: %s", p, err)
	}
	return err
}

func (p *Audience) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finishTime", thrift.I64, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:finishTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FinishTime)); err != nil {
		return fmt.Errorf("%T.finishTime (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:finishTime: %s", p, err)
	}
	return err
}

func (p *Audience) writeField65(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 65); err != nil {
		return fmt.Errorf("%T write field begin error 65:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (65) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 65:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Audience) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Audience(%+v)", *p)
}
