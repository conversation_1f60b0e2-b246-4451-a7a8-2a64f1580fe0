// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dsp_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dsp_types.GoUnusedProtection__
var GoUnusedProtection__ int

type DspEventType int64

const (
	DspEventType_DET_UNKNOWN           DspEventType = 0
	DspEventType_DET_UPDATE_DAILY_DATA DspEventType = 1
)

func (p DspEventType) String() string {
	switch p {
	case DspEventType_DET_UNKNOWN:
		return "DspEventType_DET_UNKNOWN"
	case DspEventType_DET_UPDATE_DAILY_DATA:
		return "DspEventType_DET_UPDATE_DAILY_DATA"
	}
	return "<UNSET>"
}

func DspEventTypeFromString(s string) (DspEventType, error) {
	switch s {
	case "DspEventType_DET_UNKNOWN":
		return DspEventType_DET_UNKNOWN, nil
	case "DspEventType_DET_UPDATE_DAILY_DATA":
		return DspEventType_DET_UPDATE_DAILY_DATA, nil
	}
	return DspEventType(math.MinInt32 - 1), fmt.Errorf("not a valid DspEventType string")
}

type CampaignChannelType dsp_types.CampaignChannelType

type DspStatsEvent struct {
	TypeA1        DspEventType        `thrift:"type,1" json:"type"`
	Dt            int64               `thrift:"dt,2" json:"dt"`
	Hr            int32               `thrift:"hr,3" json:"hr"`
	CampaignId    int32               `thrift:"campaignId,4" json:"campaignId"`
	ChannelType   CampaignChannelType `thrift:"channelType,5" json:"channelType"`
	MediaId       int32               `thrift:"mediaId,6" json:"mediaId"`
	Pid           int32               `thrift:"pid,7" json:"pid"`
	Sid           int32               `thrift:"sid,8" json:"sid"`
	Cid           int32               `thrift:"cid,9" json:"cid"`
	PlacementType int64               `thrift:"placementType,10" json:"placementType"`
	CostType      enums.CostType      `thrift:"costType,11" json:"costType"`
}

func NewDspStatsEvent() *DspStatsEvent {
	return &DspStatsEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		ChannelType: math.MinInt32 - 1, // unset sentinal value

		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DspStatsEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DspStatsEvent) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *DspStatsEvent) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *DspStatsEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspStatsEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DspEventType(v)
	}
	return nil
}

func (p *DspStatsEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *DspStatsEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *DspStatsEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *DspStatsEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChannelType = CampaignChannelType(v)
	}
	return nil
}

func (p *DspStatsEvent) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *DspStatsEvent) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *DspStatsEvent) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *DspStatsEvent) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *DspStatsEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PlacementType = v
	}
	return nil
}

func (p *DspStatsEvent) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CostType = enums.CostType(v)
	}
	return nil
}

func (p *DspStatsEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspStatsEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspStatsEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DspStatsEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dt: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:hr: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:campaignId: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:channelType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
		return fmt.Errorf("%T.channelType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:channelType: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:mediaId: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:pid: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sid: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:cid: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:placementType: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:placementType: %s", p, err)
	}
	return err
}

func (p *DspStatsEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCostType() {
		if err := oprot.WriteFieldBegin("costType", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:costType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CostType)); err != nil {
			return fmt.Errorf("%T.costType (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:costType: %s", p, err)
		}
	}
	return err
}

func (p *DspStatsEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspStatsEvent(%+v)", *p)
}
