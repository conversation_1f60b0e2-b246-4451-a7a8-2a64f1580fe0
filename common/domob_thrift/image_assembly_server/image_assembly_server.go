// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package image_assembly_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/image_assembly_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = image_assembly_types.GoUnusedProtection__

type ImageAssemblyServer interface {
	// 函数功能：把html字符串转换成图片的相关数据返回
	// 输入参数：
	//         header: 请求头
	//         img_struct: 图片的结构体
	// 返回值：
	//         图片相关信息的结构体
	//
	// Parameters:
	//  - Header
	//  - ImgStruct
	Html2Img(header *image_assembly_types.RequestHeader, img_struct *image_assembly_types.Html2ImgRequest) (r *image_assembly_types.Html2ImgResult, e *image_assembly_types.ServerException, err error)
}

type ImageAssemblyServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewImageAssemblyServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ImageAssemblyServerClient {
	return &ImageAssemblyServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewImageAssemblyServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ImageAssemblyServerClient {
	return &ImageAssemblyServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 函数功能：把html字符串转换成图片的相关数据返回
// 输入参数：
//         header: 请求头
//         img_struct: 图片的结构体
// 返回值：
//         图片相关信息的结构体
//
// Parameters:
//  - Header
//  - ImgStruct
func (p *ImageAssemblyServerClient) Html2Img(header *image_assembly_types.RequestHeader, img_struct *image_assembly_types.Html2ImgRequest) (r *image_assembly_types.Html2ImgResult, e *image_assembly_types.ServerException, err error) {
	if err = p.sendHtml2Img(header, img_struct); err != nil {
		return
	}
	return p.recvHtml2Img()
}

func (p *ImageAssemblyServerClient) sendHtml2Img(header *image_assembly_types.RequestHeader, img_struct *image_assembly_types.Html2ImgRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("html2Img", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewHtml2ImgArgs()
	args0.Header = header
	args0.ImgStruct = img_struct
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ImageAssemblyServerClient) recvHtml2Img() (value *image_assembly_types.Html2ImgResult, e *image_assembly_types.ServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewHtml2ImgResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

type ImageAssemblyServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ImageAssemblyServer
}

func (p *ImageAssemblyServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ImageAssemblyServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ImageAssemblyServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewImageAssemblyServerProcessor(handler ImageAssemblyServer) *ImageAssemblyServerProcessor {

	self4 := &ImageAssemblyServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self4.processorMap["html2Img"] = &imageAssemblyServerProcessorHtml2Img{handler: handler}
	return self4
}

func (p *ImageAssemblyServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x5.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x5

}

type imageAssemblyServerProcessorHtml2Img struct {
	handler ImageAssemblyServer
}

func (p *imageAssemblyServerProcessorHtml2Img) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewHtml2ImgArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("html2Img", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewHtml2ImgResult()
	if result.Success, result.E, err = p.handler.Html2Img(args.Header, args.ImgStruct); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing html2Img: "+err.Error())
		oprot.WriteMessageBegin("html2Img", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("html2Img", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type Html2ImgArgs struct {
	Header    *image_assembly_types.RequestHeader   `thrift:"header,1" json:"header"`
	ImgStruct *image_assembly_types.Html2ImgRequest `thrift:"img_struct,2" json:"img_struct"`
}

func NewHtml2ImgArgs() *Html2ImgArgs {
	return &Html2ImgArgs{}
}

func (p *Html2ImgArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = image_assembly_types.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *Html2ImgArgs) readField2(iprot thrift.TProtocol) error {
	p.ImgStruct = image_assembly_types.NewHtml2ImgRequest()
	if err := p.ImgStruct.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ImgStruct)
	}
	return nil
}

func (p *Html2ImgArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("html2Img_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ImgStruct != nil {
		if err := oprot.WriteFieldBegin("img_struct", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:img_struct: %s", p, err)
		}
		if err := p.ImgStruct.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ImgStruct)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:img_struct: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Html2ImgArgs(%+v)", *p)
}

type Html2ImgResult struct {
	Success *image_assembly_types.Html2ImgResult  `thrift:"success,0" json:"success"`
	E       *image_assembly_types.ServerException `thrift:"e,1" json:"e"`
}

func NewHtml2ImgResult() *Html2ImgResult {
	return &Html2ImgResult{}
}

func (p *Html2ImgResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgResult) readField0(iprot thrift.TProtocol) error {
	p.Success = image_assembly_types.NewHtml2ImgResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *Html2ImgResult) readField1(iprot thrift.TProtocol) error {
	p.E = image_assembly_types.NewServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *Html2ImgResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("html2Img_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Html2ImgResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImgResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Html2ImgResult(%+v)", *p)
}
