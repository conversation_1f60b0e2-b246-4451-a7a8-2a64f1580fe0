// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"vico_stats"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>r, "  StatsQueryResult queryStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := vico_stats.NewVicoStatsServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryStatsData requires 5 args")
			flag.Usage()
		}
		arg6 := flag.Arg(1)
		mbTrans7 := thrift.NewTMemoryBufferLen(len(arg6))
		defer mbTrans7.Close()
		_, err8 := mbTrans7.WriteString(arg6)
		if err8 != nil {
			Usage()
			return
		}
		factory9 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt10 := factory9.GetProtocol(mbTrans7)
		argvalue0 := vico_stats.NewRequestHeader()
		err11 := argvalue0.Read(jsProt10)
		if err11 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg12 := flag.Arg(2)
		mbTrans13 := thrift.NewTMemoryBufferLen(len(arg12))
		defer mbTrans13.Close()
		_, err14 := mbTrans13.WriteString(arg12)
		if err14 != nil {
			Usage()
			return
		}
		factory15 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt16 := factory15.GetProtocol(mbTrans13)
		argvalue1 := vico_stats.NewStatsConditionParam()
		err17 := argvalue1.Read(jsProt16)
		if err17 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg18 := flag.Arg(3)
		mbTrans19 := thrift.NewTMemoryBufferLen(len(arg18))
		defer mbTrans19.Close()
		_, err20 := mbTrans19.WriteString(arg18)
		if err20 != nil {
			Usage()
			return
		}
		factory21 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt22 := factory21.GetProtocol(mbTrans19)
		argvalue2 := vico_stats.NewStatsGroupByParam()
		err23 := argvalue2.Read(jsProt22)
		if err23 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err24 := (strconv.Atoi(flag.Arg(4)))
		if err24 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err25 := (strconv.Atoi(flag.Arg(5)))
		if err25 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
