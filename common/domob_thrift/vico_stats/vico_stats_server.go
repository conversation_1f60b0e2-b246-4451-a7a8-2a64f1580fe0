// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/vico_stats_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = vico_stats_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type VicoStatsServer interface { //vico stats thrift interface

	// 查询统计信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Condition: 查询条件 *
	//  - Group: 聚合方式 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryStatsData(header *common.RequestHeader, condition *vico_stats_types.StatsConditionParam, group *vico_stats_types.StatsGroupByParam, offset int32, limit int32) (r *vico_stats_types.StatsQueryResult, e *VicoStatsException, err error)
}

//vico stats thrift interface
type VicoStatsServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewVicoStatsServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *VicoStatsServerClient {
	return &VicoStatsServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewVicoStatsServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *VicoStatsServerClient {
	return &VicoStatsServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询统计信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Condition: 查询条件 *
//  - Group: 聚合方式 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *VicoStatsServerClient) QueryStatsData(header *common.RequestHeader, condition *vico_stats_types.StatsConditionParam, group *vico_stats_types.StatsGroupByParam, offset int32, limit int32) (r *vico_stats_types.StatsQueryResult, e *VicoStatsException, err error) {
	if err = p.sendQueryStatsData(header, condition, group, offset, limit); err != nil {
		return
	}
	return p.recvQueryStatsData()
}

func (p *VicoStatsServerClient) sendQueryStatsData(header *common.RequestHeader, condition *vico_stats_types.StatsConditionParam, group *vico_stats_types.StatsGroupByParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewQueryStatsDataArgs()
	args0.Header = header
	args0.Condition = condition
	args0.Group = group
	args0.Offset = offset
	args0.Limit = limit
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoStatsServerClient) recvQueryStatsData() (value *vico_stats_types.StatsQueryResult, e *VicoStatsException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewQueryStatsDataResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

type VicoStatsServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      VicoStatsServer
}

func (p *VicoStatsServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *VicoStatsServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *VicoStatsServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewVicoStatsServerProcessor(handler VicoStatsServer) *VicoStatsServerProcessor {

	self4 := &VicoStatsServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self4.processorMap["queryStatsData"] = &vicoStatsServerProcessorQueryStatsData{handler: handler}
	return self4
}

func (p *VicoStatsServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x5.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x5

}

type vicoStatsServerProcessorQueryStatsData struct {
	handler VicoStatsServer
}

func (p *vicoStatsServerProcessorQueryStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStatsDataResult()
	if result.Success, result.E, err = p.handler.QueryStatsData(args.Header, args.Condition, args.Group, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type QueryStatsDataArgs struct {
	Header    *common.RequestHeader                 `thrift:"header,1" json:"header"`
	Condition *vico_stats_types.StatsConditionParam `thrift:"condition,2" json:"condition"`
	Group     *vico_stats_types.StatsGroupByParam   `thrift:"group,3" json:"group"`
	// unused field # 4
	Offset int32 `thrift:"offset,5" json:"offset"`
	Limit  int32 `thrift:"limit,6" json:"limit"`
}

func NewQueryStatsDataArgs() *QueryStatsDataArgs {
	return &QueryStatsDataArgs{}
}

func (p *QueryStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Condition = vico_stats_types.NewStatsConditionParam()
	if err := p.Condition.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Condition)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Group = vico_stats_types.NewStatsGroupByParam()
	if err := p.Group.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Group)
	}
	return nil
}

func (p *QueryStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Condition != nil {
		if err := oprot.WriteFieldBegin("condition", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:condition: %s", p, err)
		}
		if err := p.Condition.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Condition)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:condition: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Group != nil {
		if err := oprot.WriteFieldBegin("group", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:group: %s", p, err)
		}
		if err := p.Group.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Group)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:group: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataArgs(%+v)", *p)
}

type QueryStatsDataResult struct {
	Success *vico_stats_types.StatsQueryResult `thrift:"success,0" json:"success"`
	E       *VicoStatsException                `thrift:"e,1" json:"e"`
}

func NewQueryStatsDataResult() *QueryStatsDataResult {
	return &QueryStatsDataResult{}
}

func (p *QueryStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = vico_stats_types.NewStatsQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewVicoStatsException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStatsDataResult(%+v)", *p)
}
