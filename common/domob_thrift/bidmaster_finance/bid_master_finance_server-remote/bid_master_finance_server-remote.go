// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"bidmaster_finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  AgentTransaction agentRecharge(RequestHeader header, i32 agentUid, i64 amount, i64 cashRecharged, string note)")
	fmt.Fprintln(os.<PERSON>derr, "  AgentTransaction agentRefund(RequestHeader header, i32 agentUid, i64 amount, i64 cashBalance, string note)")
	fmt.Fprintln(os.Stderr, "  AgentTransaction rewardAgent(RequestHeader header, i32 agentUid, i64 amount, i64 award, string note)")
	fmt.Fprintln(os.Stderr, "  AppropriationTransaction performAppropriation(RequestHeader header, i32 agentUid, i32 sponsorId, i64 amount, i64 balance, string note)")
	fmt.Fprintln(os.Stderr, "  AppropriationTransaction performRecovery(RequestHeader header, i32 agentUid, i32 sponsorId, i64 amount, i64 balance, string note)")
	fmt.Fprintln(os.Stderr, "  AgentTransactionResult queryAgentTransaction(RequestHeader header, AgentTransactionSearchParam param, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  SponsorTransactionResult querySponsorTransaction(RequestHeader header, SponsorTransactionSearchParam param, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getAgentAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getSponsorAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void editSponsorDailyBudget(RequestHeader header, i32 agentUid, i32 sponsorId, i64 dailyBudget)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := bidmaster_finance.NewBidMasterFinanceServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "agentRecharge":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "AgentRecharge requires 5 args")
			flag.Usage()
		}
		arg51 := flag.Arg(1)
		mbTrans52 := thrift.NewTMemoryBufferLen(len(arg51))
		defer mbTrans52.Close()
		_, err53 := mbTrans52.WriteString(arg51)
		if err53 != nil {
			Usage()
			return
		}
		factory54 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt55 := factory54.GetProtocol(mbTrans52)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err56 := argvalue0.Read(jsProt55)
		if err56 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err57 := (strconv.Atoi(flag.Arg(2)))
		if err57 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err58 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err58 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err59 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err59 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.AgentRecharge(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "agentRefund":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "AgentRefund requires 5 args")
			flag.Usage()
		}
		arg61 := flag.Arg(1)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err66 := argvalue0.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err67 := (strconv.Atoi(flag.Arg(2)))
		if err67 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err68 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err68 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err69 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err69 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.AgentRefund(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "rewardAgent":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "RewardAgent requires 5 args")
			flag.Usage()
		}
		arg71 := flag.Arg(1)
		mbTrans72 := thrift.NewTMemoryBufferLen(len(arg71))
		defer mbTrans72.Close()
		_, err73 := mbTrans72.WriteString(arg71)
		if err73 != nil {
			Usage()
			return
		}
		factory74 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt75 := factory74.GetProtocol(mbTrans72)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err76 := argvalue0.Read(jsProt75)
		if err76 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err77 := (strconv.Atoi(flag.Arg(2)))
		if err77 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err78 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err78 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err79 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err79 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.RewardAgent(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "performAppropriation":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "PerformAppropriation requires 6 args")
			flag.Usage()
		}
		arg81 := flag.Arg(1)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err86 := argvalue0.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err87 := (strconv.Atoi(flag.Arg(2)))
		if err87 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err88 := (strconv.Atoi(flag.Arg(3)))
		if err88 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err89 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err89 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err90 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err90 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.PerformAppropriation(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "performRecovery":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "PerformRecovery requires 6 args")
			flag.Usage()
		}
		arg92 := flag.Arg(1)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err97 := argvalue0.Read(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err98 := (strconv.Atoi(flag.Arg(2)))
		if err98 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err99 := (strconv.Atoi(flag.Arg(3)))
		if err99 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err100 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err100 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err101 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err101 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.PerformRecovery(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "queryAgentTransaction":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QueryAgentTransaction requires 4 args")
			flag.Usage()
		}
		arg103 := flag.Arg(1)
		mbTrans104 := thrift.NewTMemoryBufferLen(len(arg103))
		defer mbTrans104.Close()
		_, err105 := mbTrans104.WriteString(arg103)
		if err105 != nil {
			Usage()
			return
		}
		factory106 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt107 := factory106.GetProtocol(mbTrans104)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err108 := argvalue0.Read(jsProt107)
		if err108 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg109 := flag.Arg(2)
		mbTrans110 := thrift.NewTMemoryBufferLen(len(arg109))
		defer mbTrans110.Close()
		_, err111 := mbTrans110.WriteString(arg109)
		if err111 != nil {
			Usage()
			return
		}
		factory112 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt113 := factory112.GetProtocol(mbTrans110)
		argvalue1 := bidmaster_finance.NewAgentTransactionSearchParam()
		err114 := argvalue1.Read(jsProt113)
		if err114 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err115 := (strconv.Atoi(flag.Arg(3)))
		if err115 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err116 := (strconv.Atoi(flag.Arg(4)))
		if err116 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QueryAgentTransaction(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "querySponsorTransaction":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QuerySponsorTransaction requires 4 args")
			flag.Usage()
		}
		arg117 := flag.Arg(1)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err122 := argvalue0.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg123 := flag.Arg(2)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue1 := bidmaster_finance.NewSponsorTransactionSearchParam()
		err128 := argvalue1.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err129 := (strconv.Atoi(flag.Arg(3)))
		if err129 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err130 := (strconv.Atoi(flag.Arg(4)))
		if err130 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QuerySponsorTransaction(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAgentAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAgentAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg137 := flag.Arg(2)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		containerStruct1 := bidmaster_finance.NewGetAgentAccountsByIdsArgs()
		err142 := containerStruct1.ReadField2(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAgentAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg143 := flag.Arg(1)
		mbTrans144 := thrift.NewTMemoryBufferLen(len(arg143))
		defer mbTrans144.Close()
		_, err145 := mbTrans144.WriteString(arg143)
		if err145 != nil {
			Usage()
			return
		}
		factory146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt147 := factory146.GetProtocol(mbTrans144)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err148 := argvalue0.Read(jsProt147)
		if err148 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg149 := flag.Arg(2)
		mbTrans150 := thrift.NewTMemoryBufferLen(len(arg149))
		defer mbTrans150.Close()
		_, err151 := mbTrans150.WriteString(arg149)
		if err151 != nil {
			Usage()
			return
		}
		factory152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt153 := factory152.GetProtocol(mbTrans150)
		containerStruct1 := bidmaster_finance.NewGetSponsorAccountsByIdsArgs()
		err154 := containerStruct1.ReadField2(jsProt153)
		if err154 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSponsorAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "editSponsorDailyBudget":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "EditSponsorDailyBudget requires 4 args")
			flag.Usage()
		}
		arg155 := flag.Arg(1)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err160 := argvalue0.Read(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err161 := (strconv.Atoi(flag.Arg(2)))
		if err161 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err162 := (strconv.Atoi(flag.Arg(3)))
		if err162 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err163 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err163 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.EditSponsorDailyBudget(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
