// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mediaindex_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type IdxRefreshIntervalInt int32

type IdxUidInt common.UidInt

type IdxMediaIdInt common.IdInt

type IdxPlacementIdInt common.IdInt

type IdxHouseAdRatioIdInt common.IdInt

type IdxTimeInt common.TimeInt

type IdxMediaQueryResult *common.QueryResult

type IdxMediaQueryInt common.QueryInt

type IdxRequestHeader *common.RequestHeader

type IdxMediaType common.MediaType

type IdxMediaCategory common.MediaCategory

type IdxMediaTestModeSetting common.MediaTestModeSetting

type IdxMAdCategory common.AdCategory

type IdxMAdCreativeType common.AdCreativeType

type IdxMRegionCode common.RegionCode

type IdxMUserRole common.UserRole

type IdxAdCreativeIdInt common.IdInt

type IdxAdPlacementType common.AdPlacementType

type IdxContainer *common.Container

type IdxChannel *common.Channel

type IndexMediaUser struct {
	Uid        common.UidInt `thrift:"uid,1" json:"uid"`
	Income     common.Amount `thrift:"income,2" json:"income"`
	Withdrawn  common.Amount `thrift:"withdrawn,3" json:"withdrawn"`
	Transfered common.Amount `thrift:"transfered,4" json:"transfered"`
	Balance    common.Amount `thrift:"balance,5" json:"balance"`
	Current    common.Amount `thrift:"current,6" json:"current"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Role              IdxMUserRole         `thrift:"role,10" json:"role"`
	AutoTransferRate  common.PercentageInt `thrift:"autoTransferRate,11" json:"autoTransferRate"`
	TransferAwardRate common.PercentageInt `thrift:"transferAwardRate,12" json:"transferAwardRate"`
	RechargeAwardRate common.PercentageInt `thrift:"rechargeAwardRate,13" json:"rechargeAwardRate"`
}

func NewIndexMediaUser() *IndexMediaUser {
	return &IndexMediaUser{
		Role: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexMediaUser) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *IndexMediaUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *IndexMediaUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Income = common.Amount(v)
	}
	return nil
}

func (p *IndexMediaUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Withdrawn = common.Amount(v)
	}
	return nil
}

func (p *IndexMediaUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Transfered = common.Amount(v)
	}
	return nil
}

func (p *IndexMediaUser) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Balance = common.Amount(v)
	}
	return nil
}

func (p *IndexMediaUser) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Current = common.Amount(v)
	}
	return nil
}

func (p *IndexMediaUser) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Role = IdxMUserRole(v)
	}
	return nil
}

func (p *IndexMediaUser) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AutoTransferRate = common.PercentageInt(v)
	}
	return nil
}

func (p *IndexMediaUser) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TransferAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *IndexMediaUser) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RechargeAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *IndexMediaUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:income: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Income)); err != nil {
		return fmt.Errorf("%T.income (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:income: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("withdrawn", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:withdrawn: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Withdrawn)); err != nil {
		return fmt.Errorf("%T.withdrawn (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:withdrawn: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transfered", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:transfered: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Transfered)); err != nil {
		return fmt.Errorf("%T.transfered (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:transfered: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:balance: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:current: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Current)); err != nil {
		return fmt.Errorf("%T.current (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:current: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("role", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:role: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Role)); err != nil {
		return fmt.Errorf("%T.role (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:role: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("autoTransferRate", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:autoTransferRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AutoTransferRate)); err != nil {
		return fmt.Errorf("%T.autoTransferRate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:autoTransferRate: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transferAwardRate", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:transferAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TransferAwardRate)); err != nil {
		return fmt.Errorf("%T.transferAwardRate (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:transferAwardRate: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rechargeAwardRate", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:rechargeAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RechargeAwardRate)); err != nil {
		return fmt.Errorf("%T.rechargeAwardRate (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:rechargeAwardRate: %s", p, err)
	}
	return err
}

func (p *IndexMediaUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaUser(%+v)", *p)
}

type IndexAppSetting struct {
	RefreshInterval IdxRefreshIntervalInt   `thrift:"refreshInterval,1" json:"refreshInterval"`
	TestMode        IdxMediaTestModeSetting `thrift:"testMode,2" json:"testMode"`
	LastUpdate      IdxTimeInt              `thrift:"lastUpdate,3" json:"lastUpdate"`
}

func NewIndexAppSetting() *IndexAppSetting {
	return &IndexAppSetting{
		TestMode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAppSetting) IsSetTestMode() bool {
	return int64(p.TestMode) != math.MinInt32-1
}

func (p *IndexAppSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAppSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RefreshInterval = IdxRefreshIntervalInt(v)
	}
	return nil
}

func (p *IndexAppSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TestMode = IdxMediaTestModeSetting(v)
	}
	return nil
}

func (p *IndexAppSetting) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.LastUpdate = IdxTimeInt(v)
	}
	return nil
}

func (p *IndexAppSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAppSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAppSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refreshInterval", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:refreshInterval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RefreshInterval)); err != nil {
		return fmt.Errorf("%T.refreshInterval (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:refreshInterval: %s", p, err)
	}
	return err
}

func (p *IndexAppSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("testMode", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:testMode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TestMode)); err != nil {
		return fmt.Errorf("%T.testMode (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:testMode: %s", p, err)
	}
	return err
}

func (p *IndexAppSetting) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:lastUpdate: %s", p, err)
	}
	return err
}

func (p *IndexAppSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAppSetting(%+v)", *p)
}

type IndexMediaSetting struct {
	Mid IdxMediaIdInt `thrift:"mid,1" json:"mid"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	UrlFilter        []string             `thrift:"urlFilter,10" json:"urlFilter"`
	AppFilter        []string             `thrift:"appFilter,11" json:"appFilter"`
	TextFilter       []string             `thrift:"textFilter,12" json:"textFilter"`
	AdCategoryFilter []IdxMAdCategory     `thrift:"adCategoryFilter,13" json:"adCategoryFilter"`
	AdCreativeFilter []IdxMAdCreativeType `thrift:"adCreativeFilter,14" json:"adCreativeFilter"`
}

func NewIndexMediaSetting() *IndexMediaSetting {
	return &IndexMediaSetting{}
}

func (p *IndexMediaSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = IdxMediaIdInt(v)
	}
	return nil
}

func (p *IndexMediaSetting) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.UrlFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.UrlFilter = append(p.UrlFilter, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaSetting) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AppFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.AppFilter = append(p.AppFilter, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaSetting) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TextFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.TextFilter = append(p.TextFilter, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaSetting) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdCategoryFilter = make([]IdxMAdCategory, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 IdxMAdCategory
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = IdxMAdCategory(v)
		}
		p.AdCategoryFilter = append(p.AdCategoryFilter, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaSetting) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdCreativeFilter = make([]IdxMAdCreativeType, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 IdxMAdCreativeType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = IdxMAdCreativeType(v)
		}
		p.AdCreativeFilter = append(p.AdCreativeFilter, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *IndexMediaSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if p.UrlFilter != nil {
		if err := oprot.WriteFieldBegin("urlFilter", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:urlFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.UrlFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.UrlFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:urlFilter: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AppFilter != nil {
		if err := oprot.WriteFieldBegin("appFilter", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:appFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AppFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AppFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:appFilter: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaSetting) writeField12(oprot thrift.TProtocol) (err error) {
	if p.TextFilter != nil {
		if err := oprot.WriteFieldBegin("textFilter", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:textFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TextFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TextFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:textFilter: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaSetting) writeField13(oprot thrift.TProtocol) (err error) {
	if p.AdCategoryFilter != nil {
		if err := oprot.WriteFieldBegin("adCategoryFilter", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:adCategoryFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdCategoryFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdCategoryFilter {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:adCategoryFilter: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaSetting) writeField14(oprot thrift.TProtocol) (err error) {
	if p.AdCreativeFilter != nil {
		if err := oprot.WriteFieldBegin("adCreativeFilter", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:adCreativeFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdCreativeFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdCreativeFilter {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:adCreativeFilter: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaSetting(%+v)", *p)
}

type IndexMediaTermSign struct {
	Sign1 int32 `thrift:"sign1,1" json:"sign1"`
	Sign2 int32 `thrift:"sign2,2" json:"sign2"`
}

func NewIndexMediaTermSign() *IndexMediaTermSign {
	return &IndexMediaTermSign{}
}

func (p *IndexMediaTermSign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaTermSign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Sign1 = v
	}
	return nil
}

func (p *IndexMediaTermSign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sign2 = v
	}
	return nil
}

func (p *IndexMediaTermSign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaTermSign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaTermSign) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign1", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sign1: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sign1)); err != nil {
		return fmt.Errorf("%T.sign1 (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sign1: %s", p, err)
	}
	return err
}

func (p *IndexMediaTermSign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign2", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sign2: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sign2)); err != nil {
		return fmt.Errorf("%T.sign2 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sign2: %s", p, err)
	}
	return err
}

func (p *IndexMediaTermSign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaTermSign(%+v)", *p)
}

type IndexMediaTermInfo struct {
	Sign   *IndexMediaTermSign `thrift:"sign,1" json:"sign"`
	Idx    int32               `thrift:"idx,2" json:"idx"`
	Weight float64             `thrift:"weight,3" json:"weight"`
}

func NewIndexMediaTermInfo() *IndexMediaTermInfo {
	return &IndexMediaTermInfo{}
}

func (p *IndexMediaTermInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaTermInfo) readField1(iprot thrift.TProtocol) error {
	p.Sign = NewIndexMediaTermSign()
	if err := p.Sign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sign)
	}
	return nil
}

func (p *IndexMediaTermInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Idx = v
	}
	return nil
}

func (p *IndexMediaTermInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Weight = v
	}
	return nil
}

func (p *IndexMediaTermInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaTermInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaTermInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sign != nil {
		if err := oprot.WriteFieldBegin("sign", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sign: %s", p, err)
		}
		if err := p.Sign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sign: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaTermInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idx", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:idx: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Idx)); err != nil {
		return fmt.Errorf("%T.idx (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:idx: %s", p, err)
	}
	return err
}

func (p *IndexMediaTermInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weight", thrift.DOUBLE, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:weight: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Weight)); err != nil {
		return fmt.Errorf("%T.weight (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:weight: %s", p, err)
	}
	return err
}

func (p *IndexMediaTermInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaTermInfo(%+v)", *p)
}

type IndexMedia struct {
	Uid             IdxUidInt             `thrift:"uid,1" json:"uid"`
	Mid             IdxMediaIdInt         `thrift:"mid,2" json:"mid"`
	TypeA1          IdxMediaType          `thrift:"type,3" json:"type"`
	Keywords        string                `thrift:"keywords,4" json:"keywords"`
	Addkeywords     string                `thrift:"addkeywords,5" json:"addkeywords"`
	Pubid           string                `thrift:"pubid,6" json:"pubid"`
	Url             string                `thrift:"url,7" json:"url"`
	Category        []IdxMediaCategory    `thrift:"category,8" json:"category"`
	UseLocationInfo bool                  `thrift:"useLocationInfo,9" json:"useLocationInfo"`
	Geo             []IdxMRegionCode      `thrift:"geo,10" json:"geo"`
	Terminfo        []*IndexMediaTermInfo `thrift:"terminfo,11" json:"terminfo"`
	TermWeight      float64               `thrift:"term_weight,12" json:"term_weight"`
	MediaSetting    *IndexMediaSetting    `thrift:"mediaSetting,13" json:"mediaSetting"`
	AppSetting      *IndexAppSetting      `thrift:"appSetting,14" json:"appSetting"`
}

func NewIndexMedia() *IndexMedia {
	return &IndexMedia{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexMedia) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *IndexMedia) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMedia) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = IdxUidInt(v)
	}
	return nil
}

func (p *IndexMedia) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = IdxMediaIdInt(v)
	}
	return nil
}

func (p *IndexMedia) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = IdxMediaType(v)
	}
	return nil
}

func (p *IndexMedia) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Keywords = v
	}
	return nil
}

func (p *IndexMedia) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Addkeywords = v
	}
	return nil
}

func (p *IndexMedia) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *IndexMedia) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *IndexMedia) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Category = make([]IdxMediaCategory, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 IdxMediaCategory
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = IdxMediaCategory(v)
		}
		p.Category = append(p.Category, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMedia) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UseLocationInfo = v
	}
	return nil
}

func (p *IndexMedia) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Geo = make([]IdxMRegionCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 IdxMRegionCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = IdxMRegionCode(v)
		}
		p.Geo = append(p.Geo, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMedia) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Terminfo = make([]*IndexMediaTermInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewIndexMediaTermInfo()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.Terminfo = append(p.Terminfo, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMedia) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TermWeight = v
	}
	return nil
}

func (p *IndexMedia) readField13(iprot thrift.TProtocol) error {
	p.MediaSetting = NewIndexMediaSetting()
	if err := p.MediaSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaSetting)
	}
	return nil
}

func (p *IndexMedia) readField14(iprot thrift.TProtocol) error {
	p.AppSetting = NewIndexAppSetting()
	if err := p.AppSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppSetting)
	}
	return nil
}

func (p *IndexMedia) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMedia"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMedia) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:type: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keywords", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:keywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keywords)); err != nil {
		return fmt.Errorf("%T.keywords (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:keywords: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("addkeywords", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:addkeywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Addkeywords)); err != nil {
		return fmt.Errorf("%T.addkeywords (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:addkeywords: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pubid: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:url: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Category != nil {
		if err := oprot.WriteFieldBegin("category", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:category: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Category)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Category {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:category: %s", p, err)
		}
	}
	return err
}

func (p *IndexMedia) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useLocationInfo", thrift.BOOL, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:useLocationInfo: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UseLocationInfo)); err != nil {
		return fmt.Errorf("%T.useLocationInfo (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:useLocationInfo: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Geo != nil {
		if err := oprot.WriteFieldBegin("geo", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:geo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Geo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Geo {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:geo: %s", p, err)
		}
	}
	return err
}

func (p *IndexMedia) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Terminfo != nil {
		if err := oprot.WriteFieldBegin("terminfo", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:terminfo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Terminfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Terminfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:terminfo: %s", p, err)
		}
	}
	return err
}

func (p *IndexMedia) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("term_weight", thrift.DOUBLE, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:term_weight: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.TermWeight)); err != nil {
		return fmt.Errorf("%T.term_weight (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:term_weight: %s", p, err)
	}
	return err
}

func (p *IndexMedia) writeField13(oprot thrift.TProtocol) (err error) {
	if p.MediaSetting != nil {
		if err := oprot.WriteFieldBegin("mediaSetting", thrift.STRUCT, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:mediaSetting: %s", p, err)
		}
		if err := p.MediaSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:mediaSetting: %s", p, err)
		}
	}
	return err
}

func (p *IndexMedia) writeField14(oprot thrift.TProtocol) (err error) {
	if p.AppSetting != nil {
		if err := oprot.WriteFieldBegin("appSetting", thrift.STRUCT, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:appSetting: %s", p, err)
		}
		if err := p.AppSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:appSetting: %s", p, err)
		}
	}
	return err
}

func (p *IndexMedia) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMedia(%+v)", *p)
}

type IndexMediaMeta struct {
	UserNum      int32 `thrift:"user_num,1" json:"user_num"`
	MediaNum     int32 `thrift:"media_num,2" json:"media_num"`
	PlacementNum int32 `thrift:"placement_num,3" json:"placement_num"`
}

func NewIndexMediaMeta() *IndexMediaMeta {
	return &IndexMediaMeta{}
}

func (p *IndexMediaMeta) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaMeta) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.UserNum = v
	}
	return nil
}

func (p *IndexMediaMeta) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaNum = v
	}
	return nil
}

func (p *IndexMediaMeta) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlacementNum = v
	}
	return nil
}

func (p *IndexMediaMeta) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaMeta"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:user_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserNum)); err != nil {
		return fmt.Errorf("%T.user_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:user_num: %s", p, err)
	}
	return err
}

func (p *IndexMediaMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:media_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaNum)); err != nil {
		return fmt.Errorf("%T.media_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:media_num: %s", p, err)
	}
	return err
}

func (p *IndexMediaMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placement_num", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:placement_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementNum)); err != nil {
		return fmt.Errorf("%T.placement_num (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:placement_num: %s", p, err)
	}
	return err
}

func (p *IndexMediaMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaMeta(%+v)", *p)
}

type IndexMediaCategoryMeta struct {
	FeaNum    int32 `thrift:"fea_num,1" json:"fea_num"`
	UnitNum   int32 `thrift:"unit_num,2" json:"unit_num"`
	MaxFeaLen int32 `thrift:"max_fea_len,3" json:"max_fea_len"`
}

func NewIndexMediaCategoryMeta() *IndexMediaCategoryMeta {
	return &IndexMediaCategoryMeta{}
}

func (p *IndexMediaCategoryMeta) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaCategoryMeta) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FeaNum = v
	}
	return nil
}

func (p *IndexMediaCategoryMeta) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UnitNum = v
	}
	return nil
}

func (p *IndexMediaCategoryMeta) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MaxFeaLen = v
	}
	return nil
}

func (p *IndexMediaCategoryMeta) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaCategoryMeta"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaCategoryMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fea_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fea_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FeaNum)); err != nil {
		return fmt.Errorf("%T.fea_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fea_num: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategoryMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unit_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:unit_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UnitNum)); err != nil {
		return fmt.Errorf("%T.unit_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:unit_num: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategoryMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_fea_len", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:max_fea_len: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxFeaLen)); err != nil {
		return fmt.Errorf("%T.max_fea_len (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:max_fea_len: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategoryMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaCategoryMeta(%+v)", *p)
}

type IndexMediaCategoryFeaHeader struct {
	FeaSign int64 `thrift:"fea_sign,1" json:"fea_sign"`
	AdNum   int32 `thrift:"ad_num,2" json:"ad_num"`
}

func NewIndexMediaCategoryFeaHeader() *IndexMediaCategoryFeaHeader {
	return &IndexMediaCategoryFeaHeader{}
}

func (p *IndexMediaCategoryFeaHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaCategoryFeaHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FeaSign = v
	}
	return nil
}

func (p *IndexMediaCategoryFeaHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdNum = v
	}
	return nil
}

func (p *IndexMediaCategoryFeaHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaCategoryFeaHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaCategoryFeaHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fea_sign", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fea_sign: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FeaSign)); err != nil {
		return fmt.Errorf("%T.fea_sign (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fea_sign: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategoryFeaHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ad_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdNum)); err != nil {
		return fmt.Errorf("%T.ad_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ad_num: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategoryFeaHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaCategoryFeaHeader(%+v)", *p)
}

type IndexMediaCategory struct {
	CategoryId IdxMAdCategory `thrift:"category_id,1" json:"category_id"`
	Rank       int32          `thrift:"rank,2" json:"rank"`
}

func NewIndexMediaCategory() *IndexMediaCategory {
	return &IndexMediaCategory{
		CategoryId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexMediaCategory) IsSetCategoryId() bool {
	return int64(p.CategoryId) != math.MinInt32-1
}

func (p *IndexMediaCategory) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaCategory) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CategoryId = IdxMAdCategory(v)
	}
	return nil
}

func (p *IndexMediaCategory) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *IndexMediaCategory) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaCategory"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaCategory) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:category_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CategoryId)); err != nil {
		return fmt.Errorf("%T.category_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:category_id: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategory) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rank: %s", p, err)
	}
	return err
}

func (p *IndexMediaCategory) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaCategory(%+v)", *p)
}

type IndexMediaPlacement struct {
	Uid           IdxUidInt          `thrift:"uid,1" json:"uid"`
	Mid           IdxMediaIdInt      `thrift:"mid,2" json:"mid"`
	Pmid          IdxPlacementIdInt  `thrift:"pmid,3" json:"pmid"`
	PlacementType IdxAdPlacementType `thrift:"placementType,4" json:"placementType"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CloseDomobAd               bool                 `thrift:"closeDomobAd,10" json:"closeDomobAd"`
	PremiumAds                 []IdxAdCreativeIdInt `thrift:"premiumAds,11" json:"premiumAds"`
	NormalAds                  []IdxAdCreativeIdInt `thrift:"normalAds,12" json:"normalAds"`
	HouseAdRatio               IdxHouseAdRatioIdInt `thrift:"houseAdRatio,13" json:"houseAdRatio"`
	PlacementSettingExtInfo    string               `thrift:"placementSettingExtInfo,14" json:"placementSettingExtInfo"`
	AcceptVideoLevel           int32                `thrift:"acceptVideoLevel,15" json:"acceptVideoLevel"`
	MuteVideoSetting           int32                `thrift:"muteVideoSetting,16" json:"muteVideoSetting"`
	CreativeWhiteList          []IdxAdCreativeIdInt `thrift:"creativeWhiteList,17" json:"creativeWhiteList"`
	CreativeBlackList          []IdxAdCreativeIdInt `thrift:"creativeBlackList,18" json:"creativeBlackList"`
	PlacementSettingExtInfoMap map[string]string    `thrift:"placementSettingExtInfoMap,19" json:"placementSettingExtInfoMap"`
	ContainerList              []*common.Container  `thrift:"containerList,20" json:"containerList"`
	ChannelList                []*common.Channel    `thrift:"channelList,21" json:"channelList"`
}

func NewIndexMediaPlacement() *IndexMediaPlacement {
	return &IndexMediaPlacement{
		PlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexMediaPlacement) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *IndexMediaPlacement) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.MAP {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.LIST {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = IdxUidInt(v)
	}
	return nil
}

func (p *IndexMediaPlacement) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = IdxMediaIdInt(v)
	}
	return nil
}

func (p *IndexMediaPlacement) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pmid = IdxPlacementIdInt(v)
	}
	return nil
}

func (p *IndexMediaPlacement) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PlacementType = IdxAdPlacementType(v)
	}
	return nil
}

func (p *IndexMediaPlacement) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CloseDomobAd = v
	}
	return nil
}

func (p *IndexMediaPlacement) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PremiumAds = make([]IdxAdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 IdxAdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = IdxAdCreativeIdInt(v)
		}
		p.PremiumAds = append(p.PremiumAds, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.NormalAds = make([]IdxAdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 IdxAdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = IdxAdCreativeIdInt(v)
		}
		p.NormalAds = append(p.NormalAds, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.HouseAdRatio = IdxHouseAdRatioIdInt(v)
	}
	return nil
}

func (p *IndexMediaPlacement) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PlacementSettingExtInfo = v
	}
	return nil
}

func (p *IndexMediaPlacement) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.AcceptVideoLevel = v
	}
	return nil
}

func (p *IndexMediaPlacement) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.MuteVideoSetting = v
	}
	return nil
}

func (p *IndexMediaPlacement) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeWhiteList = make([]IdxAdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 IdxAdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = IdxAdCreativeIdInt(v)
		}
		p.CreativeWhiteList = append(p.CreativeWhiteList, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeBlackList = make([]IdxAdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 IdxAdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = IdxAdCreativeIdInt(v)
		}
		p.CreativeBlackList = append(p.CreativeBlackList, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField19(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.PlacementSettingExtInfoMap = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key12 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key12 = v
		}
		var _val13 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val13 = v
		}
		p.PlacementSettingExtInfoMap[_key12] = _val13
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField20(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ContainerList = make([]*common.Container, 0, size)
	for i := 0; i < size; i++ {
		_elem14 := common.NewContainer()
		if err := _elem14.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem14)
		}
		p.ContainerList = append(p.ContainerList, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) readField21(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChannelList = make([]*common.Channel, 0, size)
	for i := 0; i < size; i++ {
		_elem15 := common.NewChannel()
		if err := _elem15.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem15)
		}
		p.ChannelList = append(p.ChannelList, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexMediaPlacement) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexMediaPlacement"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexMediaPlacement) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pmid: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:placementType: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closeDomobAd", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:closeDomobAd: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CloseDomobAd)); err != nil {
		return fmt.Errorf("%T.closeDomobAd (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:closeDomobAd: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField11(oprot thrift.TProtocol) (err error) {
	if p.PremiumAds != nil {
		if err := oprot.WriteFieldBegin("premiumAds", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:premiumAds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PremiumAds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PremiumAds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:premiumAds: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) writeField12(oprot thrift.TProtocol) (err error) {
	if p.NormalAds != nil {
		if err := oprot.WriteFieldBegin("normalAds", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:normalAds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.NormalAds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.NormalAds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:normalAds: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("houseAdRatio", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:houseAdRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HouseAdRatio)); err != nil {
		return fmt.Errorf("%T.houseAdRatio (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:houseAdRatio: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementSettingExtInfo", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:placementSettingExtInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PlacementSettingExtInfo)); err != nil {
		return fmt.Errorf("%T.placementSettingExtInfo (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:placementSettingExtInfo: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("acceptVideoLevel", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:acceptVideoLevel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AcceptVideoLevel)); err != nil {
		return fmt.Errorf("%T.acceptVideoLevel (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:acceptVideoLevel: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("muteVideoSetting", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:muteVideoSetting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MuteVideoSetting)); err != nil {
		return fmt.Errorf("%T.muteVideoSetting (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:muteVideoSetting: %s", p, err)
	}
	return err
}

func (p *IndexMediaPlacement) writeField17(oprot thrift.TProtocol) (err error) {
	if p.CreativeWhiteList != nil {
		if err := oprot.WriteFieldBegin("creativeWhiteList", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:creativeWhiteList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CreativeWhiteList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeWhiteList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:creativeWhiteList: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) writeField18(oprot thrift.TProtocol) (err error) {
	if p.CreativeBlackList != nil {
		if err := oprot.WriteFieldBegin("creativeBlackList", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:creativeBlackList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CreativeBlackList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeBlackList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:creativeBlackList: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) writeField19(oprot thrift.TProtocol) (err error) {
	if p.PlacementSettingExtInfoMap != nil {
		if err := oprot.WriteFieldBegin("placementSettingExtInfoMap", thrift.MAP, 19); err != nil {
			return fmt.Errorf("%T write field begin error 19:placementSettingExtInfoMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.PlacementSettingExtInfoMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.PlacementSettingExtInfoMap {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 19:placementSettingExtInfoMap: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) writeField20(oprot thrift.TProtocol) (err error) {
	if p.ContainerList != nil {
		if err := oprot.WriteFieldBegin("containerList", thrift.LIST, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:containerList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ContainerList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ContainerList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:containerList: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) writeField21(oprot thrift.TProtocol) (err error) {
	if p.ChannelList != nil {
		if err := oprot.WriteFieldBegin("channelList", thrift.LIST, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:channelList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChannelList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChannelList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:channelList: %s", p, err)
		}
	}
	return err
}

func (p *IndexMediaPlacement) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexMediaPlacement(%+v)", *p)
}
