// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//点击积分墙广告后的行为
type OwAdActionType int64

const (
	OwAdActionType_OWAAT_ACTION_UNKNOWN OwAdActionType = 0
	OwAdActionType_OWAAT_ACTION_URL     OwAdActionType = 1
	OwAdActionType_OWAAT_ACTION_DETAIL  OwAdActionType = 2
)

func (p OwAdActionType) String() string {
	switch p {
	case OwAdActionType_OWAAT_ACTION_UNKNOWN:
		return "OwAdActionType_OWAAT_ACTION_UNKNOWN"
	case OwAdActionType_OWAAT_ACTION_URL:
		return "OwAdActionType_OWAAT_ACTION_URL"
	case OwAdActionType_OWAAT_ACTION_DETAIL:
		return "OwAdActionType_OWAAT_ACTION_DETAIL"
	}
	return "<UNSET>"
}

func OwAdActionTypeFromString(s string) (OwAdActionType, error) {
	switch s {
	case "OwAdActionType_OWAAT_ACTION_UNKNOWN":
		return OwAdActionType_OWAAT_ACTION_UNKNOWN, nil
	case "OwAdActionType_OWAAT_ACTION_URL":
		return OwAdActionType_OWAAT_ACTION_URL, nil
	case "OwAdActionType_OWAAT_ACTION_DETAIL":
		return OwAdActionType_OWAAT_ACTION_DETAIL, nil
	}
	return OwAdActionType(math.MinInt32 - 1), fmt.Errorf("not a valid OwAdActionType string")
}

//媒体自推荐应用在广告列表的位置
type OwMediaSrPlacmentType int64

const (
	OwMediaSrPlacmentType_OWMSPT_AFTER  OwMediaSrPlacmentType = 0
	OwMediaSrPlacmentType_OWMSPT_BEFORE OwMediaSrPlacmentType = 1
)

func (p OwMediaSrPlacmentType) String() string {
	switch p {
	case OwMediaSrPlacmentType_OWMSPT_AFTER:
		return "OwMediaSrPlacmentType_OWMSPT_AFTER"
	case OwMediaSrPlacmentType_OWMSPT_BEFORE:
		return "OwMediaSrPlacmentType_OWMSPT_BEFORE"
	}
	return "<UNSET>"
}

func OwMediaSrPlacmentTypeFromString(s string) (OwMediaSrPlacmentType, error) {
	switch s {
	case "OwMediaSrPlacmentType_OWMSPT_AFTER":
		return OwMediaSrPlacmentType_OWMSPT_AFTER, nil
	case "OwMediaSrPlacmentType_OWMSPT_BEFORE":
		return OwMediaSrPlacmentType_OWMSPT_BEFORE, nil
	}
	return OwMediaSrPlacmentType(math.MinInt32 - 1), fmt.Errorf("not a valid OwMediaSrPlacmentType string")
}

//广告计划状态
type OwAdPlanStatus int64

const (
	OwAdPlanStatus_OWAPS_RUNNABLE  OwAdPlanStatus = 0
	OwAdPlanStatus_OWAPS_DELETED   OwAdPlanStatus = 1
	OwAdPlanStatus_OWAPS_FORBIDDEN OwAdPlanStatus = 2
)

func (p OwAdPlanStatus) String() string {
	switch p {
	case OwAdPlanStatus_OWAPS_RUNNABLE:
		return "OwAdPlanStatus_OWAPS_RUNNABLE"
	case OwAdPlanStatus_OWAPS_DELETED:
		return "OwAdPlanStatus_OWAPS_DELETED"
	case OwAdPlanStatus_OWAPS_FORBIDDEN:
		return "OwAdPlanStatus_OWAPS_FORBIDDEN"
	}
	return "<UNSET>"
}

func OwAdPlanStatusFromString(s string) (OwAdPlanStatus, error) {
	switch s {
	case "OwAdPlanStatus_OWAPS_RUNNABLE":
		return OwAdPlanStatus_OWAPS_RUNNABLE, nil
	case "OwAdPlanStatus_OWAPS_DELETED":
		return OwAdPlanStatus_OWAPS_DELETED, nil
	case "OwAdPlanStatus_OWAPS_FORBIDDEN":
		return OwAdPlanStatus_OWAPS_FORBIDDEN, nil
	}
	return OwAdPlanStatus(math.MinInt32 - 1), fmt.Errorf("not a valid OwAdPlanStatus string")
}

//媒体状态
type OwMediaStatus int64

const (
	OwMediaStatus_OWMS_RUNNABLE                  OwMediaStatus = 0
	OwMediaStatus_OWMS_DELETED                   OwMediaStatus = 1
	OwMediaStatus_OWMS_FORBIDDEN                 OwMediaStatus = 2
	OwMediaStatus_OWMS_DRAFT                     OwMediaStatus = 3
	OwMediaStatus_OWMS_PENDING_APPROVAL          OwMediaStatus = 4
	OwMediaStatus_OWMS_PENDING_APPROVAL_REUPLOAD OwMediaStatus = 5
	OwMediaStatus_OWMS_REJECTED                  OwMediaStatus = 6
	OwMediaStatus_OWMS_PENDING_UPLOAD            OwMediaStatus = 7
)

func (p OwMediaStatus) String() string {
	switch p {
	case OwMediaStatus_OWMS_RUNNABLE:
		return "OwMediaStatus_OWMS_RUNNABLE"
	case OwMediaStatus_OWMS_DELETED:
		return "OwMediaStatus_OWMS_DELETED"
	case OwMediaStatus_OWMS_FORBIDDEN:
		return "OwMediaStatus_OWMS_FORBIDDEN"
	case OwMediaStatus_OWMS_DRAFT:
		return "OwMediaStatus_OWMS_DRAFT"
	case OwMediaStatus_OWMS_PENDING_APPROVAL:
		return "OwMediaStatus_OWMS_PENDING_APPROVAL"
	case OwMediaStatus_OWMS_PENDING_APPROVAL_REUPLOAD:
		return "OwMediaStatus_OWMS_PENDING_APPROVAL_REUPLOAD"
	case OwMediaStatus_OWMS_REJECTED:
		return "OwMediaStatus_OWMS_REJECTED"
	case OwMediaStatus_OWMS_PENDING_UPLOAD:
		return "OwMediaStatus_OWMS_PENDING_UPLOAD"
	}
	return "<UNSET>"
}

func OwMediaStatusFromString(s string) (OwMediaStatus, error) {
	switch s {
	case "OwMediaStatus_OWMS_RUNNABLE":
		return OwMediaStatus_OWMS_RUNNABLE, nil
	case "OwMediaStatus_OWMS_DELETED":
		return OwMediaStatus_OWMS_DELETED, nil
	case "OwMediaStatus_OWMS_FORBIDDEN":
		return OwMediaStatus_OWMS_FORBIDDEN, nil
	case "OwMediaStatus_OWMS_DRAFT":
		return OwMediaStatus_OWMS_DRAFT, nil
	case "OwMediaStatus_OWMS_PENDING_APPROVAL":
		return OwMediaStatus_OWMS_PENDING_APPROVAL, nil
	case "OwMediaStatus_OWMS_PENDING_APPROVAL_REUPLOAD":
		return OwMediaStatus_OWMS_PENDING_APPROVAL_REUPLOAD, nil
	case "OwMediaStatus_OWMS_REJECTED":
		return OwMediaStatus_OWMS_REJECTED, nil
	case "OwMediaStatus_OWMS_PENDING_UPLOAD":
		return OwMediaStatus_OWMS_PENDING_UPLOAD, nil
	}
	return OwMediaStatus(math.MinInt32 - 1), fmt.Errorf("not a valid OwMediaStatus string")
}

//积分墙激活数据反馈方式
type OwFeedback int64

const (
	OwFeedback_OWFB_UNKNOWN     OwFeedback = 0
	OwFeedback_OWFB_TRACKINGSDK OwFeedback = 1
	OwFeedback_OWFB_SERVERAPI   OwFeedback = 2
	OwFeedback_OWFB_MANUAL      OwFeedback = 3
)

func (p OwFeedback) String() string {
	switch p {
	case OwFeedback_OWFB_UNKNOWN:
		return "OwFeedback_OWFB_UNKNOWN"
	case OwFeedback_OWFB_TRACKINGSDK:
		return "OwFeedback_OWFB_TRACKINGSDK"
	case OwFeedback_OWFB_SERVERAPI:
		return "OwFeedback_OWFB_SERVERAPI"
	case OwFeedback_OWFB_MANUAL:
		return "OwFeedback_OWFB_MANUAL"
	}
	return "<UNSET>"
}

func OwFeedbackFromString(s string) (OwFeedback, error) {
	switch s {
	case "OwFeedback_OWFB_UNKNOWN":
		return OwFeedback_OWFB_UNKNOWN, nil
	case "OwFeedback_OWFB_TRACKINGSDK":
		return OwFeedback_OWFB_TRACKINGSDK, nil
	case "OwFeedback_OWFB_SERVERAPI":
		return OwFeedback_OWFB_SERVERAPI, nil
	case "OwFeedback_OWFB_MANUAL":
		return OwFeedback_OWFB_MANUAL, nil
	}
	return OwFeedback(math.MinInt32 - 1), fmt.Errorf("not a valid OwFeedback string")
}

//用于积分墙激活数据核对的设备号类型
type OwFeedbackDevice int64

const (
	OwFeedbackDevice_OWFBD_UNKNOWN OwFeedbackDevice = 0
	OwFeedbackDevice_OWFBD_MACMD5  OwFeedbackDevice = 1
	OwFeedbackDevice_OWFBD_OID     OwFeedbackDevice = 2
	OwFeedbackDevice_OWFBD_UUID    OwFeedbackDevice = 3
)

func (p OwFeedbackDevice) String() string {
	switch p {
	case OwFeedbackDevice_OWFBD_UNKNOWN:
		return "OwFeedbackDevice_OWFBD_UNKNOWN"
	case OwFeedbackDevice_OWFBD_MACMD5:
		return "OwFeedbackDevice_OWFBD_MACMD5"
	case OwFeedbackDevice_OWFBD_OID:
		return "OwFeedbackDevice_OWFBD_OID"
	case OwFeedbackDevice_OWFBD_UUID:
		return "OwFeedbackDevice_OWFBD_UUID"
	}
	return "<UNSET>"
}

func OwFeedbackDeviceFromString(s string) (OwFeedbackDevice, error) {
	switch s {
	case "OwFeedbackDevice_OWFBD_UNKNOWN":
		return OwFeedbackDevice_OWFBD_UNKNOWN, nil
	case "OwFeedbackDevice_OWFBD_MACMD5":
		return OwFeedbackDevice_OWFBD_MACMD5, nil
	case "OwFeedbackDevice_OWFBD_OID":
		return OwFeedbackDevice_OWFBD_OID, nil
	case "OwFeedbackDevice_OWFBD_UUID":
		return OwFeedbackDevice_OWFBD_UUID, nil
	}
	return OwFeedbackDevice(math.MinInt32 - 1), fmt.Errorf("not a valid OwFeedbackDevice string")
}

//@Description("积分墙广告状态，主要用于显示分类")
type OwAdStatus int64

const (
	OwAdStatus_TODO    OwAdStatus = 0
	OwAdStatus_SIGN_IN OwAdStatus = 1
	OwAdStatus_DONE    OwAdStatus = 2
)

func (p OwAdStatus) String() string {
	switch p {
	case OwAdStatus_TODO:
		return "OwAdStatus_TODO"
	case OwAdStatus_SIGN_IN:
		return "OwAdStatus_SIGN_IN"
	case OwAdStatus_DONE:
		return "OwAdStatus_DONE"
	}
	return "<UNSET>"
}

func OwAdStatusFromString(s string) (OwAdStatus, error) {
	switch s {
	case "OwAdStatus_TODO":
		return OwAdStatus_TODO, nil
	case "OwAdStatus_SIGN_IN":
		return OwAdStatus_SIGN_IN, nil
	case "OwAdStatus_DONE":
		return OwAdStatus_DONE, nil
	}
	return OwAdStatus(math.MinInt32 - 1), fmt.Errorf("not a valid OwAdStatus string")
}

//url打开方式
type OwAdUrlActionType int64

const (
	OwAdUrlActionType_OUTAPP OwAdUrlActionType = 0
	OwAdUrlActionType_INAPP  OwAdUrlActionType = 1
)

func (p OwAdUrlActionType) String() string {
	switch p {
	case OwAdUrlActionType_OUTAPP:
		return "OwAdUrlActionType_OUTAPP"
	case OwAdUrlActionType_INAPP:
		return "OwAdUrlActionType_INAPP"
	}
	return "<UNSET>"
}

func OwAdUrlActionTypeFromString(s string) (OwAdUrlActionType, error) {
	switch s {
	case "OwAdUrlActionType_OUTAPP":
		return OwAdUrlActionType_OUTAPP, nil
	case "OwAdUrlActionType_INAPP":
		return OwAdUrlActionType_INAPP, nil
	}
	return OwAdUrlActionType(math.MinInt32 - 1), fmt.Errorf("not a valid OwAdUrlActionType string")
}

type LargeIdInt common.LargeIdInt

type UidInt common.UidInt

type MediaIdInt common.IdInt

type PlanIdInt common.IdInt

type CreativeIdInt common.IdInt

type ImgIdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type JailBreakCode common.JailBreakCode

type OwAct struct {
	SearchId   LargeIdInt    `thrift:"searchId,1" json:"searchId"`
	PubId      string        `thrift:"pubId,2" json:"pubId"`
	DeverId    UidInt        `thrift:"deverId,3" json:"deverId"`
	MediaId    MediaIdInt    `thrift:"mediaId,4" json:"mediaId"`
	SponsorId  UidInt        `thrift:"sponsorId,5" json:"sponsorId"`
	PlanId     PlanIdInt     `thrift:"planId,6" json:"planId"`
	Cid        CreativeIdInt `thrift:"cid,7" json:"cid"`
	AppId      string        `thrift:"appId,8" json:"appId"`
	DeviceId   string        `thrift:"deviceId,9" json:"deviceId"`
	Price      Amount        `thrift:"price,10" json:"price"`
	MediaShare Amount        `thrift:"mediaShare,11" json:"mediaShare"`
	Point      Amount        `thrift:"point,12" json:"point"`
	ClickTime  TimeInt       `thrift:"clickTime,13" json:"clickTime"`
	ActTime    TimeInt       `thrift:"actTime,14" json:"actTime"`
	SettleTime TimeInt       `thrift:"settleTime,15" json:"settleTime"`
	OverBudget bool          `thrift:"overBudget,16" json:"overBudget"`
	VideoMedia int32         `thrift:"video_media,17" json:"video_media"`
}

func NewOwAct() *OwAct {
	return &OwAct{}
}

func (p *OwAct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *OwAct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PubId = v
	}
	return nil
}

func (p *OwAct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DeverId = UidInt(v)
	}
	return nil
}

func (p *OwAct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MediaId = MediaIdInt(v)
	}
	return nil
}

func (p *OwAct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SponsorId = UidInt(v)
	}
	return nil
}

func (p *OwAct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PlanId = PlanIdInt(v)
	}
	return nil
}

func (p *OwAct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Cid = CreativeIdInt(v)
	}
	return nil
}

func (p *OwAct) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *OwAct) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.DeviceId = v
	}
	return nil
}

func (p *OwAct) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *OwAct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MediaShare = Amount(v)
	}
	return nil
}

func (p *OwAct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Point = Amount(v)
	}
	return nil
}

func (p *OwAct) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ClickTime = TimeInt(v)
	}
	return nil
}

func (p *OwAct) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActTime = TimeInt(v)
	}
	return nil
}

func (p *OwAct) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.SettleTime = TimeInt(v)
	}
	return nil
}

func (p *OwAct) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.OverBudget = v
	}
	return nil
}

func (p *OwAct) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.VideoMedia = v
	}
	return nil
}

func (p *OwAct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pubId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PubId)); err != nil {
		return fmt.Errorf("%T.pubId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pubId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:deverId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mediaId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sponsorId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:planId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:cid: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:appId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:appId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceId", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:deviceId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceId)); err != nil {
		return fmt.Errorf("%T.deviceId (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:deviceId: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:price: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaShare", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:mediaShare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaShare)); err != nil {
		return fmt.Errorf("%T.mediaShare (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:mediaShare: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:point: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTime", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:clickTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickTime)); err != nil {
		return fmt.Errorf("%T.clickTime (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:clickTime: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actTime", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:actTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ActTime)); err != nil {
		return fmt.Errorf("%T.actTime (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:actTime: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settleTime", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:settleTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleTime)); err != nil {
		return fmt.Errorf("%T.settleTime (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:settleTime: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("overBudget", thrift.BOOL, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:overBudget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.OverBudget)); err != nil {
		return fmt.Errorf("%T.overBudget (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:overBudget: %s", p, err)
	}
	return err
}

func (p *OwAct) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_media", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:video_media: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoMedia)); err != nil {
		return fmt.Errorf("%T.video_media (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:video_media: %s", p, err)
	}
	return err
}

func (p *OwAct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAct(%+v)", *p)
}

type OwRawAct struct {
	Actid       int64  `thrift:"actid,1" json:"actid"`
	Appid       string `thrift:"appid,2" json:"appid"`
	Macmd5      string `thrift:"macmd5,3" json:"macmd5"`
	Odin1       string `thrift:"odin1,4" json:"odin1"`
	Uuid        string `thrift:"uuid,5" json:"uuid"`
	Oid         string `thrift:"oid,6" json:"oid"`
	ActTime     int32  `thrift:"act_time,7" json:"act_time"`
	Status      int32  `thrift:"status,8" json:"status"`
	ProcTime    int32  `thrift:"proc_time,9" json:"proc_time"`
	Hostid      string `thrift:"hostid,10" json:"hostid"`
	Dt          int32  `thrift:"dt,11" json:"dt"`
	Hr          int32  `thrift:"hr,12" json:"hr"`
	Dmac        string `thrift:"dmac,13" json:"dmac"`
	Source      string `thrift:"source,14" json:"source"`
	Imei        string `thrift:"imei,15" json:"imei"`
	AndroidId   string `thrift:"android_id,16" json:"android_id"`
	Idfa        string `thrift:"idfa,17" json:"idfa"`
	Platform    int32  `thrift:"platform,18" json:"platform"`
	Action      int32  `thrift:"action,19" json:"action"`
	Searchid    int64  `thrift:"searchid,20" json:"searchid"`
	Sr          int16  `thrift:"sr,21" json:"sr"`
	Cid         int32  `thrift:"cid,22" json:"cid"`
	Planid      int32  `thrift:"planid,23" json:"planid"`
	Uid         int32  `thrift:"uid,24" json:"uid"`
	Price       int64  `thrift:"price,25" json:"price"`
	Mediashare  int64  `thrift:"mediashare,26" json:"mediashare"`
	Point       int64  `thrift:"point,27" json:"point"`
	AccessCode  int32  `thrift:"access_code,28" json:"access_code"`
	DeviceCode  int32  `thrift:"device_code,29" json:"device_code"`
	Pubid       string `thrift:"pubid,30" json:"pubid"`
	PubMediaid  int32  `thrift:"pub_mediaid,31" json:"pub_mediaid"`
	PubDeverid  int32  `thrift:"pub_deverid,32" json:"pub_deverid"`
	Userid      string `thrift:"userid,33" json:"userid"`
	Ip          string `thrift:"ip,34" json:"ip"`
	RegionCode  int32  `thrift:"region_code,35" json:"region_code"`
	SpPrice     int64  `thrift:"sp_price,36" json:"sp_price"`
	Rank        int32  `thrift:"rank,37" json:"rank"`
	DisplayType int32  `thrift:"display_type,38" json:"display_type"`
	EntryType   int32  `thrift:"entry_type,39" json:"entry_type"`
	Sv          int32  `thrift:"sv,40" json:"sv"`
	Isspam      bool   `thrift:"isspam,41" json:"isspam"`
	SpamType    string `thrift:"spam_type,42" json:"spam_type"`
	OfferType   int32  `thrift:"offer_type,43" json:"offer_type"`
	CostType    int32  `thrift:"cost_type,44" json:"cost_type"`
	AdCharge    bool   `thrift:"ad_charge,45" json:"ad_charge"`
	MediaCharge bool   `thrift:"media_charge,46" json:"media_charge"`
	OsCode      int32  `thrift:"os_code,47" json:"os_code"`
	Idfv        string `thrift:"idfv,48" json:"idfv"`
	AntiStatus  int16  `thrift:"anti_status,49" json:"anti_status"`
	Duid        string `thrift:"duid,50" json:"duid"`
	Dmid        string `thrift:"dmid,51" json:"dmid"`
	Amac        string `thrift:"amac,52" json:"amac"`
	Amn         string `thrift:"amn,53" json:"amn"`
	Latitude    string `thrift:"latitude,54" json:"latitude"`
	Longitude   string `thrift:"longitude,55" json:"longitude"`
	Oifa        string `thrift:"oifa,56" json:"oifa"`
	Aifa        string `thrift:"aifa,57" json:"aifa"`
	Extinfo     string `thrift:"extinfo,58" json:"extinfo"`
	ClkTime     int32  `thrift:"clk_time,59" json:"clk_time"`
	Clkid       int32  `thrift:"clkid,60" json:"clkid"`
	ClkIsspam   bool   `thrift:"clk_isspam,61" json:"clk_isspam"`
	ClkSpamType string `thrift:"clk_spam_type,62" json:"clk_spam_type"`
	PackageName string `thrift:"package_name,63" json:"package_name"`
}

func NewOwRawAct() *OwRawAct {
	return &OwRawAct{}
}

func (p *OwRawAct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I16 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I64 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I32 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I32 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.STRING {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.I16 {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRING {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.STRING {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.STRING {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.STRING {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.STRING {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.STRING {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.I32 {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I32 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.STRING {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.STRING {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwRawAct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Actid = v
	}
	return nil
}

func (p *OwRawAct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *OwRawAct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *OwRawAct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwRawAct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *OwRawAct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwRawAct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ActTime = v
	}
	return nil
}

func (p *OwRawAct) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwRawAct) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ProcTime = v
	}
	return nil
}

func (p *OwRawAct) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Hostid = v
	}
	return nil
}

func (p *OwRawAct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *OwRawAct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *OwRawAct) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *OwRawAct) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *OwRawAct) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *OwRawAct) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *OwRawAct) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *OwRawAct) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *OwRawAct) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *OwRawAct) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *OwRawAct) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *OwRawAct) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *OwRawAct) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *OwRawAct) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwRawAct) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *OwRawAct) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *OwRawAct) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *OwRawAct) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.AccessCode = v
	}
	return nil
}

func (p *OwRawAct) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *OwRawAct) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *OwRawAct) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *OwRawAct) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *OwRawAct) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *OwRawAct) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *OwRawAct) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *OwRawAct) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *OwRawAct) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *OwRawAct) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *OwRawAct) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.EntryType = v
	}
	return nil
}

func (p *OwRawAct) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *OwRawAct) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *OwRawAct) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *OwRawAct) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwRawAct) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwRawAct) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *OwRawAct) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *OwRawAct) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *OwRawAct) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.Idfv = v
	}
	return nil
}

func (p *OwRawAct) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.AntiStatus = v
	}
	return nil
}

func (p *OwRawAct) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *OwRawAct) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Dmid = v
	}
	return nil
}

func (p *OwRawAct) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *OwRawAct) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *OwRawAct) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *OwRawAct) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *OwRawAct) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.Oifa = v
	}
	return nil
}

func (p *OwRawAct) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.Aifa = v
	}
	return nil
}

func (p *OwRawAct) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.Extinfo = v
	}
	return nil
}

func (p *OwRawAct) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.ClkTime = v
	}
	return nil
}

func (p *OwRawAct) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Clkid = v
	}
	return nil
}

func (p *OwRawAct) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.ClkIsspam = v
	}
	return nil
}

func (p *OwRawAct) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.ClkSpamType = v
	}
	return nil
}

func (p *OwRawAct) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *OwRawAct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwRawAct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwRawAct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:actid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Actid)); err != nil {
		return fmt.Errorf("%T.actid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:actid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:macmd5: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:odin1: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:uuid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:oid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_time", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:act_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActTime)); err != nil {
		return fmt.Errorf("%T.act_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:act_time: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:status: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("proc_time", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:proc_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProcTime)); err != nil {
		return fmt.Errorf("%T.proc_time (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:proc_time: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostid", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:hostid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostid)); err != nil {
		return fmt.Errorf("%T.hostid (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:hostid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:dt: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:hr: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:dmac: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:source: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:imei: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:android_id: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:idfa: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:platform: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:action: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:searchid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:sr: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:cid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:planid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:uid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:price: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:mediashare: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:point: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:access_code: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:device_code: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:pubid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:pub_deverid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:userid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:ip: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:region_code: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:sp_price: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:rank: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:display_type: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entry_type", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:entry_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EntryType)); err != nil {
		return fmt.Errorf("%T.entry_type (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:entry_type: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:sv: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:isspam: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:spam_type: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I32, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:offer_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:offer_type: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:cost_type: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:ad_charge: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:media_charge: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:os_code: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfv", thrift.STRING, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:idfv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfv)); err != nil {
		return fmt.Errorf("%T.idfv (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:idfv: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anti_status", thrift.I16, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:anti_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.AntiStatus)); err != nil {
		return fmt.Errorf("%T.anti_status (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:anti_status: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.STRING, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:duid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:duid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmid", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:dmid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmid)); err != nil {
		return fmt.Errorf("%T.dmid (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:dmid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:amac: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:amn: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:latitude: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:longitude: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oifa", thrift.STRING, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:oifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oifa)); err != nil {
		return fmt.Errorf("%T.oifa (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:oifa: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aifa", thrift.STRING, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:aifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Aifa)); err != nil {
		return fmt.Errorf("%T.aifa (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:aifa: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extinfo", thrift.STRING, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:extinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extinfo)); err != nil {
		return fmt.Errorf("%T.extinfo (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:extinfo: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_time", thrift.I32, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:clk_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkTime)); err != nil {
		return fmt.Errorf("%T.clk_time (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:clk_time: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkid", thrift.I32, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:clkid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Clkid)); err != nil {
		return fmt.Errorf("%T.clkid (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:clkid: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_isspam", thrift.BOOL, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:clk_isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ClkIsspam)); err != nil {
		return fmt.Errorf("%T.clk_isspam (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:clk_isspam: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_spam_type", thrift.STRING, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:clk_spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkSpamType)); err != nil {
		return fmt.Errorf("%T.clk_spam_type (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:clk_spam_type: %s", p, err)
	}
	return err
}

func (p *OwRawAct) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:package_name: %s", p, err)
	}
	return err
}

func (p *OwRawAct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwRawAct(%+v)", *p)
}

type OwRawClick struct {
	Clkid       int64  `thrift:"clkid,1" json:"clkid"`
	Searchid    int64  `thrift:"searchid,2" json:"searchid"`
	Sr          int16  `thrift:"sr,3" json:"sr"`
	Cid         int32  `thrift:"cid,4" json:"cid"`
	Planid      int32  `thrift:"planid,5" json:"planid"`
	Uid         int32  `thrift:"uid,6" json:"uid"`
	Price       int64  `thrift:"price,7" json:"price"`
	Mediashare  int64  `thrift:"mediashare,8" json:"mediashare"`
	Point       int64  `thrift:"point,9" json:"point"`
	AccessCode  int32  `thrift:"access_code,10" json:"access_code"`
	DeviceCode  int32  `thrift:"device_code,11" json:"device_code"`
	Appid       string `thrift:"appid,12" json:"appid"`
	Pubid       string `thrift:"pubid,13" json:"pubid"`
	PubMediaid  int32  `thrift:"pub_mediaid,14" json:"pub_mediaid"`
	PubDeverid  int32  `thrift:"pub_deverid,15" json:"pub_deverid"`
	Macmd5      string `thrift:"macmd5,16" json:"macmd5"`
	Odin1       string `thrift:"odin1,17" json:"odin1"`
	Uuid        string `thrift:"uuid,18" json:"uuid"`
	Oid         string `thrift:"oid,19" json:"oid"`
	Dmac        string `thrift:"dmac,20" json:"dmac"`
	ClkTime     int32  `thrift:"clk_time,21" json:"clk_time"`
	Hostid      string `thrift:"hostid,22" json:"hostid"`
	Dt          int32  `thrift:"dt,23" json:"dt"`
	Hr          int32  `thrift:"hr,24" json:"hr"`
	Sendstatus  int32  `thrift:"sendstatus,25" json:"sendstatus"`
	Userid      string `thrift:"userid,26" json:"userid"`
	CallbackUrl string `thrift:"callback_url,27" json:"callback_url"`
	Ip          string `thrift:"ip,28" json:"ip"`
	RegionCode  int32  `thrift:"region_code,29" json:"region_code"`
	SpPrice     int64  `thrift:"sp_price,30" json:"sp_price"`
	Rank        int32  `thrift:"rank,31" json:"rank"`
	IsAct       int32  `thrift:"is_act,32" json:"is_act"`
	DisplayType int32  `thrift:"display_type,33" json:"display_type"`
	EntryType   int32  `thrift:"entry_type,34" json:"entry_type"`
	Sv          int32  `thrift:"sv,35" json:"sv"`
	Imei        string `thrift:"imei,36" json:"imei"`
	AndroidId   string `thrift:"android_id,37" json:"android_id"`
	Idfa        string `thrift:"idfa,38" json:"idfa"`
	Platform    int32  `thrift:"platform,39" json:"platform"`
	Action      int32  `thrift:"action,40" json:"action"`
	CostType    int32  `thrift:"cost_type,41" json:"cost_type"`
	OfferType   int32  `thrift:"offer_type,42" json:"offer_type"`
	Isspam      bool   `thrift:"isspam,43" json:"isspam"`
	SpamType    string `thrift:"spam_type,44" json:"spam_type"`
	AdCharge    bool   `thrift:"ad_charge,45" json:"ad_charge"`
	MediaCharge bool   `thrift:"media_charge,46" json:"media_charge"`
	OsCode      int32  `thrift:"os_code,47" json:"os_code"`
	Idfv        string `thrift:"idfv,48" json:"idfv"`
	Duid        string `thrift:"duid,49" json:"duid"`
	Dmid        string `thrift:"dmid,50" json:"dmid"`
	Amac        string `thrift:"amac,51" json:"amac"`
	Amn         string `thrift:"amn,52" json:"amn"`
	Latitude    string `thrift:"latitude,53" json:"latitude"`
	Longitude   string `thrift:"longitude,54" json:"longitude"`
	Oifa        string `thrift:"oifa,55" json:"oifa"`
	Aifa        string `thrift:"aifa,56" json:"aifa"`
	Extinfo     string `thrift:"extinfo,57" json:"extinfo"`
	AntiStatus  int16  `thrift:"anti_status,58" json:"anti_status"`
	PackageName string `thrift:"package_name,59" json:"package_name"`
	Source      int16  `thrift:"source,60" json:"source"`
}

func NewOwRawClick() *OwRawClick {
	return &OwRawClick{}
}

func (p *OwRawClick) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.STRING {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.STRING {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRING {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.STRING {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.STRING {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.STRING {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.STRING {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.I16 {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.STRING {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I16 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwRawClick) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Clkid = v
	}
	return nil
}

func (p *OwRawClick) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *OwRawClick) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *OwRawClick) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *OwRawClick) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *OwRawClick) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwRawClick) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *OwRawClick) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *OwRawClick) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *OwRawClick) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AccessCode = v
	}
	return nil
}

func (p *OwRawClick) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *OwRawClick) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *OwRawClick) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *OwRawClick) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *OwRawClick) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *OwRawClick) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *OwRawClick) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwRawClick) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *OwRawClick) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwRawClick) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *OwRawClick) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ClkTime = v
	}
	return nil
}

func (p *OwRawClick) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Hostid = v
	}
	return nil
}

func (p *OwRawClick) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *OwRawClick) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *OwRawClick) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Sendstatus = v
	}
	return nil
}

func (p *OwRawClick) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *OwRawClick) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.CallbackUrl = v
	}
	return nil
}

func (p *OwRawClick) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *OwRawClick) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *OwRawClick) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *OwRawClick) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *OwRawClick) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.IsAct = v
	}
	return nil
}

func (p *OwRawClick) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *OwRawClick) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.EntryType = v
	}
	return nil
}

func (p *OwRawClick) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *OwRawClick) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *OwRawClick) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *OwRawClick) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *OwRawClick) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *OwRawClick) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *OwRawClick) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwRawClick) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwRawClick) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *OwRawClick) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *OwRawClick) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *OwRawClick) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *OwRawClick) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *OwRawClick) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.Idfv = v
	}
	return nil
}

func (p *OwRawClick) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *OwRawClick) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.Dmid = v
	}
	return nil
}

func (p *OwRawClick) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *OwRawClick) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *OwRawClick) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *OwRawClick) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *OwRawClick) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.Oifa = v
	}
	return nil
}

func (p *OwRawClick) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.Aifa = v
	}
	return nil
}

func (p *OwRawClick) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.Extinfo = v
	}
	return nil
}

func (p *OwRawClick) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.AntiStatus = v
	}
	return nil
}

func (p *OwRawClick) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *OwRawClick) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *OwRawClick) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwRawClick"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwRawClick) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:clkid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clkid)); err != nil {
		return fmt.Errorf("%T.clkid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:clkid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sr: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:planid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:price: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:mediashare: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:point: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:access_code: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:device_code: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:appid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:pubid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:pub_deverid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:macmd5: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:odin1: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:uuid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:oid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:dmac: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_time", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:clk_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkTime)); err != nil {
		return fmt.Errorf("%T.clk_time (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:clk_time: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostid", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:hostid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostid)); err != nil {
		return fmt.Errorf("%T.hostid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:hostid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:dt: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:hr: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sendstatus", thrift.I32, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:sendstatus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sendstatus)); err != nil {
		return fmt.Errorf("%T.sendstatus (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:sendstatus: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:userid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("callback_url", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:callback_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CallbackUrl)); err != nil {
		return fmt.Errorf("%T.callback_url (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:callback_url: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:ip: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:region_code: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:sp_price: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:rank: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_act", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:is_act: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsAct)); err != nil {
		return fmt.Errorf("%T.is_act (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:is_act: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:display_type: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entry_type", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:entry_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EntryType)); err != nil {
		return fmt.Errorf("%T.entry_type (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:entry_type: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:sv: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:imei: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:android_id: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:idfa: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:platform: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:action: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:cost_type: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:offer_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:offer_type: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:isspam: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:spam_type: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:ad_charge: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:media_charge: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:os_code: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfv", thrift.STRING, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:idfv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfv)); err != nil {
		return fmt.Errorf("%T.idfv (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:idfv: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.STRING, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:duid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:duid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmid", thrift.STRING, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:dmid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmid)); err != nil {
		return fmt.Errorf("%T.dmid (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:dmid: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:amac: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:amn: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:latitude: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:longitude: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oifa", thrift.STRING, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:oifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oifa)); err != nil {
		return fmt.Errorf("%T.oifa (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:oifa: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aifa", thrift.STRING, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:aifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Aifa)); err != nil {
		return fmt.Errorf("%T.aifa (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:aifa: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extinfo", thrift.STRING, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:extinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extinfo)); err != nil {
		return fmt.Errorf("%T.extinfo (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:extinfo: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anti_status", thrift.I16, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:anti_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.AntiStatus)); err != nil {
		return fmt.Errorf("%T.anti_status (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:anti_status: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:package_name: %s", p, err)
	}
	return err
}

func (p *OwRawClick) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.I16, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:source: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Source)); err != nil {
		return fmt.Errorf("%T.source (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:source: %s", p, err)
	}
	return err
}

func (p *OwRawClick) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwRawClick(%+v)", *p)
}

type OwFullAct struct {
	Act              *OwRawAct   `thrift:"act,1" json:"act"`
	Clk              *OwRawClick `thrift:"clk,2" json:"clk"`
	SettlePrice      Amount      `thrift:"settle_price,3" json:"settle_price"`
	SettleMediashare Amount      `thrift:"settle_mediashare,4" json:"settle_mediashare"`
	SettlePoint      Amount      `thrift:"settle_point,5" json:"settle_point"`
	SettleTime       TimeInt     `thrift:"settle_time,6" json:"settle_time"`
	SettleDt         int32       `thrift:"settle_dt,7" json:"settle_dt"`
	SettleHr         int32       `thrift:"settle_hr,8" json:"settle_hr"`
	SettleStatus     int32       `thrift:"settle_status,9" json:"settle_status"`
	SettleOverbudget bool        `thrift:"settle_overbudget,10" json:"settle_overbudget"`
	Isspam           bool        `thrift:"isspam,11" json:"isspam"`
	SpamType         string      `thrift:"spam_type,12" json:"spam_type"`
	AdCharge         bool        `thrift:"ad_charge,13" json:"ad_charge"`
	MediaCharge      bool        `thrift:"media_charge,14" json:"media_charge"`
}

func NewOwFullAct() *OwFullAct {
	return &OwFullAct{}
}

func (p *OwFullAct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwFullAct) readField1(iprot thrift.TProtocol) error {
	p.Act = NewOwRawAct()
	if err := p.Act.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Act)
	}
	return nil
}

func (p *OwFullAct) readField2(iprot thrift.TProtocol) error {
	p.Clk = NewOwRawClick()
	if err := p.Clk.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Clk)
	}
	return nil
}

func (p *OwFullAct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SettlePrice = Amount(v)
	}
	return nil
}

func (p *OwFullAct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SettleMediashare = Amount(v)
	}
	return nil
}

func (p *OwFullAct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SettlePoint = Amount(v)
	}
	return nil
}

func (p *OwFullAct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SettleTime = TimeInt(v)
	}
	return nil
}

func (p *OwFullAct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SettleDt = v
	}
	return nil
}

func (p *OwFullAct) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SettleHr = v
	}
	return nil
}

func (p *OwFullAct) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SettleStatus = v
	}
	return nil
}

func (p *OwFullAct) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.SettleOverbudget = v
	}
	return nil
}

func (p *OwFullAct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *OwFullAct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *OwFullAct) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *OwFullAct) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *OwFullAct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwFullAct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwFullAct) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Act != nil {
		if err := oprot.WriteFieldBegin("act", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:act: %s", p, err)
		}
		if err := p.Act.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Act)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:act: %s", p, err)
		}
	}
	return err
}

func (p *OwFullAct) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Clk != nil {
		if err := oprot.WriteFieldBegin("clk", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:clk: %s", p, err)
		}
		if err := p.Clk.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Clk)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:clk: %s", p, err)
		}
	}
	return err
}

func (p *OwFullAct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_price", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:settle_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettlePrice)); err != nil {
		return fmt.Errorf("%T.settle_price (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:settle_price: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_mediashare", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:settle_mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleMediashare)); err != nil {
		return fmt.Errorf("%T.settle_mediashare (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:settle_mediashare: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_point", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:settle_point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettlePoint)); err != nil {
		return fmt.Errorf("%T.settle_point (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:settle_point: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_time", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:settle_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleTime)); err != nil {
		return fmt.Errorf("%T.settle_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:settle_time: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_dt", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:settle_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SettleDt)); err != nil {
		return fmt.Errorf("%T.settle_dt (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:settle_dt: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_hr", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:settle_hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SettleHr)); err != nil {
		return fmt.Errorf("%T.settle_hr (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:settle_hr: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_status", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:settle_status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SettleStatus)); err != nil {
		return fmt.Errorf("%T.settle_status (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:settle_status: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_overbudget", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:settle_overbudget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SettleOverbudget)); err != nil {
		return fmt.Errorf("%T.settle_overbudget (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:settle_overbudget: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:isspam: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:spam_type: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ad_charge: %s", p, err)
	}
	return err
}

func (p *OwFullAct) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:media_charge: %s", p, err)
	}
	return err
}

func (p *OwFullAct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwFullAct(%+v)", *p)
}

type OwAdRequest struct {
	SdkUa          string        `thrift:"sdk_ua,1" json:"sdk_ua"`
	Sdk            string        `thrift:"sdk,2" json:"sdk"`
	Rt             string        `thrift:"rt,3" json:"rt"`
	Ts             string        `thrift:"ts,4" json:"ts"`
	Ipb            string        `thrift:"ipb,5" json:"ipb"`
	Idv            string        `thrift:"idv,6" json:"idv"`
	V              string        `thrift:"v,7" json:"v"`
	Sv             string        `thrift:"sv,8" json:"sv"`
	L              string        `thrift:"l,9" json:"l"`
	So             string        `thrift:"so,10" json:"so"`
	Sw             string        `thrift:"sw,11" json:"sw"`
	Sh             string        `thrift:"sh,12" json:"sh"`
	Sd             string        `thrift:"sd,13" json:"sd"`
	PbIdentifier   string        `thrift:"pb_identifier,14" json:"pb_identifier"`
	PbVersion      string        `thrift:"pb_version,15" json:"pb_version"`
	PbName         string        `thrift:"pb_name,16" json:"pb_name"`
	CoordTimestamp string        `thrift:"coord_timestamp,17" json:"coord_timestamp"`
	Coord          string        `thrift:"coord,18" json:"coord"`
	Network        string        `thrift:"network,19" json:"network"`
	Jb             JailBreakCode `thrift:"jb,20" json:"jb"`
	Ma             string        `thrift:"ma,21" json:"ma"`
	Oid            string        `thrift:"oid,22" json:"oid"`
	Dma            string        `thrift:"dma,23" json:"dma"`
	Odin1          string        `thrift:"odin1,24" json:"odin1"`
	Ifa            string        `thrift:"ifa,25" json:"ifa"`
	Lat            string        `thrift:"lat,26" json:"lat"`
	Ifv            string        `thrift:"ifv,27" json:"ifv"`
	UserId         string        `thrift:"userId,28" json:"userId"`
	C              string        `thrift:"c,29" json:"c"`
	Num            string        `thrift:"num,30" json:"num"`
	SearchId       LargeIdInt    `thrift:"searchId,31" json:"searchId"`
	Isv            int32         `thrift:"isv,32" json:"isv"`
	Uid            UidInt        `thrift:"uid,33" json:"uid"`
	Mid            MediaIdInt    `thrift:"mid,34" json:"mid"`
	DeviceCode     int32         `thrift:"deviceCode,35" json:"deviceCode"`
	RegionCode     int32         `thrift:"regionCode,36" json:"regionCode"`
	Imei           string        `thrift:"imei,37" json:"imei"`
	Uuid           string        `thrift:"uuid,38" json:"uuid"`
	OsCode         string        `thrift:"osCode,39" json:"osCode"`
	SdkPlatform    int32         `thrift:"sdkPlatform,40" json:"sdkPlatform"`
}

func NewOwAdRequest() *OwAdRequest {
	return &OwAdRequest{
		Jb: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAdRequest) IsSetJb() bool {
	return int64(p.Jb) != math.MinInt32-1
}

func (p *OwAdRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SdkUa = v
	}
	return nil
}

func (p *OwAdRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sdk = v
	}
	return nil
}

func (p *OwAdRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rt = v
	}
	return nil
}

func (p *OwAdRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Ts = v
	}
	return nil
}

func (p *OwAdRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *OwAdRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *OwAdRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.V = v
	}
	return nil
}

func (p *OwAdRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *OwAdRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.L = v
	}
	return nil
}

func (p *OwAdRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.So = v
	}
	return nil
}

func (p *OwAdRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Sw = v
	}
	return nil
}

func (p *OwAdRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sh = v
	}
	return nil
}

func (p *OwAdRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Sd = v
	}
	return nil
}

func (p *OwAdRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PbIdentifier = v
	}
	return nil
}

func (p *OwAdRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PbVersion = v
	}
	return nil
}

func (p *OwAdRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.PbName = v
	}
	return nil
}

func (p *OwAdRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.CoordTimestamp = v
	}
	return nil
}

func (p *OwAdRequest) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Coord = v
	}
	return nil
}

func (p *OwAdRequest) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *OwAdRequest) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Jb = JailBreakCode(v)
	}
	return nil
}

func (p *OwAdRequest) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Ma = v
	}
	return nil
}

func (p *OwAdRequest) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwAdRequest) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Dma = v
	}
	return nil
}

func (p *OwAdRequest) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwAdRequest) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Ifa = v
	}
	return nil
}

func (p *OwAdRequest) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Lat = v
	}
	return nil
}

func (p *OwAdRequest) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Ifv = v
	}
	return nil
}

func (p *OwAdRequest) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *OwAdRequest) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.C = v
	}
	return nil
}

func (p *OwAdRequest) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Num = v
	}
	return nil
}

func (p *OwAdRequest) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *OwAdRequest) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Isv = v
	}
	return nil
}

func (p *OwAdRequest) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *OwAdRequest) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *OwAdRequest) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *OwAdRequest) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *OwAdRequest) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *OwAdRequest) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *OwAdRequest) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *OwAdRequest) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.SdkPlatform = v
	}
	return nil
}

func (p *OwAdRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_ua", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sdk_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkUa)); err != nil {
		return fmt.Errorf("%T.sdk_ua (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sdk_ua: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sdk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sdk)); err != nil {
		return fmt.Errorf("%T.sdk (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sdk: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rt", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Rt)); err != nil {
		return fmt.Errorf("%T.rt (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rt: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ts", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ts)); err != nil {
		return fmt.Errorf("%T.ts (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ts: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ipb: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:idv: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("v", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:v: %s", p, err)
	}
	if err := oprot.WriteString(string(p.V)); err != nil {
		return fmt.Errorf("%T.v (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:v: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sv: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("l", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:l: %s", p, err)
	}
	if err := oprot.WriteString(string(p.L)); err != nil {
		return fmt.Errorf("%T.l (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:l: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("so", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:so: %s", p, err)
	}
	if err := oprot.WriteString(string(p.So)); err != nil {
		return fmt.Errorf("%T.so (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:so: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sw", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sw: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sw)); err != nil {
		return fmt.Errorf("%T.sw (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sw: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sh", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sh: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sh)); err != nil {
		return fmt.Errorf("%T.sh (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sh: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:sd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sd)); err != nil {
		return fmt.Errorf("%T.sd (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:sd: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_identifier", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:pb_identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbIdentifier)); err != nil {
		return fmt.Errorf("%T.pb_identifier (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:pb_identifier: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_version", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:pb_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbVersion)); err != nil {
		return fmt.Errorf("%T.pb_version (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:pb_version: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_name", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:pb_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbName)); err != nil {
		return fmt.Errorf("%T.pb_name (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:pb_name: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_timestamp", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:coord_timestamp: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordTimestamp)); err != nil {
		return fmt.Errorf("%T.coord_timestamp (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:coord_timestamp: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:coord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Coord)); err != nil {
		return fmt.Errorf("%T.coord (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:coord: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:network: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jb", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:jb: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Jb)); err != nil {
		return fmt.Errorf("%T.jb (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:jb: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ma", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:ma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ma)); err != nil {
		return fmt.Errorf("%T.ma (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:ma: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:oid: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dma", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:dma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dma)); err != nil {
		return fmt.Errorf("%T.dma (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:dma: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:odin1: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ifa", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:ifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ifa)); err != nil {
		return fmt.Errorf("%T.ifa (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:ifa: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lat", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:lat: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Lat)); err != nil {
		return fmt.Errorf("%T.lat (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:lat: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ifv", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:ifv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ifv)); err != nil {
		return fmt.Errorf("%T.ifv (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:ifv: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:userId: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("c", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:c: %s", p, err)
	}
	if err := oprot.WriteString(string(p.C)); err != nil {
		return fmt.Errorf("%T.c (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:c: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("num", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Num)); err != nil {
		return fmt.Errorf("%T.num (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:num: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:searchId: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isv", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:isv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Isv)); err != nil {
		return fmt.Errorf("%T.isv (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:isv: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:uid: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:mid: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceCode", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:deviceCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.deviceCode (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:deviceCode: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regionCode", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:regionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.regionCode (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:regionCode: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:imei: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:uuid: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("osCode", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:osCode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsCode)); err != nil {
		return fmt.Errorf("%T.osCode (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:osCode: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdkPlatform", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:sdkPlatform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkPlatform)); err != nil {
		return fmt.Errorf("%T.sdkPlatform (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:sdkPlatform: %s", p, err)
	}
	return err
}

func (p *OwAdRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdRequest(%+v)", *p)
}

type OwAd struct {
	AppId         string            `thrift:"appId,1" json:"appId"`
	Icon          ImgIdInt          `thrift:"icon,2" json:"icon"`
	Text          []string          `thrift:"text,3" json:"text"`
	Developer     string            `thrift:"developer,4" json:"developer"`
	Name          string            `thrift:"name,5" json:"name"`
	Version       string            `thrift:"version,6" json:"version"`
	Detail        string            `thrift:"detail,7" json:"detail"`
	ITunesId      string            `thrift:"iTunesId,8" json:"iTunesId"`
	Size          string            `thrift:"size,9" json:"size"`
	Point         LargeIdInt        `thrift:"point,10" json:"point"`
	TrackerId     string            `thrift:"trackerId,11" json:"trackerId"`
	AdStatus      OwAdStatus        `thrift:"adStatus,12" json:"adStatus"`
	Identifier    string            `thrift:"identifier,13" json:"identifier"`
	FileUrl       string            `thrift:"fileUrl,14" json:"fileUrl"`
	ActionStr     string            `thrift:"actionStr,15" json:"actionStr"`
	Screenshot    []ImgIdInt        `thrift:"screenshot,16" json:"screenshot"`
	Description   string            `thrift:"description,17" json:"description"`
	AdTag         int32             `thrift:"adTag,18" json:"adTag"`
	OpenDetail    bool              `thrift:"openDetail,19" json:"openDetail"`
	OfferType     int32             `thrift:"offerType,20" json:"offerType"`
	UrlActionType OwAdUrlActionType `thrift:"urlActionType,21" json:"urlActionType"`
	LaunchPrompt  string            `thrift:"launchPrompt,22" json:"launchPrompt"`
	Action        int32             `thrift:"action,23" json:"action"`
}

func NewOwAd() *OwAd {
	return &OwAd{
		AdStatus: math.MinInt32 - 1, // unset sentinal value

		UrlActionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAd) IsSetAdStatus() bool {
	return int64(p.AdStatus) != math.MinInt32-1
}

func (p *OwAd) IsSetUrlActionType() bool {
	return int64(p.UrlActionType) != math.MinInt32-1
}

func (p *OwAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *OwAd) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Icon = ImgIdInt(v)
	}
	return nil
}

func (p *OwAd) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Text = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Text = append(p.Text, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAd) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Developer = v
	}
	return nil
}

func (p *OwAd) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *OwAd) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *OwAd) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *OwAd) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ITunesId = v
	}
	return nil
}

func (p *OwAd) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *OwAd) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Point = LargeIdInt(v)
	}
	return nil
}

func (p *OwAd) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TrackerId = v
	}
	return nil
}

func (p *OwAd) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AdStatus = OwAdStatus(v)
	}
	return nil
}

func (p *OwAd) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Identifier = v
	}
	return nil
}

func (p *OwAd) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.FileUrl = v
	}
	return nil
}

func (p *OwAd) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ActionStr = v
	}
	return nil
}

func (p *OwAd) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screenshot = make([]ImgIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 ImgIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = ImgIdInt(v)
		}
		p.Screenshot = append(p.Screenshot, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAd) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *OwAd) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.AdTag = v
	}
	return nil
}

func (p *OwAd) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.OpenDetail = v
	}
	return nil
}

func (p *OwAd) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwAd) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.UrlActionType = OwAdUrlActionType(v)
	}
	return nil
}

func (p *OwAd) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.LaunchPrompt = v
	}
	return nil
}

func (p *OwAd) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *OwAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appId: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:icon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Icon)); err != nil {
		return fmt.Errorf("%T.icon (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:icon: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Text != nil {
		if err := oprot.WriteFieldBegin("text", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:text: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Text)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Text {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:text: %s", p, err)
		}
	}
	return err
}

func (p *OwAd) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("developer", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:developer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Developer)); err != nil {
		return fmt.Errorf("%T.developer (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:developer: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:version: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:detail: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iTunesId", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:iTunesId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ITunesId)); err != nil {
		return fmt.Errorf("%T.iTunesId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:iTunesId: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:size: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Size)); err != nil {
		return fmt.Errorf("%T.size (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:size: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:point: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackerId", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:trackerId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackerId)); err != nil {
		return fmt.Errorf("%T.trackerId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:trackerId: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdStatus() {
		if err := oprot.WriteFieldBegin("adStatus", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:adStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdStatus)); err != nil {
			return fmt.Errorf("%T.adStatus (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:adStatus: %s", p, err)
		}
	}
	return err
}

func (p *OwAd) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("identifier", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Identifier)); err != nil {
		return fmt.Errorf("%T.identifier (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:identifier: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileUrl", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:fileUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileUrl)); err != nil {
		return fmt.Errorf("%T.fileUrl (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:fileUrl: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionStr", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:actionStr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionStr)); err != nil {
		return fmt.Errorf("%T.actionStr (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:actionStr: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Screenshot != nil {
		if err := oprot.WriteFieldBegin("screenshot", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:screenshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Screenshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screenshot {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:screenshot: %s", p, err)
		}
	}
	return err
}

func (p *OwAd) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:description: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adTag", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:adTag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdTag)); err != nil {
		return fmt.Errorf("%T.adTag (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:adTag: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openDetail", thrift.BOOL, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:openDetail: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.OpenDetail)); err != nil {
		return fmt.Errorf("%T.openDetail (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:openDetail: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offerType", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:offerType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offerType (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:offerType: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetUrlActionType() {
		if err := oprot.WriteFieldBegin("urlActionType", thrift.I32, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:urlActionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UrlActionType)); err != nil {
			return fmt.Errorf("%T.urlActionType (21) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:urlActionType: %s", p, err)
		}
	}
	return err
}

func (p *OwAd) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("launchPrompt", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:launchPrompt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LaunchPrompt)); err != nil {
		return fmt.Errorf("%T.launchPrompt (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:launchPrompt: %s", p, err)
	}
	return err
}

func (p *OwAd) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:action: %s", p, err)
	}
	return err
}

func (p *OwAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAd(%+v)", *p)
}

type OwAdResponse struct {
	Status     int32                  `thrift:"status,1" json:"status"`
	PointAds   map[OwAdStatus][]*OwAd `thrift:"pointAds,2" json:"pointAds"`
	NoPointAds map[OwAdStatus][]*OwAd `thrift:"noPointAds,3" json:"noPointAds"`
	SearchId   LargeIdInt             `thrift:"searchId,4" json:"searchId"`
	UnitName   string                 `thrift:"unitName,5" json:"unitName"`
}

func NewOwAdResponse() *OwAdResponse {
	return &OwAdResponse{}
}

func (p *OwAdResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwAdResponse) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.PointAds = make(map[OwAdStatus][]*OwAd, size)
	for i := 0; i < size; i++ {
		var _key2 OwAdStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = OwAdStatus(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val3 := make([]*OwAd, 0, size)
		for i := 0; i < size; i++ {
			_elem4 := NewOwAd()
			if err := _elem4.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem4)
			}
			_val3 = append(_val3, _elem4)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.PointAds[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *OwAdResponse) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.NoPointAds = make(map[OwAdStatus][]*OwAd, size)
	for i := 0; i < size; i++ {
		var _key5 OwAdStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key5 = OwAdStatus(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val6 := make([]*OwAd, 0, size)
		for i := 0; i < size; i++ {
			_elem7 := NewOwAd()
			if err := _elem7.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem7)
			}
			_val6 = append(_val6, _elem7)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.NoPointAds[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *OwAdResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *OwAdResponse) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UnitName = v
	}
	return nil
}

func (p *OwAdResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *OwAdResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PointAds != nil {
		if err := oprot.WriteFieldBegin("pointAds", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:pointAds: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.PointAds)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.PointAds {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:pointAds: %s", p, err)
		}
	}
	return err
}

func (p *OwAdResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.NoPointAds != nil {
		if err := oprot.WriteFieldBegin("noPointAds", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:noPointAds: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.NoPointAds)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.NoPointAds {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:noPointAds: %s", p, err)
		}
	}
	return err
}

func (p *OwAdResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:searchId: %s", p, err)
	}
	return err
}

func (p *OwAdResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unitName", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:unitName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UnitName)); err != nil {
		return fmt.Errorf("%T.unitName (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:unitName: %s", p, err)
	}
	return err
}

func (p *OwAdResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdResponse(%+v)", *p)
}
