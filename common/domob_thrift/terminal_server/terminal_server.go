// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package terminal_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/airport_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/p2p_server"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = airport_types.GoUnusedProtection__
var _ = p2p_server.GoUnusedProtection__

type TerminalServer interface {
	p2p_server.P2pServer

	// 添加文件，供其它terminal server拉取
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	AddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 拉取文件
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	GetFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 获取已收到的文件块编号
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - NeedBlocks: 我想要的数据块，约定如果为空，则要全部
	DoYouHaveBlocks(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, needBlocks []int32) (r *airport_types.ResultI32List, err error)
	// 获取已收到的随机文件块编号
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - BlockNum: 数据块数量
	WhatBlocksDoYouHave(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, blockNum int32) (r *airport_types.ResultI32List, err error)
	// 获取正在执行的任务
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	GetReceivingTopic(requestHeader *common.RequestHeader) (r *airport_types.ResultTopicInfoList, err error)
	// 获取已经执行完的任务
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	GetFinishedTopic(requestHeader *common.RequestHeader) (r *airport_types.ResultTopicInfoList, err error)
	// 是否在完成列表中
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	IsInFinishedTopicList(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 是否在下载列表中
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	IsInDownloadingTopicList(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 删除文件，如果正在下载，则停止下载删除
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	DeleteFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
}

type TerminalServerClient struct {
	*p2p_server.P2pServerClient
}

func NewTerminalServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *TerminalServerClient {
	return &TerminalServerClient{P2pServerClient: p2p_server.NewP2pServerClientFactory(t, f)}
}

func NewTerminalServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *TerminalServerClient {
	return &TerminalServerClient{P2pServerClient: p2p_server.NewP2pServerClientProtocol(t, iprot, oprot)}
}

// 添加文件，供其它terminal server拉取
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *TerminalServerClient) AddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendAddFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvAddFile()
}

func (p *TerminalServerClient) sendAddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddFileArgs()
	args0.RequestHeader = requestHeader
	args0.TopicInfo = topicInfo
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvAddFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddFileResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 拉取文件
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *TerminalServerClient) GetFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendGetFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvGetFile()
}

func (p *TerminalServerClient) sendGetFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetFileArgs()
	args4.RequestHeader = requestHeader
	args4.TopicInfo = topicInfo
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvGetFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetFileResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// 获取已收到的文件块编号
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - NeedBlocks: 我想要的数据块，约定如果为空，则要全部
func (p *TerminalServerClient) DoYouHaveBlocks(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, needBlocks []int32) (r *airport_types.ResultI32List, err error) {
	if err = p.sendDoYouHaveBlocks(requestHeader, topicInfo, needBlocks); err != nil {
		return
	}
	return p.recvDoYouHaveBlocks()
}

func (p *TerminalServerClient) sendDoYouHaveBlocks(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, needBlocks []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("doYouHaveBlocks", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewDoYouHaveBlocksArgs()
	args8.RequestHeader = requestHeader
	args8.TopicInfo = topicInfo
	args8.NeedBlocks = needBlocks
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvDoYouHaveBlocks() (value *airport_types.ResultI32List, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewDoYouHaveBlocksResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 获取已收到的随机文件块编号
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - BlockNum: 数据块数量
func (p *TerminalServerClient) WhatBlocksDoYouHave(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, blockNum int32) (r *airport_types.ResultI32List, err error) {
	if err = p.sendWhatBlocksDoYouHave(requestHeader, topicInfo, blockNum); err != nil {
		return
	}
	return p.recvWhatBlocksDoYouHave()
}

func (p *TerminalServerClient) sendWhatBlocksDoYouHave(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, blockNum int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("whatBlocksDoYouHave", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewWhatBlocksDoYouHaveArgs()
	args12.RequestHeader = requestHeader
	args12.TopicInfo = topicInfo
	args12.BlockNum = blockNum
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvWhatBlocksDoYouHave() (value *airport_types.ResultI32List, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewWhatBlocksDoYouHaveResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 获取正在执行的任务
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
func (p *TerminalServerClient) GetReceivingTopic(requestHeader *common.RequestHeader) (r *airport_types.ResultTopicInfoList, err error) {
	if err = p.sendGetReceivingTopic(requestHeader); err != nil {
		return
	}
	return p.recvGetReceivingTopic()
}

func (p *TerminalServerClient) sendGetReceivingTopic(requestHeader *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getReceivingTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetReceivingTopicArgs()
	args16.RequestHeader = requestHeader
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvGetReceivingTopic() (value *airport_types.ResultTopicInfoList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetReceivingTopicResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 获取已经执行完的任务
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
func (p *TerminalServerClient) GetFinishedTopic(requestHeader *common.RequestHeader) (r *airport_types.ResultTopicInfoList, err error) {
	if err = p.sendGetFinishedTopic(requestHeader); err != nil {
		return
	}
	return p.recvGetFinishedTopic()
}

func (p *TerminalServerClient) sendGetFinishedTopic(requestHeader *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFinishedTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetFinishedTopicArgs()
	args20.RequestHeader = requestHeader
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvGetFinishedTopic() (value *airport_types.ResultTopicInfoList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetFinishedTopicResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 是否在完成列表中
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *TerminalServerClient) IsInFinishedTopicList(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendIsInFinishedTopicList(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvIsInFinishedTopicList()
}

func (p *TerminalServerClient) sendIsInFinishedTopicList(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("isInFinishedTopicList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewIsInFinishedTopicListArgs()
	args24.RequestHeader = requestHeader
	args24.TopicInfo = topicInfo
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvIsInFinishedTopicList() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewIsInFinishedTopicListResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

// 是否在下载列表中
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *TerminalServerClient) IsInDownloadingTopicList(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendIsInDownloadingTopicList(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvIsInDownloadingTopicList()
}

func (p *TerminalServerClient) sendIsInDownloadingTopicList(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("isInDownloadingTopicList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewIsInDownloadingTopicListArgs()
	args28.RequestHeader = requestHeader
	args28.TopicInfo = topicInfo
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvIsInDownloadingTopicList() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewIsInDownloadingTopicListResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	return
}

// 删除文件，如果正在下载，则停止下载删除
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *TerminalServerClient) DeleteFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendDeleteFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvDeleteFile()
}

func (p *TerminalServerClient) sendDeleteFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewDeleteFileArgs()
	args32.RequestHeader = requestHeader
	args32.TopicInfo = topicInfo
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TerminalServerClient) recvDeleteFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewDeleteFileResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	return
}

type TerminalServerProcessor struct {
	*p2p_server.P2pServerProcessor
}

func NewTerminalServerProcessor(handler TerminalServer) *TerminalServerProcessor {
	self36 := &TerminalServerProcessor{p2p_server.NewP2pServerProcessor(handler)}
	self36.AddToProcessorMap("addFile", &terminalServerProcessorAddFile{handler: handler})
	self36.AddToProcessorMap("getFile", &terminalServerProcessorGetFile{handler: handler})
	self36.AddToProcessorMap("doYouHaveBlocks", &terminalServerProcessorDoYouHaveBlocks{handler: handler})
	self36.AddToProcessorMap("whatBlocksDoYouHave", &terminalServerProcessorWhatBlocksDoYouHave{handler: handler})
	self36.AddToProcessorMap("getReceivingTopic", &terminalServerProcessorGetReceivingTopic{handler: handler})
	self36.AddToProcessorMap("getFinishedTopic", &terminalServerProcessorGetFinishedTopic{handler: handler})
	self36.AddToProcessorMap("isInFinishedTopicList", &terminalServerProcessorIsInFinishedTopicList{handler: handler})
	self36.AddToProcessorMap("isInDownloadingTopicList", &terminalServerProcessorIsInDownloadingTopicList{handler: handler})
	self36.AddToProcessorMap("deleteFile", &terminalServerProcessorDeleteFile{handler: handler})
	return self36
}

type terminalServerProcessorAddFile struct {
	handler TerminalServer
}

func (p *terminalServerProcessorAddFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFileResult()
	if result.Success, err = p.handler.AddFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addFile: "+err.Error())
		oprot.WriteMessageBegin("addFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorGetFile struct {
	handler TerminalServer
}

func (p *terminalServerProcessorGetFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFileResult()
	if result.Success, err = p.handler.GetFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFile: "+err.Error())
		oprot.WriteMessageBegin("getFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorDoYouHaveBlocks struct {
	handler TerminalServer
}

func (p *terminalServerProcessorDoYouHaveBlocks) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDoYouHaveBlocksArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("doYouHaveBlocks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDoYouHaveBlocksResult()
	if result.Success, err = p.handler.DoYouHaveBlocks(args.RequestHeader, args.TopicInfo, args.NeedBlocks); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing doYouHaveBlocks: "+err.Error())
		oprot.WriteMessageBegin("doYouHaveBlocks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("doYouHaveBlocks", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorWhatBlocksDoYouHave struct {
	handler TerminalServer
}

func (p *terminalServerProcessorWhatBlocksDoYouHave) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewWhatBlocksDoYouHaveArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("whatBlocksDoYouHave", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewWhatBlocksDoYouHaveResult()
	if result.Success, err = p.handler.WhatBlocksDoYouHave(args.RequestHeader, args.TopicInfo, args.BlockNum); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing whatBlocksDoYouHave: "+err.Error())
		oprot.WriteMessageBegin("whatBlocksDoYouHave", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("whatBlocksDoYouHave", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorGetReceivingTopic struct {
	handler TerminalServer
}

func (p *terminalServerProcessorGetReceivingTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetReceivingTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getReceivingTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetReceivingTopicResult()
	if result.Success, err = p.handler.GetReceivingTopic(args.RequestHeader); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getReceivingTopic: "+err.Error())
		oprot.WriteMessageBegin("getReceivingTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getReceivingTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorGetFinishedTopic struct {
	handler TerminalServer
}

func (p *terminalServerProcessorGetFinishedTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFinishedTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFinishedTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFinishedTopicResult()
	if result.Success, err = p.handler.GetFinishedTopic(args.RequestHeader); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFinishedTopic: "+err.Error())
		oprot.WriteMessageBegin("getFinishedTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFinishedTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorIsInFinishedTopicList struct {
	handler TerminalServer
}

func (p *terminalServerProcessorIsInFinishedTopicList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewIsInFinishedTopicListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("isInFinishedTopicList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewIsInFinishedTopicListResult()
	if result.Success, err = p.handler.IsInFinishedTopicList(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isInFinishedTopicList: "+err.Error())
		oprot.WriteMessageBegin("isInFinishedTopicList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("isInFinishedTopicList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorIsInDownloadingTopicList struct {
	handler TerminalServer
}

func (p *terminalServerProcessorIsInDownloadingTopicList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewIsInDownloadingTopicListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("isInDownloadingTopicList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewIsInDownloadingTopicListResult()
	if result.Success, err = p.handler.IsInDownloadingTopicList(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isInDownloadingTopicList: "+err.Error())
		oprot.WriteMessageBegin("isInDownloadingTopicList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("isInDownloadingTopicList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type terminalServerProcessorDeleteFile struct {
	handler TerminalServer
}

func (p *terminalServerProcessorDeleteFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteFileResult()
	if result.Success, err = p.handler.DeleteFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteFile: "+err.Error())
		oprot.WriteMessageBegin("deleteFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewAddFileArgs() *AddFileArgs {
	return &AddFileArgs{}
}

func (p *AddFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *AddFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *AddFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *AddFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *AddFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFileArgs(%+v)", *p)
}

type AddFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewAddFileResult() *AddFileResult {
	return &AddFileResult{}
}

func (p *AddFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFileResult(%+v)", *p)
}

type GetFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewGetFileArgs() *GetFileArgs {
	return &GetFileArgs{}
}

func (p *GetFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *GetFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileArgs(%+v)", *p)
}

type GetFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewGetFileResult() *GetFileResult {
	return &GetFileResult{}
}

func (p *GetFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileResult(%+v)", *p)
}

type DoYouHaveBlocksArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	NeedBlocks    []int32                  `thrift:"needBlocks,3" json:"needBlocks"`
}

func NewDoYouHaveBlocksArgs() *DoYouHaveBlocksArgs {
	return &DoYouHaveBlocksArgs{}
}

func (p *DoYouHaveBlocksArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DoYouHaveBlocksArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *DoYouHaveBlocksArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *DoYouHaveBlocksArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.NeedBlocks = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem37 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem37 = v
		}
		p.NeedBlocks = append(p.NeedBlocks, _elem37)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DoYouHaveBlocksArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("doYouHaveBlocks_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DoYouHaveBlocksArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *DoYouHaveBlocksArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *DoYouHaveBlocksArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.NeedBlocks != nil {
		if err := oprot.WriteFieldBegin("needBlocks", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:needBlocks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.NeedBlocks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.NeedBlocks {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:needBlocks: %s", p, err)
		}
	}
	return err
}

func (p *DoYouHaveBlocksArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DoYouHaveBlocksArgs(%+v)", *p)
}

type DoYouHaveBlocksResult struct {
	Success *airport_types.ResultI32List `thrift:"success,0" json:"success"`
}

func NewDoYouHaveBlocksResult() *DoYouHaveBlocksResult {
	return &DoYouHaveBlocksResult{}
}

func (p *DoYouHaveBlocksResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DoYouHaveBlocksResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultI32List()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DoYouHaveBlocksResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("doYouHaveBlocks_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DoYouHaveBlocksResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DoYouHaveBlocksResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DoYouHaveBlocksResult(%+v)", *p)
}

type WhatBlocksDoYouHaveArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	BlockNum      int32                    `thrift:"blockNum,3" json:"blockNum"`
}

func NewWhatBlocksDoYouHaveArgs() *WhatBlocksDoYouHaveArgs {
	return &WhatBlocksDoYouHaveArgs{}
}

func (p *WhatBlocksDoYouHaveArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.BlockNum = v
	}
	return nil
}

func (p *WhatBlocksDoYouHaveArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("whatBlocksDoYouHave_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *WhatBlocksDoYouHaveArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *WhatBlocksDoYouHaveArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("blockNum", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:blockNum: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BlockNum)); err != nil {
		return fmt.Errorf("%T.blockNum (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:blockNum: %s", p, err)
	}
	return err
}

func (p *WhatBlocksDoYouHaveArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WhatBlocksDoYouHaveArgs(%+v)", *p)
}

type WhatBlocksDoYouHaveResult struct {
	Success *airport_types.ResultI32List `thrift:"success,0" json:"success"`
}

func NewWhatBlocksDoYouHaveResult() *WhatBlocksDoYouHaveResult {
	return &WhatBlocksDoYouHaveResult{}
}

func (p *WhatBlocksDoYouHaveResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultI32List()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("whatBlocksDoYouHave_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WhatBlocksDoYouHaveResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *WhatBlocksDoYouHaveResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WhatBlocksDoYouHaveResult(%+v)", *p)
}

type GetReceivingTopicArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
}

func NewGetReceivingTopicArgs() *GetReceivingTopicArgs {
	return &GetReceivingTopicArgs{}
}

func (p *GetReceivingTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetReceivingTopicArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetReceivingTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getReceivingTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetReceivingTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetReceivingTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetReceivingTopicArgs(%+v)", *p)
}

type GetReceivingTopicResult struct {
	Success *airport_types.ResultTopicInfoList `thrift:"success,0" json:"success"`
}

func NewGetReceivingTopicResult() *GetReceivingTopicResult {
	return &GetReceivingTopicResult{}
}

func (p *GetReceivingTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetReceivingTopicResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultTopicInfoList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetReceivingTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getReceivingTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetReceivingTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetReceivingTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetReceivingTopicResult(%+v)", *p)
}

type GetFinishedTopicArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
}

func NewGetFinishedTopicArgs() *GetFinishedTopicArgs {
	return &GetFinishedTopicArgs{}
}

func (p *GetFinishedTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedTopicArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFinishedTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinishedTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFinishedTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinishedTopicArgs(%+v)", *p)
}

type GetFinishedTopicResult struct {
	Success *airport_types.ResultTopicInfoList `thrift:"success,0" json:"success"`
}

func NewGetFinishedTopicResult() *GetFinishedTopicResult {
	return &GetFinishedTopicResult{}
}

func (p *GetFinishedTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedTopicResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultTopicInfoList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFinishedTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinishedTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFinishedTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinishedTopicResult(%+v)", *p)
}

type IsInFinishedTopicListArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewIsInFinishedTopicListArgs() *IsInFinishedTopicListArgs {
	return &IsInFinishedTopicListArgs{}
}

func (p *IsInFinishedTopicListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsInFinishedTopicListArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *IsInFinishedTopicListArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *IsInFinishedTopicListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isInFinishedTopicList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsInFinishedTopicListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *IsInFinishedTopicListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *IsInFinishedTopicListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsInFinishedTopicListArgs(%+v)", *p)
}

type IsInFinishedTopicListResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewIsInFinishedTopicListResult() *IsInFinishedTopicListResult {
	return &IsInFinishedTopicListResult{}
}

func (p *IsInFinishedTopicListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsInFinishedTopicListResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *IsInFinishedTopicListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isInFinishedTopicList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsInFinishedTopicListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *IsInFinishedTopicListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsInFinishedTopicListResult(%+v)", *p)
}

type IsInDownloadingTopicListArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewIsInDownloadingTopicListArgs() *IsInDownloadingTopicListArgs {
	return &IsInDownloadingTopicListArgs{}
}

func (p *IsInDownloadingTopicListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsInDownloadingTopicListArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *IsInDownloadingTopicListArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *IsInDownloadingTopicListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isInDownloadingTopicList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsInDownloadingTopicListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *IsInDownloadingTopicListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *IsInDownloadingTopicListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsInDownloadingTopicListArgs(%+v)", *p)
}

type IsInDownloadingTopicListResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewIsInDownloadingTopicListResult() *IsInDownloadingTopicListResult {
	return &IsInDownloadingTopicListResult{}
}

func (p *IsInDownloadingTopicListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsInDownloadingTopicListResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *IsInDownloadingTopicListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isInDownloadingTopicList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsInDownloadingTopicListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *IsInDownloadingTopicListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsInDownloadingTopicListResult(%+v)", *p)
}

type DeleteFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewDeleteFileArgs() *DeleteFileArgs {
	return &DeleteFileArgs{}
}

func (p *DeleteFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *DeleteFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *DeleteFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *DeleteFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *DeleteFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFileArgs(%+v)", *p)
}

type DeleteFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewDeleteFileResult() *DeleteFileResult {
	return &DeleteFileResult{}
}

func (p *DeleteFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DeleteFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DeleteFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFileResult(%+v)", *p)
}
