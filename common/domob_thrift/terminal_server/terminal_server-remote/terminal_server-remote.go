// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"terminal_server"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ResultBool addFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool getFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.<PERSON>derr, "  ResultI32List doYouHaveBlocks(RequestHeader requestHeader, TopicInfo topicInfo,  needBlocks)")
	fmt.Fprintln(os.Stderr, "  ResultI32List whatBlocksDoYouHave(RequestHeader requestHeader, TopicInfo topicInfo, i32 blockNum)")
	fmt.Fprintln(os.Stderr, "  ResultTopicInfoList getReceivingTopic(RequestHeader requestHeader)")
	fmt.Fprintln(os.Stderr, "  ResultTopicInfoList getFinishedTopic(RequestHeader requestHeader)")
	fmt.Fprintln(os.Stderr, "  ResultBool isInFinishedTopicList(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool isInDownloadingTopicList(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool deleteFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultI32List getFileFinishedBlockIndex(RequestHeader requestHeader, TopicInfo topicInfo,  needBlocks)")
	fmt.Fprintln(os.Stderr, "  ResultData getFileData(RequestHeader requestHeader, TopicInfo topicInfo, i32 blockIndex)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := terminal_server.NewTerminalServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddFile requires 2 args")
			flag.Usage()
		}
		arg38 := flag.Arg(1)
		mbTrans39 := thrift.NewTMemoryBufferLen(len(arg38))
		defer mbTrans39.Close()
		_, err40 := mbTrans39.WriteString(arg38)
		if err40 != nil {
			Usage()
			return
		}
		factory41 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt42 := factory41.GetProtocol(mbTrans39)
		argvalue0 := terminal_server.NewRequestHeader()
		err43 := argvalue0.Read(jsProt42)
		if err43 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg44 := flag.Arg(2)
		mbTrans45 := thrift.NewTMemoryBufferLen(len(arg44))
		defer mbTrans45.Close()
		_, err46 := mbTrans45.WriteString(arg44)
		if err46 != nil {
			Usage()
			return
		}
		factory47 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt48 := factory47.GetProtocol(mbTrans45)
		argvalue1 := terminal_server.NewTopicInfo()
		err49 := argvalue1.Read(jsProt48)
		if err49 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddFile(value0, value1))
		fmt.Print("\n")
		break
	case "getFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFile requires 2 args")
			flag.Usage()
		}
		arg50 := flag.Arg(1)
		mbTrans51 := thrift.NewTMemoryBufferLen(len(arg50))
		defer mbTrans51.Close()
		_, err52 := mbTrans51.WriteString(arg50)
		if err52 != nil {
			Usage()
			return
		}
		factory53 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt54 := factory53.GetProtocol(mbTrans51)
		argvalue0 := terminal_server.NewRequestHeader()
		err55 := argvalue0.Read(jsProt54)
		if err55 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg56 := flag.Arg(2)
		mbTrans57 := thrift.NewTMemoryBufferLen(len(arg56))
		defer mbTrans57.Close()
		_, err58 := mbTrans57.WriteString(arg56)
		if err58 != nil {
			Usage()
			return
		}
		factory59 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt60 := factory59.GetProtocol(mbTrans57)
		argvalue1 := terminal_server.NewTopicInfo()
		err61 := argvalue1.Read(jsProt60)
		if err61 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFile(value0, value1))
		fmt.Print("\n")
		break
	case "doYouHaveBlocks":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DoYouHaveBlocks requires 3 args")
			flag.Usage()
		}
		arg62 := flag.Arg(1)
		mbTrans63 := thrift.NewTMemoryBufferLen(len(arg62))
		defer mbTrans63.Close()
		_, err64 := mbTrans63.WriteString(arg62)
		if err64 != nil {
			Usage()
			return
		}
		factory65 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt66 := factory65.GetProtocol(mbTrans63)
		argvalue0 := terminal_server.NewRequestHeader()
		err67 := argvalue0.Read(jsProt66)
		if err67 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg68 := flag.Arg(2)
		mbTrans69 := thrift.NewTMemoryBufferLen(len(arg68))
		defer mbTrans69.Close()
		_, err70 := mbTrans69.WriteString(arg68)
		if err70 != nil {
			Usage()
			return
		}
		factory71 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt72 := factory71.GetProtocol(mbTrans69)
		argvalue1 := terminal_server.NewTopicInfo()
		err73 := argvalue1.Read(jsProt72)
		if err73 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg74 := flag.Arg(3)
		mbTrans75 := thrift.NewTMemoryBufferLen(len(arg74))
		defer mbTrans75.Close()
		_, err76 := mbTrans75.WriteString(arg74)
		if err76 != nil {
			Usage()
			return
		}
		factory77 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt78 := factory77.GetProtocol(mbTrans75)
		containerStruct2 := terminal_server.NewDoYouHaveBlocksArgs()
		err79 := containerStruct2.ReadField3(jsProt78)
		if err79 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.NeedBlocks
		value2 := argvalue2
		fmt.Print(client.DoYouHaveBlocks(value0, value1, value2))
		fmt.Print("\n")
		break
	case "whatBlocksDoYouHave":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "WhatBlocksDoYouHave requires 3 args")
			flag.Usage()
		}
		arg80 := flag.Arg(1)
		mbTrans81 := thrift.NewTMemoryBufferLen(len(arg80))
		defer mbTrans81.Close()
		_, err82 := mbTrans81.WriteString(arg80)
		if err82 != nil {
			Usage()
			return
		}
		factory83 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt84 := factory83.GetProtocol(mbTrans81)
		argvalue0 := terminal_server.NewRequestHeader()
		err85 := argvalue0.Read(jsProt84)
		if err85 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg86 := flag.Arg(2)
		mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
		defer mbTrans87.Close()
		_, err88 := mbTrans87.WriteString(arg86)
		if err88 != nil {
			Usage()
			return
		}
		factory89 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt90 := factory89.GetProtocol(mbTrans87)
		argvalue1 := terminal_server.NewTopicInfo()
		err91 := argvalue1.Read(jsProt90)
		if err91 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err92 := (strconv.Atoi(flag.Arg(3)))
		if err92 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.WhatBlocksDoYouHave(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getReceivingTopic":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetReceivingTopic requires 1 args")
			flag.Usage()
		}
		arg93 := flag.Arg(1)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue0 := terminal_server.NewRequestHeader()
		err98 := argvalue0.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetReceivingTopic(value0))
		fmt.Print("\n")
		break
	case "getFinishedTopic":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetFinishedTopic requires 1 args")
			flag.Usage()
		}
		arg99 := flag.Arg(1)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue0 := terminal_server.NewRequestHeader()
		err104 := argvalue0.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetFinishedTopic(value0))
		fmt.Print("\n")
		break
	case "isInFinishedTopicList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "IsInFinishedTopicList requires 2 args")
			flag.Usage()
		}
		arg105 := flag.Arg(1)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue0 := terminal_server.NewRequestHeader()
		err110 := argvalue0.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg111 := flag.Arg(2)
		mbTrans112 := thrift.NewTMemoryBufferLen(len(arg111))
		defer mbTrans112.Close()
		_, err113 := mbTrans112.WriteString(arg111)
		if err113 != nil {
			Usage()
			return
		}
		factory114 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt115 := factory114.GetProtocol(mbTrans112)
		argvalue1 := terminal_server.NewTopicInfo()
		err116 := argvalue1.Read(jsProt115)
		if err116 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.IsInFinishedTopicList(value0, value1))
		fmt.Print("\n")
		break
	case "isInDownloadingTopicList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "IsInDownloadingTopicList requires 2 args")
			flag.Usage()
		}
		arg117 := flag.Arg(1)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue0 := terminal_server.NewRequestHeader()
		err122 := argvalue0.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg123 := flag.Arg(2)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue1 := terminal_server.NewTopicInfo()
		err128 := argvalue1.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.IsInDownloadingTopicList(value0, value1))
		fmt.Print("\n")
		break
	case "deleteFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteFile requires 2 args")
			flag.Usage()
		}
		arg129 := flag.Arg(1)
		mbTrans130 := thrift.NewTMemoryBufferLen(len(arg129))
		defer mbTrans130.Close()
		_, err131 := mbTrans130.WriteString(arg129)
		if err131 != nil {
			Usage()
			return
		}
		factory132 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt133 := factory132.GetProtocol(mbTrans130)
		argvalue0 := terminal_server.NewRequestHeader()
		err134 := argvalue0.Read(jsProt133)
		if err134 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg135 := flag.Arg(2)
		mbTrans136 := thrift.NewTMemoryBufferLen(len(arg135))
		defer mbTrans136.Close()
		_, err137 := mbTrans136.WriteString(arg135)
		if err137 != nil {
			Usage()
			return
		}
		factory138 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt139 := factory138.GetProtocol(mbTrans136)
		argvalue1 := terminal_server.NewTopicInfo()
		err140 := argvalue1.Read(jsProt139)
		if err140 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DeleteFile(value0, value1))
		fmt.Print("\n")
		break
	case "getFileFinishedBlockIndex":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileFinishedBlockIndex requires 3 args")
			flag.Usage()
		}
		arg141 := flag.Arg(1)
		mbTrans142 := thrift.NewTMemoryBufferLen(len(arg141))
		defer mbTrans142.Close()
		_, err143 := mbTrans142.WriteString(arg141)
		if err143 != nil {
			Usage()
			return
		}
		factory144 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt145 := factory144.GetProtocol(mbTrans142)
		argvalue0 := terminal_server.NewRequestHeader()
		err146 := argvalue0.Read(jsProt145)
		if err146 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg147 := flag.Arg(2)
		mbTrans148 := thrift.NewTMemoryBufferLen(len(arg147))
		defer mbTrans148.Close()
		_, err149 := mbTrans148.WriteString(arg147)
		if err149 != nil {
			Usage()
			return
		}
		factory150 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt151 := factory150.GetProtocol(mbTrans148)
		argvalue1 := terminal_server.NewTopicInfo()
		err152 := argvalue1.Read(jsProt151)
		if err152 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg153 := flag.Arg(3)
		mbTrans154 := thrift.NewTMemoryBufferLen(len(arg153))
		defer mbTrans154.Close()
		_, err155 := mbTrans154.WriteString(arg153)
		if err155 != nil {
			Usage()
			return
		}
		factory156 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt157 := factory156.GetProtocol(mbTrans154)
		containerStruct2 := terminal_server.NewGetFileFinishedBlockIndexArgs()
		err158 := containerStruct2.ReadField3(jsProt157)
		if err158 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.NeedBlocks
		value2 := argvalue2
		fmt.Print(client.GetFileFinishedBlockIndex(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFileData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileData requires 3 args")
			flag.Usage()
		}
		arg159 := flag.Arg(1)
		mbTrans160 := thrift.NewTMemoryBufferLen(len(arg159))
		defer mbTrans160.Close()
		_, err161 := mbTrans160.WriteString(arg159)
		if err161 != nil {
			Usage()
			return
		}
		factory162 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt163 := factory162.GetProtocol(mbTrans160)
		argvalue0 := terminal_server.NewRequestHeader()
		err164 := argvalue0.Read(jsProt163)
		if err164 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg165 := flag.Arg(2)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		argvalue1 := terminal_server.NewTopicInfo()
		err170 := argvalue1.Read(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err171 := (strconv.Atoi(flag.Arg(3)))
		if err171 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetFileData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
