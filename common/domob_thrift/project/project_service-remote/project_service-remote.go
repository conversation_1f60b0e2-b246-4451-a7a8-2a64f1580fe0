// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"project"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  Project getProjectByPid(RequestHeader header, i32 pj_id)")
	fmt.Fprintln(os.Stderr, "  QueryResult getAllProjectsByAmIds(RequestHeader header,  amids, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.<PERSON><PERSON>, "  QueryResult getAllSchedulesByPid(RequestHeader header, i32 pj_id, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  Project getProjectBySid(RequestHeader header, i32 sd_id)")
	fmt.Fprintln(os.Stderr, "   getProjectsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getSchedulesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getAllSchedulesByCmid(RequestHeader header, i32 cm_id)")
	fmt.Fprintln(os.Stderr, "  QueryResult getAllSchedulesByCmidNew(RequestHeader header, i32 cm_id, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  QueryResult getSchedulesByParam(RequestHeader header, QueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool addPlanProjectRelation(RequestHeader header, i32 plan_id, i32 sd_id, i32 product_type, i32 task_flag, i32 supplement_flag)")
	fmt.Fprintln(os.Stderr, "  bool editOrder(RequestHeader header, i32 uid, i32 pj_id)")
	fmt.Fprintln(os.Stderr, "  bool editSchedule(RequestHeader header, i32 uid, i32 sd_id)")
	fmt.Fprintln(os.Stderr, "  bool assignProjectToAM(RequestHeader header, i32 pj_id, i32 am_id)")
	fmt.Fprintln(os.Stderr, "  bool assignScheduleToAM(RequestHeader header, i32 uid, i32 sd_id, i32 am_id)")
	fmt.Fprintln(os.Stderr, "  bool updatePlanProjectRelation(RequestHeader header, i32 plan_id, i32 sd_id, i32 product_type, i32 task_flag, i32 supplement_flag)")
	fmt.Fprintln(os.Stderr, "  Schedule getScheduleById(RequestHeader header, i32 sd_id)")
	fmt.Fprintln(os.Stderr, "  QueryResult getAllProjectsByDepIds(RequestHeader header,  depids, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := project.NewProjectServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getProjectByPid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProjectByPid requires 2 args")
			flag.Usage()
		}
		arg79 := flag.Arg(1)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue0 := project.NewRequestHeader()
		err84 := argvalue0.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err85 := (strconv.Atoi(flag.Arg(2)))
		if err85 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetProjectByPid(value0, value1))
		fmt.Print("\n")
		break
	case "getAllProjectsByAmIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAllProjectsByAmIds requires 5 args")
			flag.Usage()
		}
		arg86 := flag.Arg(1)
		mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
		defer mbTrans87.Close()
		_, err88 := mbTrans87.WriteString(arg86)
		if err88 != nil {
			Usage()
			return
		}
		factory89 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt90 := factory89.GetProtocol(mbTrans87)
		argvalue0 := project.NewRequestHeader()
		err91 := argvalue0.Read(jsProt90)
		if err91 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg92 := flag.Arg(2)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		containerStruct1 := project.NewGetAllProjectsByAmIdsArgs()
		err97 := containerStruct1.ReadField2(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Amids
		value1 := argvalue1
		tmp2, err98 := (strconv.Atoi(flag.Arg(3)))
		if err98 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err99 := (strconv.Atoi(flag.Arg(4)))
		if err99 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.GetAllProjectsByAmIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAllSchedulesByPid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAllSchedulesByPid requires 5 args")
			flag.Usage()
		}
		arg101 := flag.Arg(1)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		argvalue0 := project.NewRequestHeader()
		err106 := argvalue0.Read(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err107 := (strconv.Atoi(flag.Arg(2)))
		if err107 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err108 := (strconv.Atoi(flag.Arg(3)))
		if err108 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err109 := (strconv.Atoi(flag.Arg(4)))
		if err109 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.GetAllSchedulesByPid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getProjectBySid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProjectBySid requires 2 args")
			flag.Usage()
		}
		arg111 := flag.Arg(1)
		mbTrans112 := thrift.NewTMemoryBufferLen(len(arg111))
		defer mbTrans112.Close()
		_, err113 := mbTrans112.WriteString(arg111)
		if err113 != nil {
			Usage()
			return
		}
		factory114 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt115 := factory114.GetProtocol(mbTrans112)
		argvalue0 := project.NewRequestHeader()
		err116 := argvalue0.Read(jsProt115)
		if err116 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err117 := (strconv.Atoi(flag.Arg(2)))
		if err117 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetProjectBySid(value0, value1))
		fmt.Print("\n")
		break
	case "getProjectsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProjectsByIds requires 2 args")
			flag.Usage()
		}
		arg118 := flag.Arg(1)
		mbTrans119 := thrift.NewTMemoryBufferLen(len(arg118))
		defer mbTrans119.Close()
		_, err120 := mbTrans119.WriteString(arg118)
		if err120 != nil {
			Usage()
			return
		}
		factory121 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt122 := factory121.GetProtocol(mbTrans119)
		argvalue0 := project.NewRequestHeader()
		err123 := argvalue0.Read(jsProt122)
		if err123 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg124 := flag.Arg(2)
		mbTrans125 := thrift.NewTMemoryBufferLen(len(arg124))
		defer mbTrans125.Close()
		_, err126 := mbTrans125.WriteString(arg124)
		if err126 != nil {
			Usage()
			return
		}
		factory127 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt128 := factory127.GetProtocol(mbTrans125)
		containerStruct1 := project.NewGetProjectsByIdsArgs()
		err129 := containerStruct1.ReadField2(jsProt128)
		if err129 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetProjectsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getSchedulesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSchedulesByIds requires 2 args")
			flag.Usage()
		}
		arg130 := flag.Arg(1)
		mbTrans131 := thrift.NewTMemoryBufferLen(len(arg130))
		defer mbTrans131.Close()
		_, err132 := mbTrans131.WriteString(arg130)
		if err132 != nil {
			Usage()
			return
		}
		factory133 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt134 := factory133.GetProtocol(mbTrans131)
		argvalue0 := project.NewRequestHeader()
		err135 := argvalue0.Read(jsProt134)
		if err135 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg136 := flag.Arg(2)
		mbTrans137 := thrift.NewTMemoryBufferLen(len(arg136))
		defer mbTrans137.Close()
		_, err138 := mbTrans137.WriteString(arg136)
		if err138 != nil {
			Usage()
			return
		}
		factory139 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt140 := factory139.GetProtocol(mbTrans137)
		containerStruct1 := project.NewGetSchedulesByIdsArgs()
		err141 := containerStruct1.ReadField2(jsProt140)
		if err141 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSchedulesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAllSchedulesByCmid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAllSchedulesByCmid requires 2 args")
			flag.Usage()
		}
		arg142 := flag.Arg(1)
		mbTrans143 := thrift.NewTMemoryBufferLen(len(arg142))
		defer mbTrans143.Close()
		_, err144 := mbTrans143.WriteString(arg142)
		if err144 != nil {
			Usage()
			return
		}
		factory145 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt146 := factory145.GetProtocol(mbTrans143)
		argvalue0 := project.NewRequestHeader()
		err147 := argvalue0.Read(jsProt146)
		if err147 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err148 := (strconv.Atoi(flag.Arg(2)))
		if err148 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetAllSchedulesByCmid(value0, value1))
		fmt.Print("\n")
		break
	case "getAllSchedulesByCmidNew":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAllSchedulesByCmidNew requires 5 args")
			flag.Usage()
		}
		arg149 := flag.Arg(1)
		mbTrans150 := thrift.NewTMemoryBufferLen(len(arg149))
		defer mbTrans150.Close()
		_, err151 := mbTrans150.WriteString(arg149)
		if err151 != nil {
			Usage()
			return
		}
		factory152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt153 := factory152.GetProtocol(mbTrans150)
		argvalue0 := project.NewRequestHeader()
		err154 := argvalue0.Read(jsProt153)
		if err154 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err155 := (strconv.Atoi(flag.Arg(2)))
		if err155 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err156 := (strconv.Atoi(flag.Arg(3)))
		if err156 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err157 := (strconv.Atoi(flag.Arg(4)))
		if err157 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.GetAllSchedulesByCmidNew(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getSchedulesByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSchedulesByParam requires 2 args")
			flag.Usage()
		}
		arg159 := flag.Arg(1)
		mbTrans160 := thrift.NewTMemoryBufferLen(len(arg159))
		defer mbTrans160.Close()
		_, err161 := mbTrans160.WriteString(arg159)
		if err161 != nil {
			Usage()
			return
		}
		factory162 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt163 := factory162.GetProtocol(mbTrans160)
		argvalue0 := project.NewRequestHeader()
		err164 := argvalue0.Read(jsProt163)
		if err164 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg165 := flag.Arg(2)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		argvalue1 := project.NewQueryParam()
		err170 := argvalue1.Read(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetSchedulesByParam(value0, value1))
		fmt.Print("\n")
		break
	case "addPlanProjectRelation":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "AddPlanProjectRelation requires 6 args")
			flag.Usage()
		}
		arg171 := flag.Arg(1)
		mbTrans172 := thrift.NewTMemoryBufferLen(len(arg171))
		defer mbTrans172.Close()
		_, err173 := mbTrans172.WriteString(arg171)
		if err173 != nil {
			Usage()
			return
		}
		factory174 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt175 := factory174.GetProtocol(mbTrans172)
		argvalue0 := project.NewRequestHeader()
		err176 := argvalue0.Read(jsProt175)
		if err176 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err177 := (strconv.Atoi(flag.Arg(2)))
		if err177 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err178 := (strconv.Atoi(flag.Arg(3)))
		if err178 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err179 := (strconv.Atoi(flag.Arg(4)))
		if err179 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err180 := (strconv.Atoi(flag.Arg(5)))
		if err180 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err181 := (strconv.Atoi(flag.Arg(6)))
		if err181 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.AddPlanProjectRelation(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "editOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditOrder requires 3 args")
			flag.Usage()
		}
		arg182 := flag.Arg(1)
		mbTrans183 := thrift.NewTMemoryBufferLen(len(arg182))
		defer mbTrans183.Close()
		_, err184 := mbTrans183.WriteString(arg182)
		if err184 != nil {
			Usage()
			return
		}
		factory185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt186 := factory185.GetProtocol(mbTrans183)
		argvalue0 := project.NewRequestHeader()
		err187 := argvalue0.Read(jsProt186)
		if err187 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err188 := (strconv.Atoi(flag.Arg(2)))
		if err188 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err189 := (strconv.Atoi(flag.Arg(3)))
		if err189 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.EditOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editSchedule":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSchedule requires 3 args")
			flag.Usage()
		}
		arg190 := flag.Arg(1)
		mbTrans191 := thrift.NewTMemoryBufferLen(len(arg190))
		defer mbTrans191.Close()
		_, err192 := mbTrans191.WriteString(arg190)
		if err192 != nil {
			Usage()
			return
		}
		factory193 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt194 := factory193.GetProtocol(mbTrans191)
		argvalue0 := project.NewRequestHeader()
		err195 := argvalue0.Read(jsProt194)
		if err195 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err196 := (strconv.Atoi(flag.Arg(2)))
		if err196 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err197 := (strconv.Atoi(flag.Arg(3)))
		if err197 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.EditSchedule(value0, value1, value2))
		fmt.Print("\n")
		break
	case "assignProjectToAM":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AssignProjectToAM requires 3 args")
			flag.Usage()
		}
		arg198 := flag.Arg(1)
		mbTrans199 := thrift.NewTMemoryBufferLen(len(arg198))
		defer mbTrans199.Close()
		_, err200 := mbTrans199.WriteString(arg198)
		if err200 != nil {
			Usage()
			return
		}
		factory201 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt202 := factory201.GetProtocol(mbTrans199)
		argvalue0 := project.NewRequestHeader()
		err203 := argvalue0.Read(jsProt202)
		if err203 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err204 := (strconv.Atoi(flag.Arg(2)))
		if err204 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err205 := (strconv.Atoi(flag.Arg(3)))
		if err205 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.AssignProjectToAM(value0, value1, value2))
		fmt.Print("\n")
		break
	case "assignScheduleToAM":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "AssignScheduleToAM requires 4 args")
			flag.Usage()
		}
		arg206 := flag.Arg(1)
		mbTrans207 := thrift.NewTMemoryBufferLen(len(arg206))
		defer mbTrans207.Close()
		_, err208 := mbTrans207.WriteString(arg206)
		if err208 != nil {
			Usage()
			return
		}
		factory209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt210 := factory209.GetProtocol(mbTrans207)
		argvalue0 := project.NewRequestHeader()
		err211 := argvalue0.Read(jsProt210)
		if err211 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err212 := (strconv.Atoi(flag.Arg(2)))
		if err212 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err213 := (strconv.Atoi(flag.Arg(3)))
		if err213 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err214 := (strconv.Atoi(flag.Arg(4)))
		if err214 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.AssignScheduleToAM(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updatePlanProjectRelation":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "UpdatePlanProjectRelation requires 6 args")
			flag.Usage()
		}
		arg215 := flag.Arg(1)
		mbTrans216 := thrift.NewTMemoryBufferLen(len(arg215))
		defer mbTrans216.Close()
		_, err217 := mbTrans216.WriteString(arg215)
		if err217 != nil {
			Usage()
			return
		}
		factory218 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt219 := factory218.GetProtocol(mbTrans216)
		argvalue0 := project.NewRequestHeader()
		err220 := argvalue0.Read(jsProt219)
		if err220 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err221 := (strconv.Atoi(flag.Arg(2)))
		if err221 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err222 := (strconv.Atoi(flag.Arg(3)))
		if err222 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err223 := (strconv.Atoi(flag.Arg(4)))
		if err223 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err224 := (strconv.Atoi(flag.Arg(5)))
		if err224 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err225 := (strconv.Atoi(flag.Arg(6)))
		if err225 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.UpdatePlanProjectRelation(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getScheduleById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetScheduleById requires 2 args")
			flag.Usage()
		}
		arg226 := flag.Arg(1)
		mbTrans227 := thrift.NewTMemoryBufferLen(len(arg226))
		defer mbTrans227.Close()
		_, err228 := mbTrans227.WriteString(arg226)
		if err228 != nil {
			Usage()
			return
		}
		factory229 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt230 := factory229.GetProtocol(mbTrans227)
		argvalue0 := project.NewRequestHeader()
		err231 := argvalue0.Read(jsProt230)
		if err231 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err232 := (strconv.Atoi(flag.Arg(2)))
		if err232 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetScheduleById(value0, value1))
		fmt.Print("\n")
		break
	case "getAllProjectsByDepIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAllProjectsByDepIds requires 5 args")
			flag.Usage()
		}
		arg233 := flag.Arg(1)
		mbTrans234 := thrift.NewTMemoryBufferLen(len(arg233))
		defer mbTrans234.Close()
		_, err235 := mbTrans234.WriteString(arg233)
		if err235 != nil {
			Usage()
			return
		}
		factory236 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt237 := factory236.GetProtocol(mbTrans234)
		argvalue0 := project.NewRequestHeader()
		err238 := argvalue0.Read(jsProt237)
		if err238 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg239 := flag.Arg(2)
		mbTrans240 := thrift.NewTMemoryBufferLen(len(arg239))
		defer mbTrans240.Close()
		_, err241 := mbTrans240.WriteString(arg239)
		if err241 != nil {
			Usage()
			return
		}
		factory242 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt243 := factory242.GetProtocol(mbTrans240)
		containerStruct1 := project.NewGetAllProjectsByDepIdsArgs()
		err244 := containerStruct1.ReadField2(jsProt243)
		if err244 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Depids
		value1 := argvalue1
		tmp2, err245 := (strconv.Atoi(flag.Arg(3)))
		if err245 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err246 := (strconv.Atoi(flag.Arg(4)))
		if err246 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.GetAllProjectsByDepIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
