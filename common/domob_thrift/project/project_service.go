// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package project

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/project_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = project_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type ProjectService interface { //项目(订单)管理服务接口

	// 根据订单id返回订单完整信息
	// @return Project
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - PjId: 订单的pj_id
	GetProjectByPid(header *common.RequestHeader, pj_id int32) (r *project_types.Project, pe *ProjectException, err error)
	// 根据amid list获取所有分配ams下的所有订单ids
	// 按pj_id进行排序，升序或降序由ascending指定,默认降序
	// @return 排期ids common.QueryResult
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Amids: amid列表
	//  - Offset: 偏移量
	//  - Limit: 数量限制,最大支持100个
	//  - Ascending: 是否升序
	GetAllProjectsByAmIds(header *common.RequestHeader, amids []int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error)
	// 根据订单id获取所有相关订单下的所有排期
	// 按sd_id进行排序，升序或降序由ascending指定,默认降序
	// @return 排期ids common.QueryResult
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - PjId: 账户所属公司id
	//  - Offset: 偏移量
	//  - Limit: 数量限制,最大支持100个
	//  - Ascending: 是否升序
	GetAllSchedulesByPid(header *common.RequestHeader, pj_id int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error)
	// 根据排期id返回订单完整信息
	// @return Project
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - SdId: 业务线从订单管理系统获取的排期id
	GetProjectBySid(header *common.RequestHeader, sd_id int32) (r *project_types.Project, pe *ProjectException, err error)
	// 批量获取订单信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 订单的编号列表 *
	GetProjectsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*project_types.Project, pe *ProjectException, err error)
	// 批量获取排期信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 订单的编号列表 *
	GetSchedulesByIds(header *common.RequestHeader, ids []int32) (r map[int32]*project_types.Schedule, pe *ProjectException, err error)
	// 老接口根据公司id获取所有相关项目下的所有排期
	// @return 排期list
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - CmId: 账户所属公司id
	GetAllSchedulesByCmid(header *common.RequestHeader, cm_id int32) (r []*project_types.Schedule, pe *ProjectException, err error)
	// 新接口根据公司id获取所有相关项目下的所有排期
	// 按sd_id进行排序，升序或降序由ascending指定,默认降序
	// @return 排期ids common.QueryResult
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - CmId: 账户所属公司id
	//  - Offset: 偏移量
	//  - Limit: 数量限制,最大支持100个
	//  - Ascending: 是否升序
	GetAllSchedulesByCmidNew(header *common.RequestHeader, cm_id int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error)
	// 根据amid获取所有相关的排期
	// 按sd_id进行排序，升序或降序由ascending指定,默认降序
	// @return 排期ids common.QueryResult
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Param: 查询参数 *
	GetSchedulesByParam(header *common.RequestHeader, param *project_types.QueryParam) (r *common.QueryResult, pe *ProjectException, err error)
	// 增加广告计划和项目(订单)排期的关联关系
	// 通过接口插入项目(订单)管理的关联表
	// @return bool
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - PlanId: 业务线广告计划id
	//  - SdId: 业务线从项目管理系统获取的排期id
	//  - ProductType: 业务线类型标示符
	//  - TaskFlag: 投放标记
	//  - SupplementFlag: 当投放标记为补量时的补量说明
	AddPlanProjectRelation(header *common.RequestHeader, plan_id int32, sd_id int32, product_type int32, task_flag int32, supplement_flag int32) (r bool, pe *ProjectException, err error)
	// 编辑订单
	// 编辑后:
	//  触发订单排期更新事件发送
	//  界面根据返回值判断更新成功与否
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: uid
	//  - PjId: 订单id
	EditOrder(header *common.RequestHeader, uid int32, pj_id int32) (r bool, pe *ProjectException, err error)
	// 编辑排期
	// 编辑后:
	//  触发排期更新事件发送
	//  界面根据返回值判断更新成功与否
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: uid
	//  - SdId: 订单id
	EditSchedule(header *common.RequestHeader, uid int32, sd_id int32) (r bool, pe *ProjectException, err error)
	// 将指定项目分配给指定的 am
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - PjId: project id
	//  - AmId: am id
	AssignProjectToAM(header *common.RequestHeader, pj_id int32, am_id int32) (r bool, pe *ProjectException, err error)
	// 将指定排期分配给指定的 am
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: uid
	//  - SdId: schedule id
	//  - AmId: am id
	AssignScheduleToAM(header *common.RequestHeader, uid int32, sd_id int32, am_id int32) (r bool, pe *ProjectException, err error)
	// 修改广告计划id,项目排期id,产品线标示符,投放标记,补量说明
	// @return bool
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - PlanId: 业务线广告计划id
	//  - SdId: 业务线从项目管理系统获取的排期id
	//  - ProductType: 业务线类型标示符
	//  - TaskFlag: 投放标记
	//  - SupplementFlag: 当投放标记为补量时的补量说明
	UpdatePlanProjectRelation(header *common.RequestHeader, plan_id int32, sd_id int32, product_type int32, task_flag int32, supplement_flag int32) (r bool, pe *ProjectException, err error)
	// 根据排期id返回排期完整信息
	// @return Schedule
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - SdId: 业务线从项目管理系统获取的排期id
	GetScheduleById(header *common.RequestHeader, sd_id int32) (r *project_types.Schedule, pe *ProjectException, err error)
	// 根据depid list获取部门下的所有订单ids
	// 按pj_id进行排序，升序或降序由ascending指定,默认降序
	// @return 排期ids common.QueryResult
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Depids: 账户所属公司id
	//  - Offset: 偏移量
	//  - Limit: 数量限制,最大支持100个
	//  - Ascending: 是否升序
	GetAllProjectsByDepIds(header *common.RequestHeader, depids []int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error)
}

//项目(订单)管理服务接口
type ProjectServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewProjectServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ProjectServiceClient {
	return &ProjectServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewProjectServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ProjectServiceClient {
	return &ProjectServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 根据订单id返回订单完整信息
// @return Project
//
// Parameters:
//  - Header: 请求消息头结构体
//  - PjId: 订单的pj_id
func (p *ProjectServiceClient) GetProjectByPid(header *common.RequestHeader, pj_id int32) (r *project_types.Project, pe *ProjectException, err error) {
	if err = p.sendGetProjectByPid(header, pj_id); err != nil {
		return
	}
	return p.recvGetProjectByPid()
}

func (p *ProjectServiceClient) sendGetProjectByPid(header *common.RequestHeader, pj_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getProjectByPid", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetProjectByPidArgs()
	args0.Header = header
	args0.PjId = pj_id
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetProjectByPid() (value *project_types.Project, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetProjectByPidResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Pe != nil {
		pe = result1.Pe
	}
	return
}

// 根据amid list获取所有分配ams下的所有订单ids
// 按pj_id进行排序，升序或降序由ascending指定,默认降序
// @return 排期ids common.QueryResult
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Amids: amid列表
//  - Offset: 偏移量
//  - Limit: 数量限制,最大支持100个
//  - Ascending: 是否升序
func (p *ProjectServiceClient) GetAllProjectsByAmIds(header *common.RequestHeader, amids []int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error) {
	if err = p.sendGetAllProjectsByAmIds(header, amids, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAllProjectsByAmIds()
}

func (p *ProjectServiceClient) sendGetAllProjectsByAmIds(header *common.RequestHeader, amids []int32, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllProjectsByAmIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetAllProjectsByAmIdsArgs()
	args4.Header = header
	args4.Amids = amids
	args4.Offset = offset
	args4.Limit = limit
	args4.Ascending = ascending
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetAllProjectsByAmIds() (value *common.QueryResult, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetAllProjectsByAmIdsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Pe != nil {
		pe = result5.Pe
	}
	return
}

// 根据订单id获取所有相关订单下的所有排期
// 按sd_id进行排序，升序或降序由ascending指定,默认降序
// @return 排期ids common.QueryResult
//
// Parameters:
//  - Header: 请求消息头结构体
//  - PjId: 账户所属公司id
//  - Offset: 偏移量
//  - Limit: 数量限制,最大支持100个
//  - Ascending: 是否升序
func (p *ProjectServiceClient) GetAllSchedulesByPid(header *common.RequestHeader, pj_id int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error) {
	if err = p.sendGetAllSchedulesByPid(header, pj_id, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAllSchedulesByPid()
}

func (p *ProjectServiceClient) sendGetAllSchedulesByPid(header *common.RequestHeader, pj_id int32, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllSchedulesByPid", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetAllSchedulesByPidArgs()
	args8.Header = header
	args8.PjId = pj_id
	args8.Offset = offset
	args8.Limit = limit
	args8.Ascending = ascending
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetAllSchedulesByPid() (value *common.QueryResult, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetAllSchedulesByPidResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Pe != nil {
		pe = result9.Pe
	}
	return
}

// 根据排期id返回订单完整信息
// @return Project
//
// Parameters:
//  - Header: 请求消息头结构体
//  - SdId: 业务线从订单管理系统获取的排期id
func (p *ProjectServiceClient) GetProjectBySid(header *common.RequestHeader, sd_id int32) (r *project_types.Project, pe *ProjectException, err error) {
	if err = p.sendGetProjectBySid(header, sd_id); err != nil {
		return
	}
	return p.recvGetProjectBySid()
}

func (p *ProjectServiceClient) sendGetProjectBySid(header *common.RequestHeader, sd_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getProjectBySid", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetProjectBySidArgs()
	args12.Header = header
	args12.SdId = sd_id
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetProjectBySid() (value *project_types.Project, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetProjectBySidResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Pe != nil {
		pe = result13.Pe
	}
	return
}

// 批量获取订单信息
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 订单的编号列表 *
func (p *ProjectServiceClient) GetProjectsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*project_types.Project, pe *ProjectException, err error) {
	if err = p.sendGetProjectsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetProjectsByIds()
}

func (p *ProjectServiceClient) sendGetProjectsByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getProjectsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetProjectsByIdsArgs()
	args16.Header = header
	args16.Ids = ids
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetProjectsByIds() (value map[int32]*project_types.Project, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetProjectsByIdsResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Pe != nil {
		pe = result17.Pe
	}
	return
}

// 批量获取排期信息
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 订单的编号列表 *
func (p *ProjectServiceClient) GetSchedulesByIds(header *common.RequestHeader, ids []int32) (r map[int32]*project_types.Schedule, pe *ProjectException, err error) {
	if err = p.sendGetSchedulesByIds(header, ids); err != nil {
		return
	}
	return p.recvGetSchedulesByIds()
}

func (p *ProjectServiceClient) sendGetSchedulesByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSchedulesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetSchedulesByIdsArgs()
	args20.Header = header
	args20.Ids = ids
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetSchedulesByIds() (value map[int32]*project_types.Schedule, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetSchedulesByIdsResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Pe != nil {
		pe = result21.Pe
	}
	return
}

// 老接口根据公司id获取所有相关项目下的所有排期
// @return 排期list
//
// Parameters:
//  - Header: 请求消息头结构体
//  - CmId: 账户所属公司id
func (p *ProjectServiceClient) GetAllSchedulesByCmid(header *common.RequestHeader, cm_id int32) (r []*project_types.Schedule, pe *ProjectException, err error) {
	if err = p.sendGetAllSchedulesByCmid(header, cm_id); err != nil {
		return
	}
	return p.recvGetAllSchedulesByCmid()
}

func (p *ProjectServiceClient) sendGetAllSchedulesByCmid(header *common.RequestHeader, cm_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllSchedulesByCmid", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetAllSchedulesByCmidArgs()
	args24.Header = header
	args24.CmId = cm_id
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetAllSchedulesByCmid() (value []*project_types.Schedule, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetAllSchedulesByCmidResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Pe != nil {
		pe = result25.Pe
	}
	return
}

// 新接口根据公司id获取所有相关项目下的所有排期
// 按sd_id进行排序，升序或降序由ascending指定,默认降序
// @return 排期ids common.QueryResult
//
// Parameters:
//  - Header: 请求消息头结构体
//  - CmId: 账户所属公司id
//  - Offset: 偏移量
//  - Limit: 数量限制,最大支持100个
//  - Ascending: 是否升序
func (p *ProjectServiceClient) GetAllSchedulesByCmidNew(header *common.RequestHeader, cm_id int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error) {
	if err = p.sendGetAllSchedulesByCmidNew(header, cm_id, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAllSchedulesByCmidNew()
}

func (p *ProjectServiceClient) sendGetAllSchedulesByCmidNew(header *common.RequestHeader, cm_id int32, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllSchedulesByCmidNew", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetAllSchedulesByCmidNewArgs()
	args28.Header = header
	args28.CmId = cm_id
	args28.Offset = offset
	args28.Limit = limit
	args28.Ascending = ascending
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetAllSchedulesByCmidNew() (value *common.QueryResult, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetAllSchedulesByCmidNewResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Pe != nil {
		pe = result29.Pe
	}
	return
}

// 根据amid获取所有相关的排期
// 按sd_id进行排序，升序或降序由ascending指定,默认降序
// @return 排期ids common.QueryResult
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Param: 查询参数 *
func (p *ProjectServiceClient) GetSchedulesByParam(header *common.RequestHeader, param *project_types.QueryParam) (r *common.QueryResult, pe *ProjectException, err error) {
	if err = p.sendGetSchedulesByParam(header, param); err != nil {
		return
	}
	return p.recvGetSchedulesByParam()
}

func (p *ProjectServiceClient) sendGetSchedulesByParam(header *common.RequestHeader, param *project_types.QueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSchedulesByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetSchedulesByParamArgs()
	args32.Header = header
	args32.Param = param
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetSchedulesByParam() (value *common.QueryResult, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetSchedulesByParamResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Pe != nil {
		pe = result33.Pe
	}
	return
}

// 增加广告计划和项目(订单)排期的关联关系
// 通过接口插入项目(订单)管理的关联表
// @return bool
//
// Parameters:
//  - Header: 请求消息头结构体
//  - PlanId: 业务线广告计划id
//  - SdId: 业务线从项目管理系统获取的排期id
//  - ProductType: 业务线类型标示符
//  - TaskFlag: 投放标记
//  - SupplementFlag: 当投放标记为补量时的补量说明
func (p *ProjectServiceClient) AddPlanProjectRelation(header *common.RequestHeader, plan_id int32, sd_id int32, product_type int32, task_flag int32, supplement_flag int32) (r bool, pe *ProjectException, err error) {
	if err = p.sendAddPlanProjectRelation(header, plan_id, sd_id, product_type, task_flag, supplement_flag); err != nil {
		return
	}
	return p.recvAddPlanProjectRelation()
}

func (p *ProjectServiceClient) sendAddPlanProjectRelation(header *common.RequestHeader, plan_id int32, sd_id int32, product_type int32, task_flag int32, supplement_flag int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addPlanProjectRelation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewAddPlanProjectRelationArgs()
	args36.Header = header
	args36.PlanId = plan_id
	args36.SdId = sd_id
	args36.ProductType = product_type
	args36.TaskFlag = task_flag
	args36.SupplementFlag = supplement_flag
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvAddPlanProjectRelation() (value bool, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewAddPlanProjectRelationResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Pe != nil {
		pe = result37.Pe
	}
	return
}

// 编辑订单
// 编辑后:
//  触发订单排期更新事件发送
//  界面根据返回值判断更新成功与否
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: uid
//  - PjId: 订单id
func (p *ProjectServiceClient) EditOrder(header *common.RequestHeader, uid int32, pj_id int32) (r bool, pe *ProjectException, err error) {
	if err = p.sendEditOrder(header, uid, pj_id); err != nil {
		return
	}
	return p.recvEditOrder()
}

func (p *ProjectServiceClient) sendEditOrder(header *common.RequestHeader, uid int32, pj_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editOrder", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewEditOrderArgs()
	args40.Header = header
	args40.Uid = uid
	args40.PjId = pj_id
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvEditOrder() (value bool, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewEditOrderResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Pe != nil {
		pe = result41.Pe
	}
	return
}

// 编辑排期
// 编辑后:
//  触发排期更新事件发送
//  界面根据返回值判断更新成功与否
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: uid
//  - SdId: 订单id
func (p *ProjectServiceClient) EditSchedule(header *common.RequestHeader, uid int32, sd_id int32) (r bool, pe *ProjectException, err error) {
	if err = p.sendEditSchedule(header, uid, sd_id); err != nil {
		return
	}
	return p.recvEditSchedule()
}

func (p *ProjectServiceClient) sendEditSchedule(header *common.RequestHeader, uid int32, sd_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editSchedule", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewEditScheduleArgs()
	args44.Header = header
	args44.Uid = uid
	args44.SdId = sd_id
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvEditSchedule() (value bool, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewEditScheduleResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Pe != nil {
		pe = result45.Pe
	}
	return
}

// 将指定项目分配给指定的 am
//
// Parameters:
//  - Header: 请求消息头结构体
//  - PjId: project id
//  - AmId: am id
func (p *ProjectServiceClient) AssignProjectToAM(header *common.RequestHeader, pj_id int32, am_id int32) (r bool, pe *ProjectException, err error) {
	if err = p.sendAssignProjectToAM(header, pj_id, am_id); err != nil {
		return
	}
	return p.recvAssignProjectToAM()
}

func (p *ProjectServiceClient) sendAssignProjectToAM(header *common.RequestHeader, pj_id int32, am_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("assignProjectToAM", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewAssignProjectToAMArgs()
	args48.Header = header
	args48.PjId = pj_id
	args48.AmId = am_id
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvAssignProjectToAM() (value bool, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewAssignProjectToAMResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Pe != nil {
		pe = result49.Pe
	}
	return
}

// 将指定排期分配给指定的 am
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: uid
//  - SdId: schedule id
//  - AmId: am id
func (p *ProjectServiceClient) AssignScheduleToAM(header *common.RequestHeader, uid int32, sd_id int32, am_id int32) (r bool, pe *ProjectException, err error) {
	if err = p.sendAssignScheduleToAM(header, uid, sd_id, am_id); err != nil {
		return
	}
	return p.recvAssignScheduleToAM()
}

func (p *ProjectServiceClient) sendAssignScheduleToAM(header *common.RequestHeader, uid int32, sd_id int32, am_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("assignScheduleToAM", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewAssignScheduleToAMArgs()
	args52.Header = header
	args52.Uid = uid
	args52.SdId = sd_id
	args52.AmId = am_id
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvAssignScheduleToAM() (value bool, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewAssignScheduleToAMResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Pe != nil {
		pe = result53.Pe
	}
	return
}

// 修改广告计划id,项目排期id,产品线标示符,投放标记,补量说明
// @return bool
//
// Parameters:
//  - Header: 请求消息头结构体
//  - PlanId: 业务线广告计划id
//  - SdId: 业务线从项目管理系统获取的排期id
//  - ProductType: 业务线类型标示符
//  - TaskFlag: 投放标记
//  - SupplementFlag: 当投放标记为补量时的补量说明
func (p *ProjectServiceClient) UpdatePlanProjectRelation(header *common.RequestHeader, plan_id int32, sd_id int32, product_type int32, task_flag int32, supplement_flag int32) (r bool, pe *ProjectException, err error) {
	if err = p.sendUpdatePlanProjectRelation(header, plan_id, sd_id, product_type, task_flag, supplement_flag); err != nil {
		return
	}
	return p.recvUpdatePlanProjectRelation()
}

func (p *ProjectServiceClient) sendUpdatePlanProjectRelation(header *common.RequestHeader, plan_id int32, sd_id int32, product_type int32, task_flag int32, supplement_flag int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updatePlanProjectRelation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewUpdatePlanProjectRelationArgs()
	args56.Header = header
	args56.PlanId = plan_id
	args56.SdId = sd_id
	args56.ProductType = product_type
	args56.TaskFlag = task_flag
	args56.SupplementFlag = supplement_flag
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvUpdatePlanProjectRelation() (value bool, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewUpdatePlanProjectRelationResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.Pe != nil {
		pe = result57.Pe
	}
	return
}

// 根据排期id返回排期完整信息
// @return Schedule
//
// Parameters:
//  - Header: 请求消息头结构体
//  - SdId: 业务线从项目管理系统获取的排期id
func (p *ProjectServiceClient) GetScheduleById(header *common.RequestHeader, sd_id int32) (r *project_types.Schedule, pe *ProjectException, err error) {
	if err = p.sendGetScheduleById(header, sd_id); err != nil {
		return
	}
	return p.recvGetScheduleById()
}

func (p *ProjectServiceClient) sendGetScheduleById(header *common.RequestHeader, sd_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getScheduleById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewGetScheduleByIdArgs()
	args60.Header = header
	args60.SdId = sd_id
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetScheduleById() (value *project_types.Schedule, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewGetScheduleByIdResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	if result61.Pe != nil {
		pe = result61.Pe
	}
	return
}

// 根据depid list获取部门下的所有订单ids
// 按pj_id进行排序，升序或降序由ascending指定,默认降序
// @return 排期ids common.QueryResult
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Depids: 账户所属公司id
//  - Offset: 偏移量
//  - Limit: 数量限制,最大支持100个
//  - Ascending: 是否升序
func (p *ProjectServiceClient) GetAllProjectsByDepIds(header *common.RequestHeader, depids []int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, pe *ProjectException, err error) {
	if err = p.sendGetAllProjectsByDepIds(header, depids, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAllProjectsByDepIds()
}

func (p *ProjectServiceClient) sendGetAllProjectsByDepIds(header *common.RequestHeader, depids []int32, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllProjectsByDepIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewGetAllProjectsByDepIdsArgs()
	args64.Header = header
	args64.Depids = depids
	args64.Offset = offset
	args64.Limit = limit
	args64.Ascending = ascending
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectServiceClient) recvGetAllProjectsByDepIds() (value *common.QueryResult, pe *ProjectException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewGetAllProjectsByDepIdsResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.Pe != nil {
		pe = result65.Pe
	}
	return
}

type ProjectServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ProjectService
}

func (p *ProjectServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ProjectServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ProjectServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewProjectServiceProcessor(handler ProjectService) *ProjectServiceProcessor {

	self68 := &ProjectServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self68.processorMap["getProjectByPid"] = &projectServiceProcessorGetProjectByPid{handler: handler}
	self68.processorMap["getAllProjectsByAmIds"] = &projectServiceProcessorGetAllProjectsByAmIds{handler: handler}
	self68.processorMap["getAllSchedulesByPid"] = &projectServiceProcessorGetAllSchedulesByPid{handler: handler}
	self68.processorMap["getProjectBySid"] = &projectServiceProcessorGetProjectBySid{handler: handler}
	self68.processorMap["getProjectsByIds"] = &projectServiceProcessorGetProjectsByIds{handler: handler}
	self68.processorMap["getSchedulesByIds"] = &projectServiceProcessorGetSchedulesByIds{handler: handler}
	self68.processorMap["getAllSchedulesByCmid"] = &projectServiceProcessorGetAllSchedulesByCmid{handler: handler}
	self68.processorMap["getAllSchedulesByCmidNew"] = &projectServiceProcessorGetAllSchedulesByCmidNew{handler: handler}
	self68.processorMap["getSchedulesByParam"] = &projectServiceProcessorGetSchedulesByParam{handler: handler}
	self68.processorMap["addPlanProjectRelation"] = &projectServiceProcessorAddPlanProjectRelation{handler: handler}
	self68.processorMap["editOrder"] = &projectServiceProcessorEditOrder{handler: handler}
	self68.processorMap["editSchedule"] = &projectServiceProcessorEditSchedule{handler: handler}
	self68.processorMap["assignProjectToAM"] = &projectServiceProcessorAssignProjectToAM{handler: handler}
	self68.processorMap["assignScheduleToAM"] = &projectServiceProcessorAssignScheduleToAM{handler: handler}
	self68.processorMap["updatePlanProjectRelation"] = &projectServiceProcessorUpdatePlanProjectRelation{handler: handler}
	self68.processorMap["getScheduleById"] = &projectServiceProcessorGetScheduleById{handler: handler}
	self68.processorMap["getAllProjectsByDepIds"] = &projectServiceProcessorGetAllProjectsByDepIds{handler: handler}
	return self68
}

func (p *ProjectServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x69 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x69.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x69

}

type projectServiceProcessorGetProjectByPid struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetProjectByPid) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetProjectByPidArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getProjectByPid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetProjectByPidResult()
	if result.Success, result.Pe, err = p.handler.GetProjectByPid(args.Header, args.PjId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getProjectByPid: "+err.Error())
		oprot.WriteMessageBegin("getProjectByPid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getProjectByPid", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetAllProjectsByAmIds struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetAllProjectsByAmIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllProjectsByAmIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllProjectsByAmIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllProjectsByAmIdsResult()
	if result.Success, result.Pe, err = p.handler.GetAllProjectsByAmIds(args.Header, args.Amids, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllProjectsByAmIds: "+err.Error())
		oprot.WriteMessageBegin("getAllProjectsByAmIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllProjectsByAmIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetAllSchedulesByPid struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetAllSchedulesByPid) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllSchedulesByPidArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllSchedulesByPid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllSchedulesByPidResult()
	if result.Success, result.Pe, err = p.handler.GetAllSchedulesByPid(args.Header, args.PjId, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllSchedulesByPid: "+err.Error())
		oprot.WriteMessageBegin("getAllSchedulesByPid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllSchedulesByPid", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetProjectBySid struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetProjectBySid) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetProjectBySidArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getProjectBySid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetProjectBySidResult()
	if result.Success, result.Pe, err = p.handler.GetProjectBySid(args.Header, args.SdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getProjectBySid: "+err.Error())
		oprot.WriteMessageBegin("getProjectBySid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getProjectBySid", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetProjectsByIds struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetProjectsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetProjectsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getProjectsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetProjectsByIdsResult()
	if result.Success, result.Pe, err = p.handler.GetProjectsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getProjectsByIds: "+err.Error())
		oprot.WriteMessageBegin("getProjectsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getProjectsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetSchedulesByIds struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetSchedulesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSchedulesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSchedulesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSchedulesByIdsResult()
	if result.Success, result.Pe, err = p.handler.GetSchedulesByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSchedulesByIds: "+err.Error())
		oprot.WriteMessageBegin("getSchedulesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSchedulesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetAllSchedulesByCmid struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetAllSchedulesByCmid) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllSchedulesByCmidArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllSchedulesByCmid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllSchedulesByCmidResult()
	if result.Success, result.Pe, err = p.handler.GetAllSchedulesByCmid(args.Header, args.CmId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllSchedulesByCmid: "+err.Error())
		oprot.WriteMessageBegin("getAllSchedulesByCmid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllSchedulesByCmid", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetAllSchedulesByCmidNew struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetAllSchedulesByCmidNew) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllSchedulesByCmidNewArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllSchedulesByCmidNew", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllSchedulesByCmidNewResult()
	if result.Success, result.Pe, err = p.handler.GetAllSchedulesByCmidNew(args.Header, args.CmId, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllSchedulesByCmidNew: "+err.Error())
		oprot.WriteMessageBegin("getAllSchedulesByCmidNew", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllSchedulesByCmidNew", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetSchedulesByParam struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetSchedulesByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSchedulesByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSchedulesByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSchedulesByParamResult()
	if result.Success, result.Pe, err = p.handler.GetSchedulesByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSchedulesByParam: "+err.Error())
		oprot.WriteMessageBegin("getSchedulesByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSchedulesByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorAddPlanProjectRelation struct {
	handler ProjectService
}

func (p *projectServiceProcessorAddPlanProjectRelation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddPlanProjectRelationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addPlanProjectRelation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddPlanProjectRelationResult()
	if result.Success, result.Pe, err = p.handler.AddPlanProjectRelation(args.Header, args.PlanId, args.SdId, args.ProductType, args.TaskFlag, args.SupplementFlag); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addPlanProjectRelation: "+err.Error())
		oprot.WriteMessageBegin("addPlanProjectRelation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addPlanProjectRelation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorEditOrder struct {
	handler ProjectService
}

func (p *projectServiceProcessorEditOrder) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditOrderArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditOrderResult()
	if result.Success, result.Pe, err = p.handler.EditOrder(args.Header, args.Uid, args.PjId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editOrder: "+err.Error())
		oprot.WriteMessageBegin("editOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editOrder", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorEditSchedule struct {
	handler ProjectService
}

func (p *projectServiceProcessorEditSchedule) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditScheduleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editSchedule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditScheduleResult()
	if result.Success, result.Pe, err = p.handler.EditSchedule(args.Header, args.Uid, args.SdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editSchedule: "+err.Error())
		oprot.WriteMessageBegin("editSchedule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editSchedule", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorAssignProjectToAM struct {
	handler ProjectService
}

func (p *projectServiceProcessorAssignProjectToAM) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAssignProjectToAMArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("assignProjectToAM", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAssignProjectToAMResult()
	if result.Success, result.Pe, err = p.handler.AssignProjectToAM(args.Header, args.PjId, args.AmId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing assignProjectToAM: "+err.Error())
		oprot.WriteMessageBegin("assignProjectToAM", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("assignProjectToAM", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorAssignScheduleToAM struct {
	handler ProjectService
}

func (p *projectServiceProcessorAssignScheduleToAM) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAssignScheduleToAMArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("assignScheduleToAM", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAssignScheduleToAMResult()
	if result.Success, result.Pe, err = p.handler.AssignScheduleToAM(args.Header, args.Uid, args.SdId, args.AmId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing assignScheduleToAM: "+err.Error())
		oprot.WriteMessageBegin("assignScheduleToAM", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("assignScheduleToAM", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorUpdatePlanProjectRelation struct {
	handler ProjectService
}

func (p *projectServiceProcessorUpdatePlanProjectRelation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdatePlanProjectRelationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updatePlanProjectRelation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdatePlanProjectRelationResult()
	if result.Success, result.Pe, err = p.handler.UpdatePlanProjectRelation(args.Header, args.PlanId, args.SdId, args.ProductType, args.TaskFlag, args.SupplementFlag); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updatePlanProjectRelation: "+err.Error())
		oprot.WriteMessageBegin("updatePlanProjectRelation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updatePlanProjectRelation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetScheduleById struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetScheduleById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetScheduleByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getScheduleById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetScheduleByIdResult()
	if result.Success, result.Pe, err = p.handler.GetScheduleById(args.Header, args.SdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getScheduleById: "+err.Error())
		oprot.WriteMessageBegin("getScheduleById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getScheduleById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectServiceProcessorGetAllProjectsByDepIds struct {
	handler ProjectService
}

func (p *projectServiceProcessorGetAllProjectsByDepIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllProjectsByDepIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllProjectsByDepIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllProjectsByDepIdsResult()
	if result.Success, result.Pe, err = p.handler.GetAllProjectsByDepIds(args.Header, args.Depids, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllProjectsByDepIds: "+err.Error())
		oprot.WriteMessageBegin("getAllProjectsByDepIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllProjectsByDepIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetProjectByPidArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PjId   int32                 `thrift:"pj_id,2" json:"pj_id"`
}

func NewGetProjectByPidArgs() *GetProjectByPidArgs {
	return &GetProjectByPidArgs{}
}

func (p *GetProjectByPidArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProjectByPidArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetProjectByPidArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PjId = v
	}
	return nil
}

func (p *GetProjectByPidArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProjectByPid_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProjectByPidArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectByPidArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PjId)); err != nil {
		return fmt.Errorf("%T.pj_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pj_id: %s", p, err)
	}
	return err
}

func (p *GetProjectByPidArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectByPidArgs(%+v)", *p)
}

type GetProjectByPidResult struct {
	Success *project_types.Project `thrift:"success,0" json:"success"`
	Pe      *ProjectException      `thrift:"pe,1" json:"pe"`
}

func NewGetProjectByPidResult() *GetProjectByPidResult {
	return &GetProjectByPidResult{}
}

func (p *GetProjectByPidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProjectByPidResult) readField0(iprot thrift.TProtocol) error {
	p.Success = project_types.NewProject()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetProjectByPidResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetProjectByPidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProjectByPid_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProjectByPidResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectByPidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectByPidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectByPidResult(%+v)", *p)
}

type GetAllProjectsByAmIdsArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Amids     []int32               `thrift:"amids,2" json:"amids"`
	Offset    int32                 `thrift:"offset,3" json:"offset"`
	Limit     int32                 `thrift:"limit,4" json:"limit"`
	Ascending bool                  `thrift:"ascending,5" json:"ascending"`
}

func NewGetAllProjectsByAmIdsArgs() *GetAllProjectsByAmIdsArgs {
	return &GetAllProjectsByAmIdsArgs{}
}

func (p *GetAllProjectsByAmIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Amids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem70 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem70 = v
		}
		p.Amids = append(p.Amids, _elem70)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllProjectsByAmIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByAmIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Amids != nil {
		if err := oprot.WriteFieldBegin("amids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:amids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Amids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Amids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:amids: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByAmIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *GetAllProjectsByAmIdsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *GetAllProjectsByAmIdsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *GetAllProjectsByAmIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllProjectsByAmIdsArgs(%+v)", *p)
}

type GetAllProjectsByAmIdsResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Pe      *ProjectException   `thrift:"pe,1" json:"pe"`
}

func NewGetAllProjectsByAmIdsResult() *GetAllProjectsByAmIdsResult {
	return &GetAllProjectsByAmIdsResult{}
}

func (p *GetAllProjectsByAmIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllProjectsByAmIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByAmIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByAmIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByAmIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllProjectsByAmIdsResult(%+v)", *p)
}

type GetAllSchedulesByPidArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	PjId      int32                 `thrift:"pj_id,2" json:"pj_id"`
	Offset    int32                 `thrift:"offset,3" json:"offset"`
	Limit     int32                 `thrift:"limit,4" json:"limit"`
	Ascending bool                  `thrift:"ascending,5" json:"ascending"`
}

func NewGetAllSchedulesByPidArgs() *GetAllSchedulesByPidArgs {
	return &GetAllSchedulesByPidArgs{}
}

func (p *GetAllSchedulesByPidArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PjId = v
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllSchedulesByPid_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByPidArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByPidArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PjId)); err != nil {
		return fmt.Errorf("%T.pj_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pj_id: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByPidArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByPidArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByPidArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByPidArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllSchedulesByPidArgs(%+v)", *p)
}

type GetAllSchedulesByPidResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Pe      *ProjectException   `thrift:"pe,1" json:"pe"`
}

func NewGetAllSchedulesByPidResult() *GetAllSchedulesByPidResult {
	return &GetAllSchedulesByPidResult{}
}

func (p *GetAllSchedulesByPidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByPidResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAllSchedulesByPidResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetAllSchedulesByPidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllSchedulesByPid_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByPidResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByPidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByPidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllSchedulesByPidResult(%+v)", *p)
}

type GetProjectBySidArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	SdId   int32                 `thrift:"sd_id,2" json:"sd_id"`
}

func NewGetProjectBySidArgs() *GetProjectBySidArgs {
	return &GetProjectBySidArgs{}
}

func (p *GetProjectBySidArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProjectBySidArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetProjectBySidArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *GetProjectBySidArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProjectBySid_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProjectBySidArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectBySidArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sd_id: %s", p, err)
	}
	return err
}

func (p *GetProjectBySidArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectBySidArgs(%+v)", *p)
}

type GetProjectBySidResult struct {
	Success *project_types.Project `thrift:"success,0" json:"success"`
	Pe      *ProjectException      `thrift:"pe,1" json:"pe"`
}

func NewGetProjectBySidResult() *GetProjectBySidResult {
	return &GetProjectBySidResult{}
}

func (p *GetProjectBySidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProjectBySidResult) readField0(iprot thrift.TProtocol) error {
	p.Success = project_types.NewProject()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetProjectBySidResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetProjectBySidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProjectBySid_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProjectBySidResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectBySidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectBySidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectBySidResult(%+v)", *p)
}

type GetProjectsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetProjectsByIdsArgs() *GetProjectsByIdsArgs {
	return &GetProjectsByIdsArgs{}
}

func (p *GetProjectsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProjectsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetProjectsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem71 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem71 = v
		}
		p.Ids = append(p.Ids, _elem71)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetProjectsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProjectsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProjectsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectsByIdsArgs(%+v)", *p)
}

type GetProjectsByIdsResult struct {
	Success map[int32]*project_types.Project `thrift:"success,0" json:"success"`
	Pe      *ProjectException                `thrift:"pe,1" json:"pe"`
}

func NewGetProjectsByIdsResult() *GetProjectsByIdsResult {
	return &GetProjectsByIdsResult{}
}

func (p *GetProjectsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetProjectsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*project_types.Project, size)
	for i := 0; i < size; i++ {
		var _key72 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key72 = v
		}
		_val73 := project_types.NewProject()
		if err := _val73.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val73)
		}
		p.Success[_key72] = _val73
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetProjectsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetProjectsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getProjectsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetProjectsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetProjectsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectsByIdsResult(%+v)", *p)
}

type GetSchedulesByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetSchedulesByIdsArgs() *GetSchedulesByIdsArgs {
	return &GetSchedulesByIdsArgs{}
}

func (p *GetSchedulesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSchedulesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem74 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem74 = v
		}
		p.Ids = append(p.Ids, _elem74)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetSchedulesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSchedulesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSchedulesByIdsArgs(%+v)", *p)
}

type GetSchedulesByIdsResult struct {
	Success map[int32]*project_types.Schedule `thrift:"success,0" json:"success"`
	Pe      *ProjectException                 `thrift:"pe,1" json:"pe"`
}

func NewGetSchedulesByIdsResult() *GetSchedulesByIdsResult {
	return &GetSchedulesByIdsResult{}
}

func (p *GetSchedulesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*project_types.Schedule, size)
	for i := 0; i < size; i++ {
		var _key75 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key75 = v
		}
		_val76 := project_types.NewSchedule()
		if err := _val76.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val76)
		}
		p.Success[_key75] = _val76
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetSchedulesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetSchedulesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSchedulesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSchedulesByIdsResult(%+v)", *p)
}

type GetAllSchedulesByCmidArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	CmId   int32                 `thrift:"cm_id,2" json:"cm_id"`
}

func NewGetAllSchedulesByCmidArgs() *GetAllSchedulesByCmidArgs {
	return &GetAllSchedulesByCmidArgs{}
}

func (p *GetAllSchedulesByCmidArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAllSchedulesByCmidArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CmId = v
	}
	return nil
}

func (p *GetAllSchedulesByCmidArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllSchedulesByCmid_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByCmidArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cm_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cm_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CmId)); err != nil {
		return fmt.Errorf("%T.cm_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cm_id: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByCmidArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllSchedulesByCmidArgs(%+v)", *p)
}

type GetAllSchedulesByCmidResult struct {
	Success []*project_types.Schedule `thrift:"success,0" json:"success"`
	Pe      *ProjectException         `thrift:"pe,1" json:"pe"`
}

func NewGetAllSchedulesByCmidResult() *GetAllSchedulesByCmidResult {
	return &GetAllSchedulesByCmidResult{}
}

func (p *GetAllSchedulesByCmidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*project_types.Schedule, 0, size)
	for i := 0; i < size; i++ {
		_elem77 := project_types.NewSchedule()
		if err := _elem77.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem77)
		}
		p.Success = append(p.Success, _elem77)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetAllSchedulesByCmidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllSchedulesByCmid_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByCmidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByCmidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllSchedulesByCmidResult(%+v)", *p)
}

type GetAllSchedulesByCmidNewArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	CmId      int32                 `thrift:"cm_id,2" json:"cm_id"`
	Offset    int32                 `thrift:"offset,3" json:"offset"`
	Limit     int32                 `thrift:"limit,4" json:"limit"`
	Ascending bool                  `thrift:"ascending,5" json:"ascending"`
}

func NewGetAllSchedulesByCmidNewArgs() *GetAllSchedulesByCmidNewArgs {
	return &GetAllSchedulesByCmidNewArgs{}
}

func (p *GetAllSchedulesByCmidNewArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CmId = v
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllSchedulesByCmidNew_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByCmidNewArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cm_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cm_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CmId)); err != nil {
		return fmt.Errorf("%T.cm_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cm_id: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByCmidNewArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByCmidNewArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByCmidNewArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *GetAllSchedulesByCmidNewArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllSchedulesByCmidNewArgs(%+v)", *p)
}

type GetAllSchedulesByCmidNewResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Pe      *ProjectException   `thrift:"pe,1" json:"pe"`
}

func NewGetAllSchedulesByCmidNewResult() *GetAllSchedulesByCmidNewResult {
	return &GetAllSchedulesByCmidNewResult{}
}

func (p *GetAllSchedulesByCmidNewResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllSchedulesByCmidNew_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllSchedulesByCmidNewResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByCmidNewResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetAllSchedulesByCmidNewResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllSchedulesByCmidNewResult(%+v)", *p)
}

type GetSchedulesByParamArgs struct {
	Header *common.RequestHeader     `thrift:"header,1" json:"header"`
	Param  *project_types.QueryParam `thrift:"param,2" json:"param"`
}

func NewGetSchedulesByParamArgs() *GetSchedulesByParamArgs {
	return &GetSchedulesByParamArgs{}
}

func (p *GetSchedulesByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSchedulesByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = project_types.NewQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *GetSchedulesByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSchedulesByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSchedulesByParamArgs(%+v)", *p)
}

type GetSchedulesByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Pe      *ProjectException   `thrift:"pe,1" json:"pe"`
}

func NewGetSchedulesByParamResult() *GetSchedulesByParamResult {
	return &GetSchedulesByParamResult{}
}

func (p *GetSchedulesByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSchedulesByParamResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetSchedulesByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSchedulesByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSchedulesByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetSchedulesByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSchedulesByParamResult(%+v)", *p)
}

type AddPlanProjectRelationArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId         int32                 `thrift:"plan_id,2" json:"plan_id"`
	SdId           int32                 `thrift:"sd_id,3" json:"sd_id"`
	ProductType    int32                 `thrift:"product_type,4" json:"product_type"`
	TaskFlag       int32                 `thrift:"task_flag,5" json:"task_flag"`
	SupplementFlag int32                 `thrift:"supplement_flag,6" json:"supplement_flag"`
}

func NewAddPlanProjectRelationArgs() *AddPlanProjectRelationArgs {
	return &AddPlanProjectRelationArgs{}
}

func (p *AddPlanProjectRelationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ProductType = v
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TaskFlag = v
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SupplementFlag = v
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPlanProjectRelation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPlanProjectRelationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanProjectRelationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:plan_id: %s", p, err)
	}
	return err
}

func (p *AddPlanProjectRelationArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
	}
	return err
}

func (p *AddPlanProjectRelationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:product_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductType)); err != nil {
		return fmt.Errorf("%T.product_type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:product_type: %s", p, err)
	}
	return err
}

func (p *AddPlanProjectRelationArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_flag", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:task_flag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskFlag)); err != nil {
		return fmt.Errorf("%T.task_flag (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:task_flag: %s", p, err)
	}
	return err
}

func (p *AddPlanProjectRelationArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("supplement_flag", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:supplement_flag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SupplementFlag)); err != nil {
		return fmt.Errorf("%T.supplement_flag (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:supplement_flag: %s", p, err)
	}
	return err
}

func (p *AddPlanProjectRelationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPlanProjectRelationArgs(%+v)", *p)
}

type AddPlanProjectRelationResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Pe      *ProjectException `thrift:"pe,1" json:"pe"`
}

func NewAddPlanProjectRelationResult() *AddPlanProjectRelationResult {
	return &AddPlanProjectRelationResult{}
}

func (p *AddPlanProjectRelationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPlanProjectRelationResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddPlanProjectRelationResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *AddPlanProjectRelationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPlanProjectRelation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPlanProjectRelationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddPlanProjectRelationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanProjectRelationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPlanProjectRelationResult(%+v)", *p)
}

type EditOrderArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
	PjId   int32                 `thrift:"pj_id,3" json:"pj_id"`
}

func NewEditOrderArgs() *EditOrderArgs {
	return &EditOrderArgs{}
}

func (p *EditOrderArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditOrderArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditOrderArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *EditOrderArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PjId = v
	}
	return nil
}

func (p *EditOrderArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editOrder_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditOrderArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *EditOrderArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PjId)); err != nil {
		return fmt.Errorf("%T.pj_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pj_id: %s", p, err)
	}
	return err
}

func (p *EditOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditOrderArgs(%+v)", *p)
}

type EditOrderResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Pe      *ProjectException `thrift:"pe,1" json:"pe"`
}

func NewEditOrderResult() *EditOrderResult {
	return &EditOrderResult{}
}

func (p *EditOrderResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditOrderResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *EditOrderResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *EditOrderResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editOrder_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *EditOrderResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *EditOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditOrderResult(%+v)", *p)
}

type EditScheduleArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
	SdId   int32                 `thrift:"sd_id,3" json:"sd_id"`
}

func NewEditScheduleArgs() *EditScheduleArgs {
	return &EditScheduleArgs{}
}

func (p *EditScheduleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditScheduleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditScheduleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *EditScheduleArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *EditScheduleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editSchedule_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditScheduleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditScheduleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *EditScheduleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
	}
	return err
}

func (p *EditScheduleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditScheduleArgs(%+v)", *p)
}

type EditScheduleResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Pe      *ProjectException `thrift:"pe,1" json:"pe"`
}

func NewEditScheduleResult() *EditScheduleResult {
	return &EditScheduleResult{}
}

func (p *EditScheduleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditScheduleResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *EditScheduleResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *EditScheduleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editSchedule_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditScheduleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *EditScheduleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *EditScheduleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditScheduleResult(%+v)", *p)
}

type AssignProjectToAMArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PjId   int32                 `thrift:"pj_id,2" json:"pj_id"`
	AmId   int32                 `thrift:"am_id,3" json:"am_id"`
}

func NewAssignProjectToAMArgs() *AssignProjectToAMArgs {
	return &AssignProjectToAMArgs{}
}

func (p *AssignProjectToAMArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AssignProjectToAMArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AssignProjectToAMArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PjId = v
	}
	return nil
}

func (p *AssignProjectToAMArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AmId = v
	}
	return nil
}

func (p *AssignProjectToAMArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("assignProjectToAM_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AssignProjectToAMArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AssignProjectToAMArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pj_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PjId)); err != nil {
		return fmt.Errorf("%T.pj_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pj_id: %s", p, err)
	}
	return err
}

func (p *AssignProjectToAMArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("am_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:am_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AmId)); err != nil {
		return fmt.Errorf("%T.am_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:am_id: %s", p, err)
	}
	return err
}

func (p *AssignProjectToAMArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssignProjectToAMArgs(%+v)", *p)
}

type AssignProjectToAMResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Pe      *ProjectException `thrift:"pe,1" json:"pe"`
}

func NewAssignProjectToAMResult() *AssignProjectToAMResult {
	return &AssignProjectToAMResult{}
}

func (p *AssignProjectToAMResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AssignProjectToAMResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AssignProjectToAMResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *AssignProjectToAMResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("assignProjectToAM_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AssignProjectToAMResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AssignProjectToAMResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *AssignProjectToAMResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssignProjectToAMResult(%+v)", *p)
}

type AssignScheduleToAMArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
	SdId   int32                 `thrift:"sd_id,3" json:"sd_id"`
	AmId   int32                 `thrift:"am_id,4" json:"am_id"`
}

func NewAssignScheduleToAMArgs() *AssignScheduleToAMArgs {
	return &AssignScheduleToAMArgs{}
}

func (p *AssignScheduleToAMArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AssignScheduleToAMArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AssignScheduleToAMArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AssignScheduleToAMArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *AssignScheduleToAMArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AmId = v
	}
	return nil
}

func (p *AssignScheduleToAMArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("assignScheduleToAM_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AssignScheduleToAMArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AssignScheduleToAMArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AssignScheduleToAMArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
	}
	return err
}

func (p *AssignScheduleToAMArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("am_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:am_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AmId)); err != nil {
		return fmt.Errorf("%T.am_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:am_id: %s", p, err)
	}
	return err
}

func (p *AssignScheduleToAMArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssignScheduleToAMArgs(%+v)", *p)
}

type AssignScheduleToAMResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Pe      *ProjectException `thrift:"pe,1" json:"pe"`
}

func NewAssignScheduleToAMResult() *AssignScheduleToAMResult {
	return &AssignScheduleToAMResult{}
}

func (p *AssignScheduleToAMResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AssignScheduleToAMResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AssignScheduleToAMResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *AssignScheduleToAMResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("assignScheduleToAM_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AssignScheduleToAMResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AssignScheduleToAMResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *AssignScheduleToAMResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssignScheduleToAMResult(%+v)", *p)
}

type UpdatePlanProjectRelationArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId         int32                 `thrift:"plan_id,2" json:"plan_id"`
	SdId           int32                 `thrift:"sd_id,3" json:"sd_id"`
	ProductType    int32                 `thrift:"product_type,4" json:"product_type"`
	TaskFlag       int32                 `thrift:"task_flag,5" json:"task_flag"`
	SupplementFlag int32                 `thrift:"supplement_flag,6" json:"supplement_flag"`
}

func NewUpdatePlanProjectRelationArgs() *UpdatePlanProjectRelationArgs {
	return &UpdatePlanProjectRelationArgs{}
}

func (p *UpdatePlanProjectRelationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ProductType = v
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TaskFlag = v
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SupplementFlag = v
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updatePlanProjectRelation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdatePlanProjectRelationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePlanProjectRelationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:plan_id: %s", p, err)
	}
	return err
}

func (p *UpdatePlanProjectRelationArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
	}
	return err
}

func (p *UpdatePlanProjectRelationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:product_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductType)); err != nil {
		return fmt.Errorf("%T.product_type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:product_type: %s", p, err)
	}
	return err
}

func (p *UpdatePlanProjectRelationArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_flag", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:task_flag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskFlag)); err != nil {
		return fmt.Errorf("%T.task_flag (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:task_flag: %s", p, err)
	}
	return err
}

func (p *UpdatePlanProjectRelationArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("supplement_flag", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:supplement_flag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SupplementFlag)); err != nil {
		return fmt.Errorf("%T.supplement_flag (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:supplement_flag: %s", p, err)
	}
	return err
}

func (p *UpdatePlanProjectRelationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePlanProjectRelationArgs(%+v)", *p)
}

type UpdatePlanProjectRelationResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Pe      *ProjectException `thrift:"pe,1" json:"pe"`
}

func NewUpdatePlanProjectRelationResult() *UpdatePlanProjectRelationResult {
	return &UpdatePlanProjectRelationResult{}
}

func (p *UpdatePlanProjectRelationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdatePlanProjectRelationResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *UpdatePlanProjectRelationResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *UpdatePlanProjectRelationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updatePlanProjectRelation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdatePlanProjectRelationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *UpdatePlanProjectRelationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePlanProjectRelationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePlanProjectRelationResult(%+v)", *p)
}

type GetScheduleByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	SdId   int32                 `thrift:"sd_id,2" json:"sd_id"`
}

func NewGetScheduleByIdArgs() *GetScheduleByIdArgs {
	return &GetScheduleByIdArgs{}
}

func (p *GetScheduleByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetScheduleByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetScheduleByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *GetScheduleByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getScheduleById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetScheduleByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetScheduleByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdId)); err != nil {
		return fmt.Errorf("%T.sd_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sd_id: %s", p, err)
	}
	return err
}

func (p *GetScheduleByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetScheduleByIdArgs(%+v)", *p)
}

type GetScheduleByIdResult struct {
	Success *project_types.Schedule `thrift:"success,0" json:"success"`
	Pe      *ProjectException       `thrift:"pe,1" json:"pe"`
}

func NewGetScheduleByIdResult() *GetScheduleByIdResult {
	return &GetScheduleByIdResult{}
}

func (p *GetScheduleByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetScheduleByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = project_types.NewSchedule()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetScheduleByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetScheduleByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getScheduleById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetScheduleByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetScheduleByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetScheduleByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetScheduleByIdResult(%+v)", *p)
}

type GetAllProjectsByDepIdsArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Depids    []int32               `thrift:"depids,2" json:"depids"`
	Offset    int32                 `thrift:"offset,3" json:"offset"`
	Limit     int32                 `thrift:"limit,4" json:"limit"`
	Ascending bool                  `thrift:"ascending,5" json:"ascending"`
}

func NewGetAllProjectsByDepIdsArgs() *GetAllProjectsByDepIdsArgs {
	return &GetAllProjectsByDepIdsArgs{}
}

func (p *GetAllProjectsByDepIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Depids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem78 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem78 = v
		}
		p.Depids = append(p.Depids, _elem78)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllProjectsByDepIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByDepIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Depids != nil {
		if err := oprot.WriteFieldBegin("depids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:depids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Depids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Depids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:depids: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByDepIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *GetAllProjectsByDepIdsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *GetAllProjectsByDepIdsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *GetAllProjectsByDepIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllProjectsByDepIdsArgs(%+v)", *p)
}

type GetAllProjectsByDepIdsResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Pe      *ProjectException   `thrift:"pe,1" json:"pe"`
}

func NewGetAllProjectsByDepIdsResult() *GetAllProjectsByDepIdsResult {
	return &GetAllProjectsByDepIdsResult{}
}

func (p *GetAllProjectsByDepIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Pe = NewProjectException()
	if err := p.Pe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pe)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllProjectsByDepIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Pe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllProjectsByDepIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByDepIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Pe != nil {
		if err := oprot.WriteFieldBegin("pe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:pe: %s", p, err)
		}
		if err := p.Pe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:pe: %s", p, err)
		}
	}
	return err
}

func (p *GetAllProjectsByDepIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllProjectsByDepIdsResult(%+v)", *p)
}
