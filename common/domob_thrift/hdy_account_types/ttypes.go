// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_account_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

type HdyAccountRole int64

const (
	HdyAccountRole_HAR_UNKNOWN HdyAccountRole = 0
)

func (p HdyAccountRole) String() string {
	switch p {
	case HdyAccountRole_HAR_UNKNOWN:
		return "HdyAccountRole_HAR_UNKNOWN"
	}
	return "<UNSET>"
}

func HdyAccountRoleFromString(s string) (HdyAccountRole, error) {
	switch s {
	case "HdyAccountRole_HAR_UNKNOWN":
		return HdyAccountRole_HAR_UNKNOWN, nil
	}
	return HdyAccountRole(math.MinInt32 - 1), fmt.Errorf("not a valid HdyAccountRole string")
}

//账户余额消耗策略
type AccountBalanceStrategy int64

const (
	AccountBalanceStrategy_ABS_UNKNOWN        AccountBalanceStrategy = 0
	AccountBalanceStrategy_ABS_BALANCE_RATIO  AccountBalanceStrategy = 1
	AccountBalanceStrategy_ABS_RECHARGE_FIRST AccountBalanceStrategy = 2
	AccountBalanceStrategy_ABS_REWARD_FIRST   AccountBalanceStrategy = 3
)

func (p AccountBalanceStrategy) String() string {
	switch p {
	case AccountBalanceStrategy_ABS_UNKNOWN:
		return "AccountBalanceStrategy_ABS_UNKNOWN"
	case AccountBalanceStrategy_ABS_BALANCE_RATIO:
		return "AccountBalanceStrategy_ABS_BALANCE_RATIO"
	case AccountBalanceStrategy_ABS_RECHARGE_FIRST:
		return "AccountBalanceStrategy_ABS_RECHARGE_FIRST"
	case AccountBalanceStrategy_ABS_REWARD_FIRST:
		return "AccountBalanceStrategy_ABS_REWARD_FIRST"
	}
	return "<UNSET>"
}

func AccountBalanceStrategyFromString(s string) (AccountBalanceStrategy, error) {
	switch s {
	case "AccountBalanceStrategy_ABS_UNKNOWN":
		return AccountBalanceStrategy_ABS_UNKNOWN, nil
	case "AccountBalanceStrategy_ABS_BALANCE_RATIO":
		return AccountBalanceStrategy_ABS_BALANCE_RATIO, nil
	case "AccountBalanceStrategy_ABS_RECHARGE_FIRST":
		return AccountBalanceStrategy_ABS_RECHARGE_FIRST, nil
	case "AccountBalanceStrategy_ABS_REWARD_FIRST":
		return AccountBalanceStrategy_ABS_REWARD_FIRST, nil
	}
	return AccountBalanceStrategy(math.MinInt32 - 1), fmt.Errorf("not a valid AccountBalanceStrategy string")
}

type AccountSearchParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewAccountSearchParams() *AccountSearchParams {
	return &AccountSearchParams{}
}

func (p *AccountSearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AccountSearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *AccountSearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AccountSearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AccountSearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *AccountSearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AccountSearchParams(%+v)", *p)
}

type HdyAccountInfo struct {
	Id               int32                        `thrift:"id,1" json:"id"`
	UserRole         HdyAccountRole               `thrift:"userRole,2" json:"userRole"`
	Mobile           int64                        `thrift:"mobile,3" json:"mobile"`
	Email            string                       `thrift:"email,4" json:"email"`
	Password         string                       `thrift:"password,5" json:"password"`
	Recharge         int64                        `thrift:"recharge,6" json:"recharge"`
	Reward           int64                        `thrift:"reward,7" json:"reward"`
	CompanyName      string                       `thrift:"companyName,8" json:"companyName"`
	LicenseNo        string                       `thrift:"licenseNo,9" json:"licenseNo"`
	Address          string                       `thrift:"address,10" json:"address"`
	BusinessLicense  string                       `thrift:"businessLicense,11" json:"businessLicense"`
	ContactName      string                       `thrift:"contactName,12" json:"contactName"`
	ContactPhone     string                       `thrift:"contactPhone,13" json:"contactPhone"`
	LastLogin        int64                        `thrift:"lastLogin,14" json:"lastLogin"`
	BalanceStrategy  AccountBalanceStrategy       `thrift:"balanceStrategy,15" json:"balanceStrategy"`
	ClkPrice         int64                        `thrift:"clkPrice,16" json:"clkPrice"`
	ParentAccountId  int32                        `thrift:"parentAccountId,17" json:"parentAccountId"`
	TotalClkLimit    int64                        `thrift:"totalClkLimit,18" json:"totalClkLimit"`
	UseBudgetControl int16                        `thrift:"useBudgetControl,19" json:"useBudgetControl"`
	CreateTime       int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate       int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status           enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewHdyAccountInfo() *HdyAccountInfo {
	return &HdyAccountInfo{
		UserRole: math.MinInt32 - 1, // unset sentinal value

		BalanceStrategy: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *HdyAccountInfo) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *HdyAccountInfo) IsSetBalanceStrategy() bool {
	return int64(p.BalanceStrategy) != math.MinInt32-1
}

func (p *HdyAccountInfo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *HdyAccountInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I16 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *HdyAccountInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *HdyAccountInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UserRole = HdyAccountRole(v)
	}
	return nil
}

func (p *HdyAccountInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *HdyAccountInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *HdyAccountInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *HdyAccountInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Recharge = v
	}
	return nil
}

func (p *HdyAccountInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Reward = v
	}
	return nil
}

func (p *HdyAccountInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *HdyAccountInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.LicenseNo = v
	}
	return nil
}

func (p *HdyAccountInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Address = v
	}
	return nil
}

func (p *HdyAccountInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.BusinessLicense = v
	}
	return nil
}

func (p *HdyAccountInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ContactName = v
	}
	return nil
}

func (p *HdyAccountInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ContactPhone = v
	}
	return nil
}

func (p *HdyAccountInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.LastLogin = v
	}
	return nil
}

func (p *HdyAccountInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.BalanceStrategy = AccountBalanceStrategy(v)
	}
	return nil
}

func (p *HdyAccountInfo) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ClkPrice = v
	}
	return nil
}

func (p *HdyAccountInfo) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ParentAccountId = v
	}
	return nil
}

func (p *HdyAccountInfo) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.TotalClkLimit = v
	}
	return nil
}

func (p *HdyAccountInfo) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.UseBudgetControl = v
	}
	return nil
}

func (p *HdyAccountInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *HdyAccountInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *HdyAccountInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *HdyAccountInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("HdyAccountInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *HdyAccountInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:userRole: %s", p, err)
		}
	}
	return err
}

func (p *HdyAccountInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mobile: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mobile: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:email: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:password: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("recharge", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:recharge: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Recharge)); err != nil {
		return fmt.Errorf("%T.recharge (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:recharge: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reward", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:reward: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Reward)); err != nil {
		return fmt.Errorf("%T.reward (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:reward: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyName", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:companyName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CompanyName)); err != nil {
		return fmt.Errorf("%T.companyName (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:companyName: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("licenseNo", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:licenseNo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LicenseNo)); err != nil {
		return fmt.Errorf("%T.licenseNo (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:licenseNo: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("address", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:address: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Address)); err != nil {
		return fmt.Errorf("%T.address (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:address: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("businessLicense", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:businessLicense: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BusinessLicense)); err != nil {
		return fmt.Errorf("%T.businessLicense (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:businessLicense: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("contactName", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:contactName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ContactName)); err != nil {
		return fmt.Errorf("%T.contactName (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:contactName: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("contactPhone", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:contactPhone: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ContactPhone)); err != nil {
		return fmt.Errorf("%T.contactPhone (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:contactPhone: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastLogin", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:lastLogin: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastLogin)); err != nil {
		return fmt.Errorf("%T.lastLogin (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:lastLogin: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetBalanceStrategy() {
		if err := oprot.WriteFieldBegin("balanceStrategy", thrift.I32, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:balanceStrategy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BalanceStrategy)); err != nil {
			return fmt.Errorf("%T.balanceStrategy (15) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:balanceStrategy: %s", p, err)
		}
	}
	return err
}

func (p *HdyAccountInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkPrice", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:clkPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClkPrice)); err != nil {
		return fmt.Errorf("%T.clkPrice (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:clkPrice: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parentAccountId", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:parentAccountId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ParentAccountId)); err != nil {
		return fmt.Errorf("%T.parentAccountId (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:parentAccountId: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalClkLimit", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:totalClkLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalClkLimit)); err != nil {
		return fmt.Errorf("%T.totalClkLimit (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:totalClkLimit: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useBudgetControl", thrift.I16, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:useBudgetControl: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.UseBudgetControl)); err != nil {
		return fmt.Errorf("%T.useBudgetControl (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:useBudgetControl: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *HdyAccountInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *HdyAccountInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HdyAccountInfo(%+v)", *p)
}
