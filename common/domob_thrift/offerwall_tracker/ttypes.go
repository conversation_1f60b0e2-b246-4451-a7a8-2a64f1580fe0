// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_tracker

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type LargeIdInt common.LargeIdInt

type UidInt common.UidInt

type MediaIdInt common.IdInt

type PlanIdInt common.IdInt

type CreativeIdInt common.IdInt

type ImgIdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type IdInt common.IdInt

type AccessTypeCode common.AccessTypeCode

type DeviceCode common.DeviceCode

type RegionCode common.RegionCode

type JailBreakCode common.JailBreakCode

type OwAdTracker struct {
	Searchid         LargeIdInt     `thrift:"searchid,1" json:"searchid"`
	Mediaid          MediaIdInt     `thrift:"mediaid,2" json:"mediaid"`
	Deverid          UidInt         `thrift:"deverid,3" json:"deverid"`
	Sponsorid        UidInt         `thrift:"sponsorid,4" json:"sponsorid"`
	Planid           PlanIdInt      `thrift:"planid,5" json:"planid"`
	Cid              CreativeIdInt  `thrift:"cid,6" json:"cid"`
	Sr               bool           `thrift:"sr,7" json:"sr"`
	Pts              Amount         `thrift:"pts,8" json:"pts"`
	Ac               AccessTypeCode `thrift:"ac,9" json:"ac"`
	Dc               DeviceCode     `thrift:"dc,10" json:"dc"`
	Ts               TimeInt        `thrift:"ts,11" json:"ts"`
	Price            Amount         `thrift:"price,12" json:"price"`
	Ms               Amount         `thrift:"ms,13" json:"ms"`
	Rc               RegionCode     `thrift:"rc,14" json:"rc"`
	SpPrice          Amount         `thrift:"sp_price,15" json:"sp_price"`
	Rank             int32          `thrift:"rank,16" json:"rank"`
	DisplayType      int32          `thrift:"display_type,17" json:"display_type"`
	Platform         int32          `thrift:"platform,18" json:"platform"`
	Action           int32          `thrift:"action,19" json:"action"`
	ExpId            int32          `thrift:"exp_id,20" json:"exp_id"`
	CostType         int32          `thrift:"cost_type,21" json:"cost_type"`
	OfferType        int16          `thrift:"offer_type,22" json:"offer_type"`
	OsCode           int32          `thrift:"os_code,23" json:"os_code"`
	Idfv             string         `thrift:"idfv,24" json:"idfv"`
	Duid             string         `thrift:"duid,25" json:"duid"`
	Dmid             string         `thrift:"dmid,26" json:"dmid"`
	Amac             string         `thrift:"amac,27" json:"amac"`
	Jb               JailBreakCode  `thrift:"jb,28" json:"jb"`
	ExtInfo          string         `thrift:"ext_info,29" json:"ext_info"`
	PkgName          string         `thrift:"pkg_name,30" json:"pkg_name"`
	DeviceIp         string         `thrift:"device_ip,31" json:"device_ip"`
	KernelStartTime  int64          `thrift:"kernel_start_time,32" json:"kernel_start_time"`
	DeviceName       string         `thrift:"device_name,33" json:"device_name"`
	Mcc              string         `thrift:"mcc,34" json:"mcc"`
	Mnc              string         `thrift:"mnc,35" json:"mnc"`
	DiskSpace        int64          `thrift:"disk_space,36" json:"disk_space"`
	FreeDiskSpace    int64          `thrift:"free_disk_space,37" json:"free_disk_space"`
	RemainingBattery float64        `thrift:"remaining_battery,38" json:"remaining_battery"`
	IsUnlimit        bool           `thrift:"is_unlimit,39" json:"is_unlimit"`
}

func NewOwAdTracker() *OwAdTracker {
	return &OwAdTracker{
		Ac: math.MinInt32 - 1, // unset sentinal value

		Rc: math.MinInt32 - 1, // unset sentinal value

		Jb: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAdTracker) IsSetAc() bool {
	return int64(p.Ac) != math.MinInt32-1
}

func (p *OwAdTracker) IsSetRc() bool {
	return int64(p.Rc) != math.MinInt32-1
}

func (p *OwAdTracker) IsSetJb() bool {
	return int64(p.Jb) != math.MinInt32-1
}

func (p *OwAdTracker) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I16 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I64 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdTracker) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Searchid = LargeIdInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mediaid = MediaIdInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Deverid = UidInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Sponsorid = UidInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Planid = PlanIdInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Cid = CreativeIdInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *OwAdTracker) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Pts = Amount(v)
	}
	return nil
}

func (p *OwAdTracker) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Ac = AccessTypeCode(v)
	}
	return nil
}

func (p *OwAdTracker) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Dc = DeviceCode(v)
	}
	return nil
}

func (p *OwAdTracker) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Ts = TimeInt(v)
	}
	return nil
}

func (p *OwAdTracker) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *OwAdTracker) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ms = Amount(v)
	}
	return nil
}

func (p *OwAdTracker) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Rc = RegionCode(v)
	}
	return nil
}

func (p *OwAdTracker) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.SpPrice = Amount(v)
	}
	return nil
}

func (p *OwAdTracker) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *OwAdTracker) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *OwAdTracker) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *OwAdTracker) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *OwAdTracker) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ExpId = v
	}
	return nil
}

func (p *OwAdTracker) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwAdTracker) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwAdTracker) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *OwAdTracker) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Idfv = v
	}
	return nil
}

func (p *OwAdTracker) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *OwAdTracker) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Dmid = v
	}
	return nil
}

func (p *OwAdTracker) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *OwAdTracker) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Jb = JailBreakCode(v)
	}
	return nil
}

func (p *OwAdTracker) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *OwAdTracker) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *OwAdTracker) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.DeviceIp = v
	}
	return nil
}

func (p *OwAdTracker) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.KernelStartTime = v
	}
	return nil
}

func (p *OwAdTracker) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.DeviceName = v
	}
	return nil
}

func (p *OwAdTracker) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Mcc = v
	}
	return nil
}

func (p *OwAdTracker) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Mnc = v
	}
	return nil
}

func (p *OwAdTracker) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.DiskSpace = v
	}
	return nil
}

func (p *OwAdTracker) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.FreeDiskSpace = v
	}
	return nil
}

func (p *OwAdTracker) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.RemainingBattery = v
	}
	return nil
}

func (p *OwAdTracker) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.IsUnlimit = v
	}
	return nil
}

func (p *OwAdTracker) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdTracker"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdTracker) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mediaid)); err != nil {
		return fmt.Errorf("%T.mediaid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mediaid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deverid)); err != nil {
		return fmt.Errorf("%T.deverid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:deverid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sponsorid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sponsorid)); err != nil {
		return fmt.Errorf("%T.sponsorid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sponsorid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:planid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:cid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:sr: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:sr: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pts", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:pts: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Pts)); err != nil {
		return fmt.Errorf("%T.pts (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:pts: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ac", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ac: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ac)); err != nil {
		return fmt.Errorf("%T.ac (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ac: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dc", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:dc: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dc)); err != nil {
		return fmt.Errorf("%T.dc (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:dc: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ts", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:ts: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ts)); err != nil {
		return fmt.Errorf("%T.ts (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:ts: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:price: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ms", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ms)); err != nil {
		return fmt.Errorf("%T.ms (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ms: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rc", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:rc: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rc)); err != nil {
		return fmt.Errorf("%T.rc (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:rc: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:sp_price: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:rank: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:display_type: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:platform: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:action: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exp_id", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:exp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpId)); err != nil {
		return fmt.Errorf("%T.exp_id (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:exp_id: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:cost_type: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I16, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:offer_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:offer_type: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:os_code: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfv", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:idfv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfv)); err != nil {
		return fmt.Errorf("%T.idfv (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:idfv: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:duid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:duid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmid", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:dmid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmid)); err != nil {
		return fmt.Errorf("%T.dmid (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:dmid: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:amac: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jb", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:jb: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Jb)); err != nil {
		return fmt.Errorf("%T.jb (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:jb: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext_info", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:ext_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.ext_info (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:ext_info: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_name", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkg_name (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:pkg_name: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_ip", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:device_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceIp)); err != nil {
		return fmt.Errorf("%T.device_ip (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:device_ip: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("kernel_start_time", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:kernel_start_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.KernelStartTime)); err != nil {
		return fmt.Errorf("%T.kernel_start_time (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:kernel_start_time: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_name", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:device_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceName)); err != nil {
		return fmt.Errorf("%T.device_name (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:device_name: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mcc", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:mcc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mcc)); err != nil {
		return fmt.Errorf("%T.mcc (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:mcc: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mnc", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:mnc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mnc)); err != nil {
		return fmt.Errorf("%T.mnc (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:mnc: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disk_space", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:disk_space: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DiskSpace)); err != nil {
		return fmt.Errorf("%T.disk_space (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:disk_space: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("free_disk_space", thrift.I64, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:free_disk_space: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FreeDiskSpace)); err != nil {
		return fmt.Errorf("%T.free_disk_space (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:free_disk_space: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remaining_battery", thrift.DOUBLE, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:remaining_battery: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.RemainingBattery)); err != nil {
		return fmt.Errorf("%T.remaining_battery (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:remaining_battery: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_unlimit", thrift.BOOL, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:is_unlimit: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsUnlimit)); err != nil {
		return fmt.Errorf("%T.is_unlimit (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:is_unlimit: %s", p, err)
	}
	return err
}

func (p *OwAdTracker) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdTracker(%+v)", *p)
}
