// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adn_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("广告ID类型")
type ADStatsIdType int64

const (
	ADStatsIdType_AD_ID_NONE          ADStatsIdType = 0
	ADStatsIdType_AD_ID_TYPE_ACCOUNT  ADStatsIdType = 1
	ADStatsIdType_AD_ID_TYPE_PLAN     ADStatsIdType = 2
	ADStatsIdType_AD_ID_TYPE_STRATEGY ADStatsIdType = 3
	ADStatsIdType_AD_ID_TYPE_CREATIVE ADStatsIdType = 4
)

func (p ADStatsIdType) String() string {
	switch p {
	case ADStatsIdType_AD_ID_NONE:
		return "ADStatsIdType_AD_ID_NONE"
	case ADStatsIdType_AD_ID_TYPE_ACCOUNT:
		return "ADStatsIdType_AD_ID_TYPE_ACCOUNT"
	case ADStatsIdType_AD_ID_TYPE_PLAN:
		return "ADStatsIdType_AD_ID_TYPE_PLAN"
	case ADStatsIdType_AD_ID_TYPE_STRATEGY:
		return "ADStatsIdType_AD_ID_TYPE_STRATEGY"
	case ADStatsIdType_AD_ID_TYPE_CREATIVE:
		return "ADStatsIdType_AD_ID_TYPE_CREATIVE"
	}
	return "<UNSET>"
}

func ADStatsIdTypeFromString(s string) (ADStatsIdType, error) {
	switch s {
	case "ADStatsIdType_AD_ID_NONE":
		return ADStatsIdType_AD_ID_NONE, nil
	case "ADStatsIdType_AD_ID_TYPE_ACCOUNT":
		return ADStatsIdType_AD_ID_TYPE_ACCOUNT, nil
	case "ADStatsIdType_AD_ID_TYPE_PLAN":
		return ADStatsIdType_AD_ID_TYPE_PLAN, nil
	case "ADStatsIdType_AD_ID_TYPE_STRATEGY":
		return ADStatsIdType_AD_ID_TYPE_STRATEGY, nil
	case "ADStatsIdType_AD_ID_TYPE_CREATIVE":
		return ADStatsIdType_AD_ID_TYPE_CREATIVE, nil
	}
	return ADStatsIdType(math.MinInt32 - 1), fmt.Errorf("not a valid ADStatsIdType string")
}

//@Description("媒体ID类型")
type MediaStatsIdType int64

const (
	MediaStatsIdType_MEDIA_ID_NONE           MediaStatsIdType = 0
	MediaStatsIdType_MEDIA_ID_TYPE_ACCOUNT   MediaStatsIdType = 1
	MediaStatsIdType_MEDIA_ID_TYPE_MEDIA     MediaStatsIdType = 2
	MediaStatsIdType_MEDIA_ID_TYPE_PLACEMENT MediaStatsIdType = 3
)

func (p MediaStatsIdType) String() string {
	switch p {
	case MediaStatsIdType_MEDIA_ID_NONE:
		return "MediaStatsIdType_MEDIA_ID_NONE"
	case MediaStatsIdType_MEDIA_ID_TYPE_ACCOUNT:
		return "MediaStatsIdType_MEDIA_ID_TYPE_ACCOUNT"
	case MediaStatsIdType_MEDIA_ID_TYPE_MEDIA:
		return "MediaStatsIdType_MEDIA_ID_TYPE_MEDIA"
	case MediaStatsIdType_MEDIA_ID_TYPE_PLACEMENT:
		return "MediaStatsIdType_MEDIA_ID_TYPE_PLACEMENT"
	}
	return "<UNSET>"
}

func MediaStatsIdTypeFromString(s string) (MediaStatsIdType, error) {
	switch s {
	case "MediaStatsIdType_MEDIA_ID_NONE":
		return MediaStatsIdType_MEDIA_ID_NONE, nil
	case "MediaStatsIdType_MEDIA_ID_TYPE_ACCOUNT":
		return MediaStatsIdType_MEDIA_ID_TYPE_ACCOUNT, nil
	case "MediaStatsIdType_MEDIA_ID_TYPE_MEDIA":
		return MediaStatsIdType_MEDIA_ID_TYPE_MEDIA, nil
	case "MediaStatsIdType_MEDIA_ID_TYPE_PLACEMENT":
		return MediaStatsIdType_MEDIA_ID_TYPE_PLACEMENT, nil
	}
	return MediaStatsIdType(math.MinInt32 - 1), fmt.Errorf("not a valid MediaStatsIdType string")
}

//@Description("时间分片的类型")
type TimeSpan int64

const (
	TimeSpan_TS_NONE TimeSpan = 1
	TimeSpan_TS_DATE TimeSpan = 2
	TimeSpan_TS_HOUR TimeSpan = 3
)

func (p TimeSpan) String() string {
	switch p {
	case TimeSpan_TS_NONE:
		return "TimeSpan_TS_NONE"
	case TimeSpan_TS_DATE:
		return "TimeSpan_TS_DATE"
	case TimeSpan_TS_HOUR:
		return "TimeSpan_TS_HOUR"
	}
	return "<UNSET>"
}

func TimeSpanFromString(s string) (TimeSpan, error) {
	switch s {
	case "TimeSpan_TS_NONE":
		return TimeSpan_TS_NONE, nil
	case "TimeSpan_TS_DATE":
		return TimeSpan_TS_DATE, nil
	case "TimeSpan_TS_HOUR":
		return TimeSpan_TS_HOUR, nil
	}
	return TimeSpan(math.MinInt32 - 1), fmt.Errorf("not a valid TimeSpan string")
}

type ADStatsData struct {
	Id        common.IdInt   `thrift:"id,1" json:"id"`
	IdType    ADStatsIdType  `thrift:"idType,2" json:"idType"`
	StartTime common.TimeInt `thrift:"startTime,3" json:"startTime"`
	EndTime   common.TimeInt `thrift:"endTime,4" json:"endTime"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	ImpressionCount int64 `thrift:"impressionCount,10" json:"impressionCount"`
	ClickCount      int64 `thrift:"clickCount,11" json:"clickCount"`
	AdPrice         int64 `thrift:"adPrice,12" json:"adPrice"`
	MediaPrice      int64 `thrift:"mediaPrice,13" json:"mediaPrice"`
	ActivationCount int64 `thrift:"activationCount,14" json:"activationCount"`
	DownloadCount   int64 `thrift:"downloadCount,15" json:"downloadCount"`
}

func NewADStatsData() *ADStatsData {
	return &ADStatsData{
		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ADStatsData) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *ADStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ADStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = common.IdInt(v)
	}
	return nil
}

func (p *ADStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IdType = ADStatsIdType(v)
	}
	return nil
}

func (p *ADStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartTime = common.TimeInt(v)
	}
	return nil
}

func (p *ADStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EndTime = common.TimeInt(v)
	}
	return nil
}

func (p *ADStatsData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ImpressionCount = v
	}
	return nil
}

func (p *ADStatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ClickCount = v
	}
	return nil
}

func (p *ADStatsData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AdPrice = v
	}
	return nil
}

func (p *ADStatsData) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.MediaPrice = v
	}
	return nil
}

func (p *ADStatsData) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActivationCount = v
	}
	return nil
}

func (p *ADStatsData) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DownloadCount = v
	}
	return nil
}

func (p *ADStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ADStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ADStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idType: %s", p, err)
		}
	}
	return err
}

func (p *ADStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:startTime: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:endTime: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionCount", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:impressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionCount)); err != nil {
		return fmt.Errorf("%T.impressionCount (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:impressionCount: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickCount", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:clickCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickCount)); err != nil {
		return fmt.Errorf("%T.clickCount (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:clickCount: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPrice", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:adPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPrice)); err != nil {
		return fmt.Errorf("%T.adPrice (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:adPrice: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaPrice", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:mediaPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.mediaPrice (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:mediaPrice: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("activationCount", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:activationCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ActivationCount)); err != nil {
		return fmt.Errorf("%T.activationCount (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:activationCount: %s", p, err)
	}
	return err
}

func (p *ADStatsData) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("downloadCount", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:downloadCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DownloadCount)); err != nil {
		return fmt.Errorf("%T.downloadCount (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:downloadCount: %s", p, err)
	}
	return err
}

func (p *ADStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ADStatsData(%+v)", *p)
}

type MediaStatsData struct {
	Id        common.IdInt     `thrift:"id,1" json:"id"`
	IdType    MediaStatsIdType `thrift:"idType,2" json:"idType"`
	StartTime common.TimeInt   `thrift:"startTime,3" json:"startTime"`
	EndTime   common.TimeInt   `thrift:"endTime,4" json:"endTime"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	RequestCount    int64 `thrift:"requestCount,10" json:"requestCount"`
	ResponseCount   int64 `thrift:"responseCount,11" json:"responseCount"`
	ImpressionCount int64 `thrift:"impressionCount,12" json:"impressionCount"`
	ClickCount      int64 `thrift:"clickCount,13" json:"clickCount"`
	AdPrice         int64 `thrift:"adPrice,14" json:"adPrice"`
	MediaPrice      int64 `thrift:"mediaPrice,15" json:"mediaPrice"`
	ActivationCount int64 `thrift:"activationCount,16" json:"activationCount"`
}

func NewMediaStatsData() *MediaStatsData {
	return &MediaStatsData{
		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaStatsData) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *MediaStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = common.IdInt(v)
	}
	return nil
}

func (p *MediaStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IdType = MediaStatsIdType(v)
	}
	return nil
}

func (p *MediaStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartTime = common.TimeInt(v)
	}
	return nil
}

func (p *MediaStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EndTime = common.TimeInt(v)
	}
	return nil
}

func (p *MediaStatsData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RequestCount = v
	}
	return nil
}

func (p *MediaStatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ResponseCount = v
	}
	return nil
}

func (p *MediaStatsData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ImpressionCount = v
	}
	return nil
}

func (p *MediaStatsData) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ClickCount = v
	}
	return nil
}

func (p *MediaStatsData) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AdPrice = v
	}
	return nil
}

func (p *MediaStatsData) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.MediaPrice = v
	}
	return nil
}

func (p *MediaStatsData) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ActivationCount = v
	}
	return nil
}

func (p *MediaStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idType: %s", p, err)
		}
	}
	return err
}

func (p *MediaStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:startTime: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:endTime: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestCount", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:requestCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestCount)); err != nil {
		return fmt.Errorf("%T.requestCount (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:requestCount: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("responseCount", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:responseCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ResponseCount)); err != nil {
		return fmt.Errorf("%T.responseCount (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:responseCount: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionCount", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:impressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionCount)); err != nil {
		return fmt.Errorf("%T.impressionCount (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:impressionCount: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickCount", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:clickCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickCount)); err != nil {
		return fmt.Errorf("%T.clickCount (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:clickCount: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPrice", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:adPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPrice)); err != nil {
		return fmt.Errorf("%T.adPrice (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:adPrice: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaPrice", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:mediaPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.mediaPrice (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:mediaPrice: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("activationCount", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:activationCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ActivationCount)); err != nil {
		return fmt.Errorf("%T.activationCount (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:activationCount: %s", p, err)
	}
	return err
}

func (p *MediaStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaStatsData(%+v)", *p)
}

type StatsServiceException struct {
	Code    int32  `thrift:"code,1" json:"code"`
	Message string `thrift:"message,2" json:"message"`
}

func NewStatsServiceException() *StatsServiceException {
	return &StatsServiceException{}
}

func (p *StatsServiceException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsServiceException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *StatsServiceException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *StatsServiceException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsServiceException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsServiceException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return fmt.Errorf("%T.code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:code: %s", p, err)
	}
	return err
}

func (p *StatsServiceException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *StatsServiceException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsServiceException(%+v)", *p)
}
