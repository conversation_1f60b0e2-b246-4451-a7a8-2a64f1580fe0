// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adn_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__

type StatsService interface { //@Description("统计查询服务")

	// 查询广告统计数据
	// @return &lt;id, AD 统计数据列表&gt; 的 map; key 的 id 为查询 id, 如果不按 id 分组, key 为 0 表示全部; 列表分时间片, 按时间片排序
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - IdList: 查询的 id 列表
	//  - IdType: 查询的 id 类型
	//  - GroupingIdType: 返回的 id 分组方式
	//  - StartTime: 开始时间
	//  - EndTime: 结束时间
	//  - Ts: 返回的时间分片方式
	//  - Pt: 广告位类型, 接受 all, 不接受 unknown
	//  - Ct: 计费类型, 接受 all, 不接受 unknown
	GetADStatsData(requestHeader *common.RequestHeader, idList []common.IdInt, idType ADStatsIdType, groupingIdType ADStatsIdType, startTime common.TimeInt, endTime common.TimeInt, ts TimeSpan, pt common.AdPlacementType, ct common.CostType) (r map[common.IdInt][]*ADStatsData, se *StatsServiceException, err error)
	// 查询媒体统计数据
	// @return &lt;id, Media 统计数据列表&gt; 的 map; key 的 id 为查询 id, 如果不按 id 分组, key 为 0 表示全部; 列表分时间片, 按时间片排序
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - IdList: 查询的 id 列表
	//  - IdType: 查询的 id 类型
	//  - GroupingIdType: 返回的 id 分组方式
	//  - StartTime: 开始时间
	//  - EndTime: 结束时间
	//  - Ts: 返回的时间分片方式
	//  - Pt: 广告位类型, 接受 all, 不接受 unknown
	//  - Ct: 计费类型, 接受 all, 不接受 unknown
	GetMediaStatsData(requestHeader *common.RequestHeader, idList []common.IdInt, idType MediaStatsIdType, groupingIdType MediaStatsIdType, startTime common.TimeInt, endTime common.TimeInt, ts TimeSpan, pt common.AdPlacementType, ct common.CostType) (r map[common.IdInt][]*MediaStatsData, se *StatsServiceException, err error)
}

//@Description("统计查询服务")
type StatsServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewStatsServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *StatsServiceClient {
	return &StatsServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewStatsServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *StatsServiceClient {
	return &StatsServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询广告统计数据
// @return &lt;id, AD 统计数据列表&gt; 的 map; key 的 id 为查询 id, 如果不按 id 分组, key 为 0 表示全部; 列表分时间片, 按时间片排序
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - IdList: 查询的 id 列表
//  - IdType: 查询的 id 类型
//  - GroupingIdType: 返回的 id 分组方式
//  - StartTime: 开始时间
//  - EndTime: 结束时间
//  - Ts: 返回的时间分片方式
//  - Pt: 广告位类型, 接受 all, 不接受 unknown
//  - Ct: 计费类型, 接受 all, 不接受 unknown
func (p *StatsServiceClient) GetADStatsData(requestHeader *common.RequestHeader, idList []common.IdInt, idType ADStatsIdType, groupingIdType ADStatsIdType, startTime common.TimeInt, endTime common.TimeInt, ts TimeSpan, pt common.AdPlacementType, ct common.CostType) (r map[common.IdInt][]*ADStatsData, se *StatsServiceException, err error) {
	if err = p.sendGetADStatsData(requestHeader, idList, idType, groupingIdType, startTime, endTime, ts, pt, ct); err != nil {
		return
	}
	return p.recvGetADStatsData()
}

func (p *StatsServiceClient) sendGetADStatsData(requestHeader *common.RequestHeader, idList []common.IdInt, idType ADStatsIdType, groupingIdType ADStatsIdType, startTime common.TimeInt, endTime common.TimeInt, ts TimeSpan, pt common.AdPlacementType, ct common.CostType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getADStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetADStatsDataArgs()
	args0.RequestHeader = requestHeader
	args0.IdList = idList
	args0.IdType = idType
	args0.GroupingIdType = groupingIdType
	args0.StartTime = startTime
	args0.EndTime = endTime
	args0.Ts = ts
	args0.Pt = pt
	args0.Ct = ct
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsServiceClient) recvGetADStatsData() (value map[common.IdInt][]*ADStatsData, se *StatsServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetADStatsDataResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Se != nil {
		se = result1.Se
	}
	return
}

// 查询媒体统计数据
// @return &lt;id, Media 统计数据列表&gt; 的 map; key 的 id 为查询 id, 如果不按 id 分组, key 为 0 表示全部; 列表分时间片, 按时间片排序
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - IdList: 查询的 id 列表
//  - IdType: 查询的 id 类型
//  - GroupingIdType: 返回的 id 分组方式
//  - StartTime: 开始时间
//  - EndTime: 结束时间
//  - Ts: 返回的时间分片方式
//  - Pt: 广告位类型, 接受 all, 不接受 unknown
//  - Ct: 计费类型, 接受 all, 不接受 unknown
func (p *StatsServiceClient) GetMediaStatsData(requestHeader *common.RequestHeader, idList []common.IdInt, idType MediaStatsIdType, groupingIdType MediaStatsIdType, startTime common.TimeInt, endTime common.TimeInt, ts TimeSpan, pt common.AdPlacementType, ct common.CostType) (r map[common.IdInt][]*MediaStatsData, se *StatsServiceException, err error) {
	if err = p.sendGetMediaStatsData(requestHeader, idList, idType, groupingIdType, startTime, endTime, ts, pt, ct); err != nil {
		return
	}
	return p.recvGetMediaStatsData()
}

func (p *StatsServiceClient) sendGetMediaStatsData(requestHeader *common.RequestHeader, idList []common.IdInt, idType MediaStatsIdType, groupingIdType MediaStatsIdType, startTime common.TimeInt, endTime common.TimeInt, ts TimeSpan, pt common.AdPlacementType, ct common.CostType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMediaStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetMediaStatsDataArgs()
	args4.RequestHeader = requestHeader
	args4.IdList = idList
	args4.IdType = idType
	args4.GroupingIdType = groupingIdType
	args4.StartTime = startTime
	args4.EndTime = endTime
	args4.Ts = ts
	args4.Pt = pt
	args4.Ct = ct
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsServiceClient) recvGetMediaStatsData() (value map[common.IdInt][]*MediaStatsData, se *StatsServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetMediaStatsDataResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Se != nil {
		se = result5.Se
	}
	return
}

type StatsServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      StatsService
}

func (p *StatsServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *StatsServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *StatsServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewStatsServiceProcessor(handler StatsService) *StatsServiceProcessor {

	self8 := &StatsServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self8.processorMap["getADStatsData"] = &statsServiceProcessorGetADStatsData{handler: handler}
	self8.processorMap["getMediaStatsData"] = &statsServiceProcessorGetMediaStatsData{handler: handler}
	return self8
}

func (p *StatsServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x9 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x9.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x9

}

type statsServiceProcessorGetADStatsData struct {
	handler StatsService
}

func (p *statsServiceProcessorGetADStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetADStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getADStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetADStatsDataResult()
	if result.Success, result.Se, err = p.handler.GetADStatsData(args.RequestHeader, args.IdList, args.IdType, args.GroupingIdType, args.StartTime, args.EndTime, args.Ts, args.Pt, args.Ct); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getADStatsData: "+err.Error())
		oprot.WriteMessageBegin("getADStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getADStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsServiceProcessorGetMediaStatsData struct {
	handler StatsService
}

func (p *statsServiceProcessorGetMediaStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMediaStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMediaStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMediaStatsDataResult()
	if result.Success, result.Se, err = p.handler.GetMediaStatsData(args.RequestHeader, args.IdList, args.IdType, args.GroupingIdType, args.StartTime, args.EndTime, args.Ts, args.Pt, args.Ct); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMediaStatsData: "+err.Error())
		oprot.WriteMessageBegin("getMediaStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMediaStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetADStatsDataArgs struct {
	RequestHeader  *common.RequestHeader  `thrift:"requestHeader,1" json:"requestHeader"`
	IdList         []common.IdInt         `thrift:"idList,2" json:"idList"`
	IdType         ADStatsIdType          `thrift:"idType,3" json:"idType"`
	GroupingIdType ADStatsIdType          `thrift:"groupingIdType,4" json:"groupingIdType"`
	StartTime      common.TimeInt         `thrift:"startTime,5" json:"startTime"`
	EndTime        common.TimeInt         `thrift:"endTime,6" json:"endTime"`
	Ts             TimeSpan               `thrift:"ts,7" json:"ts"`
	Pt             common.AdPlacementType `thrift:"pt,8" json:"pt"`
	Ct             common.CostType        `thrift:"ct,9" json:"ct"`
}

func NewGetADStatsDataArgs() *GetADStatsDataArgs {
	return &GetADStatsDataArgs{
		IdType: math.MinInt32 - 1, // unset sentinal value

		GroupingIdType: math.MinInt32 - 1, // unset sentinal value

		Ts: math.MinInt32 - 1, // unset sentinal value

		Pt: math.MinInt32 - 1, // unset sentinal value

		Ct: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetADStatsDataArgs) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *GetADStatsDataArgs) IsSetGroupingIdType() bool {
	return int64(p.GroupingIdType) != math.MinInt32-1
}

func (p *GetADStatsDataArgs) IsSetTs() bool {
	return int64(p.Ts) != math.MinInt32-1
}

func (p *GetADStatsDataArgs) IsSetPt() bool {
	return int64(p.Pt) != math.MinInt32-1
}

func (p *GetADStatsDataArgs) IsSetCt() bool {
	return int64(p.Ct) != math.MinInt32-1
}

func (p *GetADStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = common.IdInt(v)
		}
		p.IdList = append(p.IdList, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdType = ADStatsIdType(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.GroupingIdType = ADStatsIdType(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = common.TimeInt(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = common.TimeInt(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ts = TimeSpan(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Pt = common.AdPlacementType(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Ct = common.CostType(v)
	}
	return nil
}

func (p *GetADStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getADStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetADStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idList: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idType: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetGroupingIdType() {
		if err := oprot.WriteFieldBegin("groupingIdType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:groupingIdType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.GroupingIdType)); err != nil {
			return fmt.Errorf("%T.groupingIdType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:groupingIdType: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startTime: %s", p, err)
	}
	return err
}

func (p *GetADStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endTime: %s", p, err)
	}
	return err
}

func (p *GetADStatsDataArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTs() {
		if err := oprot.WriteFieldBegin("ts", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:ts: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Ts)); err != nil {
			return fmt.Errorf("%T.ts (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:ts: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pt", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:pt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pt)); err != nil {
		return fmt.Errorf("%T.pt (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:pt: %s", p, err)
	}
	return err
}

func (p *GetADStatsDataArgs) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ct", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ct: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ct)); err != nil {
		return fmt.Errorf("%T.ct (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ct: %s", p, err)
	}
	return err
}

func (p *GetADStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetADStatsDataArgs(%+v)", *p)
}

type GetADStatsDataResult struct {
	Success map[common.IdInt][]*ADStatsData `thrift:"success,0" json:"success"`
	Se      *StatsServiceException          `thrift:"se,1" json:"se"`
}

func NewGetADStatsDataResult() *GetADStatsDataResult {
	return &GetADStatsDataResult{}
}

func (p *GetADStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetADStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[common.IdInt][]*ADStatsData, size)
	for i := 0; i < size; i++ {
		var _key11 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key11 = common.IdInt(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val12 := make([]*ADStatsData, 0, size)
		for i := 0; i < size; i++ {
			_elem13 := NewADStatsData()
			if err := _elem13.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem13)
			}
			_val12 = append(_val12, _elem13)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key11] = _val12
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetADStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Se = NewStatsServiceException()
	if err := p.Se.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Se)
	}
	return nil
}

func (p *GetADStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getADStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Se != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetADStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Se != nil {
		if err := oprot.WriteFieldBegin("se", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:se: %s", p, err)
		}
		if err := p.Se.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Se)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:se: %s", p, err)
		}
	}
	return err
}

func (p *GetADStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetADStatsDataResult(%+v)", *p)
}

type GetMediaStatsDataArgs struct {
	RequestHeader  *common.RequestHeader  `thrift:"requestHeader,1" json:"requestHeader"`
	IdList         []common.IdInt         `thrift:"idList,2" json:"idList"`
	IdType         MediaStatsIdType       `thrift:"idType,3" json:"idType"`
	GroupingIdType MediaStatsIdType       `thrift:"groupingIdType,4" json:"groupingIdType"`
	StartTime      common.TimeInt         `thrift:"startTime,5" json:"startTime"`
	EndTime        common.TimeInt         `thrift:"endTime,6" json:"endTime"`
	Ts             TimeSpan               `thrift:"ts,7" json:"ts"`
	Pt             common.AdPlacementType `thrift:"pt,8" json:"pt"`
	Ct             common.CostType        `thrift:"ct,9" json:"ct"`
}

func NewGetMediaStatsDataArgs() *GetMediaStatsDataArgs {
	return &GetMediaStatsDataArgs{
		IdType: math.MinInt32 - 1, // unset sentinal value

		GroupingIdType: math.MinInt32 - 1, // unset sentinal value

		Ts: math.MinInt32 - 1, // unset sentinal value

		Pt: math.MinInt32 - 1, // unset sentinal value

		Ct: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetMediaStatsDataArgs) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *GetMediaStatsDataArgs) IsSetGroupingIdType() bool {
	return int64(p.GroupingIdType) != math.MinInt32-1
}

func (p *GetMediaStatsDataArgs) IsSetTs() bool {
	return int64(p.Ts) != math.MinInt32-1
}

func (p *GetMediaStatsDataArgs) IsSetPt() bool {
	return int64(p.Pt) != math.MinInt32-1
}

func (p *GetMediaStatsDataArgs) IsSetCt() bool {
	return int64(p.Ct) != math.MinInt32-1
}

func (p *GetMediaStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = common.IdInt(v)
		}
		p.IdList = append(p.IdList, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdType = MediaStatsIdType(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.GroupingIdType = MediaStatsIdType(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = common.TimeInt(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = common.TimeInt(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ts = TimeSpan(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Pt = common.AdPlacementType(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Ct = common.CostType(v)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idList: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idType: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetGroupingIdType() {
		if err := oprot.WriteFieldBegin("groupingIdType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:groupingIdType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.GroupingIdType)); err != nil {
			return fmt.Errorf("%T.groupingIdType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:groupingIdType: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startTime: %s", p, err)
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endTime: %s", p, err)
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTs() {
		if err := oprot.WriteFieldBegin("ts", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:ts: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Ts)); err != nil {
			return fmt.Errorf("%T.ts (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:ts: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pt", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:pt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pt)); err != nil {
		return fmt.Errorf("%T.pt (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:pt: %s", p, err)
	}
	return err
}

func (p *GetMediaStatsDataArgs) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ct", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ct: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ct)); err != nil {
		return fmt.Errorf("%T.ct (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ct: %s", p, err)
	}
	return err
}

func (p *GetMediaStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaStatsDataArgs(%+v)", *p)
}

type GetMediaStatsDataResult struct {
	Success map[common.IdInt][]*MediaStatsData `thrift:"success,0" json:"success"`
	Se      *StatsServiceException             `thrift:"se,1" json:"se"`
}

func NewGetMediaStatsDataResult() *GetMediaStatsDataResult {
	return &GetMediaStatsDataResult{}
}

func (p *GetMediaStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[common.IdInt][]*MediaStatsData, size)
	for i := 0; i < size; i++ {
		var _key15 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key15 = common.IdInt(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val16 := make([]*MediaStatsData, 0, size)
		for i := 0; i < size; i++ {
			_elem17 := NewMediaStatsData()
			if err := _elem17.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem17)
			}
			_val16 = append(_val16, _elem17)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key15] = _val16
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetMediaStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Se = NewStatsServiceException()
	if err := p.Se.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Se)
	}
	return nil
}

func (p *GetMediaStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Se != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Se != nil {
		if err := oprot.WriteFieldBegin("se", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:se: %s", p, err)
		}
		if err := p.Se.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Se)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:se: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaStatsDataResult(%+v)", *p)
}
