// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"adn_stats"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "   getADStatsData(RequestHeader requestHeader,  idList, ADStatsIdType idType, ADStatsIdType groupingIdType, TimeInt startTime, TimeInt endTime, TimeSpan ts, AdPlacementType pt, CostType ct)")
	fmt.Fprintln(os.Stderr, "   getMediaStatsData(RequestHeader requestHeader,  idList, MediaStatsIdType idType, MediaStatsIdType groupingIdType, TimeInt startTime, TimeInt endTime, TimeSpan ts, AdPlacementType pt, CostType ct)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := adn_stats.NewStatsServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getADStatsData":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "GetADStatsData requires 9 args")
			flag.Usage()
		}
		arg18 := flag.Arg(1)
		mbTrans19 := thrift.NewTMemoryBufferLen(len(arg18))
		defer mbTrans19.Close()
		_, err20 := mbTrans19.WriteString(arg18)
		if err20 != nil {
			Usage()
			return
		}
		factory21 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt22 := factory21.GetProtocol(mbTrans19)
		argvalue0 := adn_stats.NewRequestHeader()
		err23 := argvalue0.Read(jsProt22)
		if err23 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg24 := flag.Arg(2)
		mbTrans25 := thrift.NewTMemoryBufferLen(len(arg24))
		defer mbTrans25.Close()
		_, err26 := mbTrans25.WriteString(arg24)
		if err26 != nil {
			Usage()
			return
		}
		factory27 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt28 := factory27.GetProtocol(mbTrans25)
		containerStruct1 := adn_stats.NewGetADStatsDataArgs()
		err29 := containerStruct1.ReadField2(jsProt28)
		if err29 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adn_stats.ADStatsIdType(tmp2)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := adn_stats.ADStatsIdType(tmp3)
		value3 := argvalue3
		argvalue4, err30 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err30 != nil {
			Usage()
			return
		}
		value4 := adn_stats.TimeInt(argvalue4)
		argvalue5, err31 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err31 != nil {
			Usage()
			return
		}
		value5 := adn_stats.TimeInt(argvalue5)
		tmp6, err := (strconv.Atoi(flag.Arg(7)))
		if err != nil {
			Usage()
			return
		}
		argvalue6 := adn_stats.TimeSpan(tmp6)
		value6 := argvalue6
		tmp7, err := (strconv.Atoi(flag.Arg(8)))
		if err != nil {
			Usage()
			return
		}
		argvalue7 := adn_stats.AdPlacementType(tmp7)
		value7 := adn_stats.AdPlacementType(argvalue7)
		tmp8, err := (strconv.Atoi(flag.Arg(9)))
		if err != nil {
			Usage()
			return
		}
		argvalue8 := adn_stats.CostType(tmp8)
		value8 := adn_stats.CostType(argvalue8)
		fmt.Print(client.GetADStatsData(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "getMediaStatsData":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "GetMediaStatsData requires 9 args")
			flag.Usage()
		}
		arg32 := flag.Arg(1)
		mbTrans33 := thrift.NewTMemoryBufferLen(len(arg32))
		defer mbTrans33.Close()
		_, err34 := mbTrans33.WriteString(arg32)
		if err34 != nil {
			Usage()
			return
		}
		factory35 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt36 := factory35.GetProtocol(mbTrans33)
		argvalue0 := adn_stats.NewRequestHeader()
		err37 := argvalue0.Read(jsProt36)
		if err37 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg38 := flag.Arg(2)
		mbTrans39 := thrift.NewTMemoryBufferLen(len(arg38))
		defer mbTrans39.Close()
		_, err40 := mbTrans39.WriteString(arg38)
		if err40 != nil {
			Usage()
			return
		}
		factory41 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt42 := factory41.GetProtocol(mbTrans39)
		containerStruct1 := adn_stats.NewGetMediaStatsDataArgs()
		err43 := containerStruct1.ReadField2(jsProt42)
		if err43 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adn_stats.MediaStatsIdType(tmp2)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := adn_stats.MediaStatsIdType(tmp3)
		value3 := argvalue3
		argvalue4, err44 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err44 != nil {
			Usage()
			return
		}
		value4 := adn_stats.TimeInt(argvalue4)
		argvalue5, err45 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err45 != nil {
			Usage()
			return
		}
		value5 := adn_stats.TimeInt(argvalue5)
		tmp6, err := (strconv.Atoi(flag.Arg(7)))
		if err != nil {
			Usage()
			return
		}
		argvalue6 := adn_stats.TimeSpan(tmp6)
		value6 := argvalue6
		tmp7, err := (strconv.Atoi(flag.Arg(8)))
		if err != nil {
			Usage()
			return
		}
		argvalue7 := adn_stats.AdPlacementType(tmp7)
		value7 := adn_stats.AdPlacementType(argvalue7)
		tmp8, err := (strconv.Atoi(flag.Arg(9)))
		if err != nil {
			Usage()
			return
		}
		argvalue8 := adn_stats.CostType(tmp8)
		value8 := adn_stats.CostType(argvalue8)
		fmt.Print(client.GetMediaStatsData(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
