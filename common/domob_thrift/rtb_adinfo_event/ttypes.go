// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_adinfo_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = rtb_adinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

//事件类型,暂时只支持RET_AUDIT_APPROVE RET_AUDIT_REJECT
type RTBEventType int64

const (
	RTBEventType_RET_UNKNOWN       RTBEventType = 0
	RTBEventType_RET_ADD           RTBEventType = 1
	RTBEventType_RET_UPDATE        RTBEventType = 2
	RTBEventType_RET_DELETE        RTBEventType = 3
	RTBEventType_RET_PAUSE         RTBEventType = 4
	RTBEventType_RET_RESUME        RTBEventType = 5
	RTBEventType_RET_AUDIT_APPROVE RTBEventType = 10
	RTBEventType_RET_AUDIT_REJECT  RTBEventType = 11
	RTBEventType_RET_BUDGET_OK     RTBEventType = 20
	RTBEventType_RET_BUDGET_OVER   RTBEventType = 21
)

func (p RTBEventType) String() string {
	switch p {
	case RTBEventType_RET_UNKNOWN:
		return "RTBEventType_RET_UNKNOWN"
	case RTBEventType_RET_ADD:
		return "RTBEventType_RET_ADD"
	case RTBEventType_RET_UPDATE:
		return "RTBEventType_RET_UPDATE"
	case RTBEventType_RET_DELETE:
		return "RTBEventType_RET_DELETE"
	case RTBEventType_RET_PAUSE:
		return "RTBEventType_RET_PAUSE"
	case RTBEventType_RET_RESUME:
		return "RTBEventType_RET_RESUME"
	case RTBEventType_RET_AUDIT_APPROVE:
		return "RTBEventType_RET_AUDIT_APPROVE"
	case RTBEventType_RET_AUDIT_REJECT:
		return "RTBEventType_RET_AUDIT_REJECT"
	case RTBEventType_RET_BUDGET_OK:
		return "RTBEventType_RET_BUDGET_OK"
	case RTBEventType_RET_BUDGET_OVER:
		return "RTBEventType_RET_BUDGET_OVER"
	}
	return "<UNSET>"
}

func RTBEventTypeFromString(s string) (RTBEventType, error) {
	switch s {
	case "RTBEventType_RET_UNKNOWN":
		return RTBEventType_RET_UNKNOWN, nil
	case "RTBEventType_RET_ADD":
		return RTBEventType_RET_ADD, nil
	case "RTBEventType_RET_UPDATE":
		return RTBEventType_RET_UPDATE, nil
	case "RTBEventType_RET_DELETE":
		return RTBEventType_RET_DELETE, nil
	case "RTBEventType_RET_PAUSE":
		return RTBEventType_RET_PAUSE, nil
	case "RTBEventType_RET_RESUME":
		return RTBEventType_RET_RESUME, nil
	case "RTBEventType_RET_AUDIT_APPROVE":
		return RTBEventType_RET_AUDIT_APPROVE, nil
	case "RTBEventType_RET_AUDIT_REJECT":
		return RTBEventType_RET_AUDIT_REJECT, nil
	case "RTBEventType_RET_BUDGET_OK":
		return RTBEventType_RET_BUDGET_OK, nil
	case "RTBEventType_RET_BUDGET_OVER":
		return RTBEventType_RET_BUDGET_OVER, nil
	}
	return RTBEventType(math.MinInt32 - 1), fmt.Errorf("not a valid RTBEventType string")
}

type RTBCampaignUpdateEvent struct {
	SponsorId  int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	CampaignId int32 `thrift:"campaign_id,2" json:"campaign_id"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Campaign *rtb_adinfo_types.RTBCampaign `thrift:"campaign,20" json:"campaign"`
}

func NewRTBCampaignUpdateEvent() *RTBCampaignUpdateEvent {
	return &RTBCampaignUpdateEvent{}
}

func (p *RTBCampaignUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBCampaignUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBCampaignUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RTBCampaignUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBCampaignUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.Campaign = rtb_adinfo_types.NewRTBCampaign()
	if err := p.Campaign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Campaign)
	}
	return nil
}

func (p *RTBCampaignUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBCampaignUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBCampaignUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBCampaignUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *RTBCampaignUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBCampaignUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Campaign != nil {
		if err := oprot.WriteFieldBegin("campaign", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:campaign: %s", p, err)
		}
		if err := p.Campaign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Campaign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:campaign: %s", p, err)
		}
	}
	return err
}

func (p *RTBCampaignUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBCampaignUpdateEvent(%+v)", *p)
}

type RTBStrategyUpdateEvent struct {
	SponsorId  int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	CampaignId int32 `thrift:"campaign_id,2" json:"campaign_id"`
	StrategyId int32 `thrift:"strategy_id,3" json:"strategy_id"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Strategy *rtb_adinfo_types.RTBStrategy `thrift:"strategy,20" json:"strategy"`
}

func NewRTBStrategyUpdateEvent() *RTBStrategyUpdateEvent {
	return &RTBStrategyUpdateEvent{}
}

func (p *RTBStrategyUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.Strategy = rtb_adinfo_types.NewRTBStrategy()
	if err := p.Strategy.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Strategy)
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBStrategyUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBStrategyUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBStrategyUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *RTBStrategyUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:strategy_id: %s", p, err)
	}
	return err
}

func (p *RTBStrategyUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBStrategyUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Strategy != nil {
		if err := oprot.WriteFieldBegin("strategy", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:strategy: %s", p, err)
		}
		if err := p.Strategy.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Strategy)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:strategy: %s", p, err)
		}
	}
	return err
}

func (p *RTBStrategyUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBStrategyUpdateEvent(%+v)", *p)
}

type RTBCreativeUpdateEvent struct {
	SponsorId  int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	CampaignId int32 `thrift:"campaign_id,2" json:"campaign_id"`
	CreativeId int32 `thrift:"creative_id,3" json:"creative_id"`
	StrategyId int32 `thrift:"strategy_id,4" json:"strategy_id"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable        bool         `thrift:"runnable,10" json:"runnable"`
	EventType       RTBEventType `thrift:"event_type,11" json:"event_type"`
	EventExchangeId int32        `thrift:"event_exchange_id,12" json:"event_exchange_id"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Creative *rtb_adinfo_types.RTBCreative `thrift:"creative,20" json:"creative"`
}

func NewRTBCreativeUpdateEvent() *RTBCreativeUpdateEvent {
	return &RTBCreativeUpdateEvent{
		EventType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBCreativeUpdateEvent) IsSetEventType() bool {
	return int64(p.EventType) != math.MinInt32-1
}

func (p *RTBCreativeUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.EventType = RTBEventType(v)
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.EventExchangeId = v
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.Creative = rtb_adinfo_types.NewRTBCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBCreativeUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBCreativeUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creative_id: %s", p, err)
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:strategy_id: %s", p, err)
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventType() {
		if err := oprot.WriteFieldBegin("event_type", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:event_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.EventType)); err != nil {
			return fmt.Errorf("%T.event_type (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:event_type: %s", p, err)
		}
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_exchange_id", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:event_exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EventExchangeId)); err != nil {
		return fmt.Errorf("%T.event_exchange_id (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:event_exchange_id: %s", p, err)
	}
	return err
}

func (p *RTBCreativeUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:creative: %s", p, err)
		}
	}
	return err
}

func (p *RTBCreativeUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBCreativeUpdateEvent(%+v)", *p)
}

type RTBSponsorUpdateEvent struct {
	SponsorId int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable        bool         `thrift:"runnable,10" json:"runnable"`
	EventType       RTBEventType `thrift:"event_type,11" json:"event_type"`
	EventExchangeId int32        `thrift:"event_exchange_id,12" json:"event_exchange_id"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Sponsor *rtb_adinfo_types.RTBSponsor `thrift:"sponsor,20" json:"sponsor"`
}

func NewRTBSponsorUpdateEvent() *RTBSponsorUpdateEvent {
	return &RTBSponsorUpdateEvent{
		EventType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBSponsorUpdateEvent) IsSetEventType() bool {
	return int64(p.EventType) != math.MinInt32-1
}

func (p *RTBSponsorUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.EventType = RTBEventType(v)
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.EventExchangeId = v
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.Sponsor = rtb_adinfo_types.NewRTBSponsor()
	if err := p.Sponsor.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sponsor)
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBSponsorUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBSponsorUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBSponsorUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBSponsorUpdateEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventType() {
		if err := oprot.WriteFieldBegin("event_type", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:event_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.EventType)); err != nil {
			return fmt.Errorf("%T.event_type (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:event_type: %s", p, err)
		}
	}
	return err
}

func (p *RTBSponsorUpdateEvent) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_exchange_id", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:event_exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EventExchangeId)); err != nil {
		return fmt.Errorf("%T.event_exchange_id (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:event_exchange_id: %s", p, err)
	}
	return err
}

func (p *RTBSponsorUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Sponsor != nil {
		if err := oprot.WriteFieldBegin("sponsor", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:sponsor: %s", p, err)
		}
		if err := p.Sponsor.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sponsor)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:sponsor: %s", p, err)
		}
	}
	return err
}

func (p *RTBSponsorUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBSponsorUpdateEvent(%+v)", *p)
}

type RTBPromotionUpdateEvent struct {
	// unused field # 1
	PromotionId int32 `thrift:"promotion_id,2" json:"promotion_id"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Promotion *rtb_adinfo_types.RTBPromotion `thrift:"promotion,20" json:"promotion"`
}

func NewRTBPromotionUpdateEvent() *RTBPromotionUpdateEvent {
	return &RTBPromotionUpdateEvent{}
}

func (p *RTBPromotionUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBPromotionUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *RTBPromotionUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBPromotionUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.Promotion = rtb_adinfo_types.NewRTBPromotion()
	if err := p.Promotion.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Promotion)
	}
	return nil
}

func (p *RTBPromotionUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBPromotionUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBPromotionUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotion_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:promotion_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotion_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:promotion_id: %s", p, err)
	}
	return err
}

func (p *RTBPromotionUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBPromotionUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Promotion != nil {
		if err := oprot.WriteFieldBegin("promotion", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:promotion: %s", p, err)
		}
		if err := p.Promotion.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Promotion)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:promotion: %s", p, err)
		}
	}
	return err
}

func (p *RTBPromotionUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBPromotionUpdateEvent(%+v)", *p)
}

type RTBAdInfoFlushEvent struct {
}

func NewRTBAdInfoFlushEvent() *RTBAdInfoFlushEvent {
	return &RTBAdInfoFlushEvent{}
}

func (p *RTBAdInfoFlushEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBAdInfoFlushEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBAdInfoFlushEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBAdInfoFlushEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBAdInfoFlushEvent(%+v)", *p)
}

type RTBCampaignTotalBudgetEvent struct {
	SponsorId  int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	CampaignId int32 `thrift:"campaign_id,2" json:"campaign_id"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	BudgetType    rtb_adinfo_types.BudgetType `thrift:"budget_type,20" json:"budget_type"`
	TotalBudget   int64                       `thrift:"total_budget,21" json:"total_budget"`
	TotalConsumed int64                       `thrift:"total_consumed,22" json:"total_consumed"`
	Timestamp     int64                       `thrift:"timestamp,23" json:"timestamp"`
}

func NewRTBCampaignTotalBudgetEvent() *RTBCampaignTotalBudgetEvent {
	return &RTBCampaignTotalBudgetEvent{
		BudgetType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBCampaignTotalBudgetEvent) IsSetBudgetType() bool {
	return int64(p.BudgetType) != math.MinInt32-1
}

func (p *RTBCampaignTotalBudgetEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.BudgetType = rtb_adinfo_types.BudgetType(v)
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.TotalConsumed = v
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBCampaignTotalBudgetEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBCampaignTotalBudgetEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetBudgetType() {
		if err := oprot.WriteFieldBegin("budget_type", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:budget_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BudgetType)); err != nil {
			return fmt.Errorf("%T.budget_type (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:budget_type: %s", p, err)
		}
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_budget", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:total_budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.total_budget (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:total_budget: %s", p, err)
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_consumed", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:total_consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalConsumed)); err != nil {
		return fmt.Errorf("%T.total_consumed (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:total_consumed: %s", p, err)
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:timestamp: %s", p, err)
	}
	return err
}

func (p *RTBCampaignTotalBudgetEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBCampaignTotalBudgetEvent(%+v)", *p)
}

type RTBCampaignDailyBudgetEvent struct {
	SponsorId  int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	CampaignId int32 `thrift:"campaign_id,2" json:"campaign_id"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	BudgetType    rtb_adinfo_types.BudgetType `thrift:"budget_type,20" json:"budget_type"`
	DailyBudget   int64                       `thrift:"daily_budget,21" json:"daily_budget"`
	DailyConsumed int64                       `thrift:"daily_consumed,22" json:"daily_consumed"`
	Timestamp     int64                       `thrift:"timestamp,23" json:"timestamp"`
}

func NewRTBCampaignDailyBudgetEvent() *RTBCampaignDailyBudgetEvent {
	return &RTBCampaignDailyBudgetEvent{
		BudgetType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBCampaignDailyBudgetEvent) IsSetBudgetType() bool {
	return int64(p.BudgetType) != math.MinInt32-1
}

func (p *RTBCampaignDailyBudgetEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.BudgetType = rtb_adinfo_types.BudgetType(v)
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.DailyConsumed = v
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBCampaignDailyBudgetEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBCampaignDailyBudgetEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetBudgetType() {
		if err := oprot.WriteFieldBegin("budget_type", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:budget_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BudgetType)); err != nil {
			return fmt.Errorf("%T.budget_type (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:budget_type: %s", p, err)
		}
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("daily_budget", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:daily_budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.daily_budget (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:daily_budget: %s", p, err)
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("daily_consumed", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:daily_consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyConsumed)); err != nil {
		return fmt.Errorf("%T.daily_consumed (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:daily_consumed: %s", p, err)
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:timestamp: %s", p, err)
	}
	return err
}

func (p *RTBCampaignDailyBudgetEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBCampaignDailyBudgetEvent(%+v)", *p)
}

type RTBStrategyDailyBudgetEvent struct {
	SponsorId  int32 `thrift:"sponsor_id,1" json:"sponsor_id"`
	CampaignId int32 `thrift:"campaign_id,2" json:"campaign_id"`
	StrategyId int32 `thrift:"strategy_id,3" json:"strategy_id"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	BudgetType    rtb_adinfo_types.BudgetType `thrift:"budget_type,20" json:"budget_type"`
	DailyBudget   int64                       `thrift:"daily_budget,21" json:"daily_budget"`
	DailyConsumed int64                       `thrift:"daily_consumed,22" json:"daily_consumed"`
	Timestamp     int64                       `thrift:"timestamp,23" json:"timestamp"`
}

func NewRTBStrategyDailyBudgetEvent() *RTBStrategyDailyBudgetEvent {
	return &RTBStrategyDailyBudgetEvent{
		BudgetType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBStrategyDailyBudgetEvent) IsSetBudgetType() bool {
	return int64(p.BudgetType) != math.MinInt32-1
}

func (p *RTBStrategyDailyBudgetEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.BudgetType = rtb_adinfo_types.BudgetType(v)
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.DailyConsumed = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBStrategyDailyBudgetEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBStrategyDailyBudgetEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sponsor_id: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_id: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:strategy_id: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetBudgetType() {
		if err := oprot.WriteFieldBegin("budget_type", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:budget_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BudgetType)); err != nil {
			return fmt.Errorf("%T.budget_type (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:budget_type: %s", p, err)
		}
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("daily_budget", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:daily_budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.daily_budget (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:daily_budget: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("daily_consumed", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:daily_consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyConsumed)); err != nil {
		return fmt.Errorf("%T.daily_consumed (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:daily_consumed: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:timestamp: %s", p, err)
	}
	return err
}

func (p *RTBStrategyDailyBudgetEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBStrategyDailyBudgetEvent(%+v)", *p)
}

type RTBPoiInfoUpdateEvent struct {
	Id int32 `thrift:"id,1" json:"id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Poi *rtb_adinfo_types.RTBPoiInfo `thrift:"poi,20" json:"poi"`
}

func NewRTBPoiInfoUpdateEvent() *RTBPoiInfoUpdateEvent {
	return &RTBPoiInfoUpdateEvent{}
}

func (p *RTBPoiInfoUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBPoiInfoUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *RTBPoiInfoUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBPoiInfoUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.Poi = rtb_adinfo_types.NewRTBPoiInfo()
	if err := p.Poi.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Poi)
	}
	return nil
}

func (p *RTBPoiInfoUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBPoiInfoUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBPoiInfoUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *RTBPoiInfoUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBPoiInfoUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Poi != nil {
		if err := oprot.WriteFieldBegin("poi", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:poi: %s", p, err)
		}
		if err := p.Poi.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Poi)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:poi: %s", p, err)
		}
	}
	return err
}

func (p *RTBPoiInfoUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBPoiInfoUpdateEvent(%+v)", *p)
}

type RTBPoiGroupUpdateEvent struct {
	GroupId int32 `thrift:"group_id,1" json:"group_id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Runnable bool `thrift:"runnable,10" json:"runnable"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	PoiGroup *rtb_adinfo_types.RTBPoiGroup `thrift:"poi_group,20" json:"poi_group"`
}

func NewRTBPoiGroupUpdateEvent() *RTBPoiGroupUpdateEvent {
	return &RTBPoiGroupUpdateEvent{}
}

func (p *RTBPoiGroupUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBPoiGroupUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.GroupId = v
	}
	return nil
}

func (p *RTBPoiGroupUpdateEvent) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBPoiGroupUpdateEvent) readField20(iprot thrift.TProtocol) error {
	p.PoiGroup = rtb_adinfo_types.NewRTBPoiGroup()
	if err := p.PoiGroup.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PoiGroup)
	}
	return nil
}

func (p *RTBPoiGroupUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBPoiGroupUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBPoiGroupUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("group_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:group_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GroupId)); err != nil {
		return fmt.Errorf("%T.group_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:group_id: %s", p, err)
	}
	return err
}

func (p *RTBPoiGroupUpdateEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:runnable: %s", p, err)
	}
	return err
}

func (p *RTBPoiGroupUpdateEvent) writeField20(oprot thrift.TProtocol) (err error) {
	if p.PoiGroup != nil {
		if err := oprot.WriteFieldBegin("poi_group", thrift.STRUCT, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:poi_group: %s", p, err)
		}
		if err := p.PoiGroup.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PoiGroup)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:poi_group: %s", p, err)
		}
	}
	return err
}

func (p *RTBPoiGroupUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBPoiGroupUpdateEvent(%+v)", *p)
}

type RTBAdTrackingUpdateEvent struct {
	AdTrackingId int32                            `thrift:"ad_tracking_id,1" json:"ad_tracking_id"`
	AdTracking   *rtb_adinfo_types.AdTrackingInfo `thrift:"ad_tracking,2" json:"ad_tracking"`
	Runnable     bool                             `thrift:"runnable,3" json:"runnable"`
}

func NewRTBAdTrackingUpdateEvent() *RTBAdTrackingUpdateEvent {
	return &RTBAdTrackingUpdateEvent{}
}

func (p *RTBAdTrackingUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBAdTrackingUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdTrackingId = v
	}
	return nil
}

func (p *RTBAdTrackingUpdateEvent) readField2(iprot thrift.TProtocol) error {
	p.AdTracking = rtb_adinfo_types.NewAdTrackingInfo()
	if err := p.AdTracking.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdTracking)
	}
	return nil
}

func (p *RTBAdTrackingUpdateEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Runnable = v
	}
	return nil
}

func (p *RTBAdTrackingUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBAdTrackingUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBAdTrackingUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_tracking_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ad_tracking_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdTrackingId)); err != nil {
		return fmt.Errorf("%T.ad_tracking_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ad_tracking_id: %s", p, err)
	}
	return err
}

func (p *RTBAdTrackingUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AdTracking != nil {
		if err := oprot.WriteFieldBegin("ad_tracking", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ad_tracking: %s", p, err)
		}
		if err := p.AdTracking.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdTracking)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ad_tracking: %s", p, err)
		}
	}
	return err
}

func (p *RTBAdTrackingUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runnable", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:runnable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Runnable)); err != nil {
		return fmt.Errorf("%T.runnable (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:runnable: %s", p, err)
	}
	return err
}

func (p *RTBAdTrackingUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBAdTrackingUpdateEvent(%+v)", *p)
}

type RTBAdExportCommandEvent struct {
	ExtType string `thrift:"ext_type,1" json:"ext_type"`
}

func NewRTBAdExportCommandEvent() *RTBAdExportCommandEvent {
	return &RTBAdExportCommandEvent{}
}

func (p *RTBAdExportCommandEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBAdExportCommandEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExtType = v
	}
	return nil
}

func (p *RTBAdExportCommandEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBAdExportCommandEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBAdExportCommandEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext_type", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ext_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtType)); err != nil {
		return fmt.Errorf("%T.ext_type (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ext_type: %s", p, err)
	}
	return err
}

func (p *RTBAdExportCommandEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBAdExportCommandEvent(%+v)", *p)
}
