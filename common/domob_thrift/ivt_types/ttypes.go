// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package ivt_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//视频转换状态
type VideoTranscodingStatus int64

const (
	VideoTranscodingStatus_VTS_UNKNOWN     VideoTranscodingStatus = 0
	VideoTranscodingStatus_VTS_PENDING     VideoTranscodingStatus = 1
	VideoTranscodingStatus_VTS_TRANSCODING VideoTranscodingStatus = 2
	VideoTranscodingStatus_VTS_COMPLETE    VideoTranscodingStatus = 3
	VideoTranscodingStatus_VTS_FAIL        VideoTranscodingStatus = 4
)

func (p VideoTranscodingStatus) String() string {
	switch p {
	case VideoTranscodingStatus_VTS_UNKNOWN:
		return "VideoTranscodingStatus_VTS_UNKNOWN"
	case VideoTranscodingStatus_VTS_PENDING:
		return "VideoTranscodingStatus_VTS_PENDING"
	case VideoTranscodingStatus_VTS_TRANSCODING:
		return "VideoTranscodingStatus_VTS_TRANSCODING"
	case VideoTranscodingStatus_VTS_COMPLETE:
		return "VideoTranscodingStatus_VTS_COMPLETE"
	case VideoTranscodingStatus_VTS_FAIL:
		return "VideoTranscodingStatus_VTS_FAIL"
	}
	return "<UNSET>"
}

func VideoTranscodingStatusFromString(s string) (VideoTranscodingStatus, error) {
	switch s {
	case "VideoTranscodingStatus_VTS_UNKNOWN":
		return VideoTranscodingStatus_VTS_UNKNOWN, nil
	case "VideoTranscodingStatus_VTS_PENDING":
		return VideoTranscodingStatus_VTS_PENDING, nil
	case "VideoTranscodingStatus_VTS_TRANSCODING":
		return VideoTranscodingStatus_VTS_TRANSCODING, nil
	case "VideoTranscodingStatus_VTS_COMPLETE":
		return VideoTranscodingStatus_VTS_COMPLETE, nil
	case "VideoTranscodingStatus_VTS_FAIL":
		return VideoTranscodingStatus_VTS_FAIL, nil
	}
	return VideoTranscodingStatus(math.MinInt32 - 1), fmt.Errorf("not a valid VideoTranscodingStatus string")
}

type Video struct {
	Id         int32                  `thrift:"id,1" json:"id"`
	Name       string                 `thrift:"name,2" json:"name"`
	TemplateId int32                  `thrift:"templateId,3" json:"templateId"`
	Size       int32                  `thrift:"size,4" json:"size"`
	FileSize   int32                  `thrift:"fileSize,5" json:"fileSize"`
	Duration   int32                  `thrift:"duration,6" json:"duration"`
	AudioUrl   string                 `thrift:"audioUrl,7" json:"audioUrl"`
	VideoUrl   string                 `thrift:"videoUrl,8" json:"videoUrl"`
	Scene      string                 `thrift:"scene,9" json:"scene"`
	Status     VideoTranscodingStatus `thrift:"status,10" json:"status"`
}

func NewVideo() *Video {
	return &Video{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Video) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Video) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Video) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Video) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Video) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *Video) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *Video) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.FileSize = v
	}
	return nil
}

func (p *Video) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *Video) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AudioUrl = v
	}
	return nil
}

func (p *Video) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.VideoUrl = v
	}
	return nil
}

func (p *Video) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Scene = v
	}
	return nil
}

func (p *Video) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = VideoTranscodingStatus(v)
	}
	return nil
}

func (p *Video) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Video"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Video) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Video) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Video) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:templateId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.templateId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:templateId: %s", p, err)
	}
	return err
}

func (p *Video) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *Video) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileSize", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:fileSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileSize)); err != nil {
		return fmt.Errorf("%T.fileSize (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:fileSize: %s", p, err)
	}
	return err
}

func (p *Video) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:duration: %s", p, err)
	}
	return err
}

func (p *Video) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("audioUrl", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:audioUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AudioUrl)); err != nil {
		return fmt.Errorf("%T.audioUrl (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:audioUrl: %s", p, err)
	}
	return err
}

func (p *Video) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoUrl", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:videoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoUrl)); err != nil {
		return fmt.Errorf("%T.videoUrl (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:videoUrl: %s", p, err)
	}
	return err
}

func (p *Video) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("scene", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:scene: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Scene)); err != nil {
		return fmt.Errorf("%T.scene (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:scene: %s", p, err)
	}
	return err
}

func (p *Video) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:status: %s", p, err)
		}
	}
	return err
}

func (p *Video) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Video(%+v)", *p)
}
