// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mqbroker_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type FileReadyEvent struct {
	FilePath  string           `thrift:"filePath,1" json:"filePath"`
	BeginTime common.TimeInt   `thrift:"beginTime,2" json:"beginTime"`
	EndTime   common.TimeInt   `thrift:"endTime,3" json:"endTime"`
	ObjectNum map[string]int32 `thrift:"objectNum,4" json:"objectNum"`
	Producer  string           `thrift:"producer,5" json:"producer"`
}

func NewFileReadyEvent() *FileReadyEvent {
	return &FileReadyEvent{}
}

func (p *FileReadyEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FileReadyEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FilePath = v
	}
	return nil
}

func (p *FileReadyEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BeginTime = common.TimeInt(v)
	}
	return nil
}

func (p *FileReadyEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.EndTime = common.TimeInt(v)
	}
	return nil
}

func (p *FileReadyEvent) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ObjectNum = make(map[string]int32, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.ObjectNum[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *FileReadyEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Producer = v
	}
	return nil
}

func (p *FileReadyEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FileReadyEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FileReadyEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filePath", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:filePath: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FilePath)); err != nil {
		return fmt.Errorf("%T.filePath (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:filePath: %s", p, err)
	}
	return err
}

func (p *FileReadyEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beginTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:beginTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.BeginTime)); err != nil {
		return fmt.Errorf("%T.beginTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:beginTime: %s", p, err)
	}
	return err
}

func (p *FileReadyEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:endTime: %s", p, err)
	}
	return err
}

func (p *FileReadyEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ObjectNum != nil {
		if err := oprot.WriteFieldBegin("objectNum", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:objectNum: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.ObjectNum)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ObjectNum {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:objectNum: %s", p, err)
		}
	}
	return err
}

func (p *FileReadyEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("producer", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:producer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Producer)); err != nil {
		return fmt.Errorf("%T.producer (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:producer: %s", p, err)
	}
	return err
}

func (p *FileReadyEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileReadyEvent(%+v)", *p)
}
