// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"zeus_user"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i64 addUser(RequestHeader header, ZeusUser user)")
	fmt.Fprintln(os.Stderr, "  i64 modifyUserRole(RequestHeader header, i64 id, UserRole role)")
	fmt.Fprintln(os.<PERSON>, "  i64 deleteUser(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  i64 connectFacebook(RequestHeader header, ZeusFbUser facebook_user)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn connectFacebooks(RequestHeader header,  facebook_users)")
	fmt.Fprintln(os.Stderr, "  i64 updateAccessToken(RequestHeader header, i64 facebook_user_id, string access_token)")
	fmt.Fprintln(os.Stderr, "   getAccessTokensByFbUserIds(RequestHeader header,  fb_user_ids)")
	fmt.Fprintln(os.Stderr, "   getAdAccountByFacebookUser(RequestHeader header, i64 facebook_user_id)")
	fmt.Fprintln(os.Stderr, "   getAdAccounts(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getFacebookUserList(RequestHeader header, i64 zeus_user_id)")
	fmt.Fprintln(os.Stderr, "  FbUser getFbUserById(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteFacebookUser(RequestHeader header, i64 facebook_user_id)")
	fmt.Fprintln(os.Stderr, "  string getAccessTokenByFbUserId(RequestHeader header, i64 fb_user_id)")
	fmt.Fprintln(os.Stderr, "   getAccessTokensByAccountIds(RequestHeader header,  account_ids)")
	fmt.Fprintln(os.Stderr, "  i16 getTimezoneOffsetHoursUTCByAccountId(RequestHeader header, i64 account_id)")
	fmt.Fprintln(os.Stderr, "  i64 changeZeusUserPwd(RequestHeader header, i64 zeus_user_id, string new_pwd)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := zeus_user.NewZeusUserServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddUser requires 2 args")
			flag.Usage()
		}
		arg75 := flag.Arg(1)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue0 := zeus_user.NewRequestHeader()
		err80 := argvalue0.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg81 := flag.Arg(2)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue1 := zeus_user.NewZeusUser()
		err86 := argvalue1.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddUser(value0, value1))
		fmt.Print("\n")
		break
	case "modifyUserRole":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ModifyUserRole requires 3 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := zeus_user.NewRequestHeader()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err93 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err93 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_user.UserRole(tmp2)
		value2 := argvalue2
		fmt.Print(client.ModifyUserRole(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteUser requires 2 args")
			flag.Usage()
		}
		arg94 := flag.Arg(1)
		mbTrans95 := thrift.NewTMemoryBufferLen(len(arg94))
		defer mbTrans95.Close()
		_, err96 := mbTrans95.WriteString(arg94)
		if err96 != nil {
			Usage()
			return
		}
		factory97 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt98 := factory97.GetProtocol(mbTrans95)
		argvalue0 := zeus_user.NewRequestHeader()
		err99 := argvalue0.Read(jsProt98)
		if err99 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err100 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err100 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DeleteUser(value0, value1))
		fmt.Print("\n")
		break
	case "connectFacebook":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ConnectFacebook requires 2 args")
			flag.Usage()
		}
		arg101 := flag.Arg(1)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		argvalue0 := zeus_user.NewRequestHeader()
		err106 := argvalue0.Read(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg107 := flag.Arg(2)
		mbTrans108 := thrift.NewTMemoryBufferLen(len(arg107))
		defer mbTrans108.Close()
		_, err109 := mbTrans108.WriteString(arg107)
		if err109 != nil {
			Usage()
			return
		}
		factory110 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt111 := factory110.GetProtocol(mbTrans108)
		argvalue1 := zeus_user.NewZeusFbUser()
		err112 := argvalue1.Read(jsProt111)
		if err112 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ConnectFacebook(value0, value1))
		fmt.Print("\n")
		break
	case "connectFacebooks":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ConnectFacebooks requires 2 args")
			flag.Usage()
		}
		arg113 := flag.Arg(1)
		mbTrans114 := thrift.NewTMemoryBufferLen(len(arg113))
		defer mbTrans114.Close()
		_, err115 := mbTrans114.WriteString(arg113)
		if err115 != nil {
			Usage()
			return
		}
		factory116 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt117 := factory116.GetProtocol(mbTrans114)
		argvalue0 := zeus_user.NewRequestHeader()
		err118 := argvalue0.Read(jsProt117)
		if err118 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg119 := flag.Arg(2)
		mbTrans120 := thrift.NewTMemoryBufferLen(len(arg119))
		defer mbTrans120.Close()
		_, err121 := mbTrans120.WriteString(arg119)
		if err121 != nil {
			Usage()
			return
		}
		factory122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt123 := factory122.GetProtocol(mbTrans120)
		containerStruct1 := zeus_user.NewConnectFacebooksArgs()
		err124 := containerStruct1.ReadField2(jsProt123)
		if err124 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.FacebookUsers
		value1 := argvalue1
		fmt.Print(client.ConnectFacebooks(value0, value1))
		fmt.Print("\n")
		break
	case "updateAccessToken":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateAccessToken requires 3 args")
			flag.Usage()
		}
		arg125 := flag.Arg(1)
		mbTrans126 := thrift.NewTMemoryBufferLen(len(arg125))
		defer mbTrans126.Close()
		_, err127 := mbTrans126.WriteString(arg125)
		if err127 != nil {
			Usage()
			return
		}
		factory128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt129 := factory128.GetProtocol(mbTrans126)
		argvalue0 := zeus_user.NewRequestHeader()
		err130 := argvalue0.Read(jsProt129)
		if err130 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err131 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err131 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.UpdateAccessToken(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAccessTokensByFbUserIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAccessTokensByFbUserIds requires 2 args")
			flag.Usage()
		}
		arg133 := flag.Arg(1)
		mbTrans134 := thrift.NewTMemoryBufferLen(len(arg133))
		defer mbTrans134.Close()
		_, err135 := mbTrans134.WriteString(arg133)
		if err135 != nil {
			Usage()
			return
		}
		factory136 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt137 := factory136.GetProtocol(mbTrans134)
		argvalue0 := zeus_user.NewRequestHeader()
		err138 := argvalue0.Read(jsProt137)
		if err138 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg139 := flag.Arg(2)
		mbTrans140 := thrift.NewTMemoryBufferLen(len(arg139))
		defer mbTrans140.Close()
		_, err141 := mbTrans140.WriteString(arg139)
		if err141 != nil {
			Usage()
			return
		}
		factory142 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt143 := factory142.GetProtocol(mbTrans140)
		containerStruct1 := zeus_user.NewGetAccessTokensByFbUserIdsArgs()
		err144 := containerStruct1.ReadField2(jsProt143)
		if err144 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.FbUserIds
		value1 := argvalue1
		fmt.Print(client.GetAccessTokensByFbUserIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAdAccountByFacebookUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdAccountByFacebookUser requires 2 args")
			flag.Usage()
		}
		arg145 := flag.Arg(1)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		argvalue0 := zeus_user.NewRequestHeader()
		err150 := argvalue0.Read(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err151 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err151 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAdAccountByFacebookUser(value0, value1))
		fmt.Print("\n")
		break
	case "getAdAccounts":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAdAccounts requires 1 args")
			flag.Usage()
		}
		arg152 := flag.Arg(1)
		mbTrans153 := thrift.NewTMemoryBufferLen(len(arg152))
		defer mbTrans153.Close()
		_, err154 := mbTrans153.WriteString(arg152)
		if err154 != nil {
			Usage()
			return
		}
		factory155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt156 := factory155.GetProtocol(mbTrans153)
		argvalue0 := zeus_user.NewRequestHeader()
		err157 := argvalue0.Read(jsProt156)
		if err157 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetAdAccounts(value0))
		fmt.Print("\n")
		break
	case "getFacebookUserList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFacebookUserList requires 2 args")
			flag.Usage()
		}
		arg158 := flag.Arg(1)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue0 := zeus_user.NewRequestHeader()
		err163 := argvalue0.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err164 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err164 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFacebookUserList(value0, value1))
		fmt.Print("\n")
		break
	case "getFbUserById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFbUserById requires 2 args")
			flag.Usage()
		}
		arg165 := flag.Arg(1)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		argvalue0 := zeus_user.NewRequestHeader()
		err170 := argvalue0.Read(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err171 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err171 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFbUserById(value0, value1))
		fmt.Print("\n")
		break
	case "deleteFacebookUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteFacebookUser requires 2 args")
			flag.Usage()
		}
		arg172 := flag.Arg(1)
		mbTrans173 := thrift.NewTMemoryBufferLen(len(arg172))
		defer mbTrans173.Close()
		_, err174 := mbTrans173.WriteString(arg172)
		if err174 != nil {
			Usage()
			return
		}
		factory175 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt176 := factory175.GetProtocol(mbTrans173)
		argvalue0 := zeus_user.NewRequestHeader()
		err177 := argvalue0.Read(jsProt176)
		if err177 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err178 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err178 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DeleteFacebookUser(value0, value1))
		fmt.Print("\n")
		break
	case "getAccessTokenByFbUserId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAccessTokenByFbUserId requires 2 args")
			flag.Usage()
		}
		arg179 := flag.Arg(1)
		mbTrans180 := thrift.NewTMemoryBufferLen(len(arg179))
		defer mbTrans180.Close()
		_, err181 := mbTrans180.WriteString(arg179)
		if err181 != nil {
			Usage()
			return
		}
		factory182 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt183 := factory182.GetProtocol(mbTrans180)
		argvalue0 := zeus_user.NewRequestHeader()
		err184 := argvalue0.Read(jsProt183)
		if err184 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err185 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err185 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAccessTokenByFbUserId(value0, value1))
		fmt.Print("\n")
		break
	case "getAccessTokensByAccountIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAccessTokensByAccountIds requires 2 args")
			flag.Usage()
		}
		arg186 := flag.Arg(1)
		mbTrans187 := thrift.NewTMemoryBufferLen(len(arg186))
		defer mbTrans187.Close()
		_, err188 := mbTrans187.WriteString(arg186)
		if err188 != nil {
			Usage()
			return
		}
		factory189 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt190 := factory189.GetProtocol(mbTrans187)
		argvalue0 := zeus_user.NewRequestHeader()
		err191 := argvalue0.Read(jsProt190)
		if err191 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg192 := flag.Arg(2)
		mbTrans193 := thrift.NewTMemoryBufferLen(len(arg192))
		defer mbTrans193.Close()
		_, err194 := mbTrans193.WriteString(arg192)
		if err194 != nil {
			Usage()
			return
		}
		factory195 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt196 := factory195.GetProtocol(mbTrans193)
		containerStruct1 := zeus_user.NewGetAccessTokensByAccountIdsArgs()
		err197 := containerStruct1.ReadField2(jsProt196)
		if err197 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AccountIds
		value1 := argvalue1
		fmt.Print(client.GetAccessTokensByAccountIds(value0, value1))
		fmt.Print("\n")
		break
	case "getTimezoneOffsetHoursUTCByAccountId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTimezoneOffsetHoursUTCByAccountId requires 2 args")
			flag.Usage()
		}
		arg198 := flag.Arg(1)
		mbTrans199 := thrift.NewTMemoryBufferLen(len(arg198))
		defer mbTrans199.Close()
		_, err200 := mbTrans199.WriteString(arg198)
		if err200 != nil {
			Usage()
			return
		}
		factory201 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt202 := factory201.GetProtocol(mbTrans199)
		argvalue0 := zeus_user.NewRequestHeader()
		err203 := argvalue0.Read(jsProt202)
		if err203 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err204 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err204 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTimezoneOffsetHoursUTCByAccountId(value0, value1))
		fmt.Print("\n")
		break
	case "changeZeusUserPwd":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ChangeZeusUserPwd requires 3 args")
			flag.Usage()
		}
		arg205 := flag.Arg(1)
		mbTrans206 := thrift.NewTMemoryBufferLen(len(arg205))
		defer mbTrans206.Close()
		_, err207 := mbTrans206.WriteString(arg205)
		if err207 != nil {
			Usage()
			return
		}
		factory208 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt209 := factory208.GetProtocol(mbTrans206)
		argvalue0 := zeus_user.NewRequestHeader()
		err210 := argvalue0.Read(jsProt209)
		if err210 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err211 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err211 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.ChangeZeusUserPwd(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err217 := (strconv.Atoi(flag.Arg(1)))
		if err217 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
