// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dos_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dsp_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dsp_types.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("暂停开关状态")
type PauseStatus int64

const (
	PauseStatus_PAST_RUNNABLE PauseStatus = 0
	PauseStatus_PAST_PAUSED   PauseStatus = 1
)

func (p PauseStatus) String() string {
	switch p {
	case PauseStatus_PAST_RUNNABLE:
		return "PauseStatus_PAST_RUNNABLE"
	case PauseStatus_PAST_PAUSED:
		return "PauseStatus_PAST_PAUSED"
	}
	return "<UNSET>"
}

func PauseStatusFromString(s string) (PauseStatus, error) {
	switch s {
	case "PauseStatus_PAST_RUNNABLE":
		return PauseStatus_PAST_RUNNABLE, nil
	case "PauseStatus_PAST_PAUSED":
		return PauseStatus_PAST_PAUSED, nil
	}
	return PauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PauseStatus string")
}

//计划状态开关
type PlanStatus int64

const (
	PlanStatus_CS_RUNNABLE PlanStatus = 0
	PlanStatus_CS_DELETED  PlanStatus = 1
)

func (p PlanStatus) String() string {
	switch p {
	case PlanStatus_CS_RUNNABLE:
		return "PlanStatus_CS_RUNNABLE"
	case PlanStatus_CS_DELETED:
		return "PlanStatus_CS_DELETED"
	}
	return "<UNSET>"
}

func PlanStatusFromString(s string) (PlanStatus, error) {
	switch s {
	case "PlanStatus_CS_RUNNABLE":
		return PlanStatus_CS_RUNNABLE, nil
	case "PlanStatus_CS_DELETED":
		return PlanStatus_CS_DELETED, nil
	}
	return PlanStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PlanStatus string")
}

//策略状态开关
type StrategyStatus int64

const (
	StrategyStatus_SS_RUNNABLE StrategyStatus = 0
	StrategyStatus_SS_DELETED  StrategyStatus = 1
)

func (p StrategyStatus) String() string {
	switch p {
	case StrategyStatus_SS_RUNNABLE:
		return "StrategyStatus_SS_RUNNABLE"
	case StrategyStatus_SS_DELETED:
		return "StrategyStatus_SS_DELETED"
	}
	return "<UNSET>"
}

func StrategyStatusFromString(s string) (StrategyStatus, error) {
	switch s {
	case "StrategyStatus_SS_RUNNABLE":
		return StrategyStatus_SS_RUNNABLE, nil
	case "StrategyStatus_SS_DELETED":
		return StrategyStatus_SS_DELETED, nil
	}
	return StrategyStatus(math.MinInt32 - 1), fmt.Errorf("not a valid StrategyStatus string")
}

//创意状态 *
type CreativeStatus int64

const (
	CreativeStatus_CS_RUNNABLE CreativeStatus = 0
	CreativeStatus_CS_DELETED  CreativeStatus = 1
)

func (p CreativeStatus) String() string {
	switch p {
	case CreativeStatus_CS_RUNNABLE:
		return "CreativeStatus_CS_RUNNABLE"
	case CreativeStatus_CS_DELETED:
		return "CreativeStatus_CS_DELETED"
	}
	return "<UNSET>"
}

func CreativeStatusFromString(s string) (CreativeStatus, error) {
	switch s {
	case "CreativeStatus_CS_RUNNABLE":
		return CreativeStatus_CS_RUNNABLE, nil
	case "CreativeStatus_CS_DELETED":
		return CreativeStatus_CS_DELETED, nil
	}
	return CreativeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeStatus string")
}

//计划运行状态标记
type PlanRunnableStatus int64

const (
	PlanRunnableStatus_PRS_RUNNABLE    PlanRunnableStatus = 1
	PlanRunnableStatus_PRS_PLAN_PAUSED PlanRunnableStatus = 2
)

func (p PlanRunnableStatus) String() string {
	switch p {
	case PlanRunnableStatus_PRS_RUNNABLE:
		return "PlanRunnableStatus_PRS_RUNNABLE"
	case PlanRunnableStatus_PRS_PLAN_PAUSED:
		return "PlanRunnableStatus_PRS_PLAN_PAUSED"
	}
	return "<UNSET>"
}

func PlanRunnableStatusFromString(s string) (PlanRunnableStatus, error) {
	switch s {
	case "PlanRunnableStatus_PRS_RUNNABLE":
		return PlanRunnableStatus_PRS_RUNNABLE, nil
	case "PlanRunnableStatus_PRS_PLAN_PAUSED":
		return PlanRunnableStatus_PRS_PLAN_PAUSED, nil
	}
	return PlanRunnableStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PlanRunnableStatus string")
}

//活动查询排序字段枚举值
//
type OrderByField int64

const (
	OrderByField_CREATE_TIME OrderByField = 0
)

func (p OrderByField) String() string {
	switch p {
	case OrderByField_CREATE_TIME:
		return "OrderByField_CREATE_TIME"
	}
	return "<UNSET>"
}

func OrderByFieldFromString(s string) (OrderByField, error) {
	switch s {
	case "OrderByField_CREATE_TIME":
		return OrderByField_CREATE_TIME, nil
	}
	return OrderByField(math.MinInt32 - 1), fmt.Errorf("not a valid OrderByField string")
}

//策略查询排序字段枚举值
type StrategyOrderByField int64

const (
	StrategyOrderByField_CREATE_TIME StrategyOrderByField = 0
)

func (p StrategyOrderByField) String() string {
	switch p {
	case StrategyOrderByField_CREATE_TIME:
		return "StrategyOrderByField_CREATE_TIME"
	}
	return "<UNSET>"
}

func StrategyOrderByFieldFromString(s string) (StrategyOrderByField, error) {
	switch s {
	case "StrategyOrderByField_CREATE_TIME":
		return StrategyOrderByField_CREATE_TIME, nil
	}
	return StrategyOrderByField(math.MinInt32 - 1), fmt.Errorf("not a valid StrategyOrderByField string")
}

//创意查询排序字段枚举值
type CreativeOrderByField int64

const (
	CreativeOrderByField_CREATE_TIME CreativeOrderByField = 0
)

func (p CreativeOrderByField) String() string {
	switch p {
	case CreativeOrderByField_CREATE_TIME:
		return "CreativeOrderByField_CREATE_TIME"
	}
	return "<UNSET>"
}

func CreativeOrderByFieldFromString(s string) (CreativeOrderByField, error) {
	switch s {
	case "CreativeOrderByField_CREATE_TIME":
		return CreativeOrderByField_CREATE_TIME, nil
	}
	return CreativeOrderByField(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeOrderByField string")
}

//地域定向
//参考但不沿用enum -> RegionCode
type GeoCityTarget int64

const (
	GeoCityTarget_REGION_UNKNOWN        GeoCityTarget = 0
	GeoCityTarget_REGION_ALL            GeoCityTarget = 1
	GeoCityTarget_REGION_ASIA_CHINA_ALL GeoCityTarget = 10000
	GeoCityTarget_REGION_MAINLAND       GeoCityTarget = 13500
)

func (p GeoCityTarget) String() string {
	switch p {
	case GeoCityTarget_REGION_UNKNOWN:
		return "GeoCityTarget_REGION_UNKNOWN"
	case GeoCityTarget_REGION_ALL:
		return "GeoCityTarget_REGION_ALL"
	case GeoCityTarget_REGION_ASIA_CHINA_ALL:
		return "GeoCityTarget_REGION_ASIA_CHINA_ALL"
	case GeoCityTarget_REGION_MAINLAND:
		return "GeoCityTarget_REGION_MAINLAND"
	}
	return "<UNSET>"
}

func GeoCityTargetFromString(s string) (GeoCityTarget, error) {
	switch s {
	case "GeoCityTarget_REGION_UNKNOWN":
		return GeoCityTarget_REGION_UNKNOWN, nil
	case "GeoCityTarget_REGION_ALL":
		return GeoCityTarget_REGION_ALL, nil
	case "GeoCityTarget_REGION_ASIA_CHINA_ALL":
		return GeoCityTarget_REGION_ASIA_CHINA_ALL, nil
	case "GeoCityTarget_REGION_MAINLAND":
		return GeoCityTarget_REGION_MAINLAND, nil
	}
	return GeoCityTarget(math.MinInt32 - 1), fmt.Errorf("not a valid GeoCityTarget string")
}

//创意运行状态标记
type CreativeRunnableStatus int64

const (
	CreativeRunnableStatus_CRS_RUNNABLE        CreativeRunnableStatus = 1
	CreativeRunnableStatus_CRS_CREATIVE_PAUSED CreativeRunnableStatus = 2
	CreativeRunnableStatus_CRS_PLAN_PAUSED     CreativeRunnableStatus = 3
)

func (p CreativeRunnableStatus) String() string {
	switch p {
	case CreativeRunnableStatus_CRS_RUNNABLE:
		return "CreativeRunnableStatus_CRS_RUNNABLE"
	case CreativeRunnableStatus_CRS_CREATIVE_PAUSED:
		return "CreativeRunnableStatus_CRS_CREATIVE_PAUSED"
	case CreativeRunnableStatus_CRS_PLAN_PAUSED:
		return "CreativeRunnableStatus_CRS_PLAN_PAUSED"
	}
	return "<UNSET>"
}

func CreativeRunnableStatusFromString(s string) (CreativeRunnableStatus, error) {
	switch s {
	case "CreativeRunnableStatus_CRS_RUNNABLE":
		return CreativeRunnableStatus_CRS_RUNNABLE, nil
	case "CreativeRunnableStatus_CRS_CREATIVE_PAUSED":
		return CreativeRunnableStatus_CRS_CREATIVE_PAUSED, nil
	case "CreativeRunnableStatus_CRS_PLAN_PAUSED":
		return CreativeRunnableStatus_CRS_PLAN_PAUSED, nil
	}
	return CreativeRunnableStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeRunnableStatus string")
}

type ProductType dsp_types.PromotionType

type QueryParam struct {
	DspCampaignId      []int32            `thrift:"dspCampaignId,1" json:"dspCampaignId"`
	ChnId              []int32            `thrift:"chnId,2" json:"chnId"`
	PlanId             []int32            `thrift:"planId,3" json:"planId"`
	Name               string             `thrift:"name,4" json:"name"`
	ExcludeDeleted     bool               `thrift:"excludeDeleted,5" json:"excludeDeleted"`
	Consultant         []int32            `thrift:"consultant,6" json:"consultant"`
	PlanRunnableStatus PlanRunnableStatus `thrift:"planRunnableStatus,7" json:"planRunnableStatus"`
}

func NewQueryParam() *QueryParam {
	return &QueryParam{
		PlanRunnableStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryParam) IsSetPlanRunnableStatus() bool {
	return int64(p.PlanRunnableStatus) != math.MinInt32-1
}

func (p *QueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DspCampaignId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.DspCampaignId = append(p.DspCampaignId, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChnId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.ChnId = append(p.ChnId, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlanId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.PlanId = append(p.PlanId, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *QueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExcludeDeleted = v
	}
	return nil
}

func (p *QueryParam) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Consultant = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Consultant = append(p.Consultant, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PlanRunnableStatus = PlanRunnableStatus(v)
	}
	return nil
}

func (p *QueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.DspCampaignId != nil {
		if err := oprot.WriteFieldBegin("dspCampaignId", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dspCampaignId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DspCampaignId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DspCampaignId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dspCampaignId: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ChnId != nil {
		if err := oprot.WriteFieldBegin("chnId", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:chnId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ChnId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChnId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:chnId: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.PlanId != nil {
		if err := oprot.WriteFieldBegin("planId", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:planId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlanId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlanId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:planId: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeDeleted", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:excludeDeleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeDeleted)); err != nil {
		return fmt.Errorf("%T.excludeDeleted (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:excludeDeleted: %s", p, err)
	}
	return err
}

func (p *QueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Consultant != nil {
		if err := oprot.WriteFieldBegin("consultant", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:consultant: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Consultant)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Consultant {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:consultant: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlanRunnableStatus() {
		if err := oprot.WriteFieldBegin("planRunnableStatus", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:planRunnableStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PlanRunnableStatus)); err != nil {
			return fmt.Errorf("%T.planRunnableStatus (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:planRunnableStatus: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryParam(%+v)", *p)
}

type StrategyQueryParam struct {
	PlanId         []int32 `thrift:"planId,1" json:"planId"`
	ExcludeDeleted bool    `thrift:"excludeDeleted,2" json:"excludeDeleted"`
}

func NewStrategyQueryParam() *StrategyQueryParam {
	return &StrategyQueryParam{}
}

func (p *StrategyQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StrategyQueryParam) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlanId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.PlanId = append(p.PlanId, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ExcludeDeleted = v
	}
	return nil
}

func (p *StrategyQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StrategyQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StrategyQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.PlanId != nil {
		if err := oprot.WriteFieldBegin("planId", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:planId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlanId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlanId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:planId: %s", p, err)
		}
	}
	return err
}

func (p *StrategyQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeDeleted", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:excludeDeleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeDeleted)); err != nil {
		return fmt.Errorf("%T.excludeDeleted (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:excludeDeleted: %s", p, err)
	}
	return err
}

func (p *StrategyQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrategyQueryParam(%+v)", *p)
}

type CreativeQueryParam struct {
	PlanId         []int32 `thrift:"planId,1" json:"planId"`
	StrategyId     []int32 `thrift:"StrategyId,2" json:"StrategyId"`
	ExcludeDeleted bool    `thrift:"excludeDeleted,3" json:"excludeDeleted"`
}

func NewCreativeQueryParam() *CreativeQueryParam {
	return &CreativeQueryParam{}
}

func (p *CreativeQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeQueryParam) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlanId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.PlanId = append(p.PlanId, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeQueryParam) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StrategyId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.StrategyId = append(p.StrategyId, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExcludeDeleted = v
	}
	return nil
}

func (p *CreativeQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.PlanId != nil {
		if err := oprot.WriteFieldBegin("planId", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:planId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlanId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlanId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:planId: %s", p, err)
		}
	}
	return err
}

func (p *CreativeQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.StrategyId != nil {
		if err := oprot.WriteFieldBegin("StrategyId", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:StrategyId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.StrategyId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StrategyId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:StrategyId: %s", p, err)
		}
	}
	return err
}

func (p *CreativeQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeDeleted", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:excludeDeleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeDeleted)); err != nil {
		return fmt.Errorf("%T.excludeDeleted (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:excludeDeleted: %s", p, err)
	}
	return err
}

func (p *CreativeQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeQueryParam(%+v)", *p)
}

type OrderParam struct {
	OrderBy OrderByField `thrift:"orderBy,1" json:"orderBy"`
	IsAsc   bool         `thrift:"isAsc,2" json:"isAsc"`
}

func NewOrderParam() *OrderParam {
	return &OrderParam{
		OrderBy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OrderParam) IsSetOrderBy() bool {
	return int64(p.OrderBy) != math.MinInt32-1
}

func (p *OrderParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OrderParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderBy = OrderByField(v)
	}
	return nil
}

func (p *OrderParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsAsc = v
	}
	return nil
}

func (p *OrderParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OrderParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OrderParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err := oprot.WriteFieldBegin("orderBy", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:orderBy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
			return fmt.Errorf("%T.orderBy (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:orderBy: %s", p, err)
		}
	}
	return err
}

func (p *OrderParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isAsc", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isAsc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAsc)); err != nil {
		return fmt.Errorf("%T.isAsc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isAsc: %s", p, err)
	}
	return err
}

func (p *OrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderParam(%+v)", *p)
}

type StrategyOrderParam struct {
	OrderBy StrategyOrderByField `thrift:"orderBy,1" json:"orderBy"`
	IsAsc   bool                 `thrift:"isAsc,2" json:"isAsc"`
}

func NewStrategyOrderParam() *StrategyOrderParam {
	return &StrategyOrderParam{
		OrderBy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *StrategyOrderParam) IsSetOrderBy() bool {
	return int64(p.OrderBy) != math.MinInt32-1
}

func (p *StrategyOrderParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StrategyOrderParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderBy = StrategyOrderByField(v)
	}
	return nil
}

func (p *StrategyOrderParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsAsc = v
	}
	return nil
}

func (p *StrategyOrderParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StrategyOrderParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StrategyOrderParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err := oprot.WriteFieldBegin("orderBy", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:orderBy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
			return fmt.Errorf("%T.orderBy (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:orderBy: %s", p, err)
		}
	}
	return err
}

func (p *StrategyOrderParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isAsc", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isAsc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAsc)); err != nil {
		return fmt.Errorf("%T.isAsc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isAsc: %s", p, err)
	}
	return err
}

func (p *StrategyOrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrategyOrderParam(%+v)", *p)
}

type CreativeOrderParam struct {
	OrderBy CreativeOrderByField `thrift:"orderBy,1" json:"orderBy"`
	IsAsc   bool                 `thrift:"isAsc,2" json:"isAsc"`
}

func NewCreativeOrderParam() *CreativeOrderParam {
	return &CreativeOrderParam{
		OrderBy: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CreativeOrderParam) IsSetOrderBy() bool {
	return int64(p.OrderBy) != math.MinInt32-1
}

func (p *CreativeOrderParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeOrderParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderBy = CreativeOrderByField(v)
	}
	return nil
}

func (p *CreativeOrderParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsAsc = v
	}
	return nil
}

func (p *CreativeOrderParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeOrderParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeOrderParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err := oprot.WriteFieldBegin("orderBy", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:orderBy: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
			return fmt.Errorf("%T.orderBy (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:orderBy: %s", p, err)
		}
	}
	return err
}

func (p *CreativeOrderParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isAsc", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isAsc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAsc)); err != nil {
		return fmt.Errorf("%T.isAsc (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isAsc: %s", p, err)
	}
	return err
}

func (p *CreativeOrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeOrderParam(%+v)", *p)
}

type DOSPlan struct {
	Id             int32             `thrift:"id,1" json:"id"`
	DspCampaignId  int32             `thrift:"dspCampaignId,2" json:"dspCampaignId"`
	Name           string            `thrift:"name,3" json:"name"`
	ChnId          int32             `thrift:"chnId,4" json:"chnId"`
	CostType       enums.CostType    `thrift:"costType,5" json:"costType"`
	TaskFlag       int32             `thrift:"taskFlag,6" json:"taskFlag"`
	SupplementFlag int32             `thrift:"supplementFlag,7" json:"supplementFlag"`
	DailyBudget    int64             `thrift:"dailyBudget,8" json:"dailyBudget"`
	TotalBudget    int64             `thrift:"totalBudget,9" json:"totalBudget"`
	StartTime      int64             `thrift:"startTime,10" json:"startTime"`
	EndTime        int64             `thrift:"endTime,11" json:"endTime"`
	HasAct         bool              `thrift:"hasAct,12" json:"hasAct"`
	RelySource     int32             `thrift:"relySource,13" json:"relySource"`
	ProductId      int32             `thrift:"productId,14" json:"productId"`
	ProductType    ProductType       `thrift:"productType,15" json:"productType"`
	ProductChnId   int32             `thrift:"productChnId,16" json:"productChnId"`
	Consultant     int32             `thrift:"consultant,17" json:"consultant"`
	Attrs          map[string]string `thrift:"attrs,18" json:"attrs"`
	Creator        string            `thrift:"creator,19" json:"creator"`
	ProId          int32             `thrift:"proId,20" json:"proId"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Status     PlanStatus  `thrift:"status,30" json:"status"`
	Paused     PauseStatus `thrift:"paused,31" json:"paused"`
	CreateTime int64       `thrift:"createTime,32" json:"createTime"`
	Lastupdate int64       `thrift:"lastupdate,33" json:"lastupdate"`
}

func NewDOSPlan() *DOSPlan {
	return &DOSPlan{
		CostType: math.MinInt32 - 1, // unset sentinal value

		ProductType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DOSPlan) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *DOSPlan) IsSetProductType() bool {
	return int64(p.ProductType) != math.MinInt32-1
}

func (p *DOSPlan) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DOSPlan) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *DOSPlan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.MAP {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSPlan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DOSPlan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *DOSPlan) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DOSPlan) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *DOSPlan) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CostType = enums.CostType(v)
	}
	return nil
}

func (p *DOSPlan) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TaskFlag = v
	}
	return nil
}

func (p *DOSPlan) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SupplementFlag = v
	}
	return nil
}

func (p *DOSPlan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *DOSPlan) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *DOSPlan) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *DOSPlan) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *DOSPlan) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.HasAct = v
	}
	return nil
}

func (p *DOSPlan) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RelySource = v
	}
	return nil
}

func (p *DOSPlan) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *DOSPlan) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ProductType = ProductType(v)
	}
	return nil
}

func (p *DOSPlan) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ProductChnId = v
	}
	return nil
}

func (p *DOSPlan) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Consultant = v
	}
	return nil
}

func (p *DOSPlan) readField18(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key7 = v
		}
		var _val8 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val8 = v
		}
		p.Attrs[_key7] = _val8
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DOSPlan) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *DOSPlan) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ProId = v
	}
	return nil
}

func (p *DOSPlan) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Status = PlanStatus(v)
	}
	return nil
}

func (p *DOSPlan) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Paused = PauseStatus(v)
	}
	return nil
}

func (p *DOSPlan) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DOSPlan) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DOSPlan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSPlan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSPlan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dspCampaignId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dspCampaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dspCampaignId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dspCampaignId: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:chnId: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCostType() {
		if err := oprot.WriteFieldBegin("costType", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:costType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CostType)); err != nil {
			return fmt.Errorf("%T.costType (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:costType: %s", p, err)
		}
	}
	return err
}

func (p *DOSPlan) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskFlag", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:taskFlag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskFlag)); err != nil {
		return fmt.Errorf("%T.taskFlag (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:taskFlag: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("supplementFlag", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:supplementFlag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SupplementFlag)); err != nil {
		return fmt.Errorf("%T.supplementFlag (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:supplementFlag: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudget", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:dailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.dailyBudget (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:dailyBudget: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:totalBudget: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:startTime: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:endTime: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hasAct", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:hasAct: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.HasAct)); err != nil {
		return fmt.Errorf("%T.hasAct (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:hasAct: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("relySource", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:relySource: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RelySource)); err != nil {
		return fmt.Errorf("%T.relySource (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:relySource: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productId", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:productId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductId)); err != nil {
		return fmt.Errorf("%T.productId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:productId: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productType", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:productType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductType)); err != nil {
		return fmt.Errorf("%T.productType (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:productType: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productChnId", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:productChnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductChnId)); err != nil {
		return fmt.Errorf("%T.productChnId (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:productChnId: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consultant", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:consultant: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Consultant)); err != nil {
		return fmt.Errorf("%T.consultant (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:consultant: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField18(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:attrs: %s", p, err)
		}
	}
	return err
}

func (p *DOSPlan) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:creator: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:creator: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("proId", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:proId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProId)); err != nil {
		return fmt.Errorf("%T.proId (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:proId: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:status: %s", p, err)
		}
	}
	return err
}

func (p *DOSPlan) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:paused: %s", p, err)
		}
	}
	return err
}

func (p *DOSPlan) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *DOSPlan) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastupdate: %s", p, err)
	}
	return err
}

func (p *DOSPlan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSPlan(%+v)", *p)
}

type DOSStrategy struct {
	Id                    int32          `thrift:"id,1" json:"id"`
	Name                  string         `thrift:"name,2" json:"name"`
	PlanId                int32          `thrift:"planId,3" json:"planId"`
	DeviceTarget          int32          `thrift:"deviceTarget,4" json:"deviceTarget"`
	MinOsvTarget          int32          `thrift:"min_osv_target,5" json:"min_osv_target"`
	AccessTarget          []int32        `thrift:"accessTarget,6" json:"accessTarget"`
	StartTime             int64          `thrift:"startTime,7" json:"startTime"`
	EndTime               int64          `thrift:"endTime,8" json:"endTime"`
	GeoCityTarget         GeoCityTarget  `thrift:"geoCityTarget,9" json:"geoCityTarget"`
	CostType              enums.CostType `thrift:"costType,10" json:"costType"`
	SettledAdPriceRmb     int64          `thrift:"settledAdPriceRmb,11" json:"settledAdPriceRmb"`
	SettledAdPriceDollar  int64          `thrift:"settledAdPriceDollar,12" json:"settledAdPriceDollar"`
	TotalBudgetRmb        int64          `thrift:"totalBudgetRmb,13" json:"totalBudgetRmb"`
	TotalBudgetDollar     int64          `thrift:"totalBudgetDollar,14" json:"totalBudgetDollar"`
	DailyBudgetRmb        int64          `thrift:"dailyBudgetRmb,15" json:"dailyBudgetRmb"`
	DailyBudgetDollar     int64          `thrift:"dailyBudgetDollar,16" json:"dailyBudgetDollar"`
	MediaChargeRateRmb    int64          `thrift:"mediaChargeRateRmb,17" json:"mediaChargeRateRmb"`
	MediaChargeRateDollar int64          `thrift:"mediaChargeRateDollar,18" json:"mediaChargeRateDollar"`
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Status     StrategyStatus `thrift:"status,30" json:"status"`
	Paused     PauseStatus    `thrift:"paused,31" json:"paused"`
	CreateTime int64          `thrift:"createTime,32" json:"createTime"`
	Lastupdate int64          `thrift:"lastupdate,33" json:"lastupdate"`
}

func NewDOSStrategy() *DOSStrategy {
	return &DOSStrategy{
		GeoCityTarget: math.MinInt32 - 1, // unset sentinal value

		CostType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DOSStrategy) IsSetGeoCityTarget() bool {
	return int64(p.GeoCityTarget) != math.MinInt32-1
}

func (p *DOSStrategy) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *DOSStrategy) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DOSStrategy) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *DOSStrategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSStrategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DOSStrategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DOSStrategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSStrategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DeviceTarget = v
	}
	return nil
}

func (p *DOSStrategy) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MinOsvTarget = v
	}
	return nil
}

func (p *DOSStrategy) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccessTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.AccessTarget = append(p.AccessTarget, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSStrategy) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *DOSStrategy) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *DOSStrategy) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.GeoCityTarget = GeoCityTarget(v)
	}
	return nil
}

func (p *DOSStrategy) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CostType = enums.CostType(v)
	}
	return nil
}

func (p *DOSStrategy) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SettledAdPriceRmb = v
	}
	return nil
}

func (p *DOSStrategy) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SettledAdPriceDollar = v
	}
	return nil
}

func (p *DOSStrategy) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.TotalBudgetRmb = v
	}
	return nil
}

func (p *DOSStrategy) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.TotalBudgetDollar = v
	}
	return nil
}

func (p *DOSStrategy) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DailyBudgetRmb = v
	}
	return nil
}

func (p *DOSStrategy) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.DailyBudgetDollar = v
	}
	return nil
}

func (p *DOSStrategy) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.MediaChargeRateRmb = v
	}
	return nil
}

func (p *DOSStrategy) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.MediaChargeRateDollar = v
	}
	return nil
}

func (p *DOSStrategy) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Status = StrategyStatus(v)
	}
	return nil
}

func (p *DOSStrategy) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Paused = PauseStatus(v)
	}
	return nil
}

func (p *DOSStrategy) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DOSStrategy) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DOSStrategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSStrategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:planId: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceTarget", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:deviceTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceTarget)); err != nil {
		return fmt.Errorf("%T.deviceTarget (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:deviceTarget: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("min_osv_target", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:min_osv_target: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinOsvTarget)); err != nil {
		return fmt.Errorf("%T.min_osv_target (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:min_osv_target: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField6(oprot thrift.TProtocol) (err error) {
	if p.AccessTarget != nil {
		if err := oprot.WriteFieldBegin("accessTarget", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:accessTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AccessTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccessTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:accessTarget: %s", p, err)
		}
	}
	return err
}

func (p *DOSStrategy) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:startTime: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:endTime: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetGeoCityTarget() {
		if err := oprot.WriteFieldBegin("geoCityTarget", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:geoCityTarget: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.GeoCityTarget)); err != nil {
			return fmt.Errorf("%T.geoCityTarget (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:geoCityTarget: %s", p, err)
		}
	}
	return err
}

func (p *DOSStrategy) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCostType() {
		if err := oprot.WriteFieldBegin("costType", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:costType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CostType)); err != nil {
			return fmt.Errorf("%T.costType (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:costType: %s", p, err)
		}
	}
	return err
}

func (p *DOSStrategy) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settledAdPriceRmb", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:settledAdPriceRmb: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettledAdPriceRmb)); err != nil {
		return fmt.Errorf("%T.settledAdPriceRmb (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:settledAdPriceRmb: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settledAdPriceDollar", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:settledAdPriceDollar: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettledAdPriceDollar)); err != nil {
		return fmt.Errorf("%T.settledAdPriceDollar (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:settledAdPriceDollar: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudgetRmb", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:totalBudgetRmb: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudgetRmb)); err != nil {
		return fmt.Errorf("%T.totalBudgetRmb (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:totalBudgetRmb: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudgetDollar", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:totalBudgetDollar: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudgetDollar)); err != nil {
		return fmt.Errorf("%T.totalBudgetDollar (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:totalBudgetDollar: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudgetRmb", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:dailyBudgetRmb: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudgetRmb)); err != nil {
		return fmt.Errorf("%T.dailyBudgetRmb (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:dailyBudgetRmb: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudgetDollar", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:dailyBudgetDollar: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudgetDollar)); err != nil {
		return fmt.Errorf("%T.dailyBudgetDollar (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:dailyBudgetDollar: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaChargeRateRmb", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:mediaChargeRateRmb: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaChargeRateRmb)); err != nil {
		return fmt.Errorf("%T.mediaChargeRateRmb (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:mediaChargeRateRmb: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaChargeRateDollar", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:mediaChargeRateDollar: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaChargeRateDollar)); err != nil {
		return fmt.Errorf("%T.mediaChargeRateDollar (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:mediaChargeRateDollar: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:status: %s", p, err)
		}
	}
	return err
}

func (p *DOSStrategy) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:paused: %s", p, err)
		}
	}
	return err
}

func (p *DOSStrategy) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastupdate: %s", p, err)
	}
	return err
}

func (p *DOSStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSStrategy(%+v)", *p)
}

type DOSCreative struct {
	Id             int32                  `thrift:"id,1" json:"id"`
	Name           string                 `thrift:"name,2" json:"name"`
	PlanId         int32                  `thrift:"planId,3" json:"planId"`
	StrategyId     int32                  `thrift:"strategyId,4" json:"strategyId"`
	ItunesUrl      string                 `thrift:"itunesUrl,5" json:"itunesUrl"`
	ClickSenderUrl string                 `thrift:"clickSenderUrl,6" json:"clickSenderUrl"`
	TrackingUrl    string                 `thrift:"trackingUrl,7" json:"trackingUrl"`
	StopTime       int64                  `thrift:"stopTime,8" json:"stopTime"`
	Runnable       CreativeRunnableStatus `thrift:"runnable,9" json:"runnable"`
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Status     CreativeStatus `thrift:"status,30" json:"status"`
	Paused     PauseStatus    `thrift:"paused,31" json:"paused"`
	CreateTime int64          `thrift:"createTime,32" json:"createTime"`
	Lastupdate int64          `thrift:"lastupdate,33" json:"lastupdate"`
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	Appkey         string `thrift:"appkey,40" json:"appkey"`
	ImpTrackingUrl string `thrift:"impTrackingUrl,41" json:"impTrackingUrl"`
}

func NewDOSCreative() *DOSCreative {
	return &DOSCreative{
		Runnable: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DOSCreative) IsSetRunnable() bool {
	return int64(p.Runnable) != math.MinInt32-1
}

func (p *DOSCreative) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DOSCreative) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *DOSCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DOSCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DOSCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *DOSCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ItunesUrl = v
	}
	return nil
}

func (p *DOSCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ClickSenderUrl = v
	}
	return nil
}

func (p *DOSCreative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TrackingUrl = v
	}
	return nil
}

func (p *DOSCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StopTime = v
	}
	return nil
}

func (p *DOSCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Runnable = CreativeRunnableStatus(v)
	}
	return nil
}

func (p *DOSCreative) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Status = CreativeStatus(v)
	}
	return nil
}

func (p *DOSCreative) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Paused = PauseStatus(v)
	}
	return nil
}

func (p *DOSCreative) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DOSCreative) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DOSCreative) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Appkey = v
	}
	return nil
}

func (p *DOSCreative) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.ImpTrackingUrl = v
	}
	return nil
}

func (p *DOSCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:planId: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:strategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:strategyId: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:itunesUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesUrl)); err != nil {
		return fmt.Errorf("%T.itunesUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:itunesUrl: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickSenderUrl", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:clickSenderUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickSenderUrl)); err != nil {
		return fmt.Errorf("%T.clickSenderUrl (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:clickSenderUrl: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingUrl", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:trackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackingUrl)); err != nil {
		return fmt.Errorf("%T.trackingUrl (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:trackingUrl: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stopTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:stopTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StopTime)); err != nil {
		return fmt.Errorf("%T.stopTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:stopTime: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRunnable() {
		if err := oprot.WriteFieldBegin("runnable", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:runnable: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Runnable)); err != nil {
			return fmt.Errorf("%T.runnable (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:runnable: %s", p, err)
		}
	}
	return err
}

func (p *DOSCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:status: %s", p, err)
		}
	}
	return err
}

func (p *DOSCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:paused: %s", p, err)
		}
	}
	return err
}

func (p *DOSCreative) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastupdate: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appkey", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:appkey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appkey)); err != nil {
		return fmt.Errorf("%T.appkey (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:appkey: %s", p, err)
	}
	return err
}

func (p *DOSCreative) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impTrackingUrl", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:impTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpTrackingUrl)); err != nil {
		return fmt.Errorf("%T.impTrackingUrl (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:impTrackingUrl: %s", p, err)
	}
	return err
}

func (p *DOSCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSCreative(%+v)", *p)
}

type DOSClk struct {
	PlanId        int32  `thrift:"plan_id,1" json:"plan_id"`
	StrategyId    int32  `thrift:"strategy_id,2" json:"strategy_id"`
	CreativeId    int32  `thrift:"creative_id,3" json:"creative_id"`
	DspCampaignId int64  `thrift:"dsp_campaign_id,4" json:"dsp_campaign_id"`
	ChnId         int32  `thrift:"chn_id,5" json:"chn_id"`
	ProductId     int32  `thrift:"product_id,6" json:"product_id"`
	ProductChnId  int32  `thrift:"product_chn_id,7" json:"product_chn_id"`
	SearchId      int64  `thrift:"search_id,8" json:"search_id"`
	ClkId         int64  `thrift:"clk_id,9" json:"clk_id"`
	ProId         int32  `thrift:"pro_id,10" json:"pro_id"`
	Mac           string `thrift:"mac,11" json:"mac"`
	Macmd5        string `thrift:"macmd5,12" json:"macmd5"`
	Idfa          string `thrift:"idfa,13" json:"idfa"`
	Idfamd5       string `thrift:"idfamd5,14" json:"idfamd5"`
	Imei          string `thrift:"imei,15" json:"imei"`
	Imeimd5       string `thrift:"imeimd5,16" json:"imeimd5"`
	ClkIp         string `thrift:"clk_ip,17" json:"clk_ip"`
	ClkTs         int32  `thrift:"clk_ts,18" json:"clk_ts"`
	Oaidmd5       string `thrift:"oaidmd5,19" json:"oaidmd5"`
	// unused field # 20
	Platform   int32  `thrift:"platform,21" json:"platform"`
	AdPrice    int64  `thrift:"ad_price,22" json:"ad_price"`
	MediaPrice int64  `thrift:"media_price,23" json:"media_price"`
	CostType   int32  `thrift:"cost_type,24" json:"cost_type"`
	UserAgent  string `thrift:"user_agent,25" json:"user_agent"`
	RemoteIp   string `thrift:"remote_ip,26" json:"remote_ip"`
	ExtInfo    string `thrift:"ext_info,27" json:"ext_info"`
	// unused field # 28
	// unused field # 29
	Oaid             string `thrift:"oaid,30" json:"oaid"`
	IsFake           int32  `thrift:"is_fake,31" json:"is_fake"`
	IsRepeated       int32  `thrift:"is_repeated,32" json:"is_repeated"`
	Ua               string `thrift:"ua,33" json:"ua"`
	Location         string `thrift:"location,34" json:"location"`
	Model            string `thrift:"model,35" json:"model"`
	Caid1            string `thrift:"caid1,36" json:"caid1"`
	AndroidId        string `thrift:"android_id,37" json:"android_id"`
	MediaCreativeId  int64  `thrift:"media_creative_id,38" json:"media_creative_id"`
	MediaGroupId     int64  `thrift:"media_group_id,39" json:"media_group_id"`
	MediaPlanId      int64  `thrift:"media_plan_id,40" json:"media_plan_id"`
	MediaAccountId   int64  `thrift:"media_account_id,41" json:"media_account_id"`
	Source           string `thrift:"source,42" json:"source"`
	TraceId          string `thrift:"trace_id,43" json:"trace_id"`
	ClkCheckId       string `thrift:"clk_check_id,44" json:"clk_check_id"`
	Gaid             string `thrift:"gaid,45" json:"gaid"`
	OverseaChannel   string `thrift:"oversea_channel,46" json:"oversea_channel"`
	MediaProjectId   int64  `thrift:"media_project_id,47" json:"media_project_id"`
	MediaPromotionId int64  `thrift:"media_promotion_id,48" json:"media_promotion_id"`
	Media            int64  `thrift:"media,49" json:"media"`
	TempId           int64  `thrift:"temp_id,50" json:"temp_id"`
	OpenId           string `thrift:"open_id,51" json:"open_id"`
}

func NewDOSClk() *DOSClk {
	return &DOSClk{}
}

func (p *DOSClk) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I64 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I64 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.STRING {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I64 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.I64 {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.I64 {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSClk) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSClk) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *DOSClk) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DOSClk) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *DOSClk) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *DOSClk) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *DOSClk) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ProductChnId = v
	}
	return nil
}

func (p *DOSClk) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *DOSClk) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClkId = v
	}
	return nil
}

func (p *DOSClk) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ProId = v
	}
	return nil
}

func (p *DOSClk) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *DOSClk) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *DOSClk) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *DOSClk) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Idfamd5 = v
	}
	return nil
}

func (p *DOSClk) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *DOSClk) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Imeimd5 = v
	}
	return nil
}

func (p *DOSClk) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Oaid = v
	}
	return nil
}

func (p *DOSClk) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Oaidmd5 = v
	}
	return nil
}

func (p *DOSClk) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ClkIp = v
	}
	return nil
}

func (p *DOSClk) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.ClkTs = v
	}
	return nil
}

func (p *DOSClk) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *DOSClk) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AdPrice = v
	}
	return nil
}

func (p *DOSClk) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.MediaPrice = v
	}
	return nil
}

func (p *DOSClk) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *DOSClk) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.UserAgent = v
	}
	return nil
}

func (p *DOSClk) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.RemoteIp = v
	}
	return nil
}

func (p *DOSClk) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *DOSClk) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.IsFake = v
	}
	return nil
}

func (p *DOSClk) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.IsRepeated = v
	}
	return nil
}

func (p *DOSClk) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Ua = v
	}
	return nil
}

func (p *DOSClk) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Location = v
	}
	return nil
}

func (p *DOSClk) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Model = v
	}
	return nil
}

func (p *DOSClk) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Caid1 = v
	}
	return nil
}

func (p *DOSClk) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *DOSClk) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.MediaCreativeId = v
	}
	return nil
}

func (p *DOSClk) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.MediaGroupId = v
	}
	return nil
}

func (p *DOSClk) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.MediaPlanId = v
	}
	return nil
}

func (p *DOSClk) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.MediaAccountId = v
	}
	return nil
}

func (p *DOSClk) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *DOSClk) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.TraceId = v
	}
	return nil
}

func (p *DOSClk) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.ClkCheckId = v
	}
	return nil
}

func (p *DOSClk) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.Gaid = v
	}
	return nil
}

func (p *DOSClk) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.OverseaChannel = v
	}
	return nil
}

func (p *DOSClk) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.MediaProjectId = v
	}
	return nil
}

func (p *DOSClk) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.MediaPromotionId = v
	}
	return nil
}

func (p *DOSClk) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *DOSClk) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.TempId = v
	}
	return nil
}

func (p *DOSClk) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.OpenId = v
	}
	return nil
}

func (p *DOSClk) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSClk"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSClk) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:plan_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:strategy_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creative_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_campaign_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dsp_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dsp_campaign_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dsp_campaign_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chn_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chn_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:chn_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:product_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductId)); err != nil {
		return fmt.Errorf("%T.product_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:product_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_chn_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:product_chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductChnId)); err != nil {
		return fmt.Errorf("%T.product_chn_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:product_chn_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:search_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_id", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:clk_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClkId)); err != nil {
		return fmt.Errorf("%T.clk_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:clk_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pro_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:pro_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProId)); err != nil {
		return fmt.Errorf("%T.pro_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:pro_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:mac: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:macmd5: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:idfa: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfamd5", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:idfamd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfamd5)); err != nil {
		return fmt.Errorf("%T.idfamd5 (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:idfamd5: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:imei: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imeimd5", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:imeimd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imeimd5)); err != nil {
		return fmt.Errorf("%T.imeimd5 (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:imeimd5: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_ip", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:clk_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkIp)); err != nil {
		return fmt.Errorf("%T.clk_ip (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:clk_ip: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_ts", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:clk_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkTs)); err != nil {
		return fmt.Errorf("%T.clk_ts (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:clk_ts: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaidmd5", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:oaidmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaidmd5)); err != nil {
		return fmt.Errorf("%T.oaidmd5 (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:oaidmd5: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:platform: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_price", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:ad_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPrice)); err != nil {
		return fmt.Errorf("%T.ad_price (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:ad_price: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_price", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:media_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.media_price (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:media_price: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:cost_type: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_agent", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:user_agent: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAgent)); err != nil {
		return fmt.Errorf("%T.user_agent (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:user_agent: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remote_ip", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:remote_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RemoteIp)); err != nil {
		return fmt.Errorf("%T.remote_ip (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:remote_ip: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext_info", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:ext_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.ext_info (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:ext_info: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaid", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:oaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaid)); err != nil {
		return fmt.Errorf("%T.oaid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:oaid: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_fake", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:is_fake: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsFake)); err != nil {
		return fmt.Errorf("%T.is_fake (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:is_fake: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_repeated", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:is_repeated: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsRepeated)); err != nil {
		return fmt.Errorf("%T.is_repeated (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:is_repeated: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ua", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ua)); err != nil {
		return fmt.Errorf("%T.ua (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:ua: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("location", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:location: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Location)); err != nil {
		return fmt.Errorf("%T.location (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:location: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Model)); err != nil {
		return fmt.Errorf("%T.model (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:model: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("caid1", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:caid1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Caid1)); err != nil {
		return fmt.Errorf("%T.caid1 (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:caid1: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:android_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_creative_id", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:media_creative_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaCreativeId)); err != nil {
		return fmt.Errorf("%T.media_creative_id (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:media_creative_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_group_id", thrift.I64, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:media_group_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaGroupId)); err != nil {
		return fmt.Errorf("%T.media_group_id (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:media_group_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_plan_id", thrift.I64, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:media_plan_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPlanId)); err != nil {
		return fmt.Errorf("%T.media_plan_id (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:media_plan_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_account_id", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:media_account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaAccountId)); err != nil {
		return fmt.Errorf("%T.media_account_id (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:media_account_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:source: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trace_id", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:trace_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TraceId)); err != nil {
		return fmt.Errorf("%T.trace_id (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:trace_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_check_id", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:clk_check_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkCheckId)); err != nil {
		return fmt.Errorf("%T.clk_check_id (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:clk_check_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gaid", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:gaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Gaid)); err != nil {
		return fmt.Errorf("%T.gaid (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:gaid: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oversea_channel", thrift.STRING, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:oversea_channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OverseaChannel)); err != nil {
		return fmt.Errorf("%T.oversea_channel (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:oversea_channel: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_project_id", thrift.I64, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:media_project_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaProjectId)); err != nil {
		return fmt.Errorf("%T.media_project_id (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:media_project_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_promotion_id", thrift.I64, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:media_promotion_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPromotionId)); err != nil {
		return fmt.Errorf("%T.media_promotion_id (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:media_promotion_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.I64, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:media: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Media)); err != nil {
		return fmt.Errorf("%T.media (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:media: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("temp_id", thrift.I64, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:temp_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TempId)); err != nil {
		return fmt.Errorf("%T.temp_id (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:temp_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("open_id", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:open_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OpenId)); err != nil {
		return fmt.Errorf("%T.open_id (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:open_id: %s", p, err)
	}
	return err
}

func (p *DOSClk) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSClk(%+v)", *p)
}

type DOSThirdParty struct {
	PlanId       int32    `thrift:"planId,1" json:"planId"`
	CampaignId   []int64  `thrift:"campaignId,2" json:"campaignId"`
	AccountId    []int64  `thrift:"accountId,3" json:"accountId"`
	AccountName  string   `thrift:"accountName,4" json:"accountName"`
	Dt           int32    `thrift:"dt,5" json:"dt"`
	CampaignName []string `thrift:"campaignName,6" json:"campaignName"`
	Platform     int32    `thrift:"platform,7" json:"platform"`
	AdgroupId    []int64  `thrift:"adgroupId,8" json:"adgroupId"`
	AdgroupName  []string `thrift:"adgroupName,9" json:"adgroupName"`
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Status     int32 `thrift:"status,31" json:"status"`
	CreateTime int64 `thrift:"createTime,32" json:"createTime"`
	Lastupdate int64 `thrift:"lastupdate,33" json:"lastupdate"`
}

func NewDOSThirdParty() *DOSThirdParty {
	return &DOSThirdParty{}
}

func (p *DOSThirdParty) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSThirdParty) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSThirdParty) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignId = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.CampaignId = append(p.CampaignId, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSThirdParty) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccountId = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.AccountId = append(p.AccountId, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSThirdParty) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccountName = v
	}
	return nil
}

func (p *DOSThirdParty) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *DOSThirdParty) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignName = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.CampaignName = append(p.CampaignName, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSThirdParty) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *DOSThirdParty) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdgroupId = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.AdgroupId = append(p.AdgroupId, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSThirdParty) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdgroupName = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.AdgroupName = append(p.AdgroupName, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSThirdParty) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *DOSThirdParty) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DOSThirdParty) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Lastupdate = v
	}
	return nil
}

func (p *DOSThirdParty) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSThirdParty"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSThirdParty) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:planId: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) writeField2(oprot thrift.TProtocol) (err error) {
	if p.CampaignId != nil {
		if err := oprot.WriteFieldBegin("campaignId", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:campaignId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.CampaignId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignId {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:campaignId: %s", p, err)
		}
	}
	return err
}

func (p *DOSThirdParty) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AccountId != nil {
		if err := oprot.WriteFieldBegin("accountId", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:accountId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.AccountId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccountId {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:accountId: %s", p, err)
		}
	}
	return err
}

func (p *DOSThirdParty) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:accountName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountName)); err != nil {
		return fmt.Errorf("%T.accountName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:accountName: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:dt: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) writeField6(oprot thrift.TProtocol) (err error) {
	if p.CampaignName != nil {
		if err := oprot.WriteFieldBegin("campaignName", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:campaignName: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.CampaignName)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignName {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:campaignName: %s", p, err)
		}
	}
	return err
}

func (p *DOSThirdParty) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:platform: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) writeField8(oprot thrift.TProtocol) (err error) {
	if p.AdgroupId != nil {
		if err := oprot.WriteFieldBegin("adgroupId", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:adgroupId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.AdgroupId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdgroupId {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:adgroupId: %s", p, err)
		}
	}
	return err
}

func (p *DOSThirdParty) writeField9(oprot thrift.TProtocol) (err error) {
	if p.AdgroupName != nil {
		if err := oprot.WriteFieldBegin("adgroupName", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:adgroupName: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AdgroupName)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdgroupName {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:adgroupName: %s", p, err)
		}
	}
	return err
}

func (p *DOSThirdParty) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:status: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastupdate: %s", p, err)
	}
	return err
}

func (p *DOSThirdParty) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSThirdParty(%+v)", *p)
}

type DOSAct struct {
	PlanId        int32  `thrift:"plan_id,1" json:"plan_id"`
	StrategyId    int32  `thrift:"strategy_id,2" json:"strategy_id"`
	CreativeId    int32  `thrift:"creative_id,3" json:"creative_id"`
	DspCampaignId int64  `thrift:"dsp_campaign_id,4" json:"dsp_campaign_id"`
	ChnId         int32  `thrift:"chn_id,5" json:"chn_id"`
	ProductId     int32  `thrift:"product_id,6" json:"product_id"`
	ProductChnId  int32  `thrift:"product_chn_id,7" json:"product_chn_id"`
	SearchId      int64  `thrift:"search_id,8" json:"search_id"`
	Appid         string `thrift:"appid,9" json:"appid"`
	// unused field # 10
	Mac        string `thrift:"mac,11" json:"mac"`
	Macmd5     string `thrift:"macmd5,12" json:"macmd5"`
	Idfa       string `thrift:"idfa,13" json:"idfa"`
	Idfamd5    string `thrift:"idfamd5,14" json:"idfamd5"`
	Imei       string `thrift:"imei,15" json:"imei"`
	Imeimd5    string `thrift:"imeimd5,16" json:"imeimd5"`
	ClkIp      string `thrift:"clk_ip,17" json:"clk_ip"`
	ClkTs      int32  `thrift:"clk_ts,18" json:"clk_ts"`
	ActTs      int32  `thrift:"act_ts,19" json:"act_ts"`
	Platform   int32  `thrift:"platform,20" json:"platform"`
	AdPrice    int64  `thrift:"ad_price,21" json:"ad_price"`
	MediaPrice int64  `thrift:"media_price,22" json:"media_price"`
	CostType   int32  `thrift:"cost_type,23" json:"cost_type"`
	ActType    int32  `thrift:"act_type,24" json:"act_type"`
	UserAgent  string `thrift:"user_agent,25" json:"user_agent"`
	RemoteIp   string `thrift:"remote_ip,26" json:"remote_ip"`
	ExtInfo    string `thrift:"ext_info,27" json:"ext_info"`
	IsAct      int32  `thrift:"is_act,28" json:"is_act"`
	// unused field # 29
	Oaid    string `thrift:"oaid,30" json:"oaid"`
	Oaidmd5 string `thrift:"oaidmd5,31" json:"oaidmd5"`
}

func NewDOSAct() *DOSAct {
	return &DOSAct{}
}

func (p *DOSAct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSAct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSAct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *DOSAct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DOSAct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *DOSAct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *DOSAct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *DOSAct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ProductChnId = v
	}
	return nil
}

func (p *DOSAct) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *DOSAct) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *DOSAct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *DOSAct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *DOSAct) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *DOSAct) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Idfamd5 = v
	}
	return nil
}

func (p *DOSAct) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *DOSAct) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Imeimd5 = v
	}
	return nil
}

func (p *DOSAct) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Oaid = v
	}
	return nil
}

func (p *DOSAct) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Oaidmd5 = v
	}
	return nil
}

func (p *DOSAct) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ClkIp = v
	}
	return nil
}

func (p *DOSAct) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.ClkTs = v
	}
	return nil
}

func (p *DOSAct) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.ActTs = v
	}
	return nil
}

func (p *DOSAct) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *DOSAct) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.AdPrice = v
	}
	return nil
}

func (p *DOSAct) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.MediaPrice = v
	}
	return nil
}

func (p *DOSAct) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *DOSAct) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.ActType = v
	}
	return nil
}

func (p *DOSAct) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.UserAgent = v
	}
	return nil
}

func (p *DOSAct) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.RemoteIp = v
	}
	return nil
}

func (p *DOSAct) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *DOSAct) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.IsAct = v
	}
	return nil
}

func (p *DOSAct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSAct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSAct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:plan_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:strategy_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creative_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_campaign_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dsp_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dsp_campaign_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dsp_campaign_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chn_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chn_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:chn_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:product_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductId)); err != nil {
		return fmt.Errorf("%T.product_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:product_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_chn_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:product_chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductChnId)); err != nil {
		return fmt.Errorf("%T.product_chn_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:product_chn_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:search_id: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:appid: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:mac: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:macmd5: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:idfa: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfamd5", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:idfamd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfamd5)); err != nil {
		return fmt.Errorf("%T.idfamd5 (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:idfamd5: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:imei: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imeimd5", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:imeimd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imeimd5)); err != nil {
		return fmt.Errorf("%T.imeimd5 (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:imeimd5: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_ip", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:clk_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkIp)); err != nil {
		return fmt.Errorf("%T.clk_ip (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:clk_ip: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_ts", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:clk_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkTs)); err != nil {
		return fmt.Errorf("%T.clk_ts (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:clk_ts: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_ts", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:act_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActTs)); err != nil {
		return fmt.Errorf("%T.act_ts (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:act_ts: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:platform: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_price", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:ad_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPrice)); err != nil {
		return fmt.Errorf("%T.ad_price (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:ad_price: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_price", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:media_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.media_price (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:media_price: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:cost_type: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_type", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:act_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActType)); err != nil {
		return fmt.Errorf("%T.act_type (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:act_type: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_agent", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:user_agent: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAgent)); err != nil {
		return fmt.Errorf("%T.user_agent (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:user_agent: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remote_ip", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:remote_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RemoteIp)); err != nil {
		return fmt.Errorf("%T.remote_ip (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:remote_ip: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext_info", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:ext_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.ext_info (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:ext_info: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_act", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:is_act: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsAct)); err != nil {
		return fmt.Errorf("%T.is_act (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:is_act: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaid", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:oaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaid)); err != nil {
		return fmt.Errorf("%T.oaid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:oaid: %s", p, err)
	}
	return err
}

func (p *DOSAct) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaidmd5", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:oaidmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaidmd5)); err != nil {
		return fmt.Errorf("%T.oaidmd5 (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:oaidmd5: %s", p, err)
	}
	return err
}

func (p *DOSAct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSAct(%+v)", *p)
}

type DOSImp struct {
	PlanId        int32  `thrift:"plan_id,1" json:"plan_id"`
	StrategyId    int32  `thrift:"strategy_id,2" json:"strategy_id"`
	CreativeId    int32  `thrift:"creative_id,3" json:"creative_id"`
	DspCampaignId int64  `thrift:"dsp_campaign_id,4" json:"dsp_campaign_id"`
	ChnId         int32  `thrift:"chn_id,5" json:"chn_id"`
	ProductId     int32  `thrift:"product_id,6" json:"product_id"`
	ProductChnId  int32  `thrift:"product_chn_id,7" json:"product_chn_id"`
	SearchId      int64  `thrift:"search_id,8" json:"search_id"`
	ImpId         int64  `thrift:"imp_id,9" json:"imp_id"`
	ProId         int32  `thrift:"pro_id,10" json:"pro_id"`
	Mac           string `thrift:"mac,11" json:"mac"`
	Macmd5        string `thrift:"macmd5,12" json:"macmd5"`
	Idfa          string `thrift:"idfa,13" json:"idfa"`
	Idfamd5       string `thrift:"idfamd5,14" json:"idfamd5"`
	Imei          string `thrift:"imei,15" json:"imei"`
	Imeimd5       string `thrift:"imeimd5,16" json:"imeimd5"`
	ImpIp         string `thrift:"imp_ip,17" json:"imp_ip"`
	ImpTs         int32  `thrift:"imp_ts,18" json:"imp_ts"`
	Oaidmd5       string `thrift:"oaidmd5,19" json:"oaidmd5"`
	// unused field # 20
	Platform         int32  `thrift:"platform,21" json:"platform"`
	AdPrice          int64  `thrift:"ad_price,22" json:"ad_price"`
	MediaPrice       int64  `thrift:"media_price,23" json:"media_price"`
	CostType         int32  `thrift:"cost_type,24" json:"cost_type"`
	UserAgent        string `thrift:"user_agent,25" json:"user_agent"`
	RemoteIp         string `thrift:"remote_ip,26" json:"remote_ip"`
	ExtInfo          string `thrift:"ext_info,27" json:"ext_info"`
	Ua               string `thrift:"ua,28" json:"ua"`
	Location         string `thrift:"location,29" json:"location"`
	Oaid             string `thrift:"oaid,30" json:"oaid"`
	Model            string `thrift:"model,31" json:"model"`
	AndroidId        string `thrift:"android_id,32" json:"android_id"`
	MediaCreativeId  int64  `thrift:"media_creative_id,33" json:"media_creative_id"`
	MediaGroupId     int64  `thrift:"media_group_id,34" json:"media_group_id"`
	MediaPlanId      int64  `thrift:"media_plan_id,35" json:"media_plan_id"`
	MediaAccountId   int64  `thrift:"media_account_id,36" json:"media_account_id"`
	Gaid             string `thrift:"gaid,37" json:"gaid"`
	MediaProjectId   int64  `thrift:"media_project_id,38" json:"media_project_id"`
	MediaPromotionId int64  `thrift:"media_promotion_id,39" json:"media_promotion_id"`
	Media            int64  `thrift:"media,40" json:"media"`
	TempId           int64  `thrift:"temp_id,41" json:"temp_id"`
	OpenId           string `thrift:"open_id,42" json:"open_id"`
}

func NewDOSImp() *DOSImp {
	return &DOSImp{}
}

func (p *DOSImp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I64 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I64 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I64 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I64 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSImp) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSImp) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *DOSImp) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DOSImp) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *DOSImp) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *DOSImp) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *DOSImp) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ProductChnId = v
	}
	return nil
}

func (p *DOSImp) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *DOSImp) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ImpId = v
	}
	return nil
}

func (p *DOSImp) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ProId = v
	}
	return nil
}

func (p *DOSImp) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *DOSImp) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *DOSImp) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *DOSImp) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Idfamd5 = v
	}
	return nil
}

func (p *DOSImp) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *DOSImp) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Imeimd5 = v
	}
	return nil
}

func (p *DOSImp) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Oaid = v
	}
	return nil
}

func (p *DOSImp) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Oaidmd5 = v
	}
	return nil
}

func (p *DOSImp) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ImpIp = v
	}
	return nil
}

func (p *DOSImp) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.ImpTs = v
	}
	return nil
}

func (p *DOSImp) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *DOSImp) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AdPrice = v
	}
	return nil
}

func (p *DOSImp) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.MediaPrice = v
	}
	return nil
}

func (p *DOSImp) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *DOSImp) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.UserAgent = v
	}
	return nil
}

func (p *DOSImp) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.RemoteIp = v
	}
	return nil
}

func (p *DOSImp) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *DOSImp) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Ua = v
	}
	return nil
}

func (p *DOSImp) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Location = v
	}
	return nil
}

func (p *DOSImp) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Model = v
	}
	return nil
}

func (p *DOSImp) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *DOSImp) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.MediaCreativeId = v
	}
	return nil
}

func (p *DOSImp) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.MediaGroupId = v
	}
	return nil
}

func (p *DOSImp) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.MediaPlanId = v
	}
	return nil
}

func (p *DOSImp) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.MediaAccountId = v
	}
	return nil
}

func (p *DOSImp) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Gaid = v
	}
	return nil
}

func (p *DOSImp) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.MediaProjectId = v
	}
	return nil
}

func (p *DOSImp) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.MediaPromotionId = v
	}
	return nil
}

func (p *DOSImp) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *DOSImp) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.TempId = v
	}
	return nil
}

func (p *DOSImp) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.OpenId = v
	}
	return nil
}

func (p *DOSImp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSImp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSImp) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:plan_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:strategy_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creative_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsp_campaign_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dsp_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dsp_campaign_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dsp_campaign_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chn_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chn_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:chn_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:product_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductId)); err != nil {
		return fmt.Errorf("%T.product_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:product_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_chn_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:product_chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductChnId)); err != nil {
		return fmt.Errorf("%T.product_chn_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:product_chn_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:search_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_id", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:imp_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpId)); err != nil {
		return fmt.Errorf("%T.imp_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:imp_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pro_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:pro_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProId)); err != nil {
		return fmt.Errorf("%T.pro_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:pro_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:mac: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:macmd5: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:idfa: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfamd5", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:idfamd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfamd5)); err != nil {
		return fmt.Errorf("%T.idfamd5 (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:idfamd5: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:imei: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imeimd5", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:imeimd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imeimd5)); err != nil {
		return fmt.Errorf("%T.imeimd5 (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:imeimd5: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_ip", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:imp_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpIp)); err != nil {
		return fmt.Errorf("%T.imp_ip (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:imp_ip: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_ts", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:imp_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImpTs)); err != nil {
		return fmt.Errorf("%T.imp_ts (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:imp_ts: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaidmd5", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:oaidmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaidmd5)); err != nil {
		return fmt.Errorf("%T.oaidmd5 (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:oaidmd5: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:platform: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_price", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:ad_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPrice)); err != nil {
		return fmt.Errorf("%T.ad_price (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:ad_price: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_price", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:media_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.media_price (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:media_price: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:cost_type: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_agent", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:user_agent: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAgent)); err != nil {
		return fmt.Errorf("%T.user_agent (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:user_agent: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remote_ip", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:remote_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RemoteIp)); err != nil {
		return fmt.Errorf("%T.remote_ip (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:remote_ip: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext_info", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:ext_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.ext_info (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:ext_info: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ua", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ua)); err != nil {
		return fmt.Errorf("%T.ua (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:ua: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("location", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:location: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Location)); err != nil {
		return fmt.Errorf("%T.location (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:location: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaid", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:oaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaid)); err != nil {
		return fmt.Errorf("%T.oaid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:oaid: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Model)); err != nil {
		return fmt.Errorf("%T.model (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:model: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:android_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_creative_id", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:media_creative_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaCreativeId)); err != nil {
		return fmt.Errorf("%T.media_creative_id (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:media_creative_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_group_id", thrift.I64, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:media_group_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaGroupId)); err != nil {
		return fmt.Errorf("%T.media_group_id (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:media_group_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_plan_id", thrift.I64, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:media_plan_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPlanId)); err != nil {
		return fmt.Errorf("%T.media_plan_id (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:media_plan_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_account_id", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:media_account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaAccountId)); err != nil {
		return fmt.Errorf("%T.media_account_id (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:media_account_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gaid", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:gaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Gaid)); err != nil {
		return fmt.Errorf("%T.gaid (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:gaid: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_project_id", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:media_project_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaProjectId)); err != nil {
		return fmt.Errorf("%T.media_project_id (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:media_project_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_promotion_id", thrift.I64, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:media_promotion_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPromotionId)); err != nil {
		return fmt.Errorf("%T.media_promotion_id (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:media_promotion_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.I64, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:media: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Media)); err != nil {
		return fmt.Errorf("%T.media (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:media: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("temp_id", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:temp_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TempId)); err != nil {
		return fmt.Errorf("%T.temp_id (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:temp_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("open_id", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:open_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OpenId)); err != nil {
		return fmt.Errorf("%T.open_id (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:open_id: %s", p, err)
	}
	return err
}

func (p *DOSImp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSImp(%+v)", *p)
}

type QueryThirdPartyToken struct {
	Platform        int32  `thrift:"platform,1" json:"platform"`
	AccountId       int32  `thrift:"account_id,2" json:"account_id"`
	AccountIdString string `thrift:"account_id_string,3" json:"account_id_string"`
}

func NewQueryThirdPartyToken() *QueryThirdPartyToken {
	return &QueryThirdPartyToken{}
}

func (p *QueryThirdPartyToken) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryThirdPartyToken) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *QueryThirdPartyToken) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *QueryThirdPartyToken) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountIdString = v
	}
	return nil
}

func (p *QueryThirdPartyToken) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryThirdPartyToken"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryThirdPartyToken) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:platform: %s", p, err)
	}
	return err
}

func (p *QueryThirdPartyToken) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:account_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccountId)); err != nil {
		return fmt.Errorf("%T.account_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:account_id: %s", p, err)
	}
	return err
}

func (p *QueryThirdPartyToken) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id_string", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account_id_string: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountIdString)); err != nil {
		return fmt.Errorf("%T.account_id_string (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account_id_string: %s", p, err)
	}
	return err
}

func (p *QueryThirdPartyToken) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryThirdPartyToken(%+v)", *p)
}

type DOSQueryThirdPartyTokenResult struct {
	AccessToken string `thrift:"access_token,1" json:"access_token"`
	UpdateTime  int64  `thrift:"update_time,2" json:"update_time"`
}

func NewDOSQueryThirdPartyTokenResult() *DOSQueryThirdPartyTokenResult {
	return &DOSQueryThirdPartyTokenResult{}
}

func (p *DOSQueryThirdPartyTokenResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSQueryThirdPartyTokenResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *DOSQueryThirdPartyTokenResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *DOSQueryThirdPartyTokenResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSQueryThirdPartyTokenResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSQueryThirdPartyTokenResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:access_token: %s", p, err)
	}
	return err
}

func (p *DOSQueryThirdPartyTokenResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:update_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:update_time: %s", p, err)
	}
	return err
}

func (p *DOSQueryThirdPartyTokenResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSQueryThirdPartyTokenResult(%+v)", *p)
}

type DOSThirdBandingInfo struct {
	PlanId     int32   `thrift:"plan_id,1" json:"plan_id"`
	CreativeId int32   `thrift:"creative_id,2" json:"creative_id"`
	Ids        []int64 `thrift:"ids,3" json:"ids"`
	Level      int32   `thrift:"level,4" json:"level"`
	Platform   int32   `thrift:"platform,5" json:"platform"`
}

func NewDOSThirdBandingInfo() *DOSThirdBandingInfo {
	return &DOSThirdBandingInfo{}
}

func (p *DOSThirdBandingInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DOSThirdBandingInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *DOSThirdBandingInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *DOSThirdBandingInfo) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = v
		}
		p.Ids = append(p.Ids, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DOSThirdBandingInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Level = v
	}
	return nil
}

func (p *DOSThirdBandingInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *DOSThirdBandingInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DOSThirdBandingInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DOSThirdBandingInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:plan_id: %s", p, err)
	}
	return err
}

func (p *DOSThirdBandingInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:creative_id: %s", p, err)
	}
	return err
}

func (p *DOSThirdBandingInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DOSThirdBandingInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("level", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:level: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Level)); err != nil {
		return fmt.Errorf("%T.level (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:level: %s", p, err)
	}
	return err
}

func (p *DOSThirdBandingInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:platform: %s", p, err)
	}
	return err
}

func (p *DOSThirdBandingInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DOSThirdBandingInfo(%+v)", *p)
}
