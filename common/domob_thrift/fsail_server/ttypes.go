// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package fsail_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type FeaDataUsage int64

const (
	FeaDataUsage_TRAIN FeaDataUsage = 1
	FeaDataUsage_TEST  FeaDataUsage = 2
)

func (p FeaDataUsage) String() string {
	switch p {
	case FeaDataUsage_TRAIN:
		return "FeaDataUsage_TRAIN"
	case FeaDataUsage_TEST:
		return "FeaDataUsage_TEST"
	}
	return "<UNSET>"
}

func FeaDataUsageFromString(s string) (FeaDataUsage, error) {
	switch s {
	case "FeaDataUsage_TRAIN":
		return FeaDataUsage_TRAIN, nil
	case "FeaDataUsage_TEST":
		return FeaDataUsage_TEST, nil
	}
	return FeaDataUsage(math.MinInt32 - 1), fmt.Errorf("not a valid FeaDataUsage string")
}

type FeaDataPrintMode int64

const (
	FeaDataPrintMode_STDOUT FeaDataPrintMode = 1
	FeaDataPrintMode_HDFS   FeaDataPrintMode = 2
)

func (p FeaDataPrintMode) String() string {
	switch p {
	case FeaDataPrintMode_STDOUT:
		return "FeaDataPrintMode_STDOUT"
	case FeaDataPrintMode_HDFS:
		return "FeaDataPrintMode_HDFS"
	}
	return "<UNSET>"
}

func FeaDataPrintModeFromString(s string) (FeaDataPrintMode, error) {
	switch s {
	case "FeaDataPrintMode_STDOUT":
		return FeaDataPrintMode_STDOUT, nil
	case "FeaDataPrintMode_HDFS":
		return FeaDataPrintMode_HDFS, nil
	}
	return FeaDataPrintMode(math.MinInt32 - 1), fmt.Errorf("not a valid FeaDataPrintMode string")
}

type ModelType int64

const (
	ModelType_CTR ModelType = 1
	ModelType_ATR ModelType = 2
)

func (p ModelType) String() string {
	switch p {
	case ModelType_CTR:
		return "ModelType_CTR"
	case ModelType_ATR:
		return "ModelType_ATR"
	}
	return "<UNSET>"
}

func ModelTypeFromString(s string) (ModelType, error) {
	switch s {
	case "ModelType_CTR":
		return ModelType_CTR, nil
	case "ModelType_ATR":
		return ModelType_ATR, nil
	}
	return ModelType(math.MinInt32 - 1), fmt.Errorf("not a valid ModelType string")
}

type AssessmentStatus int64

const (
	AssessmentStatus_SUCC    AssessmentStatus = 1
	AssessmentStatus_NEW     AssessmentStatus = 2
	AssessmentStatus_RUNNING AssessmentStatus = 3
)

func (p AssessmentStatus) String() string {
	switch p {
	case AssessmentStatus_SUCC:
		return "AssessmentStatus_SUCC"
	case AssessmentStatus_NEW:
		return "AssessmentStatus_NEW"
	case AssessmentStatus_RUNNING:
		return "AssessmentStatus_RUNNING"
	}
	return "<UNSET>"
}

func AssessmentStatusFromString(s string) (AssessmentStatus, error) {
	switch s {
	case "AssessmentStatus_SUCC":
		return AssessmentStatus_SUCC, nil
	case "AssessmentStatus_NEW":
		return AssessmentStatus_NEW, nil
	case "AssessmentStatus_RUNNING":
		return AssessmentStatus_RUNNING, nil
	}
	return AssessmentStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AssessmentStatus string")
}

type AggregationStatus int64

const (
	AggregationStatus_SUCC    AggregationStatus = 1
	AggregationStatus_NEW     AggregationStatus = 2
	AggregationStatus_RUNNING AggregationStatus = 3
)

func (p AggregationStatus) String() string {
	switch p {
	case AggregationStatus_SUCC:
		return "AggregationStatus_SUCC"
	case AggregationStatus_NEW:
		return "AggregationStatus_NEW"
	case AggregationStatus_RUNNING:
		return "AggregationStatus_RUNNING"
	}
	return "<UNSET>"
}

func AggregationStatusFromString(s string) (AggregationStatus, error) {
	switch s {
	case "AggregationStatus_SUCC":
		return AggregationStatus_SUCC, nil
	case "AggregationStatus_NEW":
		return AggregationStatus_NEW, nil
	case "AggregationStatus_RUNNING":
		return AggregationStatus_RUNNING, nil
	}
	return AggregationStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AggregationStatus string")
}

type BatchFeaBuildStatus int64

const (
	BatchFeaBuildStatus_SUCC                 BatchFeaBuildStatus = 1
	BatchFeaBuildStatus_NEW                  BatchFeaBuildStatus = 2
	BatchFeaBuildStatus_MADE_FEA_CODE        BatchFeaBuildStatus = 3
	BatchFeaBuildStatus_DAILY_FEA_JOINED     BatchFeaBuildStatus = 4
	BatchFeaBuildStatus_TOTAL_FEA_JOINED     BatchFeaBuildStatus = 5
	BatchFeaBuildStatus_AGGREGATE_TASK_ADDED BatchFeaBuildStatus = 6
	BatchFeaBuildStatus_TASK_AGGREGATED      BatchFeaBuildStatus = 7
)

func (p BatchFeaBuildStatus) String() string {
	switch p {
	case BatchFeaBuildStatus_SUCC:
		return "BatchFeaBuildStatus_SUCC"
	case BatchFeaBuildStatus_NEW:
		return "BatchFeaBuildStatus_NEW"
	case BatchFeaBuildStatus_MADE_FEA_CODE:
		return "BatchFeaBuildStatus_MADE_FEA_CODE"
	case BatchFeaBuildStatus_DAILY_FEA_JOINED:
		return "BatchFeaBuildStatus_DAILY_FEA_JOINED"
	case BatchFeaBuildStatus_TOTAL_FEA_JOINED:
		return "BatchFeaBuildStatus_TOTAL_FEA_JOINED"
	case BatchFeaBuildStatus_AGGREGATE_TASK_ADDED:
		return "BatchFeaBuildStatus_AGGREGATE_TASK_ADDED"
	case BatchFeaBuildStatus_TASK_AGGREGATED:
		return "BatchFeaBuildStatus_TASK_AGGREGATED"
	}
	return "<UNSET>"
}

func BatchFeaBuildStatusFromString(s string) (BatchFeaBuildStatus, error) {
	switch s {
	case "BatchFeaBuildStatus_SUCC":
		return BatchFeaBuildStatus_SUCC, nil
	case "BatchFeaBuildStatus_NEW":
		return BatchFeaBuildStatus_NEW, nil
	case "BatchFeaBuildStatus_MADE_FEA_CODE":
		return BatchFeaBuildStatus_MADE_FEA_CODE, nil
	case "BatchFeaBuildStatus_DAILY_FEA_JOINED":
		return BatchFeaBuildStatus_DAILY_FEA_JOINED, nil
	case "BatchFeaBuildStatus_TOTAL_FEA_JOINED":
		return BatchFeaBuildStatus_TOTAL_FEA_JOINED, nil
	case "BatchFeaBuildStatus_AGGREGATE_TASK_ADDED":
		return BatchFeaBuildStatus_AGGREGATE_TASK_ADDED, nil
	case "BatchFeaBuildStatus_TASK_AGGREGATED":
		return BatchFeaBuildStatus_TASK_AGGREGATED, nil
	}
	return BatchFeaBuildStatus(math.MinInt32 - 1), fmt.Errorf("not a valid BatchFeaBuildStatus string")
}

type FsailServerErrorCode int64

const (
	FsailServerErrorCode_SUCC                   FsailServerErrorCode = 0
	FsailServerErrorCode_ERROR                  FsailServerErrorCode = 1
	FsailServerErrorCode_EMPTY_TO_RUN           FsailServerErrorCode = 2
	FsailServerErrorCode_DB_OP_ERR              FsailServerErrorCode = 3
	FsailServerErrorCode_UPDATE_CONDITION_ERROR FsailServerErrorCode = 4
)

func (p FsailServerErrorCode) String() string {
	switch p {
	case FsailServerErrorCode_SUCC:
		return "FsailServerErrorCode_SUCC"
	case FsailServerErrorCode_ERROR:
		return "FsailServerErrorCode_ERROR"
	case FsailServerErrorCode_EMPTY_TO_RUN:
		return "FsailServerErrorCode_EMPTY_TO_RUN"
	case FsailServerErrorCode_DB_OP_ERR:
		return "FsailServerErrorCode_DB_OP_ERR"
	case FsailServerErrorCode_UPDATE_CONDITION_ERROR:
		return "FsailServerErrorCode_UPDATE_CONDITION_ERROR"
	}
	return "<UNSET>"
}

func FsailServerErrorCodeFromString(s string) (FsailServerErrorCode, error) {
	switch s {
	case "FsailServerErrorCode_SUCC":
		return FsailServerErrorCode_SUCC, nil
	case "FsailServerErrorCode_ERROR":
		return FsailServerErrorCode_ERROR, nil
	case "FsailServerErrorCode_EMPTY_TO_RUN":
		return FsailServerErrorCode_EMPTY_TO_RUN, nil
	case "FsailServerErrorCode_DB_OP_ERR":
		return FsailServerErrorCode_DB_OP_ERR, nil
	case "FsailServerErrorCode_UPDATE_CONDITION_ERROR":
		return FsailServerErrorCode_UPDATE_CONDITION_ERROR, nil
	}
	return FsailServerErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid FsailServerErrorCode string")
}

type FeaData struct {
	UsageDesc   FeaDataUsage     `thrift:"usage_desc,1" json:"usage_desc"`
	BegDt       int32            `thrift:"beg_dt,2" json:"beg_dt"`
	EndDt       int32            `thrift:"end_dt,3" json:"end_dt"`
	PrintMode   FeaDataPrintMode `thrift:"print_mode,4" json:"print_mode"`
	FeadataPath string           `thrift:"feadata_path,5" json:"feadata_path"`
	ExchangeId  int32            `thrift:"exchange_id,6" json:"exchange_id"`
	Mt          ModelType        `thrift:"mt,7" json:"mt"`
	Version     string           `thrift:"version,8" json:"version"`
	CommandPath string           `thrift:"command_path,9" json:"command_path"`
	CommitUser  string           `thrift:"commit_user,10" json:"commit_user"`
	OpTs        int32            `thrift:"op_ts,11" json:"op_ts"`
}

func NewFeaData() *FeaData {
	return &FeaData{
		UsageDesc: math.MinInt32 - 1, // unset sentinal value

		PrintMode: math.MinInt32 - 1, // unset sentinal value

		Mt: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FeaData) IsSetUsageDesc() bool {
	return int64(p.UsageDesc) != math.MinInt32-1
}

func (p *FeaData) IsSetPrintMode() bool {
	return int64(p.PrintMode) != math.MinInt32-1
}

func (p *FeaData) IsSetMt() bool {
	return int64(p.Mt) != math.MinInt32-1
}

func (p *FeaData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FeaData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.UsageDesc = FeaDataUsage(v)
	}
	return nil
}

func (p *FeaData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BegDt = v
	}
	return nil
}

func (p *FeaData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.EndDt = v
	}
	return nil
}

func (p *FeaData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PrintMode = FeaDataPrintMode(v)
	}
	return nil
}

func (p *FeaData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.FeadataPath = v
	}
	return nil
}

func (p *FeaData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *FeaData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Mt = ModelType(v)
	}
	return nil
}

func (p *FeaData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *FeaData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CommandPath = v
	}
	return nil
}

func (p *FeaData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CommitUser = v
	}
	return nil
}

func (p *FeaData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.OpTs = v
	}
	return nil
}

func (p *FeaData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FeaData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FeaData) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUsageDesc() {
		if err := oprot.WriteFieldBegin("usage_desc", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:usage_desc: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UsageDesc)); err != nil {
			return fmt.Errorf("%T.usage_desc (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:usage_desc: %s", p, err)
		}
	}
	return err
}

func (p *FeaData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beg_dt", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:beg_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BegDt)); err != nil {
		return fmt.Errorf("%T.beg_dt (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:beg_dt: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_dt", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:end_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndDt)); err != nil {
		return fmt.Errorf("%T.end_dt (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:end_dt: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrintMode() {
		if err := oprot.WriteFieldBegin("print_mode", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:print_mode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PrintMode)); err != nil {
			return fmt.Errorf("%T.print_mode (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:print_mode: %s", p, err)
		}
	}
	return err
}

func (p *FeaData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("feadata_path", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:feadata_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FeadataPath)); err != nil {
		return fmt.Errorf("%T.feadata_path (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:feadata_path: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:exchange_id: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMt() {
		if err := oprot.WriteFieldBegin("mt", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:mt: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Mt)); err != nil {
			return fmt.Errorf("%T.mt (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:mt: %s", p, err)
		}
	}
	return err
}

func (p *FeaData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:version: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("command_path", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:command_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CommandPath)); err != nil {
		return fmt.Errorf("%T.command_path (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:command_path: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("commit_user", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:commit_user: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CommitUser)); err != nil {
		return fmt.Errorf("%T.commit_user (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:commit_user: %s", p, err)
	}
	return err
}

func (p *FeaData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("op_ts", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:op_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OpTs)); err != nil {
		return fmt.Errorf("%T.op_ts (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:op_ts: %s", p, err)
	}
	return err
}

func (p *FeaData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeaData(%+v)", *p)
}

type AggregationData struct {
	BegDt        int32             `thrift:"beg_dt,1" json:"beg_dt"`
	EndDt        int32             `thrift:"end_dt,2" json:"end_dt"`
	ExchangeId   int32             `thrift:"exchange_id,3" json:"exchange_id"`
	Mt           ModelType         `thrift:"mt,4" json:"mt"`
	Status       AggregationStatus `thrift:"status,5" json:"status"`
	Version      string            `thrift:"version,6" json:"version"`
	StatusTs     int32             `thrift:"status_ts,7" json:"status_ts"`
	InstanceName string            `thrift:"instance_name,8" json:"instance_name"`
	CreateTs     int32             `thrift:"create_ts,9" json:"create_ts"`
	FeaName      string            `thrift:"fea_name,10" json:"fea_name"`
}

func NewAggregationData() *AggregationData {
	return &AggregationData{
		Mt: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		FeaName: "",
	}
}

func (p *AggregationData) IsSetMt() bool {
	return int64(p.Mt) != math.MinInt32-1
}

func (p *AggregationData) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AggregationData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AggregationData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BegDt = v
	}
	return nil
}

func (p *AggregationData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndDt = v
	}
	return nil
}

func (p *AggregationData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *AggregationData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mt = ModelType(v)
	}
	return nil
}

func (p *AggregationData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = AggregationStatus(v)
	}
	return nil
}

func (p *AggregationData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *AggregationData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.StatusTs = v
	}
	return nil
}

func (p *AggregationData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.InstanceName = v
	}
	return nil
}

func (p *AggregationData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CreateTs = v
	}
	return nil
}

func (p *AggregationData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.FeaName = v
	}
	return nil
}

func (p *AggregationData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AggregationData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AggregationData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beg_dt", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:beg_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BegDt)); err != nil {
		return fmt.Errorf("%T.beg_dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:beg_dt: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_dt", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:end_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndDt)); err != nil {
		return fmt.Errorf("%T.end_dt (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:end_dt: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exchange_id: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMt() {
		if err := oprot.WriteFieldBegin("mt", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:mt: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Mt)); err != nil {
			return fmt.Errorf("%T.mt (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:mt: %s", p, err)
		}
	}
	return err
}

func (p *AggregationData) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *AggregationData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:version: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status_ts", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:status_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatusTs)); err != nil {
		return fmt.Errorf("%T.status_ts (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:status_ts: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("instance_name", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:instance_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.InstanceName)); err != nil {
		return fmt.Errorf("%T.instance_name (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:instance_name: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_ts", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:create_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTs)); err != nil {
		return fmt.Errorf("%T.create_ts (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:create_ts: %s", p, err)
	}
	return err
}

func (p *AggregationData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fea_name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:fea_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FeaName)); err != nil {
		return fmt.Errorf("%T.fea_name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:fea_name: %s", p, err)
	}
	return err
}

func (p *AggregationData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AggregationData(%+v)", *p)
}

type BatchFeaBuild struct {
	BfbId         int64               `thrift:"bfb_id,1" json:"bfb_id"`
	HivePartition string              `thrift:"hive_partition,2" json:"hive_partition"`
	FeaName       string              `thrift:"fea_name,3" json:"fea_name"`
	BegDt         int32               `thrift:"beg_dt,4" json:"beg_dt"`
	EndDt         int32               `thrift:"end_dt,5" json:"end_dt"`
	ExchangeId    int32               `thrift:"exchange_id,6" json:"exchange_id"`
	Mt            ModelType           `thrift:"mt,7" json:"mt"`
	BfbStatus     BatchFeaBuildStatus `thrift:"bfb_status,8" json:"bfb_status"`
	StatusTs      int32               `thrift:"status_ts,9" json:"status_ts"`
	CreateTs      int32               `thrift:"create_ts,10" json:"create_ts"`
}

func NewBatchFeaBuild() *BatchFeaBuild {
	return &BatchFeaBuild{
		Mt: math.MinInt32 - 1, // unset sentinal value

		BfbStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BatchFeaBuild) IsSetMt() bool {
	return int64(p.Mt) != math.MinInt32-1
}

func (p *BatchFeaBuild) IsSetBfbStatus() bool {
	return int64(p.BfbStatus) != math.MinInt32-1
}

func (p *BatchFeaBuild) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchFeaBuild) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BfbId = v
	}
	return nil
}

func (p *BatchFeaBuild) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.HivePartition = v
	}
	return nil
}

func (p *BatchFeaBuild) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FeaName = v
	}
	return nil
}

func (p *BatchFeaBuild) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.BegDt = v
	}
	return nil
}

func (p *BatchFeaBuild) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndDt = v
	}
	return nil
}

func (p *BatchFeaBuild) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *BatchFeaBuild) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Mt = ModelType(v)
	}
	return nil
}

func (p *BatchFeaBuild) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.BfbStatus = BatchFeaBuildStatus(v)
	}
	return nil
}

func (p *BatchFeaBuild) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.StatusTs = v
	}
	return nil
}

func (p *BatchFeaBuild) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTs = v
	}
	return nil
}

func (p *BatchFeaBuild) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BatchFeaBuild"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchFeaBuild) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bfb_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bfb_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.BfbId)); err != nil {
		return fmt.Errorf("%T.bfb_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bfb_id: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hive_partition", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hive_partition: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HivePartition)); err != nil {
		return fmt.Errorf("%T.hive_partition (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hive_partition: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fea_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fea_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FeaName)); err != nil {
		return fmt.Errorf("%T.fea_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fea_name: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beg_dt", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:beg_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BegDt)); err != nil {
		return fmt.Errorf("%T.beg_dt (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:beg_dt: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_dt", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:end_dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndDt)); err != nil {
		return fmt.Errorf("%T.end_dt (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:end_dt: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:exchange_id: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMt() {
		if err := oprot.WriteFieldBegin("mt", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:mt: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Mt)); err != nil {
			return fmt.Errorf("%T.mt (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:mt: %s", p, err)
		}
	}
	return err
}

func (p *BatchFeaBuild) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBfbStatus() {
		if err := oprot.WriteFieldBegin("bfb_status", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:bfb_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BfbStatus)); err != nil {
			return fmt.Errorf("%T.bfb_status (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:bfb_status: %s", p, err)
		}
	}
	return err
}

func (p *BatchFeaBuild) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status_ts", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:status_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatusTs)); err != nil {
		return fmt.Errorf("%T.status_ts (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:status_ts: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_ts", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:create_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTs)); err != nil {
		return fmt.Errorf("%T.create_ts (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:create_ts: %s", p, err)
	}
	return err
}

func (p *BatchFeaBuild) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchFeaBuild(%+v)", *p)
}

type Report struct {
	ExchangeId            int32           `thrift:"exchange_id,1" json:"exchange_id"`
	PredictAvgPossibility int32           `thrift:"predict_avg_possibility,2" json:"predict_avg_possibility"`
	StatAvgPossibility    int32           `thrift:"stat_avg_possibility,3" json:"stat_avg_possibility"`
	Mt                    ModelType       `thrift:"mt,4" json:"mt"`
	PredictVariance       int32           `thrift:"predict_variance,5" json:"predict_variance"`
	StatVariance          int32           `thrift:"stat_variance,6" json:"stat_variance"`
	DiffVariance          int32           `thrift:"diff_variance,7" json:"diff_variance"`
	DiffSum               map[int32]int32 `thrift:"diff_sum,8" json:"diff_sum"`
	ReportName            string          `thrift:"report_name,9" json:"report_name"`
	OpTs                  int32           `thrift:"op_ts,10" json:"op_ts"`
}

func NewReport() *Report {
	return &Report{
		Mt: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Report) IsSetMt() bool {
	return int64(p.Mt) != math.MinInt32-1
}

func (p *Report) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Report) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *Report) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PredictAvgPossibility = v
	}
	return nil
}

func (p *Report) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StatAvgPossibility = v
	}
	return nil
}

func (p *Report) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mt = ModelType(v)
	}
	return nil
}

func (p *Report) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PredictVariance = v
	}
	return nil
}

func (p *Report) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StatVariance = v
	}
	return nil
}

func (p *Report) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.DiffVariance = v
	}
	return nil
}

func (p *Report) readField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.DiffSum = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.DiffSum[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Report) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ReportName = v
	}
	return nil
}

func (p *Report) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.OpTs = v
	}
	return nil
}

func (p *Report) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Report"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Report) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:exchange_id: %s", p, err)
	}
	return err
}

func (p *Report) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("predict_avg_possibility", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:predict_avg_possibility: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PredictAvgPossibility)); err != nil {
		return fmt.Errorf("%T.predict_avg_possibility (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:predict_avg_possibility: %s", p, err)
	}
	return err
}

func (p *Report) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stat_avg_possibility", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stat_avg_possibility: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatAvgPossibility)); err != nil {
		return fmt.Errorf("%T.stat_avg_possibility (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stat_avg_possibility: %s", p, err)
	}
	return err
}

func (p *Report) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMt() {
		if err := oprot.WriteFieldBegin("mt", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:mt: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Mt)); err != nil {
			return fmt.Errorf("%T.mt (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:mt: %s", p, err)
		}
	}
	return err
}

func (p *Report) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("predict_variance", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:predict_variance: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PredictVariance)); err != nil {
		return fmt.Errorf("%T.predict_variance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:predict_variance: %s", p, err)
	}
	return err
}

func (p *Report) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stat_variance", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:stat_variance: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatVariance)); err != nil {
		return fmt.Errorf("%T.stat_variance (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:stat_variance: %s", p, err)
	}
	return err
}

func (p *Report) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("diff_variance", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:diff_variance: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DiffVariance)); err != nil {
		return fmt.Errorf("%T.diff_variance (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:diff_variance: %s", p, err)
	}
	return err
}

func (p *Report) writeField8(oprot thrift.TProtocol) (err error) {
	if p.DiffSum != nil {
		if err := oprot.WriteFieldBegin("diff_sum", thrift.MAP, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:diff_sum: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.DiffSum)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.DiffSum {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:diff_sum: %s", p, err)
		}
	}
	return err
}

func (p *Report) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("report_name", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:report_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReportName)); err != nil {
		return fmt.Errorf("%T.report_name (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:report_name: %s", p, err)
	}
	return err
}

func (p *Report) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("op_ts", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:op_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OpTs)); err != nil {
		return fmt.Errorf("%T.op_ts (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:op_ts: %s", p, err)
	}
	return err
}

func (p *Report) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Report(%+v)", *p)
}

type Assessment struct {
	TrainDataId          int64            `thrift:"train_data_id,1" json:"train_data_id"`
	TestDataId           int64            `thrift:"test_data_id,2" json:"test_data_id"`
	AggregateDataId      int64            `thrift:"aggregate_data_id,3" json:"aggregate_data_id"`
	AssessmentCommitFile string           `thrift:"assessment_commit_file,4" json:"assessment_commit_file"`
	AssessmentName       string           `thrift:"assessment_name,5" json:"assessment_name"`
	ReportId             int64            `thrift:"report_id,6" json:"report_id"`
	ParameterFilePath    string           `thrift:"parameter_file_path,7" json:"parameter_file_path"`
	ExchangeId           int32            `thrift:"exchange_id,8" json:"exchange_id"`
	Mt                   ModelType        `thrift:"mt,9" json:"mt"`
	Version              string           `thrift:"version,10" json:"version"`
	Ass                  AssessmentStatus `thrift:"ass,11" json:"ass"`
	StatusTs             int32            `thrift:"status_ts,12" json:"status_ts"`
	Id                   int64            `thrift:"id,13" json:"id"`
	TestType             int32            `thrift:"test_type,14" json:"test_type"`
	HdfsTestFile         string           `thrift:"hdfs_test_file,15" json:"hdfs_test_file"`
}

func NewAssessment() *Assessment {
	return &Assessment{
		Mt: math.MinInt32 - 1, // unset sentinal value

		Ass: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Assessment) IsSetMt() bool {
	return int64(p.Mt) != math.MinInt32-1
}

func (p *Assessment) IsSetAss() bool {
	return int64(p.Ass) != math.MinInt32-1
}

func (p *Assessment) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Assessment) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TrainDataId = v
	}
	return nil
}

func (p *Assessment) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TestDataId = v
	}
	return nil
}

func (p *Assessment) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AggregateDataId = v
	}
	return nil
}

func (p *Assessment) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AssessmentCommitFile = v
	}
	return nil
}

func (p *Assessment) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AssessmentName = v
	}
	return nil
}

func (p *Assessment) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ReportId = v
	}
	return nil
}

func (p *Assessment) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ParameterFilePath = v
	}
	return nil
}

func (p *Assessment) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *Assessment) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Mt = ModelType(v)
	}
	return nil
}

func (p *Assessment) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *Assessment) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Ass = AssessmentStatus(v)
	}
	return nil
}

func (p *Assessment) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.StatusTs = v
	}
	return nil
}

func (p *Assessment) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Assessment) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.TestType = v
	}
	return nil
}

func (p *Assessment) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.HdfsTestFile = v
	}
	return nil
}

func (p *Assessment) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Assessment"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Assessment) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("train_data_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:train_data_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TrainDataId)); err != nil {
		return fmt.Errorf("%T.train_data_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:train_data_id: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("test_data_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:test_data_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TestDataId)); err != nil {
		return fmt.Errorf("%T.test_data_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:test_data_id: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aggregate_data_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:aggregate_data_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AggregateDataId)); err != nil {
		return fmt.Errorf("%T.aggregate_data_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:aggregate_data_id: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assessment_commit_file", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:assessment_commit_file: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AssessmentCommitFile)); err != nil {
		return fmt.Errorf("%T.assessment_commit_file (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:assessment_commit_file: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assessment_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:assessment_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AssessmentName)); err != nil {
		return fmt.Errorf("%T.assessment_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:assessment_name: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("report_id", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:report_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReportId)); err != nil {
		return fmt.Errorf("%T.report_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:report_id: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parameter_file_path", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:parameter_file_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ParameterFilePath)); err != nil {
		return fmt.Errorf("%T.parameter_file_path (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:parameter_file_path: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:exchange_id: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetMt() {
		if err := oprot.WriteFieldBegin("mt", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:mt: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Mt)); err != nil {
			return fmt.Errorf("%T.mt (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:mt: %s", p, err)
		}
	}
	return err
}

func (p *Assessment) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:version: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetAss() {
		if err := oprot.WriteFieldBegin("ass", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:ass: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Ass)); err != nil {
			return fmt.Errorf("%T.ass (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:ass: %s", p, err)
		}
	}
	return err
}

func (p *Assessment) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status_ts", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:status_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatusTs)); err != nil {
		return fmt.Errorf("%T.status_ts (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:status_ts: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:id: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("test_type", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:test_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TestType)); err != nil {
		return fmt.Errorf("%T.test_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:test_type: %s", p, err)
	}
	return err
}

func (p *Assessment) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hdfs_test_file", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:hdfs_test_file: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HdfsTestFile)); err != nil {
		return fmt.Errorf("%T.hdfs_test_file (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:hdfs_test_file: %s", p, err)
	}
	return err
}

func (p *Assessment) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Assessment(%+v)", *p)
}

type FsailServerException struct {
	Code    FsailServerErrorCode `thrift:"code,1" json:"code"`
	Message string               `thrift:"message,2" json:"message"`
}

func NewFsailServerException() *FsailServerException {
	return &FsailServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FsailServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *FsailServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FsailServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = FsailServerErrorCode(v)
	}
	return nil
}

func (p *FsailServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *FsailServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FsailServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FsailServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *FsailServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *FsailServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FsailServerException(%+v)", *p)
}
