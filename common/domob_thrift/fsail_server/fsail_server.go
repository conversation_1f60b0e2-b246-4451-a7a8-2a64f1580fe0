// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package fsail_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type FsailServer interface {
	dm303.DomobService

	// Parameters:
	//  - Fd
	AddFeaData(fd *FeaData) (r FsailServerErrorCode, fse *FsailServerException, err error)
	// Parameters:
	//  - Ad
	AddAggregateData(ad *AggregationData) (r FsailServerErrorCode, fse *FsailServerException, err error)
	GetAggregateTask() (r *AggregationData, fse *FsailServerException, err error)
	// Parameters:
	//  - Ad
	UpdateAggregateTask(ad *AggregationData) (r FsailServerErrorCode, fse *FsailServerException, err error)
	// Parameters:
	//  - Ass
	AddAssessmentData(ass *Assessment) (r FsailServerErrorCode, fse *FsailServerException, err error)
	GetAssessmentTask() (r *Assessment, fse *FsailServerException, err error)
	// Parameters:
	//  - AssessmentId
	//  - Rpt
	AddReportData(assessment_id int64, rpt *Report) (r FsailServerErrorCode, fse *FsailServerException, err error)
	// Parameters:
	//  - AssessmentName
	GetReportData(assessment_name string) (r *Report, fse *FsailServerException, err error)
	// Parameters:
	//  - Bfb
	AddFeaBuildTask(bfb *BatchFeaBuild) (r FsailServerErrorCode, fse *FsailServerException, err error)
	// Parameters:
	//  - Name
	GetFeaBuildStatus(name string) (r BatchFeaBuildStatus, fse *FsailServerException, err error)
	// Parameters:
	//  - BfbStatus
	GetFeaBuildTask(bfb_status BatchFeaBuildStatus) (r *BatchFeaBuild, fse *FsailServerException, err error)
	// Parameters:
	//  - BfbId
	//  - BfbStatus
	UpdateFeaBuildStatus(bfb_id int64, bfb_status BatchFeaBuildStatus) (r FsailServerErrorCode, fse *FsailServerException, err error)
}

type FsailServerClient struct {
	*dm303.DomobServiceClient
}

func NewFsailServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FsailServerClient {
	return &FsailServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewFsailServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FsailServerClient {
	return &FsailServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Fd
func (p *FsailServerClient) AddFeaData(fd *FeaData) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendAddFeaData(fd); err != nil {
		return
	}
	return p.recvAddFeaData()
}

func (p *FsailServerClient) sendAddFeaData(fd *FeaData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_fea_data", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewAddFeaDataArgs()
	args2.Fd = fd
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvAddFeaData() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewAddFeaDataResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	if result3.Fse != nil {
		fse = result3.Fse
	}
	return
}

// Parameters:
//  - Ad
func (p *FsailServerClient) AddAggregateData(ad *AggregationData) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendAddAggregateData(ad); err != nil {
		return
	}
	return p.recvAddAggregateData()
}

func (p *FsailServerClient) sendAddAggregateData(ad *AggregationData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_aggregate_data", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args6 := NewAddAggregateDataArgs()
	args6.Ad = ad
	if err = args6.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvAddAggregateData() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error8 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error9 error
		error9, err = error8.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error9
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result7 := NewAddAggregateDataResult()
	if err = result7.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result7.Success
	if result7.Fse != nil {
		fse = result7.Fse
	}
	return
}

func (p *FsailServerClient) GetAggregateTask() (r *AggregationData, fse *FsailServerException, err error) {
	if err = p.sendGetAggregateTask(); err != nil {
		return
	}
	return p.recvGetAggregateTask()
}

func (p *FsailServerClient) sendGetAggregateTask() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_aggregate_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args10 := NewGetAggregateTaskArgs()
	if err = args10.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvGetAggregateTask() (value *AggregationData, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error12 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error13 error
		error13, err = error12.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error13
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result11 := NewGetAggregateTaskResult()
	if err = result11.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result11.Success
	if result11.Fse != nil {
		fse = result11.Fse
	}
	return
}

// Parameters:
//  - Ad
func (p *FsailServerClient) UpdateAggregateTask(ad *AggregationData) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendUpdateAggregateTask(ad); err != nil {
		return
	}
	return p.recvUpdateAggregateTask()
}

func (p *FsailServerClient) sendUpdateAggregateTask(ad *AggregationData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_aggregate_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args14 := NewUpdateAggregateTaskArgs()
	args14.Ad = ad
	if err = args14.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvUpdateAggregateTask() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error16 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error17 error
		error17, err = error16.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error17
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result15 := NewUpdateAggregateTaskResult()
	if err = result15.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result15.Success
	if result15.Fse != nil {
		fse = result15.Fse
	}
	return
}

// Parameters:
//  - Ass
func (p *FsailServerClient) AddAssessmentData(ass *Assessment) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendAddAssessmentData(ass); err != nil {
		return
	}
	return p.recvAddAssessmentData()
}

func (p *FsailServerClient) sendAddAssessmentData(ass *Assessment) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_assessment_data", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args18 := NewAddAssessmentDataArgs()
	args18.Ass = ass
	if err = args18.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvAddAssessmentData() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error20 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error21 error
		error21, err = error20.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error21
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result19 := NewAddAssessmentDataResult()
	if err = result19.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result19.Success
	if result19.Fse != nil {
		fse = result19.Fse
	}
	return
}

func (p *FsailServerClient) GetAssessmentTask() (r *Assessment, fse *FsailServerException, err error) {
	if err = p.sendGetAssessmentTask(); err != nil {
		return
	}
	return p.recvGetAssessmentTask()
}

func (p *FsailServerClient) sendGetAssessmentTask() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_assessment_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args22 := NewGetAssessmentTaskArgs()
	if err = args22.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvGetAssessmentTask() (value *Assessment, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error24 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error25 error
		error25, err = error24.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error25
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result23 := NewGetAssessmentTaskResult()
	if err = result23.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result23.Success
	if result23.Fse != nil {
		fse = result23.Fse
	}
	return
}

// Parameters:
//  - AssessmentId
//  - Rpt
func (p *FsailServerClient) AddReportData(assessment_id int64, rpt *Report) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendAddReportData(assessment_id, rpt); err != nil {
		return
	}
	return p.recvAddReportData()
}

func (p *FsailServerClient) sendAddReportData(assessment_id int64, rpt *Report) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_report_data", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args26 := NewAddReportDataArgs()
	args26.AssessmentId = assessment_id
	args26.Rpt = rpt
	if err = args26.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvAddReportData() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error28 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error29 error
		error29, err = error28.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error29
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result27 := NewAddReportDataResult()
	if err = result27.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result27.Success
	if result27.Fse != nil {
		fse = result27.Fse
	}
	return
}

// Parameters:
//  - AssessmentName
func (p *FsailServerClient) GetReportData(assessment_name string) (r *Report, fse *FsailServerException, err error) {
	if err = p.sendGetReportData(assessment_name); err != nil {
		return
	}
	return p.recvGetReportData()
}

func (p *FsailServerClient) sendGetReportData(assessment_name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_report_data", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args30 := NewGetReportDataArgs()
	args30.AssessmentName = assessment_name
	if err = args30.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvGetReportData() (value *Report, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error32 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error33 error
		error33, err = error32.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error33
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result31 := NewGetReportDataResult()
	if err = result31.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result31.Success
	if result31.Fse != nil {
		fse = result31.Fse
	}
	return
}

// Parameters:
//  - Bfb
func (p *FsailServerClient) AddFeaBuildTask(bfb *BatchFeaBuild) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendAddFeaBuildTask(bfb); err != nil {
		return
	}
	return p.recvAddFeaBuildTask()
}

func (p *FsailServerClient) sendAddFeaBuildTask(bfb *BatchFeaBuild) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_fea_build_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args34 := NewAddFeaBuildTaskArgs()
	args34.Bfb = bfb
	if err = args34.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvAddFeaBuildTask() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error36 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error37 error
		error37, err = error36.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error37
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result35 := NewAddFeaBuildTaskResult()
	if err = result35.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result35.Success
	if result35.Fse != nil {
		fse = result35.Fse
	}
	return
}

// Parameters:
//  - Name
func (p *FsailServerClient) GetFeaBuildStatus(name string) (r BatchFeaBuildStatus, fse *FsailServerException, err error) {
	if err = p.sendGetFeaBuildStatus(name); err != nil {
		return
	}
	return p.recvGetFeaBuildStatus()
}

func (p *FsailServerClient) sendGetFeaBuildStatus(name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_fea_build_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args38 := NewGetFeaBuildStatusArgs()
	args38.Name = name
	if err = args38.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvGetFeaBuildStatus() (value BatchFeaBuildStatus, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error40 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error41 error
		error41, err = error40.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error41
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result39 := NewGetFeaBuildStatusResult()
	if err = result39.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result39.Success
	if result39.Fse != nil {
		fse = result39.Fse
	}
	return
}

// Parameters:
//  - BfbStatus
func (p *FsailServerClient) GetFeaBuildTask(bfb_status BatchFeaBuildStatus) (r *BatchFeaBuild, fse *FsailServerException, err error) {
	if err = p.sendGetFeaBuildTask(bfb_status); err != nil {
		return
	}
	return p.recvGetFeaBuildTask()
}

func (p *FsailServerClient) sendGetFeaBuildTask(bfb_status BatchFeaBuildStatus) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_fea_build_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args42 := NewGetFeaBuildTaskArgs()
	args42.BfbStatus = bfb_status
	if err = args42.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvGetFeaBuildTask() (value *BatchFeaBuild, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error44 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error45 error
		error45, err = error44.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error45
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result43 := NewGetFeaBuildTaskResult()
	if err = result43.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result43.Success
	if result43.Fse != nil {
		fse = result43.Fse
	}
	return
}

// Parameters:
//  - BfbId
//  - BfbStatus
func (p *FsailServerClient) UpdateFeaBuildStatus(bfb_id int64, bfb_status BatchFeaBuildStatus) (r FsailServerErrorCode, fse *FsailServerException, err error) {
	if err = p.sendUpdateFeaBuildStatus(bfb_id, bfb_status); err != nil {
		return
	}
	return p.recvUpdateFeaBuildStatus()
}

func (p *FsailServerClient) sendUpdateFeaBuildStatus(bfb_id int64, bfb_status BatchFeaBuildStatus) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_fea_build_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args46 := NewUpdateFeaBuildStatusArgs()
	args46.BfbId = bfb_id
	args46.BfbStatus = bfb_status
	if err = args46.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FsailServerClient) recvUpdateFeaBuildStatus() (value FsailServerErrorCode, fse *FsailServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error48 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error49 error
		error49, err = error48.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error49
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result47 := NewUpdateFeaBuildStatusResult()
	if err = result47.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result47.Success
	if result47.Fse != nil {
		fse = result47.Fse
	}
	return
}

type FsailServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewFsailServerProcessor(handler FsailServer) *FsailServerProcessor {
	self50 := &FsailServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self50.AddToProcessorMap("add_fea_data", &fsailServerProcessorAddFeaData{handler: handler})
	self50.AddToProcessorMap("add_aggregate_data", &fsailServerProcessorAddAggregateData{handler: handler})
	self50.AddToProcessorMap("get_aggregate_task", &fsailServerProcessorGetAggregateTask{handler: handler})
	self50.AddToProcessorMap("update_aggregate_task", &fsailServerProcessorUpdateAggregateTask{handler: handler})
	self50.AddToProcessorMap("add_assessment_data", &fsailServerProcessorAddAssessmentData{handler: handler})
	self50.AddToProcessorMap("get_assessment_task", &fsailServerProcessorGetAssessmentTask{handler: handler})
	self50.AddToProcessorMap("add_report_data", &fsailServerProcessorAddReportData{handler: handler})
	self50.AddToProcessorMap("get_report_data", &fsailServerProcessorGetReportData{handler: handler})
	self50.AddToProcessorMap("add_fea_build_task", &fsailServerProcessorAddFeaBuildTask{handler: handler})
	self50.AddToProcessorMap("get_fea_build_status", &fsailServerProcessorGetFeaBuildStatus{handler: handler})
	self50.AddToProcessorMap("get_fea_build_task", &fsailServerProcessorGetFeaBuildTask{handler: handler})
	self50.AddToProcessorMap("update_fea_build_status", &fsailServerProcessorUpdateFeaBuildStatus{handler: handler})
	return self50
}

type fsailServerProcessorAddFeaData struct {
	handler FsailServer
}

func (p *fsailServerProcessorAddFeaData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFeaDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_fea_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFeaDataResult()
	if result.Success, result.Fse, err = p.handler.AddFeaData(args.Fd); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_fea_data: "+err.Error())
		oprot.WriteMessageBegin("add_fea_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_fea_data", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorAddAggregateData struct {
	handler FsailServer
}

func (p *fsailServerProcessorAddAggregateData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAggregateDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_aggregate_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAggregateDataResult()
	if result.Success, result.Fse, err = p.handler.AddAggregateData(args.Ad); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_aggregate_data: "+err.Error())
		oprot.WriteMessageBegin("add_aggregate_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_aggregate_data", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorGetAggregateTask struct {
	handler FsailServer
}

func (p *fsailServerProcessorGetAggregateTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAggregateTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_aggregate_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAggregateTaskResult()
	if result.Success, result.Fse, err = p.handler.GetAggregateTask(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_aggregate_task: "+err.Error())
		oprot.WriteMessageBegin("get_aggregate_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_aggregate_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorUpdateAggregateTask struct {
	handler FsailServer
}

func (p *fsailServerProcessorUpdateAggregateTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateAggregateTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_aggregate_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateAggregateTaskResult()
	if result.Success, result.Fse, err = p.handler.UpdateAggregateTask(args.Ad); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_aggregate_task: "+err.Error())
		oprot.WriteMessageBegin("update_aggregate_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_aggregate_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorAddAssessmentData struct {
	handler FsailServer
}

func (p *fsailServerProcessorAddAssessmentData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAssessmentDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_assessment_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAssessmentDataResult()
	if result.Success, result.Fse, err = p.handler.AddAssessmentData(args.Ass); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_assessment_data: "+err.Error())
		oprot.WriteMessageBegin("add_assessment_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_assessment_data", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorGetAssessmentTask struct {
	handler FsailServer
}

func (p *fsailServerProcessorGetAssessmentTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAssessmentTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_assessment_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAssessmentTaskResult()
	if result.Success, result.Fse, err = p.handler.GetAssessmentTask(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_assessment_task: "+err.Error())
		oprot.WriteMessageBegin("get_assessment_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_assessment_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorAddReportData struct {
	handler FsailServer
}

func (p *fsailServerProcessorAddReportData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddReportDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_report_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddReportDataResult()
	if result.Success, result.Fse, err = p.handler.AddReportData(args.AssessmentId, args.Rpt); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_report_data: "+err.Error())
		oprot.WriteMessageBegin("add_report_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_report_data", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorGetReportData struct {
	handler FsailServer
}

func (p *fsailServerProcessorGetReportData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetReportDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_report_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetReportDataResult()
	if result.Success, result.Fse, err = p.handler.GetReportData(args.AssessmentName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_report_data: "+err.Error())
		oprot.WriteMessageBegin("get_report_data", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_report_data", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorAddFeaBuildTask struct {
	handler FsailServer
}

func (p *fsailServerProcessorAddFeaBuildTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFeaBuildTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_fea_build_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFeaBuildTaskResult()
	if result.Success, result.Fse, err = p.handler.AddFeaBuildTask(args.Bfb); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_fea_build_task: "+err.Error())
		oprot.WriteMessageBegin("add_fea_build_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_fea_build_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorGetFeaBuildStatus struct {
	handler FsailServer
}

func (p *fsailServerProcessorGetFeaBuildStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFeaBuildStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_fea_build_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFeaBuildStatusResult()
	if result.Success, result.Fse, err = p.handler.GetFeaBuildStatus(args.Name); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_fea_build_status: "+err.Error())
		oprot.WriteMessageBegin("get_fea_build_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_fea_build_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorGetFeaBuildTask struct {
	handler FsailServer
}

func (p *fsailServerProcessorGetFeaBuildTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFeaBuildTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_fea_build_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFeaBuildTaskResult()
	if result.Success, result.Fse, err = p.handler.GetFeaBuildTask(args.BfbStatus); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_fea_build_task: "+err.Error())
		oprot.WriteMessageBegin("get_fea_build_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_fea_build_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type fsailServerProcessorUpdateFeaBuildStatus struct {
	handler FsailServer
}

func (p *fsailServerProcessorUpdateFeaBuildStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateFeaBuildStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_fea_build_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateFeaBuildStatusResult()
	if result.Success, result.Fse, err = p.handler.UpdateFeaBuildStatus(args.BfbId, args.BfbStatus); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_fea_build_status: "+err.Error())
		oprot.WriteMessageBegin("update_fea_build_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_fea_build_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddFeaDataArgs struct {
	Fd *FeaData `thrift:"fd,1" json:"fd"`
}

func NewAddFeaDataArgs() *AddFeaDataArgs {
	return &AddFeaDataArgs{}
}

func (p *AddFeaDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFeaDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Fd = NewFeaData()
	if err := p.Fd.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fd)
	}
	return nil
}

func (p *AddFeaDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_fea_data_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFeaDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fd != nil {
		if err := oprot.WriteFieldBegin("fd", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fd: %s", p, err)
		}
		if err := p.Fd.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fd)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fd: %s", p, err)
		}
	}
	return err
}

func (p *AddFeaDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFeaDataArgs(%+v)", *p)
}

type AddFeaDataResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewAddFeaDataResult() *AddFeaDataResult {
	return &AddFeaDataResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddFeaDataResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddFeaDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFeaDataResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *AddFeaDataResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *AddFeaDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_fea_data_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFeaDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddFeaDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *AddFeaDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFeaDataResult(%+v)", *p)
}

type AddAggregateDataArgs struct {
	Ad *AggregationData `thrift:"ad,1" json:"ad"`
}

func NewAddAggregateDataArgs() *AddAggregateDataArgs {
	return &AddAggregateDataArgs{}
}

func (p *AddAggregateDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAggregateDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Ad = NewAggregationData()
	if err := p.Ad.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ad)
	}
	return nil
}

func (p *AddAggregateDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_aggregate_data_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAggregateDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ad != nil {
		if err := oprot.WriteFieldBegin("ad", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ad: %s", p, err)
		}
		if err := p.Ad.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ad)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ad: %s", p, err)
		}
	}
	return err
}

func (p *AddAggregateDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAggregateDataArgs(%+v)", *p)
}

type AddAggregateDataResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewAddAggregateDataResult() *AddAggregateDataResult {
	return &AddAggregateDataResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddAggregateDataResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddAggregateDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAggregateDataResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *AddAggregateDataResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *AddAggregateDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_aggregate_data_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAggregateDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddAggregateDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *AddAggregateDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAggregateDataResult(%+v)", *p)
}

type GetAggregateTaskArgs struct {
}

func NewGetAggregateTaskArgs() *GetAggregateTaskArgs {
	return &GetAggregateTaskArgs{}
}

func (p *GetAggregateTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAggregateTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_aggregate_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAggregateTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAggregateTaskArgs(%+v)", *p)
}

type GetAggregateTaskResult struct {
	Success *AggregationData      `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewGetAggregateTaskResult() *GetAggregateTaskResult {
	return &GetAggregateTaskResult{}
}

func (p *GetAggregateTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAggregateTaskResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAggregationData()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAggregateTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *GetAggregateTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_aggregate_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAggregateTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAggregateTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *GetAggregateTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAggregateTaskResult(%+v)", *p)
}

type UpdateAggregateTaskArgs struct {
	Ad *AggregationData `thrift:"ad,1" json:"ad"`
}

func NewUpdateAggregateTaskArgs() *UpdateAggregateTaskArgs {
	return &UpdateAggregateTaskArgs{}
}

func (p *UpdateAggregateTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateAggregateTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Ad = NewAggregationData()
	if err := p.Ad.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ad)
	}
	return nil
}

func (p *UpdateAggregateTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_aggregate_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateAggregateTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ad != nil {
		if err := oprot.WriteFieldBegin("ad", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ad: %s", p, err)
		}
		if err := p.Ad.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ad)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ad: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAggregateTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAggregateTaskArgs(%+v)", *p)
}

type UpdateAggregateTaskResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewUpdateAggregateTaskResult() *UpdateAggregateTaskResult {
	return &UpdateAggregateTaskResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateAggregateTaskResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateAggregateTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateAggregateTaskResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *UpdateAggregateTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *UpdateAggregateTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_aggregate_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateAggregateTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAggregateTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAggregateTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAggregateTaskResult(%+v)", *p)
}

type AddAssessmentDataArgs struct {
	Ass *Assessment `thrift:"ass,1" json:"ass"`
}

func NewAddAssessmentDataArgs() *AddAssessmentDataArgs {
	return &AddAssessmentDataArgs{}
}

func (p *AddAssessmentDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAssessmentDataArgs) readField1(iprot thrift.TProtocol) error {
	p.Ass = NewAssessment()
	if err := p.Ass.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ass)
	}
	return nil
}

func (p *AddAssessmentDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_assessment_data_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAssessmentDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ass != nil {
		if err := oprot.WriteFieldBegin("ass", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ass: %s", p, err)
		}
		if err := p.Ass.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ass)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ass: %s", p, err)
		}
	}
	return err
}

func (p *AddAssessmentDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAssessmentDataArgs(%+v)", *p)
}

type AddAssessmentDataResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewAddAssessmentDataResult() *AddAssessmentDataResult {
	return &AddAssessmentDataResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddAssessmentDataResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddAssessmentDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAssessmentDataResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *AddAssessmentDataResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *AddAssessmentDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_assessment_data_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAssessmentDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddAssessmentDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *AddAssessmentDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAssessmentDataResult(%+v)", *p)
}

type GetAssessmentTaskArgs struct {
}

func NewGetAssessmentTaskArgs() *GetAssessmentTaskArgs {
	return &GetAssessmentTaskArgs{}
}

func (p *GetAssessmentTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAssessmentTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_assessment_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAssessmentTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAssessmentTaskArgs(%+v)", *p)
}

type GetAssessmentTaskResult struct {
	Success *Assessment           `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewGetAssessmentTaskResult() *GetAssessmentTaskResult {
	return &GetAssessmentTaskResult{}
}

func (p *GetAssessmentTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAssessmentTaskResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAssessment()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAssessmentTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *GetAssessmentTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_assessment_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAssessmentTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAssessmentTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *GetAssessmentTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAssessmentTaskResult(%+v)", *p)
}

type AddReportDataArgs struct {
	AssessmentId int64   `thrift:"assessment_id,1" json:"assessment_id"`
	Rpt          *Report `thrift:"rpt,2" json:"rpt"`
}

func NewAddReportDataArgs() *AddReportDataArgs {
	return &AddReportDataArgs{}
}

func (p *AddReportDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddReportDataArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AssessmentId = v
	}
	return nil
}

func (p *AddReportDataArgs) readField2(iprot thrift.TProtocol) error {
	p.Rpt = NewReport()
	if err := p.Rpt.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rpt)
	}
	return nil
}

func (p *AddReportDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_report_data_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddReportDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assessment_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:assessment_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AssessmentId)); err != nil {
		return fmt.Errorf("%T.assessment_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:assessment_id: %s", p, err)
	}
	return err
}

func (p *AddReportDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Rpt != nil {
		if err := oprot.WriteFieldBegin("rpt", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:rpt: %s", p, err)
		}
		if err := p.Rpt.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rpt)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:rpt: %s", p, err)
		}
	}
	return err
}

func (p *AddReportDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddReportDataArgs(%+v)", *p)
}

type AddReportDataResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewAddReportDataResult() *AddReportDataResult {
	return &AddReportDataResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddReportDataResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddReportDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddReportDataResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *AddReportDataResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *AddReportDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_report_data_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddReportDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddReportDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *AddReportDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddReportDataResult(%+v)", *p)
}

type GetReportDataArgs struct {
	AssessmentName string `thrift:"assessment_name,1" json:"assessment_name"`
}

func NewGetReportDataArgs() *GetReportDataArgs {
	return &GetReportDataArgs{}
}

func (p *GetReportDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetReportDataArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AssessmentName = v
	}
	return nil
}

func (p *GetReportDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_report_data_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetReportDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assessment_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:assessment_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AssessmentName)); err != nil {
		return fmt.Errorf("%T.assessment_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:assessment_name: %s", p, err)
	}
	return err
}

func (p *GetReportDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetReportDataArgs(%+v)", *p)
}

type GetReportDataResult struct {
	Success *Report               `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewGetReportDataResult() *GetReportDataResult {
	return &GetReportDataResult{}
}

func (p *GetReportDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetReportDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewReport()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetReportDataResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *GetReportDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_report_data_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetReportDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetReportDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *GetReportDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetReportDataResult(%+v)", *p)
}

type AddFeaBuildTaskArgs struct {
	Bfb *BatchFeaBuild `thrift:"bfb,1" json:"bfb"`
}

func NewAddFeaBuildTaskArgs() *AddFeaBuildTaskArgs {
	return &AddFeaBuildTaskArgs{}
}

func (p *AddFeaBuildTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFeaBuildTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Bfb = NewBatchFeaBuild()
	if err := p.Bfb.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Bfb)
	}
	return nil
}

func (p *AddFeaBuildTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_fea_build_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFeaBuildTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Bfb != nil {
		if err := oprot.WriteFieldBegin("bfb", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:bfb: %s", p, err)
		}
		if err := p.Bfb.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Bfb)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:bfb: %s", p, err)
		}
	}
	return err
}

func (p *AddFeaBuildTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFeaBuildTaskArgs(%+v)", *p)
}

type AddFeaBuildTaskResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewAddFeaBuildTaskResult() *AddFeaBuildTaskResult {
	return &AddFeaBuildTaskResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddFeaBuildTaskResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddFeaBuildTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFeaBuildTaskResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *AddFeaBuildTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *AddFeaBuildTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_fea_build_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFeaBuildTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddFeaBuildTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *AddFeaBuildTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFeaBuildTaskResult(%+v)", *p)
}

type GetFeaBuildStatusArgs struct {
	Name string `thrift:"name,1" json:"name"`
}

func NewGetFeaBuildStatusArgs() *GetFeaBuildStatusArgs {
	return &GetFeaBuildStatusArgs{}
}

func (p *GetFeaBuildStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildStatusArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *GetFeaBuildStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_fea_build_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *GetFeaBuildStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFeaBuildStatusArgs(%+v)", *p)
}

type GetFeaBuildStatusResult struct {
	Success BatchFeaBuildStatus   `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewGetFeaBuildStatusResult() *GetFeaBuildStatusResult {
	return &GetFeaBuildStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetFeaBuildStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *GetFeaBuildStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = BatchFeaBuildStatus(v)
	}
	return nil
}

func (p *GetFeaBuildStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *GetFeaBuildStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_fea_build_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFeaBuildStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *GetFeaBuildStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFeaBuildStatusResult(%+v)", *p)
}

type GetFeaBuildTaskArgs struct {
	BfbStatus BatchFeaBuildStatus `thrift:"bfb_status,1" json:"bfb_status"`
}

func NewGetFeaBuildTaskArgs() *GetFeaBuildTaskArgs {
	return &GetFeaBuildTaskArgs{
		BfbStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetFeaBuildTaskArgs) IsSetBfbStatus() bool {
	return int64(p.BfbStatus) != math.MinInt32-1
}

func (p *GetFeaBuildTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildTaskArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BfbStatus = BatchFeaBuildStatus(v)
	}
	return nil
}

func (p *GetFeaBuildTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_fea_build_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBfbStatus() {
		if err := oprot.WriteFieldBegin("bfb_status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:bfb_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BfbStatus)); err != nil {
			return fmt.Errorf("%T.bfb_status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:bfb_status: %s", p, err)
		}
	}
	return err
}

func (p *GetFeaBuildTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFeaBuildTaskArgs(%+v)", *p)
}

type GetFeaBuildTaskResult struct {
	Success *BatchFeaBuild        `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewGetFeaBuildTaskResult() *GetFeaBuildTaskResult {
	return &GetFeaBuildTaskResult{}
}

func (p *GetFeaBuildTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildTaskResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewBatchFeaBuild()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFeaBuildTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *GetFeaBuildTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_fea_build_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFeaBuildTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFeaBuildTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *GetFeaBuildTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFeaBuildTaskResult(%+v)", *p)
}

type UpdateFeaBuildStatusArgs struct {
	BfbId     int64               `thrift:"bfb_id,1" json:"bfb_id"`
	BfbStatus BatchFeaBuildStatus `thrift:"bfb_status,2" json:"bfb_status"`
}

func NewUpdateFeaBuildStatusArgs() *UpdateFeaBuildStatusArgs {
	return &UpdateFeaBuildStatusArgs{
		BfbStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateFeaBuildStatusArgs) IsSetBfbStatus() bool {
	return int64(p.BfbStatus) != math.MinInt32-1
}

func (p *UpdateFeaBuildStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFeaBuildStatusArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BfbId = v
	}
	return nil
}

func (p *UpdateFeaBuildStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BfbStatus = BatchFeaBuildStatus(v)
	}
	return nil
}

func (p *UpdateFeaBuildStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_fea_build_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFeaBuildStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bfb_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bfb_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.BfbId)); err != nil {
		return fmt.Errorf("%T.bfb_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bfb_id: %s", p, err)
	}
	return err
}

func (p *UpdateFeaBuildStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBfbStatus() {
		if err := oprot.WriteFieldBegin("bfb_status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:bfb_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BfbStatus)); err != nil {
			return fmt.Errorf("%T.bfb_status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:bfb_status: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFeaBuildStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFeaBuildStatusArgs(%+v)", *p)
}

type UpdateFeaBuildStatusResult struct {
	Success FsailServerErrorCode  `thrift:"success,0" json:"success"`
	Fse     *FsailServerException `thrift:"fse,1" json:"fse"`
}

func NewUpdateFeaBuildStatusResult() *UpdateFeaBuildStatusResult {
	return &UpdateFeaBuildStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateFeaBuildStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateFeaBuildStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFeaBuildStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FsailServerErrorCode(v)
	}
	return nil
}

func (p *UpdateFeaBuildStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Fse = NewFsailServerException()
	if err := p.Fse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fse)
	}
	return nil
}

func (p *UpdateFeaBuildStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_fea_build_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFeaBuildStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFeaBuildStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fse != nil {
		if err := oprot.WriteFieldBegin("fse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fse: %s", p, err)
		}
		if err := p.Fse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fse: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFeaBuildStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFeaBuildStatusResult(%+v)", *p)
}
