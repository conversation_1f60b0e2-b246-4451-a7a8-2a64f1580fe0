// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"fsail_server"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode add_fea_data(FeaData fd)")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode add_aggregate_data(AggregationData ad)")
	fmt.Fprintln(os.<PERSON>, "  AggregationData get_aggregate_task()")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode update_aggregate_task(AggregationData ad)")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode add_assessment_data(Assessment ass)")
	fmt.Fprintln(os.Stderr, "  Assessment get_assessment_task()")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode add_report_data(i64 assessment_id, Report rpt)")
	fmt.Fprintln(os.Stderr, "  Report get_report_data(string assessment_name)")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode add_fea_build_task(BatchFeaBuild bfb)")
	fmt.Fprintln(os.Stderr, "  BatchFeaBuildStatus get_fea_build_status(string name)")
	fmt.Fprintln(os.Stderr, "  BatchFeaBuild get_fea_build_task(BatchFeaBuildStatus bfb_status)")
	fmt.Fprintln(os.Stderr, "  FsailServerErrorCode update_fea_build_status(i64 bfb_id, BatchFeaBuildStatus bfb_status)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := fsail_server.NewFsailServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "add_fea_data":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddFeaData requires 1 args")
			flag.Usage()
		}
		arg51 := flag.Arg(1)
		mbTrans52 := thrift.NewTMemoryBufferLen(len(arg51))
		defer mbTrans52.Close()
		_, err53 := mbTrans52.WriteString(arg51)
		if err53 != nil {
			Usage()
			return
		}
		factory54 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt55 := factory54.GetProtocol(mbTrans52)
		argvalue0 := fsail_server.NewFeaData()
		err56 := argvalue0.Read(jsProt55)
		if err56 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddFeaData(value0))
		fmt.Print("\n")
		break
	case "add_aggregate_data":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddAggregateData requires 1 args")
			flag.Usage()
		}
		arg57 := flag.Arg(1)
		mbTrans58 := thrift.NewTMemoryBufferLen(len(arg57))
		defer mbTrans58.Close()
		_, err59 := mbTrans58.WriteString(arg57)
		if err59 != nil {
			Usage()
			return
		}
		factory60 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt61 := factory60.GetProtocol(mbTrans58)
		argvalue0 := fsail_server.NewAggregationData()
		err62 := argvalue0.Read(jsProt61)
		if err62 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddAggregateData(value0))
		fmt.Print("\n")
		break
	case "get_aggregate_task":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetAggregateTask requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetAggregateTask())
		fmt.Print("\n")
		break
	case "update_aggregate_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateAggregateTask requires 1 args")
			flag.Usage()
		}
		arg63 := flag.Arg(1)
		mbTrans64 := thrift.NewTMemoryBufferLen(len(arg63))
		defer mbTrans64.Close()
		_, err65 := mbTrans64.WriteString(arg63)
		if err65 != nil {
			Usage()
			return
		}
		factory66 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt67 := factory66.GetProtocol(mbTrans64)
		argvalue0 := fsail_server.NewAggregationData()
		err68 := argvalue0.Read(jsProt67)
		if err68 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateAggregateTask(value0))
		fmt.Print("\n")
		break
	case "add_assessment_data":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddAssessmentData requires 1 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := fsail_server.NewAssessment()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddAssessmentData(value0))
		fmt.Print("\n")
		break
	case "get_assessment_task":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetAssessmentTask requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetAssessmentTask())
		fmt.Print("\n")
		break
	case "add_report_data":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddReportData requires 2 args")
			flag.Usage()
		}
		argvalue0, err75 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err75 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg76 := flag.Arg(2)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue1 := fsail_server.NewReport()
		err81 := argvalue1.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddReportData(value0, value1))
		fmt.Print("\n")
		break
	case "get_report_data":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetReportData requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetReportData(value0))
		fmt.Print("\n")
		break
	case "add_fea_build_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddFeaBuildTask requires 1 args")
			flag.Usage()
		}
		arg83 := flag.Arg(1)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		argvalue0 := fsail_server.NewBatchFeaBuild()
		err88 := argvalue0.Read(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddFeaBuildTask(value0))
		fmt.Print("\n")
		break
	case "get_fea_build_status":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetFeaBuildStatus requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetFeaBuildStatus(value0))
		fmt.Print("\n")
		break
	case "get_fea_build_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetFeaBuildTask requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := fsail_server.BatchFeaBuildStatus(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetFeaBuildTask(value0))
		fmt.Print("\n")
		break
	case "update_fea_build_status":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateFeaBuildStatus requires 2 args")
			flag.Usage()
		}
		argvalue0, err90 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err90 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := fsail_server.BatchFeaBuildStatus(tmp1)
		value1 := argvalue1
		fmt.Print(client.UpdateFeaBuildStatus(value0, value1))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err95 := (strconv.Atoi(flag.Arg(1)))
		if err95 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
