// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_adserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/sos_adserver_types"
	"rtb_model_server/common/domob_thrift/sos_ui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = sos_adserver_types.GoUnusedProtection__
var _ = sos_ui_types.GoUnusedProtection__

type AdServer interface {
	// 请求广告列表
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetAds(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error)
	// 根据offerid请求offer列表,用作翻页后的请求,请求单个offer详情时也使用这个接口
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetSpecificAds(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error)
	// Parameters:
	//  - Header
	//  - UiRequest
	GetSuperTasks(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error)
	// 获取用户的当前积分
	//
	// 如果系统中有这个用户的记录，则consumeStatus=CONSUME_SUCCESS,否则
	// consumeStatus = INVALID_USER，并且返回的积分都是0
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetPoint(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.PointResponse, err error)
	// 积分消费
	//
	// consumeStatus可能出现各种值，客户端应该判断一下
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	ConsumePoint(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.PointResponse, err error)
	// 获取imei对应的uid
	//
	// Parameters:
	//  - Header
	//  - Imei
	//  - Channel
	//  - Vc
	GetDeviceId(header *common.RequestHeader, imei string, channel string, vc string) (r int32, err error)
	// 请求深度任务列表
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetTasks(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error)
}

type AdServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAdServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AdServerClient {
	return &AdServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAdServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AdServerClient {
	return &AdServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 请求广告列表
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AdServerClient) GetAds(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error) {
	if err = p.sendGetAds(header, ui_request); err != nil {
		return
	}
	return p.recvGetAds()
}

func (p *AdServerClient) sendGetAds(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetAdsArgs()
	args0.Header = header
	args0.UiRequest = ui_request
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvGetAds() (value *sos_adserver_types.AdResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetAdsResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 根据offerid请求offer列表,用作翻页后的请求,请求单个offer详情时也使用这个接口
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AdServerClient) GetSpecificAds(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error) {
	if err = p.sendGetSpecificAds(header, ui_request); err != nil {
		return
	}
	return p.recvGetSpecificAds()
}

func (p *AdServerClient) sendGetSpecificAds(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSpecificAds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetSpecificAdsArgs()
	args4.Header = header
	args4.UiRequest = ui_request
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvGetSpecificAds() (value *sos_adserver_types.AdResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetSpecificAdsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// Parameters:
//  - Header
//  - UiRequest
func (p *AdServerClient) GetSuperTasks(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error) {
	if err = p.sendGetSuperTasks(header, ui_request); err != nil {
		return
	}
	return p.recvGetSuperTasks()
}

func (p *AdServerClient) sendGetSuperTasks(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSuperTasks", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetSuperTasksArgs()
	args8.Header = header
	args8.UiRequest = ui_request
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvGetSuperTasks() (value *sos_adserver_types.AdResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetSuperTasksResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 获取用户的当前积分
//
// 如果系统中有这个用户的记录，则consumeStatus=CONSUME_SUCCESS,否则
// consumeStatus = INVALID_USER，并且返回的积分都是0
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AdServerClient) GetPoint(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.PointResponse, err error) {
	if err = p.sendGetPoint(header, ui_request); err != nil {
		return
	}
	return p.recvGetPoint()
}

func (p *AdServerClient) sendGetPoint(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetPointArgs()
	args12.Header = header
	args12.UiRequest = ui_request
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvGetPoint() (value *sos_adserver_types.PointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetPointResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 积分消费
//
// consumeStatus可能出现各种值，客户端应该判断一下
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AdServerClient) ConsumePoint(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.PointResponse, err error) {
	if err = p.sendConsumePoint(header, ui_request); err != nil {
		return
	}
	return p.recvConsumePoint()
}

func (p *AdServerClient) sendConsumePoint(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("ConsumePoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewConsumePointArgs()
	args16.Header = header
	args16.UiRequest = ui_request
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvConsumePoint() (value *sos_adserver_types.PointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewConsumePointResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 获取imei对应的uid
//
// Parameters:
//  - Header
//  - Imei
//  - Channel
//  - Vc
func (p *AdServerClient) GetDeviceId(header *common.RequestHeader, imei string, channel string, vc string) (r int32, err error) {
	if err = p.sendGetDeviceId(header, imei, channel, vc); err != nil {
		return
	}
	return p.recvGetDeviceId()
}

func (p *AdServerClient) sendGetDeviceId(header *common.RequestHeader, imei string, channel string, vc string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getDeviceId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetDeviceIdArgs()
	args20.Header = header
	args20.Imei = imei
	args20.Channel = channel
	args20.Vc = vc
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvGetDeviceId() (value int32, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetDeviceIdResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 请求深度任务列表
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AdServerClient) GetTasks(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (r *sos_adserver_types.AdResponse, err error) {
	if err = p.sendGetTasks(header, ui_request); err != nil {
		return
	}
	return p.recvGetTasks()
}

func (p *AdServerClient) sendGetTasks(header *common.RequestHeader, ui_request *sos_ui_types.SosUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTasks", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetTasksArgs()
	args24.Header = header
	args24.UiRequest = ui_request
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AdServerClient) recvGetTasks() (value *sos_adserver_types.AdResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetTasksResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

type AdServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AdServer
}

func (p *AdServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AdServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AdServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAdServerProcessor(handler AdServer) *AdServerProcessor {

	self28 := &AdServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self28.processorMap["getAds"] = &adServerProcessorGetAds{handler: handler}
	self28.processorMap["getSpecificAds"] = &adServerProcessorGetSpecificAds{handler: handler}
	self28.processorMap["getSuperTasks"] = &adServerProcessorGetSuperTasks{handler: handler}
	self28.processorMap["getPoint"] = &adServerProcessorGetPoint{handler: handler}
	self28.processorMap["ConsumePoint"] = &adServerProcessorConsumePoint{handler: handler}
	self28.processorMap["getDeviceId"] = &adServerProcessorGetDeviceId{handler: handler}
	self28.processorMap["getTasks"] = &adServerProcessorGetTasks{handler: handler}
	return self28
}

func (p *AdServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x29 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x29.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x29

}

type adServerProcessorGetAds struct {
	handler AdServer
}

func (p *adServerProcessorGetAds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAdsResult()
	if result.Success, err = p.handler.GetAds(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAds: "+err.Error())
		oprot.WriteMessageBegin("getAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type adServerProcessorGetSpecificAds struct {
	handler AdServer
}

func (p *adServerProcessorGetSpecificAds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSpecificAdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSpecificAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSpecificAdsResult()
	if result.Success, err = p.handler.GetSpecificAds(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSpecificAds: "+err.Error())
		oprot.WriteMessageBegin("getSpecificAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSpecificAds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type adServerProcessorGetSuperTasks struct {
	handler AdServer
}

func (p *adServerProcessorGetSuperTasks) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSuperTasksArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSuperTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSuperTasksResult()
	if result.Success, err = p.handler.GetSuperTasks(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSuperTasks: "+err.Error())
		oprot.WriteMessageBegin("getSuperTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSuperTasks", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type adServerProcessorGetPoint struct {
	handler AdServer
}

func (p *adServerProcessorGetPoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPointResult()
	if result.Success, err = p.handler.GetPoint(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPoint: "+err.Error())
		oprot.WriteMessageBegin("getPoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type adServerProcessorConsumePoint struct {
	handler AdServer
}

func (p *adServerProcessorConsumePoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConsumePointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ConsumePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConsumePointResult()
	if result.Success, err = p.handler.ConsumePoint(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ConsumePoint: "+err.Error())
		oprot.WriteMessageBegin("ConsumePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("ConsumePoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type adServerProcessorGetDeviceId struct {
	handler AdServer
}

func (p *adServerProcessorGetDeviceId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDeviceIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getDeviceId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDeviceIdResult()
	if result.Success, err = p.handler.GetDeviceId(args.Header, args.Imei, args.Channel, args.Vc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDeviceId: "+err.Error())
		oprot.WriteMessageBegin("getDeviceId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getDeviceId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type adServerProcessorGetTasks struct {
	handler AdServer
}

func (p *adServerProcessorGetTasks) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTasksArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTasksResult()
	if result.Success, err = p.handler.GetTasks(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTasks: "+err.Error())
		oprot.WriteMessageBegin("getTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTasks", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetAdsArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetAdsArgs() *GetAdsArgs {
	return &GetAdsArgs{}
}

func (p *GetAdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAdsArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetAdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdsArgs(%+v)", *p)
}

type GetAdsResult struct {
	Success *sos_adserver_types.AdResponse `thrift:"success,0" json:"success"`
}

func NewGetAdsResult() *GetAdsResult {
	return &GetAdsResult{}
}

func (p *GetAdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = sos_adserver_types.NewAdResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdsResult(%+v)", *p)
}

type GetSpecificAdsArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetSpecificAdsArgs() *GetSpecificAdsArgs {
	return &GetSpecificAdsArgs{}
}

func (p *GetSpecificAdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificAdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSpecificAdsArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetSpecificAdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificAds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificAdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificAdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificAdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificAdsArgs(%+v)", *p)
}

type GetSpecificAdsResult struct {
	Success *sos_adserver_types.AdResponse `thrift:"success,0" json:"success"`
}

func NewGetSpecificAdsResult() *GetSpecificAdsResult {
	return &GetSpecificAdsResult{}
}

func (p *GetSpecificAdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificAdsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = sos_adserver_types.NewAdResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSpecificAdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificAds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificAdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificAdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificAdsResult(%+v)", *p)
}

type GetSuperTasksArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetSuperTasksArgs() *GetSuperTasksArgs {
	return &GetSuperTasksArgs{}
}

func (p *GetSuperTasksArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSuperTasksArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSuperTasksArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetSuperTasksArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSuperTasks_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSuperTasksArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSuperTasksArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetSuperTasksArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSuperTasksArgs(%+v)", *p)
}

type GetSuperTasksResult struct {
	Success *sos_adserver_types.AdResponse `thrift:"success,0" json:"success"`
}

func NewGetSuperTasksResult() *GetSuperTasksResult {
	return &GetSuperTasksResult{}
}

func (p *GetSuperTasksResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSuperTasksResult) readField0(iprot thrift.TProtocol) error {
	p.Success = sos_adserver_types.NewAdResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSuperTasksResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSuperTasks_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSuperTasksResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSuperTasksResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSuperTasksResult(%+v)", *p)
}

type GetPointArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetPointArgs() *GetPointArgs {
	return &GetPointArgs{}
}

func (p *GetPointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPointArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPointArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetPointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetPointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPointArgs(%+v)", *p)
}

type GetPointResult struct {
	Success *sos_adserver_types.PointResponse `thrift:"success,0" json:"success"`
}

func NewGetPointResult() *GetPointResult {
	return &GetPointResult{}
}

func (p *GetPointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = sos_adserver_types.NewPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPointResult(%+v)", *p)
}

type ConsumePointArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewConsumePointArgs() *ConsumePointArgs {
	return &ConsumePointArgs{}
}

func (p *ConsumePointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConsumePointArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *ConsumePointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ConsumePoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumePointArgs(%+v)", *p)
}

type ConsumePointResult struct {
	Success *sos_adserver_types.PointResponse `thrift:"success,0" json:"success"`
}

func NewConsumePointResult() *ConsumePointResult {
	return &ConsumePointResult{}
}

func (p *ConsumePointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = sos_adserver_types.NewPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConsumePointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ConsumePoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumePointResult(%+v)", *p)
}

type GetDeviceIdArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Imei    string                `thrift:"imei,2" json:"imei"`
	Channel string                `thrift:"channel,3" json:"channel"`
	Vc      string                `thrift:"vc,4" json:"vc"`
}

func NewGetDeviceIdArgs() *GetDeviceIdArgs {
	return &GetDeviceIdArgs{}
}

func (p *GetDeviceIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDeviceIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetDeviceIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *GetDeviceIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *GetDeviceIdArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Vc = v
	}
	return nil
}

func (p *GetDeviceIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDeviceId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDeviceIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetDeviceIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imei: %s", p, err)
	}
	return err
}

func (p *GetDeviceIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channel: %s", p, err)
	}
	return err
}

func (p *GetDeviceIdArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vc", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:vc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vc)); err != nil {
		return fmt.Errorf("%T.vc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:vc: %s", p, err)
	}
	return err
}

func (p *GetDeviceIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeviceIdArgs(%+v)", *p)
}

type GetDeviceIdResult struct {
	Success int32 `thrift:"success,0" json:"success"`
}

func NewGetDeviceIdResult() *GetDeviceIdResult {
	return &GetDeviceIdResult{}
}

func (p *GetDeviceIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDeviceIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetDeviceIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDeviceId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDeviceIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetDeviceIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeviceIdResult(%+v)", *p)
}

type GetTasksArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetTasksArgs() *GetTasksArgs {
	return &GetTasksArgs{}
}

func (p *GetTasksArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTasksArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTasksArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetTasksArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTasks_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTasksArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTasksArgs(%+v)", *p)
}

type GetTasksResult struct {
	Success *sos_adserver_types.AdResponse `thrift:"success,0" json:"success"`
}

func NewGetTasksResult() *GetTasksResult {
	return &GetTasksResult{}
}

func (p *GetTasksResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTasksResult) readField0(iprot thrift.TProtocol) error {
	p.Success = sos_adserver_types.NewAdResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTasksResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTasks_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTasksResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTasksResult(%+v)", *p)
}
