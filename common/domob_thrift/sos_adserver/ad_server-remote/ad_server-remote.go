// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"sos_adserver"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  AdResponse getAds(RequestHeader header, SosUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  AdResponse getSpecificAds(RequestHeader header, SosUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  AdResponse getSuperTasks(RequestHeader header, SosUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  PointResponse getPoint(RequestHeader header, SosUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  PointResponse ConsumePoint(RequestHeader header, SosUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  i32 getDeviceId(RequestHeader header, string imei, string channel, string vc)")
	fmt.Fprintln(os.Stderr, "  AdResponse getTasks(RequestHeader header, SosUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := sos_adserver.NewAdServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getAds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAds requires 2 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := sos_adserver.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg36 := flag.Arg(2)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue1 := sos_adserver.NewSosUIProccessedRequest()
		err41 := argvalue1.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAds(value0, value1))
		fmt.Print("\n")
		break
	case "getSpecificAds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSpecificAds requires 2 args")
			flag.Usage()
		}
		arg42 := flag.Arg(1)
		mbTrans43 := thrift.NewTMemoryBufferLen(len(arg42))
		defer mbTrans43.Close()
		_, err44 := mbTrans43.WriteString(arg42)
		if err44 != nil {
			Usage()
			return
		}
		factory45 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt46 := factory45.GetProtocol(mbTrans43)
		argvalue0 := sos_adserver.NewRequestHeader()
		err47 := argvalue0.Read(jsProt46)
		if err47 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg48 := flag.Arg(2)
		mbTrans49 := thrift.NewTMemoryBufferLen(len(arg48))
		defer mbTrans49.Close()
		_, err50 := mbTrans49.WriteString(arg48)
		if err50 != nil {
			Usage()
			return
		}
		factory51 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt52 := factory51.GetProtocol(mbTrans49)
		argvalue1 := sos_adserver.NewSosUIProccessedRequest()
		err53 := argvalue1.Read(jsProt52)
		if err53 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetSpecificAds(value0, value1))
		fmt.Print("\n")
		break
	case "getSuperTasks":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSuperTasks requires 2 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := sos_adserver.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg60 := flag.Arg(2)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue1 := sos_adserver.NewSosUIProccessedRequest()
		err65 := argvalue1.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetSuperTasks(value0, value1))
		fmt.Print("\n")
		break
	case "getPoint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPoint requires 2 args")
			flag.Usage()
		}
		arg66 := flag.Arg(1)
		mbTrans67 := thrift.NewTMemoryBufferLen(len(arg66))
		defer mbTrans67.Close()
		_, err68 := mbTrans67.WriteString(arg66)
		if err68 != nil {
			Usage()
			return
		}
		factory69 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt70 := factory69.GetProtocol(mbTrans67)
		argvalue0 := sos_adserver.NewRequestHeader()
		err71 := argvalue0.Read(jsProt70)
		if err71 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg72 := flag.Arg(2)
		mbTrans73 := thrift.NewTMemoryBufferLen(len(arg72))
		defer mbTrans73.Close()
		_, err74 := mbTrans73.WriteString(arg72)
		if err74 != nil {
			Usage()
			return
		}
		factory75 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt76 := factory75.GetProtocol(mbTrans73)
		argvalue1 := sos_adserver.NewSosUIProccessedRequest()
		err77 := argvalue1.Read(jsProt76)
		if err77 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetPoint(value0, value1))
		fmt.Print("\n")
		break
	case "ConsumePoint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ConsumePoint requires 2 args")
			flag.Usage()
		}
		arg78 := flag.Arg(1)
		mbTrans79 := thrift.NewTMemoryBufferLen(len(arg78))
		defer mbTrans79.Close()
		_, err80 := mbTrans79.WriteString(arg78)
		if err80 != nil {
			Usage()
			return
		}
		factory81 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt82 := factory81.GetProtocol(mbTrans79)
		argvalue0 := sos_adserver.NewRequestHeader()
		err83 := argvalue0.Read(jsProt82)
		if err83 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg84 := flag.Arg(2)
		mbTrans85 := thrift.NewTMemoryBufferLen(len(arg84))
		defer mbTrans85.Close()
		_, err86 := mbTrans85.WriteString(arg84)
		if err86 != nil {
			Usage()
			return
		}
		factory87 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt88 := factory87.GetProtocol(mbTrans85)
		argvalue1 := sos_adserver.NewSosUIProccessedRequest()
		err89 := argvalue1.Read(jsProt88)
		if err89 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ConsumePoint(value0, value1))
		fmt.Print("\n")
		break
	case "getDeviceId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetDeviceId requires 4 args")
			flag.Usage()
		}
		arg90 := flag.Arg(1)
		mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
		defer mbTrans91.Close()
		_, err92 := mbTrans91.WriteString(arg90)
		if err92 != nil {
			Usage()
			return
		}
		factory93 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt94 := factory93.GetProtocol(mbTrans91)
		argvalue0 := sos_adserver.NewRequestHeader()
		err95 := argvalue0.Read(jsProt94)
		if err95 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.GetDeviceId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getTasks":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTasks requires 2 args")
			flag.Usage()
		}
		arg99 := flag.Arg(1)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue0 := sos_adserver.NewRequestHeader()
		err104 := argvalue0.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg105 := flag.Arg(2)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue1 := sos_adserver.NewSosUIProccessedRequest()
		err110 := argvalue1.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTasks(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
