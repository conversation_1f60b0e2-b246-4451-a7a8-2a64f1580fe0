// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"p2p_server"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ResultI32List getFileFinishedBlockIndex(RequestHeader requestHeader, TopicInfo topicInfo,  needBlocks)")
	fmt.Fprintln(os.<PERSON>r, "  ResultData getFileData(RequestHeader requestHeader, TopicInfo topicInfo, i32 blockIndex)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := p2p_server.NewP2pServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getFileFinishedBlockIndex":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileFinishedBlockIndex requires 3 args")
			flag.Usage()
		}
		arg11 := flag.Arg(1)
		mbTrans12 := thrift.NewTMemoryBufferLen(len(arg11))
		defer mbTrans12.Close()
		_, err13 := mbTrans12.WriteString(arg11)
		if err13 != nil {
			Usage()
			return
		}
		factory14 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt15 := factory14.GetProtocol(mbTrans12)
		argvalue0 := p2p_server.NewRequestHeader()
		err16 := argvalue0.Read(jsProt15)
		if err16 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg17 := flag.Arg(2)
		mbTrans18 := thrift.NewTMemoryBufferLen(len(arg17))
		defer mbTrans18.Close()
		_, err19 := mbTrans18.WriteString(arg17)
		if err19 != nil {
			Usage()
			return
		}
		factory20 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt21 := factory20.GetProtocol(mbTrans18)
		argvalue1 := p2p_server.NewTopicInfo()
		err22 := argvalue1.Read(jsProt21)
		if err22 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg23 := flag.Arg(3)
		mbTrans24 := thrift.NewTMemoryBufferLen(len(arg23))
		defer mbTrans24.Close()
		_, err25 := mbTrans24.WriteString(arg23)
		if err25 != nil {
			Usage()
			return
		}
		factory26 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt27 := factory26.GetProtocol(mbTrans24)
		containerStruct2 := p2p_server.NewGetFileFinishedBlockIndexArgs()
		err28 := containerStruct2.ReadField3(jsProt27)
		if err28 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.NeedBlocks
		value2 := argvalue2
		fmt.Print(client.GetFileFinishedBlockIndex(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFileData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileData requires 3 args")
			flag.Usage()
		}
		arg29 := flag.Arg(1)
		mbTrans30 := thrift.NewTMemoryBufferLen(len(arg29))
		defer mbTrans30.Close()
		_, err31 := mbTrans30.WriteString(arg29)
		if err31 != nil {
			Usage()
			return
		}
		factory32 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt33 := factory32.GetProtocol(mbTrans30)
		argvalue0 := p2p_server.NewRequestHeader()
		err34 := argvalue0.Read(jsProt33)
		if err34 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg35 := flag.Arg(2)
		mbTrans36 := thrift.NewTMemoryBufferLen(len(arg35))
		defer mbTrans36.Close()
		_, err37 := mbTrans36.WriteString(arg35)
		if err37 != nil {
			Usage()
			return
		}
		factory38 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt39 := factory38.GetProtocol(mbTrans36)
		argvalue1 := p2p_server.NewTopicInfo()
		err40 := argvalue1.Read(jsProt39)
		if err40 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err41 := (strconv.Atoi(flag.Arg(3)))
		if err41 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetFileData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
