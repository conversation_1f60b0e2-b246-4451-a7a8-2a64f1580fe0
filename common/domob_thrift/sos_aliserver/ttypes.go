// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_aliserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//标记不同的产品线，这里汇聚不同产品线的充值数据，所以需要保存订单所在的产品线以及订单在产品线里
//的唯一标记id。不同产品线的标记id可能是相同的。unique key为product_sporder_id
//
//最后发送给支付宝平台的订单流水号是由AliServer维护的兑换订单的id。
type Product int64

const (
	Product_SOS    Product = 1
	Product_WECHAT Product = 2
)

func (p Product) String() string {
	switch p {
	case Product_SOS:
		return "Product_SOS"
	case Product_WECHAT:
		return "Product_WECHAT"
	}
	return "<UNSET>"
}

func ProductFromString(s string) (Product, error) {
	switch s {
	case "Product_SOS":
		return Product_SOS, nil
	case "Product_WECHAT":
		return Product_WECHAT, nil
	}
	return Product(math.MinInt32 - 1), fmt.Errorf("not a valid Product string")
}

//体验达人
type BillStatus int64

const (
	BillStatus_SUCCESS   BillStatus = 0
	BillStatus_FAILED    BillStatus = 1
	BillStatus_CHARGEING BillStatus = 2
	BillStatus_NOT_EXIST BillStatus = 3
)

func (p BillStatus) String() string {
	switch p {
	case BillStatus_SUCCESS:
		return "BillStatus_SUCCESS"
	case BillStatus_FAILED:
		return "BillStatus_FAILED"
	case BillStatus_CHARGEING:
		return "BillStatus_CHARGEING"
	case BillStatus_NOT_EXIST:
		return "BillStatus_NOT_EXIST"
	}
	return "<UNSET>"
}

func BillStatusFromString(s string) (BillStatus, error) {
	switch s {
	case "BillStatus_SUCCESS":
		return BillStatus_SUCCESS, nil
	case "BillStatus_FAILED":
		return BillStatus_FAILED, nil
	case "BillStatus_CHARGEING":
		return BillStatus_CHARGEING, nil
	case "BillStatus_NOT_EXIST":
		return BillStatus_NOT_EXIST, nil
	}
	return BillStatus(math.MinInt32 - 1), fmt.Errorf("not a valid BillStatus string")
}

type AliChargeNotify struct {
	OriginalId int64   `thrift:"original_id,1" json:"original_id"`
	Account    string  `thrift:"account,2" json:"account"`
	Name       string  `thrift:"name,3" json:"name"`
	Amount     int32   `thrift:"amount,4" json:"amount"`
	Note       string  `thrift:"note,5" json:"note"`
	Product    Product `thrift:"product,6" json:"product"`
}

func NewAliChargeNotify() *AliChargeNotify {
	return &AliChargeNotify{
		Product: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AliChargeNotify) IsSetProduct() bool {
	return int64(p.Product) != math.MinInt32-1
}

func (p *AliChargeNotify) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AliChargeNotify) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OriginalId = v
	}
	return nil
}

func (p *AliChargeNotify) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *AliChargeNotify) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AliChargeNotify) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *AliChargeNotify) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AliChargeNotify) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Product = Product(v)
	}
	return nil
}

func (p *AliChargeNotify) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AliChargeNotify"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AliChargeNotify) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("original_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:original_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OriginalId)); err != nil {
		return fmt.Errorf("%T.original_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:original_id: %s", p, err)
	}
	return err
}

func (p *AliChargeNotify) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:account: %s", p, err)
	}
	return err
}

func (p *AliChargeNotify) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *AliChargeNotify) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:amount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:amount: %s", p, err)
	}
	return err
}

func (p *AliChargeNotify) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:note: %s", p, err)
	}
	return err
}

func (p *AliChargeNotify) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProduct() {
		if err := oprot.WriteFieldBegin("product", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:product: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Product)); err != nil {
			return fmt.Errorf("%T.product (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:product: %s", p, err)
		}
	}
	return err
}

func (p *AliChargeNotify) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AliChargeNotify(%+v)", *p)
}

type BillInfo struct {
	Status BillStatus `thrift:"status,1" json:"status"`
	Msg    string     `thrift:"msg,2" json:"msg"`
}

func NewBillInfo() *BillInfo {
	return &BillInfo{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BillInfo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *BillInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BillInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = BillStatus(v)
	}
	return nil
}

func (p *BillInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *BillInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BillInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BillInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *BillInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *BillInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BillInfo(%+v)", *p)
}

type FileImportProcessRes struct {
	Ok  bool   `thrift:"ok,1" json:"ok"`
	Msg string `thrift:"msg,2" json:"msg"`
}

func NewFileImportProcessRes() *FileImportProcessRes {
	return &FileImportProcessRes{}
}

func (p *FileImportProcessRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FileImportProcessRes) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Ok = v
	}
	return nil
}

func (p *FileImportProcessRes) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *FileImportProcessRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FileImportProcessRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FileImportProcessRes) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ok", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ok: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ok)); err != nil {
		return fmt.Errorf("%T.ok (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ok: %s", p, err)
	}
	return err
}

func (p *FileImportProcessRes) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *FileImportProcessRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileImportProcessRes(%+v)", *p)
}
