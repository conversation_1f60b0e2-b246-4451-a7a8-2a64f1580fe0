// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"sos_aliserver"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  bool charge_notify(RequestHeader header, AliChargeNotify notify)")
	fmt.Fprintln(os.Stderr, "  BillInfo bill_status_query(RequestHeader header, Product product, i64 sporder_id)")
	fmt.Fprintln(os.Stderr, "  FileImportProcessRes file_import_process(RequestHeader header, string hostname, string remote_file_path, string filename)")
	fmt.Fprintln(os.Stderr, "  FileImportProcessRes information_supply(RequestHeader header, string hostname, string remote_file_path, string filename)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := sos_aliserver.NewAliServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "charge_notify":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ChargeNotify requires 2 args")
			flag.Usage()
		}
		arg18 := flag.Arg(1)
		mbTrans19 := thrift.NewTMemoryBufferLen(len(arg18))
		defer mbTrans19.Close()
		_, err20 := mbTrans19.WriteString(arg18)
		if err20 != nil {
			Usage()
			return
		}
		factory21 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt22 := factory21.GetProtocol(mbTrans19)
		argvalue0 := sos_aliserver.NewRequestHeader()
		err23 := argvalue0.Read(jsProt22)
		if err23 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg24 := flag.Arg(2)
		mbTrans25 := thrift.NewTMemoryBufferLen(len(arg24))
		defer mbTrans25.Close()
		_, err26 := mbTrans25.WriteString(arg24)
		if err26 != nil {
			Usage()
			return
		}
		factory27 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt28 := factory27.GetProtocol(mbTrans25)
		argvalue1 := sos_aliserver.NewAliChargeNotify()
		err29 := argvalue1.Read(jsProt28)
		if err29 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ChargeNotify(value0, value1))
		fmt.Print("\n")
		break
	case "bill_status_query":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "BillStatusQuery requires 3 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := sos_aliserver.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := sos_aliserver.Product(tmp1)
		value1 := argvalue1
		argvalue2, err36 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err36 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.BillStatusQuery(value0, value1, value2))
		fmt.Print("\n")
		break
	case "file_import_process":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "FileImportProcess requires 4 args")
			flag.Usage()
		}
		arg37 := flag.Arg(1)
		mbTrans38 := thrift.NewTMemoryBufferLen(len(arg37))
		defer mbTrans38.Close()
		_, err39 := mbTrans38.WriteString(arg37)
		if err39 != nil {
			Usage()
			return
		}
		factory40 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt41 := factory40.GetProtocol(mbTrans38)
		argvalue0 := sos_aliserver.NewRequestHeader()
		err42 := argvalue0.Read(jsProt41)
		if err42 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.FileImportProcess(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "information_supply":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "InformationSupply requires 4 args")
			flag.Usage()
		}
		arg46 := flag.Arg(1)
		mbTrans47 := thrift.NewTMemoryBufferLen(len(arg46))
		defer mbTrans47.Close()
		_, err48 := mbTrans47.WriteString(arg46)
		if err48 != nil {
			Usage()
			return
		}
		factory49 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt50 := factory49.GetProtocol(mbTrans47)
		argvalue0 := sos_aliserver.NewRequestHeader()
		err51 := argvalue0.Read(jsProt50)
		if err51 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.InformationSupply(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
