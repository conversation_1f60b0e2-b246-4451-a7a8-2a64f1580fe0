// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_fancytrendy_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/vico_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = vico_types.GoUnusedProtection__
var GoUnusedProtection__ int

type FTPostType int64

const (
	FTPostType_FTPT_UNKNOWN    FTPostType = 0
	FTPostType_FTPT_PRODUCT    FTPostType = 1
	FTPostType_FTPT_COLLECTION FTPostType = 2
	FTPostType_FTPT_LANDING    FTPostType = 3
)

func (p FTPostType) String() string {
	switch p {
	case FTPostType_FTPT_UNKNOWN:
		return "FTPostType_FTPT_UNKNOWN"
	case FTPostType_FTPT_PRODUCT:
		return "FTPostType_FTPT_PRODUCT"
	case FTPostType_FTPT_COLLECTION:
		return "FTPostType_FTPT_COLLECTION"
	case FTPostType_FTPT_LANDING:
		return "FTPostType_FTPT_LANDING"
	}
	return "<UNSET>"
}

func FTPostTypeFromString(s string) (FTPostType, error) {
	switch s {
	case "FTPostType_FTPT_UNKNOWN":
		return FTPostType_FTPT_UNKNOWN, nil
	case "FTPostType_FTPT_PRODUCT":
		return FTPostType_FTPT_PRODUCT, nil
	case "FTPostType_FTPT_COLLECTION":
		return FTPostType_FTPT_COLLECTION, nil
	case "FTPostType_FTPT_LANDING":
		return FTPostType_FTPT_LANDING, nil
	}
	return FTPostType(math.MinInt32 - 1), fmt.Errorf("not a valid FTPostType string")
}

type UgcInfo struct {
	Id       int64  `thrift:"id,1" json:"id"`
	UgcId    string `thrift:"ugc_id,2" json:"ugc_id"`
	Username string `thrift:"username,3" json:"username"`
	IconUrl  string `thrift:"iconUrl,4" json:"iconUrl"`
	ImgUrl   string `thrift:"imgUrl,5" json:"imgUrl"`
	Content  string `thrift:"content,6" json:"content"`
}

func NewUgcInfo() *UgcInfo {
	return &UgcInfo{}
}

func (p *UgcInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UgcInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *UgcInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *UgcInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *UgcInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IconUrl = v
	}
	return nil
}

func (p *UgcInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *UgcInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *UgcInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UgcInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UgcInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *UgcInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_id", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ugc_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugc_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ugc_id: %s", p, err)
	}
	return err
}

func (p *UgcInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:username: %s", p, err)
	}
	return err
}

func (p *UgcInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iconUrl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:iconUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IconUrl)); err != nil {
		return fmt.Errorf("%T.iconUrl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:iconUrl: %s", p, err)
	}
	return err
}

func (p *UgcInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:imgUrl: %s", p, err)
	}
	return err
}

func (p *UgcInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:content: %s", p, err)
	}
	return err
}

func (p *UgcInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UgcInfo(%+v)", *p)
}

type PublisherInfo struct {
	Id             int64  `thrift:"id,1" json:"id"`
	Logo           string `thrift:"logo,2" json:"logo"`
	NickName       string `thrift:"nickName,3" json:"nickName"`
	FollowingCount int64  `thrift:"followingCount,4" json:"followingCount"`
	UseShopifyBtn  bool   `thrift:"useShopifyBtn,5" json:"useShopifyBtn"`
	ShopifyApiKey  string `thrift:"shopifyApiKey,6" json:"shopifyApiKey"`
	ShopifyToken   string `thrift:"shopifyToken,7" json:"shopifyToken"`
}

func NewPublisherInfo() *PublisherInfo {
	return &PublisherInfo{}
}

func (p *PublisherInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PublisherInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PublisherInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *PublisherInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NickName = v
	}
	return nil
}

func (p *PublisherInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FollowingCount = v
	}
	return nil
}

func (p *PublisherInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UseShopifyBtn = v
	}
	return nil
}

func (p *PublisherInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ShopifyApiKey = v
	}
	return nil
}

func (p *PublisherInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ShopifyToken = v
	}
	return nil
}

func (p *PublisherInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PublisherInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PublisherInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:logo: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:nickName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NickName)); err != nil {
		return fmt.Errorf("%T.nickName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:nickName: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("followingCount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:followingCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FollowingCount)); err != nil {
		return fmt.Errorf("%T.followingCount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:followingCount: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useShopifyBtn", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:useShopifyBtn: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UseShopifyBtn)); err != nil {
		return fmt.Errorf("%T.useShopifyBtn (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:useShopifyBtn: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shopifyApiKey", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:shopifyApiKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShopifyApiKey)); err != nil {
		return fmt.Errorf("%T.shopifyApiKey (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:shopifyApiKey: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shopifyToken", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:shopifyToken: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShopifyToken)); err != nil {
		return fmt.Errorf("%T.shopifyToken (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:shopifyToken: %s", p, err)
	}
	return err
}

func (p *PublisherInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublisherInfo(%+v)", *p)
}

type ProductInfo struct {
	Id            int64                      `thrift:"id,1" json:"id"`
	Platform      vico_types.MallPlatform    `thrift:"platform,2" json:"platform"`
	Name          string                     `thrift:"name,3" json:"name"`
	Url           string                     `thrift:"url,4" json:"url"`
	Text          string                     `thrift:"text,5" json:"text"`
	ImgUrls       []string                   `thrift:"imgUrls,6" json:"imgUrls"`
	ImgUrl        string                     `thrift:"imgUrl,7" json:"imgUrl"`
	StoreId       string                     `thrift:"storeId,8" json:"storeId"`
	StoreName     string                     `thrift:"storeName,9" json:"storeName"`
	Title         string                     `thrift:"title,10" json:"title"`
	Detail        string                     `thrift:"detail,11" json:"detail"`
	Ugc           *UgcInfo                   `thrift:"ugc,12" json:"ugc"`
	UgcText       string                     `thrift:"ugcText,13" json:"ugcText"`
	Comments      []*vico_types.VicoComment  `thrift:"comments,14" json:"comments"`
	AccountId     int64                      `thrift:"AccountId,15" json:"AccountId"`
	Price         int64                      `thrift:"price,16" json:"price"`
	OriginalPrice int64                      `thrift:"originalPrice,17" json:"originalPrice"`
	UnitSymbol    string                     `thrift:"unitSymbol,18" json:"unitSymbol"`
	Category      vico_types.ProductCategory `thrift:"category,19" json:"category"`
	ShortUrl      string                     `thrift:"shortUrl,20" json:"shortUrl"`
	OutProductId  string                     `thrift:"outProductId,21" json:"outProductId"`
}

func NewProductInfo() *ProductInfo {
	return &ProductInfo{
		Platform: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ProductInfo) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *ProductInfo) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ProductInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProductInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ProductInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = vico_types.MallPlatform(v)
	}
	return nil
}

func (p *ProductInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ProductInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *ProductInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *ProductInfo) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImgUrls = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ImgUrls = append(p.ImgUrls, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProductInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *ProductInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StoreId = v
	}
	return nil
}

func (p *ProductInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.StoreName = v
	}
	return nil
}

func (p *ProductInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *ProductInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *ProductInfo) readField12(iprot thrift.TProtocol) error {
	p.Ugc = NewUgcInfo()
	if err := p.Ugc.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ugc)
	}
	return nil
}

func (p *ProductInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.UgcText = v
	}
	return nil
}

func (p *ProductInfo) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Comments = make([]*vico_types.VicoComment, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := vico_types.NewVicoComment()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Comments = append(p.Comments, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProductInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ProductInfo) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *ProductInfo) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.OriginalPrice = v
	}
	return nil
}

func (p *ProductInfo) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.UnitSymbol = v
	}
	return nil
}

func (p *ProductInfo) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Category = vico_types.ProductCategory(v)
	}
	return nil
}

func (p *ProductInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ShortUrl = v
	}
	return nil
}

func (p *ProductInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.OutProductId = v
	}
	return nil
}

func (p *ProductInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProductInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProductInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *ProductInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:url: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:text: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.ImgUrls != nil {
		if err := oprot.WriteFieldBegin("imgUrls", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:imgUrls: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ImgUrls)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImgUrls {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:imgUrls: %s", p, err)
		}
	}
	return err
}

func (p *ProductInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:imgUrl: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("storeId", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:storeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StoreId)); err != nil {
		return fmt.Errorf("%T.storeId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:storeId: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("storeName", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:storeName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StoreName)); err != nil {
		return fmt.Errorf("%T.storeName (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:storeName: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:title: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:detail: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Ugc != nil {
		if err := oprot.WriteFieldBegin("ugc", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:ugc: %s", p, err)
		}
		if err := p.Ugc.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ugc)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:ugc: %s", p, err)
		}
	}
	return err
}

func (p *ProductInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcText", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ugcText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcText)); err != nil {
		return fmt.Errorf("%T.ugcText (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ugcText: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Comments != nil {
		if err := oprot.WriteFieldBegin("comments", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:comments: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Comments)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Comments {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:comments: %s", p, err)
		}
	}
	return err
}

func (p *ProductInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("AccountId", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:AccountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.AccountId (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:AccountId: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:price: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("originalPrice", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:originalPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OriginalPrice)); err != nil {
		return fmt.Errorf("%T.originalPrice (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:originalPrice: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unitSymbol", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:unitSymbol: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UnitSymbol)); err != nil {
		return fmt.Errorf("%T.unitSymbol (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:unitSymbol: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 19); err != nil {
			return fmt.Errorf("%T write field begin error 19:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (19) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 19:category: %s", p, err)
		}
	}
	return err
}

func (p *ProductInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shortUrl", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:shortUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShortUrl)); err != nil {
		return fmt.Errorf("%T.shortUrl (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:shortUrl: %s", p, err)
	}
	return err
}

func (p *ProductInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("outProductId", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:outProductId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OutProductId)); err != nil {
		return fmt.Errorf("%T.outProductId (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:outProductId: %s", p, err)
	}
	return err
}

func (p *ProductInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductInfo(%+v)", *p)
}

type CollectionInfo struct {
	Id            int64                      `thrift:"id,1" json:"id"`
	Name          string                     `thrift:"name,2" json:"name"`
	Category      vico_types.ProductCategory `thrift:"category,3" json:"category"`
	ImgUrl        string                     `thrift:"imgUrl,4" json:"imgUrl"`
	Title         string                     `thrift:"title,5" json:"title"`
	Body          string                     `thrift:"body,6" json:"body"`
	Products      []*ProductInfo             `thrift:"products,7" json:"products"`
	AccountId     int64                      `thrift:"AccountId,8" json:"AccountId"`
	PublishStatus vico_types.PublishStatus   `thrift:"publishStatus,9" json:"publishStatus"`
	IsTop         int16                      `thrift:"isTop,10" json:"isTop"`
	CreateTime    int64                      `thrift:"createTime,11" json:"createTime"`
	LastUpdate    int64                      `thrift:"lastUpdate,12" json:"lastUpdate"`
}

func NewCollectionInfo() *CollectionInfo {
	return &CollectionInfo{
		Category: math.MinInt32 - 1, // unset sentinal value

		PublishStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CollectionInfo) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *CollectionInfo) IsSetPublishStatus() bool {
	return int64(p.PublishStatus) != math.MinInt32-1
}

func (p *CollectionInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I16 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CollectionInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *CollectionInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CollectionInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Category = vico_types.ProductCategory(v)
	}
	return nil
}

func (p *CollectionInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *CollectionInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *CollectionInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Body = v
	}
	return nil
}

func (p *CollectionInfo) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Products = make([]*ProductInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewProductInfo()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Products = append(p.Products, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CollectionInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *CollectionInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PublishStatus = vico_types.PublishStatus(v)
	}
	return nil
}

func (p *CollectionInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.IsTop = v
	}
	return nil
}

func (p *CollectionInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *CollectionInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *CollectionInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CollectionInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CollectionInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:category: %s", p, err)
		}
	}
	return err
}

func (p *CollectionInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imgUrl: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:title: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("body", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:body: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Body)); err != nil {
		return fmt.Errorf("%T.body (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:body: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Products != nil {
		if err := oprot.WriteFieldBegin("products", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:products: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Products)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Products {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:products: %s", p, err)
		}
	}
	return err
}

func (p *CollectionInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("AccountId", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:AccountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.AccountId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:AccountId: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishStatus() {
		if err := oprot.WriteFieldBegin("publishStatus", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:publishStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PublishStatus)); err != nil {
			return fmt.Errorf("%T.publishStatus (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:publishStatus: %s", p, err)
		}
	}
	return err
}

func (p *CollectionInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isTop", thrift.I16, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:isTop: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.IsTop)); err != nil {
		return fmt.Errorf("%T.isTop (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:isTop: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:createTime: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:lastUpdate: %s", p, err)
	}
	return err
}

func (p *CollectionInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CollectionInfo(%+v)", *p)
}

type FtLandingInfo struct {
	Id            int64                      `thrift:"id,1" json:"id"`
	PromotionType vico_types.AdPromotionType `thrift:"promotionType,2" json:"promotionType"`
	Channel       int16                      `thrift:"channel,3" json:"channel"`
	Collection    *CollectionInfo            `thrift:"collection,4" json:"collection"`
	Product       *ProductInfo               `thrift:"product,5" json:"product"`
	OrderId       int64                      `thrift:"orderId,6" json:"orderId"`
	Name          string                     `thrift:"name,7" json:"name"`
	ImgUrl        string                     `thrift:"imgUrl,8" json:"imgUrl"`
	UgcText       string                     `thrift:"ugcText,9" json:"ugcText"`
	Ugc           *UgcInfo                   `thrift:"ugc,10" json:"ugc"`
	Comments      []*vico_types.VicoComment  `thrift:"comments,11" json:"comments"`
	TemplateId    int64                      `thrift:"templateId,12" json:"templateId"`
	AccountId     int64                      `thrift:"accountId,13" json:"accountId"`
}

func NewFtLandingInfo() *FtLandingInfo {
	return &FtLandingInfo{
		PromotionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FtLandingInfo) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *FtLandingInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FtLandingInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FtLandingInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PromotionType = vico_types.AdPromotionType(v)
	}
	return nil
}

func (p *FtLandingInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *FtLandingInfo) readField4(iprot thrift.TProtocol) error {
	p.Collection = NewCollectionInfo()
	if err := p.Collection.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Collection)
	}
	return nil
}

func (p *FtLandingInfo) readField5(iprot thrift.TProtocol) error {
	p.Product = NewProductInfo()
	if err := p.Product.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Product)
	}
	return nil
}

func (p *FtLandingInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *FtLandingInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FtLandingInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *FtLandingInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UgcText = v
	}
	return nil
}

func (p *FtLandingInfo) readField10(iprot thrift.TProtocol) error {
	p.Ugc = NewUgcInfo()
	if err := p.Ugc.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ugc)
	}
	return nil
}

func (p *FtLandingInfo) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Comments = make([]*vico_types.VicoComment, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := vico_types.NewVicoComment()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Comments = append(p.Comments, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *FtLandingInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *FtLandingInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *FtLandingInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FtLandingInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FtLandingInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channel: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channel: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Collection != nil {
		if err := oprot.WriteFieldBegin("collection", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:collection: %s", p, err)
		}
		if err := p.Collection.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Collection)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:collection: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Product != nil {
		if err := oprot.WriteFieldBegin("product", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:product: %s", p, err)
		}
		if err := p.Product.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Product)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:product: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:orderId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:orderId: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:name: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:imgUrl: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcText", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ugcText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcText)); err != nil {
		return fmt.Errorf("%T.ugcText (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ugcText: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Ugc != nil {
		if err := oprot.WriteFieldBegin("ugc", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:ugc: %s", p, err)
		}
		if err := p.Ugc.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ugc)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:ugc: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Comments != nil {
		if err := oprot.WriteFieldBegin("comments", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:comments: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Comments)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Comments {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:comments: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateId", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:templateId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.templateId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:templateId: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:accountId: %s", p, err)
	}
	return err
}

func (p *FtLandingInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FtLandingInfo(%+v)", *p)
}

type PostInfo struct {
	Id          int64           `thrift:"id,1" json:"id"`
	TypeA1      FTPostType      `thrift:"type,2" json:"type"`
	Product     *ProductInfo    `thrift:"product,3" json:"product"`
	Collection  *CollectionInfo `thrift:"collection,4" json:"collection"`
	Landing     *FtLandingInfo  `thrift:"landing,5" json:"landing"`
	Publisher   *PublisherInfo  `thrift:"publisher,6" json:"publisher"`
	PublishTime int64           `thrift:"publishTime,7" json:"publishTime"`
	Likes       int64           `thrift:"likes,8" json:"likes"`
	Liked       bool            `thrift:"liked,9" json:"liked"`
}

func NewPostInfo() *PostInfo {
	return &PostInfo{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PostInfo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PostInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PostInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PostInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = FTPostType(v)
	}
	return nil
}

func (p *PostInfo) readField3(iprot thrift.TProtocol) error {
	p.Product = NewProductInfo()
	if err := p.Product.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Product)
	}
	return nil
}

func (p *PostInfo) readField4(iprot thrift.TProtocol) error {
	p.Collection = NewCollectionInfo()
	if err := p.Collection.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Collection)
	}
	return nil
}

func (p *PostInfo) readField5(iprot thrift.TProtocol) error {
	p.Landing = NewFtLandingInfo()
	if err := p.Landing.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Landing)
	}
	return nil
}

func (p *PostInfo) readField6(iprot thrift.TProtocol) error {
	p.Publisher = NewPublisherInfo()
	if err := p.Publisher.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Publisher)
	}
	return nil
}

func (p *PostInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PublishTime = v
	}
	return nil
}

func (p *PostInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Likes = v
	}
	return nil
}

func (p *PostInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Liked = v
	}
	return nil
}

func (p *PostInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PostInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PostInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PostInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *PostInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Product != nil {
		if err := oprot.WriteFieldBegin("product", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:product: %s", p, err)
		}
		if err := p.Product.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Product)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:product: %s", p, err)
		}
	}
	return err
}

func (p *PostInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Collection != nil {
		if err := oprot.WriteFieldBegin("collection", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:collection: %s", p, err)
		}
		if err := p.Collection.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Collection)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:collection: %s", p, err)
		}
	}
	return err
}

func (p *PostInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Landing != nil {
		if err := oprot.WriteFieldBegin("landing", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:landing: %s", p, err)
		}
		if err := p.Landing.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Landing)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:landing: %s", p, err)
		}
	}
	return err
}

func (p *PostInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Publisher != nil {
		if err := oprot.WriteFieldBegin("publisher", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:publisher: %s", p, err)
		}
		if err := p.Publisher.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Publisher)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:publisher: %s", p, err)
		}
	}
	return err
}

func (p *PostInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publishTime", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:publishTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PublishTime)); err != nil {
		return fmt.Errorf("%T.publishTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:publishTime: %s", p, err)
	}
	return err
}

func (p *PostInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("likes", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:likes: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Likes)); err != nil {
		return fmt.Errorf("%T.likes (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:likes: %s", p, err)
	}
	return err
}

func (p *PostInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("liked", thrift.BOOL, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:liked: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Liked)); err != nil {
		return fmt.Errorf("%T.liked (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:liked: %s", p, err)
	}
	return err
}

func (p *PostInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PostInfo(%+v)", *p)
}
