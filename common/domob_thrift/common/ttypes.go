// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package common

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//DOMOB最基础的接口定义
type IdInt int32

type UidInt int32

type LargeIdInt int64

type QueryInt int32

type IpInt int64

type TimeInt int64

type ErrorCode int32

type Amount int64

type PercentageInt int32

//重声明 types 内的数据结构，以便其他模块直接从 common 引用
type UserRole enums.UserRole

type GenderCode enums.GenderCode

type AgeCode enums.AgeCode

type RegionCode enums.RegionCode

type CarrierCode enums.CarrierCode

type DeviceCode int32

type OSCode int32

type BrowserCode int32

type DeviceGroupIdInt int32

type SDKTypeCode enums.SDKTypeCode

type LanguageType enums.LanguageType

type ADResponseType enums.ADResponseType

type EncodingCode enums.EncodingCode

type AccessTypeCode enums.AccessTypeCode

type CostType enums.CostType

type AdCategory enums.AdCategory

type AdActionType enums.AdActionType

type AdStrategyType enums.AdStrategyType

type AdCreativeType enums.AdCreativeType

type AdCreativeIconType enums.AdCreativeIconType

type TemplateSizeCode enums.TemplateSizeCode

type TemplateSizeCodeInt int32

type MediaType enums.MediaType

type MediaCategory enums.MediaCategory

type MediaTestModeSetting enums.MediaTestModeSetting

type AccountStatus enums.AccountStatus

type AccountCategory enums.AccountCategory

type PaymentType enums.PaymentType

type TrafficType enums.TrafficType

type MediaTargetType enums.MediaTargetType

type SDKUrlOpenType enums.SDKUrlOpenType

type SDKProtocolVersion enums.SDKProtocolVersion

type ImageType enums.ImageType

type SDKPlatform enums.SDKPlatform

type SDKVersion enums.SDKVersion

type CapabilityType enums.CapabilityType

type IdentityType enums.IdentityType

type CommonAuditStatus enums.CommonAuditStatus

type AdRenderType enums.AdRenderType

type JailBreakCode enums.JailBreakCode

type LandingDirection enums.LandingDirection

type AdPlacementType enums.AdPlacementType

type WaterMarkPosition enums.WaterMarkPosition

type CloseButtonPosition enums.CloseButtonPosition

type SponsorType enums.SponsorType

type CooperateCompany enums.CooperateCompany

type BudgetType enums.BudgetType

type FreqCycleType enums.FreqCycleType

type ResourceType enums.ResourceType

type StatusWhetherAvailable enums.StatusWhetherAvailable

type PauseStatus enums.PauseStatus

type ContainerFlags enums.ContainerFlags

type AdCreativeTemplateType enums.AdCreativeTemplateType

type QueryResult struct {
	Total    QueryInt `thrift:"total,1" json:"total"`
	Offset   QueryInt `thrift:"offset,2" json:"offset"`
	Maxlimit QueryInt `thrift:"maxlimit,3" json:"maxlimit"`
	Ids      []IdInt  `thrift:"ids,4" json:"ids"`
}

func NewQueryResult() *QueryResult {
	return &QueryResult{}
}

func (p *QueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Total = QueryInt(v)
	}
	return nil
}

func (p *QueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Offset = QueryInt(v)
	}
	return nil
}

func (p *QueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Maxlimit = QueryInt(v)
	}
	return nil
}

func (p *QueryResult) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = IdInt(v)
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total: %s", p, err)
	}
	return err
}

func (p *QueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:offset: %s", p, err)
	}
	return err
}

func (p *QueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxlimit", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:maxlimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Maxlimit)); err != nil {
		return fmt.Errorf("%T.maxlimit (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:maxlimit: %s", p, err)
	}
	return err
}

func (p *QueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *QueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryResult(%+v)", *p)
}

type RequestHeader struct {
	Requester   string     `thrift:"requester,1" json:"requester"`
	Trackid     LargeIdInt `thrift:"trackid,2" json:"trackid"`
	OperatorUid IdInt      `thrift:"operatorUid,3" json:"operatorUid"`
}

func NewRequestHeader() *RequestHeader {
	return &RequestHeader{}
}

func (p *RequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Requester = v
	}
	return nil
}

func (p *RequestHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Trackid = LargeIdInt(v)
	}
	return nil
}

func (p *RequestHeader) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OperatorUid = IdInt(v)
	}
	return nil
}

func (p *RequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requester", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:requester: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Requester)); err != nil {
		return fmt.Errorf("%T.requester (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:requester: %s", p, err)
	}
	return err
}

func (p *RequestHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:trackid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Trackid)); err != nil {
		return fmt.Errorf("%T.trackid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:trackid: %s", p, err)
	}
	return err
}

func (p *RequestHeader) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("operatorUid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:operatorUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OperatorUid)); err != nil {
		return fmt.Errorf("%T.operatorUid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:operatorUid: %s", p, err)
	}
	return err
}

func (p *RequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RequestHeader(%+v)", *p)
}

type FreqInfo struct {
	FreqId    int32         `thrift:"freqId,1" json:"freqId"`
	Frequency int32         `thrift:"frequency,2" json:"frequency"`
	Cycle     FreqCycleType `thrift:"cycle,3" json:"cycle"`
}

func NewFreqInfo() *FreqInfo {
	return &FreqInfo{
		Cycle: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FreqInfo) IsSetCycle() bool {
	return int64(p.Cycle) != math.MinInt32-1
}

func (p *FreqInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FreqInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FreqId = v
	}
	return nil
}

func (p *FreqInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Frequency = v
	}
	return nil
}

func (p *FreqInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cycle = FreqCycleType(v)
	}
	return nil
}

func (p *FreqInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FreqInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FreqInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("freqId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:freqId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FreqId)); err != nil {
		return fmt.Errorf("%T.freqId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:freqId: %s", p, err)
	}
	return err
}

func (p *FreqInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("frequency", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:frequency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Frequency)); err != nil {
		return fmt.Errorf("%T.frequency (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:frequency: %s", p, err)
	}
	return err
}

func (p *FreqInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cycle", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cycle: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cycle)); err != nil {
		return fmt.Errorf("%T.cycle (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cycle: %s", p, err)
	}
	return err
}

func (p *FreqInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FreqInfo(%+v)", *p)
}

type Resource struct {
	Rcid      int32        `thrift:"rcid,1" json:"rcid"`
	Url       string       `thrift:"url,2" json:"url"`
	Rctype    ResourceType `thrift:"rctype,3" json:"rctype"`
	Rgid      int32        `thrift:"rgid,4" json:"rgid"`
	PixelSize int32        `thrift:"pixelSize,5" json:"pixelSize"`
}

func NewResource() *Resource {
	return &Resource{
		Rctype: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Resource) IsSetRctype() bool {
	return int64(p.Rctype) != math.MinInt32-1
}

func (p *Resource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Resource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Rcid = v
	}
	return nil
}

func (p *Resource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Resource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rctype = ResourceType(v)
	}
	return nil
}

func (p *Resource) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Rgid = v
	}
	return nil
}

func (p *Resource) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PixelSize = v
	}
	return nil
}

func (p *Resource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Resource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Resource) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rcid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rcid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rcid)); err != nil {
		return fmt.Errorf("%T.rcid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rcid: %s", p, err)
	}
	return err
}

func (p *Resource) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:url: %s", p, err)
	}
	return err
}

func (p *Resource) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rctype", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rctype: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rctype)); err != nil {
		return fmt.Errorf("%T.rctype (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rctype: %s", p, err)
	}
	return err
}

func (p *Resource) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:rgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rgid)); err != nil {
		return fmt.Errorf("%T.rgid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:rgid: %s", p, err)
	}
	return err
}

func (p *Resource) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pixelSize", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pixelSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PixelSize)); err != nil {
		return fmt.Errorf("%T.pixelSize (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pixelSize: %s", p, err)
	}
	return err
}

func (p *Resource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Resource(%+v)", *p)
}

type ResourceGroup struct {
	Rgid         int32       `thrift:"rgid,1" json:"rgid"`
	ResourceList []*Resource `thrift:"resourceList,2" json:"resourceList"`
	RgName       string      `thrift:"rgName,3" json:"rgName"`
	ExpireTime   TimeInt     `thrift:"expireTime,4" json:"expireTime"`
}

func NewResourceGroup() *ResourceGroup {
	return &ResourceGroup{}
}

func (p *ResourceGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResourceGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Rgid = v
	}
	return nil
}

func (p *ResourceGroup) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResourceList = make([]*Resource, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewResource()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ResourceList = append(p.ResourceList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResourceGroup) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RgName = v
	}
	return nil
}

func (p *ResourceGroup) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExpireTime = TimeInt(v)
	}
	return nil
}

func (p *ResourceGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResourceGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResourceGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rgid)); err != nil {
		return fmt.Errorf("%T.rgid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rgid: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ResourceList != nil {
		if err := oprot.WriteFieldBegin("resourceList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:resourceList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResourceList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:resourceList: %s", p, err)
		}
	}
	return err
}

func (p *ResourceGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RgName)); err != nil {
		return fmt.Errorf("%T.rgName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rgName: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expireTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:expireTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExpireTime)); err != nil {
		return fmt.Errorf("%T.expireTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:expireTime: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceGroup(%+v)", *p)
}

type Container struct {
	Id               int32             `thrift:"id,1" json:"id"`
	Pmid             int32             `thrift:"pmid,2" json:"pmid"`
	Name             string            `thrift:"name,3" json:"name"`
	DisplayName      string            `thrift:"displayName,4" json:"displayName"`
	Flags            []int32           `thrift:"flags,5" json:"flags"`
	ContainerSetting map[string]string `thrift:"containerSetting,6" json:"containerSetting"`
	Status           PauseStatus       `thrift:"status,7" json:"status"`
	HouseAdPosition  map[int32]int32   `thrift:"houseAdPosition,8" json:"houseAdPosition"`
	CloseDomobAd     bool              `thrift:"closeDomobAd,9" json:"closeDomobAd"`
	SortedAds        []int32           `thrift:"sortedAds,10" json:"sortedAds"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime TimeInt `thrift:"createTime,30" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewContainer() *Container {
	return &Container{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Container) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Container) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Container) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Container) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pmid = v
	}
	return nil
}

func (p *Container) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Container) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *Container) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Flags = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Flags = append(p.Flags, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Container) readField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ContainerSetting = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key3 = v
		}
		var _val4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val4 = v
		}
		p.ContainerSetting[_key3] = _val4
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Container) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = PauseStatus(v)
	}
	return nil
}

func (p *Container) readField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.HouseAdPosition = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key5 = v
		}
		var _val6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val6 = v
		}
		p.HouseAdPosition[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Container) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CloseDomobAd = v
	}
	return nil
}

func (p *Container) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SortedAds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.SortedAds = append(p.SortedAds, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Container) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Container) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *Container) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Container"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Container) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Container) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pmid: %s", p, err)
	}
	return err
}

func (p *Container) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Container) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:displayName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.displayName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:displayName: %s", p, err)
	}
	return err
}

func (p *Container) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Flags != nil {
		if err := oprot.WriteFieldBegin("flags", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:flags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Flags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Flags {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:flags: %s", p, err)
		}
	}
	return err
}

func (p *Container) writeField6(oprot thrift.TProtocol) (err error) {
	if p.ContainerSetting != nil {
		if err := oprot.WriteFieldBegin("containerSetting", thrift.MAP, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:containerSetting: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ContainerSetting)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ContainerSetting {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:containerSetting: %s", p, err)
		}
	}
	return err
}

func (p *Container) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:status: %s", p, err)
	}
	return err
}

func (p *Container) writeField8(oprot thrift.TProtocol) (err error) {
	if p.HouseAdPosition != nil {
		if err := oprot.WriteFieldBegin("houseAdPosition", thrift.MAP, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:houseAdPosition: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.HouseAdPosition)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.HouseAdPosition {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:houseAdPosition: %s", p, err)
		}
	}
	return err
}

func (p *Container) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closeDomobAd", thrift.BOOL, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:closeDomobAd: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CloseDomobAd)); err != nil {
		return fmt.Errorf("%T.closeDomobAd (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:closeDomobAd: %s", p, err)
	}
	return err
}

func (p *Container) writeField10(oprot thrift.TProtocol) (err error) {
	if p.SortedAds != nil {
		if err := oprot.WriteFieldBegin("sortedAds", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:sortedAds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SortedAds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SortedAds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:sortedAds: %s", p, err)
		}
	}
	return err
}

func (p *Container) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *Container) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Container) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Container(%+v)", *p)
}

type Channel struct {
	Id           IdInt                  `thrift:"id,1" json:"id"`
	Pmid         IdInt                  `thrift:"pmid,2" json:"pmid"`
	ChannelType  int32                  `thrift:"channelType,3" json:"channelType"`
	Name         string                 `thrift:"name,4" json:"name"`
	Position     int32                  `thrift:"position,5" json:"position"`
	ContainerIds []int32                `thrift:"containerIds,6" json:"containerIds"`
	Setting      map[string]string      `thrift:"setting,7" json:"setting"`
	Status       StatusWhetherAvailable `thrift:"status,8" json:"status"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewChannel() *Channel {
	return &Channel{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Channel) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Channel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Channel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *Channel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pmid = IdInt(v)
	}
	return nil
}

func (p *Channel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChannelType = v
	}
	return nil
}

func (p *Channel) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Channel) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Position = v
	}
	return nil
}

func (p *Channel) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ContainerIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.ContainerIds = append(p.ContainerIds, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Channel) readField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Setting = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key9 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key9 = v
		}
		var _val10 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val10 = v
		}
		p.Setting[_key9] = _val10
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Channel) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Status = StatusWhetherAvailable(v)
	}
	return nil
}

func (p *Channel) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Channel) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *Channel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Channel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Channel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Channel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pmid: %s", p, err)
	}
	return err
}

func (p *Channel) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelType", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channelType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
		return fmt.Errorf("%T.channelType (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channelType: %s", p, err)
	}
	return err
}

func (p *Channel) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Channel) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("position", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:position: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Position)); err != nil {
		return fmt.Errorf("%T.position (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:position: %s", p, err)
	}
	return err
}

func (p *Channel) writeField6(oprot thrift.TProtocol) (err error) {
	if p.ContainerIds != nil {
		if err := oprot.WriteFieldBegin("containerIds", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:containerIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ContainerIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ContainerIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:containerIds: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Setting != nil {
		if err := oprot.WriteFieldBegin("setting", thrift.MAP, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:setting: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Setting)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Setting {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:setting: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:status: %s", p, err)
	}
	return err
}

func (p *Channel) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Channel) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Channel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Channel(%+v)", *p)
}
