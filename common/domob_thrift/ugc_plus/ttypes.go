// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package ugc_plus

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/ugc"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = ugc.GoUnusedProtection__
var GoUnusedProtection__ int

type VideoInfoStruct struct {
	HasVideoStream bool    `thrift:"hasVideoStream,1" json:"hasVideoStream"`
	HasAudioStream bool    `thrift:"hasAudioStream,2" json:"hasAudioStream"`
	VideoWidth     int32   `thrift:"videoWidth,3" json:"videoWidth"`
	VideoHeight    int32   `thrift:"videoHeight,4" json:"videoHeight"`
	Duration       float64 `thrift:"duration,5" json:"duration"`
	VideoCodecName string  `thrift:"videoCodecName,6" json:"videoCodecName"`
	VideoBitrate   int32   `thrift:"videoBitrate,7" json:"videoBitrate"`
	AudioCodecName string  `thrift:"audioCodecName,8" json:"audioCodecName"`
	// unused field # 9
	// unused field # 10
	FileFormat    string `thrift:"file_format,11" json:"file_format"`
	FileExtension string `thrift:"file_extension,12" json:"file_extension"`
}

func NewVideoInfoStruct() *VideoInfoStruct {
	return &VideoInfoStruct{}
}

func (p *VideoInfoStruct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoInfoStruct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.HasVideoStream = v
	}
	return nil
}

func (p *VideoInfoStruct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.HasAudioStream = v
	}
	return nil
}

func (p *VideoInfoStruct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.VideoWidth = v
	}
	return nil
}

func (p *VideoInfoStruct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.VideoHeight = v
	}
	return nil
}

func (p *VideoInfoStruct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *VideoInfoStruct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.VideoCodecName = v
	}
	return nil
}

func (p *VideoInfoStruct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.VideoBitrate = v
	}
	return nil
}

func (p *VideoInfoStruct) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AudioCodecName = v
	}
	return nil
}

func (p *VideoInfoStruct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.FileFormat = v
	}
	return nil
}

func (p *VideoInfoStruct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.FileExtension = v
	}
	return nil
}

func (p *VideoInfoStruct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VideoInfoStruct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoInfoStruct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hasVideoStream", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:hasVideoStream: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.HasVideoStream)); err != nil {
		return fmt.Errorf("%T.hasVideoStream (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:hasVideoStream: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hasAudioStream", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hasAudioStream: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.HasAudioStream)); err != nil {
		return fmt.Errorf("%T.hasAudioStream (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hasAudioStream: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoWidth", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:videoWidth: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoWidth)); err != nil {
		return fmt.Errorf("%T.videoWidth (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:videoWidth: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoHeight", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:videoHeight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoHeight)); err != nil {
		return fmt.Errorf("%T.videoHeight (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:videoHeight: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.DOUBLE, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:duration: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:duration: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoCodecName", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:videoCodecName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoCodecName)); err != nil {
		return fmt.Errorf("%T.videoCodecName (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:videoCodecName: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoBitrate", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:videoBitrate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoBitrate)); err != nil {
		return fmt.Errorf("%T.videoBitrate (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:videoBitrate: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("audioCodecName", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:audioCodecName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AudioCodecName)); err != nil {
		return fmt.Errorf("%T.audioCodecName (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:audioCodecName: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("file_format", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:file_format: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileFormat)); err != nil {
		return fmt.Errorf("%T.file_format (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:file_format: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("file_extension", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:file_extension: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileExtension)); err != nil {
		return fmt.Errorf("%T.file_extension (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:file_extension: %s", p, err)
	}
	return err
}

func (p *VideoInfoStruct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoInfoStruct(%+v)", *p)
}

type FileStruct struct {
	FileId           int32          `thrift:"fileId,1" json:"fileId"`
	FileData         string         `thrift:"fileData,2" json:"fileData"`
	FileEtag         string         `thrift:"fileEtag,3" json:"fileEtag"`
	FileLastModified string         `thrift:"fileLastModified,4" json:"fileLastModified"`
	Response         int32          `thrift:"response,5" json:"response"`
	FileFormat       ugc.FormatCode `thrift:"fileFormat,6" json:"fileFormat"`
	FileEncodeId     string         `thrift:"fileEncodeId,7" json:"fileEncodeId"`
	// unused field # 8
	// unused field # 9
	VideoInfo *VideoInfoStruct `thrift:"videoInfo,10" json:"videoInfo"`
}

func NewFileStruct() *FileStruct {
	return &FileStruct{
		FileFormat: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FileStruct) IsSetFileFormat() bool {
	return int64(p.FileFormat) != math.MinInt32-1
}

func (p *FileStruct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FileStruct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FileId = v
	}
	return nil
}

func (p *FileStruct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.FileEncodeId = v
	}
	return nil
}

func (p *FileStruct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileData = v
	}
	return nil
}

func (p *FileStruct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FileEtag = v
	}
	return nil
}

func (p *FileStruct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FileLastModified = v
	}
	return nil
}

func (p *FileStruct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Response = v
	}
	return nil
}

func (p *FileStruct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FileFormat = ugc.FormatCode(v)
	}
	return nil
}

func (p *FileStruct) readField10(iprot thrift.TProtocol) error {
	p.VideoInfo = NewVideoInfoStruct()
	if err := p.VideoInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VideoInfo)
	}
	return nil
}

func (p *FileStruct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FileStruct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FileStruct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fileId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileId)); err != nil {
		return fmt.Errorf("%T.fileId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fileId: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileData", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fileData: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileData)); err != nil {
		return fmt.Errorf("%T.fileData (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fileData: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEtag", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fileEtag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEtag)); err != nil {
		return fmt.Errorf("%T.fileEtag (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fileEtag: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileLastModified", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:fileLastModified: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileLastModified)); err != nil {
		return fmt.Errorf("%T.fileLastModified (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:fileLastModified: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("response", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:response: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Response)); err != nil {
		return fmt.Errorf("%T.response (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:response: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileFormat() {
		if err := oprot.WriteFieldBegin("fileFormat", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:fileFormat: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileFormat)); err != nil {
			return fmt.Errorf("%T.fileFormat (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:fileFormat: %s", p, err)
		}
	}
	return err
}

func (p *FileStruct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileEncodeId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:fileEncodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileEncodeId)); err != nil {
		return fmt.Errorf("%T.fileEncodeId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:fileEncodeId: %s", p, err)
	}
	return err
}

func (p *FileStruct) writeField10(oprot thrift.TProtocol) (err error) {
	if p.VideoInfo != nil {
		if err := oprot.WriteFieldBegin("videoInfo", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:videoInfo: %s", p, err)
		}
		if err := p.VideoInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VideoInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:videoInfo: %s", p, err)
		}
	}
	return err
}

func (p *FileStruct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileStruct(%+v)", *p)
}
