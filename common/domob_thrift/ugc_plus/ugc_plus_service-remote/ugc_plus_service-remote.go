// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"ugc_plus"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  FileStruct uploadFile(RequestHeader header, string fileData, FormatCode fileFormat)")
	fmt.Fprintln(os.Stderr, "  FileStruct getVideoInfo(RequestHeader header, string fileEncodeId, string fileEtag, string url)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := ugc_plus.NewUgcPlusServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "uploadFile":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UploadFile requires 3 args")
			flag.Usage()
		}
		arg10 := flag.Arg(1)
		mbTrans11 := thrift.NewTMemoryBufferLen(len(arg10))
		defer mbTrans11.Close()
		_, err12 := mbTrans11.WriteString(arg10)
		if err12 != nil {
			Usage()
			return
		}
		factory13 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt14 := factory13.GetProtocol(mbTrans11)
		argvalue0 := ugc_plus.NewRequestHeader()
		err15 := argvalue0.Read(jsProt14)
		if err15 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := ugc_plus.FormatCode(tmp2)
		value2 := argvalue2
		fmt.Print(client.UploadFile(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getVideoInfo":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetVideoInfo requires 4 args")
			flag.Usage()
		}
		arg17 := flag.Arg(1)
		mbTrans18 := thrift.NewTMemoryBufferLen(len(arg17))
		defer mbTrans18.Close()
		_, err19 := mbTrans18.WriteString(arg17)
		if err19 != nil {
			Usage()
			return
		}
		factory20 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt21 := factory20.GetProtocol(mbTrans18)
		argvalue0 := ugc_plus.NewRequestHeader()
		err22 := argvalue0.Read(jsProt21)
		if err22 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.GetVideoInfo(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
