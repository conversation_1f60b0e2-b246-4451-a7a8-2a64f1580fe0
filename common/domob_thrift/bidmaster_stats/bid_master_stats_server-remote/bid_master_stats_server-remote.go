// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"bidmaster_stats"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>r, "  StatsQueryResult queryStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, i32 offset, i32 limit)")
	fmt.Fprintln(os.<PERSON>derr, "  StatsQueryResult queryMarkupStatsData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryStatsAnalysisData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, StatsAnalysisDimension dimension, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  StatsQueryResult queryMarkupStatsAnalysisData(RequestHeader header, StatsConditionParam condition, StatsGroupByParam group, StatsOrderByParam order, StatsAnalysisDimension dimension, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := bidmaster_stats.NewBidMasterStatsServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryStatsData requires 5 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := bidmaster_stats.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg36 := flag.Arg(2)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue1 := bidmaster_stats.NewStatsConditionParam()
		err41 := argvalue1.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg42 := flag.Arg(3)
		mbTrans43 := thrift.NewTMemoryBufferLen(len(arg42))
		defer mbTrans43.Close()
		_, err44 := mbTrans43.WriteString(arg42)
		if err44 != nil {
			Usage()
			return
		}
		factory45 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt46 := factory45.GetProtocol(mbTrans43)
		argvalue2 := bidmaster_stats.NewStatsGroupByParam()
		err47 := argvalue2.Read(jsProt46)
		if err47 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err48 := (strconv.Atoi(flag.Arg(4)))
		if err48 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err49 := (strconv.Atoi(flag.Arg(5)))
		if err49 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryMarkupStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryMarkupStatsData requires 5 args")
			flag.Usage()
		}
		arg50 := flag.Arg(1)
		mbTrans51 := thrift.NewTMemoryBufferLen(len(arg50))
		defer mbTrans51.Close()
		_, err52 := mbTrans51.WriteString(arg50)
		if err52 != nil {
			Usage()
			return
		}
		factory53 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt54 := factory53.GetProtocol(mbTrans51)
		argvalue0 := bidmaster_stats.NewRequestHeader()
		err55 := argvalue0.Read(jsProt54)
		if err55 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg56 := flag.Arg(2)
		mbTrans57 := thrift.NewTMemoryBufferLen(len(arg56))
		defer mbTrans57.Close()
		_, err58 := mbTrans57.WriteString(arg56)
		if err58 != nil {
			Usage()
			return
		}
		factory59 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt60 := factory59.GetProtocol(mbTrans57)
		argvalue1 := bidmaster_stats.NewStatsConditionParam()
		err61 := argvalue1.Read(jsProt60)
		if err61 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg62 := flag.Arg(3)
		mbTrans63 := thrift.NewTMemoryBufferLen(len(arg62))
		defer mbTrans63.Close()
		_, err64 := mbTrans63.WriteString(arg62)
		if err64 != nil {
			Usage()
			return
		}
		factory65 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt66 := factory65.GetProtocol(mbTrans63)
		argvalue2 := bidmaster_stats.NewStatsGroupByParam()
		err67 := argvalue2.Read(jsProt66)
		if err67 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err68 := (strconv.Atoi(flag.Arg(4)))
		if err68 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err69 := (strconv.Atoi(flag.Arg(5)))
		if err69 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryMarkupStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryStatsAnalysisData":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryStatsAnalysisData requires 7 args")
			flag.Usage()
		}
		arg70 := flag.Arg(1)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		argvalue0 := bidmaster_stats.NewRequestHeader()
		err75 := argvalue0.Read(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg76 := flag.Arg(2)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue1 := bidmaster_stats.NewStatsConditionParam()
		err81 := argvalue1.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg82 := flag.Arg(3)
		mbTrans83 := thrift.NewTMemoryBufferLen(len(arg82))
		defer mbTrans83.Close()
		_, err84 := mbTrans83.WriteString(arg82)
		if err84 != nil {
			Usage()
			return
		}
		factory85 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt86 := factory85.GetProtocol(mbTrans83)
		argvalue2 := bidmaster_stats.NewStatsGroupByParam()
		err87 := argvalue2.Read(jsProt86)
		if err87 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg88 := flag.Arg(4)
		mbTrans89 := thrift.NewTMemoryBufferLen(len(arg88))
		defer mbTrans89.Close()
		_, err90 := mbTrans89.WriteString(arg88)
		if err90 != nil {
			Usage()
			return
		}
		factory91 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt92 := factory91.GetProtocol(mbTrans89)
		argvalue3 := bidmaster_stats.NewStatsOrderByParam()
		err93 := argvalue3.Read(jsProt92)
		if err93 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := bidmaster_stats.StatsAnalysisDimension(tmp4)
		value4 := argvalue4
		tmp5, err94 := (strconv.Atoi(flag.Arg(6)))
		if err94 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err95 := (strconv.Atoi(flag.Arg(7)))
		if err95 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		fmt.Print(client.QueryStatsAnalysisData(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "queryMarkupStatsAnalysisData":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryMarkupStatsAnalysisData requires 7 args")
			flag.Usage()
		}
		arg96 := flag.Arg(1)
		mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
		defer mbTrans97.Close()
		_, err98 := mbTrans97.WriteString(arg96)
		if err98 != nil {
			Usage()
			return
		}
		factory99 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt100 := factory99.GetProtocol(mbTrans97)
		argvalue0 := bidmaster_stats.NewRequestHeader()
		err101 := argvalue0.Read(jsProt100)
		if err101 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg102 := flag.Arg(2)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue1 := bidmaster_stats.NewStatsConditionParam()
		err107 := argvalue1.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg108 := flag.Arg(3)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue2 := bidmaster_stats.NewStatsGroupByParam()
		err113 := argvalue2.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg114 := flag.Arg(4)
		mbTrans115 := thrift.NewTMemoryBufferLen(len(arg114))
		defer mbTrans115.Close()
		_, err116 := mbTrans115.WriteString(arg114)
		if err116 != nil {
			Usage()
			return
		}
		factory117 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt118 := factory117.GetProtocol(mbTrans115)
		argvalue3 := bidmaster_stats.NewStatsOrderByParam()
		err119 := argvalue3.Read(jsProt118)
		if err119 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := bidmaster_stats.StatsAnalysisDimension(tmp4)
		value4 := argvalue4
		tmp5, err120 := (strconv.Atoi(flag.Arg(6)))
		if err120 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err121 := (strconv.Atoi(flag.Arg(7)))
		if err121 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		fmt.Print(client.QueryMarkupStatsAnalysisData(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
