// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_evaluate_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type DspEvaluateResult struct {
	Pv int64 `thrift:"pv,1" json:"pv"`
}

func NewDspEvaluateResult() *DspEvaluateResult {
	return &DspEvaluateResult{}
}

func (p *DspEvaluateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspEvaluateResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pv = v
	}
	return nil
}

func (p *DspEvaluateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspEvaluateResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspEvaluateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pv", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pv: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Pv)); err != nil {
		return fmt.Errorf("%T.pv (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pv: %s", p, err)
	}
	return err
}

func (p *DspEvaluateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspEvaluateResult(%+v)", *p)
}

type DspTagsNode struct {
	Tags []int32 `thrift:"tags,1" json:"tags"`
}

func NewDspTagsNode() *DspTagsNode {
	return &DspTagsNode{}
}

func (p *DspTagsNode) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspTagsNode) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tags = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Tags = append(p.Tags, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DspTagsNode) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspTagsNode"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspTagsNode) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Tags != nil {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:tags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Tags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tags {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:tags: %s", p, err)
		}
	}
	return err
}

func (p *DspTagsNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspTagsNode(%+v)", *p)
}

type DspTagEvaluateResult struct {
	TotalCrowdNum int64           `thrift:"totalCrowdNum,1" json:"totalCrowdNum"`
	TagCrowdNum   map[int32]int64 `thrift:"tagCrowdNum,2" json:"tagCrowdNum"`
}

func NewDspTagEvaluateResult() *DspTagEvaluateResult {
	return &DspTagEvaluateResult{}
}

func (p *DspTagEvaluateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspTagEvaluateResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCrowdNum = v
	}
	return nil
}

func (p *DspTagEvaluateResult) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.TagCrowdNum = make(map[int32]int64, size)
	for i := 0; i < size; i++ {
		var _key1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.TagCrowdNum[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DspTagEvaluateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspTagEvaluateResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspTagEvaluateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCrowdNum", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCrowdNum: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalCrowdNum)); err != nil {
		return fmt.Errorf("%T.totalCrowdNum (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCrowdNum: %s", p, err)
	}
	return err
}

func (p *DspTagEvaluateResult) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TagCrowdNum != nil {
		if err := oprot.WriteFieldBegin("tagCrowdNum", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:tagCrowdNum: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I64, len(p.TagCrowdNum)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.TagCrowdNum {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:tagCrowdNum: %s", p, err)
		}
	}
	return err
}

func (p *DspTagEvaluateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspTagEvaluateResult(%+v)", *p)
}
