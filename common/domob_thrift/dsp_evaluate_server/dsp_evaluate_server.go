// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_evaluate_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type DspEvaluateServer interface {
	dm303.DomobService
	//DSP 估量服务接口定义

	// 预估符合条件的RTB的日PV
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - ExchangeId: 交易所id，如果为空则表示全部
	//  - MediaCategory: 媒体分类，如果为空则表示全部
	//  - InventoryType: 广告类型，如果为空则表示全部
	// 0 unknown
	// 1 in-app banner
	// 2 in-app instl banner
	// 3 in-app pre-roll video
	// 4 in-app pre-roll video(instl)
	// 5 in-app mid-roll video
	// 6 in-app mid-roll video(instl)
	// 7 in-app post-roll video
	// 8 in-app post-roll video(instl)
	// 9 in-app native feeds
	//  - Platform: SDK的平台类型，如果为空则表示全部，参见enums.thrift中的定义
	//  - AccessType: 接入方式，如果为空则表示全部，参见enums.thrift中的定义
	//  - CarrierCode: 运营商，如果为空则表示全部，参见enums.thrift中的定义
	//  - GeoCity: 地域识别出来的城市，如果想要省级别的，则填省级的code，如果为空则表示全部，地域值详见http://git.domob-inc.cn/domobsearch/up_service/blob/master/data/region_code
	//  - Bid: 出价 单位:盟元/千次展示
	GetRTBDailyPV(requestHeader *common.RequestHeader, exchangeId []int32, mediaCategory []int32, inventoryType []int32, platform []enums.SDKPlatform, accessType []enums.AccessTypeCode, carrierCode []enums.CarrierCode, geoCity []int32, bid int64) (r *DspEvaluateResult, err error)
	// 预估符合条件的tag的人群数
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TagsList: 不同属性间的tag列表，按与选择
	GetTagCrowdNum(requestHeader *common.RequestHeader, tagsList []*DspTagsNode) (r *DspTagEvaluateResult, err error)
}

//DSP 估量服务接口定义
type DspEvaluateServerClient struct {
	*dm303.DomobServiceClient
}

func NewDspEvaluateServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DspEvaluateServerClient {
	return &DspEvaluateServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDspEvaluateServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DspEvaluateServerClient {
	return &DspEvaluateServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 预估符合条件的RTB的日PV
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - ExchangeId: 交易所id，如果为空则表示全部
//  - MediaCategory: 媒体分类，如果为空则表示全部
//  - InventoryType: 广告类型，如果为空则表示全部
// 0 unknown
// 1 in-app banner
// 2 in-app instl banner
// 3 in-app pre-roll video
// 4 in-app pre-roll video(instl)
// 5 in-app mid-roll video
// 6 in-app mid-roll video(instl)
// 7 in-app post-roll video
// 8 in-app post-roll video(instl)
// 9 in-app native feeds
//  - Platform: SDK的平台类型，如果为空则表示全部，参见enums.thrift中的定义
//  - AccessType: 接入方式，如果为空则表示全部，参见enums.thrift中的定义
//  - CarrierCode: 运营商，如果为空则表示全部，参见enums.thrift中的定义
//  - GeoCity: 地域识别出来的城市，如果想要省级别的，则填省级的code，如果为空则表示全部，地域值详见http://git.domob-inc.cn/domobsearch/up_service/blob/master/data/region_code
//  - Bid: 出价 单位:盟元/千次展示
func (p *DspEvaluateServerClient) GetRTBDailyPV(requestHeader *common.RequestHeader, exchangeId []int32, mediaCategory []int32, inventoryType []int32, platform []enums.SDKPlatform, accessType []enums.AccessTypeCode, carrierCode []enums.CarrierCode, geoCity []int32, bid int64) (r *DspEvaluateResult, err error) {
	if err = p.sendGetRTBDailyPV(requestHeader, exchangeId, mediaCategory, inventoryType, platform, accessType, carrierCode, geoCity, bid); err != nil {
		return
	}
	return p.recvGetRTBDailyPV()
}

func (p *DspEvaluateServerClient) sendGetRTBDailyPV(requestHeader *common.RequestHeader, exchangeId []int32, mediaCategory []int32, inventoryType []int32, platform []enums.SDKPlatform, accessType []enums.AccessTypeCode, carrierCode []enums.CarrierCode, geoCity []int32, bid int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getRTBDailyPV", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args3 := NewGetRTBDailyPVArgs()
	args3.RequestHeader = requestHeader
	args3.ExchangeId = exchangeId
	args3.MediaCategory = mediaCategory
	args3.InventoryType = inventoryType
	args3.Platform = platform
	args3.AccessType = accessType
	args3.CarrierCode = carrierCode
	args3.GeoCity = geoCity
	args3.Bid = bid
	if err = args3.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspEvaluateServerClient) recvGetRTBDailyPV() (value *DspEvaluateResult, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error5 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error6 error
		error6, err = error5.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error6
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result4 := NewGetRTBDailyPVResult()
	if err = result4.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result4.Success
	return
}

// 预估符合条件的tag的人群数
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TagsList: 不同属性间的tag列表，按与选择
func (p *DspEvaluateServerClient) GetTagCrowdNum(requestHeader *common.RequestHeader, tagsList []*DspTagsNode) (r *DspTagEvaluateResult, err error) {
	if err = p.sendGetTagCrowdNum(requestHeader, tagsList); err != nil {
		return
	}
	return p.recvGetTagCrowdNum()
}

func (p *DspEvaluateServerClient) sendGetTagCrowdNum(requestHeader *common.RequestHeader, tagsList []*DspTagsNode) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTagCrowdNum", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args7 := NewGetTagCrowdNumArgs()
	args7.RequestHeader = requestHeader
	args7.TagsList = tagsList
	if err = args7.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspEvaluateServerClient) recvGetTagCrowdNum() (value *DspTagEvaluateResult, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error9 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error10 error
		error10, err = error9.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error10
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result8 := NewGetTagCrowdNumResult()
	if err = result8.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result8.Success
	return
}

type DspEvaluateServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDspEvaluateServerProcessor(handler DspEvaluateServer) *DspEvaluateServerProcessor {
	self11 := &DspEvaluateServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self11.AddToProcessorMap("getRTBDailyPV", &dspEvaluateServerProcessorGetRTBDailyPV{handler: handler})
	self11.AddToProcessorMap("getTagCrowdNum", &dspEvaluateServerProcessorGetTagCrowdNum{handler: handler})
	return self11
}

type dspEvaluateServerProcessorGetRTBDailyPV struct {
	handler DspEvaluateServer
}

func (p *dspEvaluateServerProcessorGetRTBDailyPV) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRTBDailyPVArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getRTBDailyPV", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRTBDailyPVResult()
	if result.Success, err = p.handler.GetRTBDailyPV(args.RequestHeader, args.ExchangeId, args.MediaCategory, args.InventoryType, args.Platform, args.AccessType, args.CarrierCode, args.GeoCity, args.Bid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRTBDailyPV: "+err.Error())
		oprot.WriteMessageBegin("getRTBDailyPV", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getRTBDailyPV", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspEvaluateServerProcessorGetTagCrowdNum struct {
	handler DspEvaluateServer
}

func (p *dspEvaluateServerProcessorGetTagCrowdNum) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTagCrowdNumArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTagCrowdNum", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTagCrowdNumResult()
	if result.Success, err = p.handler.GetTagCrowdNum(args.RequestHeader, args.TagsList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTagCrowdNum: "+err.Error())
		oprot.WriteMessageBegin("getTagCrowdNum", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTagCrowdNum", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetRTBDailyPVArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	ExchangeId    []int32                `thrift:"exchangeId,11" json:"exchangeId"`
	MediaCategory []int32                `thrift:"mediaCategory,12" json:"mediaCategory"`
	InventoryType []int32                `thrift:"inventoryType,13" json:"inventoryType"`
	Platform      []enums.SDKPlatform    `thrift:"platform,14" json:"platform"`
	AccessType    []enums.AccessTypeCode `thrift:"accessType,15" json:"accessType"`
	CarrierCode   []enums.CarrierCode    `thrift:"carrierCode,16" json:"carrierCode"`
	GeoCity       []int32                `thrift:"geoCity,17" json:"geoCity"`
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Bid int64 `thrift:"bid,31" json:"bid"`
}

func NewGetRTBDailyPVArgs() *GetRTBDailyPVArgs {
	return &GetRTBDailyPVArgs{}
}

func (p *GetRTBDailyPVArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExchangeId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.ExchangeId = append(p.ExchangeId, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaCategory = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.MediaCategory = append(p.MediaCategory, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.InventoryType = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.InventoryType = append(p.InventoryType, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platform = make([]enums.SDKPlatform, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 enums.SDKPlatform
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = enums.SDKPlatform(v)
		}
		p.Platform = append(p.Platform, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccessType = make([]enums.AccessTypeCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 enums.AccessTypeCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = enums.AccessTypeCode(v)
		}
		p.AccessType = append(p.AccessType, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CarrierCode = make([]enums.CarrierCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem17 enums.CarrierCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem17 = enums.CarrierCode(v)
		}
		p.CarrierCode = append(p.CarrierCode, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoCity = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = v
		}
		p.GeoCity = append(p.GeoCity, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Bid = v
	}
	return nil
}

func (p *GetRTBDailyPVArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRTBDailyPV_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRTBDailyPVArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if p.ExchangeId != nil {
		if err := oprot.WriteFieldBegin("exchangeId", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:exchangeId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExchangeId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExchangeId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:exchangeId: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if p.MediaCategory != nil {
		if err := oprot.WriteFieldBegin("mediaCategory", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:mediaCategory: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaCategory)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaCategory {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:mediaCategory: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if p.InventoryType != nil {
		if err := oprot.WriteFieldBegin("inventoryType", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:inventoryType: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.InventoryType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.InventoryType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:inventoryType: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Platform != nil {
		if err := oprot.WriteFieldBegin("platform", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:platform: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Platform)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platform {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:platform: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField15(oprot thrift.TProtocol) (err error) {
	if p.AccessType != nil {
		if err := oprot.WriteFieldBegin("accessType", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:accessType: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AccessType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccessType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:accessType: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField16(oprot thrift.TProtocol) (err error) {
	if p.CarrierCode != nil {
		if err := oprot.WriteFieldBegin("carrierCode", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:carrierCode: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CarrierCode)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CarrierCode {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:carrierCode: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField17(oprot thrift.TProtocol) (err error) {
	if p.GeoCity != nil {
		if err := oprot.WriteFieldBegin("geoCity", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:geoCity: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GeoCity)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoCity {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:geoCity: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVArgs) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:bid: %s", p, err)
	}
	return err
}

func (p *GetRTBDailyPVArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRTBDailyPVArgs(%+v)", *p)
}

type GetRTBDailyPVResult struct {
	Success *DspEvaluateResult `thrift:"success,0" json:"success"`
}

func NewGetRTBDailyPVResult() *GetRTBDailyPVResult {
	return &GetRTBDailyPVResult{}
}

func (p *GetRTBDailyPVResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRTBDailyPVResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDspEvaluateResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRTBDailyPVResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRTBDailyPV_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRTBDailyPVResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRTBDailyPVResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRTBDailyPVResult(%+v)", *p)
}

type GetTagCrowdNumArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	TagsList []*DspTagsNode `thrift:"tagsList,11" json:"tagsList"`
}

func NewGetTagCrowdNumArgs() *GetTagCrowdNumArgs {
	return &GetTagCrowdNumArgs{}
}

func (p *GetTagCrowdNumArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTagCrowdNumArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetTagCrowdNumArgs) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TagsList = make([]*DspTagsNode, 0, size)
	for i := 0; i < size; i++ {
		_elem19 := NewDspTagsNode()
		if err := _elem19.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem19)
		}
		p.TagsList = append(p.TagsList, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTagCrowdNumArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTagCrowdNum_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTagCrowdNumArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetTagCrowdNumArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if p.TagsList != nil {
		if err := oprot.WriteFieldBegin("tagsList", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:tagsList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TagsList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TagsList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:tagsList: %s", p, err)
		}
	}
	return err
}

func (p *GetTagCrowdNumArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTagCrowdNumArgs(%+v)", *p)
}

type GetTagCrowdNumResult struct {
	Success *DspTagEvaluateResult `thrift:"success,0" json:"success"`
}

func NewGetTagCrowdNumResult() *GetTagCrowdNumResult {
	return &GetTagCrowdNumResult{}
}

func (p *GetTagCrowdNumResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTagCrowdNumResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDspTagEvaluateResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTagCrowdNumResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTagCrowdNum_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTagCrowdNumResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTagCrowdNumResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTagCrowdNumResult(%+v)", *p)
}
