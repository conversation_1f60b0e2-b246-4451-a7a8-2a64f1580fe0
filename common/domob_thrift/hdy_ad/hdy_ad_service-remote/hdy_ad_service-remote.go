// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"hdy_ad"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getAds(RequestHeader header, string media, i32 landingId, OSType os, string device, i64 ip,  adPlacementIds)")
	fmt.Fprintln(os.Stderr, "   getMediaRedirects(RequestHeader header, string media, i32 mediaChId, OSType os, string device, i64 ip)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := hdy_ad.NewHdyAdServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getAds":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetAds requires 7 args")
			flag.Usage()
		}
		arg15 := flag.Arg(1)
		mbTrans16 := thrift.NewTMemoryBufferLen(len(arg15))
		defer mbTrans16.Close()
		_, err17 := mbTrans16.WriteString(arg15)
		if err17 != nil {
			Usage()
			return
		}
		factory18 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt19 := factory18.GetProtocol(mbTrans16)
		argvalue0 := hdy_ad.NewRequestHeader()
		err20 := argvalue0.Read(jsProt19)
		if err20 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err22 := (strconv.Atoi(flag.Arg(3)))
		if err22 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := hdy_ad.OSType(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5, err24 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err24 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		arg25 := flag.Arg(7)
		mbTrans26 := thrift.NewTMemoryBufferLen(len(arg25))
		defer mbTrans26.Close()
		_, err27 := mbTrans26.WriteString(arg25)
		if err27 != nil {
			Usage()
			return
		}
		factory28 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt29 := factory28.GetProtocol(mbTrans26)
		containerStruct6 := hdy_ad.NewGetAdsArgs()
		err30 := containerStruct6.ReadField7(jsProt29)
		if err30 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.AdPlacementIds
		value6 := argvalue6
		fmt.Print(client.GetAds(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getMediaRedirects":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetMediaRedirects requires 6 args")
			flag.Usage()
		}
		arg31 := flag.Arg(1)
		mbTrans32 := thrift.NewTMemoryBufferLen(len(arg31))
		defer mbTrans32.Close()
		_, err33 := mbTrans32.WriteString(arg31)
		if err33 != nil {
			Usage()
			return
		}
		factory34 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt35 := factory34.GetProtocol(mbTrans32)
		argvalue0 := hdy_ad.NewRequestHeader()
		err36 := argvalue0.Read(jsProt35)
		if err36 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err38 := (strconv.Atoi(flag.Arg(3)))
		if err38 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := hdy_ad.OSType(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5, err40 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err40 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		fmt.Print(client.GetMediaRedirects(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
