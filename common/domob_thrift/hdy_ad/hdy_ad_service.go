// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_ad

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/hdy_ad_types"
	"rtb_model_server/common/domob_thrift/hdy_exception"
	"rtb_model_server/common/domob_thrift/hdy_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = hdy_exception.GoUnusedProtection__
var _ = hdy_ad_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = hdy_types.GoUnusedProtection__

type HdyAdService interface {
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Media: 媒体名称 *
	//  - LandingId: landing id *
	//  - Os: 操作系统 *
	//  - Device: 设备ID *
	//  - Ip: IP按段拼接的10进制表示如 ******* => 1002003004 *
	//  - AdPlacementIds
	GetAds(header *common.RequestHeader, media string, landingId int32, os hdy_types.OSType, device string, ip int64, adPlacementIds []int32) (r map[int32][]*hdy_ad_types.Ad, e *hdy_exception.HdyException, err error)
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Media: 媒体name *
	//  - MediaChId: 媒体渠道ID *
	//  - Os: 操作系统 *
	//  - Device: 设备ID *
	//  - Ip: IP按段拼接的10进制表示如 ******* => 1002003004 *
	GetMediaRedirects(header *common.RequestHeader, media string, mediaChId int32, os hdy_types.OSType, device string, ip int64) (r []*hdy_ad_types.Landing, e *hdy_exception.HdyException, err error)
}

type HdyAdServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewHdyAdServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *HdyAdServiceClient {
	return &HdyAdServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewHdyAdServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *HdyAdServiceClient {
	return &HdyAdServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// Parameters:
//  - Header: 请求头部信息 *
//  - Media: 媒体名称 *
//  - LandingId: landing id *
//  - Os: 操作系统 *
//  - Device: 设备ID *
//  - Ip: IP按段拼接的10进制表示如 ******* => 1002003004 *
//  - AdPlacementIds
func (p *HdyAdServiceClient) GetAds(header *common.RequestHeader, media string, landingId int32, os hdy_types.OSType, device string, ip int64, adPlacementIds []int32) (r map[int32][]*hdy_ad_types.Ad, e *hdy_exception.HdyException, err error) {
	if err = p.sendGetAds(header, media, landingId, os, device, ip, adPlacementIds); err != nil {
		return
	}
	return p.recvGetAds()
}

func (p *HdyAdServiceClient) sendGetAds(header *common.RequestHeader, media string, landingId int32, os hdy_types.OSType, device string, ip int64, adPlacementIds []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetAdsArgs()
	args0.Header = header
	args0.Media = media
	args0.LandingId = landingId
	args0.Os = os
	args0.Device = device
	args0.Ip = ip
	args0.AdPlacementIds = adPlacementIds
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *HdyAdServiceClient) recvGetAds() (value map[int32][]*hdy_ad_types.Ad, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetAdsResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// Parameters:
//  - Header: 请求头部信息 *
//  - Media: 媒体name *
//  - MediaChId: 媒体渠道ID *
//  - Os: 操作系统 *
//  - Device: 设备ID *
//  - Ip: IP按段拼接的10进制表示如 ******* => 1002003004 *
func (p *HdyAdServiceClient) GetMediaRedirects(header *common.RequestHeader, media string, mediaChId int32, os hdy_types.OSType, device string, ip int64) (r []*hdy_ad_types.Landing, e *hdy_exception.HdyException, err error) {
	if err = p.sendGetMediaRedirects(header, media, mediaChId, os, device, ip); err != nil {
		return
	}
	return p.recvGetMediaRedirects()
}

func (p *HdyAdServiceClient) sendGetMediaRedirects(header *common.RequestHeader, media string, mediaChId int32, os hdy_types.OSType, device string, ip int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMediaRedirects", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetMediaRedirectsArgs()
	args4.Header = header
	args4.Media = media
	args4.MediaChId = mediaChId
	args4.Os = os
	args4.Device = device
	args4.Ip = ip
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *HdyAdServiceClient) recvGetMediaRedirects() (value []*hdy_ad_types.Landing, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetMediaRedirectsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

type HdyAdServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      HdyAdService
}

func (p *HdyAdServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *HdyAdServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *HdyAdServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewHdyAdServiceProcessor(handler HdyAdService) *HdyAdServiceProcessor {

	self8 := &HdyAdServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self8.processorMap["getAds"] = &hdyAdServiceProcessorGetAds{handler: handler}
	self8.processorMap["getMediaRedirects"] = &hdyAdServiceProcessorGetMediaRedirects{handler: handler}
	return self8
}

func (p *HdyAdServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x9 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x9.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x9

}

type hdyAdServiceProcessorGetAds struct {
	handler HdyAdService
}

func (p *hdyAdServiceProcessorGetAds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAdsResult()
	if result.Success, result.E, err = p.handler.GetAds(args.Header, args.Media, args.LandingId, args.Os, args.Device, args.Ip, args.AdPlacementIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAds: "+err.Error())
		oprot.WriteMessageBegin("getAds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type hdyAdServiceProcessorGetMediaRedirects struct {
	handler HdyAdService
}

func (p *hdyAdServiceProcessorGetMediaRedirects) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMediaRedirectsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMediaRedirects", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMediaRedirectsResult()
	if result.Success, result.E, err = p.handler.GetMediaRedirects(args.Header, args.Media, args.MediaChId, args.Os, args.Device, args.Ip); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMediaRedirects: "+err.Error())
		oprot.WriteMessageBegin("getMediaRedirects", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMediaRedirects", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetAdsArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	Media          string                `thrift:"media,2" json:"media"`
	LandingId      int32                 `thrift:"landingId,3" json:"landingId"`
	Os             hdy_types.OSType      `thrift:"os,4" json:"os"`
	Device         string                `thrift:"device,5" json:"device"`
	Ip             int64                 `thrift:"ip,6" json:"ip"`
	AdPlacementIds []int32               `thrift:"adPlacementIds,7" json:"adPlacementIds"`
}

func NewGetAdsArgs() *GetAdsArgs {
	return &GetAdsArgs{
		Os: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAdsArgs) IsSetOs() bool {
	return int64(p.Os) != math.MinInt32-1
}

func (p *GetAdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAdsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *GetAdsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.LandingId = v
	}
	return nil
}

func (p *GetAdsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Os = hdy_types.OSType(v)
	}
	return nil
}

func (p *GetAdsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Device = v
	}
	return nil
}

func (p *GetAdsArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *GetAdsArgs) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdPlacementIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.AdPlacementIds = append(p.AdPlacementIds, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:media: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Media)); err != nil {
		return fmt.Errorf("%T.media (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:media: %s", p, err)
	}
	return err
}

func (p *GetAdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:landingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LandingId)); err != nil {
		return fmt.Errorf("%T.landingId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:landingId: %s", p, err)
	}
	return err
}

func (p *GetAdsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOs() {
		if err := oprot.WriteFieldBegin("os", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:os: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Os)); err != nil {
			return fmt.Errorf("%T.os (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:os: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:device: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Device)); err != nil {
		return fmt.Errorf("%T.device (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:device: %s", p, err)
	}
	return err
}

func (p *GetAdsArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ip: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ip: %s", p, err)
	}
	return err
}

func (p *GetAdsArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.AdPlacementIds != nil {
		if err := oprot.WriteFieldBegin("adPlacementIds", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:adPlacementIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdPlacementIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdPlacementIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:adPlacementIds: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdsArgs(%+v)", *p)
}

type GetAdsResult struct {
	Success map[int32][]*hdy_ad_types.Ad `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException  `thrift:"e,1" json:"e"`
}

func NewGetAdsResult() *GetAdsResult {
	return &GetAdsResult{}
}

func (p *GetAdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32][]*hdy_ad_types.Ad, size)
	for i := 0; i < size; i++ {
		var _key11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key11 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val12 := make([]*hdy_ad_types.Ad, 0, size)
		for i := 0; i < size; i++ {
			_elem13 := hdy_ad_types.NewAd()
			if err := _elem13.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem13)
			}
			_val12 = append(_val12, _elem13)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key11] = _val12
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetAdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetAdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdsResult(%+v)", *p)
}

type GetMediaRedirectsArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Media     string                `thrift:"media,2" json:"media"`
	MediaChId int32                 `thrift:"mediaChId,3" json:"mediaChId"`
	Os        hdy_types.OSType      `thrift:"os,4" json:"os"`
	Device    string                `thrift:"device,5" json:"device"`
	Ip        int64                 `thrift:"ip,6" json:"ip"`
}

func NewGetMediaRedirectsArgs() *GetMediaRedirectsArgs {
	return &GetMediaRedirectsArgs{
		Os: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetMediaRedirectsArgs) IsSetOs() bool {
	return int64(p.Os) != math.MinInt32-1
}

func (p *GetMediaRedirectsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaRedirectsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetMediaRedirectsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *GetMediaRedirectsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaChId = v
	}
	return nil
}

func (p *GetMediaRedirectsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Os = hdy_types.OSType(v)
	}
	return nil
}

func (p *GetMediaRedirectsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Device = v
	}
	return nil
}

func (p *GetMediaRedirectsArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *GetMediaRedirectsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaRedirects_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaRedirectsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaRedirectsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:media: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Media)); err != nil {
		return fmt.Errorf("%T.media (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:media: %s", p, err)
	}
	return err
}

func (p *GetMediaRedirectsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaChId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mediaChId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaChId)); err != nil {
		return fmt.Errorf("%T.mediaChId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mediaChId: %s", p, err)
	}
	return err
}

func (p *GetMediaRedirectsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOs() {
		if err := oprot.WriteFieldBegin("os", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:os: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Os)); err != nil {
			return fmt.Errorf("%T.os (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:os: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaRedirectsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:device: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Device)); err != nil {
		return fmt.Errorf("%T.device (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:device: %s", p, err)
	}
	return err
}

func (p *GetMediaRedirectsArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ip: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ip: %s", p, err)
	}
	return err
}

func (p *GetMediaRedirectsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaRedirectsArgs(%+v)", *p)
}

type GetMediaRedirectsResult struct {
	Success []*hdy_ad_types.Landing     `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewGetMediaRedirectsResult() *GetMediaRedirectsResult {
	return &GetMediaRedirectsResult{}
}

func (p *GetMediaRedirectsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaRedirectsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*hdy_ad_types.Landing, 0, size)
	for i := 0; i < size; i++ {
		_elem14 := hdy_ad_types.NewLanding()
		if err := _elem14.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem14)
		}
		p.Success = append(p.Success, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetMediaRedirectsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetMediaRedirectsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaRedirects_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaRedirectsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaRedirectsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaRedirectsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaRedirectsResult(%+v)", *p)
}
