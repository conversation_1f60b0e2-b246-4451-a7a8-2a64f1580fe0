// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dp_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//开发者服务类型
type ServiceType int64

const (
	ServiceType_ST_DPUSH ServiceType = 2000001
	ServiceType_ST_SSP   ServiceType = 2000002
)

func (p ServiceType) String() string {
	switch p {
	case ServiceType_ST_DPUSH:
		return "ServiceType_ST_DPUSH"
	case ServiceType_ST_SSP:
		return "ServiceType_ST_SSP"
	}
	return "<UNSET>"
}

func ServiceTypeFromString(s string) (ServiceType, error) {
	switch s {
	case "ServiceType_ST_DPUSH":
		return ServiceType_ST_DPUSH, nil
	case "ServiceType_ST_SSP":
		return ServiceType_ST_SSP, nil
	}
	return ServiceType(math.MinInt32 - 1), fmt.Errorf("not a valid ServiceType string")
}

//app应用类型
type AppPlatform int64

const (
	AppPlatform_APP_PLATFORM_UNKNOWN AppPlatform = 0
	AppPlatform_APP_PLATFORM_ANDROID AppPlatform = 1
	AppPlatform_APP_PLATFORM_IOS     AppPlatform = 2
)

func (p AppPlatform) String() string {
	switch p {
	case AppPlatform_APP_PLATFORM_UNKNOWN:
		return "AppPlatform_APP_PLATFORM_UNKNOWN"
	case AppPlatform_APP_PLATFORM_ANDROID:
		return "AppPlatform_APP_PLATFORM_ANDROID"
	case AppPlatform_APP_PLATFORM_IOS:
		return "AppPlatform_APP_PLATFORM_IOS"
	}
	return "<UNSET>"
}

func AppPlatformFromString(s string) (AppPlatform, error) {
	switch s {
	case "AppPlatform_APP_PLATFORM_UNKNOWN":
		return AppPlatform_APP_PLATFORM_UNKNOWN, nil
	case "AppPlatform_APP_PLATFORM_ANDROID":
		return AppPlatform_APP_PLATFORM_ANDROID, nil
	case "AppPlatform_APP_PLATFORM_IOS":
		return AppPlatform_APP_PLATFORM_IOS, nil
	}
	return AppPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid AppPlatform string")
}

//app应用审核状态
type AppAuditStatus int64

const (
	AppAuditStatus_AAS_UNKNOWN  AppAuditStatus = 0
	AppAuditStatus_AAS_AUDITING AppAuditStatus = 1
	AppAuditStatus_AAS_APPROVED AppAuditStatus = 2
	AppAuditStatus_AAS_REJECTED AppAuditStatus = 3
	AppAuditStatus_AAS_TESTING  AppAuditStatus = 10
)

func (p AppAuditStatus) String() string {
	switch p {
	case AppAuditStatus_AAS_UNKNOWN:
		return "AppAuditStatus_AAS_UNKNOWN"
	case AppAuditStatus_AAS_AUDITING:
		return "AppAuditStatus_AAS_AUDITING"
	case AppAuditStatus_AAS_APPROVED:
		return "AppAuditStatus_AAS_APPROVED"
	case AppAuditStatus_AAS_REJECTED:
		return "AppAuditStatus_AAS_REJECTED"
	case AppAuditStatus_AAS_TESTING:
		return "AppAuditStatus_AAS_TESTING"
	}
	return "<UNSET>"
}

func AppAuditStatusFromString(s string) (AppAuditStatus, error) {
	switch s {
	case "AppAuditStatus_AAS_UNKNOWN":
		return AppAuditStatus_AAS_UNKNOWN, nil
	case "AppAuditStatus_AAS_AUDITING":
		return AppAuditStatus_AAS_AUDITING, nil
	case "AppAuditStatus_AAS_APPROVED":
		return AppAuditStatus_AAS_APPROVED, nil
	case "AppAuditStatus_AAS_REJECTED":
		return AppAuditStatus_AAS_REJECTED, nil
	case "AppAuditStatus_AAS_TESTING":
		return AppAuditStatus_AAS_TESTING, nil
	}
	return AppAuditStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AppAuditStatus string")
}

//app搜索需要查询的字段
type AppSearchField int64

const (
	AppSearchField_ASF_ID   AppSearchField = 0
	AppSearchField_ASF_NAME AppSearchField = 1
)

func (p AppSearchField) String() string {
	switch p {
	case AppSearchField_ASF_ID:
		return "AppSearchField_ASF_ID"
	case AppSearchField_ASF_NAME:
		return "AppSearchField_ASF_NAME"
	}
	return "<UNSET>"
}

func AppSearchFieldFromString(s string) (AppSearchField, error) {
	switch s {
	case "AppSearchField_ASF_ID":
		return AppSearchField_ASF_ID, nil
	case "AppSearchField_ASF_NAME":
		return AppSearchField_ASF_NAME, nil
	}
	return AppSearchField(math.MinInt32 - 1), fmt.Errorf("not a valid AppSearchField string")
}

//推广产品搜索需要查询的字段
type PromotionSearchField int64

const (
	PromotionSearchField_PSF_ID   PromotionSearchField = 0
	PromotionSearchField_PSF_NAME PromotionSearchField = 1
)

func (p PromotionSearchField) String() string {
	switch p {
	case PromotionSearchField_PSF_ID:
		return "PromotionSearchField_PSF_ID"
	case PromotionSearchField_PSF_NAME:
		return "PromotionSearchField_PSF_NAME"
	}
	return "<UNSET>"
}

func PromotionSearchFieldFromString(s string) (PromotionSearchField, error) {
	switch s {
	case "PromotionSearchField_PSF_ID":
		return PromotionSearchField_PSF_ID, nil
	case "PromotionSearchField_PSF_NAME":
		return PromotionSearchField_PSF_NAME, nil
	}
	return PromotionSearchField(math.MinInt32 - 1), fmt.Errorf("not a valid PromotionSearchField string")
}

//推广链接搜索需要查询的字段
type PromotionLinkSearchField int64

const (
	PromotionLinkSearchField_PLSF_ID   PromotionLinkSearchField = 0
	PromotionLinkSearchField_PLSF_NAME PromotionLinkSearchField = 1
)

func (p PromotionLinkSearchField) String() string {
	switch p {
	case PromotionLinkSearchField_PLSF_ID:
		return "PromotionLinkSearchField_PLSF_ID"
	case PromotionLinkSearchField_PLSF_NAME:
		return "PromotionLinkSearchField_PLSF_NAME"
	}
	return "<UNSET>"
}

func PromotionLinkSearchFieldFromString(s string) (PromotionLinkSearchField, error) {
	switch s {
	case "PromotionLinkSearchField_PLSF_ID":
		return PromotionLinkSearchField_PLSF_ID, nil
	case "PromotionLinkSearchField_PLSF_NAME":
		return PromotionLinkSearchField_PLSF_NAME, nil
	}
	return PromotionLinkSearchField(math.MinInt32 - 1), fmt.Errorf("not a valid PromotionLinkSearchField string")
}

//task搜索需要查询的字段
type TaskSearchField int64

const (
	TaskSearchField_TSF_ID   TaskSearchField = 0
	TaskSearchField_TSF_NAME TaskSearchField = 1
)

func (p TaskSearchField) String() string {
	switch p {
	case TaskSearchField_TSF_ID:
		return "TaskSearchField_TSF_ID"
	case TaskSearchField_TSF_NAME:
		return "TaskSearchField_TSF_NAME"
	}
	return "<UNSET>"
}

func TaskSearchFieldFromString(s string) (TaskSearchField, error) {
	switch s {
	case "TaskSearchField_TSF_ID":
		return TaskSearchField_TSF_ID, nil
	case "TaskSearchField_TSF_NAME":
		return TaskSearchField_TSF_NAME, nil
	}
	return TaskSearchField(math.MinInt32 - 1), fmt.Errorf("not a valid TaskSearchField string")
}

//推广组搜索需要查询的字段
type StrategySearchField int64

const (
	StrategySearchField_SSF_ID   StrategySearchField = 0
	StrategySearchField_SSF_NAME StrategySearchField = 1
)

func (p StrategySearchField) String() string {
	switch p {
	case StrategySearchField_SSF_ID:
		return "StrategySearchField_SSF_ID"
	case StrategySearchField_SSF_NAME:
		return "StrategySearchField_SSF_NAME"
	}
	return "<UNSET>"
}

func StrategySearchFieldFromString(s string) (StrategySearchField, error) {
	switch s {
	case "StrategySearchField_SSF_ID":
		return StrategySearchField_SSF_ID, nil
	case "StrategySearchField_SSF_NAME":
		return StrategySearchField_SSF_NAME, nil
	}
	return StrategySearchField(math.MinInt32 - 1), fmt.Errorf("not a valid StrategySearchField string")
}

//creative搜索需要查询的字段
type CreativeSearchField int64

const (
	CreativeSearchField_CSF_ID   CreativeSearchField = 0
	CreativeSearchField_CSF_NAME CreativeSearchField = 1
)

func (p CreativeSearchField) String() string {
	switch p {
	case CreativeSearchField_CSF_ID:
		return "CreativeSearchField_CSF_ID"
	case CreativeSearchField_CSF_NAME:
		return "CreativeSearchField_CSF_NAME"
	}
	return "<UNSET>"
}

func CreativeSearchFieldFromString(s string) (CreativeSearchField, error) {
	switch s {
	case "CreativeSearchField_CSF_ID":
		return CreativeSearchField_CSF_ID, nil
	case "CreativeSearchField_CSF_NAME":
		return CreativeSearchField_CSF_NAME, nil
	}
	return CreativeSearchField(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeSearchField string")
}

type App struct {
	Id          int32          `thrift:"id,1" json:"id"`
	Name        string         `thrift:"name,2" json:"name"`
	UserId      int32          `thrift:"userId,3" json:"userId"`
	LogoUrl     string         `thrift:"logoUrl,4" json:"logoUrl"`
	Platform    AppPlatform    `thrift:"platform,5" json:"platform"`
	PkgName     string         `thrift:"pkgName,6" json:"pkgName"`
	PublisherId string         `thrift:"publisherId,7" json:"publisherId"`
	AuditStatus AppAuditStatus `thrift:"auditStatus,8" json:"auditStatus"`
	OpenPush    int32          `thrift:"openPush,9" json:"openPush"`
	Reason      string         `thrift:"reason,10" json:"reason"`
	FgAllowed   int32          `thrift:"fgAllowed,11" json:"fgAllowed"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewApp() *App {
	return &App{
		Platform: math.MinInt32 - 1, // unset sentinal value

		AuditStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *App) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *App) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *App) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *App) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *App) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *App) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *App) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.LogoUrl = v
	}
	return nil
}

func (p *App) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Platform = AppPlatform(v)
	}
	return nil
}

func (p *App) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *App) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PublisherId = v
	}
	return nil
}

func (p *App) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AuditStatus = AppAuditStatus(v)
	}
	return nil
}

func (p *App) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.OpenPush = v
	}
	return nil
}

func (p *App) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Reason = v
	}
	return nil
}

func (p *App) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.FgAllowed = v
	}
	return nil
}

func (p *App) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *App) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *App) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *App) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("App"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *App) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *App) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *App) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:userId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:userId: %s", p, err)
	}
	return err
}

func (p *App) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logoUrl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:logoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LogoUrl)); err != nil {
		return fmt.Errorf("%T.logoUrl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:logoUrl: %s", p, err)
	}
	return err
}

func (p *App) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:platform: %s", p, err)
		}
	}
	return err
}

func (p *App) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgName", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pkgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkgName (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pkgName: %s", p, err)
	}
	return err
}

func (p *App) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publisherId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:publisherId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PublisherId)); err != nil {
		return fmt.Errorf("%T.publisherId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:publisherId: %s", p, err)
	}
	return err
}

func (p *App) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *App) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openPush", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:openPush: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OpenPush)); err != nil {
		return fmt.Errorf("%T.openPush (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:openPush: %s", p, err)
	}
	return err
}

func (p *App) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reason", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:reason: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Reason)); err != nil {
		return fmt.Errorf("%T.reason (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:reason: %s", p, err)
	}
	return err
}

func (p *App) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fgAllowed", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:fgAllowed: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FgAllowed)); err != nil {
		return fmt.Errorf("%T.fgAllowed (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:fgAllowed: %s", p, err)
	}
	return err
}

func (p *App) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *App) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *App) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *App) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("App(%+v)", *p)
}

type AppParams struct {
	Terms       map[AppSearchField]string `thrift:"terms,1" json:"terms"`
	AuditStatus AppAuditStatus            `thrift:"auditStatus,2" json:"auditStatus"`
	UserId      int32                     `thrift:"userId,3" json:"userId"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	Offset int32 `thrift:"offset,8" json:"offset"`
	Limit  int32 `thrift:"limit,9" json:"limit"`
}

func NewAppParams() *AppParams {
	return &AppParams{
		AuditStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppParams) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *AppParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[AppSearchField]string, size)
	for i := 0; i < size; i++ {
		var _key0 AppSearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = AppSearchField(v)
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.Terms[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AppParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AuditStatus = AppAuditStatus(v)
	}
	return nil
}

func (p *AppParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *AppParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AppParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AppParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *AppParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *AppParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:userId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:userId: %s", p, err)
	}
	return err
}

func (p *AppParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *AppParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *AppParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppParams(%+v)", *p)
}

type Promotion struct {
	Id          int32       `thrift:"id,1" json:"id"`
	Uid         int32       `thrift:"uid,2" json:"uid"`
	Name        string      `thrift:"name,3" json:"name"`
	PkgName     string      `thrift:"pkgName,4" json:"pkgName"`
	DisplayName string      `thrift:"displayName,5" json:"displayName"`
	Platform    AppPlatform `thrift:"platform,6" json:"platform"`
	LogoUrl     string      `thrift:"logoUrl,7" json:"logoUrl"`
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewPromotion() *Promotion {
	return &Promotion{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Promotion) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *Promotion) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Promotion) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Promotion) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Promotion) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Promotion) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *Promotion) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *Promotion) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Platform = AppPlatform(v)
	}
	return nil
}

func (p *Promotion) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.LogoUrl = v
	}
	return nil
}

func (p *Promotion) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Promotion) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Promotion) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Promotion) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Promotion"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Promotion) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pkgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkgName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pkgName: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayName", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:displayName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.displayName (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:displayName: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:platform: %s", p, err)
		}
	}
	return err
}

func (p *Promotion) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logoUrl", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:logoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LogoUrl)); err != nil {
		return fmt.Errorf("%T.logoUrl (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:logoUrl: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Promotion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Promotion(%+v)", *p)
}

type PromotionParams struct {
	Terms map[PromotionSearchField]string `thrift:"terms,1" json:"terms"`
	Uid   int32                           `thrift:"uid,2" json:"uid"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	Offset int32 `thrift:"offset,8" json:"offset"`
	Limit  int32 `thrift:"limit,9" json:"limit"`
}

func NewPromotionParams() *PromotionParams {
	return &PromotionParams{}
}

func (p *PromotionParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[PromotionSearchField]string, size)
	for i := 0; i < size; i++ {
		var _key2 PromotionSearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = PromotionSearchField(v)
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.Terms[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *PromotionParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *PromotionParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *PromotionParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *PromotionParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *PromotionParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *PromotionParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *PromotionParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *PromotionParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionParams(%+v)", *p)
}

type PromotionLink struct {
	Id          int32  `thrift:"id,1" json:"id"`
	Pid         int32  `thrift:"pid,2" json:"pid"`
	Uid         int32  `thrift:"uid,3" json:"uid"`
	Name        string `thrift:"name,4" json:"name"`
	ActionUrl   string `thrift:"actionUrl,5" json:"actionUrl"`
	FailSafeUrl string `thrift:"failSafeUrl,6" json:"failSafeUrl"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewPromotionLink() *PromotionLink {
	return &PromotionLink{}
}

func (p *PromotionLink) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionLink) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PromotionLink) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *PromotionLink) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *PromotionLink) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PromotionLink) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *PromotionLink) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FailSafeUrl = v
	}
	return nil
}

func (p *PromotionLink) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *PromotionLink) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *PromotionLink) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *PromotionLink) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionLink"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionLink) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:actionUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.actionUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:actionUrl: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("failSafeUrl", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:failSafeUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FailSafeUrl)); err != nil {
		return fmt.Errorf("%T.failSafeUrl (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:failSafeUrl: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *PromotionLink) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *PromotionLink) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionLink(%+v)", *p)
}

type PromotionLinkParams struct {
	Terms map[PromotionLinkSearchField]string `thrift:"terms,1" json:"terms"`
	Uid   int32                               `thrift:"uid,2" json:"uid"`
	Pids  []int32                             `thrift:"pids,3" json:"pids"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	Offset int32 `thrift:"offset,8" json:"offset"`
	Limit  int32 `thrift:"limit,9" json:"limit"`
}

func NewPromotionLinkParams() *PromotionLinkParams {
	return &PromotionLinkParams{}
}

func (p *PromotionLinkParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionLinkParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[PromotionLinkSearchField]string, size)
	for i := 0; i < size; i++ {
		var _key4 PromotionLinkSearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key4 = PromotionLinkSearchField(v)
		}
		var _val5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val5 = v
		}
		p.Terms[_key4] = _val5
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *PromotionLinkParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *PromotionLinkParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Pids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.Pids = append(p.Pids, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PromotionLinkParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *PromotionLinkParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *PromotionLinkParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionLinkParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionLinkParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *PromotionLinkParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *PromotionLinkParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Pids != nil {
		if err := oprot.WriteFieldBegin("pids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:pids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Pids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Pids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:pids: %s", p, err)
		}
	}
	return err
}

func (p *PromotionLinkParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *PromotionLinkParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *PromotionLinkParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionLinkParams(%+v)", *p)
}

type Task struct {
	Id                  int32       `thrift:"id,1" json:"id"`
	Pid                 int32       `thrift:"pid,2" json:"pid"`
	Uid                 int32       `thrift:"uid,3" json:"uid"`
	Name                string      `thrift:"name,4" json:"name"`
	DailyPushLimit      int32       `thrift:"dailyPushLimit,5" json:"dailyPushLimit"`
	StartTime           int64       `thrift:"startTime,6" json:"startTime"`
	EndTime             int64       `thrift:"endTime,7" json:"endTime"`
	Paused              int32       `thrift:"paused,8" json:"paused"`
	ServiceType         ServiceType `thrift:"serviceType,9" json:"serviceType"`
	TotalBudget         int64       `thrift:"totalBudget,10" json:"totalBudget"`
	TotalBudgetOverTime int64       `thrift:"totalBudgetOverTime,11" json:"totalBudgetOverTime"`
	DailyBudget         int64       `thrift:"dailyBudget,12" json:"dailyBudget"`
	DailyBudgetOverTime int64       `thrift:"dailyBudgetOverTime,13" json:"dailyBudgetOverTime"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewTask() *Task {
	return &Task{
		ServiceType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Task) IsSetServiceType() bool {
	return int64(p.ServiceType) != math.MinInt32-1
}

func (p *Task) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Task) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Task) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *Task) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Task) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Task) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DailyPushLimit = v
	}
	return nil
}

func (p *Task) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *Task) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *Task) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *Task) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ServiceType = ServiceType(v)
	}
	return nil
}

func (p *Task) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *Task) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TotalBudgetOverTime = v
	}
	return nil
}

func (p *Task) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *Task) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.DailyBudgetOverTime = v
	}
	return nil
}

func (p *Task) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Task) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Task) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Task) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Task"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Task) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Task) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *Task) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *Task) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Task) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyPushLimit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:dailyPushLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DailyPushLimit)); err != nil {
		return fmt.Errorf("%T.dailyPushLimit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:dailyPushLimit: %s", p, err)
	}
	return err
}

func (p *Task) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:startTime: %s", p, err)
	}
	return err
}

func (p *Task) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:endTime: %s", p, err)
	}
	return err
}

func (p *Task) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:paused: %s", p, err)
	}
	return err
}

func (p *Task) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetServiceType() {
		if err := oprot.WriteFieldBegin("serviceType", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:serviceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ServiceType)); err != nil {
			return fmt.Errorf("%T.serviceType (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:serviceType: %s", p, err)
		}
	}
	return err
}

func (p *Task) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:totalBudget: %s", p, err)
	}
	return err
}

func (p *Task) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudgetOverTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:totalBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.totalBudgetOverTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:totalBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *Task) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudget", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:dailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.dailyBudget (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:dailyBudget: %s", p, err)
	}
	return err
}

func (p *Task) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudgetOverTime", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:dailyBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.dailyBudgetOverTime (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:dailyBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *Task) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *Task) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *Task) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Task) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Task(%+v)", *p)
}

type TaskParams struct {
	Terms     map[TaskSearchField]string `thrift:"terms,1" json:"terms"`
	Uid       int32                      `thrift:"uid,2" json:"uid"`
	Pids      []int32                    `thrift:"pids,3" json:"pids"`
	StartTime int64                      `thrift:"startTime,4" json:"startTime"`
	EndTime   int64                      `thrift:"endTime,5" json:"endTime"`
	// unused field # 6
	// unused field # 7
	Offset int32 `thrift:"offset,8" json:"offset"`
	Limit  int32 `thrift:"limit,9" json:"limit"`
}

func NewTaskParams() *TaskParams {
	return &TaskParams{}
}

func (p *TaskParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[TaskSearchField]string, size)
	for i := 0; i < size; i++ {
		var _key7 TaskSearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key7 = TaskSearchField(v)
		}
		var _val8 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val8 = v
		}
		p.Terms[_key7] = _val8
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *TaskParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *TaskParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Pids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.Pids = append(p.Pids, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TaskParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *TaskParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *TaskParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *TaskParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *TaskParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *TaskParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *TaskParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Pids != nil {
		if err := oprot.WriteFieldBegin("pids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:pids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Pids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Pids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:pids: %s", p, err)
		}
	}
	return err
}

func (p *TaskParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startTime: %s", p, err)
	}
	return err
}

func (p *TaskParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:endTime: %s", p, err)
	}
	return err
}

func (p *TaskParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *TaskParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *TaskParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskParams(%+v)", *p)
}

type Strategy struct {
	Id                  int32          `thrift:"id,1" json:"id"`
	Tid                 int32          `thrift:"tid,2" json:"tid"`
	Uid                 int32          `thrift:"uid,3" json:"uid"`
	Pid                 int32          `thrift:"pid,4" json:"pid"`
	Plid                int32          `thrift:"plid,5" json:"plid"`
	Name                string         `thrift:"name,6" json:"name"`
	DailyPushLimit      int32          `thrift:"dailyPushLimit,7" json:"dailyPushLimit"`
	ProfitRate          int32          `thrift:"profitRate,8" json:"profitRate"`
	InstalledUserFilter int32          `thrift:"installedUserFilter,9" json:"installedUserFilter"`
	HourTarget          []int32        `thrift:"hourTarget,10" json:"hourTarget"`
	WeekDayTarget       []int32        `thrift:"weekDayTarget,11" json:"weekDayTarget"`
	GeoCityTarget       []int32        `thrift:"geoCityTarget,12" json:"geoCityTarget"`
	CrowdWhiteTarget    []int32        `thrift:"crowdWhiteTarget,13" json:"crowdWhiteTarget"`
	CrowdBlackTarget    []int32        `thrift:"crowdBlackTarget,14" json:"crowdBlackTarget"`
	AppTarget           []int32        `thrift:"appTarget,15" json:"appTarget"`
	Paused              int32          `thrift:"paused,16" json:"paused"`
	CostType            enums.CostType `thrift:"costType,17" json:"costType"`
	Price               int64          `thrift:"price,18" json:"price"`
	DailyBudget         int64          `thrift:"dailyBudget,19" json:"dailyBudget"`
	DailyBudgetOverTime int64          `thrift:"dailyBudgetOverTime,20" json:"dailyBudgetOverTime"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewStrategy() *Strategy {
	return &Strategy{
		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Strategy) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *Strategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Strategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Strategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Tid = v
	}
	return nil
}

func (p *Strategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Strategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *Strategy) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Plid = v
	}
	return nil
}

func (p *Strategy) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Strategy) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.DailyPushLimit = v
	}
	return nil
}

func (p *Strategy) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ProfitRate = v
	}
	return nil
}

func (p *Strategy) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.InstalledUserFilter = v
	}
	return nil
}

func (p *Strategy) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.HourTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.HourTarget = append(p.HourTarget, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WeekDayTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.WeekDayTarget = append(p.WeekDayTarget, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoCityTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.GeoCityTarget = append(p.GeoCityTarget, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CrowdWhiteTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.CrowdWhiteTarget = append(p.CrowdWhiteTarget, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CrowdBlackTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.CrowdBlackTarget = append(p.CrowdBlackTarget, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AppTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = v
		}
		p.AppTarget = append(p.AppTarget, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *Strategy) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.CostType = enums.CostType(v)
	}
	return nil
}

func (p *Strategy) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *Strategy) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *Strategy) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.DailyBudgetOverTime = v
	}
	return nil
}

func (p *Strategy) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Strategy) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Strategy) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Strategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Strategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Strategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Tid)); err != nil {
		return fmt.Errorf("%T.tid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tid: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pid: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:plid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Plid)); err != nil {
		return fmt.Errorf("%T.plid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:plid: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:name: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyPushLimit", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:dailyPushLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DailyPushLimit)); err != nil {
		return fmt.Errorf("%T.dailyPushLimit (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:dailyPushLimit: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("profitRate", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:profitRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProfitRate)); err != nil {
		return fmt.Errorf("%T.profitRate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:profitRate: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("installedUserFilter", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:installedUserFilter: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.InstalledUserFilter)); err != nil {
		return fmt.Errorf("%T.installedUserFilter (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:installedUserFilter: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField10(oprot thrift.TProtocol) (err error) {
	if p.HourTarget != nil {
		if err := oprot.WriteFieldBegin("hourTarget", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:hourTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.HourTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.HourTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:hourTarget: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField11(oprot thrift.TProtocol) (err error) {
	if p.WeekDayTarget != nil {
		if err := oprot.WriteFieldBegin("weekDayTarget", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:weekDayTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.WeekDayTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WeekDayTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:weekDayTarget: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField12(oprot thrift.TProtocol) (err error) {
	if p.GeoCityTarget != nil {
		if err := oprot.WriteFieldBegin("geoCityTarget", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:geoCityTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GeoCityTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoCityTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:geoCityTarget: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField13(oprot thrift.TProtocol) (err error) {
	if p.CrowdWhiteTarget != nil {
		if err := oprot.WriteFieldBegin("crowdWhiteTarget", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:crowdWhiteTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CrowdWhiteTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CrowdWhiteTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:crowdWhiteTarget: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField14(oprot thrift.TProtocol) (err error) {
	if p.CrowdBlackTarget != nil {
		if err := oprot.WriteFieldBegin("crowdBlackTarget", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:crowdBlackTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CrowdBlackTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CrowdBlackTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:crowdBlackTarget: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField15(oprot thrift.TProtocol) (err error) {
	if p.AppTarget != nil {
		if err := oprot.WriteFieldBegin("appTarget", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:appTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AppTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AppTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:appTarget: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:paused: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetCostType() {
		if err := oprot.WriteFieldBegin("costType", thrift.I32, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:costType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CostType)); err != nil {
			return fmt.Errorf("%T.costType (17) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:costType: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:price: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudget", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:dailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.dailyBudget (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:dailyBudget: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudgetOverTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:dailyBudgetOverTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudgetOverTime)); err != nil {
		return fmt.Errorf("%T.dailyBudgetOverTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:dailyBudgetOverTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Strategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Strategy(%+v)", *p)
}

type StrategyParams struct {
	Terms     map[StrategySearchField]string `thrift:"terms,1" json:"terms"`
	Uid       int32                          `thrift:"uid,2" json:"uid"`
	Tids      []int32                        `thrift:"tids,3" json:"tids"`
	Plids     []int32                        `thrift:"plids,4" json:"plids"`
	StartTime int64                          `thrift:"startTime,5" json:"startTime"`
	EndTime   int64                          `thrift:"endTime,6" json:"endTime"`
	// unused field # 7
	Offset int32 `thrift:"offset,8" json:"offset"`
	Limit  int32 `thrift:"limit,9" json:"limit"`
}

func NewStrategyParams() *StrategyParams {
	return &StrategyParams{}
}

func (p *StrategyParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StrategyParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[StrategySearchField]string, size)
	for i := 0; i < size; i++ {
		var _key16 StrategySearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key16 = StrategySearchField(v)
		}
		var _val17 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val17 = v
		}
		p.Terms[_key16] = _val17
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *StrategyParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = v
		}
		p.Tids = append(p.Tids, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Plids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem19 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem19 = v
		}
		p.Plids = append(p.Plids, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *StrategyParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *StrategyParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *StrategyParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *StrategyParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StrategyParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StrategyParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Tids != nil {
		if err := oprot.WriteFieldBegin("tids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:tids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Tids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:tids: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Plids != nil {
		if err := oprot.WriteFieldBegin("plids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:plids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Plids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Plids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:plids: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startTime: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endTime: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *StrategyParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrategyParams(%+v)", *p)
}

type Material struct {
	Url    string `thrift:"url,1" json:"url"`
	Width  int32  `thrift:"width,2" json:"width"`
	Height int32  `thrift:"height,3" json:"height"`
	TypeA1 string `thrift:"type,4" json:"type"`
}

func NewMaterial() *Material {
	return &Material{}
}

func (p *Material) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Material) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Material) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *Material) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *Material) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *Material) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Material"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Material) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:url: %s", p, err)
	}
	return err
}

func (p *Material) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:width: %s", p, err)
	}
	return err
}

func (p *Material) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:height: %s", p, err)
	}
	return err
}

func (p *Material) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *Material) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Material(%+v)", *p)
}

type Creative struct {
	Id          int32       `thrift:"id,1" json:"id"`
	Tid         int32       `thrift:"tid,2" json:"tid"`
	Sid         int32       `thrift:"sid,3" json:"sid"`
	Uid         int32       `thrift:"uid,4" json:"uid"`
	Name        string      `thrift:"name,5" json:"name"`
	Title       string      `thrift:"title,6" json:"title"`
	Description string      `thrift:"description,7" json:"description"`
	Paused      int32       `thrift:"paused,8" json:"paused"`
	AdMatchType int64       `thrift:"adMatchType,9" json:"adMatchType"`
	Urls        []*Material `thrift:"urls,10" json:"urls"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewCreative() *Creative {
	return &Creative{}
}

func (p *Creative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Creative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Creative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Tid = v
	}
	return nil
}

func (p *Creative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *Creative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Creative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Creative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *Creative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Creative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *Creative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *Creative) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Urls = make([]*Material, 0, size)
	for i := 0; i < size; i++ {
		_elem20 := NewMaterial()
		if err := _elem20.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem20)
		}
		p.Urls = append(p.Urls, _elem20)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Creative) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Creative) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Creative) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Creative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Creative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Creative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Creative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Tid)); err != nil {
		return fmt.Errorf("%T.tid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tid: %s", p, err)
	}
	return err
}

func (p *Creative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sid: %s", p, err)
	}
	return err
}

func (p *Creative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:uid: %s", p, err)
	}
	return err
}

func (p *Creative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *Creative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:title: %s", p, err)
	}
	return err
}

func (p *Creative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:description: %s", p, err)
	}
	return err
}

func (p *Creative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:paused: %s", p, err)
	}
	return err
}

func (p *Creative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adMatchType", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:adMatchType: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.adMatchType (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:adMatchType: %s", p, err)
	}
	return err
}

func (p *Creative) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Urls != nil {
		if err := oprot.WriteFieldBegin("urls", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:urls: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Urls)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Urls {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:urls: %s", p, err)
		}
	}
	return err
}

func (p *Creative) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *Creative) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *Creative) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Creative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Creative(%+v)", *p)
}

type CreativeParams struct {
	Terms     map[CreativeSearchField]string `thrift:"terms,1" json:"terms"`
	Uid       int32                          `thrift:"uid,2" json:"uid"`
	Sids      []int32                        `thrift:"sids,3" json:"sids"`
	StartTime int64                          `thrift:"startTime,4" json:"startTime"`
	EndTime   int64                          `thrift:"endTime,5" json:"endTime"`
	// unused field # 6
	// unused field # 7
	Offset int32 `thrift:"offset,8" json:"offset"`
	Limit  int32 `thrift:"limit,9" json:"limit"`
}

func NewCreativeParams() *CreativeParams {
	return &CreativeParams{}
}

func (p *CreativeParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[CreativeSearchField]string, size)
	for i := 0; i < size; i++ {
		var _key21 CreativeSearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key21 = CreativeSearchField(v)
		}
		var _val22 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val22 = v
		}
		p.Terms[_key21] = _val22
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CreativeParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *CreativeParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Sids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem23 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem23 = v
		}
		p.Sids = append(p.Sids, _elem23)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *CreativeParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *CreativeParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *CreativeParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *CreativeParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *CreativeParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Sids != nil {
		if err := oprot.WriteFieldBegin("sids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Sids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Sids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sids: %s", p, err)
		}
	}
	return err
}

func (p *CreativeParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startTime: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:endTime: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *CreativeParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeParams(%+v)", *p)
}
