// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package passport

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/passport_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = passport_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//UserException中可能出现的异常代码
type UserServiceCode int64

const (
	UserServiceCode_ERROR_USER_NOT_EXIST          UserServiceCode = 20301
	UserServiceCode_ERROR_USER_ALREADY_EXIST      UserServiceCode = 20302
	UserServiceCode_ERROR_USER_NOT_ACTIVATED      UserServiceCode = 20303
	UserServiceCode_ERROR_USER_ALREADY_ACTIVATED  UserServiceCode = 20304
	UserServiceCode_ERROR_USER_PASSWORD_INCORRECT UserServiceCode = 20305
	UserServiceCode_ERROR_USER_ROLE_INCORRECT     UserServiceCode = 20306
	UserServiceCode_ERROR_USER_ADER_ROLE_BANNED   UserServiceCode = 20307
	UserServiceCode_ERROR_USER_DEVER_ROLE_BANNED  UserServiceCode = 20308
	UserServiceCode_ERROR_USER_BANNED             UserServiceCode = 20309
	UserServiceCode_ERROR_USER_PARAM_INVALID      UserServiceCode = 20401
	UserServiceCode_ERROR_USER_SYSTEM_ERROR       UserServiceCode = 20501
)

func (p UserServiceCode) String() string {
	switch p {
	case UserServiceCode_ERROR_USER_NOT_EXIST:
		return "UserServiceCode_ERROR_USER_NOT_EXIST"
	case UserServiceCode_ERROR_USER_ALREADY_EXIST:
		return "UserServiceCode_ERROR_USER_ALREADY_EXIST"
	case UserServiceCode_ERROR_USER_NOT_ACTIVATED:
		return "UserServiceCode_ERROR_USER_NOT_ACTIVATED"
	case UserServiceCode_ERROR_USER_ALREADY_ACTIVATED:
		return "UserServiceCode_ERROR_USER_ALREADY_ACTIVATED"
	case UserServiceCode_ERROR_USER_PASSWORD_INCORRECT:
		return "UserServiceCode_ERROR_USER_PASSWORD_INCORRECT"
	case UserServiceCode_ERROR_USER_ROLE_INCORRECT:
		return "UserServiceCode_ERROR_USER_ROLE_INCORRECT"
	case UserServiceCode_ERROR_USER_ADER_ROLE_BANNED:
		return "UserServiceCode_ERROR_USER_ADER_ROLE_BANNED"
	case UserServiceCode_ERROR_USER_DEVER_ROLE_BANNED:
		return "UserServiceCode_ERROR_USER_DEVER_ROLE_BANNED"
	case UserServiceCode_ERROR_USER_BANNED:
		return "UserServiceCode_ERROR_USER_BANNED"
	case UserServiceCode_ERROR_USER_PARAM_INVALID:
		return "UserServiceCode_ERROR_USER_PARAM_INVALID"
	case UserServiceCode_ERROR_USER_SYSTEM_ERROR:
		return "UserServiceCode_ERROR_USER_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func UserServiceCodeFromString(s string) (UserServiceCode, error) {
	switch s {
	case "UserServiceCode_ERROR_USER_NOT_EXIST":
		return UserServiceCode_ERROR_USER_NOT_EXIST, nil
	case "UserServiceCode_ERROR_USER_ALREADY_EXIST":
		return UserServiceCode_ERROR_USER_ALREADY_EXIST, nil
	case "UserServiceCode_ERROR_USER_NOT_ACTIVATED":
		return UserServiceCode_ERROR_USER_NOT_ACTIVATED, nil
	case "UserServiceCode_ERROR_USER_ALREADY_ACTIVATED":
		return UserServiceCode_ERROR_USER_ALREADY_ACTIVATED, nil
	case "UserServiceCode_ERROR_USER_PASSWORD_INCORRECT":
		return UserServiceCode_ERROR_USER_PASSWORD_INCORRECT, nil
	case "UserServiceCode_ERROR_USER_ROLE_INCORRECT":
		return UserServiceCode_ERROR_USER_ROLE_INCORRECT, nil
	case "UserServiceCode_ERROR_USER_ADER_ROLE_BANNED":
		return UserServiceCode_ERROR_USER_ADER_ROLE_BANNED, nil
	case "UserServiceCode_ERROR_USER_DEVER_ROLE_BANNED":
		return UserServiceCode_ERROR_USER_DEVER_ROLE_BANNED, nil
	case "UserServiceCode_ERROR_USER_BANNED":
		return UserServiceCode_ERROR_USER_BANNED, nil
	case "UserServiceCode_ERROR_USER_PARAM_INVALID":
		return UserServiceCode_ERROR_USER_PARAM_INVALID, nil
	case "UserServiceCode_ERROR_USER_SYSTEM_ERROR":
		return UserServiceCode_ERROR_USER_SYSTEM_ERROR, nil
	}
	return UserServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid UserServiceCode string")
}

type UserException struct {
	Code    UserServiceCode `thrift:"code,1" json:"code"`
	Message string          `thrift:"message,2" json:"message"`
}

func NewUserException() *UserException {
	return &UserException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UserException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *UserException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = UserServiceCode(v)
	}
	return nil
}

func (p *UserException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *UserException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *UserException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *UserException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserException(%+v)", *p)
}
