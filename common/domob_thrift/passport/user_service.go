// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package passport

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/passport_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = passport_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__

type UserService interface { //用户服务接口

	// 注册用户
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - RegInfo: 用户注册提交信息结构体
	RegisterUser(header *common.RequestHeader, regInfo *passport_types.UserRegInfo) (r int32, ue *UserException, err error)
	// 判断email账户是否可以注册
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 用户Email
	IsUserEmailAvailable(header *common.RequestHeader, email string) (r bool, ue *UserException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	ActivateUser(header *common.RequestHeader, uid int32) (ue *UserException, err error)
	// 根据邮箱账户名和密码验证用户密码
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 用户Email
	//  - Password: 用户密码
	VerifyUserPassword(header *common.RequestHeader, email string, password string) (ue *UserException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - NewPassword: 用户新密码
	//  - OldPassword: 用户旧密码
	ChangeUserPassword(header *common.RequestHeader, uid int32, newPassword string, oldPassword string) (ue *UserException, err error)
	// 重置用户密码
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - NewPassword: 用户新密码
	ResetPassword(header *common.RequestHeader, uid int32, newPassword string) (ue *UserException, err error)
	// 添加用户角色
	// @Deprecated，use changeUserRole instead
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - UserRole: 用户角色
	AddRoleToUser(header *common.RequestHeader, uid int32, userRole enums.UserRole) (ue *UserException, err error)
	// 更改用户角色
	// 用户新角色必须多于旧角色，只有既是广告主又是开发者的用户才能成为流量互换用户
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - UserRole: 用户新角色
	ChangeUserRole(header *common.RequestHeader, uid int32, userRole enums.UserRole) (ue *UserException, err error)
	// 修改用户作为广告者的信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - AderProfile: 广告主角色信息
	ChangeAderProfile(header *common.RequestHeader, aderProfile *passport_types.UserAderProfile) (ue *UserException, err error)
	// 修改用户作为开发者的信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - DeverProfile: 开发者角色信息
	ChangeDeverProfile(header *common.RequestHeader, deverProfile *passport_types.UserDeverProfile) (ue *UserException, err error)
	// 修改用户的扩展信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ExtProfile: 扩展信息
	ChangeExtProfile(header *common.RequestHeader, extProfile *passport_types.UserExtProfile) (ue *UserException, err error)
	// 根据uid获取用户信息，未激活用户也会被返回
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	GetUserById(header *common.RequestHeader, uid int32) (r *passport_types.User, ue *UserException, err error)
	// 根据email获取用户信息，未激活用户也会被返回
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 用户Email
	GetUserByEmail(header *common.RequestHeader, email string) (r *passport_types.User, ue *UserException, err error)
	// 根据用户注册时间批量获取用户列表（不返回未激活用户）
	// 按用户注册时间、uid进行排序，升序或降序由ascending指定
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - StartTime: 查询开始时间
	//  - EndTime: 查询结束时间
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListUidsByRegTime(header *common.RequestHeader, startTime int64, endTime int64, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error)
	// 根据用户状态获取用户列表
	// 按用户注册时间、uid进行排序，升序或降序由ascending指定
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Status: 用户状态
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListUidsByStatus(header *common.RequestHeader, status passport_types.UserStatus, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error)
	// 根据用户类型获取正常用户列表（不返回未激活用户），只返回对应角色的用户，不做角色包含判断
	// 按用户注册时间、uid进行排序，升序或降序由ascending指定
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - UserRole: 用户角色
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListUidsByUserRole(header *common.RequestHeader, userRole enums.UserRole, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error)
	// 根据用户被禁封类型获取用户列表（不返回未激活用户）
	// 按用户注册时间、uid进行排序，升序或降序由ascending指定
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - AdminBanStatus: 管理后台禁封状态
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListUidsByAdminBanStatus(header *common.RequestHeader, adminBanStatus passport_types.AdminBanStatus, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error)
	// 根据用户标记获取用户列表（包括未激活用户）
	// 按用户注册时间进行排序，升序或降序由ascending指定
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Key: 查找键名
	//  - Value: 查找键值
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListUidsByExtInfo(header *common.RequestHeader, key string, value string, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error)
	// 管理后台根据uids批量获取用户元信息（包括未激活用户）
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uids: 用户编号列表
	GetUserMetaByUids(header *common.RequestHeader, uids []int32) (r map[int32]*passport_types.UserMeta, ue *UserException, err error)
	// 根据ids获取用户信息，未激活用户也会被返回
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 用户编号
	GetUsersByIds(header *common.RequestHeader, ids []int32) (r map[int32]*passport_types.User, ue *UserException, err error)
	// 管理后台根据uids批量封禁用户
	// 可以对未激活用户进行封禁操作
	// 重复封禁不报错
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uids: 用户编号列表
	AdminBanUser(header *common.RequestHeader, uids []int32) (ue *UserException, err error)
	// 管理后台根据uids批量解禁用户
	// 可以对未激活用户进行解禁操作
	// 相应角色本未被封禁时不报错
	// 仅限管理后台调用
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uids: 用户编号列表
	AdminUnbanUser(header *common.RequestHeader, uids []int32) (ue *UserException, err error)
	// 根据代理广告主UID获取所管理的被代理广告主UID列表（包括未激活用户）
	// 按用户注册时间、uid进行排序，升序或降序由ascending指定
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - AgentUid: 代理广告主UID
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListManagedUidsByAgentUid(header *common.RequestHeader, agentUid int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error)
	// 更改用户备注名
	// remark不允许是null，可以为空串，表示使用默认备注名
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - Remark: 用户备注名
	ChangeUserRemark(header *common.RequestHeader, uid int32, remark string) (ue *UserException, err error)
	// 更改用户注册email
	// uid和oldEmail对应关系必须正确，oldEmail必须存在，newEmail必须不存在并合法
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - OldEmail: 用户旧Email
	//  - NewEmail: 用户新Email
	ChangeUserEmail(header *common.RequestHeader, uid int32, oldEmail string, newEmail string) (ue *UserException, err error)
	// 更改用户的广告主类型
	// 用户必须是广告主：普通、综合、被代理的广告主
	// 有以下限制：
	//  1. 已经是品牌广告主的，不允许标记为其它类型
	//  2. 非效果广告主可以更改为其它类型
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - SponsorType: 新广告主类型
	ChangeSponsorType(header *common.RequestHeader, uid int32, sponsorType enums.SponsorType) (ue *UserException, err error)
	// 按key更新user的扩展信息
	// 注意，这里的更新实际上是插入或更新，不删除没有提供的key
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - ExtInfo: 扩展信息，仅更新和扩充，不删除没有提供的key
	UpdateUserExtInfo(header *common.RequestHeader, uid int32, extInfo map[string]string) (ue *UserException, err error)
	// 按key删除user的扩展信息
	// 注意，只删除提供了的key
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Uid: 用户编号
	//  - Keys: 要删除的扩展信息key，只删除提供了的key
	DeleteUserExtInfo(header *common.RequestHeader, uid int32, keys map[string]bool) (ue *UserException, err error)
}

//用户服务接口
type UserServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewUserServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UserServiceClient {
	return &UserServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewUserServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UserServiceClient {
	return &UserServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 注册用户
//
// Parameters:
//  - Header: 请求消息头结构体
//  - RegInfo: 用户注册提交信息结构体
func (p *UserServiceClient) RegisterUser(header *common.RequestHeader, regInfo *passport_types.UserRegInfo) (r int32, ue *UserException, err error) {
	if err = p.sendRegisterUser(header, regInfo); err != nil {
		return
	}
	return p.recvRegisterUser()
}

func (p *UserServiceClient) sendRegisterUser(header *common.RequestHeader, regInfo *passport_types.UserRegInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("registerUser", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewRegisterUserArgs()
	args0.Header = header
	args0.RegInfo = regInfo
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvRegisterUser() (value int32, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewRegisterUserResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Ue != nil {
		ue = result1.Ue
	}
	return
}

// 判断email账户是否可以注册
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 用户Email
func (p *UserServiceClient) IsUserEmailAvailable(header *common.RequestHeader, email string) (r bool, ue *UserException, err error) {
	if err = p.sendIsUserEmailAvailable(header, email); err != nil {
		return
	}
	return p.recvIsUserEmailAvailable()
}

func (p *UserServiceClient) sendIsUserEmailAvailable(header *common.RequestHeader, email string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("isUserEmailAvailable", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewIsUserEmailAvailableArgs()
	args4.Header = header
	args4.Email = email
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvIsUserEmailAvailable() (value bool, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewIsUserEmailAvailableResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Ue != nil {
		ue = result5.Ue
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
func (p *UserServiceClient) ActivateUser(header *common.RequestHeader, uid int32) (ue *UserException, err error) {
	if err = p.sendActivateUser(header, uid); err != nil {
		return
	}
	return p.recvActivateUser()
}

func (p *UserServiceClient) sendActivateUser(header *common.RequestHeader, uid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("activateUser", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewActivateUserArgs()
	args8.Header = header
	args8.Uid = uid
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvActivateUser() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewActivateUserResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result9.Ue != nil {
		ue = result9.Ue
	}
	return
}

// 根据邮箱账户名和密码验证用户密码
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 用户Email
//  - Password: 用户密码
func (p *UserServiceClient) VerifyUserPassword(header *common.RequestHeader, email string, password string) (ue *UserException, err error) {
	if err = p.sendVerifyUserPassword(header, email, password); err != nil {
		return
	}
	return p.recvVerifyUserPassword()
}

func (p *UserServiceClient) sendVerifyUserPassword(header *common.RequestHeader, email string, password string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("verifyUserPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewVerifyUserPasswordArgs()
	args12.Header = header
	args12.Email = email
	args12.Password = password
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvVerifyUserPassword() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewVerifyUserPasswordResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result13.Ue != nil {
		ue = result13.Ue
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - NewPassword: 用户新密码
//  - OldPassword: 用户旧密码
func (p *UserServiceClient) ChangeUserPassword(header *common.RequestHeader, uid int32, newPassword string, oldPassword string) (ue *UserException, err error) {
	if err = p.sendChangeUserPassword(header, uid, newPassword, oldPassword); err != nil {
		return
	}
	return p.recvChangeUserPassword()
}

func (p *UserServiceClient) sendChangeUserPassword(header *common.RequestHeader, uid int32, newPassword string, oldPassword string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeUserPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewChangeUserPasswordArgs()
	args16.Header = header
	args16.Uid = uid
	args16.NewPassword = newPassword
	args16.OldPassword = oldPassword
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeUserPassword() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewChangeUserPasswordResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result17.Ue != nil {
		ue = result17.Ue
	}
	return
}

// 重置用户密码
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - NewPassword: 用户新密码
func (p *UserServiceClient) ResetPassword(header *common.RequestHeader, uid int32, newPassword string) (ue *UserException, err error) {
	if err = p.sendResetPassword(header, uid, newPassword); err != nil {
		return
	}
	return p.recvResetPassword()
}

func (p *UserServiceClient) sendResetPassword(header *common.RequestHeader, uid int32, newPassword string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resetPassword", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewResetPasswordArgs()
	args20.Header = header
	args20.Uid = uid
	args20.NewPassword = newPassword
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvResetPassword() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewResetPasswordResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result21.Ue != nil {
		ue = result21.Ue
	}
	return
}

// 添加用户角色
// @Deprecated，use changeUserRole instead
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - UserRole: 用户角色
func (p *UserServiceClient) AddRoleToUser(header *common.RequestHeader, uid int32, userRole enums.UserRole) (ue *UserException, err error) {
	if err = p.sendAddRoleToUser(header, uid, userRole); err != nil {
		return
	}
	return p.recvAddRoleToUser()
}

func (p *UserServiceClient) sendAddRoleToUser(header *common.RequestHeader, uid int32, userRole enums.UserRole) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addRoleToUser", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewAddRoleToUserArgs()
	args24.Header = header
	args24.Uid = uid
	args24.UserRole = userRole
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvAddRoleToUser() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewAddRoleToUserResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result25.Ue != nil {
		ue = result25.Ue
	}
	return
}

// 更改用户角色
// 用户新角色必须多于旧角色，只有既是广告主又是开发者的用户才能成为流量互换用户
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - UserRole: 用户新角色
func (p *UserServiceClient) ChangeUserRole(header *common.RequestHeader, uid int32, userRole enums.UserRole) (ue *UserException, err error) {
	if err = p.sendChangeUserRole(header, uid, userRole); err != nil {
		return
	}
	return p.recvChangeUserRole()
}

func (p *UserServiceClient) sendChangeUserRole(header *common.RequestHeader, uid int32, userRole enums.UserRole) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeUserRole", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewChangeUserRoleArgs()
	args28.Header = header
	args28.Uid = uid
	args28.UserRole = userRole
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeUserRole() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewChangeUserRoleResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result29.Ue != nil {
		ue = result29.Ue
	}
	return
}

// 修改用户作为广告者的信息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - AderProfile: 广告主角色信息
func (p *UserServiceClient) ChangeAderProfile(header *common.RequestHeader, aderProfile *passport_types.UserAderProfile) (ue *UserException, err error) {
	if err = p.sendChangeAderProfile(header, aderProfile); err != nil {
		return
	}
	return p.recvChangeAderProfile()
}

func (p *UserServiceClient) sendChangeAderProfile(header *common.RequestHeader, aderProfile *passport_types.UserAderProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeAderProfile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewChangeAderProfileArgs()
	args32.Header = header
	args32.AderProfile = aderProfile
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeAderProfile() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewChangeAderProfileResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result33.Ue != nil {
		ue = result33.Ue
	}
	return
}

// 修改用户作为开发者的信息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - DeverProfile: 开发者角色信息
func (p *UserServiceClient) ChangeDeverProfile(header *common.RequestHeader, deverProfile *passport_types.UserDeverProfile) (ue *UserException, err error) {
	if err = p.sendChangeDeverProfile(header, deverProfile); err != nil {
		return
	}
	return p.recvChangeDeverProfile()
}

func (p *UserServiceClient) sendChangeDeverProfile(header *common.RequestHeader, deverProfile *passport_types.UserDeverProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeDeverProfile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewChangeDeverProfileArgs()
	args36.Header = header
	args36.DeverProfile = deverProfile
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeDeverProfile() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewChangeDeverProfileResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result37.Ue != nil {
		ue = result37.Ue
	}
	return
}

// 修改用户的扩展信息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - ExtProfile: 扩展信息
func (p *UserServiceClient) ChangeExtProfile(header *common.RequestHeader, extProfile *passport_types.UserExtProfile) (ue *UserException, err error) {
	if err = p.sendChangeExtProfile(header, extProfile); err != nil {
		return
	}
	return p.recvChangeExtProfile()
}

func (p *UserServiceClient) sendChangeExtProfile(header *common.RequestHeader, extProfile *passport_types.UserExtProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeExtProfile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewChangeExtProfileArgs()
	args40.Header = header
	args40.ExtProfile = extProfile
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeExtProfile() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewChangeExtProfileResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result41.Ue != nil {
		ue = result41.Ue
	}
	return
}

// 根据uid获取用户信息，未激活用户也会被返回
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
func (p *UserServiceClient) GetUserById(header *common.RequestHeader, uid int32) (r *passport_types.User, ue *UserException, err error) {
	if err = p.sendGetUserById(header, uid); err != nil {
		return
	}
	return p.recvGetUserById()
}

func (p *UserServiceClient) sendGetUserById(header *common.RequestHeader, uid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getUserById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetUserByIdArgs()
	args44.Header = header
	args44.Uid = uid
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvGetUserById() (value *passport_types.User, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetUserByIdResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Ue != nil {
		ue = result45.Ue
	}
	return
}

// 根据email获取用户信息，未激活用户也会被返回
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 用户Email
func (p *UserServiceClient) GetUserByEmail(header *common.RequestHeader, email string) (r *passport_types.User, ue *UserException, err error) {
	if err = p.sendGetUserByEmail(header, email); err != nil {
		return
	}
	return p.recvGetUserByEmail()
}

func (p *UserServiceClient) sendGetUserByEmail(header *common.RequestHeader, email string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getUserByEmail", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewGetUserByEmailArgs()
	args48.Header = header
	args48.Email = email
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvGetUserByEmail() (value *passport_types.User, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewGetUserByEmailResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Ue != nil {
		ue = result49.Ue
	}
	return
}

// 根据用户注册时间批量获取用户列表（不返回未激活用户）
// 按用户注册时间、uid进行排序，升序或降序由ascending指定
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - StartTime: 查询开始时间
//  - EndTime: 查询结束时间
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *UserServiceClient) ListUidsByRegTime(header *common.RequestHeader, startTime int64, endTime int64, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error) {
	if err = p.sendListUidsByRegTime(header, startTime, endTime, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListUidsByRegTime()
}

func (p *UserServiceClient) sendListUidsByRegTime(header *common.RequestHeader, startTime int64, endTime int64, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listUidsByRegTime", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewListUidsByRegTimeArgs()
	args52.Header = header
	args52.StartTime = startTime
	args52.EndTime = endTime
	args52.Offset = offset
	args52.Limit = limit
	args52.Ascending = ascending
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvListUidsByRegTime() (value *common.QueryResult, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewListUidsByRegTimeResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Ue != nil {
		ue = result53.Ue
	}
	return
}

// 根据用户状态获取用户列表
// 按用户注册时间、uid进行排序，升序或降序由ascending指定
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Status: 用户状态
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *UserServiceClient) ListUidsByStatus(header *common.RequestHeader, status passport_types.UserStatus, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error) {
	if err = p.sendListUidsByStatus(header, status, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListUidsByStatus()
}

func (p *UserServiceClient) sendListUidsByStatus(header *common.RequestHeader, status passport_types.UserStatus, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listUidsByStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewListUidsByStatusArgs()
	args56.Header = header
	args56.Status = status
	args56.Offset = offset
	args56.Limit = limit
	args56.Ascending = ascending
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvListUidsByStatus() (value *common.QueryResult, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewListUidsByStatusResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.Ue != nil {
		ue = result57.Ue
	}
	return
}

// 根据用户类型获取正常用户列表（不返回未激活用户），只返回对应角色的用户，不做角色包含判断
// 按用户注册时间、uid进行排序，升序或降序由ascending指定
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - UserRole: 用户角色
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *UserServiceClient) ListUidsByUserRole(header *common.RequestHeader, userRole enums.UserRole, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error) {
	if err = p.sendListUidsByUserRole(header, userRole, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListUidsByUserRole()
}

func (p *UserServiceClient) sendListUidsByUserRole(header *common.RequestHeader, userRole enums.UserRole, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listUidsByUserRole", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewListUidsByUserRoleArgs()
	args60.Header = header
	args60.UserRole = userRole
	args60.Offset = offset
	args60.Limit = limit
	args60.Ascending = ascending
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvListUidsByUserRole() (value *common.QueryResult, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewListUidsByUserRoleResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	if result61.Ue != nil {
		ue = result61.Ue
	}
	return
}

// 根据用户被禁封类型获取用户列表（不返回未激活用户）
// 按用户注册时间、uid进行排序，升序或降序由ascending指定
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - AdminBanStatus: 管理后台禁封状态
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *UserServiceClient) ListUidsByAdminBanStatus(header *common.RequestHeader, adminBanStatus passport_types.AdminBanStatus, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error) {
	if err = p.sendListUidsByAdminBanStatus(header, adminBanStatus, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListUidsByAdminBanStatus()
}

func (p *UserServiceClient) sendListUidsByAdminBanStatus(header *common.RequestHeader, adminBanStatus passport_types.AdminBanStatus, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listUidsByAdminBanStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewListUidsByAdminBanStatusArgs()
	args64.Header = header
	args64.AdminBanStatus = adminBanStatus
	args64.Offset = offset
	args64.Limit = limit
	args64.Ascending = ascending
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvListUidsByAdminBanStatus() (value *common.QueryResult, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewListUidsByAdminBanStatusResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.Ue != nil {
		ue = result65.Ue
	}
	return
}

// 根据用户标记获取用户列表（包括未激活用户）
// 按用户注册时间进行排序，升序或降序由ascending指定
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Key: 查找键名
//  - Value: 查找键值
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *UserServiceClient) ListUidsByExtInfo(header *common.RequestHeader, key string, value string, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error) {
	if err = p.sendListUidsByExtInfo(header, key, value, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListUidsByExtInfo()
}

func (p *UserServiceClient) sendListUidsByExtInfo(header *common.RequestHeader, key string, value string, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listUidsByExtInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewListUidsByExtInfoArgs()
	args68.Header = header
	args68.Key = key
	args68.Value = value
	args68.Offset = offset
	args68.Limit = limit
	args68.Ascending = ascending
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvListUidsByExtInfo() (value *common.QueryResult, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewListUidsByExtInfoResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result69.Success
	if result69.Ue != nil {
		ue = result69.Ue
	}
	return
}

// 管理后台根据uids批量获取用户元信息（包括未激活用户）
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uids: 用户编号列表
func (p *UserServiceClient) GetUserMetaByUids(header *common.RequestHeader, uids []int32) (r map[int32]*passport_types.UserMeta, ue *UserException, err error) {
	if err = p.sendGetUserMetaByUids(header, uids); err != nil {
		return
	}
	return p.recvGetUserMetaByUids()
}

func (p *UserServiceClient) sendGetUserMetaByUids(header *common.RequestHeader, uids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getUserMetaByUids", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args72 := NewGetUserMetaByUidsArgs()
	args72.Header = header
	args72.Uids = uids
	if err = args72.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvGetUserMetaByUids() (value map[int32]*passport_types.UserMeta, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error74 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error75 error
		error75, err = error74.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error75
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result73 := NewGetUserMetaByUidsResult()
	if err = result73.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result73.Success
	if result73.Ue != nil {
		ue = result73.Ue
	}
	return
}

// 根据ids获取用户信息，未激活用户也会被返回
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 用户编号
func (p *UserServiceClient) GetUsersByIds(header *common.RequestHeader, ids []int32) (r map[int32]*passport_types.User, ue *UserException, err error) {
	if err = p.sendGetUsersByIds(header, ids); err != nil {
		return
	}
	return p.recvGetUsersByIds()
}

func (p *UserServiceClient) sendGetUsersByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getUsersByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args76 := NewGetUsersByIdsArgs()
	args76.Header = header
	args76.Ids = ids
	if err = args76.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvGetUsersByIds() (value map[int32]*passport_types.User, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error78 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error79 error
		error79, err = error78.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error79
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result77 := NewGetUsersByIdsResult()
	if err = result77.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result77.Success
	if result77.Ue != nil {
		ue = result77.Ue
	}
	return
}

// 管理后台根据uids批量封禁用户
// 可以对未激活用户进行封禁操作
// 重复封禁不报错
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uids: 用户编号列表
func (p *UserServiceClient) AdminBanUser(header *common.RequestHeader, uids []int32) (ue *UserException, err error) {
	if err = p.sendAdminBanUser(header, uids); err != nil {
		return
	}
	return p.recvAdminBanUser()
}

func (p *UserServiceClient) sendAdminBanUser(header *common.RequestHeader, uids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("adminBanUser", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args80 := NewAdminBanUserArgs()
	args80.Header = header
	args80.Uids = uids
	if err = args80.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvAdminBanUser() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error82 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error83 error
		error83, err = error82.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error83
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result81 := NewAdminBanUserResult()
	if err = result81.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result81.Ue != nil {
		ue = result81.Ue
	}
	return
}

// 管理后台根据uids批量解禁用户
// 可以对未激活用户进行解禁操作
// 相应角色本未被封禁时不报错
// 仅限管理后台调用
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uids: 用户编号列表
func (p *UserServiceClient) AdminUnbanUser(header *common.RequestHeader, uids []int32) (ue *UserException, err error) {
	if err = p.sendAdminUnbanUser(header, uids); err != nil {
		return
	}
	return p.recvAdminUnbanUser()
}

func (p *UserServiceClient) sendAdminUnbanUser(header *common.RequestHeader, uids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("adminUnbanUser", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args84 := NewAdminUnbanUserArgs()
	args84.Header = header
	args84.Uids = uids
	if err = args84.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvAdminUnbanUser() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error86 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error87 error
		error87, err = error86.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error87
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result85 := NewAdminUnbanUserResult()
	if err = result85.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result85.Ue != nil {
		ue = result85.Ue
	}
	return
}

// 根据代理广告主UID获取所管理的被代理广告主UID列表（包括未激活用户）
// 按用户注册时间、uid进行排序，升序或降序由ascending指定
//
// Parameters:
//  - Header: 请求消息头结构体
//  - AgentUid: 代理广告主UID
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *UserServiceClient) ListManagedUidsByAgentUid(header *common.RequestHeader, agentUid int32, offset int32, limit int32, ascending bool) (r *common.QueryResult, ue *UserException, err error) {
	if err = p.sendListManagedUidsByAgentUid(header, agentUid, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListManagedUidsByAgentUid()
}

func (p *UserServiceClient) sendListManagedUidsByAgentUid(header *common.RequestHeader, agentUid int32, offset int32, limit int32, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listManagedUidsByAgentUid", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args88 := NewListManagedUidsByAgentUidArgs()
	args88.Header = header
	args88.AgentUid = agentUid
	args88.Offset = offset
	args88.Limit = limit
	args88.Ascending = ascending
	if err = args88.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvListManagedUidsByAgentUid() (value *common.QueryResult, ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error90 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error91 error
		error91, err = error90.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error91
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result89 := NewListManagedUidsByAgentUidResult()
	if err = result89.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result89.Success
	if result89.Ue != nil {
		ue = result89.Ue
	}
	return
}

// 更改用户备注名
// remark不允许是null，可以为空串，表示使用默认备注名
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - Remark: 用户备注名
func (p *UserServiceClient) ChangeUserRemark(header *common.RequestHeader, uid int32, remark string) (ue *UserException, err error) {
	if err = p.sendChangeUserRemark(header, uid, remark); err != nil {
		return
	}
	return p.recvChangeUserRemark()
}

func (p *UserServiceClient) sendChangeUserRemark(header *common.RequestHeader, uid int32, remark string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeUserRemark", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args92 := NewChangeUserRemarkArgs()
	args92.Header = header
	args92.Uid = uid
	args92.Remark = remark
	if err = args92.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeUserRemark() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error94 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error95 error
		error95, err = error94.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error95
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result93 := NewChangeUserRemarkResult()
	if err = result93.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result93.Ue != nil {
		ue = result93.Ue
	}
	return
}

// 更改用户注册email
// uid和oldEmail对应关系必须正确，oldEmail必须存在，newEmail必须不存在并合法
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - OldEmail: 用户旧Email
//  - NewEmail: 用户新Email
func (p *UserServiceClient) ChangeUserEmail(header *common.RequestHeader, uid int32, oldEmail string, newEmail string) (ue *UserException, err error) {
	if err = p.sendChangeUserEmail(header, uid, oldEmail, newEmail); err != nil {
		return
	}
	return p.recvChangeUserEmail()
}

func (p *UserServiceClient) sendChangeUserEmail(header *common.RequestHeader, uid int32, oldEmail string, newEmail string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeUserEmail", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args96 := NewChangeUserEmailArgs()
	args96.Header = header
	args96.Uid = uid
	args96.OldEmail = oldEmail
	args96.NewEmail = newEmail
	if err = args96.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeUserEmail() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error98 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error99 error
		error99, err = error98.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error99
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result97 := NewChangeUserEmailResult()
	if err = result97.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result97.Ue != nil {
		ue = result97.Ue
	}
	return
}

// 更改用户的广告主类型
// 用户必须是广告主：普通、综合、被代理的广告主
// 有以下限制：
//  1. 已经是品牌广告主的，不允许标记为其它类型
//  2. 非效果广告主可以更改为其它类型
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - SponsorType: 新广告主类型
func (p *UserServiceClient) ChangeSponsorType(header *common.RequestHeader, uid int32, sponsorType enums.SponsorType) (ue *UserException, err error) {
	if err = p.sendChangeSponsorType(header, uid, sponsorType); err != nil {
		return
	}
	return p.recvChangeSponsorType()
}

func (p *UserServiceClient) sendChangeSponsorType(header *common.RequestHeader, uid int32, sponsorType enums.SponsorType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("changeSponsorType", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args100 := NewChangeSponsorTypeArgs()
	args100.Header = header
	args100.Uid = uid
	args100.SponsorType = sponsorType
	if err = args100.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvChangeSponsorType() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error102 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error103 error
		error103, err = error102.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error103
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result101 := NewChangeSponsorTypeResult()
	if err = result101.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result101.Ue != nil {
		ue = result101.Ue
	}
	return
}

// 按key更新user的扩展信息
// 注意，这里的更新实际上是插入或更新，不删除没有提供的key
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - ExtInfo: 扩展信息，仅更新和扩充，不删除没有提供的key
func (p *UserServiceClient) UpdateUserExtInfo(header *common.RequestHeader, uid int32, extInfo map[string]string) (ue *UserException, err error) {
	if err = p.sendUpdateUserExtInfo(header, uid, extInfo); err != nil {
		return
	}
	return p.recvUpdateUserExtInfo()
}

func (p *UserServiceClient) sendUpdateUserExtInfo(header *common.RequestHeader, uid int32, extInfo map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateUserExtInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args104 := NewUpdateUserExtInfoArgs()
	args104.Header = header
	args104.Uid = uid
	args104.ExtInfo = extInfo
	if err = args104.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvUpdateUserExtInfo() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error106 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error107 error
		error107, err = error106.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error107
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result105 := NewUpdateUserExtInfoResult()
	if err = result105.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result105.Ue != nil {
		ue = result105.Ue
	}
	return
}

// 按key删除user的扩展信息
// 注意，只删除提供了的key
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Uid: 用户编号
//  - Keys: 要删除的扩展信息key，只删除提供了的key
func (p *UserServiceClient) DeleteUserExtInfo(header *common.RequestHeader, uid int32, keys map[string]bool) (ue *UserException, err error) {
	if err = p.sendDeleteUserExtInfo(header, uid, keys); err != nil {
		return
	}
	return p.recvDeleteUserExtInfo()
}

func (p *UserServiceClient) sendDeleteUserExtInfo(header *common.RequestHeader, uid int32, keys map[string]bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteUserExtInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args108 := NewDeleteUserExtInfoArgs()
	args108.Header = header
	args108.Uid = uid
	args108.Keys = keys
	if err = args108.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserServiceClient) recvDeleteUserExtInfo() (ue *UserException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error110 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error111 error
		error111, err = error110.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error111
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result109 := NewDeleteUserExtInfoResult()
	if err = result109.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result109.Ue != nil {
		ue = result109.Ue
	}
	return
}

type UserServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UserService
}

func (p *UserServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UserServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UserServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUserServiceProcessor(handler UserService) *UserServiceProcessor {

	self112 := &UserServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self112.processorMap["registerUser"] = &userServiceProcessorRegisterUser{handler: handler}
	self112.processorMap["isUserEmailAvailable"] = &userServiceProcessorIsUserEmailAvailable{handler: handler}
	self112.processorMap["activateUser"] = &userServiceProcessorActivateUser{handler: handler}
	self112.processorMap["verifyUserPassword"] = &userServiceProcessorVerifyUserPassword{handler: handler}
	self112.processorMap["changeUserPassword"] = &userServiceProcessorChangeUserPassword{handler: handler}
	self112.processorMap["resetPassword"] = &userServiceProcessorResetPassword{handler: handler}
	self112.processorMap["addRoleToUser"] = &userServiceProcessorAddRoleToUser{handler: handler}
	self112.processorMap["changeUserRole"] = &userServiceProcessorChangeUserRole{handler: handler}
	self112.processorMap["changeAderProfile"] = &userServiceProcessorChangeAderProfile{handler: handler}
	self112.processorMap["changeDeverProfile"] = &userServiceProcessorChangeDeverProfile{handler: handler}
	self112.processorMap["changeExtProfile"] = &userServiceProcessorChangeExtProfile{handler: handler}
	self112.processorMap["getUserById"] = &userServiceProcessorGetUserById{handler: handler}
	self112.processorMap["getUserByEmail"] = &userServiceProcessorGetUserByEmail{handler: handler}
	self112.processorMap["listUidsByRegTime"] = &userServiceProcessorListUidsByRegTime{handler: handler}
	self112.processorMap["listUidsByStatus"] = &userServiceProcessorListUidsByStatus{handler: handler}
	self112.processorMap["listUidsByUserRole"] = &userServiceProcessorListUidsByUserRole{handler: handler}
	self112.processorMap["listUidsByAdminBanStatus"] = &userServiceProcessorListUidsByAdminBanStatus{handler: handler}
	self112.processorMap["listUidsByExtInfo"] = &userServiceProcessorListUidsByExtInfo{handler: handler}
	self112.processorMap["getUserMetaByUids"] = &userServiceProcessorGetUserMetaByUids{handler: handler}
	self112.processorMap["getUsersByIds"] = &userServiceProcessorGetUsersByIds{handler: handler}
	self112.processorMap["adminBanUser"] = &userServiceProcessorAdminBanUser{handler: handler}
	self112.processorMap["adminUnbanUser"] = &userServiceProcessorAdminUnbanUser{handler: handler}
	self112.processorMap["listManagedUidsByAgentUid"] = &userServiceProcessorListManagedUidsByAgentUid{handler: handler}
	self112.processorMap["changeUserRemark"] = &userServiceProcessorChangeUserRemark{handler: handler}
	self112.processorMap["changeUserEmail"] = &userServiceProcessorChangeUserEmail{handler: handler}
	self112.processorMap["changeSponsorType"] = &userServiceProcessorChangeSponsorType{handler: handler}
	self112.processorMap["updateUserExtInfo"] = &userServiceProcessorUpdateUserExtInfo{handler: handler}
	self112.processorMap["deleteUserExtInfo"] = &userServiceProcessorDeleteUserExtInfo{handler: handler}
	return self112
}

func (p *UserServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x113 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x113.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x113

}

type userServiceProcessorRegisterUser struct {
	handler UserService
}

func (p *userServiceProcessorRegisterUser) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRegisterUserArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("registerUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRegisterUserResult()
	if result.Success, result.Ue, err = p.handler.RegisterUser(args.Header, args.RegInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing registerUser: "+err.Error())
		oprot.WriteMessageBegin("registerUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("registerUser", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorIsUserEmailAvailable struct {
	handler UserService
}

func (p *userServiceProcessorIsUserEmailAvailable) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewIsUserEmailAvailableArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("isUserEmailAvailable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewIsUserEmailAvailableResult()
	if result.Success, result.Ue, err = p.handler.IsUserEmailAvailable(args.Header, args.Email); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isUserEmailAvailable: "+err.Error())
		oprot.WriteMessageBegin("isUserEmailAvailable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("isUserEmailAvailable", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorActivateUser struct {
	handler UserService
}

func (p *userServiceProcessorActivateUser) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewActivateUserArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("activateUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewActivateUserResult()
	if result.Ue, err = p.handler.ActivateUser(args.Header, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing activateUser: "+err.Error())
		oprot.WriteMessageBegin("activateUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("activateUser", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorVerifyUserPassword struct {
	handler UserService
}

func (p *userServiceProcessorVerifyUserPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewVerifyUserPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("verifyUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewVerifyUserPasswordResult()
	if result.Ue, err = p.handler.VerifyUserPassword(args.Header, args.Email, args.Password); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing verifyUserPassword: "+err.Error())
		oprot.WriteMessageBegin("verifyUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("verifyUserPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeUserPassword struct {
	handler UserService
}

func (p *userServiceProcessorChangeUserPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeUserPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeUserPasswordResult()
	if result.Ue, err = p.handler.ChangeUserPassword(args.Header, args.Uid, args.NewPassword, args.OldPassword); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeUserPassword: "+err.Error())
		oprot.WriteMessageBegin("changeUserPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeUserPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorResetPassword struct {
	handler UserService
}

func (p *userServiceProcessorResetPassword) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResetPasswordArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resetPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResetPasswordResult()
	if result.Ue, err = p.handler.ResetPassword(args.Header, args.Uid, args.NewPassword); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resetPassword: "+err.Error())
		oprot.WriteMessageBegin("resetPassword", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resetPassword", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorAddRoleToUser struct {
	handler UserService
}

func (p *userServiceProcessorAddRoleToUser) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddRoleToUserArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addRoleToUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddRoleToUserResult()
	if result.Ue, err = p.handler.AddRoleToUser(args.Header, args.Uid, args.UserRole); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addRoleToUser: "+err.Error())
		oprot.WriteMessageBegin("addRoleToUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addRoleToUser", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeUserRole struct {
	handler UserService
}

func (p *userServiceProcessorChangeUserRole) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeUserRoleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeUserRole", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeUserRoleResult()
	if result.Ue, err = p.handler.ChangeUserRole(args.Header, args.Uid, args.UserRole); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeUserRole: "+err.Error())
		oprot.WriteMessageBegin("changeUserRole", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeUserRole", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeAderProfile struct {
	handler UserService
}

func (p *userServiceProcessorChangeAderProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeAderProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeAderProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeAderProfileResult()
	if result.Ue, err = p.handler.ChangeAderProfile(args.Header, args.AderProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeAderProfile: "+err.Error())
		oprot.WriteMessageBegin("changeAderProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeAderProfile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeDeverProfile struct {
	handler UserService
}

func (p *userServiceProcessorChangeDeverProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeDeverProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeDeverProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeDeverProfileResult()
	if result.Ue, err = p.handler.ChangeDeverProfile(args.Header, args.DeverProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeDeverProfile: "+err.Error())
		oprot.WriteMessageBegin("changeDeverProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeDeverProfile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeExtProfile struct {
	handler UserService
}

func (p *userServiceProcessorChangeExtProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeExtProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeExtProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeExtProfileResult()
	if result.Ue, err = p.handler.ChangeExtProfile(args.Header, args.ExtProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeExtProfile: "+err.Error())
		oprot.WriteMessageBegin("changeExtProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeExtProfile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorGetUserById struct {
	handler UserService
}

func (p *userServiceProcessorGetUserById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUserByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getUserById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUserByIdResult()
	if result.Success, result.Ue, err = p.handler.GetUserById(args.Header, args.Uid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserById: "+err.Error())
		oprot.WriteMessageBegin("getUserById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getUserById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorGetUserByEmail struct {
	handler UserService
}

func (p *userServiceProcessorGetUserByEmail) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUserByEmailArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getUserByEmail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUserByEmailResult()
	if result.Success, result.Ue, err = p.handler.GetUserByEmail(args.Header, args.Email); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserByEmail: "+err.Error())
		oprot.WriteMessageBegin("getUserByEmail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getUserByEmail", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorListUidsByRegTime struct {
	handler UserService
}

func (p *userServiceProcessorListUidsByRegTime) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListUidsByRegTimeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listUidsByRegTime", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListUidsByRegTimeResult()
	if result.Success, result.Ue, err = p.handler.ListUidsByRegTime(args.Header, args.StartTime, args.EndTime, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listUidsByRegTime: "+err.Error())
		oprot.WriteMessageBegin("listUidsByRegTime", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listUidsByRegTime", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorListUidsByStatus struct {
	handler UserService
}

func (p *userServiceProcessorListUidsByStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListUidsByStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listUidsByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListUidsByStatusResult()
	if result.Success, result.Ue, err = p.handler.ListUidsByStatus(args.Header, args.Status, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listUidsByStatus: "+err.Error())
		oprot.WriteMessageBegin("listUidsByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listUidsByStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorListUidsByUserRole struct {
	handler UserService
}

func (p *userServiceProcessorListUidsByUserRole) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListUidsByUserRoleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listUidsByUserRole", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListUidsByUserRoleResult()
	if result.Success, result.Ue, err = p.handler.ListUidsByUserRole(args.Header, args.UserRole, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listUidsByUserRole: "+err.Error())
		oprot.WriteMessageBegin("listUidsByUserRole", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listUidsByUserRole", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorListUidsByAdminBanStatus struct {
	handler UserService
}

func (p *userServiceProcessorListUidsByAdminBanStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListUidsByAdminBanStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listUidsByAdminBanStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListUidsByAdminBanStatusResult()
	if result.Success, result.Ue, err = p.handler.ListUidsByAdminBanStatus(args.Header, args.AdminBanStatus, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listUidsByAdminBanStatus: "+err.Error())
		oprot.WriteMessageBegin("listUidsByAdminBanStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listUidsByAdminBanStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorListUidsByExtInfo struct {
	handler UserService
}

func (p *userServiceProcessorListUidsByExtInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListUidsByExtInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listUidsByExtInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListUidsByExtInfoResult()
	if result.Success, result.Ue, err = p.handler.ListUidsByExtInfo(args.Header, args.Key, args.Value, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listUidsByExtInfo: "+err.Error())
		oprot.WriteMessageBegin("listUidsByExtInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listUidsByExtInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorGetUserMetaByUids struct {
	handler UserService
}

func (p *userServiceProcessorGetUserMetaByUids) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUserMetaByUidsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getUserMetaByUids", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUserMetaByUidsResult()
	if result.Success, result.Ue, err = p.handler.GetUserMetaByUids(args.Header, args.Uids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMetaByUids: "+err.Error())
		oprot.WriteMessageBegin("getUserMetaByUids", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getUserMetaByUids", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorGetUsersByIds struct {
	handler UserService
}

func (p *userServiceProcessorGetUsersByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUsersByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getUsersByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUsersByIdsResult()
	if result.Success, result.Ue, err = p.handler.GetUsersByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUsersByIds: "+err.Error())
		oprot.WriteMessageBegin("getUsersByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getUsersByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorAdminBanUser struct {
	handler UserService
}

func (p *userServiceProcessorAdminBanUser) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAdminBanUserArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("adminBanUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAdminBanUserResult()
	if result.Ue, err = p.handler.AdminBanUser(args.Header, args.Uids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adminBanUser: "+err.Error())
		oprot.WriteMessageBegin("adminBanUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("adminBanUser", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorAdminUnbanUser struct {
	handler UserService
}

func (p *userServiceProcessorAdminUnbanUser) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAdminUnbanUserArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("adminUnbanUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAdminUnbanUserResult()
	if result.Ue, err = p.handler.AdminUnbanUser(args.Header, args.Uids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adminUnbanUser: "+err.Error())
		oprot.WriteMessageBegin("adminUnbanUser", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("adminUnbanUser", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorListManagedUidsByAgentUid struct {
	handler UserService
}

func (p *userServiceProcessorListManagedUidsByAgentUid) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListManagedUidsByAgentUidArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listManagedUidsByAgentUid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListManagedUidsByAgentUidResult()
	if result.Success, result.Ue, err = p.handler.ListManagedUidsByAgentUid(args.Header, args.AgentUid, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listManagedUidsByAgentUid: "+err.Error())
		oprot.WriteMessageBegin("listManagedUidsByAgentUid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listManagedUidsByAgentUid", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeUserRemark struct {
	handler UserService
}

func (p *userServiceProcessorChangeUserRemark) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeUserRemarkArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeUserRemark", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeUserRemarkResult()
	if result.Ue, err = p.handler.ChangeUserRemark(args.Header, args.Uid, args.Remark); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeUserRemark: "+err.Error())
		oprot.WriteMessageBegin("changeUserRemark", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeUserRemark", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeUserEmail struct {
	handler UserService
}

func (p *userServiceProcessorChangeUserEmail) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeUserEmailArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeUserEmail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeUserEmailResult()
	if result.Ue, err = p.handler.ChangeUserEmail(args.Header, args.Uid, args.OldEmail, args.NewEmail); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeUserEmail: "+err.Error())
		oprot.WriteMessageBegin("changeUserEmail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeUserEmail", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorChangeSponsorType struct {
	handler UserService
}

func (p *userServiceProcessorChangeSponsorType) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewChangeSponsorTypeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("changeSponsorType", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewChangeSponsorTypeResult()
	if result.Ue, err = p.handler.ChangeSponsorType(args.Header, args.Uid, args.SponsorType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing changeSponsorType: "+err.Error())
		oprot.WriteMessageBegin("changeSponsorType", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("changeSponsorType", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorUpdateUserExtInfo struct {
	handler UserService
}

func (p *userServiceProcessorUpdateUserExtInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateUserExtInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateUserExtInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateUserExtInfoResult()
	if result.Ue, err = p.handler.UpdateUserExtInfo(args.Header, args.Uid, args.ExtInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateUserExtInfo: "+err.Error())
		oprot.WriteMessageBegin("updateUserExtInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateUserExtInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type userServiceProcessorDeleteUserExtInfo struct {
	handler UserService
}

func (p *userServiceProcessorDeleteUserExtInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteUserExtInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteUserExtInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteUserExtInfoResult()
	if result.Ue, err = p.handler.DeleteUserExtInfo(args.Header, args.Uid, args.Keys); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteUserExtInfo: "+err.Error())
		oprot.WriteMessageBegin("deleteUserExtInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteUserExtInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type RegisterUserArgs struct {
	Header  *common.RequestHeader       `thrift:"header,1" json:"header"`
	RegInfo *passport_types.UserRegInfo `thrift:"regInfo,2" json:"regInfo"`
}

func NewRegisterUserArgs() *RegisterUserArgs {
	return &RegisterUserArgs{}
}

func (p *RegisterUserArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterUserArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RegisterUserArgs) readField2(iprot thrift.TProtocol) error {
	p.RegInfo = passport_types.NewUserRegInfo()
	if err := p.RegInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RegInfo)
	}
	return nil
}

func (p *RegisterUserArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerUser_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterUserArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RegisterUserArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RegInfo != nil {
		if err := oprot.WriteFieldBegin("regInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:regInfo: %s", p, err)
		}
		if err := p.RegInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RegInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:regInfo: %s", p, err)
		}
	}
	return err
}

func (p *RegisterUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterUserArgs(%+v)", *p)
}

type RegisterUserResult struct {
	Success int32          `thrift:"success,0" json:"success"`
	Ue      *UserException `thrift:"ue,1" json:"ue"`
}

func NewRegisterUserResult() *RegisterUserResult {
	return &RegisterUserResult{}
}

func (p *RegisterUserResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterUserResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *RegisterUserResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *RegisterUserResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerUser_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterUserResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *RegisterUserResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *RegisterUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterUserResult(%+v)", *p)
}

type IsUserEmailAvailableArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Email  string                `thrift:"email,2" json:"email"`
}

func NewIsUserEmailAvailableArgs() *IsUserEmailAvailableArgs {
	return &IsUserEmailAvailableArgs{}
}

func (p *IsUserEmailAvailableArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsUserEmailAvailableArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *IsUserEmailAvailableArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *IsUserEmailAvailableArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isUserEmailAvailable_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsUserEmailAvailableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *IsUserEmailAvailableArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *IsUserEmailAvailableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsUserEmailAvailableArgs(%+v)", *p)
}

type IsUserEmailAvailableResult struct {
	Success bool           `thrift:"success,0" json:"success"`
	Ue      *UserException `thrift:"ue,1" json:"ue"`
}

func NewIsUserEmailAvailableResult() *IsUserEmailAvailableResult {
	return &IsUserEmailAvailableResult{}
}

func (p *IsUserEmailAvailableResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsUserEmailAvailableResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *IsUserEmailAvailableResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *IsUserEmailAvailableResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isUserEmailAvailable_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsUserEmailAvailableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *IsUserEmailAvailableResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *IsUserEmailAvailableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsUserEmailAvailableResult(%+v)", *p)
}

type ActivateUserArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
}

func NewActivateUserArgs() *ActivateUserArgs {
	return &ActivateUserArgs{}
}

func (p *ActivateUserArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ActivateUserArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ActivateUserArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ActivateUserArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("activateUser_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ActivateUserArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ActivateUserArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ActivateUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ActivateUserArgs(%+v)", *p)
}

type ActivateUserResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewActivateUserResult() *ActivateUserResult {
	return &ActivateUserResult{}
}

func (p *ActivateUserResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ActivateUserResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ActivateUserResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("activateUser_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ActivateUserResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ActivateUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ActivateUserResult(%+v)", *p)
}

type VerifyUserPasswordArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Email    string                `thrift:"email,2" json:"email"`
	Password string                `thrift:"password,3" json:"password"`
}

func NewVerifyUserPasswordArgs() *VerifyUserPasswordArgs {
	return &VerifyUserPasswordArgs{}
}

func (p *VerifyUserPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *VerifyUserPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *VerifyUserPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *VerifyUserPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("verifyUserPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *VerifyUserPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *VerifyUserPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:password: %s", p, err)
	}
	return err
}

func (p *VerifyUserPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VerifyUserPasswordArgs(%+v)", *p)
}

type VerifyUserPasswordResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewVerifyUserPasswordResult() *VerifyUserPasswordResult {
	return &VerifyUserPasswordResult{}
}

func (p *VerifyUserPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *VerifyUserPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("verifyUserPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VerifyUserPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *VerifyUserPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VerifyUserPasswordResult(%+v)", *p)
}

type ChangeUserPasswordArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid         int32                 `thrift:"uid,2" json:"uid"`
	NewPassword string                `thrift:"newPassword,3" json:"newPassword"`
	OldPassword string                `thrift:"oldPassword,4" json:"oldPassword"`
}

func NewChangeUserPasswordArgs() *ChangeUserPasswordArgs {
	return &ChangeUserPasswordArgs{}
}

func (p *ChangeUserPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NewPassword = v
	}
	return nil
}

func (p *ChangeUserPasswordArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OldPassword = v
	}
	return nil
}

func (p *ChangeUserPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ChangeUserPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newPassword", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:newPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewPassword)); err != nil {
		return fmt.Errorf("%T.newPassword (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:newPassword: %s", p, err)
	}
	return err
}

func (p *ChangeUserPasswordArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oldPassword", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:oldPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OldPassword)); err != nil {
		return fmt.Errorf("%T.oldPassword (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:oldPassword: %s", p, err)
	}
	return err
}

func (p *ChangeUserPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserPasswordArgs(%+v)", *p)
}

type ChangeUserPasswordResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeUserPasswordResult() *ChangeUserPasswordResult {
	return &ChangeUserPasswordResult{}
}

func (p *ChangeUserPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeUserPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserPasswordResult(%+v)", *p)
}

type ResetPasswordArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid         int32                 `thrift:"uid,2" json:"uid"`
	NewPassword string                `thrift:"newPassword,3" json:"newPassword"`
}

func NewResetPasswordArgs() *ResetPasswordArgs {
	return &ResetPasswordArgs{}
}

func (p *ResetPasswordArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswordArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ResetPasswordArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ResetPasswordArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NewPassword = v
	}
	return nil
}

func (p *ResetPasswordArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetPassword_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ResetPasswordArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ResetPasswordArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newPassword", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:newPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewPassword)); err != nil {
		return fmt.Errorf("%T.newPassword (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:newPassword: %s", p, err)
	}
	return err
}

func (p *ResetPasswordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetPasswordArgs(%+v)", *p)
}

type ResetPasswordResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewResetPasswordResult() *ResetPasswordResult {
	return &ResetPasswordResult{}
}

func (p *ResetPasswordResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswordResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ResetPasswordResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetPassword_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetPasswordResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ResetPasswordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetPasswordResult(%+v)", *p)
}

type AddRoleToUserArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      int32                 `thrift:"uid,2" json:"uid"`
	UserRole enums.UserRole        `thrift:"userRole,3" json:"userRole"`
}

func NewAddRoleToUserArgs() *AddRoleToUserArgs {
	return &AddRoleToUserArgs{
		UserRole: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddRoleToUserArgs) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *AddRoleToUserArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddRoleToUserArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddRoleToUserArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AddRoleToUserArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserRole = enums.UserRole(v)
	}
	return nil
}

func (p *AddRoleToUserArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addRoleToUser_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddRoleToUserArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddRoleToUserArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AddRoleToUserArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:userRole: %s", p, err)
		}
	}
	return err
}

func (p *AddRoleToUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddRoleToUserArgs(%+v)", *p)
}

type AddRoleToUserResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewAddRoleToUserResult() *AddRoleToUserResult {
	return &AddRoleToUserResult{}
}

func (p *AddRoleToUserResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddRoleToUserResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *AddRoleToUserResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addRoleToUser_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddRoleToUserResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *AddRoleToUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddRoleToUserResult(%+v)", *p)
}

type ChangeUserRoleArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      int32                 `thrift:"uid,2" json:"uid"`
	UserRole enums.UserRole        `thrift:"userRole,3" json:"userRole"`
}

func NewChangeUserRoleArgs() *ChangeUserRoleArgs {
	return &ChangeUserRoleArgs{
		UserRole: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ChangeUserRoleArgs) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *ChangeUserRoleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRoleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeUserRoleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ChangeUserRoleArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserRole = enums.UserRole(v)
	}
	return nil
}

func (p *ChangeUserRoleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserRole_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRoleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserRoleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ChangeUserRoleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:userRole: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserRoleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserRoleArgs(%+v)", *p)
}

type ChangeUserRoleResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeUserRoleResult() *ChangeUserRoleResult {
	return &ChangeUserRoleResult{}
}

func (p *ChangeUserRoleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRoleResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeUserRoleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserRole_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRoleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserRoleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserRoleResult(%+v)", *p)
}

type ChangeAderProfileArgs struct {
	Header      *common.RequestHeader           `thrift:"header,1" json:"header"`
	AderProfile *passport_types.UserAderProfile `thrift:"aderProfile,2" json:"aderProfile"`
}

func NewChangeAderProfileArgs() *ChangeAderProfileArgs {
	return &ChangeAderProfileArgs{}
}

func (p *ChangeAderProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeAderProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeAderProfileArgs) readField2(iprot thrift.TProtocol) error {
	p.AderProfile = passport_types.NewUserAderProfile()
	if err := p.AderProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AderProfile)
	}
	return nil
}

func (p *ChangeAderProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeAderProfile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeAderProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeAderProfileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AderProfile != nil {
		if err := oprot.WriteFieldBegin("aderProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:aderProfile: %s", p, err)
		}
		if err := p.AderProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AderProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:aderProfile: %s", p, err)
		}
	}
	return err
}

func (p *ChangeAderProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeAderProfileArgs(%+v)", *p)
}

type ChangeAderProfileResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeAderProfileResult() *ChangeAderProfileResult {
	return &ChangeAderProfileResult{}
}

func (p *ChangeAderProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeAderProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeAderProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeAderProfile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeAderProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeAderProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeAderProfileResult(%+v)", *p)
}

type ChangeDeverProfileArgs struct {
	Header       *common.RequestHeader            `thrift:"header,1" json:"header"`
	DeverProfile *passport_types.UserDeverProfile `thrift:"deverProfile,2" json:"deverProfile"`
}

func NewChangeDeverProfileArgs() *ChangeDeverProfileArgs {
	return &ChangeDeverProfileArgs{}
}

func (p *ChangeDeverProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeDeverProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeDeverProfileArgs) readField2(iprot thrift.TProtocol) error {
	p.DeverProfile = passport_types.NewUserDeverProfile()
	if err := p.DeverProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverProfile)
	}
	return nil
}

func (p *ChangeDeverProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeDeverProfile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeDeverProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeDeverProfileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DeverProfile != nil {
		if err := oprot.WriteFieldBegin("deverProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:deverProfile: %s", p, err)
		}
		if err := p.DeverProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:deverProfile: %s", p, err)
		}
	}
	return err
}

func (p *ChangeDeverProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeDeverProfileArgs(%+v)", *p)
}

type ChangeDeverProfileResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeDeverProfileResult() *ChangeDeverProfileResult {
	return &ChangeDeverProfileResult{}
}

func (p *ChangeDeverProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeDeverProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeDeverProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeDeverProfile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeDeverProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeDeverProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeDeverProfileResult(%+v)", *p)
}

type ChangeExtProfileArgs struct {
	Header     *common.RequestHeader          `thrift:"header,1" json:"header"`
	ExtProfile *passport_types.UserExtProfile `thrift:"extProfile,2" json:"extProfile"`
}

func NewChangeExtProfileArgs() *ChangeExtProfileArgs {
	return &ChangeExtProfileArgs{}
}

func (p *ChangeExtProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeExtProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeExtProfileArgs) readField2(iprot thrift.TProtocol) error {
	p.ExtProfile = passport_types.NewUserExtProfile()
	if err := p.ExtProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ExtProfile)
	}
	return nil
}

func (p *ChangeExtProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeExtProfile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeExtProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeExtProfileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ExtProfile != nil {
		if err := oprot.WriteFieldBegin("extProfile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:extProfile: %s", p, err)
		}
		if err := p.ExtProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ExtProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:extProfile: %s", p, err)
		}
	}
	return err
}

func (p *ChangeExtProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeExtProfileArgs(%+v)", *p)
}

type ChangeExtProfileResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeExtProfileResult() *ChangeExtProfileResult {
	return &ChangeExtProfileResult{}
}

func (p *ChangeExtProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeExtProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeExtProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeExtProfile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeExtProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeExtProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeExtProfileResult(%+v)", *p)
}

type GetUserByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
}

func NewGetUserByIdArgs() *GetUserByIdArgs {
	return &GetUserByIdArgs{}
}

func (p *GetUserByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetUserByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *GetUserByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetUserByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *GetUserByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserByIdArgs(%+v)", *p)
}

type GetUserByIdResult struct {
	Success *passport_types.User `thrift:"success,0" json:"success"`
	Ue      *UserException       `thrift:"ue,1" json:"ue"`
}

func NewGetUserByIdResult() *GetUserByIdResult {
	return &GetUserByIdResult{}
}

func (p *GetUserByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = passport_types.NewUser()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetUserByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetUserByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUserByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetUserByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserByIdResult(%+v)", *p)
}

type GetUserByEmailArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Email  string                `thrift:"email,2" json:"email"`
}

func NewGetUserByEmailArgs() *GetUserByEmailArgs {
	return &GetUserByEmailArgs{}
}

func (p *GetUserByEmailArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserByEmailArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetUserByEmailArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *GetUserByEmailArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserByEmail_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserByEmailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetUserByEmailArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *GetUserByEmailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserByEmailArgs(%+v)", *p)
}

type GetUserByEmailResult struct {
	Success *passport_types.User `thrift:"success,0" json:"success"`
	Ue      *UserException       `thrift:"ue,1" json:"ue"`
}

func NewGetUserByEmailResult() *GetUserByEmailResult {
	return &GetUserByEmailResult{}
}

func (p *GetUserByEmailResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserByEmailResult) readField0(iprot thrift.TProtocol) error {
	p.Success = passport_types.NewUser()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetUserByEmailResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetUserByEmailResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserByEmail_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserByEmailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUserByEmailResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetUserByEmailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserByEmailResult(%+v)", *p)
}

type ListUidsByRegTimeArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	StartTime int64                 `thrift:"startTime,2" json:"startTime"`
	EndTime   int64                 `thrift:"endTime,3" json:"endTime"`
	Offset    int32                 `thrift:"offset,4" json:"offset"`
	Limit     int32                 `thrift:"limit,5" json:"limit"`
	Ascending bool                  `thrift:"ascending,6" json:"ascending"`
}

func NewListUidsByRegTimeArgs() *ListUidsByRegTimeArgs {
	return &ListUidsByRegTimeArgs{}
}

func (p *ListUidsByRegTimeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByRegTime_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByRegTimeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByRegTimeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:startTime: %s", p, err)
	}
	return err
}

func (p *ListUidsByRegTimeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:endTime: %s", p, err)
	}
	return err
}

func (p *ListUidsByRegTimeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *ListUidsByRegTimeArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *ListUidsByRegTimeArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ascending: %s", p, err)
	}
	return err
}

func (p *ListUidsByRegTimeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByRegTimeArgs(%+v)", *p)
}

type ListUidsByRegTimeResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ue      *UserException      `thrift:"ue,1" json:"ue"`
}

func NewListUidsByRegTimeResult() *ListUidsByRegTimeResult {
	return &ListUidsByRegTimeResult{}
}

func (p *ListUidsByRegTimeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByRegTimeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListUidsByRegTimeResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ListUidsByRegTimeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByRegTime_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByRegTimeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByRegTimeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByRegTimeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByRegTimeResult(%+v)", *p)
}

type ListUidsByStatusArgs struct {
	Header    *common.RequestHeader     `thrift:"header,1" json:"header"`
	Status    passport_types.UserStatus `thrift:"status,2" json:"status"`
	Offset    int32                     `thrift:"offset,3" json:"offset"`
	Limit     int32                     `thrift:"limit,4" json:"limit"`
	Ascending bool                      `thrift:"ascending,5" json:"ascending"`
}

func NewListUidsByStatusArgs() *ListUidsByStatusArgs {
	return &ListUidsByStatusArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListUidsByStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ListUidsByStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListUidsByStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = passport_types.UserStatus(v)
	}
	return nil
}

func (p *ListUidsByStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListUidsByStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListUidsByStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListUidsByStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListUidsByStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListUidsByStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *ListUidsByStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByStatusArgs(%+v)", *p)
}

type ListUidsByStatusResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ue      *UserException      `thrift:"ue,1" json:"ue"`
}

func NewListUidsByStatusResult() *ListUidsByStatusResult {
	return &ListUidsByStatusResult{}
}

func (p *ListUidsByStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListUidsByStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ListUidsByStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByStatusResult(%+v)", *p)
}

type ListUidsByUserRoleArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	UserRole  enums.UserRole        `thrift:"userRole,2" json:"userRole"`
	Offset    int32                 `thrift:"offset,3" json:"offset"`
	Limit     int32                 `thrift:"limit,4" json:"limit"`
	Ascending bool                  `thrift:"ascending,5" json:"ascending"`
}

func NewListUidsByUserRoleArgs() *ListUidsByUserRoleArgs {
	return &ListUidsByUserRoleArgs{
		UserRole: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListUidsByUserRoleArgs) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *ListUidsByUserRoleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UserRole = enums.UserRole(v)
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByUserRole_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByUserRoleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByUserRoleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:userRole: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByUserRoleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListUidsByUserRoleArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListUidsByUserRoleArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *ListUidsByUserRoleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByUserRoleArgs(%+v)", *p)
}

type ListUidsByUserRoleResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ue      *UserException      `thrift:"ue,1" json:"ue"`
}

func NewListUidsByUserRoleResult() *ListUidsByUserRoleResult {
	return &ListUidsByUserRoleResult{}
}

func (p *ListUidsByUserRoleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByUserRoleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListUidsByUserRoleResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ListUidsByUserRoleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByUserRole_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByUserRoleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByUserRoleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByUserRoleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByUserRoleResult(%+v)", *p)
}

type ListUidsByAdminBanStatusArgs struct {
	Header         *common.RequestHeader         `thrift:"header,1" json:"header"`
	AdminBanStatus passport_types.AdminBanStatus `thrift:"adminBanStatus,2" json:"adminBanStatus"`
	Offset         int32                         `thrift:"offset,3" json:"offset"`
	Limit          int32                         `thrift:"limit,4" json:"limit"`
	Ascending      bool                          `thrift:"ascending,5" json:"ascending"`
}

func NewListUidsByAdminBanStatusArgs() *ListUidsByAdminBanStatusArgs {
	return &ListUidsByAdminBanStatusArgs{
		AdminBanStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListUidsByAdminBanStatusArgs) IsSetAdminBanStatus() bool {
	return int64(p.AdminBanStatus) != math.MinInt32-1
}

func (p *ListUidsByAdminBanStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdminBanStatus = passport_types.AdminBanStatus(v)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByAdminBanStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByAdminBanStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdminBanStatus() {
		if err := oprot.WriteFieldBegin("adminBanStatus", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:adminBanStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdminBanStatus)); err != nil {
			return fmt.Errorf("%T.adminBanStatus (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:adminBanStatus: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByAdminBanStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListUidsByAdminBanStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListUidsByAdminBanStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *ListUidsByAdminBanStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByAdminBanStatusArgs(%+v)", *p)
}

type ListUidsByAdminBanStatusResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ue      *UserException      `thrift:"ue,1" json:"ue"`
}

func NewListUidsByAdminBanStatusResult() *ListUidsByAdminBanStatusResult {
	return &ListUidsByAdminBanStatusResult{}
}

func (p *ListUidsByAdminBanStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByAdminBanStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByAdminBanStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByAdminBanStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByAdminBanStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByAdminBanStatusResult(%+v)", *p)
}

type ListUidsByExtInfoArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Key       string                `thrift:"key,2" json:"key"`
	Value     string                `thrift:"value,3" json:"value"`
	Offset    int32                 `thrift:"offset,4" json:"offset"`
	Limit     int32                 `thrift:"limit,5" json:"limit"`
	Ascending bool                  `thrift:"ascending,6" json:"ascending"`
}

func NewListUidsByExtInfoArgs() *ListUidsByExtInfoArgs {
	return &ListUidsByExtInfoArgs{}
}

func (p *ListUidsByExtInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Key = v
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByExtInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByExtInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByExtInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("key", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Key)); err != nil {
		return fmt.Errorf("%T.key (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:key: %s", p, err)
	}
	return err
}

func (p *ListUidsByExtInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("value", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:value: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Value)); err != nil {
		return fmt.Errorf("%T.value (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:value: %s", p, err)
	}
	return err
}

func (p *ListUidsByExtInfoArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *ListUidsByExtInfoArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *ListUidsByExtInfoArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ascending: %s", p, err)
	}
	return err
}

func (p *ListUidsByExtInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByExtInfoArgs(%+v)", *p)
}

type ListUidsByExtInfoResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ue      *UserException      `thrift:"ue,1" json:"ue"`
}

func NewListUidsByExtInfoResult() *ListUidsByExtInfoResult {
	return &ListUidsByExtInfoResult{}
}

func (p *ListUidsByExtInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByExtInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListUidsByExtInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ListUidsByExtInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUidsByExtInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUidsByExtInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByExtInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ListUidsByExtInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUidsByExtInfoResult(%+v)", *p)
}

type GetUserMetaByUidsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uids   []int32               `thrift:"uids,2" json:"uids"`
}

func NewGetUserMetaByUidsArgs() *GetUserMetaByUidsArgs {
	return &GetUserMetaByUidsArgs{}
}

func (p *GetUserMetaByUidsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserMetaByUidsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetUserMetaByUidsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem114 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem114 = v
		}
		p.Uids = append(p.Uids, _elem114)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetUserMetaByUidsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserMetaByUids_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserMetaByUidsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetUserMetaByUidsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:uids: %s", p, err)
		}
	}
	return err
}

func (p *GetUserMetaByUidsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserMetaByUidsArgs(%+v)", *p)
}

type GetUserMetaByUidsResult struct {
	Success map[int32]*passport_types.UserMeta `thrift:"success,0" json:"success"`
	Ue      *UserException                     `thrift:"ue,1" json:"ue"`
}

func NewGetUserMetaByUidsResult() *GetUserMetaByUidsResult {
	return &GetUserMetaByUidsResult{}
}

func (p *GetUserMetaByUidsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUserMetaByUidsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*passport_types.UserMeta, size)
	for i := 0; i < size; i++ {
		var _key115 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key115 = v
		}
		_val116 := passport_types.NewUserMeta()
		if err := _val116.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val116)
		}
		p.Success[_key115] = _val116
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetUserMetaByUidsResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetUserMetaByUidsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUserMetaByUids_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUserMetaByUidsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUserMetaByUidsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetUserMetaByUidsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserMetaByUidsResult(%+v)", *p)
}

type GetUsersByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetUsersByIdsArgs() *GetUsersByIdsArgs {
	return &GetUsersByIdsArgs{}
}

func (p *GetUsersByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetUsersByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem117 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem117 = v
		}
		p.Ids = append(p.Ids, _elem117)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetUsersByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUsersByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUsersByIdsArgs(%+v)", *p)
}

type GetUsersByIdsResult struct {
	Success map[int32]*passport_types.User `thrift:"success,0" json:"success"`
	Ue      *UserException                 `thrift:"ue,1" json:"ue"`
}

func NewGetUsersByIdsResult() *GetUsersByIdsResult {
	return &GetUsersByIdsResult{}
}

func (p *GetUsersByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*passport_types.User, size)
	for i := 0; i < size; i++ {
		var _key118 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key118 = v
		}
		_val119 := passport_types.NewUser()
		if err := _val119.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val119)
		}
		p.Success[_key118] = _val119
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetUsersByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetUsersByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUsersByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUsersByIdsResult(%+v)", *p)
}

type AdminBanUserArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uids   []int32               `thrift:"uids,2" json:"uids"`
}

func NewAdminBanUserArgs() *AdminBanUserArgs {
	return &AdminBanUserArgs{}
}

func (p *AdminBanUserArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminBanUserArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AdminBanUserArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem120 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem120 = v
		}
		p.Uids = append(p.Uids, _elem120)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdminBanUserArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminBanUser_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminBanUserArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AdminBanUserArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:uids: %s", p, err)
		}
	}
	return err
}

func (p *AdminBanUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminBanUserArgs(%+v)", *p)
}

type AdminBanUserResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewAdminBanUserResult() *AdminBanUserResult {
	return &AdminBanUserResult{}
}

func (p *AdminBanUserResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminBanUserResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *AdminBanUserResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminBanUser_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminBanUserResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *AdminBanUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminBanUserResult(%+v)", *p)
}

type AdminUnbanUserArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uids   []int32               `thrift:"uids,2" json:"uids"`
}

func NewAdminUnbanUserArgs() *AdminUnbanUserArgs {
	return &AdminUnbanUserArgs{}
}

func (p *AdminUnbanUserArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminUnbanUserArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AdminUnbanUserArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem121 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem121 = v
		}
		p.Uids = append(p.Uids, _elem121)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdminUnbanUserArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminUnbanUser_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminUnbanUserArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AdminUnbanUserArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:uids: %s", p, err)
		}
	}
	return err
}

func (p *AdminUnbanUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminUnbanUserArgs(%+v)", *p)
}

type AdminUnbanUserResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewAdminUnbanUserResult() *AdminUnbanUserResult {
	return &AdminUnbanUserResult{}
}

func (p *AdminUnbanUserResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdminUnbanUserResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *AdminUnbanUserResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("adminUnbanUser_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdminUnbanUserResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *AdminUnbanUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdminUnbanUserResult(%+v)", *p)
}

type ListManagedUidsByAgentUidArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid  int32                 `thrift:"agentUid,2" json:"agentUid"`
	Offset    int32                 `thrift:"offset,3" json:"offset"`
	Limit     int32                 `thrift:"limit,4" json:"limit"`
	Ascending bool                  `thrift:"ascending,5" json:"ascending"`
}

func NewListManagedUidsByAgentUidArgs() *ListManagedUidsByAgentUidArgs {
	return &ListManagedUidsByAgentUidArgs{}
}

func (p *ListManagedUidsByAgentUidArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listManagedUidsByAgentUid_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListManagedUidsByAgentUidArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *ListManagedUidsByAgentUidArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListManagedUidsByAgentUidArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListManagedUidsByAgentUidArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ascending: %s", p, err)
	}
	return err
}

func (p *ListManagedUidsByAgentUidArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListManagedUidsByAgentUidArgs(%+v)", *p)
}

type ListManagedUidsByAgentUidResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ue      *UserException      `thrift:"ue,1" json:"ue"`
}

func NewListManagedUidsByAgentUidResult() *ListManagedUidsByAgentUidResult {
	return &ListManagedUidsByAgentUidResult{}
}

func (p *ListManagedUidsByAgentUidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listManagedUidsByAgentUid_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListManagedUidsByAgentUidResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListManagedUidsByAgentUidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ListManagedUidsByAgentUidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListManagedUidsByAgentUidResult(%+v)", *p)
}

type ChangeUserRemarkArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
	Remark string                `thrift:"remark,3" json:"remark"`
}

func NewChangeUserRemarkArgs() *ChangeUserRemarkArgs {
	return &ChangeUserRemarkArgs{}
}

func (p *ChangeUserRemarkArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRemarkArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeUserRemarkArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ChangeUserRemarkArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *ChangeUserRemarkArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserRemark_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRemarkArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserRemarkArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ChangeUserRemarkArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:remark: %s", p, err)
	}
	return err
}

func (p *ChangeUserRemarkArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserRemarkArgs(%+v)", *p)
}

type ChangeUserRemarkResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeUserRemarkResult() *ChangeUserRemarkResult {
	return &ChangeUserRemarkResult{}
}

func (p *ChangeUserRemarkResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRemarkResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeUserRemarkResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserRemark_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserRemarkResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserRemarkResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserRemarkResult(%+v)", *p)
}

type ChangeUserEmailArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      int32                 `thrift:"uid,2" json:"uid"`
	OldEmail string                `thrift:"oldEmail,3" json:"oldEmail"`
	NewEmail string                `thrift:"newEmail,4" json:"newEmail"`
}

func NewChangeUserEmailArgs() *ChangeUserEmailArgs {
	return &ChangeUserEmailArgs{}
}

func (p *ChangeUserEmailArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserEmailArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeUserEmailArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ChangeUserEmailArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OldEmail = v
	}
	return nil
}

func (p *ChangeUserEmailArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.NewEmail = v
	}
	return nil
}

func (p *ChangeUserEmailArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserEmail_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserEmailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserEmailArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ChangeUserEmailArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oldEmail", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:oldEmail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OldEmail)); err != nil {
		return fmt.Errorf("%T.oldEmail (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:oldEmail: %s", p, err)
	}
	return err
}

func (p *ChangeUserEmailArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newEmail", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:newEmail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewEmail)); err != nil {
		return fmt.Errorf("%T.newEmail (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:newEmail: %s", p, err)
	}
	return err
}

func (p *ChangeUserEmailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserEmailArgs(%+v)", *p)
}

type ChangeUserEmailResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeUserEmailResult() *ChangeUserEmailResult {
	return &ChangeUserEmailResult{}
}

func (p *ChangeUserEmailResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserEmailResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeUserEmailResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeUserEmail_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeUserEmailResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeUserEmailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeUserEmailResult(%+v)", *p)
}

type ChangeSponsorTypeArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid         int32                 `thrift:"uid,2" json:"uid"`
	SponsorType enums.SponsorType     `thrift:"sponsorType,3" json:"sponsorType"`
}

func NewChangeSponsorTypeArgs() *ChangeSponsorTypeArgs {
	return &ChangeSponsorTypeArgs{
		SponsorType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ChangeSponsorTypeArgs) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *ChangeSponsorTypeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeSponsorTypeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ChangeSponsorTypeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ChangeSponsorTypeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorType = enums.SponsorType(v)
	}
	return nil
}

func (p *ChangeSponsorTypeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeSponsorType_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeSponsorTypeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ChangeSponsorTypeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ChangeSponsorTypeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSponsorType() {
		if err := oprot.WriteFieldBegin("sponsorType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sponsorType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
			return fmt.Errorf("%T.sponsorType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sponsorType: %s", p, err)
		}
	}
	return err
}

func (p *ChangeSponsorTypeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeSponsorTypeArgs(%+v)", *p)
}

type ChangeSponsorTypeResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewChangeSponsorTypeResult() *ChangeSponsorTypeResult {
	return &ChangeSponsorTypeResult{}
}

func (p *ChangeSponsorTypeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChangeSponsorTypeResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *ChangeSponsorTypeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("changeSponsorType_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChangeSponsorTypeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *ChangeSponsorTypeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChangeSponsorTypeResult(%+v)", *p)
}

type UpdateUserExtInfoArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid     int32                 `thrift:"uid,2" json:"uid"`
	ExtInfo map[string]string     `thrift:"extInfo,3" json:"extInfo"`
}

func NewUpdateUserExtInfoArgs() *UpdateUserExtInfoArgs {
	return &UpdateUserExtInfoArgs{}
}

func (p *UpdateUserExtInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateUserExtInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateUserExtInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UpdateUserExtInfoArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key122 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key122 = v
		}
		var _val123 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val123 = v
		}
		p.ExtInfo[_key122] = _val123
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UpdateUserExtInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateUserExtInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateUserExtInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateUserExtInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *UpdateUserExtInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *UpdateUserExtInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateUserExtInfoArgs(%+v)", *p)
}

type UpdateUserExtInfoResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewUpdateUserExtInfoResult() *UpdateUserExtInfoResult {
	return &UpdateUserExtInfoResult{}
}

func (p *UpdateUserExtInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateUserExtInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *UpdateUserExtInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateUserExtInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateUserExtInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *UpdateUserExtInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateUserExtInfoResult(%+v)", *p)
}

type DeleteUserExtInfoArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    int32                 `thrift:"uid,2" json:"uid"`
	Keys   map[string]bool       `thrift:"keys,3" json:"keys"`
}

func NewDeleteUserExtInfoArgs() *DeleteUserExtInfoArgs {
	return &DeleteUserExtInfoArgs{}
}

func (p *DeleteUserExtInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.SET {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteUserExtInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeleteUserExtInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *DeleteUserExtInfoArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadSetBegin()
	if err != nil {
		return fmt.Errorf("error reading set being: %s")
	}
	p.Keys = make(map[string]bool, size)
	for i := 0; i < size; i++ {
		var _elem124 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem124 = v
		}
		p.Keys[_elem124] = true
	}
	if err := iprot.ReadSetEnd(); err != nil {
		return fmt.Errorf("error reading set end: %s", err)
	}
	return nil
}

func (p *DeleteUserExtInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteUserExtInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteUserExtInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeleteUserExtInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *DeleteUserExtInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Keys != nil {
		if err := oprot.WriteFieldBegin("keys", thrift.SET, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:keys: %s", p, err)
		}
		if err := oprot.WriteSetBegin(thrift.STRING, len(p.Keys)); err != nil {
			return fmt.Errorf("error writing set begin: %s")
		}
		for v, _ := range p.Keys {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteSetEnd(); err != nil {
			return fmt.Errorf("error writing set end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:keys: %s", p, err)
		}
	}
	return err
}

func (p *DeleteUserExtInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteUserExtInfoArgs(%+v)", *p)
}

type DeleteUserExtInfoResult struct {
	Ue *UserException `thrift:"ue,1" json:"ue"`
}

func NewDeleteUserExtInfoResult() *DeleteUserExtInfoResult {
	return &DeleteUserExtInfoResult{}
}

func (p *DeleteUserExtInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteUserExtInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewUserException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *DeleteUserExtInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteUserExtInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteUserExtInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *DeleteUserExtInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteUserExtInfoResult(%+v)", *p)
}
