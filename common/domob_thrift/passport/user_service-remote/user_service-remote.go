// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"passport"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 registerUser(RequestHeader header, UserRegInfo regInfo)")
	fmt.Fprintln(os.Stderr, "  bool isUserEmailAvailable(RequestHeader header, string email)")
	fmt.Fprintln(os.<PERSON>, "  void activateUser(RequestHeader header, i32 uid)")
	fmt.Fprintln(os.Stderr, "  void verifyUserPassword(RequestHeader header, string email, string password)")
	fmt.Fprintln(os.Stderr, "  void changeUserPassword(RequestHeader header, i32 uid, string newPassword, string oldPassword)")
	fmt.Fprintln(os.Stderr, "  void resetPassword(RequestHeader header, i32 uid, string newPassword)")
	fmt.Fprintln(os.Stderr, "  void addRoleToUser(RequestHeader header, i32 uid, UserRole userRole)")
	fmt.Fprintln(os.Stderr, "  void changeUserRole(RequestHeader header, i32 uid, UserRole userRole)")
	fmt.Fprintln(os.Stderr, "  void changeAderProfile(RequestHeader header, UserAderProfile aderProfile)")
	fmt.Fprintln(os.Stderr, "  void changeDeverProfile(RequestHeader header, UserDeverProfile deverProfile)")
	fmt.Fprintln(os.Stderr, "  void changeExtProfile(RequestHeader header, UserExtProfile extProfile)")
	fmt.Fprintln(os.Stderr, "  User getUserById(RequestHeader header, i32 uid)")
	fmt.Fprintln(os.Stderr, "  User getUserByEmail(RequestHeader header, string email)")
	fmt.Fprintln(os.Stderr, "  QueryResult listUidsByRegTime(RequestHeader header, i64 startTime, i64 endTime, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  QueryResult listUidsByStatus(RequestHeader header, UserStatus status, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  QueryResult listUidsByUserRole(RequestHeader header, UserRole userRole, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  QueryResult listUidsByAdminBanStatus(RequestHeader header, AdminBanStatus adminBanStatus, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  QueryResult listUidsByExtInfo(RequestHeader header, string key, string value, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getUserMetaByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "   getUsersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void adminBanUser(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void adminUnbanUser(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  QueryResult listManagedUidsByAgentUid(RequestHeader header, i32 agentUid, i32 offset, i32 limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  void changeUserRemark(RequestHeader header, i32 uid, string remark)")
	fmt.Fprintln(os.Stderr, "  void changeUserEmail(RequestHeader header, i32 uid, string oldEmail, string newEmail)")
	fmt.Fprintln(os.Stderr, "  void changeSponsorType(RequestHeader header, i32 uid, SponsorType sponsorType)")
	fmt.Fprintln(os.Stderr, "  void updateUserExtInfo(RequestHeader header, i32 uid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  void deleteUserExtInfo(RequestHeader header, i32 uid,  keys)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := passport.NewUserServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "registerUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RegisterUser requires 2 args")
			flag.Usage()
		}
		arg125 := flag.Arg(1)
		mbTrans126 := thrift.NewTMemoryBufferLen(len(arg125))
		defer mbTrans126.Close()
		_, err127 := mbTrans126.WriteString(arg125)
		if err127 != nil {
			Usage()
			return
		}
		factory128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt129 := factory128.GetProtocol(mbTrans126)
		argvalue0 := passport.NewRequestHeader()
		err130 := argvalue0.Read(jsProt129)
		if err130 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg131 := flag.Arg(2)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue1 := passport.NewUserRegInfo()
		err136 := argvalue1.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.RegisterUser(value0, value1))
		fmt.Print("\n")
		break
	case "isUserEmailAvailable":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "IsUserEmailAvailable requires 2 args")
			flag.Usage()
		}
		arg137 := flag.Arg(1)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		argvalue0 := passport.NewRequestHeader()
		err142 := argvalue0.Read(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.IsUserEmailAvailable(value0, value1))
		fmt.Print("\n")
		break
	case "activateUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ActivateUser requires 2 args")
			flag.Usage()
		}
		arg144 := flag.Arg(1)
		mbTrans145 := thrift.NewTMemoryBufferLen(len(arg144))
		defer mbTrans145.Close()
		_, err146 := mbTrans145.WriteString(arg144)
		if err146 != nil {
			Usage()
			return
		}
		factory147 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt148 := factory147.GetProtocol(mbTrans145)
		argvalue0 := passport.NewRequestHeader()
		err149 := argvalue0.Read(jsProt148)
		if err149 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err150 := (strconv.Atoi(flag.Arg(2)))
		if err150 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.ActivateUser(value0, value1))
		fmt.Print("\n")
		break
	case "verifyUserPassword":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "VerifyUserPassword requires 3 args")
			flag.Usage()
		}
		arg151 := flag.Arg(1)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		argvalue0 := passport.NewRequestHeader()
		err156 := argvalue0.Read(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.VerifyUserPassword(value0, value1, value2))
		fmt.Print("\n")
		break
	case "changeUserPassword":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ChangeUserPassword requires 4 args")
			flag.Usage()
		}
		arg159 := flag.Arg(1)
		mbTrans160 := thrift.NewTMemoryBufferLen(len(arg159))
		defer mbTrans160.Close()
		_, err161 := mbTrans160.WriteString(arg159)
		if err161 != nil {
			Usage()
			return
		}
		factory162 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt163 := factory162.GetProtocol(mbTrans160)
		argvalue0 := passport.NewRequestHeader()
		err164 := argvalue0.Read(jsProt163)
		if err164 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err165 := (strconv.Atoi(flag.Arg(2)))
		if err165 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.ChangeUserPassword(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resetPassword":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResetPassword requires 3 args")
			flag.Usage()
		}
		arg168 := flag.Arg(1)
		mbTrans169 := thrift.NewTMemoryBufferLen(len(arg168))
		defer mbTrans169.Close()
		_, err170 := mbTrans169.WriteString(arg168)
		if err170 != nil {
			Usage()
			return
		}
		factory171 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt172 := factory171.GetProtocol(mbTrans169)
		argvalue0 := passport.NewRequestHeader()
		err173 := argvalue0.Read(jsProt172)
		if err173 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err174 := (strconv.Atoi(flag.Arg(2)))
		if err174 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.ResetPassword(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addRoleToUser":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddRoleToUser requires 3 args")
			flag.Usage()
		}
		arg176 := flag.Arg(1)
		mbTrans177 := thrift.NewTMemoryBufferLen(len(arg176))
		defer mbTrans177.Close()
		_, err178 := mbTrans177.WriteString(arg176)
		if err178 != nil {
			Usage()
			return
		}
		factory179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt180 := factory179.GetProtocol(mbTrans177)
		argvalue0 := passport.NewRequestHeader()
		err181 := argvalue0.Read(jsProt180)
		if err181 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err182 := (strconv.Atoi(flag.Arg(2)))
		if err182 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := passport.UserRole(tmp2)
		value2 := argvalue2
		fmt.Print(client.AddRoleToUser(value0, value1, value2))
		fmt.Print("\n")
		break
	case "changeUserRole":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ChangeUserRole requires 3 args")
			flag.Usage()
		}
		arg183 := flag.Arg(1)
		mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
		defer mbTrans184.Close()
		_, err185 := mbTrans184.WriteString(arg183)
		if err185 != nil {
			Usage()
			return
		}
		factory186 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt187 := factory186.GetProtocol(mbTrans184)
		argvalue0 := passport.NewRequestHeader()
		err188 := argvalue0.Read(jsProt187)
		if err188 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err189 := (strconv.Atoi(flag.Arg(2)))
		if err189 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := passport.UserRole(tmp2)
		value2 := argvalue2
		fmt.Print(client.ChangeUserRole(value0, value1, value2))
		fmt.Print("\n")
		break
	case "changeAderProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ChangeAderProfile requires 2 args")
			flag.Usage()
		}
		arg190 := flag.Arg(1)
		mbTrans191 := thrift.NewTMemoryBufferLen(len(arg190))
		defer mbTrans191.Close()
		_, err192 := mbTrans191.WriteString(arg190)
		if err192 != nil {
			Usage()
			return
		}
		factory193 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt194 := factory193.GetProtocol(mbTrans191)
		argvalue0 := passport.NewRequestHeader()
		err195 := argvalue0.Read(jsProt194)
		if err195 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg196 := flag.Arg(2)
		mbTrans197 := thrift.NewTMemoryBufferLen(len(arg196))
		defer mbTrans197.Close()
		_, err198 := mbTrans197.WriteString(arg196)
		if err198 != nil {
			Usage()
			return
		}
		factory199 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt200 := factory199.GetProtocol(mbTrans197)
		argvalue1 := passport.NewUserAderProfile()
		err201 := argvalue1.Read(jsProt200)
		if err201 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ChangeAderProfile(value0, value1))
		fmt.Print("\n")
		break
	case "changeDeverProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ChangeDeverProfile requires 2 args")
			flag.Usage()
		}
		arg202 := flag.Arg(1)
		mbTrans203 := thrift.NewTMemoryBufferLen(len(arg202))
		defer mbTrans203.Close()
		_, err204 := mbTrans203.WriteString(arg202)
		if err204 != nil {
			Usage()
			return
		}
		factory205 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt206 := factory205.GetProtocol(mbTrans203)
		argvalue0 := passport.NewRequestHeader()
		err207 := argvalue0.Read(jsProt206)
		if err207 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg208 := flag.Arg(2)
		mbTrans209 := thrift.NewTMemoryBufferLen(len(arg208))
		defer mbTrans209.Close()
		_, err210 := mbTrans209.WriteString(arg208)
		if err210 != nil {
			Usage()
			return
		}
		factory211 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt212 := factory211.GetProtocol(mbTrans209)
		argvalue1 := passport.NewUserDeverProfile()
		err213 := argvalue1.Read(jsProt212)
		if err213 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ChangeDeverProfile(value0, value1))
		fmt.Print("\n")
		break
	case "changeExtProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ChangeExtProfile requires 2 args")
			flag.Usage()
		}
		arg214 := flag.Arg(1)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		argvalue0 := passport.NewRequestHeader()
		err219 := argvalue0.Read(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg220 := flag.Arg(2)
		mbTrans221 := thrift.NewTMemoryBufferLen(len(arg220))
		defer mbTrans221.Close()
		_, err222 := mbTrans221.WriteString(arg220)
		if err222 != nil {
			Usage()
			return
		}
		factory223 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt224 := factory223.GetProtocol(mbTrans221)
		argvalue1 := passport.NewUserExtProfile()
		err225 := argvalue1.Read(jsProt224)
		if err225 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ChangeExtProfile(value0, value1))
		fmt.Print("\n")
		break
	case "getUserById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUserById requires 2 args")
			flag.Usage()
		}
		arg226 := flag.Arg(1)
		mbTrans227 := thrift.NewTMemoryBufferLen(len(arg226))
		defer mbTrans227.Close()
		_, err228 := mbTrans227.WriteString(arg226)
		if err228 != nil {
			Usage()
			return
		}
		factory229 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt230 := factory229.GetProtocol(mbTrans227)
		argvalue0 := passport.NewRequestHeader()
		err231 := argvalue0.Read(jsProt230)
		if err231 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err232 := (strconv.Atoi(flag.Arg(2)))
		if err232 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetUserById(value0, value1))
		fmt.Print("\n")
		break
	case "getUserByEmail":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUserByEmail requires 2 args")
			flag.Usage()
		}
		arg233 := flag.Arg(1)
		mbTrans234 := thrift.NewTMemoryBufferLen(len(arg233))
		defer mbTrans234.Close()
		_, err235 := mbTrans234.WriteString(arg233)
		if err235 != nil {
			Usage()
			return
		}
		factory236 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt237 := factory236.GetProtocol(mbTrans234)
		argvalue0 := passport.NewRequestHeader()
		err238 := argvalue0.Read(jsProt237)
		if err238 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetUserByEmail(value0, value1))
		fmt.Print("\n")
		break
	case "listUidsByRegTime":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListUidsByRegTime requires 6 args")
			flag.Usage()
		}
		arg240 := flag.Arg(1)
		mbTrans241 := thrift.NewTMemoryBufferLen(len(arg240))
		defer mbTrans241.Close()
		_, err242 := mbTrans241.WriteString(arg240)
		if err242 != nil {
			Usage()
			return
		}
		factory243 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt244 := factory243.GetProtocol(mbTrans241)
		argvalue0 := passport.NewRequestHeader()
		err245 := argvalue0.Read(jsProt244)
		if err245 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err246 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err246 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err247 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err247 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err248 := (strconv.Atoi(flag.Arg(4)))
		if err248 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err249 := (strconv.Atoi(flag.Arg(5)))
		if err249 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListUidsByRegTime(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listUidsByStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListUidsByStatus requires 5 args")
			flag.Usage()
		}
		arg251 := flag.Arg(1)
		mbTrans252 := thrift.NewTMemoryBufferLen(len(arg251))
		defer mbTrans252.Close()
		_, err253 := mbTrans252.WriteString(arg251)
		if err253 != nil {
			Usage()
			return
		}
		factory254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt255 := factory254.GetProtocol(mbTrans252)
		argvalue0 := passport.NewRequestHeader()
		err256 := argvalue0.Read(jsProt255)
		if err256 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := passport.UserStatus(tmp1)
		value1 := argvalue1
		tmp2, err257 := (strconv.Atoi(flag.Arg(3)))
		if err257 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err258 := (strconv.Atoi(flag.Arg(4)))
		if err258 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListUidsByStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listUidsByUserRole":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListUidsByUserRole requires 5 args")
			flag.Usage()
		}
		arg260 := flag.Arg(1)
		mbTrans261 := thrift.NewTMemoryBufferLen(len(arg260))
		defer mbTrans261.Close()
		_, err262 := mbTrans261.WriteString(arg260)
		if err262 != nil {
			Usage()
			return
		}
		factory263 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt264 := factory263.GetProtocol(mbTrans261)
		argvalue0 := passport.NewRequestHeader()
		err265 := argvalue0.Read(jsProt264)
		if err265 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := passport.UserRole(tmp1)
		value1 := argvalue1
		tmp2, err266 := (strconv.Atoi(flag.Arg(3)))
		if err266 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err267 := (strconv.Atoi(flag.Arg(4)))
		if err267 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListUidsByUserRole(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listUidsByAdminBanStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListUidsByAdminBanStatus requires 5 args")
			flag.Usage()
		}
		arg269 := flag.Arg(1)
		mbTrans270 := thrift.NewTMemoryBufferLen(len(arg269))
		defer mbTrans270.Close()
		_, err271 := mbTrans270.WriteString(arg269)
		if err271 != nil {
			Usage()
			return
		}
		factory272 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt273 := factory272.GetProtocol(mbTrans270)
		argvalue0 := passport.NewRequestHeader()
		err274 := argvalue0.Read(jsProt273)
		if err274 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := passport.AdminBanStatus(tmp1)
		value1 := argvalue1
		tmp2, err275 := (strconv.Atoi(flag.Arg(3)))
		if err275 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err276 := (strconv.Atoi(flag.Arg(4)))
		if err276 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListUidsByAdminBanStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listUidsByExtInfo":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListUidsByExtInfo requires 6 args")
			flag.Usage()
		}
		arg278 := flag.Arg(1)
		mbTrans279 := thrift.NewTMemoryBufferLen(len(arg278))
		defer mbTrans279.Close()
		_, err280 := mbTrans279.WriteString(arg278)
		if err280 != nil {
			Usage()
			return
		}
		factory281 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt282 := factory281.GetProtocol(mbTrans279)
		argvalue0 := passport.NewRequestHeader()
		err283 := argvalue0.Read(jsProt282)
		if err283 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		tmp3, err286 := (strconv.Atoi(flag.Arg(4)))
		if err286 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err287 := (strconv.Atoi(flag.Arg(5)))
		if err287 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListUidsByExtInfo(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getUserMetaByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUserMetaByUids requires 2 args")
			flag.Usage()
		}
		arg289 := flag.Arg(1)
		mbTrans290 := thrift.NewTMemoryBufferLen(len(arg289))
		defer mbTrans290.Close()
		_, err291 := mbTrans290.WriteString(arg289)
		if err291 != nil {
			Usage()
			return
		}
		factory292 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt293 := factory292.GetProtocol(mbTrans290)
		argvalue0 := passport.NewRequestHeader()
		err294 := argvalue0.Read(jsProt293)
		if err294 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg295 := flag.Arg(2)
		mbTrans296 := thrift.NewTMemoryBufferLen(len(arg295))
		defer mbTrans296.Close()
		_, err297 := mbTrans296.WriteString(arg295)
		if err297 != nil {
			Usage()
			return
		}
		factory298 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt299 := factory298.GetProtocol(mbTrans296)
		containerStruct1 := passport.NewGetUserMetaByUidsArgs()
		err300 := containerStruct1.ReadField2(jsProt299)
		if err300 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetUserMetaByUids(value0, value1))
		fmt.Print("\n")
		break
	case "getUsersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUsersByIds requires 2 args")
			flag.Usage()
		}
		arg301 := flag.Arg(1)
		mbTrans302 := thrift.NewTMemoryBufferLen(len(arg301))
		defer mbTrans302.Close()
		_, err303 := mbTrans302.WriteString(arg301)
		if err303 != nil {
			Usage()
			return
		}
		factory304 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt305 := factory304.GetProtocol(mbTrans302)
		argvalue0 := passport.NewRequestHeader()
		err306 := argvalue0.Read(jsProt305)
		if err306 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg307 := flag.Arg(2)
		mbTrans308 := thrift.NewTMemoryBufferLen(len(arg307))
		defer mbTrans308.Close()
		_, err309 := mbTrans308.WriteString(arg307)
		if err309 != nil {
			Usage()
			return
		}
		factory310 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt311 := factory310.GetProtocol(mbTrans308)
		containerStruct1 := passport.NewGetUsersByIdsArgs()
		err312 := containerStruct1.ReadField2(jsProt311)
		if err312 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetUsersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "adminBanUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AdminBanUser requires 2 args")
			flag.Usage()
		}
		arg313 := flag.Arg(1)
		mbTrans314 := thrift.NewTMemoryBufferLen(len(arg313))
		defer mbTrans314.Close()
		_, err315 := mbTrans314.WriteString(arg313)
		if err315 != nil {
			Usage()
			return
		}
		factory316 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt317 := factory316.GetProtocol(mbTrans314)
		argvalue0 := passport.NewRequestHeader()
		err318 := argvalue0.Read(jsProt317)
		if err318 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg319 := flag.Arg(2)
		mbTrans320 := thrift.NewTMemoryBufferLen(len(arg319))
		defer mbTrans320.Close()
		_, err321 := mbTrans320.WriteString(arg319)
		if err321 != nil {
			Usage()
			return
		}
		factory322 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt323 := factory322.GetProtocol(mbTrans320)
		containerStruct1 := passport.NewAdminBanUserArgs()
		err324 := containerStruct1.ReadField2(jsProt323)
		if err324 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.AdminBanUser(value0, value1))
		fmt.Print("\n")
		break
	case "adminUnbanUser":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AdminUnbanUser requires 2 args")
			flag.Usage()
		}
		arg325 := flag.Arg(1)
		mbTrans326 := thrift.NewTMemoryBufferLen(len(arg325))
		defer mbTrans326.Close()
		_, err327 := mbTrans326.WriteString(arg325)
		if err327 != nil {
			Usage()
			return
		}
		factory328 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt329 := factory328.GetProtocol(mbTrans326)
		argvalue0 := passport.NewRequestHeader()
		err330 := argvalue0.Read(jsProt329)
		if err330 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg331 := flag.Arg(2)
		mbTrans332 := thrift.NewTMemoryBufferLen(len(arg331))
		defer mbTrans332.Close()
		_, err333 := mbTrans332.WriteString(arg331)
		if err333 != nil {
			Usage()
			return
		}
		factory334 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt335 := factory334.GetProtocol(mbTrans332)
		containerStruct1 := passport.NewAdminUnbanUserArgs()
		err336 := containerStruct1.ReadField2(jsProt335)
		if err336 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.AdminUnbanUser(value0, value1))
		fmt.Print("\n")
		break
	case "listManagedUidsByAgentUid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListManagedUidsByAgentUid requires 5 args")
			flag.Usage()
		}
		arg337 := flag.Arg(1)
		mbTrans338 := thrift.NewTMemoryBufferLen(len(arg337))
		defer mbTrans338.Close()
		_, err339 := mbTrans338.WriteString(arg337)
		if err339 != nil {
			Usage()
			return
		}
		factory340 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt341 := factory340.GetProtocol(mbTrans338)
		argvalue0 := passport.NewRequestHeader()
		err342 := argvalue0.Read(jsProt341)
		if err342 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err343 := (strconv.Atoi(flag.Arg(2)))
		if err343 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err344 := (strconv.Atoi(flag.Arg(3)))
		if err344 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err345 := (strconv.Atoi(flag.Arg(4)))
		if err345 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListManagedUidsByAgentUid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "changeUserRemark":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ChangeUserRemark requires 3 args")
			flag.Usage()
		}
		arg347 := flag.Arg(1)
		mbTrans348 := thrift.NewTMemoryBufferLen(len(arg347))
		defer mbTrans348.Close()
		_, err349 := mbTrans348.WriteString(arg347)
		if err349 != nil {
			Usage()
			return
		}
		factory350 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt351 := factory350.GetProtocol(mbTrans348)
		argvalue0 := passport.NewRequestHeader()
		err352 := argvalue0.Read(jsProt351)
		if err352 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err353 := (strconv.Atoi(flag.Arg(2)))
		if err353 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.ChangeUserRemark(value0, value1, value2))
		fmt.Print("\n")
		break
	case "changeUserEmail":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ChangeUserEmail requires 4 args")
			flag.Usage()
		}
		arg355 := flag.Arg(1)
		mbTrans356 := thrift.NewTMemoryBufferLen(len(arg355))
		defer mbTrans356.Close()
		_, err357 := mbTrans356.WriteString(arg355)
		if err357 != nil {
			Usage()
			return
		}
		factory358 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt359 := factory358.GetProtocol(mbTrans356)
		argvalue0 := passport.NewRequestHeader()
		err360 := argvalue0.Read(jsProt359)
		if err360 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err361 := (strconv.Atoi(flag.Arg(2)))
		if err361 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.ChangeUserEmail(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "changeSponsorType":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ChangeSponsorType requires 3 args")
			flag.Usage()
		}
		arg364 := flag.Arg(1)
		mbTrans365 := thrift.NewTMemoryBufferLen(len(arg364))
		defer mbTrans365.Close()
		_, err366 := mbTrans365.WriteString(arg364)
		if err366 != nil {
			Usage()
			return
		}
		factory367 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt368 := factory367.GetProtocol(mbTrans365)
		argvalue0 := passport.NewRequestHeader()
		err369 := argvalue0.Read(jsProt368)
		if err369 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err370 := (strconv.Atoi(flag.Arg(2)))
		if err370 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := passport.SponsorType(tmp2)
		value2 := argvalue2
		fmt.Print(client.ChangeSponsorType(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateUserExtInfo":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateUserExtInfo requires 3 args")
			flag.Usage()
		}
		arg371 := flag.Arg(1)
		mbTrans372 := thrift.NewTMemoryBufferLen(len(arg371))
		defer mbTrans372.Close()
		_, err373 := mbTrans372.WriteString(arg371)
		if err373 != nil {
			Usage()
			return
		}
		factory374 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt375 := factory374.GetProtocol(mbTrans372)
		argvalue0 := passport.NewRequestHeader()
		err376 := argvalue0.Read(jsProt375)
		if err376 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err377 := (strconv.Atoi(flag.Arg(2)))
		if err377 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg378 := flag.Arg(3)
		mbTrans379 := thrift.NewTMemoryBufferLen(len(arg378))
		defer mbTrans379.Close()
		_, err380 := mbTrans379.WriteString(arg378)
		if err380 != nil {
			Usage()
			return
		}
		factory381 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt382 := factory381.GetProtocol(mbTrans379)
		containerStruct2 := passport.NewUpdateUserExtInfoArgs()
		err383 := containerStruct2.ReadField3(jsProt382)
		if err383 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ExtInfo
		value2 := argvalue2
		fmt.Print(client.UpdateUserExtInfo(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteUserExtInfo":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteUserExtInfo requires 3 args")
			flag.Usage()
		}
		arg384 := flag.Arg(1)
		mbTrans385 := thrift.NewTMemoryBufferLen(len(arg384))
		defer mbTrans385.Close()
		_, err386 := mbTrans385.WriteString(arg384)
		if err386 != nil {
			Usage()
			return
		}
		factory387 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt388 := factory387.GetProtocol(mbTrans385)
		argvalue0 := passport.NewRequestHeader()
		err389 := argvalue0.Read(jsProt388)
		if err389 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err390 := (strconv.Atoi(flag.Arg(2)))
		if err390 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg391 := flag.Arg(3)
		mbTrans392 := thrift.NewTMemoryBufferLen(len(arg391))
		defer mbTrans392.Close()
		_, err393 := mbTrans392.WriteString(arg391)
		if err393 != nil {
			Usage()
			return
		}
		factory394 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt395 := factory394.GetProtocol(mbTrans392)
		containerStruct2 := passport.NewDeleteUserExtInfoArgs()
		err396 := containerStruct2.ReadField3(jsProt395)
		if err396 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Keys
		value2 := argvalue2
		fmt.Print(client.DeleteUserExtInfo(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
