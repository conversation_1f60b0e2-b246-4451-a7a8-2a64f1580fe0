// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"bidmaster_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addSponsor(RequestHeader header, SponsorProfile sponsor)")
	fmt.Fprintln(os.Stderr, "  i32 addSponsorWithPassword(RequestHeader header, SponsorProfile sponsor, string password)")
	fmt.Fprintln(os.<PERSON>derr, "  void editSponsor(RequestHeader header, i32 agentUid, SponsorProfile sponsor)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchSponsorsByParams(RequestHeader header, SponsorParams params)")
	fmt.Fprintln(os.Stderr, "   getSponsorsByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void innerApproveSponsor(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  void editSponsorAuditMaterials(RequestHeader header, i32 id,  materials)")
	fmt.Fprintln(os.Stderr, "  void innerApproveAuditMaterials(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  i32 addAdOrder(RequestHeader header, i32 agentUid, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "  void editAdOrder(RequestHeader header, i32 agentUid, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "   getAdOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdOrdersByParams(RequestHeader header, AdOrderParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdOrdersByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdOrdersByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdOrderByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdPromotion(RequestHeader header, i32 sponsorId, AdPromotion promotion)")
	fmt.Fprintln(os.Stderr, "  void editAdPromotion(RequestHeader header, i32 sponsorId, AdPromotion promotion)")
	fmt.Fprintln(os.Stderr, "   getAdPromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdPromotionByParams(RequestHeader header, AdPromotionParams params)")
	fmt.Fprintln(os.Stderr, "  void deleteAdPromotionsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdCampaign(RequestHeader header, i32 agentUid, AdCampaign campaign)")
	fmt.Fprintln(os.Stderr, "  void editAdCampaign(RequestHeader header, i32 agentUid, AdCampaign campaign)")
	fmt.Fprintln(os.Stderr, "   getAdCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdCampaignByParams(RequestHeader header, AdCampaignParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCampaignsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCampaignsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCampaignsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void editCampaignActualDailyBudget(RequestHeader header, i32 campaignId, i64 actualDailyBudget)")
	fmt.Fprintln(os.Stderr, "  i32 addAdStrategy(RequestHeader header, i32 agentUid, AdStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editAdStrategy(RequestHeader header, i32 agentUid, AdStrategy strategy)")
	fmt.Fprintln(os.Stderr, "   getAdStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdStrategyByParams(RequestHeader header, AdStrategyParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdCreative(RequestHeader header, i32 agentUid, AdCreative creative)")
	fmt.Fprintln(os.Stderr, "  void editAdCreative(RequestHeader header, i32 agentUid, AdCreative creative)")
	fmt.Fprintln(os.Stderr, "   getAdCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdCreativeByParams(RequestHeader header, AdCreativeParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void innerApproveAdCreatives(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  void editCampaignMinProfitRatio(RequestHeader header,  campaignIds, i32 minProfitRatio)")
	fmt.Fprintln(os.Stderr, "   getOrderIdsByProjectIds(RequestHeader header,  projectIds)")
	fmt.Fprintln(os.Stderr, "  void debugAdTracking(RequestHeader header, i32 sponsorId, i32 promotionId, string deviceId, i32 adTrackingId)")
	fmt.Fprintln(os.Stderr, "  i32 addAdExchangeDmp(RequestHeader header, i32 sponsorId, AdExchangeDmp adExchangeDmp)")
	fmt.Fprintln(os.Stderr, "  void editAdExchangeDmp(RequestHeader header, i32 sponsorId, AdExchangeDmp adExchangeDmp)")
	fmt.Fprintln(os.Stderr, "  void deleteAdExchangeDmpByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "   getAdExchangeDmpByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdExchangeDmpByParams(RequestHeader header, AdExchangeDmpParams params)")
	fmt.Fprintln(os.Stderr, "   searchAdSuggestPrice(RequestHeader header, i32 exchangeId, i32 platform)")
	fmt.Fprintln(os.Stderr, "  i32 addAdTracking(RequestHeader header, AdTracking adTracking)")
	fmt.Fprintln(os.Stderr, "  void editAdTracking(RequestHeader header, AdTracking adTracking)")
	fmt.Fprintln(os.Stderr, "   getAdTrackingsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdTrackingByParams(RequestHeader header, AdTrackingParams params)")
	fmt.Fprintln(os.Stderr, "  void deleteAdTrackingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addDeviceFile(RequestHeader header, DeviceFile deviceFile)")
	fmt.Fprintln(os.Stderr, "   getDeviceFilesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := bidmaster_server.NewBidMasterServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSponsor requires 2 args")
			flag.Usage()
		}
		arg293 := flag.Arg(1)
		mbTrans294 := thrift.NewTMemoryBufferLen(len(arg293))
		defer mbTrans294.Close()
		_, err295 := mbTrans294.WriteString(arg293)
		if err295 != nil {
			Usage()
			return
		}
		factory296 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt297 := factory296.GetProtocol(mbTrans294)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err298 := argvalue0.Read(jsProt297)
		if err298 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg299 := flag.Arg(2)
		mbTrans300 := thrift.NewTMemoryBufferLen(len(arg299))
		defer mbTrans300.Close()
		_, err301 := mbTrans300.WriteString(arg299)
		if err301 != nil {
			Usage()
			return
		}
		factory302 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt303 := factory302.GetProtocol(mbTrans300)
		argvalue1 := bidmaster_server.NewSponsorProfile()
		err304 := argvalue1.Read(jsProt303)
		if err304 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "addSponsorWithPassword":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddSponsorWithPassword requires 3 args")
			flag.Usage()
		}
		arg305 := flag.Arg(1)
		mbTrans306 := thrift.NewTMemoryBufferLen(len(arg305))
		defer mbTrans306.Close()
		_, err307 := mbTrans306.WriteString(arg305)
		if err307 != nil {
			Usage()
			return
		}
		factory308 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt309 := factory308.GetProtocol(mbTrans306)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err310 := argvalue0.Read(jsProt309)
		if err310 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg311 := flag.Arg(2)
		mbTrans312 := thrift.NewTMemoryBufferLen(len(arg311))
		defer mbTrans312.Close()
		_, err313 := mbTrans312.WriteString(arg311)
		if err313 != nil {
			Usage()
			return
		}
		factory314 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt315 := factory314.GetProtocol(mbTrans312)
		argvalue1 := bidmaster_server.NewSponsorProfile()
		err316 := argvalue1.Read(jsProt315)
		if err316 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.AddSponsorWithPassword(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editSponsor":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSponsor requires 3 args")
			flag.Usage()
		}
		arg318 := flag.Arg(1)
		mbTrans319 := thrift.NewTMemoryBufferLen(len(arg318))
		defer mbTrans319.Close()
		_, err320 := mbTrans319.WriteString(arg318)
		if err320 != nil {
			Usage()
			return
		}
		factory321 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt322 := factory321.GetProtocol(mbTrans319)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err323 := argvalue0.Read(jsProt322)
		if err323 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err324 := (strconv.Atoi(flag.Arg(2)))
		if err324 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg325 := flag.Arg(3)
		mbTrans326 := thrift.NewTMemoryBufferLen(len(arg325))
		defer mbTrans326.Close()
		_, err327 := mbTrans326.WriteString(arg325)
		if err327 != nil {
			Usage()
			return
		}
		factory328 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt329 := factory328.GetProtocol(mbTrans326)
		argvalue2 := bidmaster_server.NewSponsorProfile()
		err330 := argvalue2.Read(jsProt329)
		if err330 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditSponsor(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchSponsorsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchSponsorsByParams requires 2 args")
			flag.Usage()
		}
		arg331 := flag.Arg(1)
		mbTrans332 := thrift.NewTMemoryBufferLen(len(arg331))
		defer mbTrans332.Close()
		_, err333 := mbTrans332.WriteString(arg331)
		if err333 != nil {
			Usage()
			return
		}
		factory334 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt335 := factory334.GetProtocol(mbTrans332)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err336 := argvalue0.Read(jsProt335)
		if err336 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg337 := flag.Arg(2)
		mbTrans338 := thrift.NewTMemoryBufferLen(len(arg337))
		defer mbTrans338.Close()
		_, err339 := mbTrans338.WriteString(arg337)
		if err339 != nil {
			Usage()
			return
		}
		factory340 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt341 := factory340.GetProtocol(mbTrans338)
		argvalue1 := bidmaster_server.NewSponsorParams()
		err342 := argvalue1.Read(jsProt341)
		if err342 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchSponsorsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorsByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorsByUids requires 2 args")
			flag.Usage()
		}
		arg343 := flag.Arg(1)
		mbTrans344 := thrift.NewTMemoryBufferLen(len(arg343))
		defer mbTrans344.Close()
		_, err345 := mbTrans344.WriteString(arg343)
		if err345 != nil {
			Usage()
			return
		}
		factory346 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt347 := factory346.GetProtocol(mbTrans344)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err348 := argvalue0.Read(jsProt347)
		if err348 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg349 := flag.Arg(2)
		mbTrans350 := thrift.NewTMemoryBufferLen(len(arg349))
		defer mbTrans350.Close()
		_, err351 := mbTrans350.WriteString(arg349)
		if err351 != nil {
			Usage()
			return
		}
		factory352 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt353 := factory352.GetProtocol(mbTrans350)
		containerStruct1 := bidmaster_server.NewGetSponsorsByUidsArgs()
		err354 := containerStruct1.ReadField2(jsProt353)
		if err354 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetSponsorsByUids(value0, value1))
		fmt.Print("\n")
		break
	case "innerApproveSponsor":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveSponsor requires 3 args")
			flag.Usage()
		}
		arg355 := flag.Arg(1)
		mbTrans356 := thrift.NewTMemoryBufferLen(len(arg355))
		defer mbTrans356.Close()
		_, err357 := mbTrans356.WriteString(arg355)
		if err357 != nil {
			Usage()
			return
		}
		factory358 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt359 := factory358.GetProtocol(mbTrans356)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err360 := argvalue0.Read(jsProt359)
		if err360 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err361 := (strconv.Atoi(flag.Arg(2)))
		if err361 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg362 := flag.Arg(3)
		mbTrans363 := thrift.NewTMemoryBufferLen(len(arg362))
		defer mbTrans363.Close()
		_, err364 := mbTrans363.WriteString(arg362)
		if err364 != nil {
			Usage()
			return
		}
		factory365 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt366 := factory365.GetProtocol(mbTrans363)
		containerStruct2 := bidmaster_server.NewInnerApproveSponsorArgs()
		err367 := containerStruct2.ReadField3(jsProt366)
		if err367 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveSponsor(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editSponsorAuditMaterials":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSponsorAuditMaterials requires 3 args")
			flag.Usage()
		}
		arg368 := flag.Arg(1)
		mbTrans369 := thrift.NewTMemoryBufferLen(len(arg368))
		defer mbTrans369.Close()
		_, err370 := mbTrans369.WriteString(arg368)
		if err370 != nil {
			Usage()
			return
		}
		factory371 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt372 := factory371.GetProtocol(mbTrans369)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err373 := argvalue0.Read(jsProt372)
		if err373 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err374 := (strconv.Atoi(flag.Arg(2)))
		if err374 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg375 := flag.Arg(3)
		mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
		defer mbTrans376.Close()
		_, err377 := mbTrans376.WriteString(arg375)
		if err377 != nil {
			Usage()
			return
		}
		factory378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt379 := factory378.GetProtocol(mbTrans376)
		containerStruct2 := bidmaster_server.NewEditSponsorAuditMaterialsArgs()
		err380 := containerStruct2.ReadField3(jsProt379)
		if err380 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Materials
		value2 := argvalue2
		fmt.Print(client.EditSponsorAuditMaterials(value0, value1, value2))
		fmt.Print("\n")
		break
	case "innerApproveAuditMaterials":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveAuditMaterials requires 3 args")
			flag.Usage()
		}
		arg381 := flag.Arg(1)
		mbTrans382 := thrift.NewTMemoryBufferLen(len(arg381))
		defer mbTrans382.Close()
		_, err383 := mbTrans382.WriteString(arg381)
		if err383 != nil {
			Usage()
			return
		}
		factory384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt385 := factory384.GetProtocol(mbTrans382)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err386 := argvalue0.Read(jsProt385)
		if err386 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err387 := (strconv.Atoi(flag.Arg(2)))
		if err387 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg388 := flag.Arg(3)
		mbTrans389 := thrift.NewTMemoryBufferLen(len(arg388))
		defer mbTrans389.Close()
		_, err390 := mbTrans389.WriteString(arg388)
		if err390 != nil {
			Usage()
			return
		}
		factory391 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt392 := factory391.GetProtocol(mbTrans389)
		containerStruct2 := bidmaster_server.NewInnerApproveAuditMaterialsArgs()
		err393 := containerStruct2.ReadField3(jsProt392)
		if err393 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveAuditMaterials(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdOrder requires 3 args")
			flag.Usage()
		}
		arg394 := flag.Arg(1)
		mbTrans395 := thrift.NewTMemoryBufferLen(len(arg394))
		defer mbTrans395.Close()
		_, err396 := mbTrans395.WriteString(arg394)
		if err396 != nil {
			Usage()
			return
		}
		factory397 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt398 := factory397.GetProtocol(mbTrans395)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err399 := argvalue0.Read(jsProt398)
		if err399 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err400 := (strconv.Atoi(flag.Arg(2)))
		if err400 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg401 := flag.Arg(3)
		mbTrans402 := thrift.NewTMemoryBufferLen(len(arg401))
		defer mbTrans402.Close()
		_, err403 := mbTrans402.WriteString(arg401)
		if err403 != nil {
			Usage()
			return
		}
		factory404 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt405 := factory404.GetProtocol(mbTrans402)
		argvalue2 := bidmaster_server.NewAdOrder()
		err406 := argvalue2.Read(jsProt405)
		if err406 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdOrder requires 3 args")
			flag.Usage()
		}
		arg407 := flag.Arg(1)
		mbTrans408 := thrift.NewTMemoryBufferLen(len(arg407))
		defer mbTrans408.Close()
		_, err409 := mbTrans408.WriteString(arg407)
		if err409 != nil {
			Usage()
			return
		}
		factory410 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt411 := factory410.GetProtocol(mbTrans408)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err412 := argvalue0.Read(jsProt411)
		if err412 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err413 := (strconv.Atoi(flag.Arg(2)))
		if err413 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg414 := flag.Arg(3)
		mbTrans415 := thrift.NewTMemoryBufferLen(len(arg414))
		defer mbTrans415.Close()
		_, err416 := mbTrans415.WriteString(arg414)
		if err416 != nil {
			Usage()
			return
		}
		factory417 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt418 := factory417.GetProtocol(mbTrans415)
		argvalue2 := bidmaster_server.NewAdOrder()
		err419 := argvalue2.Read(jsProt418)
		if err419 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg420 := flag.Arg(1)
		mbTrans421 := thrift.NewTMemoryBufferLen(len(arg420))
		defer mbTrans421.Close()
		_, err422 := mbTrans421.WriteString(arg420)
		if err422 != nil {
			Usage()
			return
		}
		factory423 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt424 := factory423.GetProtocol(mbTrans421)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err425 := argvalue0.Read(jsProt424)
		if err425 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg426 := flag.Arg(2)
		mbTrans427 := thrift.NewTMemoryBufferLen(len(arg426))
		defer mbTrans427.Close()
		_, err428 := mbTrans427.WriteString(arg426)
		if err428 != nil {
			Usage()
			return
		}
		factory429 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt430 := factory429.GetProtocol(mbTrans427)
		containerStruct1 := bidmaster_server.NewGetAdOrdersByIdsArgs()
		err431 := containerStruct1.ReadField2(jsProt430)
		if err431 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdOrdersByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdOrdersByParams requires 2 args")
			flag.Usage()
		}
		arg432 := flag.Arg(1)
		mbTrans433 := thrift.NewTMemoryBufferLen(len(arg432))
		defer mbTrans433.Close()
		_, err434 := mbTrans433.WriteString(arg432)
		if err434 != nil {
			Usage()
			return
		}
		factory435 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt436 := factory435.GetProtocol(mbTrans433)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err437 := argvalue0.Read(jsProt436)
		if err437 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg438 := flag.Arg(2)
		mbTrans439 := thrift.NewTMemoryBufferLen(len(arg438))
		defer mbTrans439.Close()
		_, err440 := mbTrans439.WriteString(arg438)
		if err440 != nil {
			Usage()
			return
		}
		factory441 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt442 := factory441.GetProtocol(mbTrans439)
		argvalue1 := bidmaster_server.NewAdOrderParams()
		err443 := argvalue1.Read(jsProt442)
		if err443 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdOrdersByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdOrdersByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseAdOrdersByIds requires 3 args")
			flag.Usage()
		}
		arg444 := flag.Arg(1)
		mbTrans445 := thrift.NewTMemoryBufferLen(len(arg444))
		defer mbTrans445.Close()
		_, err446 := mbTrans445.WriteString(arg444)
		if err446 != nil {
			Usage()
			return
		}
		factory447 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt448 := factory447.GetProtocol(mbTrans445)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err449 := argvalue0.Read(jsProt448)
		if err449 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err450 := (strconv.Atoi(flag.Arg(2)))
		if err450 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg451 := flag.Arg(3)
		mbTrans452 := thrift.NewTMemoryBufferLen(len(arg451))
		defer mbTrans452.Close()
		_, err453 := mbTrans452.WriteString(arg451)
		if err453 != nil {
			Usage()
			return
		}
		factory454 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt455 := factory454.GetProtocol(mbTrans452)
		containerStruct2 := bidmaster_server.NewPauseAdOrdersByIdsArgs()
		err456 := containerStruct2.ReadField3(jsProt455)
		if err456 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.PauseAdOrdersByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeAdOrdersByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeAdOrdersByIds requires 3 args")
			flag.Usage()
		}
		arg457 := flag.Arg(1)
		mbTrans458 := thrift.NewTMemoryBufferLen(len(arg457))
		defer mbTrans458.Close()
		_, err459 := mbTrans458.WriteString(arg457)
		if err459 != nil {
			Usage()
			return
		}
		factory460 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt461 := factory460.GetProtocol(mbTrans458)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err462 := argvalue0.Read(jsProt461)
		if err462 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err463 := (strconv.Atoi(flag.Arg(2)))
		if err463 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg464 := flag.Arg(3)
		mbTrans465 := thrift.NewTMemoryBufferLen(len(arg464))
		defer mbTrans465.Close()
		_, err466 := mbTrans465.WriteString(arg464)
		if err466 != nil {
			Usage()
			return
		}
		factory467 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt468 := factory467.GetProtocol(mbTrans465)
		containerStruct2 := bidmaster_server.NewResumeAdOrdersByIdsArgs()
		err469 := containerStruct2.ReadField3(jsProt468)
		if err469 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ResumeAdOrdersByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdOrderByIds requires 3 args")
			flag.Usage()
		}
		arg470 := flag.Arg(1)
		mbTrans471 := thrift.NewTMemoryBufferLen(len(arg470))
		defer mbTrans471.Close()
		_, err472 := mbTrans471.WriteString(arg470)
		if err472 != nil {
			Usage()
			return
		}
		factory473 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt474 := factory473.GetProtocol(mbTrans471)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err475 := argvalue0.Read(jsProt474)
		if err475 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err476 := (strconv.Atoi(flag.Arg(2)))
		if err476 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg477 := flag.Arg(3)
		mbTrans478 := thrift.NewTMemoryBufferLen(len(arg477))
		defer mbTrans478.Close()
		_, err479 := mbTrans478.WriteString(arg477)
		if err479 != nil {
			Usage()
			return
		}
		factory480 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt481 := factory480.GetProtocol(mbTrans478)
		containerStruct2 := bidmaster_server.NewDeleteAdOrderByIdsArgs()
		err482 := containerStruct2.ReadField3(jsProt481)
		if err482 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdPromotion":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdPromotion requires 3 args")
			flag.Usage()
		}
		arg483 := flag.Arg(1)
		mbTrans484 := thrift.NewTMemoryBufferLen(len(arg483))
		defer mbTrans484.Close()
		_, err485 := mbTrans484.WriteString(arg483)
		if err485 != nil {
			Usage()
			return
		}
		factory486 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt487 := factory486.GetProtocol(mbTrans484)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err488 := argvalue0.Read(jsProt487)
		if err488 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err489 := (strconv.Atoi(flag.Arg(2)))
		if err489 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg490 := flag.Arg(3)
		mbTrans491 := thrift.NewTMemoryBufferLen(len(arg490))
		defer mbTrans491.Close()
		_, err492 := mbTrans491.WriteString(arg490)
		if err492 != nil {
			Usage()
			return
		}
		factory493 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt494 := factory493.GetProtocol(mbTrans491)
		argvalue2 := bidmaster_server.NewAdPromotion()
		err495 := argvalue2.Read(jsProt494)
		if err495 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdPromotion(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdPromotion":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdPromotion requires 3 args")
			flag.Usage()
		}
		arg496 := flag.Arg(1)
		mbTrans497 := thrift.NewTMemoryBufferLen(len(arg496))
		defer mbTrans497.Close()
		_, err498 := mbTrans497.WriteString(arg496)
		if err498 != nil {
			Usage()
			return
		}
		factory499 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt500 := factory499.GetProtocol(mbTrans497)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err501 := argvalue0.Read(jsProt500)
		if err501 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err502 := (strconv.Atoi(flag.Arg(2)))
		if err502 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg503 := flag.Arg(3)
		mbTrans504 := thrift.NewTMemoryBufferLen(len(arg503))
		defer mbTrans504.Close()
		_, err505 := mbTrans504.WriteString(arg503)
		if err505 != nil {
			Usage()
			return
		}
		factory506 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt507 := factory506.GetProtocol(mbTrans504)
		argvalue2 := bidmaster_server.NewAdPromotion()
		err508 := argvalue2.Read(jsProt507)
		if err508 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdPromotion(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdPromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg509 := flag.Arg(1)
		mbTrans510 := thrift.NewTMemoryBufferLen(len(arg509))
		defer mbTrans510.Close()
		_, err511 := mbTrans510.WriteString(arg509)
		if err511 != nil {
			Usage()
			return
		}
		factory512 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt513 := factory512.GetProtocol(mbTrans510)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err514 := argvalue0.Read(jsProt513)
		if err514 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg515 := flag.Arg(2)
		mbTrans516 := thrift.NewTMemoryBufferLen(len(arg515))
		defer mbTrans516.Close()
		_, err517 := mbTrans516.WriteString(arg515)
		if err517 != nil {
			Usage()
			return
		}
		factory518 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt519 := factory518.GetProtocol(mbTrans516)
		containerStruct1 := bidmaster_server.NewGetAdPromotionsByIdsArgs()
		err520 := containerStruct1.ReadField2(jsProt519)
		if err520 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdPromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdPromotionByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdPromotionByParams requires 2 args")
			flag.Usage()
		}
		arg521 := flag.Arg(1)
		mbTrans522 := thrift.NewTMemoryBufferLen(len(arg521))
		defer mbTrans522.Close()
		_, err523 := mbTrans522.WriteString(arg521)
		if err523 != nil {
			Usage()
			return
		}
		factory524 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt525 := factory524.GetProtocol(mbTrans522)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err526 := argvalue0.Read(jsProt525)
		if err526 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg527 := flag.Arg(2)
		mbTrans528 := thrift.NewTMemoryBufferLen(len(arg527))
		defer mbTrans528.Close()
		_, err529 := mbTrans528.WriteString(arg527)
		if err529 != nil {
			Usage()
			return
		}
		factory530 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt531 := factory530.GetProtocol(mbTrans528)
		argvalue1 := bidmaster_server.NewAdPromotionParams()
		err532 := argvalue1.Read(jsProt531)
		if err532 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdPromotionByParams(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdPromotionsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdPromotionsByIds requires 3 args")
			flag.Usage()
		}
		arg533 := flag.Arg(1)
		mbTrans534 := thrift.NewTMemoryBufferLen(len(arg533))
		defer mbTrans534.Close()
		_, err535 := mbTrans534.WriteString(arg533)
		if err535 != nil {
			Usage()
			return
		}
		factory536 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt537 := factory536.GetProtocol(mbTrans534)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err538 := argvalue0.Read(jsProt537)
		if err538 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err539 := (strconv.Atoi(flag.Arg(2)))
		if err539 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg540 := flag.Arg(3)
		mbTrans541 := thrift.NewTMemoryBufferLen(len(arg540))
		defer mbTrans541.Close()
		_, err542 := mbTrans541.WriteString(arg540)
		if err542 != nil {
			Usage()
			return
		}
		factory543 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt544 := factory543.GetProtocol(mbTrans541)
		containerStruct2 := bidmaster_server.NewDeleteAdPromotionsByIdsArgs()
		err545 := containerStruct2.ReadField3(jsProt544)
		if err545 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdPromotionsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdCampaign":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdCampaign requires 3 args")
			flag.Usage()
		}
		arg546 := flag.Arg(1)
		mbTrans547 := thrift.NewTMemoryBufferLen(len(arg546))
		defer mbTrans547.Close()
		_, err548 := mbTrans547.WriteString(arg546)
		if err548 != nil {
			Usage()
			return
		}
		factory549 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt550 := factory549.GetProtocol(mbTrans547)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err551 := argvalue0.Read(jsProt550)
		if err551 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err552 := (strconv.Atoi(flag.Arg(2)))
		if err552 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg553 := flag.Arg(3)
		mbTrans554 := thrift.NewTMemoryBufferLen(len(arg553))
		defer mbTrans554.Close()
		_, err555 := mbTrans554.WriteString(arg553)
		if err555 != nil {
			Usage()
			return
		}
		factory556 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt557 := factory556.GetProtocol(mbTrans554)
		argvalue2 := bidmaster_server.NewAdCampaign()
		err558 := argvalue2.Read(jsProt557)
		if err558 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdCampaign(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdCampaign":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdCampaign requires 3 args")
			flag.Usage()
		}
		arg559 := flag.Arg(1)
		mbTrans560 := thrift.NewTMemoryBufferLen(len(arg559))
		defer mbTrans560.Close()
		_, err561 := mbTrans560.WriteString(arg559)
		if err561 != nil {
			Usage()
			return
		}
		factory562 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt563 := factory562.GetProtocol(mbTrans560)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err564 := argvalue0.Read(jsProt563)
		if err564 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err565 := (strconv.Atoi(flag.Arg(2)))
		if err565 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg566 := flag.Arg(3)
		mbTrans567 := thrift.NewTMemoryBufferLen(len(arg566))
		defer mbTrans567.Close()
		_, err568 := mbTrans567.WriteString(arg566)
		if err568 != nil {
			Usage()
			return
		}
		factory569 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt570 := factory569.GetProtocol(mbTrans567)
		argvalue2 := bidmaster_server.NewAdCampaign()
		err571 := argvalue2.Read(jsProt570)
		if err571 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdCampaign(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg572 := flag.Arg(1)
		mbTrans573 := thrift.NewTMemoryBufferLen(len(arg572))
		defer mbTrans573.Close()
		_, err574 := mbTrans573.WriteString(arg572)
		if err574 != nil {
			Usage()
			return
		}
		factory575 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt576 := factory575.GetProtocol(mbTrans573)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err577 := argvalue0.Read(jsProt576)
		if err577 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg578 := flag.Arg(2)
		mbTrans579 := thrift.NewTMemoryBufferLen(len(arg578))
		defer mbTrans579.Close()
		_, err580 := mbTrans579.WriteString(arg578)
		if err580 != nil {
			Usage()
			return
		}
		factory581 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt582 := factory581.GetProtocol(mbTrans579)
		containerStruct1 := bidmaster_server.NewGetAdCampaignsByIdsArgs()
		err583 := containerStruct1.ReadField2(jsProt582)
		if err583 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdCampaignByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdCampaignByParams requires 2 args")
			flag.Usage()
		}
		arg584 := flag.Arg(1)
		mbTrans585 := thrift.NewTMemoryBufferLen(len(arg584))
		defer mbTrans585.Close()
		_, err586 := mbTrans585.WriteString(arg584)
		if err586 != nil {
			Usage()
			return
		}
		factory587 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt588 := factory587.GetProtocol(mbTrans585)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err589 := argvalue0.Read(jsProt588)
		if err589 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg590 := flag.Arg(2)
		mbTrans591 := thrift.NewTMemoryBufferLen(len(arg590))
		defer mbTrans591.Close()
		_, err592 := mbTrans591.WriteString(arg590)
		if err592 != nil {
			Usage()
			return
		}
		factory593 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt594 := factory593.GetProtocol(mbTrans591)
		argvalue1 := bidmaster_server.NewAdCampaignParams()
		err595 := argvalue1.Read(jsProt594)
		if err595 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdCampaignByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseAdCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg596 := flag.Arg(1)
		mbTrans597 := thrift.NewTMemoryBufferLen(len(arg596))
		defer mbTrans597.Close()
		_, err598 := mbTrans597.WriteString(arg596)
		if err598 != nil {
			Usage()
			return
		}
		factory599 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt600 := factory599.GetProtocol(mbTrans597)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err601 := argvalue0.Read(jsProt600)
		if err601 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err602 := (strconv.Atoi(flag.Arg(2)))
		if err602 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg603 := flag.Arg(3)
		mbTrans604 := thrift.NewTMemoryBufferLen(len(arg603))
		defer mbTrans604.Close()
		_, err605 := mbTrans604.WriteString(arg603)
		if err605 != nil {
			Usage()
			return
		}
		factory606 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt607 := factory606.GetProtocol(mbTrans604)
		containerStruct2 := bidmaster_server.NewPauseAdCampaignsByIdsArgs()
		err608 := containerStruct2.ReadField3(jsProt607)
		if err608 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.PauseAdCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeAdCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeAdCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg609 := flag.Arg(1)
		mbTrans610 := thrift.NewTMemoryBufferLen(len(arg609))
		defer mbTrans610.Close()
		_, err611 := mbTrans610.WriteString(arg609)
		if err611 != nil {
			Usage()
			return
		}
		factory612 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt613 := factory612.GetProtocol(mbTrans610)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err614 := argvalue0.Read(jsProt613)
		if err614 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err615 := (strconv.Atoi(flag.Arg(2)))
		if err615 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg616 := flag.Arg(3)
		mbTrans617 := thrift.NewTMemoryBufferLen(len(arg616))
		defer mbTrans617.Close()
		_, err618 := mbTrans617.WriteString(arg616)
		if err618 != nil {
			Usage()
			return
		}
		factory619 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt620 := factory619.GetProtocol(mbTrans617)
		containerStruct2 := bidmaster_server.NewResumeAdCampaignsByIdsArgs()
		err621 := containerStruct2.ReadField3(jsProt620)
		if err621 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ResumeAdCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg622 := flag.Arg(1)
		mbTrans623 := thrift.NewTMemoryBufferLen(len(arg622))
		defer mbTrans623.Close()
		_, err624 := mbTrans623.WriteString(arg622)
		if err624 != nil {
			Usage()
			return
		}
		factory625 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt626 := factory625.GetProtocol(mbTrans623)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err627 := argvalue0.Read(jsProt626)
		if err627 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err628 := (strconv.Atoi(flag.Arg(2)))
		if err628 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg629 := flag.Arg(3)
		mbTrans630 := thrift.NewTMemoryBufferLen(len(arg629))
		defer mbTrans630.Close()
		_, err631 := mbTrans630.WriteString(arg629)
		if err631 != nil {
			Usage()
			return
		}
		factory632 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt633 := factory632.GetProtocol(mbTrans630)
		containerStruct2 := bidmaster_server.NewDeleteAdCampaignsByIdsArgs()
		err634 := containerStruct2.ReadField3(jsProt633)
		if err634 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editCampaignActualDailyBudget":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCampaignActualDailyBudget requires 3 args")
			flag.Usage()
		}
		arg635 := flag.Arg(1)
		mbTrans636 := thrift.NewTMemoryBufferLen(len(arg635))
		defer mbTrans636.Close()
		_, err637 := mbTrans636.WriteString(arg635)
		if err637 != nil {
			Usage()
			return
		}
		factory638 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt639 := factory638.GetProtocol(mbTrans636)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err640 := argvalue0.Read(jsProt639)
		if err640 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err641 := (strconv.Atoi(flag.Arg(2)))
		if err641 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err642 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err642 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditCampaignActualDailyBudget(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdStrategy requires 3 args")
			flag.Usage()
		}
		arg643 := flag.Arg(1)
		mbTrans644 := thrift.NewTMemoryBufferLen(len(arg643))
		defer mbTrans644.Close()
		_, err645 := mbTrans644.WriteString(arg643)
		if err645 != nil {
			Usage()
			return
		}
		factory646 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt647 := factory646.GetProtocol(mbTrans644)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err648 := argvalue0.Read(jsProt647)
		if err648 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err649 := (strconv.Atoi(flag.Arg(2)))
		if err649 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg650 := flag.Arg(3)
		mbTrans651 := thrift.NewTMemoryBufferLen(len(arg650))
		defer mbTrans651.Close()
		_, err652 := mbTrans651.WriteString(arg650)
		if err652 != nil {
			Usage()
			return
		}
		factory653 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt654 := factory653.GetProtocol(mbTrans651)
		argvalue2 := bidmaster_server.NewAdStrategy()
		err655 := argvalue2.Read(jsProt654)
		if err655 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdStrategy requires 3 args")
			flag.Usage()
		}
		arg656 := flag.Arg(1)
		mbTrans657 := thrift.NewTMemoryBufferLen(len(arg656))
		defer mbTrans657.Close()
		_, err658 := mbTrans657.WriteString(arg656)
		if err658 != nil {
			Usage()
			return
		}
		factory659 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt660 := factory659.GetProtocol(mbTrans657)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err661 := argvalue0.Read(jsProt660)
		if err661 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err662 := (strconv.Atoi(flag.Arg(2)))
		if err662 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg663 := flag.Arg(3)
		mbTrans664 := thrift.NewTMemoryBufferLen(len(arg663))
		defer mbTrans664.Close()
		_, err665 := mbTrans664.WriteString(arg663)
		if err665 != nil {
			Usage()
			return
		}
		factory666 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt667 := factory666.GetProtocol(mbTrans664)
		argvalue2 := bidmaster_server.NewAdStrategy()
		err668 := argvalue2.Read(jsProt667)
		if err668 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg669 := flag.Arg(1)
		mbTrans670 := thrift.NewTMemoryBufferLen(len(arg669))
		defer mbTrans670.Close()
		_, err671 := mbTrans670.WriteString(arg669)
		if err671 != nil {
			Usage()
			return
		}
		factory672 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt673 := factory672.GetProtocol(mbTrans670)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err674 := argvalue0.Read(jsProt673)
		if err674 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg675 := flag.Arg(2)
		mbTrans676 := thrift.NewTMemoryBufferLen(len(arg675))
		defer mbTrans676.Close()
		_, err677 := mbTrans676.WriteString(arg675)
		if err677 != nil {
			Usage()
			return
		}
		factory678 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt679 := factory678.GetProtocol(mbTrans676)
		containerStruct1 := bidmaster_server.NewGetAdStrategiesByIdsArgs()
		err680 := containerStruct1.ReadField2(jsProt679)
		if err680 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdStrategyByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdStrategyByParams requires 2 args")
			flag.Usage()
		}
		arg681 := flag.Arg(1)
		mbTrans682 := thrift.NewTMemoryBufferLen(len(arg681))
		defer mbTrans682.Close()
		_, err683 := mbTrans682.WriteString(arg681)
		if err683 != nil {
			Usage()
			return
		}
		factory684 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt685 := factory684.GetProtocol(mbTrans682)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err686 := argvalue0.Read(jsProt685)
		if err686 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg687 := flag.Arg(2)
		mbTrans688 := thrift.NewTMemoryBufferLen(len(arg687))
		defer mbTrans688.Close()
		_, err689 := mbTrans688.WriteString(arg687)
		if err689 != nil {
			Usage()
			return
		}
		factory690 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt691 := factory690.GetProtocol(mbTrans688)
		argvalue1 := bidmaster_server.NewAdStrategyParams()
		err692 := argvalue1.Read(jsProt691)
		if err692 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdStrategyByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdStrategiesByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PauseAdStrategiesByIds requires 4 args")
			flag.Usage()
		}
		arg693 := flag.Arg(1)
		mbTrans694 := thrift.NewTMemoryBufferLen(len(arg693))
		defer mbTrans694.Close()
		_, err695 := mbTrans694.WriteString(arg693)
		if err695 != nil {
			Usage()
			return
		}
		factory696 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt697 := factory696.GetProtocol(mbTrans694)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err698 := argvalue0.Read(jsProt697)
		if err698 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err699 := (strconv.Atoi(flag.Arg(2)))
		if err699 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err700 := (strconv.Atoi(flag.Arg(3)))
		if err700 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg701 := flag.Arg(4)
		mbTrans702 := thrift.NewTMemoryBufferLen(len(arg701))
		defer mbTrans702.Close()
		_, err703 := mbTrans702.WriteString(arg701)
		if err703 != nil {
			Usage()
			return
		}
		factory704 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt705 := factory704.GetProtocol(mbTrans702)
		containerStruct3 := bidmaster_server.NewPauseAdStrategiesByIdsArgs()
		err706 := containerStruct3.ReadField4(jsProt705)
		if err706 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.PauseAdStrategiesByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resumeAdStrategiesByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResumeAdStrategiesByIds requires 4 args")
			flag.Usage()
		}
		arg707 := flag.Arg(1)
		mbTrans708 := thrift.NewTMemoryBufferLen(len(arg707))
		defer mbTrans708.Close()
		_, err709 := mbTrans708.WriteString(arg707)
		if err709 != nil {
			Usage()
			return
		}
		factory710 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt711 := factory710.GetProtocol(mbTrans708)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err712 := argvalue0.Read(jsProt711)
		if err712 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err713 := (strconv.Atoi(flag.Arg(2)))
		if err713 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err714 := (strconv.Atoi(flag.Arg(3)))
		if err714 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg715 := flag.Arg(4)
		mbTrans716 := thrift.NewTMemoryBufferLen(len(arg715))
		defer mbTrans716.Close()
		_, err717 := mbTrans716.WriteString(arg715)
		if err717 != nil {
			Usage()
			return
		}
		factory718 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt719 := factory718.GetProtocol(mbTrans716)
		containerStruct3 := bidmaster_server.NewResumeAdStrategiesByIdsArgs()
		err720 := containerStruct3.ReadField4(jsProt719)
		if err720 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.ResumeAdStrategiesByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAdStrategiesByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteAdStrategiesByIds requires 4 args")
			flag.Usage()
		}
		arg721 := flag.Arg(1)
		mbTrans722 := thrift.NewTMemoryBufferLen(len(arg721))
		defer mbTrans722.Close()
		_, err723 := mbTrans722.WriteString(arg721)
		if err723 != nil {
			Usage()
			return
		}
		factory724 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt725 := factory724.GetProtocol(mbTrans722)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err726 := argvalue0.Read(jsProt725)
		if err726 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err727 := (strconv.Atoi(flag.Arg(2)))
		if err727 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err728 := (strconv.Atoi(flag.Arg(3)))
		if err728 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg729 := flag.Arg(4)
		mbTrans730 := thrift.NewTMemoryBufferLen(len(arg729))
		defer mbTrans730.Close()
		_, err731 := mbTrans730.WriteString(arg729)
		if err731 != nil {
			Usage()
			return
		}
		factory732 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt733 := factory732.GetProtocol(mbTrans730)
		containerStruct3 := bidmaster_server.NewDeleteAdStrategiesByIdsArgs()
		err734 := containerStruct3.ReadField4(jsProt733)
		if err734 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.DeleteAdStrategiesByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addAdCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdCreative requires 3 args")
			flag.Usage()
		}
		arg735 := flag.Arg(1)
		mbTrans736 := thrift.NewTMemoryBufferLen(len(arg735))
		defer mbTrans736.Close()
		_, err737 := mbTrans736.WriteString(arg735)
		if err737 != nil {
			Usage()
			return
		}
		factory738 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt739 := factory738.GetProtocol(mbTrans736)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err740 := argvalue0.Read(jsProt739)
		if err740 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err741 := (strconv.Atoi(flag.Arg(2)))
		if err741 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg742 := flag.Arg(3)
		mbTrans743 := thrift.NewTMemoryBufferLen(len(arg742))
		defer mbTrans743.Close()
		_, err744 := mbTrans743.WriteString(arg742)
		if err744 != nil {
			Usage()
			return
		}
		factory745 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt746 := factory745.GetProtocol(mbTrans743)
		argvalue2 := bidmaster_server.NewAdCreative()
		err747 := argvalue2.Read(jsProt746)
		if err747 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdCreative requires 3 args")
			flag.Usage()
		}
		arg748 := flag.Arg(1)
		mbTrans749 := thrift.NewTMemoryBufferLen(len(arg748))
		defer mbTrans749.Close()
		_, err750 := mbTrans749.WriteString(arg748)
		if err750 != nil {
			Usage()
			return
		}
		factory751 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt752 := factory751.GetProtocol(mbTrans749)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err753 := argvalue0.Read(jsProt752)
		if err753 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err754 := (strconv.Atoi(flag.Arg(2)))
		if err754 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg755 := flag.Arg(3)
		mbTrans756 := thrift.NewTMemoryBufferLen(len(arg755))
		defer mbTrans756.Close()
		_, err757 := mbTrans756.WriteString(arg755)
		if err757 != nil {
			Usage()
			return
		}
		factory758 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt759 := factory758.GetProtocol(mbTrans756)
		argvalue2 := bidmaster_server.NewAdCreative()
		err760 := argvalue2.Read(jsProt759)
		if err760 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg761 := flag.Arg(1)
		mbTrans762 := thrift.NewTMemoryBufferLen(len(arg761))
		defer mbTrans762.Close()
		_, err763 := mbTrans762.WriteString(arg761)
		if err763 != nil {
			Usage()
			return
		}
		factory764 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt765 := factory764.GetProtocol(mbTrans762)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err766 := argvalue0.Read(jsProt765)
		if err766 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg767 := flag.Arg(2)
		mbTrans768 := thrift.NewTMemoryBufferLen(len(arg767))
		defer mbTrans768.Close()
		_, err769 := mbTrans768.WriteString(arg767)
		if err769 != nil {
			Usage()
			return
		}
		factory770 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt771 := factory770.GetProtocol(mbTrans768)
		containerStruct1 := bidmaster_server.NewGetAdCreativesByIdsArgs()
		err772 := containerStruct1.ReadField2(jsProt771)
		if err772 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdCreativeByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdCreativeByParams requires 2 args")
			flag.Usage()
		}
		arg773 := flag.Arg(1)
		mbTrans774 := thrift.NewTMemoryBufferLen(len(arg773))
		defer mbTrans774.Close()
		_, err775 := mbTrans774.WriteString(arg773)
		if err775 != nil {
			Usage()
			return
		}
		factory776 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt777 := factory776.GetProtocol(mbTrans774)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err778 := argvalue0.Read(jsProt777)
		if err778 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg779 := flag.Arg(2)
		mbTrans780 := thrift.NewTMemoryBufferLen(len(arg779))
		defer mbTrans780.Close()
		_, err781 := mbTrans780.WriteString(arg779)
		if err781 != nil {
			Usage()
			return
		}
		factory782 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt783 := factory782.GetProtocol(mbTrans780)
		argvalue1 := bidmaster_server.NewAdCreativeParams()
		err784 := argvalue1.Read(jsProt783)
		if err784 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdCreativeByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCreativesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "PauseAdCreativesByIds requires 5 args")
			flag.Usage()
		}
		arg785 := flag.Arg(1)
		mbTrans786 := thrift.NewTMemoryBufferLen(len(arg785))
		defer mbTrans786.Close()
		_, err787 := mbTrans786.WriteString(arg785)
		if err787 != nil {
			Usage()
			return
		}
		factory788 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt789 := factory788.GetProtocol(mbTrans786)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err790 := argvalue0.Read(jsProt789)
		if err790 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err791 := (strconv.Atoi(flag.Arg(2)))
		if err791 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err792 := (strconv.Atoi(flag.Arg(3)))
		if err792 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err793 := (strconv.Atoi(flag.Arg(4)))
		if err793 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg794 := flag.Arg(5)
		mbTrans795 := thrift.NewTMemoryBufferLen(len(arg794))
		defer mbTrans795.Close()
		_, err796 := mbTrans795.WriteString(arg794)
		if err796 != nil {
			Usage()
			return
		}
		factory797 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt798 := factory797.GetProtocol(mbTrans795)
		containerStruct4 := bidmaster_server.NewPauseAdCreativesByIdsArgs()
		err799 := containerStruct4.ReadField5(jsProt798)
		if err799 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.PauseAdCreativesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "resumeAdCreativesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ResumeAdCreativesByIds requires 5 args")
			flag.Usage()
		}
		arg800 := flag.Arg(1)
		mbTrans801 := thrift.NewTMemoryBufferLen(len(arg800))
		defer mbTrans801.Close()
		_, err802 := mbTrans801.WriteString(arg800)
		if err802 != nil {
			Usage()
			return
		}
		factory803 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt804 := factory803.GetProtocol(mbTrans801)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err805 := argvalue0.Read(jsProt804)
		if err805 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err806 := (strconv.Atoi(flag.Arg(2)))
		if err806 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err807 := (strconv.Atoi(flag.Arg(3)))
		if err807 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err808 := (strconv.Atoi(flag.Arg(4)))
		if err808 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg809 := flag.Arg(5)
		mbTrans810 := thrift.NewTMemoryBufferLen(len(arg809))
		defer mbTrans810.Close()
		_, err811 := mbTrans810.WriteString(arg809)
		if err811 != nil {
			Usage()
			return
		}
		factory812 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt813 := factory812.GetProtocol(mbTrans810)
		containerStruct4 := bidmaster_server.NewResumeAdCreativesByIdsArgs()
		err814 := containerStruct4.ReadField5(jsProt813)
		if err814 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.ResumeAdCreativesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteAdCreativesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteAdCreativesByIds requires 5 args")
			flag.Usage()
		}
		arg815 := flag.Arg(1)
		mbTrans816 := thrift.NewTMemoryBufferLen(len(arg815))
		defer mbTrans816.Close()
		_, err817 := mbTrans816.WriteString(arg815)
		if err817 != nil {
			Usage()
			return
		}
		factory818 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt819 := factory818.GetProtocol(mbTrans816)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err820 := argvalue0.Read(jsProt819)
		if err820 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err821 := (strconv.Atoi(flag.Arg(2)))
		if err821 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err822 := (strconv.Atoi(flag.Arg(3)))
		if err822 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err823 := (strconv.Atoi(flag.Arg(4)))
		if err823 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg824 := flag.Arg(5)
		mbTrans825 := thrift.NewTMemoryBufferLen(len(arg824))
		defer mbTrans825.Close()
		_, err826 := mbTrans825.WriteString(arg824)
		if err826 != nil {
			Usage()
			return
		}
		factory827 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt828 := factory827.GetProtocol(mbTrans825)
		containerStruct4 := bidmaster_server.NewDeleteAdCreativesByIdsArgs()
		err829 := containerStruct4.ReadField5(jsProt828)
		if err829 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.DeleteAdCreativesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "innerApproveAdCreatives":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveAdCreatives requires 3 args")
			flag.Usage()
		}
		arg830 := flag.Arg(1)
		mbTrans831 := thrift.NewTMemoryBufferLen(len(arg830))
		defer mbTrans831.Close()
		_, err832 := mbTrans831.WriteString(arg830)
		if err832 != nil {
			Usage()
			return
		}
		factory833 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt834 := factory833.GetProtocol(mbTrans831)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err835 := argvalue0.Read(jsProt834)
		if err835 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err836 := (strconv.Atoi(flag.Arg(2)))
		if err836 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg837 := flag.Arg(3)
		mbTrans838 := thrift.NewTMemoryBufferLen(len(arg837))
		defer mbTrans838.Close()
		_, err839 := mbTrans838.WriteString(arg837)
		if err839 != nil {
			Usage()
			return
		}
		factory840 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt841 := factory840.GetProtocol(mbTrans838)
		containerStruct2 := bidmaster_server.NewInnerApproveAdCreativesArgs()
		err842 := containerStruct2.ReadField3(jsProt841)
		if err842 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveAdCreatives(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editCampaignMinProfitRatio":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCampaignMinProfitRatio requires 3 args")
			flag.Usage()
		}
		arg843 := flag.Arg(1)
		mbTrans844 := thrift.NewTMemoryBufferLen(len(arg843))
		defer mbTrans844.Close()
		_, err845 := mbTrans844.WriteString(arg843)
		if err845 != nil {
			Usage()
			return
		}
		factory846 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt847 := factory846.GetProtocol(mbTrans844)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err848 := argvalue0.Read(jsProt847)
		if err848 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg849 := flag.Arg(2)
		mbTrans850 := thrift.NewTMemoryBufferLen(len(arg849))
		defer mbTrans850.Close()
		_, err851 := mbTrans850.WriteString(arg849)
		if err851 != nil {
			Usage()
			return
		}
		factory852 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt853 := factory852.GetProtocol(mbTrans850)
		containerStruct1 := bidmaster_server.NewEditCampaignMinProfitRatioArgs()
		err854 := containerStruct1.ReadField2(jsProt853)
		if err854 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIds
		value1 := argvalue1
		tmp2, err855 := (strconv.Atoi(flag.Arg(3)))
		if err855 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.EditCampaignMinProfitRatio(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getOrderIdsByProjectIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOrderIdsByProjectIds requires 2 args")
			flag.Usage()
		}
		arg856 := flag.Arg(1)
		mbTrans857 := thrift.NewTMemoryBufferLen(len(arg856))
		defer mbTrans857.Close()
		_, err858 := mbTrans857.WriteString(arg856)
		if err858 != nil {
			Usage()
			return
		}
		factory859 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt860 := factory859.GetProtocol(mbTrans857)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err861 := argvalue0.Read(jsProt860)
		if err861 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg862 := flag.Arg(2)
		mbTrans863 := thrift.NewTMemoryBufferLen(len(arg862))
		defer mbTrans863.Close()
		_, err864 := mbTrans863.WriteString(arg862)
		if err864 != nil {
			Usage()
			return
		}
		factory865 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt866 := factory865.GetProtocol(mbTrans863)
		containerStruct1 := bidmaster_server.NewGetOrderIdsByProjectIdsArgs()
		err867 := containerStruct1.ReadField2(jsProt866)
		if err867 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ProjectIds
		value1 := argvalue1
		fmt.Print(client.GetOrderIdsByProjectIds(value0, value1))
		fmt.Print("\n")
		break
	case "debugAdTracking":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DebugAdTracking requires 5 args")
			flag.Usage()
		}
		arg868 := flag.Arg(1)
		mbTrans869 := thrift.NewTMemoryBufferLen(len(arg868))
		defer mbTrans869.Close()
		_, err870 := mbTrans869.WriteString(arg868)
		if err870 != nil {
			Usage()
			return
		}
		factory871 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt872 := factory871.GetProtocol(mbTrans869)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err873 := argvalue0.Read(jsProt872)
		if err873 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err874 := (strconv.Atoi(flag.Arg(2)))
		if err874 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err875 := (strconv.Atoi(flag.Arg(3)))
		if err875 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err877 := (strconv.Atoi(flag.Arg(5)))
		if err877 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.DebugAdTracking(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addAdExchangeDmp":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdExchangeDmp requires 3 args")
			flag.Usage()
		}
		arg878 := flag.Arg(1)
		mbTrans879 := thrift.NewTMemoryBufferLen(len(arg878))
		defer mbTrans879.Close()
		_, err880 := mbTrans879.WriteString(arg878)
		if err880 != nil {
			Usage()
			return
		}
		factory881 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt882 := factory881.GetProtocol(mbTrans879)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err883 := argvalue0.Read(jsProt882)
		if err883 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err884 := (strconv.Atoi(flag.Arg(2)))
		if err884 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg885 := flag.Arg(3)
		mbTrans886 := thrift.NewTMemoryBufferLen(len(arg885))
		defer mbTrans886.Close()
		_, err887 := mbTrans886.WriteString(arg885)
		if err887 != nil {
			Usage()
			return
		}
		factory888 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt889 := factory888.GetProtocol(mbTrans886)
		argvalue2 := bidmaster_server.NewAdExchangeDmp()
		err890 := argvalue2.Read(jsProt889)
		if err890 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdExchangeDmp(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdExchangeDmp":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdExchangeDmp requires 3 args")
			flag.Usage()
		}
		arg891 := flag.Arg(1)
		mbTrans892 := thrift.NewTMemoryBufferLen(len(arg891))
		defer mbTrans892.Close()
		_, err893 := mbTrans892.WriteString(arg891)
		if err893 != nil {
			Usage()
			return
		}
		factory894 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt895 := factory894.GetProtocol(mbTrans892)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err896 := argvalue0.Read(jsProt895)
		if err896 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err897 := (strconv.Atoi(flag.Arg(2)))
		if err897 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg898 := flag.Arg(3)
		mbTrans899 := thrift.NewTMemoryBufferLen(len(arg898))
		defer mbTrans899.Close()
		_, err900 := mbTrans899.WriteString(arg898)
		if err900 != nil {
			Usage()
			return
		}
		factory901 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt902 := factory901.GetProtocol(mbTrans899)
		argvalue2 := bidmaster_server.NewAdExchangeDmp()
		err903 := argvalue2.Read(jsProt902)
		if err903 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdExchangeDmp(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdExchangeDmpByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdExchangeDmpByIds requires 3 args")
			flag.Usage()
		}
		arg904 := flag.Arg(1)
		mbTrans905 := thrift.NewTMemoryBufferLen(len(arg904))
		defer mbTrans905.Close()
		_, err906 := mbTrans905.WriteString(arg904)
		if err906 != nil {
			Usage()
			return
		}
		factory907 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt908 := factory907.GetProtocol(mbTrans905)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err909 := argvalue0.Read(jsProt908)
		if err909 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err910 := (strconv.Atoi(flag.Arg(2)))
		if err910 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg911 := flag.Arg(3)
		mbTrans912 := thrift.NewTMemoryBufferLen(len(arg911))
		defer mbTrans912.Close()
		_, err913 := mbTrans912.WriteString(arg911)
		if err913 != nil {
			Usage()
			return
		}
		factory914 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt915 := factory914.GetProtocol(mbTrans912)
		containerStruct2 := bidmaster_server.NewDeleteAdExchangeDmpByIdsArgs()
		err916 := containerStruct2.ReadField3(jsProt915)
		if err916 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdExchangeDmpByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdExchangeDmpByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdExchangeDmpByIds requires 2 args")
			flag.Usage()
		}
		arg917 := flag.Arg(1)
		mbTrans918 := thrift.NewTMemoryBufferLen(len(arg917))
		defer mbTrans918.Close()
		_, err919 := mbTrans918.WriteString(arg917)
		if err919 != nil {
			Usage()
			return
		}
		factory920 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt921 := factory920.GetProtocol(mbTrans918)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err922 := argvalue0.Read(jsProt921)
		if err922 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg923 := flag.Arg(2)
		mbTrans924 := thrift.NewTMemoryBufferLen(len(arg923))
		defer mbTrans924.Close()
		_, err925 := mbTrans924.WriteString(arg923)
		if err925 != nil {
			Usage()
			return
		}
		factory926 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt927 := factory926.GetProtocol(mbTrans924)
		containerStruct1 := bidmaster_server.NewGetAdExchangeDmpByIdsArgs()
		err928 := containerStruct1.ReadField2(jsProt927)
		if err928 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdExchangeDmpByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdExchangeDmpByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdExchangeDmpByParams requires 2 args")
			flag.Usage()
		}
		arg929 := flag.Arg(1)
		mbTrans930 := thrift.NewTMemoryBufferLen(len(arg929))
		defer mbTrans930.Close()
		_, err931 := mbTrans930.WriteString(arg929)
		if err931 != nil {
			Usage()
			return
		}
		factory932 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt933 := factory932.GetProtocol(mbTrans930)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err934 := argvalue0.Read(jsProt933)
		if err934 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg935 := flag.Arg(2)
		mbTrans936 := thrift.NewTMemoryBufferLen(len(arg935))
		defer mbTrans936.Close()
		_, err937 := mbTrans936.WriteString(arg935)
		if err937 != nil {
			Usage()
			return
		}
		factory938 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt939 := factory938.GetProtocol(mbTrans936)
		argvalue1 := bidmaster_server.NewAdExchangeDmpParams()
		err940 := argvalue1.Read(jsProt939)
		if err940 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdExchangeDmpByParams(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdSuggestPrice":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SearchAdSuggestPrice requires 3 args")
			flag.Usage()
		}
		arg941 := flag.Arg(1)
		mbTrans942 := thrift.NewTMemoryBufferLen(len(arg941))
		defer mbTrans942.Close()
		_, err943 := mbTrans942.WriteString(arg941)
		if err943 != nil {
			Usage()
			return
		}
		factory944 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt945 := factory944.GetProtocol(mbTrans942)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err946 := argvalue0.Read(jsProt945)
		if err946 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err947 := (strconv.Atoi(flag.Arg(2)))
		if err947 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err948 := (strconv.Atoi(flag.Arg(3)))
		if err948 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.SearchAdSuggestPrice(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdTracking requires 2 args")
			flag.Usage()
		}
		arg949 := flag.Arg(1)
		mbTrans950 := thrift.NewTMemoryBufferLen(len(arg949))
		defer mbTrans950.Close()
		_, err951 := mbTrans950.WriteString(arg949)
		if err951 != nil {
			Usage()
			return
		}
		factory952 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt953 := factory952.GetProtocol(mbTrans950)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err954 := argvalue0.Read(jsProt953)
		if err954 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg955 := flag.Arg(2)
		mbTrans956 := thrift.NewTMemoryBufferLen(len(arg955))
		defer mbTrans956.Close()
		_, err957 := mbTrans956.WriteString(arg955)
		if err957 != nil {
			Usage()
			return
		}
		factory958 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt959 := factory958.GetProtocol(mbTrans956)
		argvalue1 := bidmaster_server.NewAdTracking()
		err960 := argvalue1.Read(jsProt959)
		if err960 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAdTracking(value0, value1))
		fmt.Print("\n")
		break
	case "editAdTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdTracking requires 2 args")
			flag.Usage()
		}
		arg961 := flag.Arg(1)
		mbTrans962 := thrift.NewTMemoryBufferLen(len(arg961))
		defer mbTrans962.Close()
		_, err963 := mbTrans962.WriteString(arg961)
		if err963 != nil {
			Usage()
			return
		}
		factory964 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt965 := factory964.GetProtocol(mbTrans962)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err966 := argvalue0.Read(jsProt965)
		if err966 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg967 := flag.Arg(2)
		mbTrans968 := thrift.NewTMemoryBufferLen(len(arg967))
		defer mbTrans968.Close()
		_, err969 := mbTrans968.WriteString(arg967)
		if err969 != nil {
			Usage()
			return
		}
		factory970 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt971 := factory970.GetProtocol(mbTrans968)
		argvalue1 := bidmaster_server.NewAdTracking()
		err972 := argvalue1.Read(jsProt971)
		if err972 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAdTracking(value0, value1))
		fmt.Print("\n")
		break
	case "getAdTrackingsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdTrackingsByIds requires 2 args")
			flag.Usage()
		}
		arg973 := flag.Arg(1)
		mbTrans974 := thrift.NewTMemoryBufferLen(len(arg973))
		defer mbTrans974.Close()
		_, err975 := mbTrans974.WriteString(arg973)
		if err975 != nil {
			Usage()
			return
		}
		factory976 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt977 := factory976.GetProtocol(mbTrans974)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err978 := argvalue0.Read(jsProt977)
		if err978 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg979 := flag.Arg(2)
		mbTrans980 := thrift.NewTMemoryBufferLen(len(arg979))
		defer mbTrans980.Close()
		_, err981 := mbTrans980.WriteString(arg979)
		if err981 != nil {
			Usage()
			return
		}
		factory982 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt983 := factory982.GetProtocol(mbTrans980)
		containerStruct1 := bidmaster_server.NewGetAdTrackingsByIdsArgs()
		err984 := containerStruct1.ReadField2(jsProt983)
		if err984 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdTrackingsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdTrackingByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdTrackingByParams requires 2 args")
			flag.Usage()
		}
		arg985 := flag.Arg(1)
		mbTrans986 := thrift.NewTMemoryBufferLen(len(arg985))
		defer mbTrans986.Close()
		_, err987 := mbTrans986.WriteString(arg985)
		if err987 != nil {
			Usage()
			return
		}
		factory988 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt989 := factory988.GetProtocol(mbTrans986)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err990 := argvalue0.Read(jsProt989)
		if err990 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg991 := flag.Arg(2)
		mbTrans992 := thrift.NewTMemoryBufferLen(len(arg991))
		defer mbTrans992.Close()
		_, err993 := mbTrans992.WriteString(arg991)
		if err993 != nil {
			Usage()
			return
		}
		factory994 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt995 := factory994.GetProtocol(mbTrans992)
		argvalue1 := bidmaster_server.NewAdTrackingParams()
		err996 := argvalue1.Read(jsProt995)
		if err996 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdTrackingByParams(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdTrackingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdTrackingByIds requires 2 args")
			flag.Usage()
		}
		arg997 := flag.Arg(1)
		mbTrans998 := thrift.NewTMemoryBufferLen(len(arg997))
		defer mbTrans998.Close()
		_, err999 := mbTrans998.WriteString(arg997)
		if err999 != nil {
			Usage()
			return
		}
		factory1000 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1001 := factory1000.GetProtocol(mbTrans998)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1002 := argvalue0.Read(jsProt1001)
		if err1002 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1003 := flag.Arg(2)
		mbTrans1004 := thrift.NewTMemoryBufferLen(len(arg1003))
		defer mbTrans1004.Close()
		_, err1005 := mbTrans1004.WriteString(arg1003)
		if err1005 != nil {
			Usage()
			return
		}
		factory1006 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1007 := factory1006.GetProtocol(mbTrans1004)
		containerStruct1 := bidmaster_server.NewDeleteAdTrackingByIdsArgs()
		err1008 := containerStruct1.ReadField2(jsProt1007)
		if err1008 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAdTrackingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addDeviceFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddDeviceFile requires 2 args")
			flag.Usage()
		}
		arg1009 := flag.Arg(1)
		mbTrans1010 := thrift.NewTMemoryBufferLen(len(arg1009))
		defer mbTrans1010.Close()
		_, err1011 := mbTrans1010.WriteString(arg1009)
		if err1011 != nil {
			Usage()
			return
		}
		factory1012 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1013 := factory1012.GetProtocol(mbTrans1010)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1014 := argvalue0.Read(jsProt1013)
		if err1014 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1015 := flag.Arg(2)
		mbTrans1016 := thrift.NewTMemoryBufferLen(len(arg1015))
		defer mbTrans1016.Close()
		_, err1017 := mbTrans1016.WriteString(arg1015)
		if err1017 != nil {
			Usage()
			return
		}
		factory1018 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1019 := factory1018.GetProtocol(mbTrans1016)
		argvalue1 := bidmaster_server.NewDeviceFile()
		err1020 := argvalue1.Read(jsProt1019)
		if err1020 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddDeviceFile(value0, value1))
		fmt.Print("\n")
		break
	case "getDeviceFilesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDeviceFilesByIds requires 2 args")
			flag.Usage()
		}
		arg1021 := flag.Arg(1)
		mbTrans1022 := thrift.NewTMemoryBufferLen(len(arg1021))
		defer mbTrans1022.Close()
		_, err1023 := mbTrans1022.WriteString(arg1021)
		if err1023 != nil {
			Usage()
			return
		}
		factory1024 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1025 := factory1024.GetProtocol(mbTrans1022)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1026 := argvalue0.Read(jsProt1025)
		if err1026 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1027 := flag.Arg(2)
		mbTrans1028 := thrift.NewTMemoryBufferLen(len(arg1027))
		defer mbTrans1028.Close()
		_, err1029 := mbTrans1028.WriteString(arg1027)
		if err1029 != nil {
			Usage()
			return
		}
		factory1030 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1031 := factory1030.GetProtocol(mbTrans1028)
		containerStruct1 := bidmaster_server.NewGetDeviceFilesByIdsArgs()
		err1032 := containerStruct1.ReadField2(jsProt1031)
		if err1032 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDeviceFilesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
