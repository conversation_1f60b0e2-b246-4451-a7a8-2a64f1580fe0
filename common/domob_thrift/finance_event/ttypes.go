// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package finance_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/event"
	"rtb_model_server/common/domob_thrift/finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__
var _ = finance_types.GoUnusedProtection__
var GoUnusedProtection__ int

type UidInt common.UidInt

type IdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type FinancialStatus *finance_types.FinancialStatus

type MediaAccountSummary *finance_types.MediaAccountSummary

type AdAccountSummary *finance_types.AdAccountSummary

type FinancialProfile *finance_types.FinancialProfile

type RechargeRecord *finance_types.RechargeRecord

type WithdrawRecord *finance_types.WithdrawRecord

type MediaInvoiceRecord *finance_types.MediaInvoiceRecord

type TeamMember *finance_types.TeamMember

type PaymentRecord *finance_types.PaymentRecord

type UserRechargedEvent struct {
	RechargeRecord      *finance_types.RechargeRecord      `thrift:"rechargeRecord,1" json:"rechargeRecord"`
	Status              *finance_types.FinancialStatus     `thrift:"status,2" json:"status"`
	AdAccountSummary    *finance_types.AdAccountSummary    `thrift:"adAccountSummary,3" json:"adAccountSummary"`
	MediaAccountSummary *finance_types.MediaAccountSummary `thrift:"mediaAccountSummary,4" json:"mediaAccountSummary"`
}

func NewUserRechargedEvent() *UserRechargedEvent {
	return &UserRechargedEvent{}
}

func (p *UserRechargedEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserRechargedEvent) readField1(iprot thrift.TProtocol) error {
	p.RechargeRecord = finance_types.NewRechargeRecord()
	if err := p.RechargeRecord.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RechargeRecord)
	}
	return nil
}

func (p *UserRechargedEvent) readField2(iprot thrift.TProtocol) error {
	p.Status = finance_types.NewFinancialStatus()
	if err := p.Status.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Status)
	}
	return nil
}

func (p *UserRechargedEvent) readField3(iprot thrift.TProtocol) error {
	p.AdAccountSummary = finance_types.NewAdAccountSummary()
	if err := p.AdAccountSummary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdAccountSummary)
	}
	return nil
}

func (p *UserRechargedEvent) readField4(iprot thrift.TProtocol) error {
	p.MediaAccountSummary = finance_types.NewMediaAccountSummary()
	if err := p.MediaAccountSummary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAccountSummary)
	}
	return nil
}

func (p *UserRechargedEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserRechargedEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserRechargedEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RechargeRecord != nil {
		if err := oprot.WriteFieldBegin("rechargeRecord", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rechargeRecord: %s", p, err)
		}
		if err := p.RechargeRecord.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RechargeRecord)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rechargeRecord: %s", p, err)
		}
	}
	return err
}

func (p *UserRechargedEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Status != nil {
		if err := oprot.WriteFieldBegin("status", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := p.Status.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Status)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *UserRechargedEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AdAccountSummary != nil {
		if err := oprot.WriteFieldBegin("adAccountSummary", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:adAccountSummary: %s", p, err)
		}
		if err := p.AdAccountSummary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdAccountSummary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:adAccountSummary: %s", p, err)
		}
	}
	return err
}

func (p *UserRechargedEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.MediaAccountSummary != nil {
		if err := oprot.WriteFieldBegin("mediaAccountSummary", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:mediaAccountSummary: %s", p, err)
		}
		if err := p.MediaAccountSummary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAccountSummary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:mediaAccountSummary: %s", p, err)
		}
	}
	return err
}

func (p *UserRechargedEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserRechargedEvent(%+v)", *p)
}

type FinancialUpdateEvent struct {
	Status              *finance_types.FinancialStatus     `thrift:"status,1" json:"status"`
	AdAccountSummary    *finance_types.AdAccountSummary    `thrift:"adAccountSummary,2" json:"adAccountSummary"`
	MediaAccountSummary *finance_types.MediaAccountSummary `thrift:"mediaAccountSummary,3" json:"mediaAccountSummary"`
	Profile             *finance_types.FinancialProfile    `thrift:"profile,4" json:"profile"`
}

func NewFinancialUpdateEvent() *FinancialUpdateEvent {
	return &FinancialUpdateEvent{}
}

func (p *FinancialUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinancialUpdateEvent) readField1(iprot thrift.TProtocol) error {
	p.Status = finance_types.NewFinancialStatus()
	if err := p.Status.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Status)
	}
	return nil
}

func (p *FinancialUpdateEvent) readField2(iprot thrift.TProtocol) error {
	p.AdAccountSummary = finance_types.NewAdAccountSummary()
	if err := p.AdAccountSummary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdAccountSummary)
	}
	return nil
}

func (p *FinancialUpdateEvent) readField3(iprot thrift.TProtocol) error {
	p.MediaAccountSummary = finance_types.NewMediaAccountSummary()
	if err := p.MediaAccountSummary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAccountSummary)
	}
	return nil
}

func (p *FinancialUpdateEvent) readField4(iprot thrift.TProtocol) error {
	p.Profile = finance_types.NewFinancialProfile()
	if err := p.Profile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Profile)
	}
	return nil
}

func (p *FinancialUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinancialUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinancialUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Status != nil {
		if err := oprot.WriteFieldBegin("status", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := p.Status.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Status)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *FinancialUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AdAccountSummary != nil {
		if err := oprot.WriteFieldBegin("adAccountSummary", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:adAccountSummary: %s", p, err)
		}
		if err := p.AdAccountSummary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdAccountSummary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:adAccountSummary: %s", p, err)
		}
	}
	return err
}

func (p *FinancialUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaAccountSummary != nil {
		if err := oprot.WriteFieldBegin("mediaAccountSummary", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaAccountSummary: %s", p, err)
		}
		if err := p.MediaAccountSummary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAccountSummary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaAccountSummary: %s", p, err)
		}
	}
	return err
}

func (p *FinancialUpdateEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Profile != nil {
		if err := oprot.WriteFieldBegin("profile", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:profile: %s", p, err)
		}
		if err := p.Profile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Profile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:profile: %s", p, err)
		}
	}
	return err
}

func (p *FinancialUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinancialUpdateEvent(%+v)", *p)
}

type AdBalancePreOverEvent struct {
	AdUid      UidInt  `thrift:"adUid,1" json:"adUid"`
	Balance    Amount  `thrift:"balance,2" json:"balance"`
	Credit     Amount  `thrift:"credit,3" json:"credit"`
	PreSettled Amount  `thrift:"preSettled,4" json:"preSettled"`
	Timestamp  TimeInt `thrift:"timestamp,5" json:"timestamp"`
}

func NewAdBalancePreOverEvent() *AdBalancePreOverEvent {
	return &AdBalancePreOverEvent{}
}

func (p *AdBalancePreOverEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdBalancePreOverEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *AdBalancePreOverEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Balance = Amount(v)
	}
	return nil
}

func (p *AdBalancePreOverEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Credit = Amount(v)
	}
	return nil
}

func (p *AdBalancePreOverEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PreSettled = Amount(v)
	}
	return nil
}

func (p *AdBalancePreOverEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *AdBalancePreOverEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdBalancePreOverEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdBalancePreOverEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.adUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adUid: %s", p, err)
	}
	return err
}

func (p *AdBalancePreOverEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:balance: %s", p, err)
	}
	return err
}

func (p *AdBalancePreOverEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("credit", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:credit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Credit)); err != nil {
		return fmt.Errorf("%T.credit (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:credit: %s", p, err)
	}
	return err
}

func (p *AdBalancePreOverEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("preSettled", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:preSettled: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PreSettled)); err != nil {
		return fmt.Errorf("%T.preSettled (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:preSettled: %s", p, err)
	}
	return err
}

func (p *AdBalancePreOverEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *AdBalancePreOverEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdBalancePreOverEvent(%+v)", *p)
}

type AdPlanOutOfBudgetEvent struct {
	AdUid      UidInt  `thrift:"adUid,1" json:"adUid"`
	AdPlanId   IdInt   `thrift:"adPlanId,2" json:"adPlanId"`
	Budget     Amount  `thrift:"budget,3" json:"budget"`
	PreSettled Amount  `thrift:"preSettled,4" json:"preSettled"`
	Timestamp  TimeInt `thrift:"timestamp,5" json:"timestamp"`
}

func NewAdPlanOutOfBudgetEvent() *AdPlanOutOfBudgetEvent {
	return &AdPlanOutOfBudgetEvent{}
}

func (p *AdPlanOutOfBudgetEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPlanId = IdInt(v)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Budget = Amount(v)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PreSettled = Amount(v)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlanOutOfBudgetEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlanOutOfBudgetEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.adUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adUid: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfBudgetEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPlanId: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfBudgetEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:budget: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfBudgetEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("preSettled", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:preSettled: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PreSettled)); err != nil {
		return fmt.Errorf("%T.preSettled (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:preSettled: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfBudgetEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfBudgetEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlanOutOfBudgetEvent(%+v)", *p)
}

type AdPlanOutOfTotalBudgetEvent struct {
	AdUid       UidInt  `thrift:"adUid,1" json:"adUid"`
	AdPlanId    IdInt   `thrift:"adPlanId,2" json:"adPlanId"`
	TotalBudget Amount  `thrift:"totalBudget,3" json:"totalBudget"`
	Consumed    Amount  `thrift:"consumed,4" json:"consumed"`
	Timestamp   TimeInt `thrift:"timestamp,5" json:"timestamp"`
}

func NewAdPlanOutOfTotalBudgetEvent() *AdPlanOutOfTotalBudgetEvent {
	return &AdPlanOutOfTotalBudgetEvent{}
}

func (p *AdPlanOutOfTotalBudgetEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPlanId = IdInt(v)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalBudget = Amount(v)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Consumed = Amount(v)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlanOutOfTotalBudgetEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlanOutOfTotalBudgetEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.adUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adUid: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfTotalBudgetEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPlanId: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfTotalBudgetEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:totalBudget: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfTotalBudgetEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumed", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consumed)); err != nil {
		return fmt.Errorf("%T.consumed (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:consumed: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfTotalBudgetEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *AdPlanOutOfTotalBudgetEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlanOutOfTotalBudgetEvent(%+v)", *p)
}

type AdPlanBudgetOkEvent struct {
	AdUid      UidInt  `thrift:"adUid,1" json:"adUid"`
	AdPlanId   IdInt   `thrift:"adPlanId,2" json:"adPlanId"`
	Budget     Amount  `thrift:"budget,3" json:"budget"`
	PreSettled Amount  `thrift:"preSettled,4" json:"preSettled"`
	Timestamp  TimeInt `thrift:"timestamp,5" json:"timestamp"`
}

func NewAdPlanBudgetOkEvent() *AdPlanBudgetOkEvent {
	return &AdPlanBudgetOkEvent{}
}

func (p *AdPlanBudgetOkEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPlanId = IdInt(v)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Budget = Amount(v)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PreSettled = Amount(v)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlanBudgetOkEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlanBudgetOkEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.adUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adUid: %s", p, err)
	}
	return err
}

func (p *AdPlanBudgetOkEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPlanId: %s", p, err)
	}
	return err
}

func (p *AdPlanBudgetOkEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:budget: %s", p, err)
	}
	return err
}

func (p *AdPlanBudgetOkEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("preSettled", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:preSettled: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PreSettled)); err != nil {
		return fmt.Errorf("%T.preSettled (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:preSettled: %s", p, err)
	}
	return err
}

func (p *AdPlanBudgetOkEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *AdPlanBudgetOkEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlanBudgetOkEvent(%+v)", *p)
}

type AdPlanTotalBudgetOkEvent struct {
	AdUid       UidInt  `thrift:"adUid,1" json:"adUid"`
	AdPlanId    IdInt   `thrift:"adPlanId,2" json:"adPlanId"`
	TotalBudget Amount  `thrift:"totalBudget,3" json:"totalBudget"`
	Consumed    Amount  `thrift:"consumed,4" json:"consumed"`
	Timestamp   TimeInt `thrift:"timestamp,5" json:"timestamp"`
}

func NewAdPlanTotalBudgetOkEvent() *AdPlanTotalBudgetOkEvent {
	return &AdPlanTotalBudgetOkEvent{}
}

func (p *AdPlanTotalBudgetOkEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPlanId = IdInt(v)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalBudget = Amount(v)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Consumed = Amount(v)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlanTotalBudgetOkEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlanTotalBudgetOkEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.adUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adUid: %s", p, err)
	}
	return err
}

func (p *AdPlanTotalBudgetOkEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPlanId: %s", p, err)
	}
	return err
}

func (p *AdPlanTotalBudgetOkEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:totalBudget: %s", p, err)
	}
	return err
}

func (p *AdPlanTotalBudgetOkEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumed", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consumed)); err != nil {
		return fmt.Errorf("%T.consumed (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:consumed: %s", p, err)
	}
	return err
}

func (p *AdPlanTotalBudgetOkEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *AdPlanTotalBudgetOkEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlanTotalBudgetOkEvent(%+v)", *p)
}

type AdStrategyOutOfBudgetEvent struct {
	AdUid        UidInt  `thrift:"adUid,1" json:"adUid"`
	AdPlanId     IdInt   `thrift:"adPlanId,2" json:"adPlanId"`
	AdStrategyId IdInt   `thrift:"adStrategyId,3" json:"adStrategyId"`
	BudgetLimit  bool    `thrift:"budgetLimit,4" json:"budgetLimit"`
	Budget       Amount  `thrift:"budget,5" json:"budget"`
	PreSettled   Amount  `thrift:"preSettled,6" json:"preSettled"`
	Timestamp    TimeInt `thrift:"timestamp,7" json:"timestamp"`
}

func NewAdStrategyOutOfBudgetEvent() *AdStrategyOutOfBudgetEvent {
	return &AdStrategyOutOfBudgetEvent{}
}

func (p *AdStrategyOutOfBudgetEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPlanId = IdInt(v)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AdStrategyId = IdInt(v)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.BudgetLimit = v
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Budget = Amount(v)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PreSettled = Amount(v)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStrategyOutOfBudgetEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStrategyOutOfBudgetEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adUid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.adUid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adUid: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPlanId: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adStrategyId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:adStrategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdStrategyId)); err != nil {
		return fmt.Errorf("%T.adStrategyId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:adStrategyId: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budgetLimit", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:budgetLimit: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.BudgetLimit)); err != nil {
		return fmt.Errorf("%T.budgetLimit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:budgetLimit: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:budget: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("preSettled", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:preSettled: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PreSettled)); err != nil {
		return fmt.Errorf("%T.preSettled (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:preSettled: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:timestamp: %s", p, err)
	}
	return err
}

func (p *AdStrategyOutOfBudgetEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStrategyOutOfBudgetEvent(%+v)", *p)
}

type MediaWithdrawEvent struct {
	Uid    UidInt                        `thrift:"uid,1" json:"uid"`
	Code   event.EventCode               `thrift:"code,2" json:"code"`
	Record *finance_types.WithdrawRecord `thrift:"record,3" json:"record"`
}

func NewMediaWithdrawEvent() *MediaWithdrawEvent {
	return &MediaWithdrawEvent{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaWithdrawEvent) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *MediaWithdrawEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaWithdrawEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaWithdrawEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Code = event.EventCode(v)
	}
	return nil
}

func (p *MediaWithdrawEvent) readField3(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewWithdrawRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *MediaWithdrawEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaWithdrawEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaWithdrawEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaWithdrawEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:code: %s", p, err)
		}
	}
	return err
}

func (p *MediaWithdrawEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:record: %s", p, err)
		}
	}
	return err
}

func (p *MediaWithdrawEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaWithdrawEvent(%+v)", *p)
}

type MediaInvoiceEvent struct {
	Uid    UidInt                            `thrift:"uid,1" json:"uid"`
	Code   event.EventCode                   `thrift:"code,2" json:"code"`
	Record *finance_types.MediaInvoiceRecord `thrift:"record,3" json:"record"`
}

func NewMediaInvoiceEvent() *MediaInvoiceEvent {
	return &MediaInvoiceEvent{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaInvoiceEvent) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *MediaInvoiceEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaInvoiceEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaInvoiceEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Code = event.EventCode(v)
	}
	return nil
}

func (p *MediaInvoiceEvent) readField3(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewMediaInvoiceRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *MediaInvoiceEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaInvoiceEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaInvoiceEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaInvoiceEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:code: %s", p, err)
		}
	}
	return err
}

func (p *MediaInvoiceEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:record: %s", p, err)
		}
	}
	return err
}

func (p *MediaInvoiceEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaInvoiceEvent(%+v)", *p)
}

type IdentityAuditEvent struct {
	Uid     UidInt                    `thrift:"uid,1" json:"uid"`
	Code    event.EventCode           `thrift:"code,2" json:"code"`
	Member  *finance_types.TeamMember `thrift:"member,3" json:"member"`
	ExtInfo map[string]string         `thrift:"extInfo,4" json:"extInfo"`
}

func NewIdentityAuditEvent() *IdentityAuditEvent {
	return &IdentityAuditEvent{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IdentityAuditEvent) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *IdentityAuditEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IdentityAuditEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *IdentityAuditEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Code = event.EventCode(v)
	}
	return nil
}

func (p *IdentityAuditEvent) readField3(iprot thrift.TProtocol) error {
	p.Member = finance_types.NewTeamMember()
	if err := p.Member.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Member)
	}
	return nil
}

func (p *IdentityAuditEvent) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.ExtInfo[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *IdentityAuditEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IdentityAuditEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IdentityAuditEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IdentityAuditEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:code: %s", p, err)
		}
	}
	return err
}

func (p *IdentityAuditEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Member != nil {
		if err := oprot.WriteFieldBegin("member", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:member: %s", p, err)
		}
		if err := p.Member.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Member)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:member: %s", p, err)
		}
	}
	return err
}

func (p *IdentityAuditEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *IdentityAuditEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IdentityAuditEvent(%+v)", *p)
}

type MediaPaymentEvent struct {
	Uid    UidInt                       `thrift:"uid,1" json:"uid"`
	Code   event.EventCode              `thrift:"code,2" json:"code"`
	Record *finance_types.PaymentRecord `thrift:"record,3" json:"record"`
}

func NewMediaPaymentEvent() *MediaPaymentEvent {
	return &MediaPaymentEvent{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaPaymentEvent) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *MediaPaymentEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaPaymentEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaPaymentEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Code = event.EventCode(v)
	}
	return nil
}

func (p *MediaPaymentEvent) readField3(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewPaymentRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *MediaPaymentEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaPaymentEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaPaymentEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaPaymentEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:code: %s", p, err)
		}
	}
	return err
}

func (p *MediaPaymentEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:record: %s", p, err)
		}
	}
	return err
}

func (p *MediaPaymentEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaPaymentEvent(%+v)", *p)
}
