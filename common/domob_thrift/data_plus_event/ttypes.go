// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_plus_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/data_plus_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = data_plus_types.GoUnusedProtection__
var GoUnusedProtection__ int

type DataPlusEventType int64

const (
	DataPlusEventType_DET_UNKNOWN DataPlusEventType = 0
	DataPlusEventType_DET_ADD     DataPlusEventType = 1
	DataPlusEventType_DET_UPDATE  DataPlusEventType = 2
	DataPlusEventType_DET_DELETE  DataPlusEventType = 3
)

func (p DataPlusEventType) String() string {
	switch p {
	case DataPlusEventType_DET_UNKNOWN:
		return "DataPlusEventType_DET_UNKNOWN"
	case DataPlusEventType_DET_ADD:
		return "DataPlusEventType_DET_ADD"
	case DataPlusEventType_DET_UPDATE:
		return "DataPlusEventType_DET_UPDATE"
	case DataPlusEventType_DET_DELETE:
		return "DataPlusEventType_DET_DELETE"
	}
	return "<UNSET>"
}

func DataPlusEventTypeFromString(s string) (DataPlusEventType, error) {
	switch s {
	case "DataPlusEventType_DET_UNKNOWN":
		return DataPlusEventType_DET_UNKNOWN, nil
	case "DataPlusEventType_DET_ADD":
		return DataPlusEventType_DET_ADD, nil
	case "DataPlusEventType_DET_UPDATE":
		return DataPlusEventType_DET_UPDATE, nil
	case "DataPlusEventType_DET_DELETE":
		return DataPlusEventType_DET_DELETE, nil
	}
	return DataPlusEventType(math.MinInt32 - 1), fmt.Errorf("not a valid DataPlusEventType string")
}

type DataPlusEventCategory int64

const (
	DataPlusEventCategory_DEC_UNKNOWN  DataPlusEventCategory = 0
	DataPlusEventCategory_DEC_Audience DataPlusEventCategory = 1
)

func (p DataPlusEventCategory) String() string {
	switch p {
	case DataPlusEventCategory_DEC_UNKNOWN:
		return "DataPlusEventCategory_DEC_UNKNOWN"
	case DataPlusEventCategory_DEC_Audience:
		return "DataPlusEventCategory_DEC_Audience"
	}
	return "<UNSET>"
}

func DataPlusEventCategoryFromString(s string) (DataPlusEventCategory, error) {
	switch s {
	case "DataPlusEventCategory_DEC_UNKNOWN":
		return DataPlusEventCategory_DEC_UNKNOWN, nil
	case "DataPlusEventCategory_DEC_Audience":
		return DataPlusEventCategory_DEC_Audience, nil
	}
	return DataPlusEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid DataPlusEventCategory string")
}

type DataPlusCommonEvent struct {
	TypeA1   DataPlusEventType     `thrift:"type,1" json:"type"`
	Category DataPlusEventCategory `thrift:"category,2" json:"category"`
	Ids      []int32               `thrift:"ids,3" json:"ids"`
}

func NewDataPlusCommonEvent() *DataPlusCommonEvent {
	return &DataPlusCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DataPlusCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DataPlusCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DataPlusCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataPlusCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DataPlusEventType(v)
	}
	return nil
}

func (p *DataPlusCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DataPlusEventCategory(v)
	}
	return nil
}

func (p *DataPlusCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DataPlusCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataPlusCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataPlusCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DataPlusCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DataPlusCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DataPlusCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataPlusCommonEvent(%+v)", *p)
}
