// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"hdy_account"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAccountsByParams(RequestHeader header, AccountSearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAccount(RequestHeader header, HdyAccountInfo account)")
	fmt.Fprintln(os.Stderr, "  void editAccount(RequestHeader header, HdyAccountInfo account)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := hdy_account.NewAccountServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "searchAccountsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchAccountsByParams requires 4 args")
			flag.Usage()
		}
		arg21 := flag.Arg(1)
		mbTrans22 := thrift.NewTMemoryBufferLen(len(arg21))
		defer mbTrans22.Close()
		_, err23 := mbTrans22.WriteString(arg21)
		if err23 != nil {
			Usage()
			return
		}
		factory24 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt25 := factory24.GetProtocol(mbTrans22)
		argvalue0 := hdy_account.NewRequestHeader()
		err26 := argvalue0.Read(jsProt25)
		if err26 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg27 := flag.Arg(2)
		mbTrans28 := thrift.NewTMemoryBufferLen(len(arg27))
		defer mbTrans28.Close()
		_, err29 := mbTrans28.WriteString(arg27)
		if err29 != nil {
			Usage()
			return
		}
		factory30 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt31 := factory30.GetProtocol(mbTrans28)
		argvalue1 := hdy_account.NewAccountSearchParams()
		err32 := argvalue1.Read(jsProt31)
		if err32 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err33 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err33 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err34 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err34 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchAccountsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg35 := flag.Arg(1)
		mbTrans36 := thrift.NewTMemoryBufferLen(len(arg35))
		defer mbTrans36.Close()
		_, err37 := mbTrans36.WriteString(arg35)
		if err37 != nil {
			Usage()
			return
		}
		factory38 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt39 := factory38.GetProtocol(mbTrans36)
		argvalue0 := hdy_account.NewRequestHeader()
		err40 := argvalue0.Read(jsProt39)
		if err40 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg41 := flag.Arg(2)
		mbTrans42 := thrift.NewTMemoryBufferLen(len(arg41))
		defer mbTrans42.Close()
		_, err43 := mbTrans42.WriteString(arg41)
		if err43 != nil {
			Usage()
			return
		}
		factory44 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt45 := factory44.GetProtocol(mbTrans42)
		containerStruct1 := hdy_account.NewGetAccountsByIdsArgs()
		err46 := containerStruct1.ReadField2(jsProt45)
		if err46 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addAccount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAccount requires 2 args")
			flag.Usage()
		}
		arg47 := flag.Arg(1)
		mbTrans48 := thrift.NewTMemoryBufferLen(len(arg47))
		defer mbTrans48.Close()
		_, err49 := mbTrans48.WriteString(arg47)
		if err49 != nil {
			Usage()
			return
		}
		factory50 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt51 := factory50.GetProtocol(mbTrans48)
		argvalue0 := hdy_account.NewRequestHeader()
		err52 := argvalue0.Read(jsProt51)
		if err52 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg53 := flag.Arg(2)
		mbTrans54 := thrift.NewTMemoryBufferLen(len(arg53))
		defer mbTrans54.Close()
		_, err55 := mbTrans54.WriteString(arg53)
		if err55 != nil {
			Usage()
			return
		}
		factory56 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt57 := factory56.GetProtocol(mbTrans54)
		argvalue1 := hdy_account.NewHdyAccountInfo()
		err58 := argvalue1.Read(jsProt57)
		if err58 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAccount(value0, value1))
		fmt.Print("\n")
		break
	case "editAccount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAccount requires 2 args")
			flag.Usage()
		}
		arg59 := flag.Arg(1)
		mbTrans60 := thrift.NewTMemoryBufferLen(len(arg59))
		defer mbTrans60.Close()
		_, err61 := mbTrans60.WriteString(arg59)
		if err61 != nil {
			Usage()
			return
		}
		factory62 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt63 := factory62.GetProtocol(mbTrans60)
		argvalue0 := hdy_account.NewRequestHeader()
		err64 := argvalue0.Read(jsProt63)
		if err64 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg65 := flag.Arg(2)
		mbTrans66 := thrift.NewTMemoryBufferLen(len(arg65))
		defer mbTrans66.Close()
		_, err67 := mbTrans66.WriteString(arg65)
		if err67 != nil {
			Usage()
			return
		}
		factory68 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt69 := factory68.GetProtocol(mbTrans66)
		argvalue1 := hdy_account.NewHdyAccountInfo()
		err70 := argvalue1.Read(jsProt69)
		if err70 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAccount(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
