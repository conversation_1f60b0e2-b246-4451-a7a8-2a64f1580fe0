// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_account

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/hdy_account_types"
	"rtb_model_server/common/domob_thrift/hdy_exception"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = hdy_exception.GoUnusedProtection__
var _ = hdy_account_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type AccountService interface {
	// 根据搜索条件查询账户列表
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Params: 查询参数列表 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量限制 *
	SearchAccountsByParams(header *common.RequestHeader, params *hdy_account_types.AccountSearchParams, offset int64, limit int64) (r *common.QueryResult, e *hdy_exception.HdyException, err error)
	// 根据ID获取hdy 广告主账户信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Ids: account ids *
	GetAccountsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*hdy_account_types.HdyAccountInfo, e *hdy_exception.HdyException, err error)
	// 添加广告主账户
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Account: account信息 *
	AddAccount(header *common.RequestHeader, account *hdy_account_types.HdyAccountInfo) (r int32, e *hdy_exception.HdyException, err error)
	// 编辑广告主信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Account: account信息 *
	EditAccount(header *common.RequestHeader, account *hdy_account_types.HdyAccountInfo) (e *hdy_exception.HdyException, err error)
}

type AccountServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAccountServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AccountServiceClient {
	return &AccountServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAccountServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AccountServiceClient {
	return &AccountServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 根据搜索条件查询账户列表
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Params: 查询参数列表 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量限制 *
func (p *AccountServiceClient) SearchAccountsByParams(header *common.RequestHeader, params *hdy_account_types.AccountSearchParams, offset int64, limit int64) (r *common.QueryResult, e *hdy_exception.HdyException, err error) {
	if err = p.sendSearchAccountsByParams(header, params, offset, limit); err != nil {
		return
	}
	return p.recvSearchAccountsByParams()
}

func (p *AccountServiceClient) sendSearchAccountsByParams(header *common.RequestHeader, params *hdy_account_types.AccountSearchParams, offset int64, limit int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchAccountsByParams", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewSearchAccountsByParamsArgs()
	args0.Header = header
	args0.Params = params
	args0.Offset = offset
	args0.Limit = limit
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServiceClient) recvSearchAccountsByParams() (value *common.QueryResult, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewSearchAccountsByParamsResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 根据ID获取hdy 广告主账户信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Ids: account ids *
func (p *AccountServiceClient) GetAccountsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*hdy_account_types.HdyAccountInfo, e *hdy_exception.HdyException, err error) {
	if err = p.sendGetAccountsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetAccountsByIds()
}

func (p *AccountServiceClient) sendGetAccountsByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAccountsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetAccountsByIdsArgs()
	args4.Header = header
	args4.Ids = ids
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServiceClient) recvGetAccountsByIds() (value map[int32]*hdy_account_types.HdyAccountInfo, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetAccountsByIdsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

// 添加广告主账户
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Account: account信息 *
func (p *AccountServiceClient) AddAccount(header *common.RequestHeader, account *hdy_account_types.HdyAccountInfo) (r int32, e *hdy_exception.HdyException, err error) {
	if err = p.sendAddAccount(header, account); err != nil {
		return
	}
	return p.recvAddAccount()
}

func (p *AccountServiceClient) sendAddAccount(header *common.RequestHeader, account *hdy_account_types.HdyAccountInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addAccount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewAddAccountArgs()
	args8.Header = header
	args8.Account = account
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServiceClient) recvAddAccount() (value int32, e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewAddAccountResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 编辑广告主信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Account: account信息 *
func (p *AccountServiceClient) EditAccount(header *common.RequestHeader, account *hdy_account_types.HdyAccountInfo) (e *hdy_exception.HdyException, err error) {
	if err = p.sendEditAccount(header, account); err != nil {
		return
	}
	return p.recvEditAccount()
}

func (p *AccountServiceClient) sendEditAccount(header *common.RequestHeader, account *hdy_account_types.HdyAccountInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editAccount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewEditAccountArgs()
	args12.Header = header
	args12.Account = account
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AccountServiceClient) recvEditAccount() (e *hdy_exception.HdyException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewEditAccountResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result13.E != nil {
		e = result13.E
	}
	return
}

type AccountServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AccountService
}

func (p *AccountServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AccountServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AccountServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAccountServiceProcessor(handler AccountService) *AccountServiceProcessor {

	self16 := &AccountServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self16.processorMap["searchAccountsByParams"] = &accountServiceProcessorSearchAccountsByParams{handler: handler}
	self16.processorMap["getAccountsByIds"] = &accountServiceProcessorGetAccountsByIds{handler: handler}
	self16.processorMap["addAccount"] = &accountServiceProcessorAddAccount{handler: handler}
	self16.processorMap["editAccount"] = &accountServiceProcessorEditAccount{handler: handler}
	return self16
}

func (p *AccountServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x17 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x17.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x17

}

type accountServiceProcessorSearchAccountsByParams struct {
	handler AccountService
}

func (p *accountServiceProcessorSearchAccountsByParams) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchAccountsByParamsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchAccountsByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchAccountsByParamsResult()
	if result.Success, result.E, err = p.handler.SearchAccountsByParams(args.Header, args.Params, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchAccountsByParams: "+err.Error())
		oprot.WriteMessageBegin("searchAccountsByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchAccountsByParams", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServiceProcessorGetAccountsByIds struct {
	handler AccountService
}

func (p *accountServiceProcessorGetAccountsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAccountsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAccountsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAccountsByIdsResult()
	if result.Success, result.E, err = p.handler.GetAccountsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAccountsByIds: "+err.Error())
		oprot.WriteMessageBegin("getAccountsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAccountsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServiceProcessorAddAccount struct {
	handler AccountService
}

func (p *accountServiceProcessorAddAccount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAccountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAccountResult()
	if result.Success, result.E, err = p.handler.AddAccount(args.Header, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addAccount: "+err.Error())
		oprot.WriteMessageBegin("addAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addAccount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type accountServiceProcessorEditAccount struct {
	handler AccountService
}

func (p *accountServiceProcessorEditAccount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditAccountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditAccountResult()
	if result.E, err = p.handler.EditAccount(args.Header, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editAccount: "+err.Error())
		oprot.WriteMessageBegin("editAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editAccount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type SearchAccountsByParamsArgs struct {
	Header *common.RequestHeader                  `thrift:"header,1" json:"header"`
	Params *hdy_account_types.AccountSearchParams `thrift:"params,2" json:"params"`
	Offset int64                                  `thrift:"offset,3" json:"offset"`
	Limit  int64                                  `thrift:"limit,4" json:"limit"`
}

func NewSearchAccountsByParamsArgs() *SearchAccountsByParamsArgs {
	return &SearchAccountsByParamsArgs{}
}

func (p *SearchAccountsByParamsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchAccountsByParamsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchAccountsByParamsArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = hdy_account_types.NewAccountSearchParams()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *SearchAccountsByParamsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SearchAccountsByParamsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SearchAccountsByParamsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchAccountsByParams_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchAccountsByParamsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchAccountsByParamsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *SearchAccountsByParamsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *SearchAccountsByParamsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *SearchAccountsByParamsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchAccountsByParamsArgs(%+v)", *p)
}

type SearchAccountsByParamsResult struct {
	Success *common.QueryResult         `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewSearchAccountsByParamsResult() *SearchAccountsByParamsResult {
	return &SearchAccountsByParamsResult{}
}

func (p *SearchAccountsByParamsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchAccountsByParamsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchAccountsByParamsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchAccountsByParamsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchAccountsByParams_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchAccountsByParamsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchAccountsByParamsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchAccountsByParamsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchAccountsByParamsResult(%+v)", *p)
}

type GetAccountsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetAccountsByIdsArgs() *GetAccountsByIdsArgs {
	return &GetAccountsByIdsArgs{}
}

func (p *GetAccountsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAccountsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAccountsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = v
		}
		p.Ids = append(p.Ids, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAccountsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAccountsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAccountsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAccountsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetAccountsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAccountsByIdsArgs(%+v)", *p)
}

type GetAccountsByIdsResult struct {
	Success map[int32]*hdy_account_types.HdyAccountInfo `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException                 `thrift:"e,1" json:"e"`
}

func NewGetAccountsByIdsResult() *GetAccountsByIdsResult {
	return &GetAccountsByIdsResult{}
}

func (p *GetAccountsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAccountsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*hdy_account_types.HdyAccountInfo, size)
	for i := 0; i < size; i++ {
		var _key19 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key19 = v
		}
		_val20 := hdy_account_types.NewHdyAccountInfo()
		if err := _val20.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val20)
		}
		p.Success[_key19] = _val20
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAccountsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetAccountsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAccountsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAccountsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAccountsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetAccountsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAccountsByIdsResult(%+v)", *p)
}

type AddAccountArgs struct {
	Header  *common.RequestHeader             `thrift:"header,1" json:"header"`
	Account *hdy_account_types.HdyAccountInfo `thrift:"account,2" json:"account"`
}

func NewAddAccountArgs() *AddAccountArgs {
	return &AddAccountArgs{}
}

func (p *AddAccountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAccountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAccountArgs) readField2(iprot thrift.TProtocol) error {
	p.Account = hdy_account_types.NewHdyAccountInfo()
	if err := p.Account.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Account)
	}
	return nil
}

func (p *AddAccountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAccount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAccountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAccountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Account != nil {
		if err := oprot.WriteFieldBegin("account", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
		}
		if err := p.Account.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Account)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:account: %s", p, err)
		}
	}
	return err
}

func (p *AddAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAccountArgs(%+v)", *p)
}

type AddAccountResult struct {
	Success int32                       `thrift:"success,0" json:"success"`
	E       *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewAddAccountResult() *AddAccountResult {
	return &AddAccountResult{}
}

func (p *AddAccountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAccountResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddAccountResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddAccountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAccount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAccountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddAccountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAccountResult(%+v)", *p)
}

type EditAccountArgs struct {
	Header  *common.RequestHeader             `thrift:"header,1" json:"header"`
	Account *hdy_account_types.HdyAccountInfo `thrift:"account,2" json:"account"`
}

func NewEditAccountArgs() *EditAccountArgs {
	return &EditAccountArgs{}
}

func (p *EditAccountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAccountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditAccountArgs) readField2(iprot thrift.TProtocol) error {
	p.Account = hdy_account_types.NewHdyAccountInfo()
	if err := p.Account.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Account)
	}
	return nil
}

func (p *EditAccountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editAccount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAccountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditAccountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Account != nil {
		if err := oprot.WriteFieldBegin("account", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
		}
		if err := p.Account.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Account)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:account: %s", p, err)
		}
	}
	return err
}

func (p *EditAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAccountArgs(%+v)", *p)
}

type EditAccountResult struct {
	E *hdy_exception.HdyException `thrift:"e,1" json:"e"`
}

func NewEditAccountResult() *EditAccountResult {
	return &EditAccountResult{}
}

func (p *EditAccountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAccountResult) readField1(iprot thrift.TProtocol) error {
	p.E = hdy_exception.NewHdyException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditAccountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editAccount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAccountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAccountResult(%+v)", *p)
}
