// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_account

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/hdy_account_types"
	"rtb_model_server/common/domob_thrift/hdy_exception"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = hdy_exception.GoUnusedProtection__
var _ = hdy_account_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

func init() {
}
