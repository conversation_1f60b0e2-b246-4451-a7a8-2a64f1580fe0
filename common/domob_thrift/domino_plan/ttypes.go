// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package domino_plan

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/domino_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = domino_types.GoUnusedProtection__
var GoUnusedProtection__ int

type RequestHeader *common.RequestHeader

type IdInt common.IdInt

type TimeInt common.TimeInt

type DominoQueryResult *common.QueryResult

type PlanCycle domino_types.PlanCycle

type PlanStatus domino_types.PlanStatus

type AlarmStyle domino_types.AlarmStyle

type Plan *domino_types.Plan

type PlanDep *domino_types.PlanDep

type DominoErrorCode domino_types.DominoErrorCode
