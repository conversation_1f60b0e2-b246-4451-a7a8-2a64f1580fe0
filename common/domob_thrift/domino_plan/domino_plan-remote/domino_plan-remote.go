// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"domino_plan"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  IdInt addPlan(RequestHeader header, Plan aPlan)")
	fmt.Fprintln(os.Stderr, "  void editPlan(RequestHeader header, Plan aPlan)")
	fmt.Fprintln(os.<PERSON>, "  Plan getPlanByName(RequestHeader header, string name)")
	fmt.Fprintln(os.Stderr, "  DominoQueryResult listPlan(RequestHeader header, IdInt offset, IdInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  DominoQueryResult listPlanByMaintainer(RequestHeader header, string maintainer, IdInt offset, IdInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  DominoQueryResult searchPlanByName(RequestHeader header, string token, string maintainer, IdInt offset, IdInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getPlanByIds(RequestHeader header,  planIds)")
	fmt.Fprintln(os.Stderr, "   getPlanDeps(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "   getPlanDepIdsAll(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "   getDepPlans(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "  void savePlanDeps(RequestHeader header, IdInt planId,  deps)")
	fmt.Fprintln(os.Stderr, "  void deletePlanDeps(RequestHeader header, IdInt planId,  depPlanIds)")
	fmt.Fprintln(os.Stderr, "  void suspendPlan(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "  void stopPlan(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "  void restartPlan(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "  void deletePlan(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "   viewPlanByHost(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getMaintainers(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getUsersByPlanId(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := domino_plan.NewDominoPlanClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPlan requires 2 args")
			flag.Usage()
		}
		arg90 := flag.Arg(1)
		mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
		defer mbTrans91.Close()
		_, err92 := mbTrans91.WriteString(arg90)
		if err92 != nil {
			Usage()
			return
		}
		factory93 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt94 := factory93.GetProtocol(mbTrans91)
		argvalue0 := domino_plan.NewRequestHeader()
		err95 := argvalue0.Read(jsProt94)
		if err95 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		arg96 := flag.Arg(2)
		mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
		defer mbTrans97.Close()
		_, err98 := mbTrans97.WriteString(arg96)
		if err98 != nil {
			Usage()
			return
		}
		factory99 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt100 := factory99.GetProtocol(mbTrans97)
		argvalue1 := domino_plan.NewPlan()
		err101 := argvalue1.Read(jsProt100)
		if err101 != nil {
			Usage()
			return
		}
		value1 := domino_plan.Plan(argvalue1)
		fmt.Print(client.AddPlan(value0, value1))
		fmt.Print("\n")
		break
	case "editPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPlan requires 2 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := domino_plan.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		arg108 := flag.Arg(2)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue1 := domino_plan.NewPlan()
		err113 := argvalue1.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value1 := domino_plan.Plan(argvalue1)
		fmt.Print(client.EditPlan(value0, value1))
		fmt.Print("\n")
		break
	case "getPlanByName":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlanByName requires 2 args")
			flag.Usage()
		}
		arg114 := flag.Arg(1)
		mbTrans115 := thrift.NewTMemoryBufferLen(len(arg114))
		defer mbTrans115.Close()
		_, err116 := mbTrans115.WriteString(arg114)
		if err116 != nil {
			Usage()
			return
		}
		factory117 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt118 := factory117.GetProtocol(mbTrans115)
		argvalue0 := domino_plan.NewRequestHeader()
		err119 := argvalue0.Read(jsProt118)
		if err119 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetPlanByName(value0, value1))
		fmt.Print("\n")
		break
	case "listPlan":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListPlan requires 4 args")
			flag.Usage()
		}
		arg121 := flag.Arg(1)
		mbTrans122 := thrift.NewTMemoryBufferLen(len(arg121))
		defer mbTrans122.Close()
		_, err123 := mbTrans122.WriteString(arg121)
		if err123 != nil {
			Usage()
			return
		}
		factory124 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt125 := factory124.GetProtocol(mbTrans122)
		argvalue0 := domino_plan.NewRequestHeader()
		err126 := argvalue0.Read(jsProt125)
		if err126 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err127 := (strconv.Atoi(flag.Arg(2)))
		if err127 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		tmp2, err128 := (strconv.Atoi(flag.Arg(3)))
		if err128 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := domino_plan.IdInt(argvalue2)
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListPlan(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listPlanByMaintainer":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListPlanByMaintainer requires 5 args")
			flag.Usage()
		}
		arg130 := flag.Arg(1)
		mbTrans131 := thrift.NewTMemoryBufferLen(len(arg130))
		defer mbTrans131.Close()
		_, err132 := mbTrans131.WriteString(arg130)
		if err132 != nil {
			Usage()
			return
		}
		factory133 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt134 := factory133.GetProtocol(mbTrans131)
		argvalue0 := domino_plan.NewRequestHeader()
		err135 := argvalue0.Read(jsProt134)
		if err135 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err137 := (strconv.Atoi(flag.Arg(3)))
		if err137 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := domino_plan.IdInt(argvalue2)
		tmp3, err138 := (strconv.Atoi(flag.Arg(4)))
		if err138 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := domino_plan.IdInt(argvalue3)
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListPlanByMaintainer(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "searchPlanByName":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "SearchPlanByName requires 6 args")
			flag.Usage()
		}
		arg140 := flag.Arg(1)
		mbTrans141 := thrift.NewTMemoryBufferLen(len(arg140))
		defer mbTrans141.Close()
		_, err142 := mbTrans141.WriteString(arg140)
		if err142 != nil {
			Usage()
			return
		}
		factory143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt144 := factory143.GetProtocol(mbTrans141)
		argvalue0 := domino_plan.NewRequestHeader()
		err145 := argvalue0.Read(jsProt144)
		if err145 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		tmp3, err148 := (strconv.Atoi(flag.Arg(4)))
		if err148 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := domino_plan.IdInt(argvalue3)
		tmp4, err149 := (strconv.Atoi(flag.Arg(5)))
		if err149 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := domino_plan.IdInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.SearchPlanByName(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getPlanByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlanByIds requires 2 args")
			flag.Usage()
		}
		arg151 := flag.Arg(1)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		argvalue0 := domino_plan.NewRequestHeader()
		err156 := argvalue0.Read(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		arg157 := flag.Arg(2)
		mbTrans158 := thrift.NewTMemoryBufferLen(len(arg157))
		defer mbTrans158.Close()
		_, err159 := mbTrans158.WriteString(arg157)
		if err159 != nil {
			Usage()
			return
		}
		factory160 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt161 := factory160.GetProtocol(mbTrans158)
		containerStruct1 := domino_plan.NewGetPlanByIdsArgs()
		err162 := containerStruct1.ReadField2(jsProt161)
		if err162 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.PlanIds
		value1 := argvalue1
		fmt.Print(client.GetPlanByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getPlanDeps":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlanDeps requires 2 args")
			flag.Usage()
		}
		arg163 := flag.Arg(1)
		mbTrans164 := thrift.NewTMemoryBufferLen(len(arg163))
		defer mbTrans164.Close()
		_, err165 := mbTrans164.WriteString(arg163)
		if err165 != nil {
			Usage()
			return
		}
		factory166 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt167 := factory166.GetProtocol(mbTrans164)
		argvalue0 := domino_plan.NewRequestHeader()
		err168 := argvalue0.Read(jsProt167)
		if err168 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err169 := (strconv.Atoi(flag.Arg(2)))
		if err169 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.GetPlanDeps(value0, value1))
		fmt.Print("\n")
		break
	case "getPlanDepIdsAll":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlanDepIdsAll requires 2 args")
			flag.Usage()
		}
		arg170 := flag.Arg(1)
		mbTrans171 := thrift.NewTMemoryBufferLen(len(arg170))
		defer mbTrans171.Close()
		_, err172 := mbTrans171.WriteString(arg170)
		if err172 != nil {
			Usage()
			return
		}
		factory173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt174 := factory173.GetProtocol(mbTrans171)
		argvalue0 := domino_plan.NewRequestHeader()
		err175 := argvalue0.Read(jsProt174)
		if err175 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err176 := (strconv.Atoi(flag.Arg(2)))
		if err176 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.GetPlanDepIdsAll(value0, value1))
		fmt.Print("\n")
		break
	case "getDepPlans":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDepPlans requires 2 args")
			flag.Usage()
		}
		arg177 := flag.Arg(1)
		mbTrans178 := thrift.NewTMemoryBufferLen(len(arg177))
		defer mbTrans178.Close()
		_, err179 := mbTrans178.WriteString(arg177)
		if err179 != nil {
			Usage()
			return
		}
		factory180 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt181 := factory180.GetProtocol(mbTrans178)
		argvalue0 := domino_plan.NewRequestHeader()
		err182 := argvalue0.Read(jsProt181)
		if err182 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err183 := (strconv.Atoi(flag.Arg(2)))
		if err183 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.GetDepPlans(value0, value1))
		fmt.Print("\n")
		break
	case "savePlanDeps":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SavePlanDeps requires 3 args")
			flag.Usage()
		}
		arg184 := flag.Arg(1)
		mbTrans185 := thrift.NewTMemoryBufferLen(len(arg184))
		defer mbTrans185.Close()
		_, err186 := mbTrans185.WriteString(arg184)
		if err186 != nil {
			Usage()
			return
		}
		factory187 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt188 := factory187.GetProtocol(mbTrans185)
		argvalue0 := domino_plan.NewRequestHeader()
		err189 := argvalue0.Read(jsProt188)
		if err189 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err190 := (strconv.Atoi(flag.Arg(2)))
		if err190 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		arg191 := flag.Arg(3)
		mbTrans192 := thrift.NewTMemoryBufferLen(len(arg191))
		defer mbTrans192.Close()
		_, err193 := mbTrans192.WriteString(arg191)
		if err193 != nil {
			Usage()
			return
		}
		factory194 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt195 := factory194.GetProtocol(mbTrans192)
		containerStruct2 := domino_plan.NewSavePlanDepsArgs()
		err196 := containerStruct2.ReadField3(jsProt195)
		if err196 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Deps
		value2 := argvalue2
		fmt.Print(client.SavePlanDeps(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deletePlanDeps":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeletePlanDeps requires 3 args")
			flag.Usage()
		}
		arg197 := flag.Arg(1)
		mbTrans198 := thrift.NewTMemoryBufferLen(len(arg197))
		defer mbTrans198.Close()
		_, err199 := mbTrans198.WriteString(arg197)
		if err199 != nil {
			Usage()
			return
		}
		factory200 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt201 := factory200.GetProtocol(mbTrans198)
		argvalue0 := domino_plan.NewRequestHeader()
		err202 := argvalue0.Read(jsProt201)
		if err202 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err203 := (strconv.Atoi(flag.Arg(2)))
		if err203 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		arg204 := flag.Arg(3)
		mbTrans205 := thrift.NewTMemoryBufferLen(len(arg204))
		defer mbTrans205.Close()
		_, err206 := mbTrans205.WriteString(arg204)
		if err206 != nil {
			Usage()
			return
		}
		factory207 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt208 := factory207.GetProtocol(mbTrans205)
		containerStruct2 := domino_plan.NewDeletePlanDepsArgs()
		err209 := containerStruct2.ReadField3(jsProt208)
		if err209 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.DepPlanIds
		value2 := argvalue2
		fmt.Print(client.DeletePlanDeps(value0, value1, value2))
		fmt.Print("\n")
		break
	case "suspendPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SuspendPlan requires 2 args")
			flag.Usage()
		}
		arg210 := flag.Arg(1)
		mbTrans211 := thrift.NewTMemoryBufferLen(len(arg210))
		defer mbTrans211.Close()
		_, err212 := mbTrans211.WriteString(arg210)
		if err212 != nil {
			Usage()
			return
		}
		factory213 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt214 := factory213.GetProtocol(mbTrans211)
		argvalue0 := domino_plan.NewRequestHeader()
		err215 := argvalue0.Read(jsProt214)
		if err215 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err216 := (strconv.Atoi(flag.Arg(2)))
		if err216 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.SuspendPlan(value0, value1))
		fmt.Print("\n")
		break
	case "stopPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "StopPlan requires 2 args")
			flag.Usage()
		}
		arg217 := flag.Arg(1)
		mbTrans218 := thrift.NewTMemoryBufferLen(len(arg217))
		defer mbTrans218.Close()
		_, err219 := mbTrans218.WriteString(arg217)
		if err219 != nil {
			Usage()
			return
		}
		factory220 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt221 := factory220.GetProtocol(mbTrans218)
		argvalue0 := domino_plan.NewRequestHeader()
		err222 := argvalue0.Read(jsProt221)
		if err222 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err223 := (strconv.Atoi(flag.Arg(2)))
		if err223 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.StopPlan(value0, value1))
		fmt.Print("\n")
		break
	case "restartPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RestartPlan requires 2 args")
			flag.Usage()
		}
		arg224 := flag.Arg(1)
		mbTrans225 := thrift.NewTMemoryBufferLen(len(arg224))
		defer mbTrans225.Close()
		_, err226 := mbTrans225.WriteString(arg224)
		if err226 != nil {
			Usage()
			return
		}
		factory227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt228 := factory227.GetProtocol(mbTrans225)
		argvalue0 := domino_plan.NewRequestHeader()
		err229 := argvalue0.Read(jsProt228)
		if err229 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err230 := (strconv.Atoi(flag.Arg(2)))
		if err230 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.RestartPlan(value0, value1))
		fmt.Print("\n")
		break
	case "deletePlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeletePlan requires 2 args")
			flag.Usage()
		}
		arg231 := flag.Arg(1)
		mbTrans232 := thrift.NewTMemoryBufferLen(len(arg231))
		defer mbTrans232.Close()
		_, err233 := mbTrans232.WriteString(arg231)
		if err233 != nil {
			Usage()
			return
		}
		factory234 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt235 := factory234.GetProtocol(mbTrans232)
		argvalue0 := domino_plan.NewRequestHeader()
		err236 := argvalue0.Read(jsProt235)
		if err236 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err237 := (strconv.Atoi(flag.Arg(2)))
		if err237 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.DeletePlan(value0, value1))
		fmt.Print("\n")
		break
	case "viewPlanByHost":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "ViewPlanByHost requires 1 args")
			flag.Usage()
		}
		arg238 := flag.Arg(1)
		mbTrans239 := thrift.NewTMemoryBufferLen(len(arg238))
		defer mbTrans239.Close()
		_, err240 := mbTrans239.WriteString(arg238)
		if err240 != nil {
			Usage()
			return
		}
		factory241 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt242 := factory241.GetProtocol(mbTrans239)
		argvalue0 := domino_plan.NewRequestHeader()
		err243 := argvalue0.Read(jsProt242)
		if err243 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		fmt.Print(client.ViewPlanByHost(value0))
		fmt.Print("\n")
		break
	case "getMaintainers":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetMaintainers requires 1 args")
			flag.Usage()
		}
		arg244 := flag.Arg(1)
		mbTrans245 := thrift.NewTMemoryBufferLen(len(arg244))
		defer mbTrans245.Close()
		_, err246 := mbTrans245.WriteString(arg244)
		if err246 != nil {
			Usage()
			return
		}
		factory247 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt248 := factory247.GetProtocol(mbTrans245)
		argvalue0 := domino_plan.NewRequestHeader()
		err249 := argvalue0.Read(jsProt248)
		if err249 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		fmt.Print(client.GetMaintainers(value0))
		fmt.Print("\n")
		break
	case "getUsersByPlanId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUsersByPlanId requires 2 args")
			flag.Usage()
		}
		arg250 := flag.Arg(1)
		mbTrans251 := thrift.NewTMemoryBufferLen(len(arg250))
		defer mbTrans251.Close()
		_, err252 := mbTrans251.WriteString(arg250)
		if err252 != nil {
			Usage()
			return
		}
		factory253 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt254 := factory253.GetProtocol(mbTrans251)
		argvalue0 := domino_plan.NewRequestHeader()
		err255 := argvalue0.Read(jsProt254)
		if err255 != nil {
			Usage()
			return
		}
		value0 := domino_plan.RequestHeader(argvalue0)
		tmp1, err256 := (strconv.Atoi(flag.Arg(2)))
		if err256 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_plan.IdInt(argvalue1)
		fmt.Print(client.GetUsersByPlanId(value0, value1))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err261 := (strconv.Atoi(flag.Arg(1)))
		if err261 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
