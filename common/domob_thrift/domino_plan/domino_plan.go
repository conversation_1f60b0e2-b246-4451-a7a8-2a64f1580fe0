// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package domino_plan

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/domino_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = domino_types.GoUnusedProtection__

type DominoPlan interface {
	dm303.DomobService
	//任务静态定义信息

	// 创建新的任务
	//
	// Parameters:
	//  - Header
	//  - APlan
	AddPlan(header *common.RequestHeader, aPlan *domino_types.Plan) (r IdInt, de *domino_types.DominoException, err error)
	//    * 编辑任务
	// *
	// * 并不是所有的字段都是可编辑的，如creator等。
	// * aPlan中的对应字段将会被忽略
	//
	// Parameters:
	//  - Header
	//  - APlan
	EditPlan(header *common.RequestHeader, aPlan *domino_types.Plan) (de *domino_types.DominoException, err error)
	// 根据名称获取任务信息
	//
	// Parameters:
	//  - Header
	//  - Name
	GetPlanByName(header *common.RequestHeader, name string) (r *domino_types.Plan, de *domino_types.DominoException, err error)
	// 批量获取任务
	//
	// 任务按照创建的先后时间排序
	//
	// Parameters:
	//  - Header
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListPlan(header *common.RequestHeader, offset IdInt, limit IdInt, ascending bool) (r *common.QueryResult, err error)
	// 批量获取某个用户维护的任务
	//
	// 任务按照创建的先后时间排序
	//
	// Parameters:
	//  - Header
	//  - Maintainer: 维护者的email
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	ListPlanByMaintainer(header *common.RequestHeader, maintainer string, offset IdInt, limit IdInt, ascending bool) (r *common.QueryResult, err error)
	//    * 根据名称和维护者查询任务，按照名称排序
	//    *
	//    * 如果token为空，则返回全部的任务，按照名称排序
	// * maintainer为null或者''，则代表不过滤
	//
	// Parameters:
	//  - Header
	//  - Token: 名称需要包含如下字符串
	//  - Maintainer: 维护者的email
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	SearchPlanByName(header *common.RequestHeader, token string, maintainer string, offset IdInt, limit IdInt, ascending bool) (r *common.QueryResult, err error)
	// 根据id获取任务信息
	//
	// Parameters:
	//  - Header
	//  - PlanIds
	GetPlanByIds(header *common.RequestHeader, planIds []IdInt) (r map[IdInt]*domino_types.Plan, de *domino_types.DominoException, err error)
	// 获取任务的全部直接依赖的列表
	//
	// 通常任务的依赖不会太多，这里不支持分页
	//
	// Parameters:
	//  - Header
	//  - PlanId
	GetPlanDeps(header *common.RequestHeader, planId IdInt) (r []*domino_types.PlanDep, de *domino_types.DominoException, err error)
	//    * 获取任务的所有层次的依赖的id列表
	// * 返回的结果中包含自己的id
	//
	// Parameters:
	//  - Header
	//  - PlanId
	GetPlanDepIdsAll(header *common.RequestHeader, planId IdInt) (r []IdInt, de *domino_types.DominoException, err error)
	// 获取直接依赖于本任务的任务列表
	//
	// 因依赖的一定是当前Plan，返回PlanDep结构中的depPlan属性为null
	//
	// Parameters:
	//  - Header
	//  - PlanId
	GetDepPlans(header *common.RequestHeader, planId IdInt) (r []*domino_types.PlanDep, de *domino_types.DominoException, err error)
	// 保存任务的依赖列表
	//
	// deps中的planId会被忽略
	// 如果数据库已经有对应记录，则更新字段，否则新增
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - Deps
	SavePlanDeps(header *common.RequestHeader, planId common.IdInt, deps []*domino_types.PlanDep) (de *domino_types.DominoException, err error)
	// 删除任务的依赖
	//
	// 如果depPlanIds是空数组，则删除全部依赖
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - DepPlanIds
	DeletePlanDeps(header *common.RequestHeader, planId common.IdInt, depPlanIds []common.IdInt) (de *domino_types.DominoException, err error)
	//    * 暂停任务
	// *
	//    * 只是将本任务状态改为暂停
	//    * 但是所有job会被创建出来
	//    * 在检查依赖时不成功，所以一直再等待重新启动运行
	//    * 依赖的任务也会因此阻塞
	//
	// Parameters:
	//  - Header
	//  - PlanId
	SuspendPlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error)
	// 停止任务
	//
	// 如果有依赖与本任务的任务，将不会停止，除非依赖的任务已经停止
	// 如果没有依赖于本任务的任务，将会停止此任务
	//
	// Parameters:
	//  - Header
	//  - PlanId
	StopPlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error)
	//    * 启动任务
	// *
	//    * 首先检查有没有依赖的不是正常运行或者暂停的任务
	//    * 如果有，将不能启动
	//    * 将会启动该任务
	//
	// Parameters:
	//  - Header
	//  - PlanId
	RestartPlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error)
	//    * 删除任务
	//    *
	//    * 如果有依赖与本任务的任务，将不会删除
	//    * 如果没有依赖于本任务的任务，将任务状态改为删除
	// *
	// * 在实现是需要特别考虑，如果有job正在运行，该如何处理？
	// * 通常在删除一个plan之前，应该先停止，待运行中任务结束后再删除
	//
	// Parameters:
	//  - Header
	//  - PlanId
	DeletePlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error)
	// 依据host分组
	// 返回host:planList
	// 这里每一次查询是返回所有任务
	// 浪费网络，需要修改
	// 修改后，先返回host
	// 后续依据host查询plan，需要两个方法
	//
	// Parameters:
	//  - Header
	ViewPlanByHost(header *common.RequestHeader) (r map[string][]*domino_types.Plan, de *domino_types.DominoException, err error)
	//    * 获取系统中的所有维护者的email
	// * 如果有的plan无人维护，则返回列表中包含空串
	//
	// Parameters:
	//  - Header
	GetMaintainers(header *common.RequestHeader) (r []string, de *domino_types.DominoException, err error)
	// 获取plan users通过planid
	//
	//
	//
	// Parameters:
	//  - Header
	//  - PlanId
	GetUsersByPlanId(header *common.RequestHeader, planId common.IdInt) (r []string, de *domino_types.DominoException, err error)
}

//任务静态定义信息
type DominoPlanClient struct {
	*dm303.DomobServiceClient
}

func NewDominoPlanClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DominoPlanClient {
	return &DominoPlanClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDominoPlanClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DominoPlanClient {
	return &DominoPlanClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 创建新的任务
//
// Parameters:
//  - Header
//  - APlan
func (p *DominoPlanClient) AddPlan(header *common.RequestHeader, aPlan *domino_types.Plan) (r IdInt, de *domino_types.DominoException, err error) {
	if err = p.sendAddPlan(header, aPlan); err != nil {
		return
	}
	return p.recvAddPlan()
}

func (p *DominoPlanClient) sendAddPlan(header *common.RequestHeader, aPlan *domino_types.Plan) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddPlanArgs()
	args0.Header = header
	args0.APlan = aPlan
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvAddPlan() (value IdInt, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddPlanResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.De != nil {
		de = result1.De
	}
	return
}

//    * 编辑任务
// *
// * 并不是所有的字段都是可编辑的，如creator等。
// * aPlan中的对应字段将会被忽略
//
// Parameters:
//  - Header
//  - APlan
func (p *DominoPlanClient) EditPlan(header *common.RequestHeader, aPlan *domino_types.Plan) (de *domino_types.DominoException, err error) {
	if err = p.sendEditPlan(header, aPlan); err != nil {
		return
	}
	return p.recvEditPlan()
}

func (p *DominoPlanClient) sendEditPlan(header *common.RequestHeader, aPlan *domino_types.Plan) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewEditPlanArgs()
	args4.Header = header
	args4.APlan = aPlan
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvEditPlan() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewEditPlanResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result5.De != nil {
		de = result5.De
	}
	return
}

// 根据名称获取任务信息
//
// Parameters:
//  - Header
//  - Name
func (p *DominoPlanClient) GetPlanByName(header *common.RequestHeader, name string) (r *domino_types.Plan, de *domino_types.DominoException, err error) {
	if err = p.sendGetPlanByName(header, name); err != nil {
		return
	}
	return p.recvGetPlanByName()
}

func (p *DominoPlanClient) sendGetPlanByName(header *common.RequestHeader, name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlanByName", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetPlanByNameArgs()
	args8.Header = header
	args8.Name = name
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetPlanByName() (value *domino_types.Plan, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetPlanByNameResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.De != nil {
		de = result9.De
	}
	return
}

// 批量获取任务
//
// 任务按照创建的先后时间排序
//
// Parameters:
//  - Header
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *DominoPlanClient) ListPlan(header *common.RequestHeader, offset IdInt, limit IdInt, ascending bool) (r *common.QueryResult, err error) {
	if err = p.sendListPlan(header, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListPlan()
}

func (p *DominoPlanClient) sendListPlan(header *common.RequestHeader, offset IdInt, limit IdInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewListPlanArgs()
	args12.Header = header
	args12.Offset = offset
	args12.Limit = limit
	args12.Ascending = ascending
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvListPlan() (value *common.QueryResult, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewListPlanResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 批量获取某个用户维护的任务
//
// 任务按照创建的先后时间排序
//
// Parameters:
//  - Header
//  - Maintainer: 维护者的email
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *DominoPlanClient) ListPlanByMaintainer(header *common.RequestHeader, maintainer string, offset IdInt, limit IdInt, ascending bool) (r *common.QueryResult, err error) {
	if err = p.sendListPlanByMaintainer(header, maintainer, offset, limit, ascending); err != nil {
		return
	}
	return p.recvListPlanByMaintainer()
}

func (p *DominoPlanClient) sendListPlanByMaintainer(header *common.RequestHeader, maintainer string, offset IdInt, limit IdInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listPlanByMaintainer", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewListPlanByMaintainerArgs()
	args16.Header = header
	args16.Maintainer = maintainer
	args16.Offset = offset
	args16.Limit = limit
	args16.Ascending = ascending
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvListPlanByMaintainer() (value *common.QueryResult, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewListPlanByMaintainerResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

//    * 根据名称和维护者查询任务，按照名称排序
//    *
//    * 如果token为空，则返回全部的任务，按照名称排序
// * maintainer为null或者''，则代表不过滤
//
// Parameters:
//  - Header
//  - Token: 名称需要包含如下字符串
//  - Maintainer: 维护者的email
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *DominoPlanClient) SearchPlanByName(header *common.RequestHeader, token string, maintainer string, offset IdInt, limit IdInt, ascending bool) (r *common.QueryResult, err error) {
	if err = p.sendSearchPlanByName(header, token, maintainer, offset, limit, ascending); err != nil {
		return
	}
	return p.recvSearchPlanByName()
}

func (p *DominoPlanClient) sendSearchPlanByName(header *common.RequestHeader, token string, maintainer string, offset IdInt, limit IdInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchPlanByName", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewSearchPlanByNameArgs()
	args20.Header = header
	args20.Token = token
	args20.Maintainer = maintainer
	args20.Offset = offset
	args20.Limit = limit
	args20.Ascending = ascending
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvSearchPlanByName() (value *common.QueryResult, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewSearchPlanByNameResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 根据id获取任务信息
//
// Parameters:
//  - Header
//  - PlanIds
func (p *DominoPlanClient) GetPlanByIds(header *common.RequestHeader, planIds []IdInt) (r map[IdInt]*domino_types.Plan, de *domino_types.DominoException, err error) {
	if err = p.sendGetPlanByIds(header, planIds); err != nil {
		return
	}
	return p.recvGetPlanByIds()
}

func (p *DominoPlanClient) sendGetPlanByIds(header *common.RequestHeader, planIds []IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlanByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetPlanByIdsArgs()
	args24.Header = header
	args24.PlanIds = planIds
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetPlanByIds() (value map[IdInt]*domino_types.Plan, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetPlanByIdsResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.De != nil {
		de = result25.De
	}
	return
}

// 获取任务的全部直接依赖的列表
//
// 通常任务的依赖不会太多，这里不支持分页
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) GetPlanDeps(header *common.RequestHeader, planId IdInt) (r []*domino_types.PlanDep, de *domino_types.DominoException, err error) {
	if err = p.sendGetPlanDeps(header, planId); err != nil {
		return
	}
	return p.recvGetPlanDeps()
}

func (p *DominoPlanClient) sendGetPlanDeps(header *common.RequestHeader, planId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlanDeps", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetPlanDepsArgs()
	args28.Header = header
	args28.PlanId = planId
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetPlanDeps() (value []*domino_types.PlanDep, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetPlanDepsResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.De != nil {
		de = result29.De
	}
	return
}

//    * 获取任务的所有层次的依赖的id列表
// * 返回的结果中包含自己的id
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) GetPlanDepIdsAll(header *common.RequestHeader, planId IdInt) (r []IdInt, de *domino_types.DominoException, err error) {
	if err = p.sendGetPlanDepIdsAll(header, planId); err != nil {
		return
	}
	return p.recvGetPlanDepIdsAll()
}

func (p *DominoPlanClient) sendGetPlanDepIdsAll(header *common.RequestHeader, planId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlanDepIdsAll", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetPlanDepIdsAllArgs()
	args32.Header = header
	args32.PlanId = planId
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetPlanDepIdsAll() (value []IdInt, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetPlanDepIdsAllResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.De != nil {
		de = result33.De
	}
	return
}

// 获取直接依赖于本任务的任务列表
//
// 因依赖的一定是当前Plan，返回PlanDep结构中的depPlan属性为null
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) GetDepPlans(header *common.RequestHeader, planId IdInt) (r []*domino_types.PlanDep, de *domino_types.DominoException, err error) {
	if err = p.sendGetDepPlans(header, planId); err != nil {
		return
	}
	return p.recvGetDepPlans()
}

func (p *DominoPlanClient) sendGetDepPlans(header *common.RequestHeader, planId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getDepPlans", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetDepPlansArgs()
	args36.Header = header
	args36.PlanId = planId
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetDepPlans() (value []*domino_types.PlanDep, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetDepPlansResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.De != nil {
		de = result37.De
	}
	return
}

// 保存任务的依赖列表
//
// deps中的planId会被忽略
// 如果数据库已经有对应记录，则更新字段，否则新增
//
// Parameters:
//  - Header
//  - PlanId
//  - Deps
func (p *DominoPlanClient) SavePlanDeps(header *common.RequestHeader, planId common.IdInt, deps []*domino_types.PlanDep) (de *domino_types.DominoException, err error) {
	if err = p.sendSavePlanDeps(header, planId, deps); err != nil {
		return
	}
	return p.recvSavePlanDeps()
}

func (p *DominoPlanClient) sendSavePlanDeps(header *common.RequestHeader, planId common.IdInt, deps []*domino_types.PlanDep) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("savePlanDeps", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewSavePlanDepsArgs()
	args40.Header = header
	args40.PlanId = planId
	args40.Deps = deps
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvSavePlanDeps() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewSavePlanDepsResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result41.De != nil {
		de = result41.De
	}
	return
}

// 删除任务的依赖
//
// 如果depPlanIds是空数组，则删除全部依赖
//
// Parameters:
//  - Header
//  - PlanId
//  - DepPlanIds
func (p *DominoPlanClient) DeletePlanDeps(header *common.RequestHeader, planId common.IdInt, depPlanIds []common.IdInt) (de *domino_types.DominoException, err error) {
	if err = p.sendDeletePlanDeps(header, planId, depPlanIds); err != nil {
		return
	}
	return p.recvDeletePlanDeps()
}

func (p *DominoPlanClient) sendDeletePlanDeps(header *common.RequestHeader, planId common.IdInt, depPlanIds []common.IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deletePlanDeps", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewDeletePlanDepsArgs()
	args44.Header = header
	args44.PlanId = planId
	args44.DepPlanIds = depPlanIds
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvDeletePlanDeps() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewDeletePlanDepsResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result45.De != nil {
		de = result45.De
	}
	return
}

//    * 暂停任务
// *
//    * 只是将本任务状态改为暂停
//    * 但是所有job会被创建出来
//    * 在检查依赖时不成功，所以一直再等待重新启动运行
//    * 依赖的任务也会因此阻塞
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) SuspendPlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error) {
	if err = p.sendSuspendPlan(header, planId); err != nil {
		return
	}
	return p.recvSuspendPlan()
}

func (p *DominoPlanClient) sendSuspendPlan(header *common.RequestHeader, planId common.IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("suspendPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewSuspendPlanArgs()
	args48.Header = header
	args48.PlanId = planId
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvSuspendPlan() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewSuspendPlanResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result49.De != nil {
		de = result49.De
	}
	return
}

// 停止任务
//
// 如果有依赖与本任务的任务，将不会停止，除非依赖的任务已经停止
// 如果没有依赖于本任务的任务，将会停止此任务
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) StopPlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error) {
	if err = p.sendStopPlan(header, planId); err != nil {
		return
	}
	return p.recvStopPlan()
}

func (p *DominoPlanClient) sendStopPlan(header *common.RequestHeader, planId common.IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("stopPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewStopPlanArgs()
	args52.Header = header
	args52.PlanId = planId
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvStopPlan() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewStopPlanResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result53.De != nil {
		de = result53.De
	}
	return
}

//    * 启动任务
// *
//    * 首先检查有没有依赖的不是正常运行或者暂停的任务
//    * 如果有，将不能启动
//    * 将会启动该任务
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) RestartPlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error) {
	if err = p.sendRestartPlan(header, planId); err != nil {
		return
	}
	return p.recvRestartPlan()
}

func (p *DominoPlanClient) sendRestartPlan(header *common.RequestHeader, planId common.IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("restartPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewRestartPlanArgs()
	args56.Header = header
	args56.PlanId = planId
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvRestartPlan() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewRestartPlanResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result57.De != nil {
		de = result57.De
	}
	return
}

//    * 删除任务
//    *
//    * 如果有依赖与本任务的任务，将不会删除
//    * 如果没有依赖于本任务的任务，将任务状态改为删除
// *
// * 在实现是需要特别考虑，如果有job正在运行，该如何处理？
// * 通常在删除一个plan之前，应该先停止，待运行中任务结束后再删除
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) DeletePlan(header *common.RequestHeader, planId common.IdInt) (de *domino_types.DominoException, err error) {
	if err = p.sendDeletePlan(header, planId); err != nil {
		return
	}
	return p.recvDeletePlan()
}

func (p *DominoPlanClient) sendDeletePlan(header *common.RequestHeader, planId common.IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deletePlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewDeletePlanArgs()
	args60.Header = header
	args60.PlanId = planId
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvDeletePlan() (de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewDeletePlanResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result61.De != nil {
		de = result61.De
	}
	return
}

// 依据host分组
// 返回host:planList
// 这里每一次查询是返回所有任务
// 浪费网络，需要修改
// 修改后，先返回host
// 后续依据host查询plan，需要两个方法
//
// Parameters:
//  - Header
func (p *DominoPlanClient) ViewPlanByHost(header *common.RequestHeader) (r map[string][]*domino_types.Plan, de *domino_types.DominoException, err error) {
	if err = p.sendViewPlanByHost(header); err != nil {
		return
	}
	return p.recvViewPlanByHost()
}

func (p *DominoPlanClient) sendViewPlanByHost(header *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("viewPlanByHost", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewViewPlanByHostArgs()
	args64.Header = header
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvViewPlanByHost() (value map[string][]*domino_types.Plan, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewViewPlanByHostResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.De != nil {
		de = result65.De
	}
	return
}

//    * 获取系统中的所有维护者的email
// * 如果有的plan无人维护，则返回列表中包含空串
//
// Parameters:
//  - Header
func (p *DominoPlanClient) GetMaintainers(header *common.RequestHeader) (r []string, de *domino_types.DominoException, err error) {
	if err = p.sendGetMaintainers(header); err != nil {
		return
	}
	return p.recvGetMaintainers()
}

func (p *DominoPlanClient) sendGetMaintainers(header *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMaintainers", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewGetMaintainersArgs()
	args68.Header = header
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetMaintainers() (value []string, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewGetMaintainersResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result69.Success
	if result69.De != nil {
		de = result69.De
	}
	return
}

// 获取plan users通过planid
//
//
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoPlanClient) GetUsersByPlanId(header *common.RequestHeader, planId common.IdInt) (r []string, de *domino_types.DominoException, err error) {
	if err = p.sendGetUsersByPlanId(header, planId); err != nil {
		return
	}
	return p.recvGetUsersByPlanId()
}

func (p *DominoPlanClient) sendGetUsersByPlanId(header *common.RequestHeader, planId common.IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getUsersByPlanId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args72 := NewGetUsersByPlanIdArgs()
	args72.Header = header
	args72.PlanId = planId
	if err = args72.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoPlanClient) recvGetUsersByPlanId() (value []string, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error74 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error75 error
		error75, err = error74.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error75
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result73 := NewGetUsersByPlanIdResult()
	if err = result73.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result73.Success
	if result73.De != nil {
		de = result73.De
	}
	return
}

type DominoPlanProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDominoPlanProcessor(handler DominoPlan) *DominoPlanProcessor {
	self76 := &DominoPlanProcessor{dm303.NewDomobServiceProcessor(handler)}
	self76.AddToProcessorMap("addPlan", &dominoPlanProcessorAddPlan{handler: handler})
	self76.AddToProcessorMap("editPlan", &dominoPlanProcessorEditPlan{handler: handler})
	self76.AddToProcessorMap("getPlanByName", &dominoPlanProcessorGetPlanByName{handler: handler})
	self76.AddToProcessorMap("listPlan", &dominoPlanProcessorListPlan{handler: handler})
	self76.AddToProcessorMap("listPlanByMaintainer", &dominoPlanProcessorListPlanByMaintainer{handler: handler})
	self76.AddToProcessorMap("searchPlanByName", &dominoPlanProcessorSearchPlanByName{handler: handler})
	self76.AddToProcessorMap("getPlanByIds", &dominoPlanProcessorGetPlanByIds{handler: handler})
	self76.AddToProcessorMap("getPlanDeps", &dominoPlanProcessorGetPlanDeps{handler: handler})
	self76.AddToProcessorMap("getPlanDepIdsAll", &dominoPlanProcessorGetPlanDepIdsAll{handler: handler})
	self76.AddToProcessorMap("getDepPlans", &dominoPlanProcessorGetDepPlans{handler: handler})
	self76.AddToProcessorMap("savePlanDeps", &dominoPlanProcessorSavePlanDeps{handler: handler})
	self76.AddToProcessorMap("deletePlanDeps", &dominoPlanProcessorDeletePlanDeps{handler: handler})
	self76.AddToProcessorMap("suspendPlan", &dominoPlanProcessorSuspendPlan{handler: handler})
	self76.AddToProcessorMap("stopPlan", &dominoPlanProcessorStopPlan{handler: handler})
	self76.AddToProcessorMap("restartPlan", &dominoPlanProcessorRestartPlan{handler: handler})
	self76.AddToProcessorMap("deletePlan", &dominoPlanProcessorDeletePlan{handler: handler})
	self76.AddToProcessorMap("viewPlanByHost", &dominoPlanProcessorViewPlanByHost{handler: handler})
	self76.AddToProcessorMap("getMaintainers", &dominoPlanProcessorGetMaintainers{handler: handler})
	self76.AddToProcessorMap("getUsersByPlanId", &dominoPlanProcessorGetUsersByPlanId{handler: handler})
	return self76
}

type dominoPlanProcessorAddPlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorAddPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddPlanResult()
	if result.Success, result.De, err = p.handler.AddPlan(args.Header, args.APlan); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addPlan: "+err.Error())
		oprot.WriteMessageBegin("addPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorEditPlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorEditPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditPlanResult()
	if result.De, err = p.handler.EditPlan(args.Header, args.APlan); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editPlan: "+err.Error())
		oprot.WriteMessageBegin("editPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetPlanByName struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetPlanByName) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlanByNameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlanByName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlanByNameResult()
	if result.Success, result.De, err = p.handler.GetPlanByName(args.Header, args.Name); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlanByName: "+err.Error())
		oprot.WriteMessageBegin("getPlanByName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlanByName", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorListPlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorListPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListPlanResult()
	if result.Success, err = p.handler.ListPlan(args.Header, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listPlan: "+err.Error())
		oprot.WriteMessageBegin("listPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorListPlanByMaintainer struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorListPlanByMaintainer) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListPlanByMaintainerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listPlanByMaintainer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListPlanByMaintainerResult()
	if result.Success, err = p.handler.ListPlanByMaintainer(args.Header, args.Maintainer, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listPlanByMaintainer: "+err.Error())
		oprot.WriteMessageBegin("listPlanByMaintainer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listPlanByMaintainer", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorSearchPlanByName struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorSearchPlanByName) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchPlanByNameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchPlanByName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchPlanByNameResult()
	if result.Success, err = p.handler.SearchPlanByName(args.Header, args.Token, args.Maintainer, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchPlanByName: "+err.Error())
		oprot.WriteMessageBegin("searchPlanByName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchPlanByName", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetPlanByIds struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetPlanByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlanByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlanByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlanByIdsResult()
	if result.Success, result.De, err = p.handler.GetPlanByIds(args.Header, args.PlanIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlanByIds: "+err.Error())
		oprot.WriteMessageBegin("getPlanByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlanByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetPlanDeps struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetPlanDeps) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlanDepsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlanDeps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlanDepsResult()
	if result.Success, result.De, err = p.handler.GetPlanDeps(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlanDeps: "+err.Error())
		oprot.WriteMessageBegin("getPlanDeps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlanDeps", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetPlanDepIdsAll struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetPlanDepIdsAll) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlanDepIdsAllArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlanDepIdsAll", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlanDepIdsAllResult()
	if result.Success, result.De, err = p.handler.GetPlanDepIdsAll(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlanDepIdsAll: "+err.Error())
		oprot.WriteMessageBegin("getPlanDepIdsAll", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlanDepIdsAll", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetDepPlans struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetDepPlans) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDepPlansArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getDepPlans", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDepPlansResult()
	if result.Success, result.De, err = p.handler.GetDepPlans(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDepPlans: "+err.Error())
		oprot.WriteMessageBegin("getDepPlans", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getDepPlans", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorSavePlanDeps struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorSavePlanDeps) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSavePlanDepsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("savePlanDeps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSavePlanDepsResult()
	if result.De, err = p.handler.SavePlanDeps(args.Header, args.PlanId, args.Deps); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing savePlanDeps: "+err.Error())
		oprot.WriteMessageBegin("savePlanDeps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("savePlanDeps", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorDeletePlanDeps struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorDeletePlanDeps) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeletePlanDepsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deletePlanDeps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeletePlanDepsResult()
	if result.De, err = p.handler.DeletePlanDeps(args.Header, args.PlanId, args.DepPlanIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deletePlanDeps: "+err.Error())
		oprot.WriteMessageBegin("deletePlanDeps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deletePlanDeps", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorSuspendPlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorSuspendPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSuspendPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("suspendPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSuspendPlanResult()
	if result.De, err = p.handler.SuspendPlan(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing suspendPlan: "+err.Error())
		oprot.WriteMessageBegin("suspendPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("suspendPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorStopPlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorStopPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewStopPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("stopPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewStopPlanResult()
	if result.De, err = p.handler.StopPlan(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing stopPlan: "+err.Error())
		oprot.WriteMessageBegin("stopPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("stopPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorRestartPlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorRestartPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRestartPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("restartPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRestartPlanResult()
	if result.De, err = p.handler.RestartPlan(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing restartPlan: "+err.Error())
		oprot.WriteMessageBegin("restartPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("restartPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorDeletePlan struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorDeletePlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeletePlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deletePlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeletePlanResult()
	if result.De, err = p.handler.DeletePlan(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deletePlan: "+err.Error())
		oprot.WriteMessageBegin("deletePlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deletePlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorViewPlanByHost struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorViewPlanByHost) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewViewPlanByHostArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("viewPlanByHost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewViewPlanByHostResult()
	if result.Success, result.De, err = p.handler.ViewPlanByHost(args.Header); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing viewPlanByHost: "+err.Error())
		oprot.WriteMessageBegin("viewPlanByHost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("viewPlanByHost", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetMaintainers struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetMaintainers) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMaintainersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMaintainers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMaintainersResult()
	if result.Success, result.De, err = p.handler.GetMaintainers(args.Header); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMaintainers: "+err.Error())
		oprot.WriteMessageBegin("getMaintainers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMaintainers", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoPlanProcessorGetUsersByPlanId struct {
	handler DominoPlan
}

func (p *dominoPlanProcessorGetUsersByPlanId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUsersByPlanIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getUsersByPlanId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUsersByPlanIdResult()
	if result.Success, result.De, err = p.handler.GetUsersByPlanId(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUsersByPlanId: "+err.Error())
		oprot.WriteMessageBegin("getUsersByPlanId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getUsersByPlanId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	APlan  *domino_types.Plan    `thrift:"aPlan,2" json:"aPlan"`
}

func NewAddPlanArgs() *AddPlanArgs {
	return &AddPlanArgs{}
}

func (p *AddPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddPlanArgs) readField2(iprot thrift.TProtocol) error {
	p.APlan = domino_types.NewPlan()
	if err := p.APlan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.APlan)
	}
	return nil
}

func (p *AddPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.APlan != nil {
		if err := oprot.WriteFieldBegin("aPlan", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:aPlan: %s", p, err)
		}
		if err := p.APlan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.APlan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:aPlan: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPlanArgs(%+v)", *p)
}

type AddPlanResult struct {
	Success IdInt                         `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewAddPlanResult() *AddPlanResult {
	return &AddPlanResult{}
}

func (p *AddPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPlanResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = IdInt(v)
	}
	return nil
}

func (p *AddPlanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *AddPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPlanResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPlanResult(%+v)", *p)
}

type EditPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	APlan  *domino_types.Plan    `thrift:"aPlan,2" json:"aPlan"`
}

func NewEditPlanArgs() *EditPlanArgs {
	return &EditPlanArgs{}
}

func (p *EditPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditPlanArgs) readField2(iprot thrift.TProtocol) error {
	p.APlan = domino_types.NewPlan()
	if err := p.APlan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.APlan)
	}
	return nil
}

func (p *EditPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.APlan != nil {
		if err := oprot.WriteFieldBegin("aPlan", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:aPlan: %s", p, err)
		}
		if err := p.APlan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.APlan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:aPlan: %s", p, err)
		}
	}
	return err
}

func (p *EditPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPlanArgs(%+v)", *p)
}

type EditPlanResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewEditPlanResult() *EditPlanResult {
	return &EditPlanResult{}
}

func (p *EditPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPlanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *EditPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *EditPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPlanResult(%+v)", *p)
}

type GetPlanByNameArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Name   string                `thrift:"name,2" json:"name"`
}

func NewGetPlanByNameArgs() *GetPlanByNameArgs {
	return &GetPlanByNameArgs{}
}

func (p *GetPlanByNameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByNameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPlanByNameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *GetPlanByNameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanByName_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByNameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *GetPlanByNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanByNameArgs(%+v)", *p)
}

type GetPlanByNameResult struct {
	Success *domino_types.Plan            `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetPlanByNameResult() *GetPlanByNameResult {
	return &GetPlanByNameResult{}
}

func (p *GetPlanByNameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByNameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewPlan()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPlanByNameResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetPlanByNameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanByName_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByNameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanByNameResult(%+v)", *p)
}

type ListPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    IdInt `thrift:"offset,10" json:"offset"`
	Limit     IdInt `thrift:"limit,11" json:"limit"`
	Ascending bool  `thrift:"ascending,12" json:"ascending"`
}

func NewListPlanArgs() *ListPlanArgs {
	return &ListPlanArgs{}
}

func (p *ListPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListPlanArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = IdInt(v)
	}
	return nil
}

func (p *ListPlanArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = IdInt(v)
	}
	return nil
}

func (p *ListPlanArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *ListPlanArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *ListPlanArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *ListPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPlanArgs(%+v)", *p)
}

type ListPlanResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
}

func NewListPlanResult() *ListPlanResult {
	return &ListPlanResult{}
}

func (p *ListPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPlanResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPlanResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPlanResult(%+v)", *p)
}

type ListPlanByMaintainerArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	Maintainer string                `thrift:"maintainer,2" json:"maintainer"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    IdInt `thrift:"offset,10" json:"offset"`
	Limit     IdInt `thrift:"limit,11" json:"limit"`
	Ascending bool  `thrift:"ascending,12" json:"ascending"`
}

func NewListPlanByMaintainerArgs() *ListPlanByMaintainerArgs {
	return &ListPlanByMaintainerArgs{}
}

func (p *ListPlanByMaintainerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Maintainer = v
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = IdInt(v)
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = IdInt(v)
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPlanByMaintainer_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPlanByMaintainerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanByMaintainerArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maintainer", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maintainer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Maintainer)); err != nil {
		return fmt.Errorf("%T.maintainer (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maintainer: %s", p, err)
	}
	return err
}

func (p *ListPlanByMaintainerArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *ListPlanByMaintainerArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *ListPlanByMaintainerArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *ListPlanByMaintainerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPlanByMaintainerArgs(%+v)", *p)
}

type ListPlanByMaintainerResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
}

func NewListPlanByMaintainerResult() *ListPlanByMaintainerResult {
	return &ListPlanByMaintainerResult{}
}

func (p *ListPlanByMaintainerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPlanByMaintainerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListPlanByMaintainerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPlanByMaintainer_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPlanByMaintainerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanByMaintainerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPlanByMaintainerResult(%+v)", *p)
}

type SearchPlanByNameArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	Token      string                `thrift:"token,2" json:"token"`
	Maintainer string                `thrift:"maintainer,3" json:"maintainer"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    IdInt `thrift:"offset,10" json:"offset"`
	Limit     IdInt `thrift:"limit,11" json:"limit"`
	Ascending bool  `thrift:"ascending,12" json:"ascending"`
}

func NewSearchPlanByNameArgs() *SearchPlanByNameArgs {
	return &SearchPlanByNameArgs{}
}

func (p *SearchPlanByNameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchPlanByNameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchPlanByNameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Token = v
	}
	return nil
}

func (p *SearchPlanByNameArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Maintainer = v
	}
	return nil
}

func (p *SearchPlanByNameArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = IdInt(v)
	}
	return nil
}

func (p *SearchPlanByNameArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = IdInt(v)
	}
	return nil
}

func (p *SearchPlanByNameArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *SearchPlanByNameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchPlanByName_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchPlanByNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchPlanByNameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("token", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Token)); err != nil {
		return fmt.Errorf("%T.token (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:token: %s", p, err)
	}
	return err
}

func (p *SearchPlanByNameArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maintainer", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:maintainer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Maintainer)); err != nil {
		return fmt.Errorf("%T.maintainer (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:maintainer: %s", p, err)
	}
	return err
}

func (p *SearchPlanByNameArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *SearchPlanByNameArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *SearchPlanByNameArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *SearchPlanByNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchPlanByNameArgs(%+v)", *p)
}

type SearchPlanByNameResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
}

func NewSearchPlanByNameResult() *SearchPlanByNameResult {
	return &SearchPlanByNameResult{}
}

func (p *SearchPlanByNameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchPlanByNameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchPlanByNameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchPlanByName_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchPlanByNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchPlanByNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchPlanByNameResult(%+v)", *p)
}

type GetPlanByIdsArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanIds []IdInt               `thrift:"planIds,2" json:"planIds"`
}

func NewGetPlanByIdsArgs() *GetPlanByIdsArgs {
	return &GetPlanByIdsArgs{}
}

func (p *GetPlanByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPlanByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlanIds = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem77 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem77 = IdInt(v)
		}
		p.PlanIds = append(p.PlanIds, _elem77)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPlanByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PlanIds != nil {
		if err := oprot.WriteFieldBegin("planIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:planIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlanIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlanIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:planIds: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanByIdsArgs(%+v)", *p)
}

type GetPlanByIdsResult struct {
	Success map[IdInt]*domino_types.Plan  `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetPlanByIdsResult() *GetPlanByIdsResult {
	return &GetPlanByIdsResult{}
}

func (p *GetPlanByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt]*domino_types.Plan, size)
	for i := 0; i < size; i++ {
		var _key78 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key78 = IdInt(v)
		}
		_val79 := domino_types.NewPlan()
		if err := _val79.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val79)
		}
		p.Success[_key78] = _val79
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetPlanByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetPlanByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanByIdsResult(%+v)", *p)
}

type GetPlanDepsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
}

func NewGetPlanDepsArgs() *GetPlanDepsArgs {
	return &GetPlanDepsArgs{}
}

func (p *GetPlanDepsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPlanDepsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetPlanDepsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanDeps_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanDepsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetPlanDepsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanDepsArgs(%+v)", *p)
}

type GetPlanDepsResult struct {
	Success []*domino_types.PlanDep       `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetPlanDepsResult() *GetPlanDepsResult {
	return &GetPlanDepsResult{}
}

func (p *GetPlanDepsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.PlanDep, 0, size)
	for i := 0; i < size; i++ {
		_elem80 := domino_types.NewPlanDep()
		if err := _elem80.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem80)
		}
		p.Success = append(p.Success, _elem80)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPlanDepsResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetPlanDepsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanDeps_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanDepsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanDepsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanDepsResult(%+v)", *p)
}

type GetPlanDepIdsAllArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
}

func NewGetPlanDepIdsAllArgs() *GetPlanDepIdsAllArgs {
	return &GetPlanDepIdsAllArgs{}
}

func (p *GetPlanDepIdsAllArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepIdsAllArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPlanDepIdsAllArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetPlanDepIdsAllArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanDepIdsAll_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepIdsAllArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanDepIdsAllArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetPlanDepIdsAllArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanDepIdsAllArgs(%+v)", *p)
}

type GetPlanDepIdsAllResult struct {
	Success []IdInt                       `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetPlanDepIdsAllResult() *GetPlanDepIdsAllResult {
	return &GetPlanDepIdsAllResult{}
}

func (p *GetPlanDepIdsAllResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepIdsAllResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem81 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem81 = IdInt(v)
		}
		p.Success = append(p.Success, _elem81)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPlanDepIdsAllResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetPlanDepIdsAllResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanDepIdsAll_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanDepIdsAllResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanDepIdsAllResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanDepIdsAllResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanDepIdsAllResult(%+v)", *p)
}

type GetDepPlansArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
}

func NewGetDepPlansArgs() *GetDepPlansArgs {
	return &GetDepPlansArgs{}
}

func (p *GetDepPlansArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDepPlansArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetDepPlansArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetDepPlansArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDepPlans_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDepPlansArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetDepPlansArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetDepPlansArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDepPlansArgs(%+v)", *p)
}

type GetDepPlansResult struct {
	Success []*domino_types.PlanDep       `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetDepPlansResult() *GetDepPlansResult {
	return &GetDepPlansResult{}
}

func (p *GetDepPlansResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDepPlansResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.PlanDep, 0, size)
	for i := 0; i < size; i++ {
		_elem82 := domino_types.NewPlanDep()
		if err := _elem82.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem82)
		}
		p.Success = append(p.Success, _elem82)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetDepPlansResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetDepPlansResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDepPlans_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDepPlansResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDepPlansResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetDepPlansResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDepPlansResult(%+v)", *p)
}

type SavePlanDepsArgs struct {
	Header *common.RequestHeader   `thrift:"header,1" json:"header"`
	PlanId common.IdInt            `thrift:"planId,2" json:"planId"`
	Deps   []*domino_types.PlanDep `thrift:"deps,3" json:"deps"`
}

func NewSavePlanDepsArgs() *SavePlanDepsArgs {
	return &SavePlanDepsArgs{}
}

func (p *SavePlanDepsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SavePlanDepsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SavePlanDepsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *SavePlanDepsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Deps = make([]*domino_types.PlanDep, 0, size)
	for i := 0; i < size; i++ {
		_elem83 := domino_types.NewPlanDep()
		if err := _elem83.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem83)
		}
		p.Deps = append(p.Deps, _elem83)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SavePlanDepsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("savePlanDeps_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SavePlanDepsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SavePlanDepsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *SavePlanDepsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Deps != nil {
		if err := oprot.WriteFieldBegin("deps", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deps: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Deps)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Deps {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deps: %s", p, err)
		}
	}
	return err
}

func (p *SavePlanDepsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SavePlanDepsArgs(%+v)", *p)
}

type SavePlanDepsResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewSavePlanDepsResult() *SavePlanDepsResult {
	return &SavePlanDepsResult{}
}

func (p *SavePlanDepsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SavePlanDepsResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *SavePlanDepsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("savePlanDeps_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SavePlanDepsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *SavePlanDepsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SavePlanDepsResult(%+v)", *p)
}

type DeletePlanDepsArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId     common.IdInt          `thrift:"planId,2" json:"planId"`
	DepPlanIds []common.IdInt        `thrift:"depPlanIds,3" json:"depPlanIds"`
}

func NewDeletePlanDepsArgs() *DeletePlanDepsArgs {
	return &DeletePlanDepsArgs{}
}

func (p *DeletePlanDepsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanDepsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeletePlanDepsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *DeletePlanDepsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DepPlanIds = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem84 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem84 = common.IdInt(v)
		}
		p.DepPlanIds = append(p.DepPlanIds, _elem84)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeletePlanDepsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deletePlanDeps_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanDepsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeletePlanDepsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *DeletePlanDepsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DepPlanIds != nil {
		if err := oprot.WriteFieldBegin("depPlanIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:depPlanIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DepPlanIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DepPlanIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:depPlanIds: %s", p, err)
		}
	}
	return err
}

func (p *DeletePlanDepsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeletePlanDepsArgs(%+v)", *p)
}

type DeletePlanDepsResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewDeletePlanDepsResult() *DeletePlanDepsResult {
	return &DeletePlanDepsResult{}
}

func (p *DeletePlanDepsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanDepsResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *DeletePlanDepsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deletePlanDeps_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanDepsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *DeletePlanDepsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeletePlanDepsResult(%+v)", *p)
}

type SuspendPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId common.IdInt          `thrift:"planId,2" json:"planId"`
}

func NewSuspendPlanArgs() *SuspendPlanArgs {
	return &SuspendPlanArgs{}
}

func (p *SuspendPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SuspendPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SuspendPlanArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *SuspendPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("suspendPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SuspendPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SuspendPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *SuspendPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SuspendPlanArgs(%+v)", *p)
}

type SuspendPlanResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewSuspendPlanResult() *SuspendPlanResult {
	return &SuspendPlanResult{}
}

func (p *SuspendPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SuspendPlanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *SuspendPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("suspendPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SuspendPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *SuspendPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SuspendPlanResult(%+v)", *p)
}

type StopPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId common.IdInt          `thrift:"planId,2" json:"planId"`
}

func NewStopPlanArgs() *StopPlanArgs {
	return &StopPlanArgs{}
}

func (p *StopPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StopPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *StopPlanArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *StopPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("stopPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StopPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *StopPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *StopPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopPlanArgs(%+v)", *p)
}

type StopPlanResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewStopPlanResult() *StopPlanResult {
	return &StopPlanResult{}
}

func (p *StopPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StopPlanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *StopPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("stopPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StopPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *StopPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopPlanResult(%+v)", *p)
}

type RestartPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId common.IdInt          `thrift:"planId,2" json:"planId"`
}

func NewRestartPlanArgs() *RestartPlanArgs {
	return &RestartPlanArgs{}
}

func (p *RestartPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RestartPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RestartPlanArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *RestartPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("restartPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RestartPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RestartPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *RestartPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestartPlanArgs(%+v)", *p)
}

type RestartPlanResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewRestartPlanResult() *RestartPlanResult {
	return &RestartPlanResult{}
}

func (p *RestartPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RestartPlanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *RestartPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("restartPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RestartPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *RestartPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestartPlanResult(%+v)", *p)
}

type DeletePlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId common.IdInt          `thrift:"planId,2" json:"planId"`
}

func NewDeletePlanArgs() *DeletePlanArgs {
	return &DeletePlanArgs{}
}

func (p *DeletePlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeletePlanArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *DeletePlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deletePlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeletePlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *DeletePlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeletePlanArgs(%+v)", *p)
}

type DeletePlanResult struct {
	De *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewDeletePlanResult() *DeletePlanResult {
	return &DeletePlanResult{}
}

func (p *DeletePlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *DeletePlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deletePlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeletePlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *DeletePlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeletePlanResult(%+v)", *p)
}

type ViewPlanByHostArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
}

func NewViewPlanByHostArgs() *ViewPlanByHostArgs {
	return &ViewPlanByHostArgs{}
}

func (p *ViewPlanByHostArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ViewPlanByHostArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ViewPlanByHostArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("viewPlanByHost_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ViewPlanByHostArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ViewPlanByHostArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ViewPlanByHostArgs(%+v)", *p)
}

type ViewPlanByHostResult struct {
	Success map[string][]*domino_types.Plan `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException   `thrift:"de,1" json:"de"`
}

func NewViewPlanByHostResult() *ViewPlanByHostResult {
	return &ViewPlanByHostResult{}
}

func (p *ViewPlanByHostResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ViewPlanByHostResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string][]*domino_types.Plan, size)
	for i := 0; i < size; i++ {
		var _key85 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key85 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val86 := make([]*domino_types.Plan, 0, size)
		for i := 0; i < size; i++ {
			_elem87 := domino_types.NewPlan()
			if err := _elem87.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem87)
			}
			_val86 = append(_val86, _elem87)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key85] = _val86
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ViewPlanByHostResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ViewPlanByHostResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("viewPlanByHost_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ViewPlanByHostResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ViewPlanByHostResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ViewPlanByHostResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ViewPlanByHostResult(%+v)", *p)
}

type GetMaintainersArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
}

func NewGetMaintainersArgs() *GetMaintainersArgs {
	return &GetMaintainersArgs{}
}

func (p *GetMaintainersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMaintainersArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetMaintainersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMaintainers_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMaintainersArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetMaintainersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMaintainersArgs(%+v)", *p)
}

type GetMaintainersResult struct {
	Success []string                      `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetMaintainersResult() *GetMaintainersResult {
	return &GetMaintainersResult{}
}

func (p *GetMaintainersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMaintainersResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem88 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem88 = v
		}
		p.Success = append(p.Success, _elem88)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetMaintainersResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetMaintainersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMaintainers_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMaintainersResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMaintainersResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetMaintainersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMaintainersResult(%+v)", *p)
}

type GetUsersByPlanIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId common.IdInt          `thrift:"planId,2" json:"planId"`
}

func NewGetUsersByPlanIdArgs() *GetUsersByPlanIdArgs {
	return &GetUsersByPlanIdArgs{}
}

func (p *GetUsersByPlanIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByPlanIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetUsersByPlanIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = common.IdInt(v)
	}
	return nil
}

func (p *GetUsersByPlanIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUsersByPlanId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByPlanIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByPlanIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetUsersByPlanIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUsersByPlanIdArgs(%+v)", *p)
}

type GetUsersByPlanIdResult struct {
	Success []string                      `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetUsersByPlanIdResult() *GetUsersByPlanIdResult {
	return &GetUsersByPlanIdResult{}
}

func (p *GetUsersByPlanIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByPlanIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem89 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem89 = v
		}
		p.Success = append(p.Success, _elem89)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetUsersByPlanIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetUsersByPlanIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getUsersByPlanId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUsersByPlanIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByPlanIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetUsersByPlanIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUsersByPlanIdResult(%+v)", *p)
}
