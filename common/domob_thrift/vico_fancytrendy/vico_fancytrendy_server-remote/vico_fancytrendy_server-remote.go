// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"vico_fancytrendy"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  PostInfo getFtLandingPostByLandingId(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  PostInfo getProductPostById(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  PostInfo getCollectionPostById(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "   getFollowedPublishers(RequestHeader header, i64 ftUserId)")
	fmt.Fprintln(os.Stderr, "   getProductPostsByCategory(RequestHeader header, ProductCategory category, string sort, i64 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getCollectionPosts(RequestHeader header, string sort, i64 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getTopCollectionPosts(RequestHeader header, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getFollowingFeed(RequestHeader header, i64 ftUserId, string sort, i64 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getRecommendCollectionPosts(RequestHeader header, i64 postId, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getFollowingPublisherIds(RequestHeader header, i64 ftUserId)")
	fmt.Fprintln(os.Stderr, "   getLikedPostIds(RequestHeader header, i64 ftUserId)")
	fmt.Fprintln(os.Stderr, "  bool markPostLike(RequestHeader header, i64 ftUserId, i64 postId, bool like)")
	fmt.Fprintln(os.Stderr, "  bool markFollowing(RequestHeader header, i64 ftUserId, i64 publisherId, bool follow)")
	fmt.Fprintln(os.Stderr, "   getPostIdsByProductIds(RequestHeader header,  productIds)")
	fmt.Fprintln(os.Stderr, "   getPostIdsByCollectionIds(RequestHeader header,  collectionIds)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := vico_fancytrendy.NewVicoFancytrendyServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getFtLandingPostByLandingId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFtLandingPostByLandingId requires 2 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err82 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err82 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFtLandingPostByLandingId(value0, value1))
		fmt.Print("\n")
		break
	case "getProductPostById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProductPostById requires 2 args")
			flag.Usage()
		}
		arg83 := flag.Arg(1)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err88 := argvalue0.Read(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err89 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err89 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetProductPostById(value0, value1))
		fmt.Print("\n")
		break
	case "getCollectionPostById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCollectionPostById requires 2 args")
			flag.Usage()
		}
		arg90 := flag.Arg(1)
		mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
		defer mbTrans91.Close()
		_, err92 := mbTrans91.WriteString(arg90)
		if err92 != nil {
			Usage()
			return
		}
		factory93 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt94 := factory93.GetProtocol(mbTrans91)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err95 := argvalue0.Read(jsProt94)
		if err95 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err96 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err96 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetCollectionPostById(value0, value1))
		fmt.Print("\n")
		break
	case "getFollowedPublishers":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFollowedPublishers requires 2 args")
			flag.Usage()
		}
		arg97 := flag.Arg(1)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err102 := argvalue0.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err103 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err103 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFollowedPublishers(value0, value1))
		fmt.Print("\n")
		break
	case "getProductPostsByCategory":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetProductPostsByCategory requires 5 args")
			flag.Usage()
		}
		arg104 := flag.Arg(1)
		mbTrans105 := thrift.NewTMemoryBufferLen(len(arg104))
		defer mbTrans105.Close()
		_, err106 := mbTrans105.WriteString(arg104)
		if err106 != nil {
			Usage()
			return
		}
		factory107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt108 := factory107.GetProtocol(mbTrans105)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err109 := argvalue0.Read(jsProt108)
		if err109 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := vico_fancytrendy.ProductCategory(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3, err111 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err111 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err112 := (strconv.Atoi(flag.Arg(5)))
		if err112 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetProductPostsByCategory(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getCollectionPosts":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetCollectionPosts requires 4 args")
			flag.Usage()
		}
		arg113 := flag.Arg(1)
		mbTrans114 := thrift.NewTMemoryBufferLen(len(arg113))
		defer mbTrans114.Close()
		_, err115 := mbTrans114.WriteString(arg113)
		if err115 != nil {
			Usage()
			return
		}
		factory116 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt117 := factory116.GetProtocol(mbTrans114)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err118 := argvalue0.Read(jsProt117)
		if err118 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2, err120 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err120 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err121 := (strconv.Atoi(flag.Arg(4)))
		if err121 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.GetCollectionPosts(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getTopCollectionPosts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTopCollectionPosts requires 2 args")
			flag.Usage()
		}
		arg122 := flag.Arg(1)
		mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
		defer mbTrans123.Close()
		_, err124 := mbTrans123.WriteString(arg122)
		if err124 != nil {
			Usage()
			return
		}
		factory125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt126 := factory125.GetProtocol(mbTrans123)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err127 := argvalue0.Read(jsProt126)
		if err127 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err128 := (strconv.Atoi(flag.Arg(2)))
		if err128 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetTopCollectionPosts(value0, value1))
		fmt.Print("\n")
		break
	case "getFollowingFeed":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetFollowingFeed requires 5 args")
			flag.Usage()
		}
		arg129 := flag.Arg(1)
		mbTrans130 := thrift.NewTMemoryBufferLen(len(arg129))
		defer mbTrans130.Close()
		_, err131 := mbTrans130.WriteString(arg129)
		if err131 != nil {
			Usage()
			return
		}
		factory132 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt133 := factory132.GetProtocol(mbTrans130)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err134 := argvalue0.Read(jsProt133)
		if err134 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err135 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err135 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3, err137 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err137 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err138 := (strconv.Atoi(flag.Arg(5)))
		if err138 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetFollowingFeed(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getRecommendCollectionPosts":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetRecommendCollectionPosts requires 3 args")
			flag.Usage()
		}
		arg139 := flag.Arg(1)
		mbTrans140 := thrift.NewTMemoryBufferLen(len(arg139))
		defer mbTrans140.Close()
		_, err141 := mbTrans140.WriteString(arg139)
		if err141 != nil {
			Usage()
			return
		}
		factory142 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt143 := factory142.GetProtocol(mbTrans140)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err144 := argvalue0.Read(jsProt143)
		if err144 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err145 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err145 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err146 := (strconv.Atoi(flag.Arg(3)))
		if err146 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetRecommendCollectionPosts(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFollowingPublisherIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFollowingPublisherIds requires 2 args")
			flag.Usage()
		}
		arg147 := flag.Arg(1)
		mbTrans148 := thrift.NewTMemoryBufferLen(len(arg147))
		defer mbTrans148.Close()
		_, err149 := mbTrans148.WriteString(arg147)
		if err149 != nil {
			Usage()
			return
		}
		factory150 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt151 := factory150.GetProtocol(mbTrans148)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err152 := argvalue0.Read(jsProt151)
		if err152 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err153 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err153 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFollowingPublisherIds(value0, value1))
		fmt.Print("\n")
		break
	case "getLikedPostIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLikedPostIds requires 2 args")
			flag.Usage()
		}
		arg154 := flag.Arg(1)
		mbTrans155 := thrift.NewTMemoryBufferLen(len(arg154))
		defer mbTrans155.Close()
		_, err156 := mbTrans155.WriteString(arg154)
		if err156 != nil {
			Usage()
			return
		}
		factory157 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt158 := factory157.GetProtocol(mbTrans155)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err159 := argvalue0.Read(jsProt158)
		if err159 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err160 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err160 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetLikedPostIds(value0, value1))
		fmt.Print("\n")
		break
	case "markPostLike":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "MarkPostLike requires 4 args")
			flag.Usage()
		}
		arg161 := flag.Arg(1)
		mbTrans162 := thrift.NewTMemoryBufferLen(len(arg161))
		defer mbTrans162.Close()
		_, err163 := mbTrans162.WriteString(arg161)
		if err163 != nil {
			Usage()
			return
		}
		factory164 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt165 := factory164.GetProtocol(mbTrans162)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err166 := argvalue0.Read(jsProt165)
		if err166 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err167 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err167 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err168 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err168 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.MarkPostLike(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "markFollowing":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "MarkFollowing requires 4 args")
			flag.Usage()
		}
		arg170 := flag.Arg(1)
		mbTrans171 := thrift.NewTMemoryBufferLen(len(arg170))
		defer mbTrans171.Close()
		_, err172 := mbTrans171.WriteString(arg170)
		if err172 != nil {
			Usage()
			return
		}
		factory173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt174 := factory173.GetProtocol(mbTrans171)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err175 := argvalue0.Read(jsProt174)
		if err175 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err176 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err176 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err177 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err177 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.MarkFollowing(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getPostIdsByProductIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPostIdsByProductIds requires 2 args")
			flag.Usage()
		}
		arg179 := flag.Arg(1)
		mbTrans180 := thrift.NewTMemoryBufferLen(len(arg179))
		defer mbTrans180.Close()
		_, err181 := mbTrans180.WriteString(arg179)
		if err181 != nil {
			Usage()
			return
		}
		factory182 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt183 := factory182.GetProtocol(mbTrans180)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err184 := argvalue0.Read(jsProt183)
		if err184 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg185 := flag.Arg(2)
		mbTrans186 := thrift.NewTMemoryBufferLen(len(arg185))
		defer mbTrans186.Close()
		_, err187 := mbTrans186.WriteString(arg185)
		if err187 != nil {
			Usage()
			return
		}
		factory188 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt189 := factory188.GetProtocol(mbTrans186)
		containerStruct1 := vico_fancytrendy.NewGetPostIdsByProductIdsArgs()
		err190 := containerStruct1.ReadField2(jsProt189)
		if err190 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ProductIds
		value1 := argvalue1
		fmt.Print(client.GetPostIdsByProductIds(value0, value1))
		fmt.Print("\n")
		break
	case "getPostIdsByCollectionIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPostIdsByCollectionIds requires 2 args")
			flag.Usage()
		}
		arg191 := flag.Arg(1)
		mbTrans192 := thrift.NewTMemoryBufferLen(len(arg191))
		defer mbTrans192.Close()
		_, err193 := mbTrans192.WriteString(arg191)
		if err193 != nil {
			Usage()
			return
		}
		factory194 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt195 := factory194.GetProtocol(mbTrans192)
		argvalue0 := vico_fancytrendy.NewRequestHeader()
		err196 := argvalue0.Read(jsProt195)
		if err196 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg197 := flag.Arg(2)
		mbTrans198 := thrift.NewTMemoryBufferLen(len(arg197))
		defer mbTrans198.Close()
		_, err199 := mbTrans198.WriteString(arg197)
		if err199 != nil {
			Usage()
			return
		}
		factory200 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt201 := factory200.GetProtocol(mbTrans198)
		containerStruct1 := vico_fancytrendy.NewGetPostIdsByCollectionIdsArgs()
		err202 := containerStruct1.ReadField2(jsProt201)
		if err202 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CollectionIds
		value1 := argvalue1
		fmt.Print(client.GetPostIdsByCollectionIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
