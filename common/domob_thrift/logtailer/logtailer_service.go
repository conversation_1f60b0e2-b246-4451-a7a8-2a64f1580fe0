// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package logtailer

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/logtailer_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = logtailer_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type LogtailerService interface { //logtailer的下游，可以实现这个服务，处理logtailer发送的日志

	// Parameters:
	//  - Message
	ProcessMessage(message *logtailer_types.Message) (r ResultCode, err error)
}

//logtailer的下游，可以实现这个服务，处理logtailer发送的日志
type LogtailerServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewLogtailerServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *LogtailerServiceClient {
	return &LogtailerServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewLogtailerServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *LogtailerServiceClient {
	return &LogtailerServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// Parameters:
//  - Message
func (p *LogtailerServiceClient) ProcessMessage(message *logtailer_types.Message) (r ResultCode, err error) {
	if err = p.sendProcessMessage(message); err != nil {
		return
	}
	return p.recvProcessMessage()
}

func (p *LogtailerServiceClient) sendProcessMessage(message *logtailer_types.Message) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("processMessage", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewProcessMessageArgs()
	args0.Message = message
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *LogtailerServiceClient) recvProcessMessage() (value ResultCode, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewProcessMessageResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

type LogtailerServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      LogtailerService
}

func (p *LogtailerServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *LogtailerServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *LogtailerServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewLogtailerServiceProcessor(handler LogtailerService) *LogtailerServiceProcessor {

	self4 := &LogtailerServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self4.processorMap["processMessage"] = &logtailerServiceProcessorProcessMessage{handler: handler}
	return self4
}

func (p *LogtailerServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x5.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x5

}

type logtailerServiceProcessorProcessMessage struct {
	handler LogtailerService
}

func (p *logtailerServiceProcessorProcessMessage) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewProcessMessageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("processMessage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewProcessMessageResult()
	if result.Success, err = p.handler.ProcessMessage(args.Message); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing processMessage: "+err.Error())
		oprot.WriteMessageBegin("processMessage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("processMessage", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ProcessMessageArgs struct {
	Message *logtailer_types.Message `thrift:"message,1" json:"message"`
}

func NewProcessMessageArgs() *ProcessMessageArgs {
	return &ProcessMessageArgs{}
}

func (p *ProcessMessageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessMessageArgs) readField1(iprot thrift.TProtocol) error {
	p.Message = logtailer_types.NewMessage()
	if err := p.Message.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Message)
	}
	return nil
}

func (p *ProcessMessageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processMessage_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessMessageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Message != nil {
		if err := oprot.WriteFieldBegin("message", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
		}
		if err := p.Message.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Message)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:message: %s", p, err)
		}
	}
	return err
}

func (p *ProcessMessageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessMessageArgs(%+v)", *p)
}

type ProcessMessageResult struct {
	Success ResultCode `thrift:"success,0" json:"success"`
}

func NewProcessMessageResult() *ProcessMessageResult {
	return &ProcessMessageResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ProcessMessageResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *ProcessMessageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessMessageResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = ResultCode(v)
	}
	return nil
}

func (p *ProcessMessageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processMessage_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessMessageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ProcessMessageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessMessageResult(%+v)", *p)
}
