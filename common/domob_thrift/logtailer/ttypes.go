// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package logtailer

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/logtailer_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = logtailer_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type ResultCode int64

const (
	ResultCode_OK        ResultCode = 0
	ResultCode_TRY_LATER ResultCode = 1
)

func (p ResultCode) String() string {
	switch p {
	case ResultCode_OK:
		return "ResultCode_OK"
	case ResultCode_TRY_LATER:
		return "ResultCode_TRY_LATER"
	}
	return "<UNSET>"
}

func ResultCodeFromString(s string) (ResultCode, error) {
	switch s {
	case "ResultCode_OK":
		return ResultCode_OK, nil
	case "ResultCode_TRY_LATER":
		return ResultCode_TRY_LATER, nil
	}
	return ResultCode(math.MinInt32 - 1), fmt.Errorf("not a valid ResultCode string")
}

type Message *logtailer_types.Message
