// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package logtailer

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/logtailer_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = logtailer_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type LogTailerApp interface {
	dm303.DomobService

	// reload a single stream by name
	//
	// Parameters:
	//  - StreamName
	Reload(streamName string) (err error)
}

type LogTailerAppClient struct {
	*dm303.DomobServiceClient
}

func NewLogTailerAppClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *LogTailerAppClient {
	return &LogTailerAppClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewLogTailerAppClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *LogTailerAppClient {
	return &LogTailerAppClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// reload a single stream by name
//
// Parameters:
//  - StreamName
func (p *LogTailerAppClient) Reload(streamName string) (err error) {
	if err = p.sendReload(streamName); err != nil {
		return
	}
	return
}

func (p *LogTailerAppClient) sendReload(streamName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("reload", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewReloadArgs()
	args12.StreamName = streamName
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *LogTailerAppClient) recvReload() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewReloadResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

type LogTailerAppProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewLogTailerAppProcessor(handler LogTailerApp) *LogTailerAppProcessor {
	self16 := &LogTailerAppProcessor{dm303.NewDomobServiceProcessor(handler)}
	self16.AddToProcessorMap("reload", &logTailerAppProcessorReload{handler: handler})
	return self16
}

type logTailerAppProcessorReload struct {
	handler LogTailerApp
}

func (p *logTailerAppProcessorReload) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewReloadArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("reload", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewReloadResult()
	if err = p.handler.Reload(args.StreamName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing reload: "+err.Error())
		oprot.WriteMessageBegin("reload", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("reload", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ReloadArgs struct {
	StreamName string `thrift:"streamName,1" json:"streamName"`
}

func NewReloadArgs() *ReloadArgs {
	return &ReloadArgs{}
}

func (p *ReloadArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReloadArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StreamName = v
	}
	return nil
}

func (p *ReloadArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("reload_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReloadArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("streamName", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:streamName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StreamName)); err != nil {
		return fmt.Errorf("%T.streamName (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:streamName: %s", p, err)
	}
	return err
}

func (p *ReloadArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReloadArgs(%+v)", *p)
}

type ReloadResult struct {
}

func NewReloadResult() *ReloadResult {
	return &ReloadResult{}
}

func (p *ReloadResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReloadResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("reload_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReloadResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReloadResult(%+v)", *p)
}
