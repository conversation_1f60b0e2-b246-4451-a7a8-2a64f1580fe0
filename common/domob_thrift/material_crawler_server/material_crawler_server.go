// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package material_crawler_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__

type MaterialCrawlerServer interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - Video
	CrawlVideoMaterial(header *common.RequestHeader, video *programmatic_creative_types.PCVideo) (rae *programmatic_creative_types.PCSException, err error)
}

type MaterialCrawlerServerClient struct {
	*dm303.DomobServiceClient
}

func NewMaterialCrawlerServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *MaterialCrawlerServerClient {
	return &MaterialCrawlerServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewMaterialCrawlerServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *MaterialCrawlerServerClient {
	return &MaterialCrawlerServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - Video
func (p *MaterialCrawlerServerClient) CrawlVideoMaterial(header *common.RequestHeader, video *programmatic_creative_types.PCVideo) (rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendCrawlVideoMaterial(header, video); err != nil {
		return
	}
	return p.recvCrawlVideoMaterial()
}

func (p *MaterialCrawlerServerClient) sendCrawlVideoMaterial(header *common.RequestHeader, video *programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("crawlVideoMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewCrawlVideoMaterialArgs()
	args0.Header = header
	args0.Video = video
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *MaterialCrawlerServerClient) recvCrawlVideoMaterial() (rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewCrawlVideoMaterialResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result1.Rae != nil {
		rae = result1.Rae
	}
	return
}

type MaterialCrawlerServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewMaterialCrawlerServerProcessor(handler MaterialCrawlerServer) *MaterialCrawlerServerProcessor {
	self4 := &MaterialCrawlerServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self4.AddToProcessorMap("crawlVideoMaterial", &materialCrawlerServerProcessorCrawlVideoMaterial{handler: handler})
	return self4
}

type materialCrawlerServerProcessorCrawlVideoMaterial struct {
	handler MaterialCrawlerServer
}

func (p *materialCrawlerServerProcessorCrawlVideoMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCrawlVideoMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("crawlVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCrawlVideoMaterialResult()
	if result.Rae, err = p.handler.CrawlVideoMaterial(args.Header, args.Video); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing crawlVideoMaterial: "+err.Error())
		oprot.WriteMessageBegin("crawlVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("crawlVideoMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type CrawlVideoMaterialArgs struct {
	Header *common.RequestHeader                `thrift:"header,1" json:"header"`
	Video  *programmatic_creative_types.PCVideo `thrift:"video,2" json:"video"`
}

func NewCrawlVideoMaterialArgs() *CrawlVideoMaterialArgs {
	return &CrawlVideoMaterialArgs{}
}

func (p *CrawlVideoMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrawlVideoMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CrawlVideoMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.Video = programmatic_creative_types.NewPCVideo()
	if err := p.Video.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Video)
	}
	return nil
}

func (p *CrawlVideoMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("crawlVideoMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrawlVideoMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CrawlVideoMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Video != nil {
		if err := oprot.WriteFieldBegin("video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:video: %s", p, err)
		}
		if err := p.Video.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Video)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:video: %s", p, err)
		}
	}
	return err
}

func (p *CrawlVideoMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrawlVideoMaterialArgs(%+v)", *p)
}

type CrawlVideoMaterialResult struct {
	Rae *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewCrawlVideoMaterialResult() *CrawlVideoMaterialResult {
	return &CrawlVideoMaterialResult{}
}

func (p *CrawlVideoMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrawlVideoMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *CrawlVideoMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("crawlVideoMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrawlVideoMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *CrawlVideoMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrawlVideoMaterialResult(%+v)", *p)
}
