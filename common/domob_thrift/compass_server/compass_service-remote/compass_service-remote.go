// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"compass_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>der<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addPromotion(RequestHeader header, Promotion promotion)")
	fmt.Fprintln(os.Stderr, "  void editPromotion(RequestHeader header, Promotion promotion)")
	fmt.Fprintln(os.<PERSON>r, "  QueryResult searchPromotionsByParams(RequestHeader header, PromotionParams params)")
	fmt.Fprintln(os.Stderr, "   getPromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addChannel(RequestHeader header, Channel channel)")
	fmt.Fprintln(os.Stderr, "  void editChannel(RequestHeader header, Channel channel)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchChannelsByParams(RequestHeader header, ChannelParams params)")
	fmt.Fprintln(os.Stderr, "   getChannelsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addProject(RequestHeader header, Project project)")
	fmt.Fprintln(os.Stderr, "  void editProject(RequestHeader header, Project project)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchProjectsByParams(RequestHeader header, ProjectParams params)")
	fmt.Fprintln(os.Stderr, "   getProjectsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCampaign(RequestHeader header, Campaign campaign)")
	fmt.Fprintln(os.Stderr, "  void editCampaign(RequestHeader header, Campaign campaign)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCampaignsByParams(RequestHeader header, CampaignParams params)")
	fmt.Fprintln(os.Stderr, "   getCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addStrategy(RequestHeader header, Strategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editStrategy(RequestHeader header, Strategy strategy)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchStrategysByParams(RequestHeader header, StrategyParams params)")
	fmt.Fprintln(os.Stderr, "   getStrategysByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  void editCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCreativesByParams(RequestHeader header, CreativeParams params)")
	fmt.Fprintln(os.Stderr, "   getCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addLifeCycleGroup(RequestHeader header, LifeCycleGroup lifecycleGroup)")
	fmt.Fprintln(os.Stderr, "  void editLifeCycleGroup(RequestHeader header, LifeCycleGroup lifecycleGroup)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchLifeCycleGroupsByParams(RequestHeader header, LifeCycleGroupParams params)")
	fmt.Fprintln(os.Stderr, "   getLifeCycleGroupsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteLifeCycleGroupsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCrowd(RequestHeader header, Crowd crowd)")
	fmt.Fprintln(os.Stderr, "  void editCrowd(RequestHeader header, Crowd crowd)")
	fmt.Fprintln(os.Stderr, "  void deleteCrowdsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCrowdsByParams(RequestHeader header, CrowdParams params)")
	fmt.Fprintln(os.Stderr, "   getCrowdsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addLifeCycle(RequestHeader header, LifeCycle lifecycle)")
	fmt.Fprintln(os.Stderr, "  void editLifeCycle(RequestHeader header, LifeCycle lifecycle)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchLifeCyclesByParams(RequestHeader header, LifeCycleParams params)")
	fmt.Fprintln(os.Stderr, "   getLifeCyclesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteLifeCyclesByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAppLaunch(RequestHeader header, AppLaunch crowd)")
	fmt.Fprintln(os.Stderr, "  void deleteAppLaunchsByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAppLaunchsByParams(RequestHeader header, AppLaunchParams params)")
	fmt.Fprintln(os.Stderr, "   getAppLaunchsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getInsightSchedules(RequestHeader header, CompassCategory category,  ids)")
	fmt.Fprintln(os.Stderr, "  void reloadUv(RequestHeader header, i32 uid, CompassCategory category,  ids)")
	fmt.Fprintln(os.Stderr, "  void reloadUvOfLifeCycles(RequestHeader header, i32 uid, i32 groupId)")
	fmt.Fprintln(os.Stderr, "  void insight(RequestHeader header, i32 uid, CompassCategory category,  platforms,  ids)")
	fmt.Fprintln(os.Stderr, "  void push(RequestHeader header, i32 uid,  platforms, i32 accountId,  ids)")
	fmt.Fprintln(os.Stderr, "  void sendEvent(RequestHeader header, CompassEventCategory category, CompassEventType type, i32 uid,  ids,  platforms)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := compass_server.NewCompassServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotion requires 2 args")
			flag.Usage()
		}
		arg244 := flag.Arg(1)
		mbTrans245 := thrift.NewTMemoryBufferLen(len(arg244))
		defer mbTrans245.Close()
		_, err246 := mbTrans245.WriteString(arg244)
		if err246 != nil {
			Usage()
			return
		}
		factory247 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt248 := factory247.GetProtocol(mbTrans245)
		argvalue0 := compass_server.NewRequestHeader()
		err249 := argvalue0.Read(jsProt248)
		if err249 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg250 := flag.Arg(2)
		mbTrans251 := thrift.NewTMemoryBufferLen(len(arg250))
		defer mbTrans251.Close()
		_, err252 := mbTrans251.WriteString(arg250)
		if err252 != nil {
			Usage()
			return
		}
		factory253 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt254 := factory253.GetProtocol(mbTrans251)
		argvalue1 := compass_server.NewPromotion()
		err255 := argvalue1.Read(jsProt254)
		if err255 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "editPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPromotion requires 2 args")
			flag.Usage()
		}
		arg256 := flag.Arg(1)
		mbTrans257 := thrift.NewTMemoryBufferLen(len(arg256))
		defer mbTrans257.Close()
		_, err258 := mbTrans257.WriteString(arg256)
		if err258 != nil {
			Usage()
			return
		}
		factory259 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt260 := factory259.GetProtocol(mbTrans257)
		argvalue0 := compass_server.NewRequestHeader()
		err261 := argvalue0.Read(jsProt260)
		if err261 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg262 := flag.Arg(2)
		mbTrans263 := thrift.NewTMemoryBufferLen(len(arg262))
		defer mbTrans263.Close()
		_, err264 := mbTrans263.WriteString(arg262)
		if err264 != nil {
			Usage()
			return
		}
		factory265 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt266 := factory265.GetProtocol(mbTrans263)
		argvalue1 := compass_server.NewPromotion()
		err267 := argvalue1.Read(jsProt266)
		if err267 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "searchPromotionsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchPromotionsByParams requires 2 args")
			flag.Usage()
		}
		arg268 := flag.Arg(1)
		mbTrans269 := thrift.NewTMemoryBufferLen(len(arg268))
		defer mbTrans269.Close()
		_, err270 := mbTrans269.WriteString(arg268)
		if err270 != nil {
			Usage()
			return
		}
		factory271 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt272 := factory271.GetProtocol(mbTrans269)
		argvalue0 := compass_server.NewRequestHeader()
		err273 := argvalue0.Read(jsProt272)
		if err273 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg274 := flag.Arg(2)
		mbTrans275 := thrift.NewTMemoryBufferLen(len(arg274))
		defer mbTrans275.Close()
		_, err276 := mbTrans275.WriteString(arg274)
		if err276 != nil {
			Usage()
			return
		}
		factory277 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt278 := factory277.GetProtocol(mbTrans275)
		argvalue1 := compass_server.NewPromotionParams()
		err279 := argvalue1.Read(jsProt278)
		if err279 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchPromotionsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg280 := flag.Arg(1)
		mbTrans281 := thrift.NewTMemoryBufferLen(len(arg280))
		defer mbTrans281.Close()
		_, err282 := mbTrans281.WriteString(arg280)
		if err282 != nil {
			Usage()
			return
		}
		factory283 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt284 := factory283.GetProtocol(mbTrans281)
		argvalue0 := compass_server.NewRequestHeader()
		err285 := argvalue0.Read(jsProt284)
		if err285 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg286 := flag.Arg(2)
		mbTrans287 := thrift.NewTMemoryBufferLen(len(arg286))
		defer mbTrans287.Close()
		_, err288 := mbTrans287.WriteString(arg286)
		if err288 != nil {
			Usage()
			return
		}
		factory289 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt290 := factory289.GetProtocol(mbTrans287)
		containerStruct1 := compass_server.NewGetPromotionsByIdsArgs()
		err291 := containerStruct1.ReadField2(jsProt290)
		if err291 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddChannel requires 2 args")
			flag.Usage()
		}
		arg292 := flag.Arg(1)
		mbTrans293 := thrift.NewTMemoryBufferLen(len(arg292))
		defer mbTrans293.Close()
		_, err294 := mbTrans293.WriteString(arg292)
		if err294 != nil {
			Usage()
			return
		}
		factory295 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt296 := factory295.GetProtocol(mbTrans293)
		argvalue0 := compass_server.NewRequestHeader()
		err297 := argvalue0.Read(jsProt296)
		if err297 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg298 := flag.Arg(2)
		mbTrans299 := thrift.NewTMemoryBufferLen(len(arg298))
		defer mbTrans299.Close()
		_, err300 := mbTrans299.WriteString(arg298)
		if err300 != nil {
			Usage()
			return
		}
		factory301 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt302 := factory301.GetProtocol(mbTrans299)
		argvalue1 := compass_server.NewChannel()
		err303 := argvalue1.Read(jsProt302)
		if err303 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddChannel(value0, value1))
		fmt.Print("\n")
		break
	case "editChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditChannel requires 2 args")
			flag.Usage()
		}
		arg304 := flag.Arg(1)
		mbTrans305 := thrift.NewTMemoryBufferLen(len(arg304))
		defer mbTrans305.Close()
		_, err306 := mbTrans305.WriteString(arg304)
		if err306 != nil {
			Usage()
			return
		}
		factory307 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt308 := factory307.GetProtocol(mbTrans305)
		argvalue0 := compass_server.NewRequestHeader()
		err309 := argvalue0.Read(jsProt308)
		if err309 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg310 := flag.Arg(2)
		mbTrans311 := thrift.NewTMemoryBufferLen(len(arg310))
		defer mbTrans311.Close()
		_, err312 := mbTrans311.WriteString(arg310)
		if err312 != nil {
			Usage()
			return
		}
		factory313 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt314 := factory313.GetProtocol(mbTrans311)
		argvalue1 := compass_server.NewChannel()
		err315 := argvalue1.Read(jsProt314)
		if err315 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditChannel(value0, value1))
		fmt.Print("\n")
		break
	case "searchChannelsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchChannelsByParams requires 2 args")
			flag.Usage()
		}
		arg316 := flag.Arg(1)
		mbTrans317 := thrift.NewTMemoryBufferLen(len(arg316))
		defer mbTrans317.Close()
		_, err318 := mbTrans317.WriteString(arg316)
		if err318 != nil {
			Usage()
			return
		}
		factory319 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt320 := factory319.GetProtocol(mbTrans317)
		argvalue0 := compass_server.NewRequestHeader()
		err321 := argvalue0.Read(jsProt320)
		if err321 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg322 := flag.Arg(2)
		mbTrans323 := thrift.NewTMemoryBufferLen(len(arg322))
		defer mbTrans323.Close()
		_, err324 := mbTrans323.WriteString(arg322)
		if err324 != nil {
			Usage()
			return
		}
		factory325 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt326 := factory325.GetProtocol(mbTrans323)
		argvalue1 := compass_server.NewChannelParams()
		err327 := argvalue1.Read(jsProt326)
		if err327 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchChannelsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getChannelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetChannelsByIds requires 2 args")
			flag.Usage()
		}
		arg328 := flag.Arg(1)
		mbTrans329 := thrift.NewTMemoryBufferLen(len(arg328))
		defer mbTrans329.Close()
		_, err330 := mbTrans329.WriteString(arg328)
		if err330 != nil {
			Usage()
			return
		}
		factory331 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt332 := factory331.GetProtocol(mbTrans329)
		argvalue0 := compass_server.NewRequestHeader()
		err333 := argvalue0.Read(jsProt332)
		if err333 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg334 := flag.Arg(2)
		mbTrans335 := thrift.NewTMemoryBufferLen(len(arg334))
		defer mbTrans335.Close()
		_, err336 := mbTrans335.WriteString(arg334)
		if err336 != nil {
			Usage()
			return
		}
		factory337 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt338 := factory337.GetProtocol(mbTrans335)
		containerStruct1 := compass_server.NewGetChannelsByIdsArgs()
		err339 := containerStruct1.ReadField2(jsProt338)
		if err339 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetChannelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addProject":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddProject requires 2 args")
			flag.Usage()
		}
		arg340 := flag.Arg(1)
		mbTrans341 := thrift.NewTMemoryBufferLen(len(arg340))
		defer mbTrans341.Close()
		_, err342 := mbTrans341.WriteString(arg340)
		if err342 != nil {
			Usage()
			return
		}
		factory343 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt344 := factory343.GetProtocol(mbTrans341)
		argvalue0 := compass_server.NewRequestHeader()
		err345 := argvalue0.Read(jsProt344)
		if err345 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg346 := flag.Arg(2)
		mbTrans347 := thrift.NewTMemoryBufferLen(len(arg346))
		defer mbTrans347.Close()
		_, err348 := mbTrans347.WriteString(arg346)
		if err348 != nil {
			Usage()
			return
		}
		factory349 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt350 := factory349.GetProtocol(mbTrans347)
		argvalue1 := compass_server.NewProject()
		err351 := argvalue1.Read(jsProt350)
		if err351 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddProject(value0, value1))
		fmt.Print("\n")
		break
	case "editProject":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditProject requires 2 args")
			flag.Usage()
		}
		arg352 := flag.Arg(1)
		mbTrans353 := thrift.NewTMemoryBufferLen(len(arg352))
		defer mbTrans353.Close()
		_, err354 := mbTrans353.WriteString(arg352)
		if err354 != nil {
			Usage()
			return
		}
		factory355 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt356 := factory355.GetProtocol(mbTrans353)
		argvalue0 := compass_server.NewRequestHeader()
		err357 := argvalue0.Read(jsProt356)
		if err357 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg358 := flag.Arg(2)
		mbTrans359 := thrift.NewTMemoryBufferLen(len(arg358))
		defer mbTrans359.Close()
		_, err360 := mbTrans359.WriteString(arg358)
		if err360 != nil {
			Usage()
			return
		}
		factory361 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt362 := factory361.GetProtocol(mbTrans359)
		argvalue1 := compass_server.NewProject()
		err363 := argvalue1.Read(jsProt362)
		if err363 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditProject(value0, value1))
		fmt.Print("\n")
		break
	case "searchProjectsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchProjectsByParams requires 2 args")
			flag.Usage()
		}
		arg364 := flag.Arg(1)
		mbTrans365 := thrift.NewTMemoryBufferLen(len(arg364))
		defer mbTrans365.Close()
		_, err366 := mbTrans365.WriteString(arg364)
		if err366 != nil {
			Usage()
			return
		}
		factory367 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt368 := factory367.GetProtocol(mbTrans365)
		argvalue0 := compass_server.NewRequestHeader()
		err369 := argvalue0.Read(jsProt368)
		if err369 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg370 := flag.Arg(2)
		mbTrans371 := thrift.NewTMemoryBufferLen(len(arg370))
		defer mbTrans371.Close()
		_, err372 := mbTrans371.WriteString(arg370)
		if err372 != nil {
			Usage()
			return
		}
		factory373 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt374 := factory373.GetProtocol(mbTrans371)
		argvalue1 := compass_server.NewProjectParams()
		err375 := argvalue1.Read(jsProt374)
		if err375 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchProjectsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getProjectsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProjectsByIds requires 2 args")
			flag.Usage()
		}
		arg376 := flag.Arg(1)
		mbTrans377 := thrift.NewTMemoryBufferLen(len(arg376))
		defer mbTrans377.Close()
		_, err378 := mbTrans377.WriteString(arg376)
		if err378 != nil {
			Usage()
			return
		}
		factory379 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt380 := factory379.GetProtocol(mbTrans377)
		argvalue0 := compass_server.NewRequestHeader()
		err381 := argvalue0.Read(jsProt380)
		if err381 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg382 := flag.Arg(2)
		mbTrans383 := thrift.NewTMemoryBufferLen(len(arg382))
		defer mbTrans383.Close()
		_, err384 := mbTrans383.WriteString(arg382)
		if err384 != nil {
			Usage()
			return
		}
		factory385 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt386 := factory385.GetProtocol(mbTrans383)
		containerStruct1 := compass_server.NewGetProjectsByIdsArgs()
		err387 := containerStruct1.ReadField2(jsProt386)
		if err387 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetProjectsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCampaign requires 2 args")
			flag.Usage()
		}
		arg388 := flag.Arg(1)
		mbTrans389 := thrift.NewTMemoryBufferLen(len(arg388))
		defer mbTrans389.Close()
		_, err390 := mbTrans389.WriteString(arg388)
		if err390 != nil {
			Usage()
			return
		}
		factory391 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt392 := factory391.GetProtocol(mbTrans389)
		argvalue0 := compass_server.NewRequestHeader()
		err393 := argvalue0.Read(jsProt392)
		if err393 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg394 := flag.Arg(2)
		mbTrans395 := thrift.NewTMemoryBufferLen(len(arg394))
		defer mbTrans395.Close()
		_, err396 := mbTrans395.WriteString(arg394)
		if err396 != nil {
			Usage()
			return
		}
		factory397 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt398 := factory397.GetProtocol(mbTrans395)
		argvalue1 := compass_server.NewCampaign()
		err399 := argvalue1.Read(jsProt398)
		if err399 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "editCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCampaign requires 2 args")
			flag.Usage()
		}
		arg400 := flag.Arg(1)
		mbTrans401 := thrift.NewTMemoryBufferLen(len(arg400))
		defer mbTrans401.Close()
		_, err402 := mbTrans401.WriteString(arg400)
		if err402 != nil {
			Usage()
			return
		}
		factory403 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt404 := factory403.GetProtocol(mbTrans401)
		argvalue0 := compass_server.NewRequestHeader()
		err405 := argvalue0.Read(jsProt404)
		if err405 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg406 := flag.Arg(2)
		mbTrans407 := thrift.NewTMemoryBufferLen(len(arg406))
		defer mbTrans407.Close()
		_, err408 := mbTrans407.WriteString(arg406)
		if err408 != nil {
			Usage()
			return
		}
		factory409 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt410 := factory409.GetProtocol(mbTrans407)
		argvalue1 := compass_server.NewCampaign()
		err411 := argvalue1.Read(jsProt410)
		if err411 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "searchCampaignsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchCampaignsByParams requires 2 args")
			flag.Usage()
		}
		arg412 := flag.Arg(1)
		mbTrans413 := thrift.NewTMemoryBufferLen(len(arg412))
		defer mbTrans413.Close()
		_, err414 := mbTrans413.WriteString(arg412)
		if err414 != nil {
			Usage()
			return
		}
		factory415 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt416 := factory415.GetProtocol(mbTrans413)
		argvalue0 := compass_server.NewRequestHeader()
		err417 := argvalue0.Read(jsProt416)
		if err417 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg418 := flag.Arg(2)
		mbTrans419 := thrift.NewTMemoryBufferLen(len(arg418))
		defer mbTrans419.Close()
		_, err420 := mbTrans419.WriteString(arg418)
		if err420 != nil {
			Usage()
			return
		}
		factory421 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt422 := factory421.GetProtocol(mbTrans419)
		argvalue1 := compass_server.NewCampaignParams()
		err423 := argvalue1.Read(jsProt422)
		if err423 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchCampaignsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg424 := flag.Arg(1)
		mbTrans425 := thrift.NewTMemoryBufferLen(len(arg424))
		defer mbTrans425.Close()
		_, err426 := mbTrans425.WriteString(arg424)
		if err426 != nil {
			Usage()
			return
		}
		factory427 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt428 := factory427.GetProtocol(mbTrans425)
		argvalue0 := compass_server.NewRequestHeader()
		err429 := argvalue0.Read(jsProt428)
		if err429 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg430 := flag.Arg(2)
		mbTrans431 := thrift.NewTMemoryBufferLen(len(arg430))
		defer mbTrans431.Close()
		_, err432 := mbTrans431.WriteString(arg430)
		if err432 != nil {
			Usage()
			return
		}
		factory433 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt434 := factory433.GetProtocol(mbTrans431)
		containerStruct1 := compass_server.NewGetCampaignsByIdsArgs()
		err435 := containerStruct1.ReadField2(jsProt434)
		if err435 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddStrategy requires 2 args")
			flag.Usage()
		}
		arg436 := flag.Arg(1)
		mbTrans437 := thrift.NewTMemoryBufferLen(len(arg436))
		defer mbTrans437.Close()
		_, err438 := mbTrans437.WriteString(arg436)
		if err438 != nil {
			Usage()
			return
		}
		factory439 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt440 := factory439.GetProtocol(mbTrans437)
		argvalue0 := compass_server.NewRequestHeader()
		err441 := argvalue0.Read(jsProt440)
		if err441 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg442 := flag.Arg(2)
		mbTrans443 := thrift.NewTMemoryBufferLen(len(arg442))
		defer mbTrans443.Close()
		_, err444 := mbTrans443.WriteString(arg442)
		if err444 != nil {
			Usage()
			return
		}
		factory445 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt446 := factory445.GetProtocol(mbTrans443)
		argvalue1 := compass_server.NewStrategy()
		err447 := argvalue1.Read(jsProt446)
		if err447 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "editStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditStrategy requires 2 args")
			flag.Usage()
		}
		arg448 := flag.Arg(1)
		mbTrans449 := thrift.NewTMemoryBufferLen(len(arg448))
		defer mbTrans449.Close()
		_, err450 := mbTrans449.WriteString(arg448)
		if err450 != nil {
			Usage()
			return
		}
		factory451 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt452 := factory451.GetProtocol(mbTrans449)
		argvalue0 := compass_server.NewRequestHeader()
		err453 := argvalue0.Read(jsProt452)
		if err453 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg454 := flag.Arg(2)
		mbTrans455 := thrift.NewTMemoryBufferLen(len(arg454))
		defer mbTrans455.Close()
		_, err456 := mbTrans455.WriteString(arg454)
		if err456 != nil {
			Usage()
			return
		}
		factory457 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt458 := factory457.GetProtocol(mbTrans455)
		argvalue1 := compass_server.NewStrategy()
		err459 := argvalue1.Read(jsProt458)
		if err459 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "searchStrategysByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchStrategysByParams requires 2 args")
			flag.Usage()
		}
		arg460 := flag.Arg(1)
		mbTrans461 := thrift.NewTMemoryBufferLen(len(arg460))
		defer mbTrans461.Close()
		_, err462 := mbTrans461.WriteString(arg460)
		if err462 != nil {
			Usage()
			return
		}
		factory463 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt464 := factory463.GetProtocol(mbTrans461)
		argvalue0 := compass_server.NewRequestHeader()
		err465 := argvalue0.Read(jsProt464)
		if err465 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg466 := flag.Arg(2)
		mbTrans467 := thrift.NewTMemoryBufferLen(len(arg466))
		defer mbTrans467.Close()
		_, err468 := mbTrans467.WriteString(arg466)
		if err468 != nil {
			Usage()
			return
		}
		factory469 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt470 := factory469.GetProtocol(mbTrans467)
		argvalue1 := compass_server.NewStrategyParams()
		err471 := argvalue1.Read(jsProt470)
		if err471 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchStrategysByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getStrategysByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetStrategysByIds requires 2 args")
			flag.Usage()
		}
		arg472 := flag.Arg(1)
		mbTrans473 := thrift.NewTMemoryBufferLen(len(arg472))
		defer mbTrans473.Close()
		_, err474 := mbTrans473.WriteString(arg472)
		if err474 != nil {
			Usage()
			return
		}
		factory475 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt476 := factory475.GetProtocol(mbTrans473)
		argvalue0 := compass_server.NewRequestHeader()
		err477 := argvalue0.Read(jsProt476)
		if err477 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg478 := flag.Arg(2)
		mbTrans479 := thrift.NewTMemoryBufferLen(len(arg478))
		defer mbTrans479.Close()
		_, err480 := mbTrans479.WriteString(arg478)
		if err480 != nil {
			Usage()
			return
		}
		factory481 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt482 := factory481.GetProtocol(mbTrans479)
		containerStruct1 := compass_server.NewGetStrategysByIdsArgs()
		err483 := containerStruct1.ReadField2(jsProt482)
		if err483 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetStrategysByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreative requires 2 args")
			flag.Usage()
		}
		arg484 := flag.Arg(1)
		mbTrans485 := thrift.NewTMemoryBufferLen(len(arg484))
		defer mbTrans485.Close()
		_, err486 := mbTrans485.WriteString(arg484)
		if err486 != nil {
			Usage()
			return
		}
		factory487 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt488 := factory487.GetProtocol(mbTrans485)
		argvalue0 := compass_server.NewRequestHeader()
		err489 := argvalue0.Read(jsProt488)
		if err489 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg490 := flag.Arg(2)
		mbTrans491 := thrift.NewTMemoryBufferLen(len(arg490))
		defer mbTrans491.Close()
		_, err492 := mbTrans491.WriteString(arg490)
		if err492 != nil {
			Usage()
			return
		}
		factory493 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt494 := factory493.GetProtocol(mbTrans491)
		argvalue1 := compass_server.NewCreative()
		err495 := argvalue1.Read(jsProt494)
		if err495 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreative(value0, value1))
		fmt.Print("\n")
		break
	case "editCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCreative requires 2 args")
			flag.Usage()
		}
		arg496 := flag.Arg(1)
		mbTrans497 := thrift.NewTMemoryBufferLen(len(arg496))
		defer mbTrans497.Close()
		_, err498 := mbTrans497.WriteString(arg496)
		if err498 != nil {
			Usage()
			return
		}
		factory499 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt500 := factory499.GetProtocol(mbTrans497)
		argvalue0 := compass_server.NewRequestHeader()
		err501 := argvalue0.Read(jsProt500)
		if err501 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg502 := flag.Arg(2)
		mbTrans503 := thrift.NewTMemoryBufferLen(len(arg502))
		defer mbTrans503.Close()
		_, err504 := mbTrans503.WriteString(arg502)
		if err504 != nil {
			Usage()
			return
		}
		factory505 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt506 := factory505.GetProtocol(mbTrans503)
		argvalue1 := compass_server.NewCreative()
		err507 := argvalue1.Read(jsProt506)
		if err507 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCreative(value0, value1))
		fmt.Print("\n")
		break
	case "searchCreativesByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchCreativesByParams requires 2 args")
			flag.Usage()
		}
		arg508 := flag.Arg(1)
		mbTrans509 := thrift.NewTMemoryBufferLen(len(arg508))
		defer mbTrans509.Close()
		_, err510 := mbTrans509.WriteString(arg508)
		if err510 != nil {
			Usage()
			return
		}
		factory511 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt512 := factory511.GetProtocol(mbTrans509)
		argvalue0 := compass_server.NewRequestHeader()
		err513 := argvalue0.Read(jsProt512)
		if err513 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg514 := flag.Arg(2)
		mbTrans515 := thrift.NewTMemoryBufferLen(len(arg514))
		defer mbTrans515.Close()
		_, err516 := mbTrans515.WriteString(arg514)
		if err516 != nil {
			Usage()
			return
		}
		factory517 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt518 := factory517.GetProtocol(mbTrans515)
		argvalue1 := compass_server.NewCreativeParams()
		err519 := argvalue1.Read(jsProt518)
		if err519 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchCreativesByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg520 := flag.Arg(1)
		mbTrans521 := thrift.NewTMemoryBufferLen(len(arg520))
		defer mbTrans521.Close()
		_, err522 := mbTrans521.WriteString(arg520)
		if err522 != nil {
			Usage()
			return
		}
		factory523 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt524 := factory523.GetProtocol(mbTrans521)
		argvalue0 := compass_server.NewRequestHeader()
		err525 := argvalue0.Read(jsProt524)
		if err525 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg526 := flag.Arg(2)
		mbTrans527 := thrift.NewTMemoryBufferLen(len(arg526))
		defer mbTrans527.Close()
		_, err528 := mbTrans527.WriteString(arg526)
		if err528 != nil {
			Usage()
			return
		}
		factory529 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt530 := factory529.GetProtocol(mbTrans527)
		containerStruct1 := compass_server.NewGetCreativesByIdsArgs()
		err531 := containerStruct1.ReadField2(jsProt530)
		if err531 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addLifeCycleGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddLifeCycleGroup requires 2 args")
			flag.Usage()
		}
		arg532 := flag.Arg(1)
		mbTrans533 := thrift.NewTMemoryBufferLen(len(arg532))
		defer mbTrans533.Close()
		_, err534 := mbTrans533.WriteString(arg532)
		if err534 != nil {
			Usage()
			return
		}
		factory535 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt536 := factory535.GetProtocol(mbTrans533)
		argvalue0 := compass_server.NewRequestHeader()
		err537 := argvalue0.Read(jsProt536)
		if err537 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg538 := flag.Arg(2)
		mbTrans539 := thrift.NewTMemoryBufferLen(len(arg538))
		defer mbTrans539.Close()
		_, err540 := mbTrans539.WriteString(arg538)
		if err540 != nil {
			Usage()
			return
		}
		factory541 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt542 := factory541.GetProtocol(mbTrans539)
		argvalue1 := compass_server.NewLifeCycleGroup()
		err543 := argvalue1.Read(jsProt542)
		if err543 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddLifeCycleGroup(value0, value1))
		fmt.Print("\n")
		break
	case "editLifeCycleGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditLifeCycleGroup requires 2 args")
			flag.Usage()
		}
		arg544 := flag.Arg(1)
		mbTrans545 := thrift.NewTMemoryBufferLen(len(arg544))
		defer mbTrans545.Close()
		_, err546 := mbTrans545.WriteString(arg544)
		if err546 != nil {
			Usage()
			return
		}
		factory547 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt548 := factory547.GetProtocol(mbTrans545)
		argvalue0 := compass_server.NewRequestHeader()
		err549 := argvalue0.Read(jsProt548)
		if err549 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg550 := flag.Arg(2)
		mbTrans551 := thrift.NewTMemoryBufferLen(len(arg550))
		defer mbTrans551.Close()
		_, err552 := mbTrans551.WriteString(arg550)
		if err552 != nil {
			Usage()
			return
		}
		factory553 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt554 := factory553.GetProtocol(mbTrans551)
		argvalue1 := compass_server.NewLifeCycleGroup()
		err555 := argvalue1.Read(jsProt554)
		if err555 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditLifeCycleGroup(value0, value1))
		fmt.Print("\n")
		break
	case "searchLifeCycleGroupsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchLifeCycleGroupsByParams requires 2 args")
			flag.Usage()
		}
		arg556 := flag.Arg(1)
		mbTrans557 := thrift.NewTMemoryBufferLen(len(arg556))
		defer mbTrans557.Close()
		_, err558 := mbTrans557.WriteString(arg556)
		if err558 != nil {
			Usage()
			return
		}
		factory559 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt560 := factory559.GetProtocol(mbTrans557)
		argvalue0 := compass_server.NewRequestHeader()
		err561 := argvalue0.Read(jsProt560)
		if err561 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg562 := flag.Arg(2)
		mbTrans563 := thrift.NewTMemoryBufferLen(len(arg562))
		defer mbTrans563.Close()
		_, err564 := mbTrans563.WriteString(arg562)
		if err564 != nil {
			Usage()
			return
		}
		factory565 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt566 := factory565.GetProtocol(mbTrans563)
		argvalue1 := compass_server.NewLifeCycleGroupParams()
		err567 := argvalue1.Read(jsProt566)
		if err567 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchLifeCycleGroupsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getLifeCycleGroupsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLifeCycleGroupsByIds requires 2 args")
			flag.Usage()
		}
		arg568 := flag.Arg(1)
		mbTrans569 := thrift.NewTMemoryBufferLen(len(arg568))
		defer mbTrans569.Close()
		_, err570 := mbTrans569.WriteString(arg568)
		if err570 != nil {
			Usage()
			return
		}
		factory571 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt572 := factory571.GetProtocol(mbTrans569)
		argvalue0 := compass_server.NewRequestHeader()
		err573 := argvalue0.Read(jsProt572)
		if err573 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg574 := flag.Arg(2)
		mbTrans575 := thrift.NewTMemoryBufferLen(len(arg574))
		defer mbTrans575.Close()
		_, err576 := mbTrans575.WriteString(arg574)
		if err576 != nil {
			Usage()
			return
		}
		factory577 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt578 := factory577.GetProtocol(mbTrans575)
		containerStruct1 := compass_server.NewGetLifeCycleGroupsByIdsArgs()
		err579 := containerStruct1.ReadField2(jsProt578)
		if err579 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetLifeCycleGroupsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteLifeCycleGroupsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteLifeCycleGroupsByIds requires 3 args")
			flag.Usage()
		}
		arg580 := flag.Arg(1)
		mbTrans581 := thrift.NewTMemoryBufferLen(len(arg580))
		defer mbTrans581.Close()
		_, err582 := mbTrans581.WriteString(arg580)
		if err582 != nil {
			Usage()
			return
		}
		factory583 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt584 := factory583.GetProtocol(mbTrans581)
		argvalue0 := compass_server.NewRequestHeader()
		err585 := argvalue0.Read(jsProt584)
		if err585 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err586 := (strconv.Atoi(flag.Arg(2)))
		if err586 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg587 := flag.Arg(3)
		mbTrans588 := thrift.NewTMemoryBufferLen(len(arg587))
		defer mbTrans588.Close()
		_, err589 := mbTrans588.WriteString(arg587)
		if err589 != nil {
			Usage()
			return
		}
		factory590 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt591 := factory590.GetProtocol(mbTrans588)
		containerStruct2 := compass_server.NewDeleteLifeCycleGroupsByIdsArgs()
		err592 := containerStruct2.ReadField3(jsProt591)
		if err592 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteLifeCycleGroupsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addCrowd":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCrowd requires 2 args")
			flag.Usage()
		}
		arg593 := flag.Arg(1)
		mbTrans594 := thrift.NewTMemoryBufferLen(len(arg593))
		defer mbTrans594.Close()
		_, err595 := mbTrans594.WriteString(arg593)
		if err595 != nil {
			Usage()
			return
		}
		factory596 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt597 := factory596.GetProtocol(mbTrans594)
		argvalue0 := compass_server.NewRequestHeader()
		err598 := argvalue0.Read(jsProt597)
		if err598 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg599 := flag.Arg(2)
		mbTrans600 := thrift.NewTMemoryBufferLen(len(arg599))
		defer mbTrans600.Close()
		_, err601 := mbTrans600.WriteString(arg599)
		if err601 != nil {
			Usage()
			return
		}
		factory602 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt603 := factory602.GetProtocol(mbTrans600)
		argvalue1 := compass_server.NewCrowd()
		err604 := argvalue1.Read(jsProt603)
		if err604 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCrowd(value0, value1))
		fmt.Print("\n")
		break
	case "editCrowd":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCrowd requires 2 args")
			flag.Usage()
		}
		arg605 := flag.Arg(1)
		mbTrans606 := thrift.NewTMemoryBufferLen(len(arg605))
		defer mbTrans606.Close()
		_, err607 := mbTrans606.WriteString(arg605)
		if err607 != nil {
			Usage()
			return
		}
		factory608 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt609 := factory608.GetProtocol(mbTrans606)
		argvalue0 := compass_server.NewRequestHeader()
		err610 := argvalue0.Read(jsProt609)
		if err610 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg611 := flag.Arg(2)
		mbTrans612 := thrift.NewTMemoryBufferLen(len(arg611))
		defer mbTrans612.Close()
		_, err613 := mbTrans612.WriteString(arg611)
		if err613 != nil {
			Usage()
			return
		}
		factory614 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt615 := factory614.GetProtocol(mbTrans612)
		argvalue1 := compass_server.NewCrowd()
		err616 := argvalue1.Read(jsProt615)
		if err616 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCrowd(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCrowdsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteCrowdsByIds requires 3 args")
			flag.Usage()
		}
		arg617 := flag.Arg(1)
		mbTrans618 := thrift.NewTMemoryBufferLen(len(arg617))
		defer mbTrans618.Close()
		_, err619 := mbTrans618.WriteString(arg617)
		if err619 != nil {
			Usage()
			return
		}
		factory620 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt621 := factory620.GetProtocol(mbTrans618)
		argvalue0 := compass_server.NewRequestHeader()
		err622 := argvalue0.Read(jsProt621)
		if err622 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err623 := (strconv.Atoi(flag.Arg(2)))
		if err623 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg624 := flag.Arg(3)
		mbTrans625 := thrift.NewTMemoryBufferLen(len(arg624))
		defer mbTrans625.Close()
		_, err626 := mbTrans625.WriteString(arg624)
		if err626 != nil {
			Usage()
			return
		}
		factory627 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt628 := factory627.GetProtocol(mbTrans625)
		containerStruct2 := compass_server.NewDeleteCrowdsByIdsArgs()
		err629 := containerStruct2.ReadField3(jsProt628)
		if err629 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteCrowdsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchCrowdsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchCrowdsByParams requires 2 args")
			flag.Usage()
		}
		arg630 := flag.Arg(1)
		mbTrans631 := thrift.NewTMemoryBufferLen(len(arg630))
		defer mbTrans631.Close()
		_, err632 := mbTrans631.WriteString(arg630)
		if err632 != nil {
			Usage()
			return
		}
		factory633 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt634 := factory633.GetProtocol(mbTrans631)
		argvalue0 := compass_server.NewRequestHeader()
		err635 := argvalue0.Read(jsProt634)
		if err635 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg636 := flag.Arg(2)
		mbTrans637 := thrift.NewTMemoryBufferLen(len(arg636))
		defer mbTrans637.Close()
		_, err638 := mbTrans637.WriteString(arg636)
		if err638 != nil {
			Usage()
			return
		}
		factory639 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt640 := factory639.GetProtocol(mbTrans637)
		argvalue1 := compass_server.NewCrowdParams()
		err641 := argvalue1.Read(jsProt640)
		if err641 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchCrowdsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getCrowdsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCrowdsByIds requires 2 args")
			flag.Usage()
		}
		arg642 := flag.Arg(1)
		mbTrans643 := thrift.NewTMemoryBufferLen(len(arg642))
		defer mbTrans643.Close()
		_, err644 := mbTrans643.WriteString(arg642)
		if err644 != nil {
			Usage()
			return
		}
		factory645 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt646 := factory645.GetProtocol(mbTrans643)
		argvalue0 := compass_server.NewRequestHeader()
		err647 := argvalue0.Read(jsProt646)
		if err647 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg648 := flag.Arg(2)
		mbTrans649 := thrift.NewTMemoryBufferLen(len(arg648))
		defer mbTrans649.Close()
		_, err650 := mbTrans649.WriteString(arg648)
		if err650 != nil {
			Usage()
			return
		}
		factory651 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt652 := factory651.GetProtocol(mbTrans649)
		containerStruct1 := compass_server.NewGetCrowdsByIdsArgs()
		err653 := containerStruct1.ReadField2(jsProt652)
		if err653 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCrowdsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addLifeCycle":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddLifeCycle requires 2 args")
			flag.Usage()
		}
		arg654 := flag.Arg(1)
		mbTrans655 := thrift.NewTMemoryBufferLen(len(arg654))
		defer mbTrans655.Close()
		_, err656 := mbTrans655.WriteString(arg654)
		if err656 != nil {
			Usage()
			return
		}
		factory657 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt658 := factory657.GetProtocol(mbTrans655)
		argvalue0 := compass_server.NewRequestHeader()
		err659 := argvalue0.Read(jsProt658)
		if err659 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg660 := flag.Arg(2)
		mbTrans661 := thrift.NewTMemoryBufferLen(len(arg660))
		defer mbTrans661.Close()
		_, err662 := mbTrans661.WriteString(arg660)
		if err662 != nil {
			Usage()
			return
		}
		factory663 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt664 := factory663.GetProtocol(mbTrans661)
		argvalue1 := compass_server.NewLifeCycle()
		err665 := argvalue1.Read(jsProt664)
		if err665 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddLifeCycle(value0, value1))
		fmt.Print("\n")
		break
	case "editLifeCycle":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditLifeCycle requires 2 args")
			flag.Usage()
		}
		arg666 := flag.Arg(1)
		mbTrans667 := thrift.NewTMemoryBufferLen(len(arg666))
		defer mbTrans667.Close()
		_, err668 := mbTrans667.WriteString(arg666)
		if err668 != nil {
			Usage()
			return
		}
		factory669 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt670 := factory669.GetProtocol(mbTrans667)
		argvalue0 := compass_server.NewRequestHeader()
		err671 := argvalue0.Read(jsProt670)
		if err671 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg672 := flag.Arg(2)
		mbTrans673 := thrift.NewTMemoryBufferLen(len(arg672))
		defer mbTrans673.Close()
		_, err674 := mbTrans673.WriteString(arg672)
		if err674 != nil {
			Usage()
			return
		}
		factory675 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt676 := factory675.GetProtocol(mbTrans673)
		argvalue1 := compass_server.NewLifeCycle()
		err677 := argvalue1.Read(jsProt676)
		if err677 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditLifeCycle(value0, value1))
		fmt.Print("\n")
		break
	case "searchLifeCyclesByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchLifeCyclesByParams requires 2 args")
			flag.Usage()
		}
		arg678 := flag.Arg(1)
		mbTrans679 := thrift.NewTMemoryBufferLen(len(arg678))
		defer mbTrans679.Close()
		_, err680 := mbTrans679.WriteString(arg678)
		if err680 != nil {
			Usage()
			return
		}
		factory681 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt682 := factory681.GetProtocol(mbTrans679)
		argvalue0 := compass_server.NewRequestHeader()
		err683 := argvalue0.Read(jsProt682)
		if err683 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg684 := flag.Arg(2)
		mbTrans685 := thrift.NewTMemoryBufferLen(len(arg684))
		defer mbTrans685.Close()
		_, err686 := mbTrans685.WriteString(arg684)
		if err686 != nil {
			Usage()
			return
		}
		factory687 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt688 := factory687.GetProtocol(mbTrans685)
		argvalue1 := compass_server.NewLifeCycleParams()
		err689 := argvalue1.Read(jsProt688)
		if err689 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchLifeCyclesByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getLifeCyclesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLifeCyclesByIds requires 2 args")
			flag.Usage()
		}
		arg690 := flag.Arg(1)
		mbTrans691 := thrift.NewTMemoryBufferLen(len(arg690))
		defer mbTrans691.Close()
		_, err692 := mbTrans691.WriteString(arg690)
		if err692 != nil {
			Usage()
			return
		}
		factory693 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt694 := factory693.GetProtocol(mbTrans691)
		argvalue0 := compass_server.NewRequestHeader()
		err695 := argvalue0.Read(jsProt694)
		if err695 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg696 := flag.Arg(2)
		mbTrans697 := thrift.NewTMemoryBufferLen(len(arg696))
		defer mbTrans697.Close()
		_, err698 := mbTrans697.WriteString(arg696)
		if err698 != nil {
			Usage()
			return
		}
		factory699 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt700 := factory699.GetProtocol(mbTrans697)
		containerStruct1 := compass_server.NewGetLifeCyclesByIdsArgs()
		err701 := containerStruct1.ReadField2(jsProt700)
		if err701 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetLifeCyclesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteLifeCyclesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteLifeCyclesByIds requires 3 args")
			flag.Usage()
		}
		arg702 := flag.Arg(1)
		mbTrans703 := thrift.NewTMemoryBufferLen(len(arg702))
		defer mbTrans703.Close()
		_, err704 := mbTrans703.WriteString(arg702)
		if err704 != nil {
			Usage()
			return
		}
		factory705 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt706 := factory705.GetProtocol(mbTrans703)
		argvalue0 := compass_server.NewRequestHeader()
		err707 := argvalue0.Read(jsProt706)
		if err707 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err708 := (strconv.Atoi(flag.Arg(2)))
		if err708 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg709 := flag.Arg(3)
		mbTrans710 := thrift.NewTMemoryBufferLen(len(arg709))
		defer mbTrans710.Close()
		_, err711 := mbTrans710.WriteString(arg709)
		if err711 != nil {
			Usage()
			return
		}
		factory712 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt713 := factory712.GetProtocol(mbTrans710)
		containerStruct2 := compass_server.NewDeleteLifeCyclesByIdsArgs()
		err714 := containerStruct2.ReadField3(jsProt713)
		if err714 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteLifeCyclesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAppLaunch":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAppLaunch requires 2 args")
			flag.Usage()
		}
		arg715 := flag.Arg(1)
		mbTrans716 := thrift.NewTMemoryBufferLen(len(arg715))
		defer mbTrans716.Close()
		_, err717 := mbTrans716.WriteString(arg715)
		if err717 != nil {
			Usage()
			return
		}
		factory718 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt719 := factory718.GetProtocol(mbTrans716)
		argvalue0 := compass_server.NewRequestHeader()
		err720 := argvalue0.Read(jsProt719)
		if err720 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg721 := flag.Arg(2)
		mbTrans722 := thrift.NewTMemoryBufferLen(len(arg721))
		defer mbTrans722.Close()
		_, err723 := mbTrans722.WriteString(arg721)
		if err723 != nil {
			Usage()
			return
		}
		factory724 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt725 := factory724.GetProtocol(mbTrans722)
		argvalue1 := compass_server.NewAppLaunch()
		err726 := argvalue1.Read(jsProt725)
		if err726 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAppLaunch(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAppLaunchsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAppLaunchsByIds requires 3 args")
			flag.Usage()
		}
		arg727 := flag.Arg(1)
		mbTrans728 := thrift.NewTMemoryBufferLen(len(arg727))
		defer mbTrans728.Close()
		_, err729 := mbTrans728.WriteString(arg727)
		if err729 != nil {
			Usage()
			return
		}
		factory730 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt731 := factory730.GetProtocol(mbTrans728)
		argvalue0 := compass_server.NewRequestHeader()
		err732 := argvalue0.Read(jsProt731)
		if err732 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err733 := (strconv.Atoi(flag.Arg(2)))
		if err733 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg734 := flag.Arg(3)
		mbTrans735 := thrift.NewTMemoryBufferLen(len(arg734))
		defer mbTrans735.Close()
		_, err736 := mbTrans735.WriteString(arg734)
		if err736 != nil {
			Usage()
			return
		}
		factory737 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt738 := factory737.GetProtocol(mbTrans735)
		containerStruct2 := compass_server.NewDeleteAppLaunchsByIdsArgs()
		err739 := containerStruct2.ReadField3(jsProt738)
		if err739 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAppLaunchsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchAppLaunchsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAppLaunchsByParams requires 2 args")
			flag.Usage()
		}
		arg740 := flag.Arg(1)
		mbTrans741 := thrift.NewTMemoryBufferLen(len(arg740))
		defer mbTrans741.Close()
		_, err742 := mbTrans741.WriteString(arg740)
		if err742 != nil {
			Usage()
			return
		}
		factory743 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt744 := factory743.GetProtocol(mbTrans741)
		argvalue0 := compass_server.NewRequestHeader()
		err745 := argvalue0.Read(jsProt744)
		if err745 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg746 := flag.Arg(2)
		mbTrans747 := thrift.NewTMemoryBufferLen(len(arg746))
		defer mbTrans747.Close()
		_, err748 := mbTrans747.WriteString(arg746)
		if err748 != nil {
			Usage()
			return
		}
		factory749 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt750 := factory749.GetProtocol(mbTrans747)
		argvalue1 := compass_server.NewAppLaunchParams()
		err751 := argvalue1.Read(jsProt750)
		if err751 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAppLaunchsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getAppLaunchsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppLaunchsByIds requires 2 args")
			flag.Usage()
		}
		arg752 := flag.Arg(1)
		mbTrans753 := thrift.NewTMemoryBufferLen(len(arg752))
		defer mbTrans753.Close()
		_, err754 := mbTrans753.WriteString(arg752)
		if err754 != nil {
			Usage()
			return
		}
		factory755 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt756 := factory755.GetProtocol(mbTrans753)
		argvalue0 := compass_server.NewRequestHeader()
		err757 := argvalue0.Read(jsProt756)
		if err757 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg758 := flag.Arg(2)
		mbTrans759 := thrift.NewTMemoryBufferLen(len(arg758))
		defer mbTrans759.Close()
		_, err760 := mbTrans759.WriteString(arg758)
		if err760 != nil {
			Usage()
			return
		}
		factory761 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt762 := factory761.GetProtocol(mbTrans759)
		containerStruct1 := compass_server.NewGetAppLaunchsByIdsArgs()
		err763 := containerStruct1.ReadField2(jsProt762)
		if err763 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAppLaunchsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getInsightSchedules":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetInsightSchedules requires 3 args")
			flag.Usage()
		}
		arg764 := flag.Arg(1)
		mbTrans765 := thrift.NewTMemoryBufferLen(len(arg764))
		defer mbTrans765.Close()
		_, err766 := mbTrans765.WriteString(arg764)
		if err766 != nil {
			Usage()
			return
		}
		factory767 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt768 := factory767.GetProtocol(mbTrans765)
		argvalue0 := compass_server.NewRequestHeader()
		err769 := argvalue0.Read(jsProt768)
		if err769 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := compass_server.CompassCategory(tmp1)
		value1 := argvalue1
		arg770 := flag.Arg(3)
		mbTrans771 := thrift.NewTMemoryBufferLen(len(arg770))
		defer mbTrans771.Close()
		_, err772 := mbTrans771.WriteString(arg770)
		if err772 != nil {
			Usage()
			return
		}
		factory773 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt774 := factory773.GetProtocol(mbTrans771)
		containerStruct2 := compass_server.NewGetInsightSchedulesArgs()
		err775 := containerStruct2.ReadField3(jsProt774)
		if err775 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetInsightSchedules(value0, value1, value2))
		fmt.Print("\n")
		break
	case "reloadUv":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ReloadUv requires 4 args")
			flag.Usage()
		}
		arg776 := flag.Arg(1)
		mbTrans777 := thrift.NewTMemoryBufferLen(len(arg776))
		defer mbTrans777.Close()
		_, err778 := mbTrans777.WriteString(arg776)
		if err778 != nil {
			Usage()
			return
		}
		factory779 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt780 := factory779.GetProtocol(mbTrans777)
		argvalue0 := compass_server.NewRequestHeader()
		err781 := argvalue0.Read(jsProt780)
		if err781 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err782 := (strconv.Atoi(flag.Arg(2)))
		if err782 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := compass_server.CompassCategory(tmp2)
		value2 := argvalue2
		arg783 := flag.Arg(4)
		mbTrans784 := thrift.NewTMemoryBufferLen(len(arg783))
		defer mbTrans784.Close()
		_, err785 := mbTrans784.WriteString(arg783)
		if err785 != nil {
			Usage()
			return
		}
		factory786 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt787 := factory786.GetProtocol(mbTrans784)
		containerStruct3 := compass_server.NewReloadUvArgs()
		err788 := containerStruct3.ReadField4(jsProt787)
		if err788 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.ReloadUv(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "reloadUvOfLifeCycles":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ReloadUvOfLifeCycles requires 3 args")
			flag.Usage()
		}
		arg789 := flag.Arg(1)
		mbTrans790 := thrift.NewTMemoryBufferLen(len(arg789))
		defer mbTrans790.Close()
		_, err791 := mbTrans790.WriteString(arg789)
		if err791 != nil {
			Usage()
			return
		}
		factory792 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt793 := factory792.GetProtocol(mbTrans790)
		argvalue0 := compass_server.NewRequestHeader()
		err794 := argvalue0.Read(jsProt793)
		if err794 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err795 := (strconv.Atoi(flag.Arg(2)))
		if err795 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err796 := (strconv.Atoi(flag.Arg(3)))
		if err796 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.ReloadUvOfLifeCycles(value0, value1, value2))
		fmt.Print("\n")
		break
	case "insight":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "Insight requires 5 args")
			flag.Usage()
		}
		arg797 := flag.Arg(1)
		mbTrans798 := thrift.NewTMemoryBufferLen(len(arg797))
		defer mbTrans798.Close()
		_, err799 := mbTrans798.WriteString(arg797)
		if err799 != nil {
			Usage()
			return
		}
		factory800 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt801 := factory800.GetProtocol(mbTrans798)
		argvalue0 := compass_server.NewRequestHeader()
		err802 := argvalue0.Read(jsProt801)
		if err802 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err803 := (strconv.Atoi(flag.Arg(2)))
		if err803 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := compass_server.CompassCategory(tmp2)
		value2 := argvalue2
		arg804 := flag.Arg(4)
		mbTrans805 := thrift.NewTMemoryBufferLen(len(arg804))
		defer mbTrans805.Close()
		_, err806 := mbTrans805.WriteString(arg804)
		if err806 != nil {
			Usage()
			return
		}
		factory807 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt808 := factory807.GetProtocol(mbTrans805)
		containerStruct3 := compass_server.NewInsightArgs()
		err809 := containerStruct3.ReadField4(jsProt808)
		if err809 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Platforms
		value3 := argvalue3
		arg810 := flag.Arg(5)
		mbTrans811 := thrift.NewTMemoryBufferLen(len(arg810))
		defer mbTrans811.Close()
		_, err812 := mbTrans811.WriteString(arg810)
		if err812 != nil {
			Usage()
			return
		}
		factory813 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt814 := factory813.GetProtocol(mbTrans811)
		containerStruct4 := compass_server.NewInsightArgs()
		err815 := containerStruct4.ReadField5(jsProt814)
		if err815 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.Insight(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "push":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "Push requires 5 args")
			flag.Usage()
		}
		arg816 := flag.Arg(1)
		mbTrans817 := thrift.NewTMemoryBufferLen(len(arg816))
		defer mbTrans817.Close()
		_, err818 := mbTrans817.WriteString(arg816)
		if err818 != nil {
			Usage()
			return
		}
		factory819 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt820 := factory819.GetProtocol(mbTrans817)
		argvalue0 := compass_server.NewRequestHeader()
		err821 := argvalue0.Read(jsProt820)
		if err821 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err822 := (strconv.Atoi(flag.Arg(2)))
		if err822 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg823 := flag.Arg(3)
		mbTrans824 := thrift.NewTMemoryBufferLen(len(arg823))
		defer mbTrans824.Close()
		_, err825 := mbTrans824.WriteString(arg823)
		if err825 != nil {
			Usage()
			return
		}
		factory826 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt827 := factory826.GetProtocol(mbTrans824)
		containerStruct2 := compass_server.NewPushArgs()
		err828 := containerStruct2.ReadField3(jsProt827)
		if err828 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Platforms
		value2 := argvalue2
		tmp3, err829 := (strconv.Atoi(flag.Arg(4)))
		if err829 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg830 := flag.Arg(5)
		mbTrans831 := thrift.NewTMemoryBufferLen(len(arg830))
		defer mbTrans831.Close()
		_, err832 := mbTrans831.WriteString(arg830)
		if err832 != nil {
			Usage()
			return
		}
		factory833 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt834 := factory833.GetProtocol(mbTrans831)
		containerStruct4 := compass_server.NewPushArgs()
		err835 := containerStruct4.ReadField5(jsProt834)
		if err835 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.Push(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "sendEvent":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "SendEvent requires 6 args")
			flag.Usage()
		}
		arg836 := flag.Arg(1)
		mbTrans837 := thrift.NewTMemoryBufferLen(len(arg836))
		defer mbTrans837.Close()
		_, err838 := mbTrans837.WriteString(arg836)
		if err838 != nil {
			Usage()
			return
		}
		factory839 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt840 := factory839.GetProtocol(mbTrans837)
		argvalue0 := compass_server.NewRequestHeader()
		err841 := argvalue0.Read(jsProt840)
		if err841 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := compass_server.CompassEventCategory(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := compass_server.CompassEventType(tmp2)
		value2 := argvalue2
		tmp3, err842 := (strconv.Atoi(flag.Arg(4)))
		if err842 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg843 := flag.Arg(5)
		mbTrans844 := thrift.NewTMemoryBufferLen(len(arg843))
		defer mbTrans844.Close()
		_, err845 := mbTrans844.WriteString(arg843)
		if err845 != nil {
			Usage()
			return
		}
		factory846 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt847 := factory846.GetProtocol(mbTrans844)
		containerStruct4 := compass_server.NewSendEventArgs()
		err848 := containerStruct4.ReadField5(jsProt847)
		if err848 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		arg849 := flag.Arg(6)
		mbTrans850 := thrift.NewTMemoryBufferLen(len(arg849))
		defer mbTrans850.Close()
		_, err851 := mbTrans850.WriteString(arg849)
		if err851 != nil {
			Usage()
			return
		}
		factory852 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt853 := factory852.GetProtocol(mbTrans850)
		containerStruct5 := compass_server.NewSendEventArgs()
		err854 := containerStruct5.ReadField6(jsProt853)
		if err854 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Platforms
		value5 := argvalue5
		fmt.Print(client.SendEvent(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
