// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package compass_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/compass_event"
	"rtb_model_server/common/domob_thrift/compass_exception"
	"rtb_model_server/common/domob_thrift/compass_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = compass_types.GoUnusedProtection__
var _ = compass_event.GoUnusedProtection__
var _ = compass_exception.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int
