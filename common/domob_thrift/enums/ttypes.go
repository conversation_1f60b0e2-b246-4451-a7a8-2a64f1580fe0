// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package enums

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//用户角色
type UserRole int64

const (
	UserRole_USER_ROLE_UNKNOWN              UserRole = 0
	UserRole_USER_ROLE_ADER                 UserRole = 1
	UserRole_USER_ROLE_DEVER                UserRole = 2
	UserRole_USER_ROLE_ADER_AND_DEVER       UserRole = 3
	UserRole_USER_ROLE_TRAFFIC_EXCHANGE     UserRole = 4
	UserRole_USER_ROLE_AGENT                UserRole = 5
	UserRole_USER_ROLE_AGENT_ADER           UserRole = 6
	UserRole_USER_ROLE_3P_CHANNEL           UserRole = 7
	UserRole_USER_ROLE_API_AGENT            UserRole = 15
	UserRole_USER_ROLE_API_AGENT_ADER       UserRole = 16
	UserRole_USER_ROLE_DSP_ADER             UserRole = 21
	UserRole_USER_ROLE_DSP_AGENT            UserRole = 25
	UserRole_USER_ROLE_DSP_AGENT_ADER       UserRole = 26
	UserRole_USER_ROLE_ADX_AGENT            UserRole = 27
	UserRole_USER_ROLE_ADX_AGENT_ADER       UserRole = 28
	UserRole_USER_ROLE_DSP_NEW_AGENT        UserRole = 29
	UserRole_USER_ROLE_DSP_NEW_AGENT_ADER   UserRole = 30
	UserRole_USER_ROLE_BIDMASTER_AGENT      UserRole = 40
	UserRole_USER_ROLE_BIDMASTER_AGENT_ADER UserRole = 41
	UserRole_USER_ROLE_BIDMASTER_ADER       UserRole = 42
)

func (p UserRole) String() string {
	switch p {
	case UserRole_USER_ROLE_UNKNOWN:
		return "UserRole_USER_ROLE_UNKNOWN"
	case UserRole_USER_ROLE_ADER:
		return "UserRole_USER_ROLE_ADER"
	case UserRole_USER_ROLE_DEVER:
		return "UserRole_USER_ROLE_DEVER"
	case UserRole_USER_ROLE_ADER_AND_DEVER:
		return "UserRole_USER_ROLE_ADER_AND_DEVER"
	case UserRole_USER_ROLE_TRAFFIC_EXCHANGE:
		return "UserRole_USER_ROLE_TRAFFIC_EXCHANGE"
	case UserRole_USER_ROLE_AGENT:
		return "UserRole_USER_ROLE_AGENT"
	case UserRole_USER_ROLE_AGENT_ADER:
		return "UserRole_USER_ROLE_AGENT_ADER"
	case UserRole_USER_ROLE_3P_CHANNEL:
		return "UserRole_USER_ROLE_3P_CHANNEL"
	case UserRole_USER_ROLE_API_AGENT:
		return "UserRole_USER_ROLE_API_AGENT"
	case UserRole_USER_ROLE_API_AGENT_ADER:
		return "UserRole_USER_ROLE_API_AGENT_ADER"
	case UserRole_USER_ROLE_DSP_ADER:
		return "UserRole_USER_ROLE_DSP_ADER"
	case UserRole_USER_ROLE_DSP_AGENT:
		return "UserRole_USER_ROLE_DSP_AGENT"
	case UserRole_USER_ROLE_DSP_AGENT_ADER:
		return "UserRole_USER_ROLE_DSP_AGENT_ADER"
	case UserRole_USER_ROLE_ADX_AGENT:
		return "UserRole_USER_ROLE_ADX_AGENT"
	case UserRole_USER_ROLE_ADX_AGENT_ADER:
		return "UserRole_USER_ROLE_ADX_AGENT_ADER"
	case UserRole_USER_ROLE_DSP_NEW_AGENT:
		return "UserRole_USER_ROLE_DSP_NEW_AGENT"
	case UserRole_USER_ROLE_DSP_NEW_AGENT_ADER:
		return "UserRole_USER_ROLE_DSP_NEW_AGENT_ADER"
	case UserRole_USER_ROLE_BIDMASTER_AGENT:
		return "UserRole_USER_ROLE_BIDMASTER_AGENT"
	case UserRole_USER_ROLE_BIDMASTER_AGENT_ADER:
		return "UserRole_USER_ROLE_BIDMASTER_AGENT_ADER"
	case UserRole_USER_ROLE_BIDMASTER_ADER:
		return "UserRole_USER_ROLE_BIDMASTER_ADER"
	}
	return "<UNSET>"
}

func UserRoleFromString(s string) (UserRole, error) {
	switch s {
	case "UserRole_USER_ROLE_UNKNOWN":
		return UserRole_USER_ROLE_UNKNOWN, nil
	case "UserRole_USER_ROLE_ADER":
		return UserRole_USER_ROLE_ADER, nil
	case "UserRole_USER_ROLE_DEVER":
		return UserRole_USER_ROLE_DEVER, nil
	case "UserRole_USER_ROLE_ADER_AND_DEVER":
		return UserRole_USER_ROLE_ADER_AND_DEVER, nil
	case "UserRole_USER_ROLE_TRAFFIC_EXCHANGE":
		return UserRole_USER_ROLE_TRAFFIC_EXCHANGE, nil
	case "UserRole_USER_ROLE_AGENT":
		return UserRole_USER_ROLE_AGENT, nil
	case "UserRole_USER_ROLE_AGENT_ADER":
		return UserRole_USER_ROLE_AGENT_ADER, nil
	case "UserRole_USER_ROLE_3P_CHANNEL":
		return UserRole_USER_ROLE_3P_CHANNEL, nil
	case "UserRole_USER_ROLE_API_AGENT":
		return UserRole_USER_ROLE_API_AGENT, nil
	case "UserRole_USER_ROLE_API_AGENT_ADER":
		return UserRole_USER_ROLE_API_AGENT_ADER, nil
	case "UserRole_USER_ROLE_DSP_ADER":
		return UserRole_USER_ROLE_DSP_ADER, nil
	case "UserRole_USER_ROLE_DSP_AGENT":
		return UserRole_USER_ROLE_DSP_AGENT, nil
	case "UserRole_USER_ROLE_DSP_AGENT_ADER":
		return UserRole_USER_ROLE_DSP_AGENT_ADER, nil
	case "UserRole_USER_ROLE_ADX_AGENT":
		return UserRole_USER_ROLE_ADX_AGENT, nil
	case "UserRole_USER_ROLE_ADX_AGENT_ADER":
		return UserRole_USER_ROLE_ADX_AGENT_ADER, nil
	case "UserRole_USER_ROLE_DSP_NEW_AGENT":
		return UserRole_USER_ROLE_DSP_NEW_AGENT, nil
	case "UserRole_USER_ROLE_DSP_NEW_AGENT_ADER":
		return UserRole_USER_ROLE_DSP_NEW_AGENT_ADER, nil
	case "UserRole_USER_ROLE_BIDMASTER_AGENT":
		return UserRole_USER_ROLE_BIDMASTER_AGENT, nil
	case "UserRole_USER_ROLE_BIDMASTER_AGENT_ADER":
		return UserRole_USER_ROLE_BIDMASTER_AGENT_ADER, nil
	case "UserRole_USER_ROLE_BIDMASTER_ADER":
		return UserRole_USER_ROLE_BIDMASTER_ADER, nil
	}
	return UserRole(math.MinInt32 - 1), fmt.Errorf("not a valid UserRole string")
}

//广告主账户类型, 目前用于标识广告主的业务类型
type SponsorType int64

const (
	SponsorType_SPONSOR_TYPE_UNKNOWN   SponsorType = 0
	SponsorType_SPONSOR_TYPE_BRANDING  SponsorType = 1
	SponsorType_SPONSOR_TYPE_A_FREE    SponsorType = 2
	SponsorType_SPONSOR_TYPE_ANDROID_A SponsorType = 3
	SponsorType_SPONSOR_TYPE_IOS_A     SponsorType = 4
	SponsorType_SPONSOR_TYPE_PACKAGED  SponsorType = 5
	SponsorType_SPONSOR_TYPE_OTHERS    SponsorType = 99
)

func (p SponsorType) String() string {
	switch p {
	case SponsorType_SPONSOR_TYPE_UNKNOWN:
		return "SponsorType_SPONSOR_TYPE_UNKNOWN"
	case SponsorType_SPONSOR_TYPE_BRANDING:
		return "SponsorType_SPONSOR_TYPE_BRANDING"
	case SponsorType_SPONSOR_TYPE_A_FREE:
		return "SponsorType_SPONSOR_TYPE_A_FREE"
	case SponsorType_SPONSOR_TYPE_ANDROID_A:
		return "SponsorType_SPONSOR_TYPE_ANDROID_A"
	case SponsorType_SPONSOR_TYPE_IOS_A:
		return "SponsorType_SPONSOR_TYPE_IOS_A"
	case SponsorType_SPONSOR_TYPE_PACKAGED:
		return "SponsorType_SPONSOR_TYPE_PACKAGED"
	case SponsorType_SPONSOR_TYPE_OTHERS:
		return "SponsorType_SPONSOR_TYPE_OTHERS"
	}
	return "<UNSET>"
}

func SponsorTypeFromString(s string) (SponsorType, error) {
	switch s {
	case "SponsorType_SPONSOR_TYPE_UNKNOWN":
		return SponsorType_SPONSOR_TYPE_UNKNOWN, nil
	case "SponsorType_SPONSOR_TYPE_BRANDING":
		return SponsorType_SPONSOR_TYPE_BRANDING, nil
	case "SponsorType_SPONSOR_TYPE_A_FREE":
		return SponsorType_SPONSOR_TYPE_A_FREE, nil
	case "SponsorType_SPONSOR_TYPE_ANDROID_A":
		return SponsorType_SPONSOR_TYPE_ANDROID_A, nil
	case "SponsorType_SPONSOR_TYPE_IOS_A":
		return SponsorType_SPONSOR_TYPE_IOS_A, nil
	case "SponsorType_SPONSOR_TYPE_PACKAGED":
		return SponsorType_SPONSOR_TYPE_PACKAGED, nil
	case "SponsorType_SPONSOR_TYPE_OTHERS":
		return SponsorType_SPONSOR_TYPE_OTHERS, nil
	}
	return SponsorType(math.MinInt32 - 1), fmt.Errorf("not a valid SponsorType string")
}

//性别代码
type GenderCode int64

const (
	GenderCode_GC_UNKNOWN GenderCode = 0
	GenderCode_GC_ALL     GenderCode = 1
	GenderCode_GC_MALE    GenderCode = 2
	GenderCode_GC_FEMALE  GenderCode = 3
)

func (p GenderCode) String() string {
	switch p {
	case GenderCode_GC_UNKNOWN:
		return "GenderCode_GC_UNKNOWN"
	case GenderCode_GC_ALL:
		return "GenderCode_GC_ALL"
	case GenderCode_GC_MALE:
		return "GenderCode_GC_MALE"
	case GenderCode_GC_FEMALE:
		return "GenderCode_GC_FEMALE"
	}
	return "<UNSET>"
}

func GenderCodeFromString(s string) (GenderCode, error) {
	switch s {
	case "GenderCode_GC_UNKNOWN":
		return GenderCode_GC_UNKNOWN, nil
	case "GenderCode_GC_ALL":
		return GenderCode_GC_ALL, nil
	case "GenderCode_GC_MALE":
		return GenderCode_GC_MALE, nil
	case "GenderCode_GC_FEMALE":
		return GenderCode_GC_FEMALE, nil
	}
	return GenderCode(math.MinInt32 - 1), fmt.Errorf("not a valid GenderCode string")
}

//年龄段代码
type AgeCode int64

const (
	AgeCode_AGC_UNKNOWN     AgeCode = 0
	AgeCode_AGC_ALL         AgeCode = 1
	AgeCode_AGC_CHILD       AgeCode = 2
	AgeCode_AGC_YOUNGSTER   AgeCode = 3
	AgeCode_AGC_YOUTH       AgeCode = 4
	AgeCode_AGC_MIDDLE_AGED AgeCode = 5
	AgeCode_AGC_ELDERLY     AgeCode = 6
)

func (p AgeCode) String() string {
	switch p {
	case AgeCode_AGC_UNKNOWN:
		return "AgeCode_AGC_UNKNOWN"
	case AgeCode_AGC_ALL:
		return "AgeCode_AGC_ALL"
	case AgeCode_AGC_CHILD:
		return "AgeCode_AGC_CHILD"
	case AgeCode_AGC_YOUNGSTER:
		return "AgeCode_AGC_YOUNGSTER"
	case AgeCode_AGC_YOUTH:
		return "AgeCode_AGC_YOUTH"
	case AgeCode_AGC_MIDDLE_AGED:
		return "AgeCode_AGC_MIDDLE_AGED"
	case AgeCode_AGC_ELDERLY:
		return "AgeCode_AGC_ELDERLY"
	}
	return "<UNSET>"
}

func AgeCodeFromString(s string) (AgeCode, error) {
	switch s {
	case "AgeCode_AGC_UNKNOWN":
		return AgeCode_AGC_UNKNOWN, nil
	case "AgeCode_AGC_ALL":
		return AgeCode_AGC_ALL, nil
	case "AgeCode_AGC_CHILD":
		return AgeCode_AGC_CHILD, nil
	case "AgeCode_AGC_YOUNGSTER":
		return AgeCode_AGC_YOUNGSTER, nil
	case "AgeCode_AGC_YOUTH":
		return AgeCode_AGC_YOUTH, nil
	case "AgeCode_AGC_MIDDLE_AGED":
		return AgeCode_AGC_MIDDLE_AGED, nil
	case "AgeCode_AGC_ELDERLY":
		return AgeCode_AGC_ELDERLY, nil
	}
	return AgeCode(math.MinInt32 - 1), fmt.Errorf("not a valid AgeCode string")
}

//地区代码,用于geoTarget,注意不要用于geoCityTarget
type RegionCode int64

const (
	RegionCode_REGION_UNKNOWN            RegionCode = 0
	RegionCode_REGION_ALL                RegionCode = 1
	RegionCode_REGION_CHINA_EXT          RegionCode = 2
	RegionCode_REGION_ASIA_CHINA_ALL     RegionCode = 10000
	RegionCode_REGION_BEIJING            RegionCode = 10100
	RegionCode_REGION_SHANGHAI           RegionCode = 10200
	RegionCode_REGION_GUANGDONG          RegionCode = 10300
	RegionCode_REGION_JIANGSU            RegionCode = 10400
	RegionCode_REGION_SHANDONG           RegionCode = 10500
	RegionCode_REGION_SICHUAN            RegionCode = 10600
	RegionCode_REGION_ZHEJIANG           RegionCode = 10700
	RegionCode_REGION_LIAONING           RegionCode = 10800
	RegionCode_REGION_HENAN              RegionCode = 10900
	RegionCode_REGION_HUBEI              RegionCode = 11000
	RegionCode_REGION_FUJIAN             RegionCode = 11100
	RegionCode_REGION_HEBEI              RegionCode = 11200
	RegionCode_REGION_HUNAN              RegionCode = 11300
	RegionCode_REGION_HEILONGJIANG       RegionCode = 11400
	RegionCode_REGION_TIANJIN            RegionCode = 11500
	RegionCode_REGION_CHONGQING          RegionCode = 11600
	RegionCode_REGION_JIANGXI            RegionCode = 11700
	RegionCode_REGION_SHANXI             RegionCode = 11800
	RegionCode_REGION_ANHUI              RegionCode = 11900
	RegionCode_REGION_SHANNXI            RegionCode = 12000
	RegionCode_REGION_HAINAN             RegionCode = 12100
	RegionCode_REGION_YUNNAN             RegionCode = 12200
	RegionCode_REGION_GANSU              RegionCode = 12300
	RegionCode_REGION_NEIMENGGU          RegionCode = 12400
	RegionCode_REGION_GUIZHOU            RegionCode = 12500
	RegionCode_REGION_XINJIANG           RegionCode = 12600
	RegionCode_REGION_TIBET              RegionCode = 12700
	RegionCode_REGION_QINGHAI            RegionCode = 12800
	RegionCode_REGION_GUANGXI            RegionCode = 12900
	RegionCode_REGION_NINGXIA            RegionCode = 13000
	RegionCode_REGION_JILIN              RegionCode = 13100
	RegionCode_REGION_HONGKONG           RegionCode = 13200
	RegionCode_REGION_MACAO              RegionCode = 13300
	RegionCode_REGION_TAIWAN             RegionCode = 13400
	RegionCode_REGION_ASIA_OTHER         RegionCode = 20000
	RegionCode_REGION_ASIA_JAPAN         RegionCode = 20100
	RegionCode_REGION_NORTH_AMERICA_ALL  RegionCode = 30000
	RegionCode_REGION_NA_AMERICA         RegionCode = 30100
	RegionCode_REGION_NA_CANNADA         RegionCode = 30200
	RegionCode_REGION_NA_OTHER           RegionCode = 30300
	RegionCode_REGION_LATIN_AMERICA_ALL  RegionCode = 40000
	RegionCode_REGION_WESTERN_EUROPE_ALL RegionCode = 50000
	RegionCode_REGION_EASTERN_EUROPE_ALL RegionCode = 60000
	RegionCode_REGION_AFRICA_ALL         RegionCode = 70000
	RegionCode_REGION_OCEANIA_ALL        RegionCode = 80000
)

func (p RegionCode) String() string {
	switch p {
	case RegionCode_REGION_UNKNOWN:
		return "RegionCode_REGION_UNKNOWN"
	case RegionCode_REGION_ALL:
		return "RegionCode_REGION_ALL"
	case RegionCode_REGION_CHINA_EXT:
		return "RegionCode_REGION_CHINA_EXT"
	case RegionCode_REGION_ASIA_CHINA_ALL:
		return "RegionCode_REGION_ASIA_CHINA_ALL"
	case RegionCode_REGION_BEIJING:
		return "RegionCode_REGION_BEIJING"
	case RegionCode_REGION_SHANGHAI:
		return "RegionCode_REGION_SHANGHAI"
	case RegionCode_REGION_GUANGDONG:
		return "RegionCode_REGION_GUANGDONG"
	case RegionCode_REGION_JIANGSU:
		return "RegionCode_REGION_JIANGSU"
	case RegionCode_REGION_SHANDONG:
		return "RegionCode_REGION_SHANDONG"
	case RegionCode_REGION_SICHUAN:
		return "RegionCode_REGION_SICHUAN"
	case RegionCode_REGION_ZHEJIANG:
		return "RegionCode_REGION_ZHEJIANG"
	case RegionCode_REGION_LIAONING:
		return "RegionCode_REGION_LIAONING"
	case RegionCode_REGION_HENAN:
		return "RegionCode_REGION_HENAN"
	case RegionCode_REGION_HUBEI:
		return "RegionCode_REGION_HUBEI"
	case RegionCode_REGION_FUJIAN:
		return "RegionCode_REGION_FUJIAN"
	case RegionCode_REGION_HEBEI:
		return "RegionCode_REGION_HEBEI"
	case RegionCode_REGION_HUNAN:
		return "RegionCode_REGION_HUNAN"
	case RegionCode_REGION_HEILONGJIANG:
		return "RegionCode_REGION_HEILONGJIANG"
	case RegionCode_REGION_TIANJIN:
		return "RegionCode_REGION_TIANJIN"
	case RegionCode_REGION_CHONGQING:
		return "RegionCode_REGION_CHONGQING"
	case RegionCode_REGION_JIANGXI:
		return "RegionCode_REGION_JIANGXI"
	case RegionCode_REGION_SHANXI:
		return "RegionCode_REGION_SHANXI"
	case RegionCode_REGION_ANHUI:
		return "RegionCode_REGION_ANHUI"
	case RegionCode_REGION_SHANNXI:
		return "RegionCode_REGION_SHANNXI"
	case RegionCode_REGION_HAINAN:
		return "RegionCode_REGION_HAINAN"
	case RegionCode_REGION_YUNNAN:
		return "RegionCode_REGION_YUNNAN"
	case RegionCode_REGION_GANSU:
		return "RegionCode_REGION_GANSU"
	case RegionCode_REGION_NEIMENGGU:
		return "RegionCode_REGION_NEIMENGGU"
	case RegionCode_REGION_GUIZHOU:
		return "RegionCode_REGION_GUIZHOU"
	case RegionCode_REGION_XINJIANG:
		return "RegionCode_REGION_XINJIANG"
	case RegionCode_REGION_TIBET:
		return "RegionCode_REGION_TIBET"
	case RegionCode_REGION_QINGHAI:
		return "RegionCode_REGION_QINGHAI"
	case RegionCode_REGION_GUANGXI:
		return "RegionCode_REGION_GUANGXI"
	case RegionCode_REGION_NINGXIA:
		return "RegionCode_REGION_NINGXIA"
	case RegionCode_REGION_JILIN:
		return "RegionCode_REGION_JILIN"
	case RegionCode_REGION_HONGKONG:
		return "RegionCode_REGION_HONGKONG"
	case RegionCode_REGION_MACAO:
		return "RegionCode_REGION_MACAO"
	case RegionCode_REGION_TAIWAN:
		return "RegionCode_REGION_TAIWAN"
	case RegionCode_REGION_ASIA_OTHER:
		return "RegionCode_REGION_ASIA_OTHER"
	case RegionCode_REGION_ASIA_JAPAN:
		return "RegionCode_REGION_ASIA_JAPAN"
	case RegionCode_REGION_NORTH_AMERICA_ALL:
		return "RegionCode_REGION_NORTH_AMERICA_ALL"
	case RegionCode_REGION_NA_AMERICA:
		return "RegionCode_REGION_NA_AMERICA"
	case RegionCode_REGION_NA_CANNADA:
		return "RegionCode_REGION_NA_CANNADA"
	case RegionCode_REGION_NA_OTHER:
		return "RegionCode_REGION_NA_OTHER"
	case RegionCode_REGION_LATIN_AMERICA_ALL:
		return "RegionCode_REGION_LATIN_AMERICA_ALL"
	case RegionCode_REGION_WESTERN_EUROPE_ALL:
		return "RegionCode_REGION_WESTERN_EUROPE_ALL"
	case RegionCode_REGION_EASTERN_EUROPE_ALL:
		return "RegionCode_REGION_EASTERN_EUROPE_ALL"
	case RegionCode_REGION_AFRICA_ALL:
		return "RegionCode_REGION_AFRICA_ALL"
	case RegionCode_REGION_OCEANIA_ALL:
		return "RegionCode_REGION_OCEANIA_ALL"
	}
	return "<UNSET>"
}

func RegionCodeFromString(s string) (RegionCode, error) {
	switch s {
	case "RegionCode_REGION_UNKNOWN":
		return RegionCode_REGION_UNKNOWN, nil
	case "RegionCode_REGION_ALL":
		return RegionCode_REGION_ALL, nil
	case "RegionCode_REGION_CHINA_EXT":
		return RegionCode_REGION_CHINA_EXT, nil
	case "RegionCode_REGION_ASIA_CHINA_ALL":
		return RegionCode_REGION_ASIA_CHINA_ALL, nil
	case "RegionCode_REGION_BEIJING":
		return RegionCode_REGION_BEIJING, nil
	case "RegionCode_REGION_SHANGHAI":
		return RegionCode_REGION_SHANGHAI, nil
	case "RegionCode_REGION_GUANGDONG":
		return RegionCode_REGION_GUANGDONG, nil
	case "RegionCode_REGION_JIANGSU":
		return RegionCode_REGION_JIANGSU, nil
	case "RegionCode_REGION_SHANDONG":
		return RegionCode_REGION_SHANDONG, nil
	case "RegionCode_REGION_SICHUAN":
		return RegionCode_REGION_SICHUAN, nil
	case "RegionCode_REGION_ZHEJIANG":
		return RegionCode_REGION_ZHEJIANG, nil
	case "RegionCode_REGION_LIAONING":
		return RegionCode_REGION_LIAONING, nil
	case "RegionCode_REGION_HENAN":
		return RegionCode_REGION_HENAN, nil
	case "RegionCode_REGION_HUBEI":
		return RegionCode_REGION_HUBEI, nil
	case "RegionCode_REGION_FUJIAN":
		return RegionCode_REGION_FUJIAN, nil
	case "RegionCode_REGION_HEBEI":
		return RegionCode_REGION_HEBEI, nil
	case "RegionCode_REGION_HUNAN":
		return RegionCode_REGION_HUNAN, nil
	case "RegionCode_REGION_HEILONGJIANG":
		return RegionCode_REGION_HEILONGJIANG, nil
	case "RegionCode_REGION_TIANJIN":
		return RegionCode_REGION_TIANJIN, nil
	case "RegionCode_REGION_CHONGQING":
		return RegionCode_REGION_CHONGQING, nil
	case "RegionCode_REGION_JIANGXI":
		return RegionCode_REGION_JIANGXI, nil
	case "RegionCode_REGION_SHANXI":
		return RegionCode_REGION_SHANXI, nil
	case "RegionCode_REGION_ANHUI":
		return RegionCode_REGION_ANHUI, nil
	case "RegionCode_REGION_SHANNXI":
		return RegionCode_REGION_SHANNXI, nil
	case "RegionCode_REGION_HAINAN":
		return RegionCode_REGION_HAINAN, nil
	case "RegionCode_REGION_YUNNAN":
		return RegionCode_REGION_YUNNAN, nil
	case "RegionCode_REGION_GANSU":
		return RegionCode_REGION_GANSU, nil
	case "RegionCode_REGION_NEIMENGGU":
		return RegionCode_REGION_NEIMENGGU, nil
	case "RegionCode_REGION_GUIZHOU":
		return RegionCode_REGION_GUIZHOU, nil
	case "RegionCode_REGION_XINJIANG":
		return RegionCode_REGION_XINJIANG, nil
	case "RegionCode_REGION_TIBET":
		return RegionCode_REGION_TIBET, nil
	case "RegionCode_REGION_QINGHAI":
		return RegionCode_REGION_QINGHAI, nil
	case "RegionCode_REGION_GUANGXI":
		return RegionCode_REGION_GUANGXI, nil
	case "RegionCode_REGION_NINGXIA":
		return RegionCode_REGION_NINGXIA, nil
	case "RegionCode_REGION_JILIN":
		return RegionCode_REGION_JILIN, nil
	case "RegionCode_REGION_HONGKONG":
		return RegionCode_REGION_HONGKONG, nil
	case "RegionCode_REGION_MACAO":
		return RegionCode_REGION_MACAO, nil
	case "RegionCode_REGION_TAIWAN":
		return RegionCode_REGION_TAIWAN, nil
	case "RegionCode_REGION_ASIA_OTHER":
		return RegionCode_REGION_ASIA_OTHER, nil
	case "RegionCode_REGION_ASIA_JAPAN":
		return RegionCode_REGION_ASIA_JAPAN, nil
	case "RegionCode_REGION_NORTH_AMERICA_ALL":
		return RegionCode_REGION_NORTH_AMERICA_ALL, nil
	case "RegionCode_REGION_NA_AMERICA":
		return RegionCode_REGION_NA_AMERICA, nil
	case "RegionCode_REGION_NA_CANNADA":
		return RegionCode_REGION_NA_CANNADA, nil
	case "RegionCode_REGION_NA_OTHER":
		return RegionCode_REGION_NA_OTHER, nil
	case "RegionCode_REGION_LATIN_AMERICA_ALL":
		return RegionCode_REGION_LATIN_AMERICA_ALL, nil
	case "RegionCode_REGION_WESTERN_EUROPE_ALL":
		return RegionCode_REGION_WESTERN_EUROPE_ALL, nil
	case "RegionCode_REGION_EASTERN_EUROPE_ALL":
		return RegionCode_REGION_EASTERN_EUROPE_ALL, nil
	case "RegionCode_REGION_AFRICA_ALL":
		return RegionCode_REGION_AFRICA_ALL, nil
	case "RegionCode_REGION_OCEANIA_ALL":
		return RegionCode_REGION_OCEANIA_ALL, nil
	}
	return RegionCode(math.MinInt32 - 1), fmt.Errorf("not a valid RegionCode string")
}

//运营商代码
type CarrierCode int64

const (
	CarrierCode_CARRIER_UNKNOWN   CarrierCode = 0
	CarrierCode_CARRIER_ALL       CarrierCode = 1
	CarrierCode_CARRIER_CMCC      CarrierCode = 2
	CarrierCode_CARRIER_CUNI      CarrierCode = 3
	CarrierCode_CARRIER_CT        CarrierCode = 4
	CarrierCode_CARRIER_CMCC_CUNI CarrierCode = 5
	CarrierCode_CARRIER_CMCC_CT   CarrierCode = 6
	CarrierCode_CARRIER_CUNI_CT   CarrierCode = 7
	CarrierCode_CARRIER_OTHER     CarrierCode = 100
)

func (p CarrierCode) String() string {
	switch p {
	case CarrierCode_CARRIER_UNKNOWN:
		return "CarrierCode_CARRIER_UNKNOWN"
	case CarrierCode_CARRIER_ALL:
		return "CarrierCode_CARRIER_ALL"
	case CarrierCode_CARRIER_CMCC:
		return "CarrierCode_CARRIER_CMCC"
	case CarrierCode_CARRIER_CUNI:
		return "CarrierCode_CARRIER_CUNI"
	case CarrierCode_CARRIER_CT:
		return "CarrierCode_CARRIER_CT"
	case CarrierCode_CARRIER_CMCC_CUNI:
		return "CarrierCode_CARRIER_CMCC_CUNI"
	case CarrierCode_CARRIER_CMCC_CT:
		return "CarrierCode_CARRIER_CMCC_CT"
	case CarrierCode_CARRIER_CUNI_CT:
		return "CarrierCode_CARRIER_CUNI_CT"
	case CarrierCode_CARRIER_OTHER:
		return "CarrierCode_CARRIER_OTHER"
	}
	return "<UNSET>"
}

func CarrierCodeFromString(s string) (CarrierCode, error) {
	switch s {
	case "CarrierCode_CARRIER_UNKNOWN":
		return CarrierCode_CARRIER_UNKNOWN, nil
	case "CarrierCode_CARRIER_ALL":
		return CarrierCode_CARRIER_ALL, nil
	case "CarrierCode_CARRIER_CMCC":
		return CarrierCode_CARRIER_CMCC, nil
	case "CarrierCode_CARRIER_CUNI":
		return CarrierCode_CARRIER_CUNI, nil
	case "CarrierCode_CARRIER_CT":
		return CarrierCode_CARRIER_CT, nil
	case "CarrierCode_CARRIER_CMCC_CUNI":
		return CarrierCode_CARRIER_CMCC_CUNI, nil
	case "CarrierCode_CARRIER_CMCC_CT":
		return CarrierCode_CARRIER_CMCC_CT, nil
	case "CarrierCode_CARRIER_CUNI_CT":
		return CarrierCode_CARRIER_CUNI_CT, nil
	case "CarrierCode_CARRIER_OTHER":
		return CarrierCode_CARRIER_OTHER, nil
	}
	return CarrierCode(math.MinInt32 - 1), fmt.Errorf("not a valid CarrierCode string")
}

//设备代码 高位表示厂商(如801000000)，低6位表示机型(如801000001)
//同一个厂商的所有机型，高位是相等的
//系统中使用device数据库device表中type为brand、model的id作为DeviceCode的可能取值
type DeviceCode int64

const (
	DeviceCode_DEVICE_UNKNOWN DeviceCode = 0
	DeviceCode_DEVICE_ALL     DeviceCode = 1
)

func (p DeviceCode) String() string {
	switch p {
	case DeviceCode_DEVICE_UNKNOWN:
		return "DeviceCode_DEVICE_UNKNOWN"
	case DeviceCode_DEVICE_ALL:
		return "DeviceCode_DEVICE_ALL"
	}
	return "<UNSET>"
}

func DeviceCodeFromString(s string) (DeviceCode, error) {
	switch s {
	case "DeviceCode_DEVICE_UNKNOWN":
		return DeviceCode_DEVICE_UNKNOWN, nil
	case "DeviceCode_DEVICE_ALL":
		return DeviceCode_DEVICE_ALL, nil
	}
	return DeviceCode(math.MinInt32 - 1), fmt.Errorf("not a valid DeviceCode string")
}

//OS代码 高位表示系统(如10000)，低4位表示版本(如10001)，支持9998个版本
//同一个系统的所有版本，高位是相等的
//系统中使用device数据库device表中type为os、osversion的id作为OSCode的可能取值
type OSCode int64

const (
	OSCode_OS_UNKNOWN OSCode = 0
	OSCode_OS_ALL     OSCode = 1
)

func (p OSCode) String() string {
	switch p {
	case OSCode_OS_UNKNOWN:
		return "OSCode_OS_UNKNOWN"
	case OSCode_OS_ALL:
		return "OSCode_OS_ALL"
	}
	return "<UNSET>"
}

func OSCodeFromString(s string) (OSCode, error) {
	switch s {
	case "OSCode_OS_UNKNOWN":
		return OSCode_OS_UNKNOWN, nil
	case "OSCode_OS_ALL":
		return OSCode_OS_ALL, nil
	}
	return OSCode(math.MinInt32 - 1), fmt.Errorf("not a valid OSCode string")
}

//广告内容分类
type AdCategory int64

const (
	AdCategory_ADC_UNKNOWN             AdCategory = 0
	AdCategory_ADC_GAME                AdCategory = 1
	AdCategory_ADC_APP                 AdCategory = 2
	AdCategory_ADC_FAST_CONSUMER_GOODS AdCategory = 3
	AdCategory_ADC_WEB                 AdCategory = 4
	AdCategory_ADC_WEB_LYHW            AdCategory = 11
	AdCategory_ADC_WEB_SHZX            AdCategory = 12
	AdCategory_ADC_WEB_YYYL            AdCategory = 13
	AdCategory_ADC_WEB_JYPX            AdCategory = 14
	AdCategory_ADC_WEB_DMYX            AdCategory = 15
	AdCategory_ADC_WEB_WZBK            AdCategory = 16
	AdCategory_ADC_WEB_WSSC            AdCategory = 17
	AdCategory_ADC_WEB_TZLC            AdCategory = 18
	AdCategory_ADC_WEB_YLJK            AdCategory = 19
	AdCategory_ADC_WEB_SMCP            AdCategory = 20
	AdCategory_ADC_WEB_RJCP            AdCategory = 21
	AdCategory_ADC_WEB_LXSH            AdCategory = 22
	AdCategory_ADC_WEB_YDTY            AdCategory = 23
	AdCategory_ADC_WEB_FDC             AdCategory = 24
	AdCategory_ADC_WEB_QC              AdCategory = 25
	AdCategory_ADC_WEB_OTHER           AdCategory = 99
	AdCategory_ADC_APP_AQRJ            AdCategory = 100
	AdCategory_ADC_APP_DMTRJ           AdCategory = 101
	AdCategory_ADC_APP_SYGJ            AdCategory = 102
	AdCategory_ADC_APP_XTRJ            AdCategory = 103
	AdCategory_ADC_APP_SHXX            AdCategory = 104
	AdCategory_ADC_APP_WLTX            AdCategory = 105
	AdCategory_ADC_APP_YLXX            AdCategory = 106
	AdCategory_ADC_APP_DZYD            AdCategory = 107
	AdCategory_ADC_APP_ZTZM            AdCategory = 108
	AdCategory_ADC_APP_XWZX            AdCategory = 109
	AdCategory_ADC_APP_OTHER           AdCategory = 199
	AdCategory_ADC_GAME_DZGD           AdCategory = 200
	AdCategory_ADC_GAME_XXYZ           AdCategory = 201
	AdCategory_ADC_GAME_TYJJ           AdCategory = 202
	AdCategory_ADC_GAME_JSBY           AdCategory = 203
	AdCategory_ADC_GAME_MNJY           AdCategory = 204
	AdCategory_ADC_GAME_WLYX           AdCategory = 205
	AdCategory_ADC_GAME_FXSJ           AdCategory = 206
	AdCategory_ADC_GAME_QPYX           AdCategory = 207
	AdCategory_ADC_GAME_OTHER          AdCategory = 299
	AdCategory_ADC_BRAND_ENTITY_QC     AdCategory = 300
	AdCategory_ADC_BRAND_ENTITY_FZ     AdCategory = 301
	AdCategory_ADC_BRAND_ENTITY_NX     AdCategory = 302
	AdCategory_ADC_BRAND_ENTITY_HZP    AdCategory = 303
	AdCategory_ADC_BRAND_ENTITY_YD     AdCategory = 304
	AdCategory_ADC_BRAND_ENTITY_MS     AdCategory = 305
	AdCategory_ADC_BRAND_ENTITY_SH     AdCategory = 306
	AdCategory_ADC_BRAND_ENTITY_DZSW   AdCategory = 307
	AdCategory_ADC_BRAND_ENTITY_SJSM   AdCategory = 308
	AdCategory_ADC_BRAND_ENTITY_OTHER  AdCategory = 399
	AdCategory_ADC_BRAND_IT            AdCategory = 400
	AdCategory_ADC_BRAND_ELEC          AdCategory = 401
	AdCategory_ADC_BRAND_NETWORK       AdCategory = 402
	AdCategory_ADC_BRAND_CARRIER       AdCategory = 403
	AdCategory_ADC_BRAND_CAR           AdCategory = 404
	AdCategory_ADC_BRAND_DAILY_GOODS   AdCategory = 405
	AdCategory_ADC_BRAND_FOOD          AdCategory = 406
	AdCategory_ADC_BRAND_CLOTHES       AdCategory = 407
	AdCategory_ADC_BRAND_RETAILING     AdCategory = 408
	AdCategory_ADC_BRAND_FINANCE       AdCategory = 409
	AdCategory_ADC_BRAND_OTHER         AdCategory = 499
)

func (p AdCategory) String() string {
	switch p {
	case AdCategory_ADC_UNKNOWN:
		return "AdCategory_ADC_UNKNOWN"
	case AdCategory_ADC_GAME:
		return "AdCategory_ADC_GAME"
	case AdCategory_ADC_APP:
		return "AdCategory_ADC_APP"
	case AdCategory_ADC_FAST_CONSUMER_GOODS:
		return "AdCategory_ADC_FAST_CONSUMER_GOODS"
	case AdCategory_ADC_WEB:
		return "AdCategory_ADC_WEB"
	case AdCategory_ADC_WEB_LYHW:
		return "AdCategory_ADC_WEB_LYHW"
	case AdCategory_ADC_WEB_SHZX:
		return "AdCategory_ADC_WEB_SHZX"
	case AdCategory_ADC_WEB_YYYL:
		return "AdCategory_ADC_WEB_YYYL"
	case AdCategory_ADC_WEB_JYPX:
		return "AdCategory_ADC_WEB_JYPX"
	case AdCategory_ADC_WEB_DMYX:
		return "AdCategory_ADC_WEB_DMYX"
	case AdCategory_ADC_WEB_WZBK:
		return "AdCategory_ADC_WEB_WZBK"
	case AdCategory_ADC_WEB_WSSC:
		return "AdCategory_ADC_WEB_WSSC"
	case AdCategory_ADC_WEB_TZLC:
		return "AdCategory_ADC_WEB_TZLC"
	case AdCategory_ADC_WEB_YLJK:
		return "AdCategory_ADC_WEB_YLJK"
	case AdCategory_ADC_WEB_SMCP:
		return "AdCategory_ADC_WEB_SMCP"
	case AdCategory_ADC_WEB_RJCP:
		return "AdCategory_ADC_WEB_RJCP"
	case AdCategory_ADC_WEB_LXSH:
		return "AdCategory_ADC_WEB_LXSH"
	case AdCategory_ADC_WEB_YDTY:
		return "AdCategory_ADC_WEB_YDTY"
	case AdCategory_ADC_WEB_FDC:
		return "AdCategory_ADC_WEB_FDC"
	case AdCategory_ADC_WEB_QC:
		return "AdCategory_ADC_WEB_QC"
	case AdCategory_ADC_WEB_OTHER:
		return "AdCategory_ADC_WEB_OTHER"
	case AdCategory_ADC_APP_AQRJ:
		return "AdCategory_ADC_APP_AQRJ"
	case AdCategory_ADC_APP_DMTRJ:
		return "AdCategory_ADC_APP_DMTRJ"
	case AdCategory_ADC_APP_SYGJ:
		return "AdCategory_ADC_APP_SYGJ"
	case AdCategory_ADC_APP_XTRJ:
		return "AdCategory_ADC_APP_XTRJ"
	case AdCategory_ADC_APP_SHXX:
		return "AdCategory_ADC_APP_SHXX"
	case AdCategory_ADC_APP_WLTX:
		return "AdCategory_ADC_APP_WLTX"
	case AdCategory_ADC_APP_YLXX:
		return "AdCategory_ADC_APP_YLXX"
	case AdCategory_ADC_APP_DZYD:
		return "AdCategory_ADC_APP_DZYD"
	case AdCategory_ADC_APP_ZTZM:
		return "AdCategory_ADC_APP_ZTZM"
	case AdCategory_ADC_APP_XWZX:
		return "AdCategory_ADC_APP_XWZX"
	case AdCategory_ADC_APP_OTHER:
		return "AdCategory_ADC_APP_OTHER"
	case AdCategory_ADC_GAME_DZGD:
		return "AdCategory_ADC_GAME_DZGD"
	case AdCategory_ADC_GAME_XXYZ:
		return "AdCategory_ADC_GAME_XXYZ"
	case AdCategory_ADC_GAME_TYJJ:
		return "AdCategory_ADC_GAME_TYJJ"
	case AdCategory_ADC_GAME_JSBY:
		return "AdCategory_ADC_GAME_JSBY"
	case AdCategory_ADC_GAME_MNJY:
		return "AdCategory_ADC_GAME_MNJY"
	case AdCategory_ADC_GAME_WLYX:
		return "AdCategory_ADC_GAME_WLYX"
	case AdCategory_ADC_GAME_FXSJ:
		return "AdCategory_ADC_GAME_FXSJ"
	case AdCategory_ADC_GAME_QPYX:
		return "AdCategory_ADC_GAME_QPYX"
	case AdCategory_ADC_GAME_OTHER:
		return "AdCategory_ADC_GAME_OTHER"
	case AdCategory_ADC_BRAND_ENTITY_QC:
		return "AdCategory_ADC_BRAND_ENTITY_QC"
	case AdCategory_ADC_BRAND_ENTITY_FZ:
		return "AdCategory_ADC_BRAND_ENTITY_FZ"
	case AdCategory_ADC_BRAND_ENTITY_NX:
		return "AdCategory_ADC_BRAND_ENTITY_NX"
	case AdCategory_ADC_BRAND_ENTITY_HZP:
		return "AdCategory_ADC_BRAND_ENTITY_HZP"
	case AdCategory_ADC_BRAND_ENTITY_YD:
		return "AdCategory_ADC_BRAND_ENTITY_YD"
	case AdCategory_ADC_BRAND_ENTITY_MS:
		return "AdCategory_ADC_BRAND_ENTITY_MS"
	case AdCategory_ADC_BRAND_ENTITY_SH:
		return "AdCategory_ADC_BRAND_ENTITY_SH"
	case AdCategory_ADC_BRAND_ENTITY_DZSW:
		return "AdCategory_ADC_BRAND_ENTITY_DZSW"
	case AdCategory_ADC_BRAND_ENTITY_SJSM:
		return "AdCategory_ADC_BRAND_ENTITY_SJSM"
	case AdCategory_ADC_BRAND_ENTITY_OTHER:
		return "AdCategory_ADC_BRAND_ENTITY_OTHER"
	case AdCategory_ADC_BRAND_IT:
		return "AdCategory_ADC_BRAND_IT"
	case AdCategory_ADC_BRAND_ELEC:
		return "AdCategory_ADC_BRAND_ELEC"
	case AdCategory_ADC_BRAND_NETWORK:
		return "AdCategory_ADC_BRAND_NETWORK"
	case AdCategory_ADC_BRAND_CARRIER:
		return "AdCategory_ADC_BRAND_CARRIER"
	case AdCategory_ADC_BRAND_CAR:
		return "AdCategory_ADC_BRAND_CAR"
	case AdCategory_ADC_BRAND_DAILY_GOODS:
		return "AdCategory_ADC_BRAND_DAILY_GOODS"
	case AdCategory_ADC_BRAND_FOOD:
		return "AdCategory_ADC_BRAND_FOOD"
	case AdCategory_ADC_BRAND_CLOTHES:
		return "AdCategory_ADC_BRAND_CLOTHES"
	case AdCategory_ADC_BRAND_RETAILING:
		return "AdCategory_ADC_BRAND_RETAILING"
	case AdCategory_ADC_BRAND_FINANCE:
		return "AdCategory_ADC_BRAND_FINANCE"
	case AdCategory_ADC_BRAND_OTHER:
		return "AdCategory_ADC_BRAND_OTHER"
	}
	return "<UNSET>"
}

func AdCategoryFromString(s string) (AdCategory, error) {
	switch s {
	case "AdCategory_ADC_UNKNOWN":
		return AdCategory_ADC_UNKNOWN, nil
	case "AdCategory_ADC_GAME":
		return AdCategory_ADC_GAME, nil
	case "AdCategory_ADC_APP":
		return AdCategory_ADC_APP, nil
	case "AdCategory_ADC_FAST_CONSUMER_GOODS":
		return AdCategory_ADC_FAST_CONSUMER_GOODS, nil
	case "AdCategory_ADC_WEB":
		return AdCategory_ADC_WEB, nil
	case "AdCategory_ADC_WEB_LYHW":
		return AdCategory_ADC_WEB_LYHW, nil
	case "AdCategory_ADC_WEB_SHZX":
		return AdCategory_ADC_WEB_SHZX, nil
	case "AdCategory_ADC_WEB_YYYL":
		return AdCategory_ADC_WEB_YYYL, nil
	case "AdCategory_ADC_WEB_JYPX":
		return AdCategory_ADC_WEB_JYPX, nil
	case "AdCategory_ADC_WEB_DMYX":
		return AdCategory_ADC_WEB_DMYX, nil
	case "AdCategory_ADC_WEB_WZBK":
		return AdCategory_ADC_WEB_WZBK, nil
	case "AdCategory_ADC_WEB_WSSC":
		return AdCategory_ADC_WEB_WSSC, nil
	case "AdCategory_ADC_WEB_TZLC":
		return AdCategory_ADC_WEB_TZLC, nil
	case "AdCategory_ADC_WEB_YLJK":
		return AdCategory_ADC_WEB_YLJK, nil
	case "AdCategory_ADC_WEB_SMCP":
		return AdCategory_ADC_WEB_SMCP, nil
	case "AdCategory_ADC_WEB_RJCP":
		return AdCategory_ADC_WEB_RJCP, nil
	case "AdCategory_ADC_WEB_LXSH":
		return AdCategory_ADC_WEB_LXSH, nil
	case "AdCategory_ADC_WEB_YDTY":
		return AdCategory_ADC_WEB_YDTY, nil
	case "AdCategory_ADC_WEB_FDC":
		return AdCategory_ADC_WEB_FDC, nil
	case "AdCategory_ADC_WEB_QC":
		return AdCategory_ADC_WEB_QC, nil
	case "AdCategory_ADC_WEB_OTHER":
		return AdCategory_ADC_WEB_OTHER, nil
	case "AdCategory_ADC_APP_AQRJ":
		return AdCategory_ADC_APP_AQRJ, nil
	case "AdCategory_ADC_APP_DMTRJ":
		return AdCategory_ADC_APP_DMTRJ, nil
	case "AdCategory_ADC_APP_SYGJ":
		return AdCategory_ADC_APP_SYGJ, nil
	case "AdCategory_ADC_APP_XTRJ":
		return AdCategory_ADC_APP_XTRJ, nil
	case "AdCategory_ADC_APP_SHXX":
		return AdCategory_ADC_APP_SHXX, nil
	case "AdCategory_ADC_APP_WLTX":
		return AdCategory_ADC_APP_WLTX, nil
	case "AdCategory_ADC_APP_YLXX":
		return AdCategory_ADC_APP_YLXX, nil
	case "AdCategory_ADC_APP_DZYD":
		return AdCategory_ADC_APP_DZYD, nil
	case "AdCategory_ADC_APP_ZTZM":
		return AdCategory_ADC_APP_ZTZM, nil
	case "AdCategory_ADC_APP_XWZX":
		return AdCategory_ADC_APP_XWZX, nil
	case "AdCategory_ADC_APP_OTHER":
		return AdCategory_ADC_APP_OTHER, nil
	case "AdCategory_ADC_GAME_DZGD":
		return AdCategory_ADC_GAME_DZGD, nil
	case "AdCategory_ADC_GAME_XXYZ":
		return AdCategory_ADC_GAME_XXYZ, nil
	case "AdCategory_ADC_GAME_TYJJ":
		return AdCategory_ADC_GAME_TYJJ, nil
	case "AdCategory_ADC_GAME_JSBY":
		return AdCategory_ADC_GAME_JSBY, nil
	case "AdCategory_ADC_GAME_MNJY":
		return AdCategory_ADC_GAME_MNJY, nil
	case "AdCategory_ADC_GAME_WLYX":
		return AdCategory_ADC_GAME_WLYX, nil
	case "AdCategory_ADC_GAME_FXSJ":
		return AdCategory_ADC_GAME_FXSJ, nil
	case "AdCategory_ADC_GAME_QPYX":
		return AdCategory_ADC_GAME_QPYX, nil
	case "AdCategory_ADC_GAME_OTHER":
		return AdCategory_ADC_GAME_OTHER, nil
	case "AdCategory_ADC_BRAND_ENTITY_QC":
		return AdCategory_ADC_BRAND_ENTITY_QC, nil
	case "AdCategory_ADC_BRAND_ENTITY_FZ":
		return AdCategory_ADC_BRAND_ENTITY_FZ, nil
	case "AdCategory_ADC_BRAND_ENTITY_NX":
		return AdCategory_ADC_BRAND_ENTITY_NX, nil
	case "AdCategory_ADC_BRAND_ENTITY_HZP":
		return AdCategory_ADC_BRAND_ENTITY_HZP, nil
	case "AdCategory_ADC_BRAND_ENTITY_YD":
		return AdCategory_ADC_BRAND_ENTITY_YD, nil
	case "AdCategory_ADC_BRAND_ENTITY_MS":
		return AdCategory_ADC_BRAND_ENTITY_MS, nil
	case "AdCategory_ADC_BRAND_ENTITY_SH":
		return AdCategory_ADC_BRAND_ENTITY_SH, nil
	case "AdCategory_ADC_BRAND_ENTITY_DZSW":
		return AdCategory_ADC_BRAND_ENTITY_DZSW, nil
	case "AdCategory_ADC_BRAND_ENTITY_SJSM":
		return AdCategory_ADC_BRAND_ENTITY_SJSM, nil
	case "AdCategory_ADC_BRAND_ENTITY_OTHER":
		return AdCategory_ADC_BRAND_ENTITY_OTHER, nil
	case "AdCategory_ADC_BRAND_IT":
		return AdCategory_ADC_BRAND_IT, nil
	case "AdCategory_ADC_BRAND_ELEC":
		return AdCategory_ADC_BRAND_ELEC, nil
	case "AdCategory_ADC_BRAND_NETWORK":
		return AdCategory_ADC_BRAND_NETWORK, nil
	case "AdCategory_ADC_BRAND_CARRIER":
		return AdCategory_ADC_BRAND_CARRIER, nil
	case "AdCategory_ADC_BRAND_CAR":
		return AdCategory_ADC_BRAND_CAR, nil
	case "AdCategory_ADC_BRAND_DAILY_GOODS":
		return AdCategory_ADC_BRAND_DAILY_GOODS, nil
	case "AdCategory_ADC_BRAND_FOOD":
		return AdCategory_ADC_BRAND_FOOD, nil
	case "AdCategory_ADC_BRAND_CLOTHES":
		return AdCategory_ADC_BRAND_CLOTHES, nil
	case "AdCategory_ADC_BRAND_RETAILING":
		return AdCategory_ADC_BRAND_RETAILING, nil
	case "AdCategory_ADC_BRAND_FINANCE":
		return AdCategory_ADC_BRAND_FINANCE, nil
	case "AdCategory_ADC_BRAND_OTHER":
		return AdCategory_ADC_BRAND_OTHER, nil
	}
	return AdCategory(math.MinInt32 - 1), fmt.Errorf("not a valid AdCategory string")
}

//推广策略类型
type AdStrategyType int64

const (
	AdStrategyType_AST_UNKNOWN                 AdStrategyType = 0
	AdStrategyType_AST_WEB_WAP                 AdStrategyType = 1
	AdStrategyType_AST_WEB_MOBILE              AdStrategyType = 2
	AdStrategyType_AST_APP_ANDROID             AdStrategyType = 11
	AdStrategyType_AST_APP_IPHONE              AdStrategyType = 12
	AdStrategyType_AST_APP_J2ME                AdStrategyType = 13
	AdStrategyType_AST_APP_SYMBIAN             AdStrategyType = 14
	AdStrategyType_AST_APP_WINDOWS_MOBILE      AdStrategyType = 15
	AdStrategyType_AST_APP_WINCE               AdStrategyType = 16
	AdStrategyType_AST_EFFECT_CALL             AdStrategyType = 31
	AdStrategyType_AST_EFFECT_SMS              AdStrategyType = 32
	AdStrategyType_AST_EFFECT_MAIL             AdStrategyType = 33
	AdStrategyType_AST_EFFECT_MAP              AdStrategyType = 34
	AdStrategyType_AST_MULTIMEDIA_ONLINE_VIDEO AdStrategyType = 41
	AdStrategyType_AST_MULTIMEDIA_ITUNES_VIDEO AdStrategyType = 42
	AdStrategyType_AST_APP2_ANDROID            AdStrategyType = 50
	AdStrategyType_AST_APP2_IOS                AdStrategyType = 51
	AdStrategyType_AST_LAUNCH_APP_IOS          AdStrategyType = 52
	AdStrategyType_AST_LAUNCH_APP_ANDROID      AdStrategyType = 53
)

func (p AdStrategyType) String() string {
	switch p {
	case AdStrategyType_AST_UNKNOWN:
		return "AdStrategyType_AST_UNKNOWN"
	case AdStrategyType_AST_WEB_WAP:
		return "AdStrategyType_AST_WEB_WAP"
	case AdStrategyType_AST_WEB_MOBILE:
		return "AdStrategyType_AST_WEB_MOBILE"
	case AdStrategyType_AST_APP_ANDROID:
		return "AdStrategyType_AST_APP_ANDROID"
	case AdStrategyType_AST_APP_IPHONE:
		return "AdStrategyType_AST_APP_IPHONE"
	case AdStrategyType_AST_APP_J2ME:
		return "AdStrategyType_AST_APP_J2ME"
	case AdStrategyType_AST_APP_SYMBIAN:
		return "AdStrategyType_AST_APP_SYMBIAN"
	case AdStrategyType_AST_APP_WINDOWS_MOBILE:
		return "AdStrategyType_AST_APP_WINDOWS_MOBILE"
	case AdStrategyType_AST_APP_WINCE:
		return "AdStrategyType_AST_APP_WINCE"
	case AdStrategyType_AST_EFFECT_CALL:
		return "AdStrategyType_AST_EFFECT_CALL"
	case AdStrategyType_AST_EFFECT_SMS:
		return "AdStrategyType_AST_EFFECT_SMS"
	case AdStrategyType_AST_EFFECT_MAIL:
		return "AdStrategyType_AST_EFFECT_MAIL"
	case AdStrategyType_AST_EFFECT_MAP:
		return "AdStrategyType_AST_EFFECT_MAP"
	case AdStrategyType_AST_MULTIMEDIA_ONLINE_VIDEO:
		return "AdStrategyType_AST_MULTIMEDIA_ONLINE_VIDEO"
	case AdStrategyType_AST_MULTIMEDIA_ITUNES_VIDEO:
		return "AdStrategyType_AST_MULTIMEDIA_ITUNES_VIDEO"
	case AdStrategyType_AST_APP2_ANDROID:
		return "AdStrategyType_AST_APP2_ANDROID"
	case AdStrategyType_AST_APP2_IOS:
		return "AdStrategyType_AST_APP2_IOS"
	case AdStrategyType_AST_LAUNCH_APP_IOS:
		return "AdStrategyType_AST_LAUNCH_APP_IOS"
	case AdStrategyType_AST_LAUNCH_APP_ANDROID:
		return "AdStrategyType_AST_LAUNCH_APP_ANDROID"
	}
	return "<UNSET>"
}

func AdStrategyTypeFromString(s string) (AdStrategyType, error) {
	switch s {
	case "AdStrategyType_AST_UNKNOWN":
		return AdStrategyType_AST_UNKNOWN, nil
	case "AdStrategyType_AST_WEB_WAP":
		return AdStrategyType_AST_WEB_WAP, nil
	case "AdStrategyType_AST_WEB_MOBILE":
		return AdStrategyType_AST_WEB_MOBILE, nil
	case "AdStrategyType_AST_APP_ANDROID":
		return AdStrategyType_AST_APP_ANDROID, nil
	case "AdStrategyType_AST_APP_IPHONE":
		return AdStrategyType_AST_APP_IPHONE, nil
	case "AdStrategyType_AST_APP_J2ME":
		return AdStrategyType_AST_APP_J2ME, nil
	case "AdStrategyType_AST_APP_SYMBIAN":
		return AdStrategyType_AST_APP_SYMBIAN, nil
	case "AdStrategyType_AST_APP_WINDOWS_MOBILE":
		return AdStrategyType_AST_APP_WINDOWS_MOBILE, nil
	case "AdStrategyType_AST_APP_WINCE":
		return AdStrategyType_AST_APP_WINCE, nil
	case "AdStrategyType_AST_EFFECT_CALL":
		return AdStrategyType_AST_EFFECT_CALL, nil
	case "AdStrategyType_AST_EFFECT_SMS":
		return AdStrategyType_AST_EFFECT_SMS, nil
	case "AdStrategyType_AST_EFFECT_MAIL":
		return AdStrategyType_AST_EFFECT_MAIL, nil
	case "AdStrategyType_AST_EFFECT_MAP":
		return AdStrategyType_AST_EFFECT_MAP, nil
	case "AdStrategyType_AST_MULTIMEDIA_ONLINE_VIDEO":
		return AdStrategyType_AST_MULTIMEDIA_ONLINE_VIDEO, nil
	case "AdStrategyType_AST_MULTIMEDIA_ITUNES_VIDEO":
		return AdStrategyType_AST_MULTIMEDIA_ITUNES_VIDEO, nil
	case "AdStrategyType_AST_APP2_ANDROID":
		return AdStrategyType_AST_APP2_ANDROID, nil
	case "AdStrategyType_AST_APP2_IOS":
		return AdStrategyType_AST_APP2_IOS, nil
	case "AdStrategyType_AST_LAUNCH_APP_IOS":
		return AdStrategyType_AST_LAUNCH_APP_IOS, nil
	case "AdStrategyType_AST_LAUNCH_APP_ANDROID":
		return AdStrategyType_AST_LAUNCH_APP_ANDROID, nil
	}
	return AdStrategyType(math.MinInt32 - 1), fmt.Errorf("not a valid AdStrategyType string")
}

//广告Action类型
type AdActionType int64

const (
	AdActionType_AAT_ACTION_UNKNOWN    AdActionType = 0
	AdActionType_AAT_ACTION_URL        AdActionType = 1
	AdActionType_AAT_ACTION_SMS        AdActionType = 2
	AdActionType_AAT_ACTION_MAIL       AdActionType = 3
	AdActionType_AAT_ACTION_MAP        AdActionType = 4
	AdActionType_AAT_ACTION_CALL       AdActionType = 5
	AdActionType_AAT_ACTION_MARKET     AdActionType = 6
	AdActionType_AAT_ACTION_AUDIO      AdActionType = 7
	AdActionType_AAT_ACTION_VIDEO      AdActionType = 8
	AdActionType_AAT_ACTION_HTML_ONLY  AdActionType = 11
	AdActionType_AAT_ACTION_WAP_ONLY   AdActionType = 12
	AdActionType_AAT_ACTION_FS_IMAGE   AdActionType = 13
	AdActionType_AAT_ACTION_APP2       AdActionType = 50
	AdActionType_AAT_ACTION_LAUNCH_APP AdActionType = 51
	AdActionType_AAT_ACTION_EXPANDABLE AdActionType = 53
	AdActionType_AAT_ACTION_APPWALL    AdActionType = 60
)

func (p AdActionType) String() string {
	switch p {
	case AdActionType_AAT_ACTION_UNKNOWN:
		return "AdActionType_AAT_ACTION_UNKNOWN"
	case AdActionType_AAT_ACTION_URL:
		return "AdActionType_AAT_ACTION_URL"
	case AdActionType_AAT_ACTION_SMS:
		return "AdActionType_AAT_ACTION_SMS"
	case AdActionType_AAT_ACTION_MAIL:
		return "AdActionType_AAT_ACTION_MAIL"
	case AdActionType_AAT_ACTION_MAP:
		return "AdActionType_AAT_ACTION_MAP"
	case AdActionType_AAT_ACTION_CALL:
		return "AdActionType_AAT_ACTION_CALL"
	case AdActionType_AAT_ACTION_MARKET:
		return "AdActionType_AAT_ACTION_MARKET"
	case AdActionType_AAT_ACTION_AUDIO:
		return "AdActionType_AAT_ACTION_AUDIO"
	case AdActionType_AAT_ACTION_VIDEO:
		return "AdActionType_AAT_ACTION_VIDEO"
	case AdActionType_AAT_ACTION_HTML_ONLY:
		return "AdActionType_AAT_ACTION_HTML_ONLY"
	case AdActionType_AAT_ACTION_WAP_ONLY:
		return "AdActionType_AAT_ACTION_WAP_ONLY"
	case AdActionType_AAT_ACTION_FS_IMAGE:
		return "AdActionType_AAT_ACTION_FS_IMAGE"
	case AdActionType_AAT_ACTION_APP2:
		return "AdActionType_AAT_ACTION_APP2"
	case AdActionType_AAT_ACTION_LAUNCH_APP:
		return "AdActionType_AAT_ACTION_LAUNCH_APP"
	case AdActionType_AAT_ACTION_EXPANDABLE:
		return "AdActionType_AAT_ACTION_EXPANDABLE"
	case AdActionType_AAT_ACTION_APPWALL:
		return "AdActionType_AAT_ACTION_APPWALL"
	}
	return "<UNSET>"
}

func AdActionTypeFromString(s string) (AdActionType, error) {
	switch s {
	case "AdActionType_AAT_ACTION_UNKNOWN":
		return AdActionType_AAT_ACTION_UNKNOWN, nil
	case "AdActionType_AAT_ACTION_URL":
		return AdActionType_AAT_ACTION_URL, nil
	case "AdActionType_AAT_ACTION_SMS":
		return AdActionType_AAT_ACTION_SMS, nil
	case "AdActionType_AAT_ACTION_MAIL":
		return AdActionType_AAT_ACTION_MAIL, nil
	case "AdActionType_AAT_ACTION_MAP":
		return AdActionType_AAT_ACTION_MAP, nil
	case "AdActionType_AAT_ACTION_CALL":
		return AdActionType_AAT_ACTION_CALL, nil
	case "AdActionType_AAT_ACTION_MARKET":
		return AdActionType_AAT_ACTION_MARKET, nil
	case "AdActionType_AAT_ACTION_AUDIO":
		return AdActionType_AAT_ACTION_AUDIO, nil
	case "AdActionType_AAT_ACTION_VIDEO":
		return AdActionType_AAT_ACTION_VIDEO, nil
	case "AdActionType_AAT_ACTION_HTML_ONLY":
		return AdActionType_AAT_ACTION_HTML_ONLY, nil
	case "AdActionType_AAT_ACTION_WAP_ONLY":
		return AdActionType_AAT_ACTION_WAP_ONLY, nil
	case "AdActionType_AAT_ACTION_FS_IMAGE":
		return AdActionType_AAT_ACTION_FS_IMAGE, nil
	case "AdActionType_AAT_ACTION_APP2":
		return AdActionType_AAT_ACTION_APP2, nil
	case "AdActionType_AAT_ACTION_LAUNCH_APP":
		return AdActionType_AAT_ACTION_LAUNCH_APP, nil
	case "AdActionType_AAT_ACTION_EXPANDABLE":
		return AdActionType_AAT_ACTION_EXPANDABLE, nil
	case "AdActionType_AAT_ACTION_APPWALL":
		return AdActionType_AAT_ACTION_APPWALL, nil
	}
	return AdActionType(math.MinInt32 - 1), fmt.Errorf("not a valid AdActionType string")
}

//广告创意类型
type AdCreativeType int64

const (
	AdCreativeType_ACT_UNKNOWN            AdCreativeType = 0
	AdCreativeType_ACT_TEXT               AdCreativeType = 1
	AdCreativeType_ACT_IMAGE              AdCreativeType = 2
	AdCreativeType_ACT_TEXT_IMAGE         AdCreativeType = 3
	AdCreativeType_ACT_GIF_ANIMATION      AdCreativeType = 4
	AdCreativeType_ACT_LONG_TEXT          AdCreativeType = 5
	AdCreativeType_ACT_INTERSTITIAL_IMAGE AdCreativeType = 6
	AdCreativeType_ACT_HTML_CUSTOMIZED    AdCreativeType = 7
	AdCreativeType_ACT_VIDEO              AdCreativeType = 8
	AdCreativeType_ACT_MIXED              AdCreativeType = 9
)

func (p AdCreativeType) String() string {
	switch p {
	case AdCreativeType_ACT_UNKNOWN:
		return "AdCreativeType_ACT_UNKNOWN"
	case AdCreativeType_ACT_TEXT:
		return "AdCreativeType_ACT_TEXT"
	case AdCreativeType_ACT_IMAGE:
		return "AdCreativeType_ACT_IMAGE"
	case AdCreativeType_ACT_TEXT_IMAGE:
		return "AdCreativeType_ACT_TEXT_IMAGE"
	case AdCreativeType_ACT_GIF_ANIMATION:
		return "AdCreativeType_ACT_GIF_ANIMATION"
	case AdCreativeType_ACT_LONG_TEXT:
		return "AdCreativeType_ACT_LONG_TEXT"
	case AdCreativeType_ACT_INTERSTITIAL_IMAGE:
		return "AdCreativeType_ACT_INTERSTITIAL_IMAGE"
	case AdCreativeType_ACT_HTML_CUSTOMIZED:
		return "AdCreativeType_ACT_HTML_CUSTOMIZED"
	case AdCreativeType_ACT_VIDEO:
		return "AdCreativeType_ACT_VIDEO"
	case AdCreativeType_ACT_MIXED:
		return "AdCreativeType_ACT_MIXED"
	}
	return "<UNSET>"
}

func AdCreativeTypeFromString(s string) (AdCreativeType, error) {
	switch s {
	case "AdCreativeType_ACT_UNKNOWN":
		return AdCreativeType_ACT_UNKNOWN, nil
	case "AdCreativeType_ACT_TEXT":
		return AdCreativeType_ACT_TEXT, nil
	case "AdCreativeType_ACT_IMAGE":
		return AdCreativeType_ACT_IMAGE, nil
	case "AdCreativeType_ACT_TEXT_IMAGE":
		return AdCreativeType_ACT_TEXT_IMAGE, nil
	case "AdCreativeType_ACT_GIF_ANIMATION":
		return AdCreativeType_ACT_GIF_ANIMATION, nil
	case "AdCreativeType_ACT_LONG_TEXT":
		return AdCreativeType_ACT_LONG_TEXT, nil
	case "AdCreativeType_ACT_INTERSTITIAL_IMAGE":
		return AdCreativeType_ACT_INTERSTITIAL_IMAGE, nil
	case "AdCreativeType_ACT_HTML_CUSTOMIZED":
		return AdCreativeType_ACT_HTML_CUSTOMIZED, nil
	case "AdCreativeType_ACT_VIDEO":
		return AdCreativeType_ACT_VIDEO, nil
	case "AdCreativeType_ACT_MIXED":
		return AdCreativeType_ACT_MIXED, nil
	}
	return AdCreativeType(math.MinInt32 - 1), fmt.Errorf("not a valid AdCreativeType string")
}

//广告位类型
type AdPlacementType int64

const (
	AdPlacementType_APT_UNKNOWN             AdPlacementType = 0
	AdPlacementType_APT_ALL                 AdPlacementType = 1
	AdPlacementType_APT_BANNER              AdPlacementType = 20
	AdPlacementType_APT_INTERSTITIAL        AdPlacementType = 22
	AdPlacementType_APT_SPLASH_INTERSTITIAL AdPlacementType = 23
	AdPlacementType_APT_NATIVE_FEEDS        AdPlacementType = 25
	AdPlacementType_APT_POPUP               AdPlacementType = 26
	AdPlacementType_APT_PATCH               AdPlacementType = 27
	AdPlacementType_APT_PUSH_MESSAGE        AdPlacementType = 28
	AdPlacementType_APT_ROTATION            AdPlacementType = 29
	AdPlacementType_APT_APP_WALL            AdPlacementType = 30
	AdPlacementType_APT_DOMOB_711           AdPlacementType = 31
	AdPlacementType_APT_NATIVE              AdPlacementType = 32
)

func (p AdPlacementType) String() string {
	switch p {
	case AdPlacementType_APT_UNKNOWN:
		return "AdPlacementType_APT_UNKNOWN"
	case AdPlacementType_APT_ALL:
		return "AdPlacementType_APT_ALL"
	case AdPlacementType_APT_BANNER:
		return "AdPlacementType_APT_BANNER"
	case AdPlacementType_APT_INTERSTITIAL:
		return "AdPlacementType_APT_INTERSTITIAL"
	case AdPlacementType_APT_SPLASH_INTERSTITIAL:
		return "AdPlacementType_APT_SPLASH_INTERSTITIAL"
	case AdPlacementType_APT_NATIVE_FEEDS:
		return "AdPlacementType_APT_NATIVE_FEEDS"
	case AdPlacementType_APT_POPUP:
		return "AdPlacementType_APT_POPUP"
	case AdPlacementType_APT_PATCH:
		return "AdPlacementType_APT_PATCH"
	case AdPlacementType_APT_PUSH_MESSAGE:
		return "AdPlacementType_APT_PUSH_MESSAGE"
	case AdPlacementType_APT_ROTATION:
		return "AdPlacementType_APT_ROTATION"
	case AdPlacementType_APT_APP_WALL:
		return "AdPlacementType_APT_APP_WALL"
	case AdPlacementType_APT_DOMOB_711:
		return "AdPlacementType_APT_DOMOB_711"
	case AdPlacementType_APT_NATIVE:
		return "AdPlacementType_APT_NATIVE"
	}
	return "<UNSET>"
}

func AdPlacementTypeFromString(s string) (AdPlacementType, error) {
	switch s {
	case "AdPlacementType_APT_UNKNOWN":
		return AdPlacementType_APT_UNKNOWN, nil
	case "AdPlacementType_APT_ALL":
		return AdPlacementType_APT_ALL, nil
	case "AdPlacementType_APT_BANNER":
		return AdPlacementType_APT_BANNER, nil
	case "AdPlacementType_APT_INTERSTITIAL":
		return AdPlacementType_APT_INTERSTITIAL, nil
	case "AdPlacementType_APT_SPLASH_INTERSTITIAL":
		return AdPlacementType_APT_SPLASH_INTERSTITIAL, nil
	case "AdPlacementType_APT_NATIVE_FEEDS":
		return AdPlacementType_APT_NATIVE_FEEDS, nil
	case "AdPlacementType_APT_POPUP":
		return AdPlacementType_APT_POPUP, nil
	case "AdPlacementType_APT_PATCH":
		return AdPlacementType_APT_PATCH, nil
	case "AdPlacementType_APT_PUSH_MESSAGE":
		return AdPlacementType_APT_PUSH_MESSAGE, nil
	case "AdPlacementType_APT_ROTATION":
		return AdPlacementType_APT_ROTATION, nil
	case "AdPlacementType_APT_APP_WALL":
		return AdPlacementType_APT_APP_WALL, nil
	case "AdPlacementType_APT_DOMOB_711":
		return AdPlacementType_APT_DOMOB_711, nil
	case "AdPlacementType_APT_NATIVE":
		return AdPlacementType_APT_NATIVE, nil
	}
	return AdPlacementType(math.MinInt32 - 1), fmt.Errorf("not a valid AdPlacementType string")
}

//LandingDirection
type LandingDirection int64

const (
	LandingDirection_LD_UNKNOWN    LandingDirection = 0
	LandingDirection_LD_HORIZONTAL LandingDirection = 1
	LandingDirection_LD_VERTICAL   LandingDirection = 2
)

func (p LandingDirection) String() string {
	switch p {
	case LandingDirection_LD_UNKNOWN:
		return "LandingDirection_LD_UNKNOWN"
	case LandingDirection_LD_HORIZONTAL:
		return "LandingDirection_LD_HORIZONTAL"
	case LandingDirection_LD_VERTICAL:
		return "LandingDirection_LD_VERTICAL"
	}
	return "<UNSET>"
}

func LandingDirectionFromString(s string) (LandingDirection, error) {
	switch s {
	case "LandingDirection_LD_UNKNOWN":
		return LandingDirection_LD_UNKNOWN, nil
	case "LandingDirection_LD_HORIZONTAL":
		return LandingDirection_LD_HORIZONTAL, nil
	case "LandingDirection_LD_VERTICAL":
		return LandingDirection_LD_VERTICAL, nil
	}
	return LandingDirection(math.MinInt32 - 1), fmt.Errorf("not a valid LandingDirection string")
}

//@Description("广告创意图标类型")
type AdCreativeIconType int64

const (
	AdCreativeIconType_ACIT_UNKNOWN              AdCreativeIconType = 0
	AdCreativeIconType_ACIT_SECURITY             AdCreativeIconType = 20
	AdCreativeIconType_ACIT_MEDIA_PLAYER         AdCreativeIconType = 21
	AdCreativeIconType_ACIT_IMAGE_BEAUTIFY       AdCreativeIconType = 22
	AdCreativeIconType_ACIT_CAMERA_ENHANCEMENT   AdCreativeIconType = 23
	AdCreativeIconType_ACIT_FINANCE_UTILITY      AdCreativeIconType = 24
	AdCreativeIconType_ACIT_SYSTEM_TOOL          AdCreativeIconType = 25
	AdCreativeIconType_ACIT_LIFE_INFO            AdCreativeIconType = 26
	AdCreativeIconType_ACIT_NETWORK_IM           AdCreativeIconType = 27
	AdCreativeIconType_ACIT_SMS_TOOL             AdCreativeIconType = 28
	AdCreativeIconType_ACIT_BROWSER              AdCreativeIconType = 29
	AdCreativeIconType_ACIT_BOOK_MAGAZINE_READER AdCreativeIconType = 30
	AdCreativeIconType_ACIT_THEME_WALLPAPER      AdCreativeIconType = 31
	AdCreativeIconType_ACIT_NEWS_INFO            AdCreativeIconType = 32
	AdCreativeIconType_ACIT_GAME                 AdCreativeIconType = 33
	AdCreativeIconType_ACIT_ONLINE_SHOPPING      AdCreativeIconType = 34
	AdCreativeIconType_ACIT_MOBILE_DIGITAL       AdCreativeIconType = 35
	AdCreativeIconType_ACIT_SPORTS               AdCreativeIconType = 36
	AdCreativeIconType_ACIT_WEB                  AdCreativeIconType = 37
	AdCreativeIconType_ACIT_CALENDAR_AGENDA      AdCreativeIconType = 38
	AdCreativeIconType_ACIT_CUSTOM               AdCreativeIconType = 100
)

func (p AdCreativeIconType) String() string {
	switch p {
	case AdCreativeIconType_ACIT_UNKNOWN:
		return "AdCreativeIconType_ACIT_UNKNOWN"
	case AdCreativeIconType_ACIT_SECURITY:
		return "AdCreativeIconType_ACIT_SECURITY"
	case AdCreativeIconType_ACIT_MEDIA_PLAYER:
		return "AdCreativeIconType_ACIT_MEDIA_PLAYER"
	case AdCreativeIconType_ACIT_IMAGE_BEAUTIFY:
		return "AdCreativeIconType_ACIT_IMAGE_BEAUTIFY"
	case AdCreativeIconType_ACIT_CAMERA_ENHANCEMENT:
		return "AdCreativeIconType_ACIT_CAMERA_ENHANCEMENT"
	case AdCreativeIconType_ACIT_FINANCE_UTILITY:
		return "AdCreativeIconType_ACIT_FINANCE_UTILITY"
	case AdCreativeIconType_ACIT_SYSTEM_TOOL:
		return "AdCreativeIconType_ACIT_SYSTEM_TOOL"
	case AdCreativeIconType_ACIT_LIFE_INFO:
		return "AdCreativeIconType_ACIT_LIFE_INFO"
	case AdCreativeIconType_ACIT_NETWORK_IM:
		return "AdCreativeIconType_ACIT_NETWORK_IM"
	case AdCreativeIconType_ACIT_SMS_TOOL:
		return "AdCreativeIconType_ACIT_SMS_TOOL"
	case AdCreativeIconType_ACIT_BROWSER:
		return "AdCreativeIconType_ACIT_BROWSER"
	case AdCreativeIconType_ACIT_BOOK_MAGAZINE_READER:
		return "AdCreativeIconType_ACIT_BOOK_MAGAZINE_READER"
	case AdCreativeIconType_ACIT_THEME_WALLPAPER:
		return "AdCreativeIconType_ACIT_THEME_WALLPAPER"
	case AdCreativeIconType_ACIT_NEWS_INFO:
		return "AdCreativeIconType_ACIT_NEWS_INFO"
	case AdCreativeIconType_ACIT_GAME:
		return "AdCreativeIconType_ACIT_GAME"
	case AdCreativeIconType_ACIT_ONLINE_SHOPPING:
		return "AdCreativeIconType_ACIT_ONLINE_SHOPPING"
	case AdCreativeIconType_ACIT_MOBILE_DIGITAL:
		return "AdCreativeIconType_ACIT_MOBILE_DIGITAL"
	case AdCreativeIconType_ACIT_SPORTS:
		return "AdCreativeIconType_ACIT_SPORTS"
	case AdCreativeIconType_ACIT_WEB:
		return "AdCreativeIconType_ACIT_WEB"
	case AdCreativeIconType_ACIT_CALENDAR_AGENDA:
		return "AdCreativeIconType_ACIT_CALENDAR_AGENDA"
	case AdCreativeIconType_ACIT_CUSTOM:
		return "AdCreativeIconType_ACIT_CUSTOM"
	}
	return "<UNSET>"
}

func AdCreativeIconTypeFromString(s string) (AdCreativeIconType, error) {
	switch s {
	case "AdCreativeIconType_ACIT_UNKNOWN":
		return AdCreativeIconType_ACIT_UNKNOWN, nil
	case "AdCreativeIconType_ACIT_SECURITY":
		return AdCreativeIconType_ACIT_SECURITY, nil
	case "AdCreativeIconType_ACIT_MEDIA_PLAYER":
		return AdCreativeIconType_ACIT_MEDIA_PLAYER, nil
	case "AdCreativeIconType_ACIT_IMAGE_BEAUTIFY":
		return AdCreativeIconType_ACIT_IMAGE_BEAUTIFY, nil
	case "AdCreativeIconType_ACIT_CAMERA_ENHANCEMENT":
		return AdCreativeIconType_ACIT_CAMERA_ENHANCEMENT, nil
	case "AdCreativeIconType_ACIT_FINANCE_UTILITY":
		return AdCreativeIconType_ACIT_FINANCE_UTILITY, nil
	case "AdCreativeIconType_ACIT_SYSTEM_TOOL":
		return AdCreativeIconType_ACIT_SYSTEM_TOOL, nil
	case "AdCreativeIconType_ACIT_LIFE_INFO":
		return AdCreativeIconType_ACIT_LIFE_INFO, nil
	case "AdCreativeIconType_ACIT_NETWORK_IM":
		return AdCreativeIconType_ACIT_NETWORK_IM, nil
	case "AdCreativeIconType_ACIT_SMS_TOOL":
		return AdCreativeIconType_ACIT_SMS_TOOL, nil
	case "AdCreativeIconType_ACIT_BROWSER":
		return AdCreativeIconType_ACIT_BROWSER, nil
	case "AdCreativeIconType_ACIT_BOOK_MAGAZINE_READER":
		return AdCreativeIconType_ACIT_BOOK_MAGAZINE_READER, nil
	case "AdCreativeIconType_ACIT_THEME_WALLPAPER":
		return AdCreativeIconType_ACIT_THEME_WALLPAPER, nil
	case "AdCreativeIconType_ACIT_NEWS_INFO":
		return AdCreativeIconType_ACIT_NEWS_INFO, nil
	case "AdCreativeIconType_ACIT_GAME":
		return AdCreativeIconType_ACIT_GAME, nil
	case "AdCreativeIconType_ACIT_ONLINE_SHOPPING":
		return AdCreativeIconType_ACIT_ONLINE_SHOPPING, nil
	case "AdCreativeIconType_ACIT_MOBILE_DIGITAL":
		return AdCreativeIconType_ACIT_MOBILE_DIGITAL, nil
	case "AdCreativeIconType_ACIT_SPORTS":
		return AdCreativeIconType_ACIT_SPORTS, nil
	case "AdCreativeIconType_ACIT_WEB":
		return AdCreativeIconType_ACIT_WEB, nil
	case "AdCreativeIconType_ACIT_CALENDAR_AGENDA":
		return AdCreativeIconType_ACIT_CALENDAR_AGENDA, nil
	case "AdCreativeIconType_ACIT_CUSTOM":
		return AdCreativeIconType_ACIT_CUSTOM, nil
	}
	return AdCreativeIconType(math.MinInt32 - 1), fmt.Errorf("not a valid AdCreativeIconType string")
}

//模板的尺寸类型
type TemplateSizeCode int64

const (
	TemplateSizeCode_SIZE_320_48   TemplateSizeCode = 32000048
	TemplateSizeCode_SIZE_120_20   TemplateSizeCode = 12000020
	TemplateSizeCode_SIZE_240_36   TemplateSizeCode = 24000036
	TemplateSizeCode_SIZE_320_270  TemplateSizeCode = 32000270
	TemplateSizeCode_SIZE_488_80   TemplateSizeCode = 48800080
	TemplateSizeCode_SIZE_748_110  TemplateSizeCode = 74800110
	TemplateSizeCode_SIZE_640_96   TemplateSizeCode = 64000096
	TemplateSizeCode_SIZE_480_72   TemplateSizeCode = 48000072
	TemplateSizeCode_SIZE_800_120  TemplateSizeCode = 80000120
	TemplateSizeCode_SIZE_600_90   TemplateSizeCode = 60000090
	TemplateSizeCode_SIZE_320_50   TemplateSizeCode = 32000050
	TemplateSizeCode_SIZE_640_100  TemplateSizeCode = 64000100
	TemplateSizeCode_SIZE_480_75   TemplateSizeCode = 48000075
	TemplateSizeCode_SIZE_800_125  TemplateSizeCode = 80000125
	TemplateSizeCode_SIZE_600_94   TemplateSizeCode = 60000094
	TemplateSizeCode_SIZE_728_90   TemplateSizeCode = 72800090
	TemplateSizeCode_SIZE_1024_768 TemplateSizeCode = 102400768
	TemplateSizeCode_SIZE_800_600  TemplateSizeCode = 80000600
	TemplateSizeCode_SIZE_640_480  TemplateSizeCode = 64000480
	TemplateSizeCode_SIZE_480_360  TemplateSizeCode = 48000360
	TemplateSizeCode_SIZE_960_640  TemplateSizeCode = 96000640
	TemplateSizeCode_SIZE_720_480  TemplateSizeCode = 72000480
	TemplateSizeCode_SIZE_480_320  TemplateSizeCode = 48000320
	TemplateSizeCode_SIZE_800_480  TemplateSizeCode = 80000480
	TemplateSizeCode_SIZE_600_500  TemplateSizeCode = 60000500
	TemplateSizeCode_SIZE_450_375  TemplateSizeCode = 45000375
	TemplateSizeCode_SIZE_300_250  TemplateSizeCode = 30000250
	TemplateSizeCode_SIZE_1280_800 TemplateSizeCode = 128000800
	TemplateSizeCode_SIZE_640_400  TemplateSizeCode = 64000400
	TemplateSizeCode_SIZE_1280_720 TemplateSizeCode = 128000720
	TemplateSizeCode_SIZE_640_360  TemplateSizeCode = 64000360
	TemplateSizeCode_SIZE_960_540  TemplateSizeCode = 96000540
	TemplateSizeCode_SIZE_854_480  TemplateSizeCode = 85400480
	TemplateSizeCode_SIZE_320_200  TemplateSizeCode = 32000200
	TemplateSizeCode_SIZE_533_320  TemplateSizeCode = 53300320
	TemplateSizeCode_SIZE_569_320  TemplateSizeCode = 56900320
	TemplateSizeCode_SIZE_768_1024 TemplateSizeCode = 76801024
	TemplateSizeCode_SIZE_600_800  TemplateSizeCode = 60000800
	TemplateSizeCode_SIZE_480_640  TemplateSizeCode = 48000640
	TemplateSizeCode_SIZE_360_480  TemplateSizeCode = 36000480
	TemplateSizeCode_SIZE_640_960  TemplateSizeCode = 64000960
	TemplateSizeCode_SIZE_480_720  TemplateSizeCode = 48000720
	TemplateSizeCode_SIZE_320_480  TemplateSizeCode = 32000480
	TemplateSizeCode_SIZE_480_800  TemplateSizeCode = 48000800
	TemplateSizeCode_SIZE_500_600  TemplateSizeCode = 50000600
	TemplateSizeCode_SIZE_375_450  TemplateSizeCode = 37500450
	TemplateSizeCode_SIZE_250_300  TemplateSizeCode = 25000300
	TemplateSizeCode_SIZE_800_1280 TemplateSizeCode = 80001280
	TemplateSizeCode_SIZE_400_640  TemplateSizeCode = 40000640
	TemplateSizeCode_SIZE_720_1280 TemplateSizeCode = 72001280
	TemplateSizeCode_SIZE_360_640  TemplateSizeCode = 36000640
	TemplateSizeCode_SIZE_480_854  TemplateSizeCode = 48000854
	TemplateSizeCode_SIZE_200_320  TemplateSizeCode = 20000320
	TemplateSizeCode_SIZE_320_533  TemplateSizeCode = 32000533
	TemplateSizeCode_SIZE_320_569  TemplateSizeCode = 32000569
	TemplateSizeCode_SIZE_240_180  TemplateSizeCode = 24000180
	TemplateSizeCode_SIZE_320_240  TemplateSizeCode = 32000240
	TemplateSizeCode_SIZE_300_200  TemplateSizeCode = 30000200
	TemplateSizeCode_SIZE_600_400  TemplateSizeCode = 60000400
	TemplateSizeCode_SIZE_300_180  TemplateSizeCode = 30000180
	TemplateSizeCode_SIZE_450_270  TemplateSizeCode = 45000270
	TemplateSizeCode_SIZE_600_360  TemplateSizeCode = 60000360
	TemplateSizeCode_SIZE_200_200  TemplateSizeCode = 20000200
	TemplateSizeCode_SIZE_300_300  TemplateSizeCode = 30000300
	TemplateSizeCode_SIZE_400_400  TemplateSizeCode = 40000400
	TemplateSizeCode_SIZE_600_600  TemplateSizeCode = 60000600
	TemplateSizeCode_SIZE_214_214  TemplateSizeCode = 21400214
	TemplateSizeCode_SIZE_300_50   TemplateSizeCode = 30000050
	TemplateSizeCode_SIZE_468_60   TemplateSizeCode = 46800060
	TemplateSizeCode_SIZE_216_36   TemplateSizeCode = 21600036
	TemplateSizeCode_SIZE_168_28   TemplateSizeCode = 16800028
)

func (p TemplateSizeCode) String() string {
	switch p {
	case TemplateSizeCode_SIZE_320_48:
		return "TemplateSizeCode_SIZE_320_48"
	case TemplateSizeCode_SIZE_120_20:
		return "TemplateSizeCode_SIZE_120_20"
	case TemplateSizeCode_SIZE_240_36:
		return "TemplateSizeCode_SIZE_240_36"
	case TemplateSizeCode_SIZE_320_270:
		return "TemplateSizeCode_SIZE_320_270"
	case TemplateSizeCode_SIZE_488_80:
		return "TemplateSizeCode_SIZE_488_80"
	case TemplateSizeCode_SIZE_748_110:
		return "TemplateSizeCode_SIZE_748_110"
	case TemplateSizeCode_SIZE_640_96:
		return "TemplateSizeCode_SIZE_640_96"
	case TemplateSizeCode_SIZE_480_72:
		return "TemplateSizeCode_SIZE_480_72"
	case TemplateSizeCode_SIZE_800_120:
		return "TemplateSizeCode_SIZE_800_120"
	case TemplateSizeCode_SIZE_600_90:
		return "TemplateSizeCode_SIZE_600_90"
	case TemplateSizeCode_SIZE_320_50:
		return "TemplateSizeCode_SIZE_320_50"
	case TemplateSizeCode_SIZE_640_100:
		return "TemplateSizeCode_SIZE_640_100"
	case TemplateSizeCode_SIZE_480_75:
		return "TemplateSizeCode_SIZE_480_75"
	case TemplateSizeCode_SIZE_800_125:
		return "TemplateSizeCode_SIZE_800_125"
	case TemplateSizeCode_SIZE_600_94:
		return "TemplateSizeCode_SIZE_600_94"
	case TemplateSizeCode_SIZE_728_90:
		return "TemplateSizeCode_SIZE_728_90"
	case TemplateSizeCode_SIZE_1024_768:
		return "TemplateSizeCode_SIZE_1024_768"
	case TemplateSizeCode_SIZE_800_600:
		return "TemplateSizeCode_SIZE_800_600"
	case TemplateSizeCode_SIZE_640_480:
		return "TemplateSizeCode_SIZE_640_480"
	case TemplateSizeCode_SIZE_480_360:
		return "TemplateSizeCode_SIZE_480_360"
	case TemplateSizeCode_SIZE_960_640:
		return "TemplateSizeCode_SIZE_960_640"
	case TemplateSizeCode_SIZE_720_480:
		return "TemplateSizeCode_SIZE_720_480"
	case TemplateSizeCode_SIZE_480_320:
		return "TemplateSizeCode_SIZE_480_320"
	case TemplateSizeCode_SIZE_800_480:
		return "TemplateSizeCode_SIZE_800_480"
	case TemplateSizeCode_SIZE_600_500:
		return "TemplateSizeCode_SIZE_600_500"
	case TemplateSizeCode_SIZE_450_375:
		return "TemplateSizeCode_SIZE_450_375"
	case TemplateSizeCode_SIZE_300_250:
		return "TemplateSizeCode_SIZE_300_250"
	case TemplateSizeCode_SIZE_1280_800:
		return "TemplateSizeCode_SIZE_1280_800"
	case TemplateSizeCode_SIZE_640_400:
		return "TemplateSizeCode_SIZE_640_400"
	case TemplateSizeCode_SIZE_1280_720:
		return "TemplateSizeCode_SIZE_1280_720"
	case TemplateSizeCode_SIZE_640_360:
		return "TemplateSizeCode_SIZE_640_360"
	case TemplateSizeCode_SIZE_960_540:
		return "TemplateSizeCode_SIZE_960_540"
	case TemplateSizeCode_SIZE_854_480:
		return "TemplateSizeCode_SIZE_854_480"
	case TemplateSizeCode_SIZE_320_200:
		return "TemplateSizeCode_SIZE_320_200"
	case TemplateSizeCode_SIZE_533_320:
		return "TemplateSizeCode_SIZE_533_320"
	case TemplateSizeCode_SIZE_569_320:
		return "TemplateSizeCode_SIZE_569_320"
	case TemplateSizeCode_SIZE_768_1024:
		return "TemplateSizeCode_SIZE_768_1024"
	case TemplateSizeCode_SIZE_600_800:
		return "TemplateSizeCode_SIZE_600_800"
	case TemplateSizeCode_SIZE_480_640:
		return "TemplateSizeCode_SIZE_480_640"
	case TemplateSizeCode_SIZE_360_480:
		return "TemplateSizeCode_SIZE_360_480"
	case TemplateSizeCode_SIZE_640_960:
		return "TemplateSizeCode_SIZE_640_960"
	case TemplateSizeCode_SIZE_480_720:
		return "TemplateSizeCode_SIZE_480_720"
	case TemplateSizeCode_SIZE_320_480:
		return "TemplateSizeCode_SIZE_320_480"
	case TemplateSizeCode_SIZE_480_800:
		return "TemplateSizeCode_SIZE_480_800"
	case TemplateSizeCode_SIZE_500_600:
		return "TemplateSizeCode_SIZE_500_600"
	case TemplateSizeCode_SIZE_375_450:
		return "TemplateSizeCode_SIZE_375_450"
	case TemplateSizeCode_SIZE_250_300:
		return "TemplateSizeCode_SIZE_250_300"
	case TemplateSizeCode_SIZE_800_1280:
		return "TemplateSizeCode_SIZE_800_1280"
	case TemplateSizeCode_SIZE_400_640:
		return "TemplateSizeCode_SIZE_400_640"
	case TemplateSizeCode_SIZE_720_1280:
		return "TemplateSizeCode_SIZE_720_1280"
	case TemplateSizeCode_SIZE_360_640:
		return "TemplateSizeCode_SIZE_360_640"
	case TemplateSizeCode_SIZE_480_854:
		return "TemplateSizeCode_SIZE_480_854"
	case TemplateSizeCode_SIZE_200_320:
		return "TemplateSizeCode_SIZE_200_320"
	case TemplateSizeCode_SIZE_320_533:
		return "TemplateSizeCode_SIZE_320_533"
	case TemplateSizeCode_SIZE_320_569:
		return "TemplateSizeCode_SIZE_320_569"
	case TemplateSizeCode_SIZE_240_180:
		return "TemplateSizeCode_SIZE_240_180"
	case TemplateSizeCode_SIZE_320_240:
		return "TemplateSizeCode_SIZE_320_240"
	case TemplateSizeCode_SIZE_300_200:
		return "TemplateSizeCode_SIZE_300_200"
	case TemplateSizeCode_SIZE_600_400:
		return "TemplateSizeCode_SIZE_600_400"
	case TemplateSizeCode_SIZE_300_180:
		return "TemplateSizeCode_SIZE_300_180"
	case TemplateSizeCode_SIZE_450_270:
		return "TemplateSizeCode_SIZE_450_270"
	case TemplateSizeCode_SIZE_600_360:
		return "TemplateSizeCode_SIZE_600_360"
	case TemplateSizeCode_SIZE_200_200:
		return "TemplateSizeCode_SIZE_200_200"
	case TemplateSizeCode_SIZE_300_300:
		return "TemplateSizeCode_SIZE_300_300"
	case TemplateSizeCode_SIZE_400_400:
		return "TemplateSizeCode_SIZE_400_400"
	case TemplateSizeCode_SIZE_600_600:
		return "TemplateSizeCode_SIZE_600_600"
	case TemplateSizeCode_SIZE_214_214:
		return "TemplateSizeCode_SIZE_214_214"
	case TemplateSizeCode_SIZE_300_50:
		return "TemplateSizeCode_SIZE_300_50"
	case TemplateSizeCode_SIZE_468_60:
		return "TemplateSizeCode_SIZE_468_60"
	case TemplateSizeCode_SIZE_216_36:
		return "TemplateSizeCode_SIZE_216_36"
	case TemplateSizeCode_SIZE_168_28:
		return "TemplateSizeCode_SIZE_168_28"
	}
	return "<UNSET>"
}

func TemplateSizeCodeFromString(s string) (TemplateSizeCode, error) {
	switch s {
	case "TemplateSizeCode_SIZE_320_48":
		return TemplateSizeCode_SIZE_320_48, nil
	case "TemplateSizeCode_SIZE_120_20":
		return TemplateSizeCode_SIZE_120_20, nil
	case "TemplateSizeCode_SIZE_240_36":
		return TemplateSizeCode_SIZE_240_36, nil
	case "TemplateSizeCode_SIZE_320_270":
		return TemplateSizeCode_SIZE_320_270, nil
	case "TemplateSizeCode_SIZE_488_80":
		return TemplateSizeCode_SIZE_488_80, nil
	case "TemplateSizeCode_SIZE_748_110":
		return TemplateSizeCode_SIZE_748_110, nil
	case "TemplateSizeCode_SIZE_640_96":
		return TemplateSizeCode_SIZE_640_96, nil
	case "TemplateSizeCode_SIZE_480_72":
		return TemplateSizeCode_SIZE_480_72, nil
	case "TemplateSizeCode_SIZE_800_120":
		return TemplateSizeCode_SIZE_800_120, nil
	case "TemplateSizeCode_SIZE_600_90":
		return TemplateSizeCode_SIZE_600_90, nil
	case "TemplateSizeCode_SIZE_320_50":
		return TemplateSizeCode_SIZE_320_50, nil
	case "TemplateSizeCode_SIZE_640_100":
		return TemplateSizeCode_SIZE_640_100, nil
	case "TemplateSizeCode_SIZE_480_75":
		return TemplateSizeCode_SIZE_480_75, nil
	case "TemplateSizeCode_SIZE_800_125":
		return TemplateSizeCode_SIZE_800_125, nil
	case "TemplateSizeCode_SIZE_600_94":
		return TemplateSizeCode_SIZE_600_94, nil
	case "TemplateSizeCode_SIZE_728_90":
		return TemplateSizeCode_SIZE_728_90, nil
	case "TemplateSizeCode_SIZE_1024_768":
		return TemplateSizeCode_SIZE_1024_768, nil
	case "TemplateSizeCode_SIZE_800_600":
		return TemplateSizeCode_SIZE_800_600, nil
	case "TemplateSizeCode_SIZE_640_480":
		return TemplateSizeCode_SIZE_640_480, nil
	case "TemplateSizeCode_SIZE_480_360":
		return TemplateSizeCode_SIZE_480_360, nil
	case "TemplateSizeCode_SIZE_960_640":
		return TemplateSizeCode_SIZE_960_640, nil
	case "TemplateSizeCode_SIZE_720_480":
		return TemplateSizeCode_SIZE_720_480, nil
	case "TemplateSizeCode_SIZE_480_320":
		return TemplateSizeCode_SIZE_480_320, nil
	case "TemplateSizeCode_SIZE_800_480":
		return TemplateSizeCode_SIZE_800_480, nil
	case "TemplateSizeCode_SIZE_600_500":
		return TemplateSizeCode_SIZE_600_500, nil
	case "TemplateSizeCode_SIZE_450_375":
		return TemplateSizeCode_SIZE_450_375, nil
	case "TemplateSizeCode_SIZE_300_250":
		return TemplateSizeCode_SIZE_300_250, nil
	case "TemplateSizeCode_SIZE_1280_800":
		return TemplateSizeCode_SIZE_1280_800, nil
	case "TemplateSizeCode_SIZE_640_400":
		return TemplateSizeCode_SIZE_640_400, nil
	case "TemplateSizeCode_SIZE_1280_720":
		return TemplateSizeCode_SIZE_1280_720, nil
	case "TemplateSizeCode_SIZE_640_360":
		return TemplateSizeCode_SIZE_640_360, nil
	case "TemplateSizeCode_SIZE_960_540":
		return TemplateSizeCode_SIZE_960_540, nil
	case "TemplateSizeCode_SIZE_854_480":
		return TemplateSizeCode_SIZE_854_480, nil
	case "TemplateSizeCode_SIZE_320_200":
		return TemplateSizeCode_SIZE_320_200, nil
	case "TemplateSizeCode_SIZE_533_320":
		return TemplateSizeCode_SIZE_533_320, nil
	case "TemplateSizeCode_SIZE_569_320":
		return TemplateSizeCode_SIZE_569_320, nil
	case "TemplateSizeCode_SIZE_768_1024":
		return TemplateSizeCode_SIZE_768_1024, nil
	case "TemplateSizeCode_SIZE_600_800":
		return TemplateSizeCode_SIZE_600_800, nil
	case "TemplateSizeCode_SIZE_480_640":
		return TemplateSizeCode_SIZE_480_640, nil
	case "TemplateSizeCode_SIZE_360_480":
		return TemplateSizeCode_SIZE_360_480, nil
	case "TemplateSizeCode_SIZE_640_960":
		return TemplateSizeCode_SIZE_640_960, nil
	case "TemplateSizeCode_SIZE_480_720":
		return TemplateSizeCode_SIZE_480_720, nil
	case "TemplateSizeCode_SIZE_320_480":
		return TemplateSizeCode_SIZE_320_480, nil
	case "TemplateSizeCode_SIZE_480_800":
		return TemplateSizeCode_SIZE_480_800, nil
	case "TemplateSizeCode_SIZE_500_600":
		return TemplateSizeCode_SIZE_500_600, nil
	case "TemplateSizeCode_SIZE_375_450":
		return TemplateSizeCode_SIZE_375_450, nil
	case "TemplateSizeCode_SIZE_250_300":
		return TemplateSizeCode_SIZE_250_300, nil
	case "TemplateSizeCode_SIZE_800_1280":
		return TemplateSizeCode_SIZE_800_1280, nil
	case "TemplateSizeCode_SIZE_400_640":
		return TemplateSizeCode_SIZE_400_640, nil
	case "TemplateSizeCode_SIZE_720_1280":
		return TemplateSizeCode_SIZE_720_1280, nil
	case "TemplateSizeCode_SIZE_360_640":
		return TemplateSizeCode_SIZE_360_640, nil
	case "TemplateSizeCode_SIZE_480_854":
		return TemplateSizeCode_SIZE_480_854, nil
	case "TemplateSizeCode_SIZE_200_320":
		return TemplateSizeCode_SIZE_200_320, nil
	case "TemplateSizeCode_SIZE_320_533":
		return TemplateSizeCode_SIZE_320_533, nil
	case "TemplateSizeCode_SIZE_320_569":
		return TemplateSizeCode_SIZE_320_569, nil
	case "TemplateSizeCode_SIZE_240_180":
		return TemplateSizeCode_SIZE_240_180, nil
	case "TemplateSizeCode_SIZE_320_240":
		return TemplateSizeCode_SIZE_320_240, nil
	case "TemplateSizeCode_SIZE_300_200":
		return TemplateSizeCode_SIZE_300_200, nil
	case "TemplateSizeCode_SIZE_600_400":
		return TemplateSizeCode_SIZE_600_400, nil
	case "TemplateSizeCode_SIZE_300_180":
		return TemplateSizeCode_SIZE_300_180, nil
	case "TemplateSizeCode_SIZE_450_270":
		return TemplateSizeCode_SIZE_450_270, nil
	case "TemplateSizeCode_SIZE_600_360":
		return TemplateSizeCode_SIZE_600_360, nil
	case "TemplateSizeCode_SIZE_200_200":
		return TemplateSizeCode_SIZE_200_200, nil
	case "TemplateSizeCode_SIZE_300_300":
		return TemplateSizeCode_SIZE_300_300, nil
	case "TemplateSizeCode_SIZE_400_400":
		return TemplateSizeCode_SIZE_400_400, nil
	case "TemplateSizeCode_SIZE_600_600":
		return TemplateSizeCode_SIZE_600_600, nil
	case "TemplateSizeCode_SIZE_214_214":
		return TemplateSizeCode_SIZE_214_214, nil
	case "TemplateSizeCode_SIZE_300_50":
		return TemplateSizeCode_SIZE_300_50, nil
	case "TemplateSizeCode_SIZE_468_60":
		return TemplateSizeCode_SIZE_468_60, nil
	case "TemplateSizeCode_SIZE_216_36":
		return TemplateSizeCode_SIZE_216_36, nil
	case "TemplateSizeCode_SIZE_168_28":
		return TemplateSizeCode_SIZE_168_28, nil
	}
	return TemplateSizeCode(math.MinInt32 - 1), fmt.Errorf("not a valid TemplateSizeCode string")
}

//SDK 类型
//注意：SDK接口协议中有个字段叫sdk，目前与SDKTypeCode的系统内部对应关系如下：
// sdk = 1  对应 SDKTYPE_PHONE = 6
// sdk = 10 对应 SDKTYPE_INTERSTITIAL = 10
type SDKTypeCode int64

const (
	SDKTypeCode_SDKTYPE_UNKNOWN      SDKTypeCode = 1
	SDKTypeCode_SDKTYPE_PHONE        SDKTypeCode = 2
	SDKTypeCode_SDKTYPE_WEB          SDKTypeCode = 3
	SDKTypeCode_SDKTYPE_WAP          SDKTypeCode = 4
	SDKTypeCode_SDKTYPE_APP2         SDKTypeCode = 6
	SDKTypeCode_SDKTYPE_INTERSTITIAL SDKTypeCode = 10
	SDKTypeCode_SDKTYPE_API          SDKTypeCode = 15
	SDKTypeCode_SDKTYPE_PUSH_MESSAGE SDKTypeCode = 16
	SDKTypeCode_SDKTYPE_OFFERWALL    SDKTypeCode = 20
	SDKTypeCode_SDKTYPE_APPWALL      SDKTypeCode = 30
	SDKTypeCode_SDKTYPE_DM711        SDKTypeCode = 31
	SDKTypeCode_SDKTYPE_OTHER        SDKTypeCode = 100
)

func (p SDKTypeCode) String() string {
	switch p {
	case SDKTypeCode_SDKTYPE_UNKNOWN:
		return "SDKTypeCode_SDKTYPE_UNKNOWN"
	case SDKTypeCode_SDKTYPE_PHONE:
		return "SDKTypeCode_SDKTYPE_PHONE"
	case SDKTypeCode_SDKTYPE_WEB:
		return "SDKTypeCode_SDKTYPE_WEB"
	case SDKTypeCode_SDKTYPE_WAP:
		return "SDKTypeCode_SDKTYPE_WAP"
	case SDKTypeCode_SDKTYPE_APP2:
		return "SDKTypeCode_SDKTYPE_APP2"
	case SDKTypeCode_SDKTYPE_INTERSTITIAL:
		return "SDKTypeCode_SDKTYPE_INTERSTITIAL"
	case SDKTypeCode_SDKTYPE_API:
		return "SDKTypeCode_SDKTYPE_API"
	case SDKTypeCode_SDKTYPE_PUSH_MESSAGE:
		return "SDKTypeCode_SDKTYPE_PUSH_MESSAGE"
	case SDKTypeCode_SDKTYPE_OFFERWALL:
		return "SDKTypeCode_SDKTYPE_OFFERWALL"
	case SDKTypeCode_SDKTYPE_APPWALL:
		return "SDKTypeCode_SDKTYPE_APPWALL"
	case SDKTypeCode_SDKTYPE_DM711:
		return "SDKTypeCode_SDKTYPE_DM711"
	case SDKTypeCode_SDKTYPE_OTHER:
		return "SDKTypeCode_SDKTYPE_OTHER"
	}
	return "<UNSET>"
}

func SDKTypeCodeFromString(s string) (SDKTypeCode, error) {
	switch s {
	case "SDKTypeCode_SDKTYPE_UNKNOWN":
		return SDKTypeCode_SDKTYPE_UNKNOWN, nil
	case "SDKTypeCode_SDKTYPE_PHONE":
		return SDKTypeCode_SDKTYPE_PHONE, nil
	case "SDKTypeCode_SDKTYPE_WEB":
		return SDKTypeCode_SDKTYPE_WEB, nil
	case "SDKTypeCode_SDKTYPE_WAP":
		return SDKTypeCode_SDKTYPE_WAP, nil
	case "SDKTypeCode_SDKTYPE_APP2":
		return SDKTypeCode_SDKTYPE_APP2, nil
	case "SDKTypeCode_SDKTYPE_INTERSTITIAL":
		return SDKTypeCode_SDKTYPE_INTERSTITIAL, nil
	case "SDKTypeCode_SDKTYPE_API":
		return SDKTypeCode_SDKTYPE_API, nil
	case "SDKTypeCode_SDKTYPE_PUSH_MESSAGE":
		return SDKTypeCode_SDKTYPE_PUSH_MESSAGE, nil
	case "SDKTypeCode_SDKTYPE_OFFERWALL":
		return SDKTypeCode_SDKTYPE_OFFERWALL, nil
	case "SDKTypeCode_SDKTYPE_APPWALL":
		return SDKTypeCode_SDKTYPE_APPWALL, nil
	case "SDKTypeCode_SDKTYPE_DM711":
		return SDKTypeCode_SDKTYPE_DM711, nil
	case "SDKTypeCode_SDKTYPE_OTHER":
		return SDKTypeCode_SDKTYPE_OTHER, nil
	}
	return SDKTypeCode(math.MinInt32 - 1), fmt.Errorf("not a valid SDKTypeCode string")
}

//语言类型
type LanguageType int64

const (
	LanguageType_LT_CH  LanguageType = 1
	LanguageType_LT_ENG LanguageType = 2
)

func (p LanguageType) String() string {
	switch p {
	case LanguageType_LT_CH:
		return "LanguageType_LT_CH"
	case LanguageType_LT_ENG:
		return "LanguageType_LT_ENG"
	}
	return "<UNSET>"
}

func LanguageTypeFromString(s string) (LanguageType, error) {
	switch s {
	case "LanguageType_LT_CH":
		return LanguageType_LT_CH, nil
	case "LanguageType_LT_ENG":
		return LanguageType_LT_ENG, nil
	}
	return LanguageType(math.MinInt32 - 1), fmt.Errorf("not a valid LanguageType string")
}

//广告应答类型
type ADResponseType int64

const (
	ADResponseType_ART_JSONP ADResponseType = 1
	ADResponseType_ART_HTML  ADResponseType = 2
	ADResponseType_ART_WML   ADResponseType = 3
)

func (p ADResponseType) String() string {
	switch p {
	case ADResponseType_ART_JSONP:
		return "ADResponseType_ART_JSONP"
	case ADResponseType_ART_HTML:
		return "ADResponseType_ART_HTML"
	case ADResponseType_ART_WML:
		return "ADResponseType_ART_WML"
	}
	return "<UNSET>"
}

func ADResponseTypeFromString(s string) (ADResponseType, error) {
	switch s {
	case "ADResponseType_ART_JSONP":
		return ADResponseType_ART_JSONP, nil
	case "ADResponseType_ART_HTML":
		return ADResponseType_ART_HTML, nil
	case "ADResponseType_ART_WML":
		return ADResponseType_ART_WML, nil
	}
	return ADResponseType(math.MinInt32 - 1), fmt.Errorf("not a valid ADResponseType string")
}

//编码类型代码
type EncodingCode int64

const (
	EncodingCode_ENCODING_UNKNOWN  EncodingCode = 0
	EncodingCode_ENCODING_UTF8     EncodingCode = 1
	EncodingCode_ENCODING_UTF16    EncodingCode = 2
	EncodingCode_ENCODING_ISO88591 EncodingCode = 3
	EncodingCode_ENCODING_GB2312   EncodingCode = 4
	EncodingCode_ENCODING_GBK      EncodingCode = 5
	EncodingCode_ENCODING_GB18030  EncodingCode = 6
	EncodingCode_ENCODING_BIG5     EncodingCode = 7
	EncodingCode_ENCODING_OTHER    EncodingCode = 100
)

func (p EncodingCode) String() string {
	switch p {
	case EncodingCode_ENCODING_UNKNOWN:
		return "EncodingCode_ENCODING_UNKNOWN"
	case EncodingCode_ENCODING_UTF8:
		return "EncodingCode_ENCODING_UTF8"
	case EncodingCode_ENCODING_UTF16:
		return "EncodingCode_ENCODING_UTF16"
	case EncodingCode_ENCODING_ISO88591:
		return "EncodingCode_ENCODING_ISO88591"
	case EncodingCode_ENCODING_GB2312:
		return "EncodingCode_ENCODING_GB2312"
	case EncodingCode_ENCODING_GBK:
		return "EncodingCode_ENCODING_GBK"
	case EncodingCode_ENCODING_GB18030:
		return "EncodingCode_ENCODING_GB18030"
	case EncodingCode_ENCODING_BIG5:
		return "EncodingCode_ENCODING_BIG5"
	case EncodingCode_ENCODING_OTHER:
		return "EncodingCode_ENCODING_OTHER"
	}
	return "<UNSET>"
}

func EncodingCodeFromString(s string) (EncodingCode, error) {
	switch s {
	case "EncodingCode_ENCODING_UNKNOWN":
		return EncodingCode_ENCODING_UNKNOWN, nil
	case "EncodingCode_ENCODING_UTF8":
		return EncodingCode_ENCODING_UTF8, nil
	case "EncodingCode_ENCODING_UTF16":
		return EncodingCode_ENCODING_UTF16, nil
	case "EncodingCode_ENCODING_ISO88591":
		return EncodingCode_ENCODING_ISO88591, nil
	case "EncodingCode_ENCODING_GB2312":
		return EncodingCode_ENCODING_GB2312, nil
	case "EncodingCode_ENCODING_GBK":
		return EncodingCode_ENCODING_GBK, nil
	case "EncodingCode_ENCODING_GB18030":
		return EncodingCode_ENCODING_GB18030, nil
	case "EncodingCode_ENCODING_BIG5":
		return EncodingCode_ENCODING_BIG5, nil
	case "EncodingCode_ENCODING_OTHER":
		return EncodingCode_ENCODING_OTHER, nil
	}
	return EncodingCode(math.MinInt32 - 1), fmt.Errorf("not a valid EncodingCode string")
}

//网络连接类型代码
type AccessTypeCode int64

const (
	AccessTypeCode_ACCESS_UNKNOWN         AccessTypeCode = 0
	AccessTypeCode_ACCESS_ALL             AccessTypeCode = 1
	AccessTypeCode_ACCESS_WIFI            AccessTypeCode = 2
	AccessTypeCode_ACCESS_GPRS            AccessTypeCode = 3
	AccessTypeCode_ACCESS_3G              AccessTypeCode = 4
	AccessTypeCode_ACCESS_NONE_WIFI       AccessTypeCode = 5
	AccessTypeCode_ACCESS_4G              AccessTypeCode = 6
	AccessTypeCode_ACCESS_2G              AccessTypeCode = 7
	AccessTypeCode_ACCESS_5G              AccessTypeCode = 8
	AccessTypeCode_ACCESS_NONE_WIFI_CMWAP AccessTypeCode = 10
	AccessTypeCode_ACCESS_OTHER           AccessTypeCode = 100
)

func (p AccessTypeCode) String() string {
	switch p {
	case AccessTypeCode_ACCESS_UNKNOWN:
		return "AccessTypeCode_ACCESS_UNKNOWN"
	case AccessTypeCode_ACCESS_ALL:
		return "AccessTypeCode_ACCESS_ALL"
	case AccessTypeCode_ACCESS_WIFI:
		return "AccessTypeCode_ACCESS_WIFI"
	case AccessTypeCode_ACCESS_GPRS:
		return "AccessTypeCode_ACCESS_GPRS"
	case AccessTypeCode_ACCESS_3G:
		return "AccessTypeCode_ACCESS_3G"
	case AccessTypeCode_ACCESS_NONE_WIFI:
		return "AccessTypeCode_ACCESS_NONE_WIFI"
	case AccessTypeCode_ACCESS_4G:
		return "AccessTypeCode_ACCESS_4G"
	case AccessTypeCode_ACCESS_2G:
		return "AccessTypeCode_ACCESS_2G"
	case AccessTypeCode_ACCESS_5G:
		return "AccessTypeCode_ACCESS_5G"
	case AccessTypeCode_ACCESS_NONE_WIFI_CMWAP:
		return "AccessTypeCode_ACCESS_NONE_WIFI_CMWAP"
	case AccessTypeCode_ACCESS_OTHER:
		return "AccessTypeCode_ACCESS_OTHER"
	}
	return "<UNSET>"
}

func AccessTypeCodeFromString(s string) (AccessTypeCode, error) {
	switch s {
	case "AccessTypeCode_ACCESS_UNKNOWN":
		return AccessTypeCode_ACCESS_UNKNOWN, nil
	case "AccessTypeCode_ACCESS_ALL":
		return AccessTypeCode_ACCESS_ALL, nil
	case "AccessTypeCode_ACCESS_WIFI":
		return AccessTypeCode_ACCESS_WIFI, nil
	case "AccessTypeCode_ACCESS_GPRS":
		return AccessTypeCode_ACCESS_GPRS, nil
	case "AccessTypeCode_ACCESS_3G":
		return AccessTypeCode_ACCESS_3G, nil
	case "AccessTypeCode_ACCESS_NONE_WIFI":
		return AccessTypeCode_ACCESS_NONE_WIFI, nil
	case "AccessTypeCode_ACCESS_4G":
		return AccessTypeCode_ACCESS_4G, nil
	case "AccessTypeCode_ACCESS_2G":
		return AccessTypeCode_ACCESS_2G, nil
	case "AccessTypeCode_ACCESS_5G":
		return AccessTypeCode_ACCESS_5G, nil
	case "AccessTypeCode_ACCESS_NONE_WIFI_CMWAP":
		return AccessTypeCode_ACCESS_NONE_WIFI_CMWAP, nil
	case "AccessTypeCode_ACCESS_OTHER":
		return AccessTypeCode_ACCESS_OTHER, nil
	}
	return AccessTypeCode(math.MinInt32 - 1), fmt.Errorf("not a valid AccessTypeCode string")
}

//广告消费类型
type CostType int64

const (
	CostType_CT_UNKNOWN  CostType = 0
	CostType_CT_CPC      CostType = 1
	CostType_CT_CPM      CostType = 2
	CostType_CT_CPA      CostType = 3
	CostType_CT_CPD      CostType = 4
	CostType_CT_CPI      CostType = 5
	CostType_CT_CPT      CostType = 6
	CostType_CT_HOUSE_AD CostType = 10
	CostType_CT_OCPC     CostType = 21
	CostType_CT_OCPM     CostType = 22
	CostType_CT_DCPC     CostType = 31
	CostType_CT_DCPM     CostType = 32
	CostType_CT_ALL      CostType = 100
)

func (p CostType) String() string {
	switch p {
	case CostType_CT_UNKNOWN:
		return "CostType_CT_UNKNOWN"
	case CostType_CT_CPC:
		return "CostType_CT_CPC"
	case CostType_CT_CPM:
		return "CostType_CT_CPM"
	case CostType_CT_CPA:
		return "CostType_CT_CPA"
	case CostType_CT_CPD:
		return "CostType_CT_CPD"
	case CostType_CT_CPI:
		return "CostType_CT_CPI"
	case CostType_CT_CPT:
		return "CostType_CT_CPT"
	case CostType_CT_HOUSE_AD:
		return "CostType_CT_HOUSE_AD"
	case CostType_CT_OCPC:
		return "CostType_CT_OCPC"
	case CostType_CT_OCPM:
		return "CostType_CT_OCPM"
	case CostType_CT_DCPC:
		return "CostType_CT_DCPC"
	case CostType_CT_DCPM:
		return "CostType_CT_DCPM"
	case CostType_CT_ALL:
		return "CostType_CT_ALL"
	}
	return "<UNSET>"
}

func CostTypeFromString(s string) (CostType, error) {
	switch s {
	case "CostType_CT_UNKNOWN":
		return CostType_CT_UNKNOWN, nil
	case "CostType_CT_CPC":
		return CostType_CT_CPC, nil
	case "CostType_CT_CPM":
		return CostType_CT_CPM, nil
	case "CostType_CT_CPA":
		return CostType_CT_CPA, nil
	case "CostType_CT_CPD":
		return CostType_CT_CPD, nil
	case "CostType_CT_CPI":
		return CostType_CT_CPI, nil
	case "CostType_CT_CPT":
		return CostType_CT_CPT, nil
	case "CostType_CT_HOUSE_AD":
		return CostType_CT_HOUSE_AD, nil
	case "CostType_CT_OCPC":
		return CostType_CT_OCPC, nil
	case "CostType_CT_OCPM":
		return CostType_CT_OCPM, nil
	case "CostType_CT_DCPC":
		return CostType_CT_DCPC, nil
	case "CostType_CT_DCPM":
		return CostType_CT_DCPM, nil
	case "CostType_CT_ALL":
		return CostType_CT_ALL, nil
	}
	return CostType(math.MinInt32 - 1), fmt.Errorf("not a valid CostType string")
}

//媒体类型
type MediaType int64

const (
	MediaType_MT_UNKNOWN             MediaType = 0
	MediaType_MT_WEB_WAP_1           MediaType = 1
	MediaType_MT_WEB_MOBILE          MediaType = 2
	MediaType_MT_WEB_WAP_2           MediaType = 3
	MediaType_MT_API_ANDROID         MediaType = 10
	MediaType_MT_API_IOS             MediaType = 11
	MediaType_MT_API_WINDOWS_PHONE   MediaType = 12
	MediaType_MT_APP_ANDROID         MediaType = 21
	MediaType_MT_APP_IPHONE          MediaType = 22
	MediaType_MT_APP_J2ME            MediaType = 23
	MediaType_MT_APP_SYMBIAN         MediaType = 24
	MediaType_MT_APP_WINCE           MediaType = 25
	MediaType_MT_APP_WINDOWS_PHONE_7 MediaType = 26
	MediaType_MT_APP_WINDOWS_PHONE   MediaType = 27
	MediaType_MT_APP2_ANDROID        MediaType = 50
	MediaType_MT_APP2_IOS            MediaType = 51
	MediaType_MT_OTHER               MediaType = 100
)

func (p MediaType) String() string {
	switch p {
	case MediaType_MT_UNKNOWN:
		return "MediaType_MT_UNKNOWN"
	case MediaType_MT_WEB_WAP_1:
		return "MediaType_MT_WEB_WAP_1"
	case MediaType_MT_WEB_MOBILE:
		return "MediaType_MT_WEB_MOBILE"
	case MediaType_MT_WEB_WAP_2:
		return "MediaType_MT_WEB_WAP_2"
	case MediaType_MT_API_ANDROID:
		return "MediaType_MT_API_ANDROID"
	case MediaType_MT_API_IOS:
		return "MediaType_MT_API_IOS"
	case MediaType_MT_API_WINDOWS_PHONE:
		return "MediaType_MT_API_WINDOWS_PHONE"
	case MediaType_MT_APP_ANDROID:
		return "MediaType_MT_APP_ANDROID"
	case MediaType_MT_APP_IPHONE:
		return "MediaType_MT_APP_IPHONE"
	case MediaType_MT_APP_J2ME:
		return "MediaType_MT_APP_J2ME"
	case MediaType_MT_APP_SYMBIAN:
		return "MediaType_MT_APP_SYMBIAN"
	case MediaType_MT_APP_WINCE:
		return "MediaType_MT_APP_WINCE"
	case MediaType_MT_APP_WINDOWS_PHONE_7:
		return "MediaType_MT_APP_WINDOWS_PHONE_7"
	case MediaType_MT_APP_WINDOWS_PHONE:
		return "MediaType_MT_APP_WINDOWS_PHONE"
	case MediaType_MT_APP2_ANDROID:
		return "MediaType_MT_APP2_ANDROID"
	case MediaType_MT_APP2_IOS:
		return "MediaType_MT_APP2_IOS"
	case MediaType_MT_OTHER:
		return "MediaType_MT_OTHER"
	}
	return "<UNSET>"
}

func MediaTypeFromString(s string) (MediaType, error) {
	switch s {
	case "MediaType_MT_UNKNOWN":
		return MediaType_MT_UNKNOWN, nil
	case "MediaType_MT_WEB_WAP_1":
		return MediaType_MT_WEB_WAP_1, nil
	case "MediaType_MT_WEB_MOBILE":
		return MediaType_MT_WEB_MOBILE, nil
	case "MediaType_MT_WEB_WAP_2":
		return MediaType_MT_WEB_WAP_2, nil
	case "MediaType_MT_API_ANDROID":
		return MediaType_MT_API_ANDROID, nil
	case "MediaType_MT_API_IOS":
		return MediaType_MT_API_IOS, nil
	case "MediaType_MT_API_WINDOWS_PHONE":
		return MediaType_MT_API_WINDOWS_PHONE, nil
	case "MediaType_MT_APP_ANDROID":
		return MediaType_MT_APP_ANDROID, nil
	case "MediaType_MT_APP_IPHONE":
		return MediaType_MT_APP_IPHONE, nil
	case "MediaType_MT_APP_J2ME":
		return MediaType_MT_APP_J2ME, nil
	case "MediaType_MT_APP_SYMBIAN":
		return MediaType_MT_APP_SYMBIAN, nil
	case "MediaType_MT_APP_WINCE":
		return MediaType_MT_APP_WINCE, nil
	case "MediaType_MT_APP_WINDOWS_PHONE_7":
		return MediaType_MT_APP_WINDOWS_PHONE_7, nil
	case "MediaType_MT_APP_WINDOWS_PHONE":
		return MediaType_MT_APP_WINDOWS_PHONE, nil
	case "MediaType_MT_APP2_ANDROID":
		return MediaType_MT_APP2_ANDROID, nil
	case "MediaType_MT_APP2_IOS":
		return MediaType_MT_APP2_IOS, nil
	case "MediaType_MT_OTHER":
		return MediaType_MT_OTHER, nil
	}
	return MediaType(math.MinInt32 - 1), fmt.Errorf("not a valid MediaType string")
}

//广告策略投放目标媒体类型
type MediaTargetType int64

const (
	MediaTargetType_MTT_UNKNOWN MediaTargetType = 0
	MediaTargetType_MTT_ALL     MediaTargetType = 1
	MediaTargetType_MTT_WEB_ALL MediaTargetType = 2
	MediaTargetType_MTT_APP_ALL MediaTargetType = 3
)

func (p MediaTargetType) String() string {
	switch p {
	case MediaTargetType_MTT_UNKNOWN:
		return "MediaTargetType_MTT_UNKNOWN"
	case MediaTargetType_MTT_ALL:
		return "MediaTargetType_MTT_ALL"
	case MediaTargetType_MTT_WEB_ALL:
		return "MediaTargetType_MTT_WEB_ALL"
	case MediaTargetType_MTT_APP_ALL:
		return "MediaTargetType_MTT_APP_ALL"
	}
	return "<UNSET>"
}

func MediaTargetTypeFromString(s string) (MediaTargetType, error) {
	switch s {
	case "MediaTargetType_MTT_UNKNOWN":
		return MediaTargetType_MTT_UNKNOWN, nil
	case "MediaTargetType_MTT_ALL":
		return MediaTargetType_MTT_ALL, nil
	case "MediaTargetType_MTT_WEB_ALL":
		return MediaTargetType_MTT_WEB_ALL, nil
	case "MediaTargetType_MTT_APP_ALL":
		return MediaTargetType_MTT_APP_ALL, nil
	}
	return MediaTargetType(math.MinInt32 - 1), fmt.Errorf("not a valid MediaTargetType string")
}

//URL在应用程序SDK中的打开位置
type SDKUrlOpenType int64

const (
	SDKUrlOpenType_SUOT_UNKNOWN             SDKUrlOpenType = 0
	SDKUrlOpenType_SUOT_OUTSIDE_APP         SDKUrlOpenType = 1
	SDKUrlOpenType_SUOT_INSIDE_APP_FS       SDKUrlOpenType = 2
	SDKUrlOpenType_SUOT_INSIDE_APP_DLG      SDKUrlOpenType = 3
	SDKUrlOpenType_SUOT_INSIDE_APP_DOWNLOAD SDKUrlOpenType = 4
	SDKUrlOpenType_SUOT_INSIDE_APP_SLIDE    SDKUrlOpenType = 5
	SDKUrlOpenType_SUOT_OPEN_ADWALL         SDKUrlOpenType = 6
)

func (p SDKUrlOpenType) String() string {
	switch p {
	case SDKUrlOpenType_SUOT_UNKNOWN:
		return "SDKUrlOpenType_SUOT_UNKNOWN"
	case SDKUrlOpenType_SUOT_OUTSIDE_APP:
		return "SDKUrlOpenType_SUOT_OUTSIDE_APP"
	case SDKUrlOpenType_SUOT_INSIDE_APP_FS:
		return "SDKUrlOpenType_SUOT_INSIDE_APP_FS"
	case SDKUrlOpenType_SUOT_INSIDE_APP_DLG:
		return "SDKUrlOpenType_SUOT_INSIDE_APP_DLG"
	case SDKUrlOpenType_SUOT_INSIDE_APP_DOWNLOAD:
		return "SDKUrlOpenType_SUOT_INSIDE_APP_DOWNLOAD"
	case SDKUrlOpenType_SUOT_INSIDE_APP_SLIDE:
		return "SDKUrlOpenType_SUOT_INSIDE_APP_SLIDE"
	case SDKUrlOpenType_SUOT_OPEN_ADWALL:
		return "SDKUrlOpenType_SUOT_OPEN_ADWALL"
	}
	return "<UNSET>"
}

func SDKUrlOpenTypeFromString(s string) (SDKUrlOpenType, error) {
	switch s {
	case "SDKUrlOpenType_SUOT_UNKNOWN":
		return SDKUrlOpenType_SUOT_UNKNOWN, nil
	case "SDKUrlOpenType_SUOT_OUTSIDE_APP":
		return SDKUrlOpenType_SUOT_OUTSIDE_APP, nil
	case "SDKUrlOpenType_SUOT_INSIDE_APP_FS":
		return SDKUrlOpenType_SUOT_INSIDE_APP_FS, nil
	case "SDKUrlOpenType_SUOT_INSIDE_APP_DLG":
		return SDKUrlOpenType_SUOT_INSIDE_APP_DLG, nil
	case "SDKUrlOpenType_SUOT_INSIDE_APP_DOWNLOAD":
		return SDKUrlOpenType_SUOT_INSIDE_APP_DOWNLOAD, nil
	case "SDKUrlOpenType_SUOT_INSIDE_APP_SLIDE":
		return SDKUrlOpenType_SUOT_INSIDE_APP_SLIDE, nil
	case "SDKUrlOpenType_SUOT_OPEN_ADWALL":
		return SDKUrlOpenType_SUOT_OPEN_ADWALL, nil
	}
	return SDKUrlOpenType(math.MinInt32 - 1), fmt.Errorf("not a valid SDKUrlOpenType string")
}

//媒体内容分类
type MediaCategory int64

const (
	MediaCategory_MCA_UNKNOWN                MediaCategory = 0
	MediaCategory_MCA_WEB_XWZX               MediaCategory = 10
	MediaCategory_MCA_WEB_SJBK               MediaCategory = 11
	MediaCategory_MCA_WEB_RJXZ               MediaCategory = 12
	MediaCategory_MCA_WEB_SQJY               MediaCategory = 13
	MediaCategory_MCA_WEB_SMKJ               MediaCategory = 14
	MediaCategory_MCA_WEB_ZHMH               MediaCategory = 15
	MediaCategory_MCA_WEB_YYTY               MediaCategory = 16
	MediaCategory_MCA_WEB_DHSS               MediaCategory = 17
	MediaCategory_MCA_WEB_CRLX               MediaCategory = 18
	MediaCategory_MCA_WEB_XXYL               MediaCategory = 19
	MediaCategory_MCA_WEB_WXXS               MediaCategory = 20
	MediaCategory_MCA_WEB_OTHER              MediaCategory = 99
	MediaCategory_MCA_APP_GAME_DZGD          MediaCategory = 100
	MediaCategory_MCA_APP_GAME_XXYZ          MediaCategory = 101
	MediaCategory_MCA_APP_GAME_TYJJ          MediaCategory = 102
	MediaCategory_MCA_APP_GAME_JSBY          MediaCategory = 103
	MediaCategory_MCA_APP_GAME_MNJY          MediaCategory = 104
	MediaCategory_MCA_APP_GAME_OTHER         MediaCategory = 199
	MediaCategory_MCA_APP_SOFTWARE_DMTRJ     MediaCategory = 200
	MediaCategory_MCA_APP_SOFTWARE_AQRJ      MediaCategory = 201
	MediaCategory_MCA_APP_SOFTWARE_WLTX      MediaCategory = 202
	MediaCategory_MCA_APP_SOFTWARE_SYGJ      MediaCategory = 203
	MediaCategory_MCA_APP_SOFTWARE_XTRJ      MediaCategory = 204
	MediaCategory_MCA_APP_SOFTWARE_SHXX      MediaCategory = 205
	MediaCategory_MCA_APP_SOFTWARE_YLXX      MediaCategory = 206
	MediaCategory_MCA_APP_SOFTWARE_DZYD      MediaCategory = 207
	MediaCategory_MCA_APP_SOFTWARE_ZTZM      MediaCategory = 208
	MediaCategory_MCA_APP_SOFTWARE_XWZX      MediaCategory = 209
	MediaCategory_MCA_APP_SOFTWARE_NAV       MediaCategory = 210
	MediaCategory_MCA_APP_SOFTWARE_HEALTH    MediaCategory = 211
	MediaCategory_MCA_APP_SOFTWARE_STUDY     MediaCategory = 212
	MediaCategory_MCA_APP_SOFTWARE_PHOTO     MediaCategory = 213
	MediaCategory_MCA_APP_SOFTWARE_FINANCE   MediaCategory = 214
	MediaCategory_MCA_APP_SOFTWARE_BUSINESS  MediaCategory = 215
	MediaCategory_MCA_APP_SOFTWARE_OTHER     MediaCategory = 299
	MediaCategory_MCA_APP_GAME_UNCATEGORIZED MediaCategory = 300
)

func (p MediaCategory) String() string {
	switch p {
	case MediaCategory_MCA_UNKNOWN:
		return "MediaCategory_MCA_UNKNOWN"
	case MediaCategory_MCA_WEB_XWZX:
		return "MediaCategory_MCA_WEB_XWZX"
	case MediaCategory_MCA_WEB_SJBK:
		return "MediaCategory_MCA_WEB_SJBK"
	case MediaCategory_MCA_WEB_RJXZ:
		return "MediaCategory_MCA_WEB_RJXZ"
	case MediaCategory_MCA_WEB_SQJY:
		return "MediaCategory_MCA_WEB_SQJY"
	case MediaCategory_MCA_WEB_SMKJ:
		return "MediaCategory_MCA_WEB_SMKJ"
	case MediaCategory_MCA_WEB_ZHMH:
		return "MediaCategory_MCA_WEB_ZHMH"
	case MediaCategory_MCA_WEB_YYTY:
		return "MediaCategory_MCA_WEB_YYTY"
	case MediaCategory_MCA_WEB_DHSS:
		return "MediaCategory_MCA_WEB_DHSS"
	case MediaCategory_MCA_WEB_CRLX:
		return "MediaCategory_MCA_WEB_CRLX"
	case MediaCategory_MCA_WEB_XXYL:
		return "MediaCategory_MCA_WEB_XXYL"
	case MediaCategory_MCA_WEB_WXXS:
		return "MediaCategory_MCA_WEB_WXXS"
	case MediaCategory_MCA_WEB_OTHER:
		return "MediaCategory_MCA_WEB_OTHER"
	case MediaCategory_MCA_APP_GAME_DZGD:
		return "MediaCategory_MCA_APP_GAME_DZGD"
	case MediaCategory_MCA_APP_GAME_XXYZ:
		return "MediaCategory_MCA_APP_GAME_XXYZ"
	case MediaCategory_MCA_APP_GAME_TYJJ:
		return "MediaCategory_MCA_APP_GAME_TYJJ"
	case MediaCategory_MCA_APP_GAME_JSBY:
		return "MediaCategory_MCA_APP_GAME_JSBY"
	case MediaCategory_MCA_APP_GAME_MNJY:
		return "MediaCategory_MCA_APP_GAME_MNJY"
	case MediaCategory_MCA_APP_GAME_OTHER:
		return "MediaCategory_MCA_APP_GAME_OTHER"
	case MediaCategory_MCA_APP_SOFTWARE_DMTRJ:
		return "MediaCategory_MCA_APP_SOFTWARE_DMTRJ"
	case MediaCategory_MCA_APP_SOFTWARE_AQRJ:
		return "MediaCategory_MCA_APP_SOFTWARE_AQRJ"
	case MediaCategory_MCA_APP_SOFTWARE_WLTX:
		return "MediaCategory_MCA_APP_SOFTWARE_WLTX"
	case MediaCategory_MCA_APP_SOFTWARE_SYGJ:
		return "MediaCategory_MCA_APP_SOFTWARE_SYGJ"
	case MediaCategory_MCA_APP_SOFTWARE_XTRJ:
		return "MediaCategory_MCA_APP_SOFTWARE_XTRJ"
	case MediaCategory_MCA_APP_SOFTWARE_SHXX:
		return "MediaCategory_MCA_APP_SOFTWARE_SHXX"
	case MediaCategory_MCA_APP_SOFTWARE_YLXX:
		return "MediaCategory_MCA_APP_SOFTWARE_YLXX"
	case MediaCategory_MCA_APP_SOFTWARE_DZYD:
		return "MediaCategory_MCA_APP_SOFTWARE_DZYD"
	case MediaCategory_MCA_APP_SOFTWARE_ZTZM:
		return "MediaCategory_MCA_APP_SOFTWARE_ZTZM"
	case MediaCategory_MCA_APP_SOFTWARE_XWZX:
		return "MediaCategory_MCA_APP_SOFTWARE_XWZX"
	case MediaCategory_MCA_APP_SOFTWARE_NAV:
		return "MediaCategory_MCA_APP_SOFTWARE_NAV"
	case MediaCategory_MCA_APP_SOFTWARE_HEALTH:
		return "MediaCategory_MCA_APP_SOFTWARE_HEALTH"
	case MediaCategory_MCA_APP_SOFTWARE_STUDY:
		return "MediaCategory_MCA_APP_SOFTWARE_STUDY"
	case MediaCategory_MCA_APP_SOFTWARE_PHOTO:
		return "MediaCategory_MCA_APP_SOFTWARE_PHOTO"
	case MediaCategory_MCA_APP_SOFTWARE_FINANCE:
		return "MediaCategory_MCA_APP_SOFTWARE_FINANCE"
	case MediaCategory_MCA_APP_SOFTWARE_BUSINESS:
		return "MediaCategory_MCA_APP_SOFTWARE_BUSINESS"
	case MediaCategory_MCA_APP_SOFTWARE_OTHER:
		return "MediaCategory_MCA_APP_SOFTWARE_OTHER"
	case MediaCategory_MCA_APP_GAME_UNCATEGORIZED:
		return "MediaCategory_MCA_APP_GAME_UNCATEGORIZED"
	}
	return "<UNSET>"
}

func MediaCategoryFromString(s string) (MediaCategory, error) {
	switch s {
	case "MediaCategory_MCA_UNKNOWN":
		return MediaCategory_MCA_UNKNOWN, nil
	case "MediaCategory_MCA_WEB_XWZX":
		return MediaCategory_MCA_WEB_XWZX, nil
	case "MediaCategory_MCA_WEB_SJBK":
		return MediaCategory_MCA_WEB_SJBK, nil
	case "MediaCategory_MCA_WEB_RJXZ":
		return MediaCategory_MCA_WEB_RJXZ, nil
	case "MediaCategory_MCA_WEB_SQJY":
		return MediaCategory_MCA_WEB_SQJY, nil
	case "MediaCategory_MCA_WEB_SMKJ":
		return MediaCategory_MCA_WEB_SMKJ, nil
	case "MediaCategory_MCA_WEB_ZHMH":
		return MediaCategory_MCA_WEB_ZHMH, nil
	case "MediaCategory_MCA_WEB_YYTY":
		return MediaCategory_MCA_WEB_YYTY, nil
	case "MediaCategory_MCA_WEB_DHSS":
		return MediaCategory_MCA_WEB_DHSS, nil
	case "MediaCategory_MCA_WEB_CRLX":
		return MediaCategory_MCA_WEB_CRLX, nil
	case "MediaCategory_MCA_WEB_XXYL":
		return MediaCategory_MCA_WEB_XXYL, nil
	case "MediaCategory_MCA_WEB_WXXS":
		return MediaCategory_MCA_WEB_WXXS, nil
	case "MediaCategory_MCA_WEB_OTHER":
		return MediaCategory_MCA_WEB_OTHER, nil
	case "MediaCategory_MCA_APP_GAME_DZGD":
		return MediaCategory_MCA_APP_GAME_DZGD, nil
	case "MediaCategory_MCA_APP_GAME_XXYZ":
		return MediaCategory_MCA_APP_GAME_XXYZ, nil
	case "MediaCategory_MCA_APP_GAME_TYJJ":
		return MediaCategory_MCA_APP_GAME_TYJJ, nil
	case "MediaCategory_MCA_APP_GAME_JSBY":
		return MediaCategory_MCA_APP_GAME_JSBY, nil
	case "MediaCategory_MCA_APP_GAME_MNJY":
		return MediaCategory_MCA_APP_GAME_MNJY, nil
	case "MediaCategory_MCA_APP_GAME_OTHER":
		return MediaCategory_MCA_APP_GAME_OTHER, nil
	case "MediaCategory_MCA_APP_SOFTWARE_DMTRJ":
		return MediaCategory_MCA_APP_SOFTWARE_DMTRJ, nil
	case "MediaCategory_MCA_APP_SOFTWARE_AQRJ":
		return MediaCategory_MCA_APP_SOFTWARE_AQRJ, nil
	case "MediaCategory_MCA_APP_SOFTWARE_WLTX":
		return MediaCategory_MCA_APP_SOFTWARE_WLTX, nil
	case "MediaCategory_MCA_APP_SOFTWARE_SYGJ":
		return MediaCategory_MCA_APP_SOFTWARE_SYGJ, nil
	case "MediaCategory_MCA_APP_SOFTWARE_XTRJ":
		return MediaCategory_MCA_APP_SOFTWARE_XTRJ, nil
	case "MediaCategory_MCA_APP_SOFTWARE_SHXX":
		return MediaCategory_MCA_APP_SOFTWARE_SHXX, nil
	case "MediaCategory_MCA_APP_SOFTWARE_YLXX":
		return MediaCategory_MCA_APP_SOFTWARE_YLXX, nil
	case "MediaCategory_MCA_APP_SOFTWARE_DZYD":
		return MediaCategory_MCA_APP_SOFTWARE_DZYD, nil
	case "MediaCategory_MCA_APP_SOFTWARE_ZTZM":
		return MediaCategory_MCA_APP_SOFTWARE_ZTZM, nil
	case "MediaCategory_MCA_APP_SOFTWARE_XWZX":
		return MediaCategory_MCA_APP_SOFTWARE_XWZX, nil
	case "MediaCategory_MCA_APP_SOFTWARE_NAV":
		return MediaCategory_MCA_APP_SOFTWARE_NAV, nil
	case "MediaCategory_MCA_APP_SOFTWARE_HEALTH":
		return MediaCategory_MCA_APP_SOFTWARE_HEALTH, nil
	case "MediaCategory_MCA_APP_SOFTWARE_STUDY":
		return MediaCategory_MCA_APP_SOFTWARE_STUDY, nil
	case "MediaCategory_MCA_APP_SOFTWARE_PHOTO":
		return MediaCategory_MCA_APP_SOFTWARE_PHOTO, nil
	case "MediaCategory_MCA_APP_SOFTWARE_FINANCE":
		return MediaCategory_MCA_APP_SOFTWARE_FINANCE, nil
	case "MediaCategory_MCA_APP_SOFTWARE_BUSINESS":
		return MediaCategory_MCA_APP_SOFTWARE_BUSINESS, nil
	case "MediaCategory_MCA_APP_SOFTWARE_OTHER":
		return MediaCategory_MCA_APP_SOFTWARE_OTHER, nil
	case "MediaCategory_MCA_APP_GAME_UNCATEGORIZED":
		return MediaCategory_MCA_APP_GAME_UNCATEGORIZED, nil
	}
	return MediaCategory(math.MinInt32 - 1), fmt.Errorf("not a valid MediaCategory string")
}

//媒体测试模式设置
type MediaTestModeSetting int64

const (
	MediaTestModeSetting_MTM_DEFAULT MediaTestModeSetting = 0
	MediaTestModeSetting_MTM_OFF     MediaTestModeSetting = 1
)

func (p MediaTestModeSetting) String() string {
	switch p {
	case MediaTestModeSetting_MTM_DEFAULT:
		return "MediaTestModeSetting_MTM_DEFAULT"
	case MediaTestModeSetting_MTM_OFF:
		return "MediaTestModeSetting_MTM_OFF"
	}
	return "<UNSET>"
}

func MediaTestModeSettingFromString(s string) (MediaTestModeSetting, error) {
	switch s {
	case "MediaTestModeSetting_MTM_DEFAULT":
		return MediaTestModeSetting_MTM_DEFAULT, nil
	case "MediaTestModeSetting_MTM_OFF":
		return MediaTestModeSetting_MTM_OFF, nil
	}
	return MediaTestModeSetting(math.MinInt32 - 1), fmt.Errorf("not a valid MediaTestModeSetting string")
}

//@Description("账户状态")
type AccountStatus int64

const (
	AccountStatus_AS_UNKNOWN       AccountStatus = 0
	AccountStatus_AS_NOT_ACTIVATED AccountStatus = 1
	AccountStatus_AS_NORMAL        AccountStatus = 2
	AccountStatus_AS_BANNED        AccountStatus = 3
)

func (p AccountStatus) String() string {
	switch p {
	case AccountStatus_AS_UNKNOWN:
		return "AccountStatus_AS_UNKNOWN"
	case AccountStatus_AS_NOT_ACTIVATED:
		return "AccountStatus_AS_NOT_ACTIVATED"
	case AccountStatus_AS_NORMAL:
		return "AccountStatus_AS_NORMAL"
	case AccountStatus_AS_BANNED:
		return "AccountStatus_AS_BANNED"
	}
	return "<UNSET>"
}

func AccountStatusFromString(s string) (AccountStatus, error) {
	switch s {
	case "AccountStatus_AS_UNKNOWN":
		return AccountStatus_AS_UNKNOWN, nil
	case "AccountStatus_AS_NOT_ACTIVATED":
		return AccountStatus_AS_NOT_ACTIVATED, nil
	case "AccountStatus_AS_NORMAL":
		return AccountStatus_AS_NORMAL, nil
	case "AccountStatus_AS_BANNED":
		return AccountStatus_AS_BANNED, nil
	}
	return AccountStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AccountStatus string")
}

//@Description("用户的账户分类：个人/公司，等")
type AccountCategory int64

const (
	AccountCategory_AC_UNKNOWN  AccountCategory = 0
	AccountCategory_AC_PERSONAL AccountCategory = 1
	AccountCategory_AC_CORP     AccountCategory = 2
	AccountCategory_AC_OTHER    AccountCategory = 10
)

func (p AccountCategory) String() string {
	switch p {
	case AccountCategory_AC_UNKNOWN:
		return "AccountCategory_AC_UNKNOWN"
	case AccountCategory_AC_PERSONAL:
		return "AccountCategory_AC_PERSONAL"
	case AccountCategory_AC_CORP:
		return "AccountCategory_AC_CORP"
	case AccountCategory_AC_OTHER:
		return "AccountCategory_AC_OTHER"
	}
	return "<UNSET>"
}

func AccountCategoryFromString(s string) (AccountCategory, error) {
	switch s {
	case "AccountCategory_AC_UNKNOWN":
		return AccountCategory_AC_UNKNOWN, nil
	case "AccountCategory_AC_PERSONAL":
		return AccountCategory_AC_PERSONAL, nil
	case "AccountCategory_AC_CORP":
		return AccountCategory_AC_CORP, nil
	case "AccountCategory_AC_OTHER":
		return AccountCategory_AC_OTHER, nil
	}
	return AccountCategory(math.MinInt32 - 1), fmt.Errorf("not a valid AccountCategory string")
}

//@Description("支付方式，目前仅针对提现")
type PaymentType int64

const (
	PaymentType_PT_UNKNOWN    PaymentType = 0
	PaymentType_PT_UNIONPAY   PaymentType = 1
	PaymentType_PT_REMITTANCE PaymentType = 2
	PaymentType_PT_TRANSFER   PaymentType = 3
)

func (p PaymentType) String() string {
	switch p {
	case PaymentType_PT_UNKNOWN:
		return "PaymentType_PT_UNKNOWN"
	case PaymentType_PT_UNIONPAY:
		return "PaymentType_PT_UNIONPAY"
	case PaymentType_PT_REMITTANCE:
		return "PaymentType_PT_REMITTANCE"
	case PaymentType_PT_TRANSFER:
		return "PaymentType_PT_TRANSFER"
	}
	return "<UNSET>"
}

func PaymentTypeFromString(s string) (PaymentType, error) {
	switch s {
	case "PaymentType_PT_UNKNOWN":
		return PaymentType_PT_UNKNOWN, nil
	case "PaymentType_PT_UNIONPAY":
		return PaymentType_PT_UNIONPAY, nil
	case "PaymentType_PT_REMITTANCE":
		return PaymentType_PT_REMITTANCE, nil
	case "PaymentType_PT_TRANSFER":
		return PaymentType_PT_TRANSFER, nil
	}
	return PaymentType(math.MinInt32 - 1), fmt.Errorf("not a valid PaymentType string")
}

//@Description("流量类别，目前特指消费发生的方向")
type TrafficType int64

const (
	TrafficType_TRAFFIC_UNKNOWN TrafficType = 0
	TrafficType_TRAFFIC_INFLOW  TrafficType = 1
	TrafficType_TRAFFIC_OUTFLOW TrafficType = 2
	TrafficType_TRAFFIC_INNER   TrafficType = 3
	TrafficType_TRAFFIC_OUTER   TrafficType = 4
)

func (p TrafficType) String() string {
	switch p {
	case TrafficType_TRAFFIC_UNKNOWN:
		return "TrafficType_TRAFFIC_UNKNOWN"
	case TrafficType_TRAFFIC_INFLOW:
		return "TrafficType_TRAFFIC_INFLOW"
	case TrafficType_TRAFFIC_OUTFLOW:
		return "TrafficType_TRAFFIC_OUTFLOW"
	case TrafficType_TRAFFIC_INNER:
		return "TrafficType_TRAFFIC_INNER"
	case TrafficType_TRAFFIC_OUTER:
		return "TrafficType_TRAFFIC_OUTER"
	}
	return "<UNSET>"
}

func TrafficTypeFromString(s string) (TrafficType, error) {
	switch s {
	case "TrafficType_TRAFFIC_UNKNOWN":
		return TrafficType_TRAFFIC_UNKNOWN, nil
	case "TrafficType_TRAFFIC_INFLOW":
		return TrafficType_TRAFFIC_INFLOW, nil
	case "TrafficType_TRAFFIC_OUTFLOW":
		return TrafficType_TRAFFIC_OUTFLOW, nil
	case "TrafficType_TRAFFIC_INNER":
		return TrafficType_TRAFFIC_INNER, nil
	case "TrafficType_TRAFFIC_OUTER":
		return TrafficType_TRAFFIC_OUTER, nil
	}
	return TrafficType(math.MinInt32 - 1), fmt.Errorf("not a valid TrafficType string")
}

//@Description("图片类型")
type ImageType int64

const (
	ImageType_IT_UNKNOWN ImageType = 0
	ImageType_IT_PNG     ImageType = 1
	ImageType_IT_GIF     ImageType = 2
	ImageType_IT_JPG     ImageType = 3
)

func (p ImageType) String() string {
	switch p {
	case ImageType_IT_UNKNOWN:
		return "ImageType_IT_UNKNOWN"
	case ImageType_IT_PNG:
		return "ImageType_IT_PNG"
	case ImageType_IT_GIF:
		return "ImageType_IT_GIF"
	case ImageType_IT_JPG:
		return "ImageType_IT_JPG"
	}
	return "<UNSET>"
}

func ImageTypeFromString(s string) (ImageType, error) {
	switch s {
	case "ImageType_IT_UNKNOWN":
		return ImageType_IT_UNKNOWN, nil
	case "ImageType_IT_PNG":
		return ImageType_IT_PNG, nil
	case "ImageType_IT_GIF":
		return ImageType_IT_GIF, nil
	case "ImageType_IT_JPG":
		return ImageType_IT_JPG, nil
	}
	return ImageType(math.MinInt32 - 1), fmt.Errorf("not a valid ImageType string")
}

//能力类型
//可能会用到该能力集合的模块：
// 1. 机型、能力识别时，识别出的结果是一个能力的集合，AS与AdServer交互时向AdServer提供这些能力的集合
// 2. 从适当的时候起，广告库导出时给检索时，某些广告显式要求的能力集合标记将使用这些定义，而不再通过ActionType
type CapabilityType int64

const (
	CapabilityType_CT_HTML                            CapabilityType = 1001
	CapabilityType_CT_WAP                             CapabilityType = 1002
	CapabilityType_CT_HTML_OR_WAP                     CapabilityType = 1003
	CapabilityType_CT_SMS                             CapabilityType = 2001
	CapabilityType_CT_MAIL                            CapabilityType = 2002
	CapabilityType_CT_MAP                             CapabilityType = 2003
	CapabilityType_CT_CALL                            CapabilityType = 2004
	CapabilityType_CT_AUDIO                           CapabilityType = 2005
	CapabilityType_CT_VIDEO                           CapabilityType = 2006
	CapabilityType_CT_RESOURCE_CACHE                  CapabilityType = 2007
	CapabilityType_CT_FREQ_CONTROL                    CapabilityType = 2008
	CapabilityType_CT_PASSBOOK                        CapabilityType = 2009
	CapabilityType_CT_BLOW                            CapabilityType = 2010
	CapabilityType_CT_CLICK_TO_VIDEO                  CapabilityType = 2011
	CapabilityType_CT_VIDEO_PLAY_ONLINE               CapabilityType = 2012
	CapabilityType_CT_MARKET                          CapabilityType = 3001
	CapabilityType_CT_GIF                             CapabilityType = 4001
	CapabilityType_CT_PNG                             CapabilityType = 4002
	CapabilityType_CT_JPG                             CapabilityType = 4003
	CapabilityType_CT_FS_IMAGE                        CapabilityType = 5001
	CapabilityType_CT_TEXT                            CapabilityType = 6000
	CapabilityType_CT_LONG_TEXT                       CapabilityType = 6001
	CapabilityType_CT_TEXT_IMAGE                      CapabilityType = 6002
	CapabilityType_CT_ACTION_APP2                     CapabilityType = 7001
	CapabilityType_CT_INAPP_DOWNLOAD                  CapabilityType = 7051
	CapabilityType_CT_LAUNCH_APP                      CapabilityType = 7052
	CapabilityType_CT_INAPP_INSTALL                   CapabilityType = 7053
	CapabilityType_CT_APP_INSTALL_EVENT_REPORT        CapabilityType = 7060
	CapabilityType_CT_APP_DOWNLOAD_EVENT_REPORT       CapabilityType = 7061
	CapabilityType_CT_RENDER_HTML_VIEW                CapabilityType = 8001
	CapabilityType_CT_RENDER_HQ_HTML_VIEW             CapabilityType = 8002
	CapabilityType_CT_EXPANDABLE                      CapabilityType = 8010
	CapabilityType_CT_ROTATE                          CapabilityType = 8011
	CapabilityType_CT_SPLASH_INTERSTITIAL             CapabilityType = 8012
	CapabilityType_CT_CUSTOMIZED_CLOSE_BUTTON         CapabilityType = 8013
	CapabilityType_CT_RICHMEDIA_MRAID_1               CapabilityType = 9001
	CapabilityType_CT_INAPP_REDIRECT_REPORT           CapabilityType = 10001
	CapabilityType_CT_OUTAPP_REDIRECT_REPORT          CapabilityType = 10002
	CapabilityType_CT_OUTAPP_DEFAULT_BROWSER          CapabilityType = 10003
	CapabilityType_CT_OUTAPP_DEFAULT_BROWSER_REDIRECT CapabilityType = 10004
	CapabilityType_CT_INAPP_BROWSER                   CapabilityType = 10005
	CapabilityType_CT_CPM_SUPPORT                     CapabilityType = 10006
	CapabilityType_CT_INAPP_TEL                       CapabilityType = 10007
	CapabilityType_CT_URL_SPECIAL_CHAR                CapabilityType = 10008
	CapabilityType_CT_INAPP_URL_SPECIAL_CHAR          CapabilityType = 10009
	CapabilityType_CT_OUTAPP_URL_SPECIAL_CHAR         CapabilityType = 10010
	CapabilityType_CT_IMP_REDIRECT_REPORT             CapabilityType = 10011
	CapabilityType_CT_TRACKING_MAC                    CapabilityType = 10020
	CapabilityType_CT_TRACKING_MACMD5                 CapabilityType = 10021
	CapabilityType_CT_TRACKING_IMEI                   CapabilityType = 10022
	CapabilityType_CT_TRACKING_ANDROID_ID             CapabilityType = 10023
	CapabilityType_CT_TRACKING_OPEN_UDID              CapabilityType = 10024
	CapabilityType_CT_TRACKING_IDFA                   CapabilityType = 10025
	CapabilityType_CT_TRACKING_IDFA_OR_MAC            CapabilityType = 10026
	CapabilityType_CT_OPEN_ADWALL                     CapabilityType = 11001
)

func (p CapabilityType) String() string {
	switch p {
	case CapabilityType_CT_HTML:
		return "CapabilityType_CT_HTML"
	case CapabilityType_CT_WAP:
		return "CapabilityType_CT_WAP"
	case CapabilityType_CT_HTML_OR_WAP:
		return "CapabilityType_CT_HTML_OR_WAP"
	case CapabilityType_CT_SMS:
		return "CapabilityType_CT_SMS"
	case CapabilityType_CT_MAIL:
		return "CapabilityType_CT_MAIL"
	case CapabilityType_CT_MAP:
		return "CapabilityType_CT_MAP"
	case CapabilityType_CT_CALL:
		return "CapabilityType_CT_CALL"
	case CapabilityType_CT_AUDIO:
		return "CapabilityType_CT_AUDIO"
	case CapabilityType_CT_VIDEO:
		return "CapabilityType_CT_VIDEO"
	case CapabilityType_CT_RESOURCE_CACHE:
		return "CapabilityType_CT_RESOURCE_CACHE"
	case CapabilityType_CT_FREQ_CONTROL:
		return "CapabilityType_CT_FREQ_CONTROL"
	case CapabilityType_CT_PASSBOOK:
		return "CapabilityType_CT_PASSBOOK"
	case CapabilityType_CT_BLOW:
		return "CapabilityType_CT_BLOW"
	case CapabilityType_CT_CLICK_TO_VIDEO:
		return "CapabilityType_CT_CLICK_TO_VIDEO"
	case CapabilityType_CT_VIDEO_PLAY_ONLINE:
		return "CapabilityType_CT_VIDEO_PLAY_ONLINE"
	case CapabilityType_CT_MARKET:
		return "CapabilityType_CT_MARKET"
	case CapabilityType_CT_GIF:
		return "CapabilityType_CT_GIF"
	case CapabilityType_CT_PNG:
		return "CapabilityType_CT_PNG"
	case CapabilityType_CT_JPG:
		return "CapabilityType_CT_JPG"
	case CapabilityType_CT_FS_IMAGE:
		return "CapabilityType_CT_FS_IMAGE"
	case CapabilityType_CT_TEXT:
		return "CapabilityType_CT_TEXT"
	case CapabilityType_CT_LONG_TEXT:
		return "CapabilityType_CT_LONG_TEXT"
	case CapabilityType_CT_TEXT_IMAGE:
		return "CapabilityType_CT_TEXT_IMAGE"
	case CapabilityType_CT_ACTION_APP2:
		return "CapabilityType_CT_ACTION_APP2"
	case CapabilityType_CT_INAPP_DOWNLOAD:
		return "CapabilityType_CT_INAPP_DOWNLOAD"
	case CapabilityType_CT_LAUNCH_APP:
		return "CapabilityType_CT_LAUNCH_APP"
	case CapabilityType_CT_INAPP_INSTALL:
		return "CapabilityType_CT_INAPP_INSTALL"
	case CapabilityType_CT_APP_INSTALL_EVENT_REPORT:
		return "CapabilityType_CT_APP_INSTALL_EVENT_REPORT"
	case CapabilityType_CT_APP_DOWNLOAD_EVENT_REPORT:
		return "CapabilityType_CT_APP_DOWNLOAD_EVENT_REPORT"
	case CapabilityType_CT_RENDER_HTML_VIEW:
		return "CapabilityType_CT_RENDER_HTML_VIEW"
	case CapabilityType_CT_RENDER_HQ_HTML_VIEW:
		return "CapabilityType_CT_RENDER_HQ_HTML_VIEW"
	case CapabilityType_CT_EXPANDABLE:
		return "CapabilityType_CT_EXPANDABLE"
	case CapabilityType_CT_ROTATE:
		return "CapabilityType_CT_ROTATE"
	case CapabilityType_CT_SPLASH_INTERSTITIAL:
		return "CapabilityType_CT_SPLASH_INTERSTITIAL"
	case CapabilityType_CT_CUSTOMIZED_CLOSE_BUTTON:
		return "CapabilityType_CT_CUSTOMIZED_CLOSE_BUTTON"
	case CapabilityType_CT_RICHMEDIA_MRAID_1:
		return "CapabilityType_CT_RICHMEDIA_MRAID_1"
	case CapabilityType_CT_INAPP_REDIRECT_REPORT:
		return "CapabilityType_CT_INAPP_REDIRECT_REPORT"
	case CapabilityType_CT_OUTAPP_REDIRECT_REPORT:
		return "CapabilityType_CT_OUTAPP_REDIRECT_REPORT"
	case CapabilityType_CT_OUTAPP_DEFAULT_BROWSER:
		return "CapabilityType_CT_OUTAPP_DEFAULT_BROWSER"
	case CapabilityType_CT_OUTAPP_DEFAULT_BROWSER_REDIRECT:
		return "CapabilityType_CT_OUTAPP_DEFAULT_BROWSER_REDIRECT"
	case CapabilityType_CT_INAPP_BROWSER:
		return "CapabilityType_CT_INAPP_BROWSER"
	case CapabilityType_CT_CPM_SUPPORT:
		return "CapabilityType_CT_CPM_SUPPORT"
	case CapabilityType_CT_INAPP_TEL:
		return "CapabilityType_CT_INAPP_TEL"
	case CapabilityType_CT_URL_SPECIAL_CHAR:
		return "CapabilityType_CT_URL_SPECIAL_CHAR"
	case CapabilityType_CT_INAPP_URL_SPECIAL_CHAR:
		return "CapabilityType_CT_INAPP_URL_SPECIAL_CHAR"
	case CapabilityType_CT_OUTAPP_URL_SPECIAL_CHAR:
		return "CapabilityType_CT_OUTAPP_URL_SPECIAL_CHAR"
	case CapabilityType_CT_IMP_REDIRECT_REPORT:
		return "CapabilityType_CT_IMP_REDIRECT_REPORT"
	case CapabilityType_CT_TRACKING_MAC:
		return "CapabilityType_CT_TRACKING_MAC"
	case CapabilityType_CT_TRACKING_MACMD5:
		return "CapabilityType_CT_TRACKING_MACMD5"
	case CapabilityType_CT_TRACKING_IMEI:
		return "CapabilityType_CT_TRACKING_IMEI"
	case CapabilityType_CT_TRACKING_ANDROID_ID:
		return "CapabilityType_CT_TRACKING_ANDROID_ID"
	case CapabilityType_CT_TRACKING_OPEN_UDID:
		return "CapabilityType_CT_TRACKING_OPEN_UDID"
	case CapabilityType_CT_TRACKING_IDFA:
		return "CapabilityType_CT_TRACKING_IDFA"
	case CapabilityType_CT_TRACKING_IDFA_OR_MAC:
		return "CapabilityType_CT_TRACKING_IDFA_OR_MAC"
	case CapabilityType_CT_OPEN_ADWALL:
		return "CapabilityType_CT_OPEN_ADWALL"
	}
	return "<UNSET>"
}

func CapabilityTypeFromString(s string) (CapabilityType, error) {
	switch s {
	case "CapabilityType_CT_HTML":
		return CapabilityType_CT_HTML, nil
	case "CapabilityType_CT_WAP":
		return CapabilityType_CT_WAP, nil
	case "CapabilityType_CT_HTML_OR_WAP":
		return CapabilityType_CT_HTML_OR_WAP, nil
	case "CapabilityType_CT_SMS":
		return CapabilityType_CT_SMS, nil
	case "CapabilityType_CT_MAIL":
		return CapabilityType_CT_MAIL, nil
	case "CapabilityType_CT_MAP":
		return CapabilityType_CT_MAP, nil
	case "CapabilityType_CT_CALL":
		return CapabilityType_CT_CALL, nil
	case "CapabilityType_CT_AUDIO":
		return CapabilityType_CT_AUDIO, nil
	case "CapabilityType_CT_VIDEO":
		return CapabilityType_CT_VIDEO, nil
	case "CapabilityType_CT_RESOURCE_CACHE":
		return CapabilityType_CT_RESOURCE_CACHE, nil
	case "CapabilityType_CT_FREQ_CONTROL":
		return CapabilityType_CT_FREQ_CONTROL, nil
	case "CapabilityType_CT_PASSBOOK":
		return CapabilityType_CT_PASSBOOK, nil
	case "CapabilityType_CT_BLOW":
		return CapabilityType_CT_BLOW, nil
	case "CapabilityType_CT_CLICK_TO_VIDEO":
		return CapabilityType_CT_CLICK_TO_VIDEO, nil
	case "CapabilityType_CT_VIDEO_PLAY_ONLINE":
		return CapabilityType_CT_VIDEO_PLAY_ONLINE, nil
	case "CapabilityType_CT_MARKET":
		return CapabilityType_CT_MARKET, nil
	case "CapabilityType_CT_GIF":
		return CapabilityType_CT_GIF, nil
	case "CapabilityType_CT_PNG":
		return CapabilityType_CT_PNG, nil
	case "CapabilityType_CT_JPG":
		return CapabilityType_CT_JPG, nil
	case "CapabilityType_CT_FS_IMAGE":
		return CapabilityType_CT_FS_IMAGE, nil
	case "CapabilityType_CT_TEXT":
		return CapabilityType_CT_TEXT, nil
	case "CapabilityType_CT_LONG_TEXT":
		return CapabilityType_CT_LONG_TEXT, nil
	case "CapabilityType_CT_TEXT_IMAGE":
		return CapabilityType_CT_TEXT_IMAGE, nil
	case "CapabilityType_CT_ACTION_APP2":
		return CapabilityType_CT_ACTION_APP2, nil
	case "CapabilityType_CT_INAPP_DOWNLOAD":
		return CapabilityType_CT_INAPP_DOWNLOAD, nil
	case "CapabilityType_CT_LAUNCH_APP":
		return CapabilityType_CT_LAUNCH_APP, nil
	case "CapabilityType_CT_INAPP_INSTALL":
		return CapabilityType_CT_INAPP_INSTALL, nil
	case "CapabilityType_CT_APP_INSTALL_EVENT_REPORT":
		return CapabilityType_CT_APP_INSTALL_EVENT_REPORT, nil
	case "CapabilityType_CT_APP_DOWNLOAD_EVENT_REPORT":
		return CapabilityType_CT_APP_DOWNLOAD_EVENT_REPORT, nil
	case "CapabilityType_CT_RENDER_HTML_VIEW":
		return CapabilityType_CT_RENDER_HTML_VIEW, nil
	case "CapabilityType_CT_RENDER_HQ_HTML_VIEW":
		return CapabilityType_CT_RENDER_HQ_HTML_VIEW, nil
	case "CapabilityType_CT_EXPANDABLE":
		return CapabilityType_CT_EXPANDABLE, nil
	case "CapabilityType_CT_ROTATE":
		return CapabilityType_CT_ROTATE, nil
	case "CapabilityType_CT_SPLASH_INTERSTITIAL":
		return CapabilityType_CT_SPLASH_INTERSTITIAL, nil
	case "CapabilityType_CT_CUSTOMIZED_CLOSE_BUTTON":
		return CapabilityType_CT_CUSTOMIZED_CLOSE_BUTTON, nil
	case "CapabilityType_CT_RICHMEDIA_MRAID_1":
		return CapabilityType_CT_RICHMEDIA_MRAID_1, nil
	case "CapabilityType_CT_INAPP_REDIRECT_REPORT":
		return CapabilityType_CT_INAPP_REDIRECT_REPORT, nil
	case "CapabilityType_CT_OUTAPP_REDIRECT_REPORT":
		return CapabilityType_CT_OUTAPP_REDIRECT_REPORT, nil
	case "CapabilityType_CT_OUTAPP_DEFAULT_BROWSER":
		return CapabilityType_CT_OUTAPP_DEFAULT_BROWSER, nil
	case "CapabilityType_CT_OUTAPP_DEFAULT_BROWSER_REDIRECT":
		return CapabilityType_CT_OUTAPP_DEFAULT_BROWSER_REDIRECT, nil
	case "CapabilityType_CT_INAPP_BROWSER":
		return CapabilityType_CT_INAPP_BROWSER, nil
	case "CapabilityType_CT_CPM_SUPPORT":
		return CapabilityType_CT_CPM_SUPPORT, nil
	case "CapabilityType_CT_INAPP_TEL":
		return CapabilityType_CT_INAPP_TEL, nil
	case "CapabilityType_CT_URL_SPECIAL_CHAR":
		return CapabilityType_CT_URL_SPECIAL_CHAR, nil
	case "CapabilityType_CT_INAPP_URL_SPECIAL_CHAR":
		return CapabilityType_CT_INAPP_URL_SPECIAL_CHAR, nil
	case "CapabilityType_CT_OUTAPP_URL_SPECIAL_CHAR":
		return CapabilityType_CT_OUTAPP_URL_SPECIAL_CHAR, nil
	case "CapabilityType_CT_IMP_REDIRECT_REPORT":
		return CapabilityType_CT_IMP_REDIRECT_REPORT, nil
	case "CapabilityType_CT_TRACKING_MAC":
		return CapabilityType_CT_TRACKING_MAC, nil
	case "CapabilityType_CT_TRACKING_MACMD5":
		return CapabilityType_CT_TRACKING_MACMD5, nil
	case "CapabilityType_CT_TRACKING_IMEI":
		return CapabilityType_CT_TRACKING_IMEI, nil
	case "CapabilityType_CT_TRACKING_ANDROID_ID":
		return CapabilityType_CT_TRACKING_ANDROID_ID, nil
	case "CapabilityType_CT_TRACKING_OPEN_UDID":
		return CapabilityType_CT_TRACKING_OPEN_UDID, nil
	case "CapabilityType_CT_TRACKING_IDFA":
		return CapabilityType_CT_TRACKING_IDFA, nil
	case "CapabilityType_CT_TRACKING_IDFA_OR_MAC":
		return CapabilityType_CT_TRACKING_IDFA_OR_MAC, nil
	case "CapabilityType_CT_OPEN_ADWALL":
		return CapabilityType_CT_OPEN_ADWALL, nil
	}
	return CapabilityType(math.MinInt32 - 1), fmt.Errorf("not a valid CapabilityType string")
}

//SDK的协议版本
type SDKProtocolVersion int64

const (
	SDKProtocolVersion_SPV_UNKNOWN        SDKProtocolVersion = 0
	SDKProtocolVersion_SPV_20101220_V_1_0 SDKProtocolVersion = 1
	SDKProtocolVersion_SPV_20110513_V_1_1 SDKProtocolVersion = 2
	SDKProtocolVersion_SPV_20110615_V_1_2 SDKProtocolVersion = 3
)

func (p SDKProtocolVersion) String() string {
	switch p {
	case SDKProtocolVersion_SPV_UNKNOWN:
		return "SDKProtocolVersion_SPV_UNKNOWN"
	case SDKProtocolVersion_SPV_20101220_V_1_0:
		return "SDKProtocolVersion_SPV_20101220_V_1_0"
	case SDKProtocolVersion_SPV_20110513_V_1_1:
		return "SDKProtocolVersion_SPV_20110513_V_1_1"
	case SDKProtocolVersion_SPV_20110615_V_1_2:
		return "SDKProtocolVersion_SPV_20110615_V_1_2"
	}
	return "<UNSET>"
}

func SDKProtocolVersionFromString(s string) (SDKProtocolVersion, error) {
	switch s {
	case "SDKProtocolVersion_SPV_UNKNOWN":
		return SDKProtocolVersion_SPV_UNKNOWN, nil
	case "SDKProtocolVersion_SPV_20101220_V_1_0":
		return SDKProtocolVersion_SPV_20101220_V_1_0, nil
	case "SDKProtocolVersion_SPV_20110513_V_1_1":
		return SDKProtocolVersion_SPV_20110513_V_1_1, nil
	case "SDKProtocolVersion_SPV_20110615_V_1_2":
		return SDKProtocolVersion_SPV_20110615_V_1_2, nil
	}
	return SDKProtocolVersion(math.MinInt32 - 1), fmt.Errorf("not a valid SDKProtocolVersion string")
}

//SDK的平台类型
type SDKPlatform int64

const (
	SDKPlatform_SDK_PLATFORM_UNKNOWN  SDKPlatform = 0
	SDKPlatform_SDK_PLATFORM_ANDROID  SDKPlatform = 1
	SDKPlatform_SDK_PLATFORM_IOS      SDKPlatform = 2
	SDKPlatform_SDK_PLATFORM_SYMBIAN  SDKPlatform = 3
	SDKPlatform_SDK_PLATFORM_WINPHONE SDKPlatform = 4
)

func (p SDKPlatform) String() string {
	switch p {
	case SDKPlatform_SDK_PLATFORM_UNKNOWN:
		return "SDKPlatform_SDK_PLATFORM_UNKNOWN"
	case SDKPlatform_SDK_PLATFORM_ANDROID:
		return "SDKPlatform_SDK_PLATFORM_ANDROID"
	case SDKPlatform_SDK_PLATFORM_IOS:
		return "SDKPlatform_SDK_PLATFORM_IOS"
	case SDKPlatform_SDK_PLATFORM_SYMBIAN:
		return "SDKPlatform_SDK_PLATFORM_SYMBIAN"
	case SDKPlatform_SDK_PLATFORM_WINPHONE:
		return "SDKPlatform_SDK_PLATFORM_WINPHONE"
	}
	return "<UNSET>"
}

func SDKPlatformFromString(s string) (SDKPlatform, error) {
	switch s {
	case "SDKPlatform_SDK_PLATFORM_UNKNOWN":
		return SDKPlatform_SDK_PLATFORM_UNKNOWN, nil
	case "SDKPlatform_SDK_PLATFORM_ANDROID":
		return SDKPlatform_SDK_PLATFORM_ANDROID, nil
	case "SDKPlatform_SDK_PLATFORM_IOS":
		return SDKPlatform_SDK_PLATFORM_IOS, nil
	case "SDKPlatform_SDK_PLATFORM_SYMBIAN":
		return SDKPlatform_SDK_PLATFORM_SYMBIAN, nil
	case "SDKPlatform_SDK_PLATFORM_WINPHONE":
		return SDKPlatform_SDK_PLATFORM_WINPHONE, nil
	}
	return SDKPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid SDKPlatform string")
}

//SDK的版本
type SDKVersion int64

const (
	SDKVersion_SDK_VERSION_UNKNOWN SDKVersion = 0
	SDKVersion_SDK_VERSION_1_0_0   SDKVersion = 10000
	SDKVersion_SDK_VERSION_1_1_0   SDKVersion = 10100
	SDKVersion_SDK_VERSION_1_2_0   SDKVersion = 10200
	SDKVersion_SDK_VERSION_1_3_0   SDKVersion = 10300
	SDKVersion_SDK_VERSION_1_4_0   SDKVersion = 10400
	SDKVersion_SDK_VERSION_1_5_0   SDKVersion = 10500
	SDKVersion_SDK_VERSION_2_0_0   SDKVersion = 20000
)

func (p SDKVersion) String() string {
	switch p {
	case SDKVersion_SDK_VERSION_UNKNOWN:
		return "SDKVersion_SDK_VERSION_UNKNOWN"
	case SDKVersion_SDK_VERSION_1_0_0:
		return "SDKVersion_SDK_VERSION_1_0_0"
	case SDKVersion_SDK_VERSION_1_1_0:
		return "SDKVersion_SDK_VERSION_1_1_0"
	case SDKVersion_SDK_VERSION_1_2_0:
		return "SDKVersion_SDK_VERSION_1_2_0"
	case SDKVersion_SDK_VERSION_1_3_0:
		return "SDKVersion_SDK_VERSION_1_3_0"
	case SDKVersion_SDK_VERSION_1_4_0:
		return "SDKVersion_SDK_VERSION_1_4_0"
	case SDKVersion_SDK_VERSION_1_5_0:
		return "SDKVersion_SDK_VERSION_1_5_0"
	case SDKVersion_SDK_VERSION_2_0_0:
		return "SDKVersion_SDK_VERSION_2_0_0"
	}
	return "<UNSET>"
}

func SDKVersionFromString(s string) (SDKVersion, error) {
	switch s {
	case "SDKVersion_SDK_VERSION_UNKNOWN":
		return SDKVersion_SDK_VERSION_UNKNOWN, nil
	case "SDKVersion_SDK_VERSION_1_0_0":
		return SDKVersion_SDK_VERSION_1_0_0, nil
	case "SDKVersion_SDK_VERSION_1_1_0":
		return SDKVersion_SDK_VERSION_1_1_0, nil
	case "SDKVersion_SDK_VERSION_1_2_0":
		return SDKVersion_SDK_VERSION_1_2_0, nil
	case "SDKVersion_SDK_VERSION_1_3_0":
		return SDKVersion_SDK_VERSION_1_3_0, nil
	case "SDKVersion_SDK_VERSION_1_4_0":
		return SDKVersion_SDK_VERSION_1_4_0, nil
	case "SDKVersion_SDK_VERSION_1_5_0":
		return SDKVersion_SDK_VERSION_1_5_0, nil
	case "SDKVersion_SDK_VERSION_2_0_0":
		return SDKVersion_SDK_VERSION_2_0_0, nil
	}
	return SDKVersion(math.MinInt32 - 1), fmt.Errorf("not a valid SDKVersion string")
}

//身份证件类型
type IdentityType int64

const (
	IdentityType_IDT_UNKNOWN  IdentityType = 0
	IdentityType_IDT_IDENTITY IdentityType = 1
	IdentityType_IDT_PASSPORT IdentityType = 2
)

func (p IdentityType) String() string {
	switch p {
	case IdentityType_IDT_UNKNOWN:
		return "IdentityType_IDT_UNKNOWN"
	case IdentityType_IDT_IDENTITY:
		return "IdentityType_IDT_IDENTITY"
	case IdentityType_IDT_PASSPORT:
		return "IdentityType_IDT_PASSPORT"
	}
	return "<UNSET>"
}

func IdentityTypeFromString(s string) (IdentityType, error) {
	switch s {
	case "IdentityType_IDT_UNKNOWN":
		return IdentityType_IDT_UNKNOWN, nil
	case "IdentityType_IDT_IDENTITY":
		return IdentityType_IDT_IDENTITY, nil
	case "IdentityType_IDT_PASSPORT":
		return IdentityType_IDT_PASSPORT, nil
	}
	return IdentityType(math.MinInt32 - 1), fmt.Errorf("not a valid IdentityType string")
}

//一般资源的审核状态
type CommonAuditStatus int64

const (
	CommonAuditStatus_CAS_UNKNOWN      CommonAuditStatus = 0
	CommonAuditStatus_CAS_UNSUBMITED   CommonAuditStatus = 1
	CommonAuditStatus_CAS_IN_AUDIT     CommonAuditStatus = 2
	CommonAuditStatus_CAS_AUDIT_REJECT CommonAuditStatus = 3
	CommonAuditStatus_CAS_AUDIT_PASS   CommonAuditStatus = 4
	CommonAuditStatus_CAS_FORBIDDEN    CommonAuditStatus = 5
)

func (p CommonAuditStatus) String() string {
	switch p {
	case CommonAuditStatus_CAS_UNKNOWN:
		return "CommonAuditStatus_CAS_UNKNOWN"
	case CommonAuditStatus_CAS_UNSUBMITED:
		return "CommonAuditStatus_CAS_UNSUBMITED"
	case CommonAuditStatus_CAS_IN_AUDIT:
		return "CommonAuditStatus_CAS_IN_AUDIT"
	case CommonAuditStatus_CAS_AUDIT_REJECT:
		return "CommonAuditStatus_CAS_AUDIT_REJECT"
	case CommonAuditStatus_CAS_AUDIT_PASS:
		return "CommonAuditStatus_CAS_AUDIT_PASS"
	case CommonAuditStatus_CAS_FORBIDDEN:
		return "CommonAuditStatus_CAS_FORBIDDEN"
	}
	return "<UNSET>"
}

func CommonAuditStatusFromString(s string) (CommonAuditStatus, error) {
	switch s {
	case "CommonAuditStatus_CAS_UNKNOWN":
		return CommonAuditStatus_CAS_UNKNOWN, nil
	case "CommonAuditStatus_CAS_UNSUBMITED":
		return CommonAuditStatus_CAS_UNSUBMITED, nil
	case "CommonAuditStatus_CAS_IN_AUDIT":
		return CommonAuditStatus_CAS_IN_AUDIT, nil
	case "CommonAuditStatus_CAS_AUDIT_REJECT":
		return CommonAuditStatus_CAS_AUDIT_REJECT, nil
	case "CommonAuditStatus_CAS_AUDIT_PASS":
		return CommonAuditStatus_CAS_AUDIT_PASS, nil
	case "CommonAuditStatus_CAS_FORBIDDEN":
		return CommonAuditStatus_CAS_FORBIDDEN, nil
	}
	return CommonAuditStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CommonAuditStatus string")
}

//广告渲染方式
type AdRenderType int64

const (
	AdRenderType_ART_TRADITIONAL AdRenderType = 0
	AdRenderType_ART_HTML_SIMPLE AdRenderType = 2
	AdRenderType_ART_HTML_NORMAL AdRenderType = 3
)

func (p AdRenderType) String() string {
	switch p {
	case AdRenderType_ART_TRADITIONAL:
		return "AdRenderType_ART_TRADITIONAL"
	case AdRenderType_ART_HTML_SIMPLE:
		return "AdRenderType_ART_HTML_SIMPLE"
	case AdRenderType_ART_HTML_NORMAL:
		return "AdRenderType_ART_HTML_NORMAL"
	}
	return "<UNSET>"
}

func AdRenderTypeFromString(s string) (AdRenderType, error) {
	switch s {
	case "AdRenderType_ART_TRADITIONAL":
		return AdRenderType_ART_TRADITIONAL, nil
	case "AdRenderType_ART_HTML_SIMPLE":
		return AdRenderType_ART_HTML_SIMPLE, nil
	case "AdRenderType_ART_HTML_NORMAL":
		return AdRenderType_ART_HTML_NORMAL, nil
	}
	return AdRenderType(math.MinInt32 - 1), fmt.Errorf("not a valid AdRenderType string")
}

//标准HTMl广告模板编号
//仅用于常量表示，程序中应使用枚举相应的值即可
type HtmlTemplateCode int64

const (
	HtmlTemplateCode_HTC_NONE  HtmlTemplateCode = 0
	HtmlTemplateCode_HTC_BLUE  HtmlTemplateCode = 301
	HtmlTemplateCode_HTC_BLACK HtmlTemplateCode = 302
	HtmlTemplateCode_HTC_PINK  HtmlTemplateCode = 303
)

func (p HtmlTemplateCode) String() string {
	switch p {
	case HtmlTemplateCode_HTC_NONE:
		return "HtmlTemplateCode_HTC_NONE"
	case HtmlTemplateCode_HTC_BLUE:
		return "HtmlTemplateCode_HTC_BLUE"
	case HtmlTemplateCode_HTC_BLACK:
		return "HtmlTemplateCode_HTC_BLACK"
	case HtmlTemplateCode_HTC_PINK:
		return "HtmlTemplateCode_HTC_PINK"
	}
	return "<UNSET>"
}

func HtmlTemplateCodeFromString(s string) (HtmlTemplateCode, error) {
	switch s {
	case "HtmlTemplateCode_HTC_NONE":
		return HtmlTemplateCode_HTC_NONE, nil
	case "HtmlTemplateCode_HTC_BLUE":
		return HtmlTemplateCode_HTC_BLUE, nil
	case "HtmlTemplateCode_HTC_BLACK":
		return HtmlTemplateCode_HTC_BLACK, nil
	case "HtmlTemplateCode_HTC_PINK":
		return HtmlTemplateCode_HTC_PINK, nil
	}
	return HtmlTemplateCode(math.MinInt32 - 1), fmt.Errorf("not a valid HtmlTemplateCode string")
}

//iOS的越狱状况，越狱状况投放限制时也用同一套
type JailBreakCode int64

const (
	JailBreakCode_JBS_UNKNOWN  JailBreakCode = 0
	JailBreakCode_JBS_ALL      JailBreakCode = 1
	JailBreakCode_JBS_DONE     JailBreakCode = 2
	JailBreakCode_JBS_NOT_DONE JailBreakCode = 3
	JailBreakCode_JBS_NOT_SET  JailBreakCode = 4
)

func (p JailBreakCode) String() string {
	switch p {
	case JailBreakCode_JBS_UNKNOWN:
		return "JailBreakCode_JBS_UNKNOWN"
	case JailBreakCode_JBS_ALL:
		return "JailBreakCode_JBS_ALL"
	case JailBreakCode_JBS_DONE:
		return "JailBreakCode_JBS_DONE"
	case JailBreakCode_JBS_NOT_DONE:
		return "JailBreakCode_JBS_NOT_DONE"
	case JailBreakCode_JBS_NOT_SET:
		return "JailBreakCode_JBS_NOT_SET"
	}
	return "<UNSET>"
}

func JailBreakCodeFromString(s string) (JailBreakCode, error) {
	switch s {
	case "JailBreakCode_JBS_UNKNOWN":
		return JailBreakCode_JBS_UNKNOWN, nil
	case "JailBreakCode_JBS_ALL":
		return JailBreakCode_JBS_ALL, nil
	case "JailBreakCode_JBS_DONE":
		return JailBreakCode_JBS_DONE, nil
	case "JailBreakCode_JBS_NOT_DONE":
		return JailBreakCode_JBS_NOT_DONE, nil
	case "JailBreakCode_JBS_NOT_SET":
		return JailBreakCode_JBS_NOT_SET, nil
	}
	return JailBreakCode(math.MinInt32 - 1), fmt.Errorf("not a valid JailBreakCode string")
}

//domob水印的位置
type WaterMarkPosition int64

const (
	WaterMarkPosition_WMP_UNKNOWN    WaterMarkPosition = 0
	WaterMarkPosition_WMP_RIGHT_DOWN WaterMarkPosition = 1
	WaterMarkPosition_WMP_LEFT_DOWN  WaterMarkPosition = 2
	WaterMarkPosition_WMP_LEFT_TOP   WaterMarkPosition = 3
	WaterMarkPosition_WMP_RIGHT_TOP  WaterMarkPosition = 4
)

func (p WaterMarkPosition) String() string {
	switch p {
	case WaterMarkPosition_WMP_UNKNOWN:
		return "WaterMarkPosition_WMP_UNKNOWN"
	case WaterMarkPosition_WMP_RIGHT_DOWN:
		return "WaterMarkPosition_WMP_RIGHT_DOWN"
	case WaterMarkPosition_WMP_LEFT_DOWN:
		return "WaterMarkPosition_WMP_LEFT_DOWN"
	case WaterMarkPosition_WMP_LEFT_TOP:
		return "WaterMarkPosition_WMP_LEFT_TOP"
	case WaterMarkPosition_WMP_RIGHT_TOP:
		return "WaterMarkPosition_WMP_RIGHT_TOP"
	}
	return "<UNSET>"
}

func WaterMarkPositionFromString(s string) (WaterMarkPosition, error) {
	switch s {
	case "WaterMarkPosition_WMP_UNKNOWN":
		return WaterMarkPosition_WMP_UNKNOWN, nil
	case "WaterMarkPosition_WMP_RIGHT_DOWN":
		return WaterMarkPosition_WMP_RIGHT_DOWN, nil
	case "WaterMarkPosition_WMP_LEFT_DOWN":
		return WaterMarkPosition_WMP_LEFT_DOWN, nil
	case "WaterMarkPosition_WMP_LEFT_TOP":
		return WaterMarkPosition_WMP_LEFT_TOP, nil
	case "WaterMarkPosition_WMP_RIGHT_TOP":
		return WaterMarkPosition_WMP_RIGHT_TOP, nil
	}
	return WaterMarkPosition(math.MinInt32 - 1), fmt.Errorf("not a valid WaterMarkPosition string")
}

//弹出式广告关闭按钮位置
type CloseButtonPosition int64

const (
	CloseButtonPosition_CBP_UNKNOWN               CloseButtonPosition = 0
	CloseButtonPosition_CBP_LEFT_TOP              CloseButtonPosition = 1
	CloseButtonPosition_CBP_RIGHT_TOP             CloseButtonPosition = 2
	CloseButtonPosition_CBP_DO_NOT_DISPLAY        CloseButtonPosition = 5
	CloseButtonPosition_CBP_INCREATIVE_CUSTOMIZED CloseButtonPosition = 6
)

func (p CloseButtonPosition) String() string {
	switch p {
	case CloseButtonPosition_CBP_UNKNOWN:
		return "CloseButtonPosition_CBP_UNKNOWN"
	case CloseButtonPosition_CBP_LEFT_TOP:
		return "CloseButtonPosition_CBP_LEFT_TOP"
	case CloseButtonPosition_CBP_RIGHT_TOP:
		return "CloseButtonPosition_CBP_RIGHT_TOP"
	case CloseButtonPosition_CBP_DO_NOT_DISPLAY:
		return "CloseButtonPosition_CBP_DO_NOT_DISPLAY"
	case CloseButtonPosition_CBP_INCREATIVE_CUSTOMIZED:
		return "CloseButtonPosition_CBP_INCREATIVE_CUSTOMIZED"
	}
	return "<UNSET>"
}

func CloseButtonPositionFromString(s string) (CloseButtonPosition, error) {
	switch s {
	case "CloseButtonPosition_CBP_UNKNOWN":
		return CloseButtonPosition_CBP_UNKNOWN, nil
	case "CloseButtonPosition_CBP_LEFT_TOP":
		return CloseButtonPosition_CBP_LEFT_TOP, nil
	case "CloseButtonPosition_CBP_RIGHT_TOP":
		return CloseButtonPosition_CBP_RIGHT_TOP, nil
	case "CloseButtonPosition_CBP_DO_NOT_DISPLAY":
		return CloseButtonPosition_CBP_DO_NOT_DISPLAY, nil
	case "CloseButtonPosition_CBP_INCREATIVE_CUSTOMIZED":
		return CloseButtonPosition_CBP_INCREATIVE_CUSTOMIZED, nil
	}
	return CloseButtonPosition(math.MinInt32 - 1), fmt.Errorf("not a valid CloseButtonPosition string")
}

//富媒体标准编码
type RichMediaCode int64

const (
	RichMediaCode_RMC_UNKNOWN RichMediaCode = 0
	RichMediaCode_RMC_ALL     RichMediaCode = 1
	RichMediaCode_RMC_MRAID_1 RichMediaCode = 10100
	RichMediaCode_RMC_MRAID_2 RichMediaCode = 10200
)

func (p RichMediaCode) String() string {
	switch p {
	case RichMediaCode_RMC_UNKNOWN:
		return "RichMediaCode_RMC_UNKNOWN"
	case RichMediaCode_RMC_ALL:
		return "RichMediaCode_RMC_ALL"
	case RichMediaCode_RMC_MRAID_1:
		return "RichMediaCode_RMC_MRAID_1"
	case RichMediaCode_RMC_MRAID_2:
		return "RichMediaCode_RMC_MRAID_2"
	}
	return "<UNSET>"
}

func RichMediaCodeFromString(s string) (RichMediaCode, error) {
	switch s {
	case "RichMediaCode_RMC_UNKNOWN":
		return RichMediaCode_RMC_UNKNOWN, nil
	case "RichMediaCode_RMC_ALL":
		return RichMediaCode_RMC_ALL, nil
	case "RichMediaCode_RMC_MRAID_1":
		return RichMediaCode_RMC_MRAID_1, nil
	case "RichMediaCode_RMC_MRAID_2":
		return RichMediaCode_RMC_MRAID_2, nil
	}
	return RichMediaCode(math.MinInt32 - 1), fmt.Errorf("not a valid RichMediaCode string")
}

//广告数据数据来源类型
type AdFeedbackSource int64

const (
	AdFeedbackSource_AFS_UNKNOWN            AdFeedbackSource = 0
	AdFeedbackSource_AFS_APP_MIS            AdFeedbackSource = 100
	AdFeedbackSource_AFS_APP_TRACKER_SDK    AdFeedbackSource = 200
	AdFeedbackSource_AFS_APP_THIRD_PLATFORM AdFeedbackSource = 300
	AdFeedbackSource_AFS_LANDING_REGISTER   AdFeedbackSource = 400
	AdFeedbackSource_AFS_LANDING_CALL       AdFeedbackSource = 500
)

func (p AdFeedbackSource) String() string {
	switch p {
	case AdFeedbackSource_AFS_UNKNOWN:
		return "AdFeedbackSource_AFS_UNKNOWN"
	case AdFeedbackSource_AFS_APP_MIS:
		return "AdFeedbackSource_AFS_APP_MIS"
	case AdFeedbackSource_AFS_APP_TRACKER_SDK:
		return "AdFeedbackSource_AFS_APP_TRACKER_SDK"
	case AdFeedbackSource_AFS_APP_THIRD_PLATFORM:
		return "AdFeedbackSource_AFS_APP_THIRD_PLATFORM"
	case AdFeedbackSource_AFS_LANDING_REGISTER:
		return "AdFeedbackSource_AFS_LANDING_REGISTER"
	case AdFeedbackSource_AFS_LANDING_CALL:
		return "AdFeedbackSource_AFS_LANDING_CALL"
	}
	return "<UNSET>"
}

func AdFeedbackSourceFromString(s string) (AdFeedbackSource, error) {
	switch s {
	case "AdFeedbackSource_AFS_UNKNOWN":
		return AdFeedbackSource_AFS_UNKNOWN, nil
	case "AdFeedbackSource_AFS_APP_MIS":
		return AdFeedbackSource_AFS_APP_MIS, nil
	case "AdFeedbackSource_AFS_APP_TRACKER_SDK":
		return AdFeedbackSource_AFS_APP_TRACKER_SDK, nil
	case "AdFeedbackSource_AFS_APP_THIRD_PLATFORM":
		return AdFeedbackSource_AFS_APP_THIRD_PLATFORM, nil
	case "AdFeedbackSource_AFS_LANDING_REGISTER":
		return AdFeedbackSource_AFS_LANDING_REGISTER, nil
	case "AdFeedbackSource_AFS_LANDING_CALL":
		return AdFeedbackSource_AFS_LANDING_CALL, nil
	}
	return AdFeedbackSource(math.MinInt32 - 1), fmt.Errorf("not a valid AdFeedbackSource string")
}

//预算类型，按什么控制预算
type BudgetType int64

const (
	BudgetType_BT_UNKNOWN    BudgetType = 0
	BudgetType_BT_CLICK      BudgetType = 1
	BudgetType_BT_IMP_REPORT BudgetType = 2
	BudgetType_BT_DOWNLOAD   BudgetType = 4
	BudgetType_BT_INSTALL    BudgetType = 5
	BudgetType_BT_IMP        BudgetType = 6
	BudgetType_BT_CONSUME    BudgetType = 7
)

func (p BudgetType) String() string {
	switch p {
	case BudgetType_BT_UNKNOWN:
		return "BudgetType_BT_UNKNOWN"
	case BudgetType_BT_CLICK:
		return "BudgetType_BT_CLICK"
	case BudgetType_BT_IMP_REPORT:
		return "BudgetType_BT_IMP_REPORT"
	case BudgetType_BT_DOWNLOAD:
		return "BudgetType_BT_DOWNLOAD"
	case BudgetType_BT_INSTALL:
		return "BudgetType_BT_INSTALL"
	case BudgetType_BT_IMP:
		return "BudgetType_BT_IMP"
	case BudgetType_BT_CONSUME:
		return "BudgetType_BT_CONSUME"
	}
	return "<UNSET>"
}

func BudgetTypeFromString(s string) (BudgetType, error) {
	switch s {
	case "BudgetType_BT_UNKNOWN":
		return BudgetType_BT_UNKNOWN, nil
	case "BudgetType_BT_CLICK":
		return BudgetType_BT_CLICK, nil
	case "BudgetType_BT_IMP_REPORT":
		return BudgetType_BT_IMP_REPORT, nil
	case "BudgetType_BT_DOWNLOAD":
		return BudgetType_BT_DOWNLOAD, nil
	case "BudgetType_BT_INSTALL":
		return BudgetType_BT_INSTALL, nil
	case "BudgetType_BT_IMP":
		return BudgetType_BT_IMP, nil
	case "BudgetType_BT_CONSUME":
		return BudgetType_BT_CONSUME, nil
	}
	return BudgetType(math.MinInt32 - 1), fmt.Errorf("not a valid BudgetType string")
}

//预算标识，1表示当前预算不可用，已过期，2表示可用状态
type BudgetWhetherAvailable int64

const (
	BudgetWhetherAvailable_BUDGET_OVER     BudgetWhetherAvailable = 1
	BudgetWhetherAvailable_BUDGET_NOT_OVER BudgetWhetherAvailable = 0
)

func (p BudgetWhetherAvailable) String() string {
	switch p {
	case BudgetWhetherAvailable_BUDGET_OVER:
		return "BudgetWhetherAvailable_BUDGET_OVER"
	case BudgetWhetherAvailable_BUDGET_NOT_OVER:
		return "BudgetWhetherAvailable_BUDGET_NOT_OVER"
	}
	return "<UNSET>"
}

func BudgetWhetherAvailableFromString(s string) (BudgetWhetherAvailable, error) {
	switch s {
	case "BudgetWhetherAvailable_BUDGET_OVER":
		return BudgetWhetherAvailable_BUDGET_OVER, nil
	case "BudgetWhetherAvailable_BUDGET_NOT_OVER":
		return BudgetWhetherAvailable_BUDGET_NOT_OVER, nil
	}
	return BudgetWhetherAvailable(math.MinInt32 - 1), fmt.Errorf("not a valid BudgetWhetherAvailable string")
}

//状态标识，标识当前的数据是否可用，0表示可用，1表示已删除
type StatusWhetherAvailable int64

const (
	StatusWhetherAvailable_STATUS_RUNNABLE StatusWhetherAvailable = 0
	StatusWhetherAvailable_STATUS_DELETED  StatusWhetherAvailable = 1
)

func (p StatusWhetherAvailable) String() string {
	switch p {
	case StatusWhetherAvailable_STATUS_RUNNABLE:
		return "StatusWhetherAvailable_STATUS_RUNNABLE"
	case StatusWhetherAvailable_STATUS_DELETED:
		return "StatusWhetherAvailable_STATUS_DELETED"
	}
	return "<UNSET>"
}

func StatusWhetherAvailableFromString(s string) (StatusWhetherAvailable, error) {
	switch s {
	case "StatusWhetherAvailable_STATUS_RUNNABLE":
		return StatusWhetherAvailable_STATUS_RUNNABLE, nil
	case "StatusWhetherAvailable_STATUS_DELETED":
		return StatusWhetherAvailable_STATUS_DELETED, nil
	}
	return StatusWhetherAvailable(math.MinInt32 - 1), fmt.Errorf("not a valid StatusWhetherAvailable string")
}

//频次控制的一个周期定义
type FreqCycleType int64

const (
	FreqCycleType_FOREVER FreqCycleType = 0
	FreqCycleType_HOUR    FreqCycleType = 1
	FreqCycleType_DAY     FreqCycleType = 2
	FreqCycleType_WEEK    FreqCycleType = 3
	FreqCycleType_MONTH   FreqCycleType = 4
)

func (p FreqCycleType) String() string {
	switch p {
	case FreqCycleType_FOREVER:
		return "FreqCycleType_FOREVER"
	case FreqCycleType_HOUR:
		return "FreqCycleType_HOUR"
	case FreqCycleType_DAY:
		return "FreqCycleType_DAY"
	case FreqCycleType_WEEK:
		return "FreqCycleType_WEEK"
	case FreqCycleType_MONTH:
		return "FreqCycleType_MONTH"
	}
	return "<UNSET>"
}

func FreqCycleTypeFromString(s string) (FreqCycleType, error) {
	switch s {
	case "FreqCycleType_FOREVER":
		return FreqCycleType_FOREVER, nil
	case "FreqCycleType_HOUR":
		return FreqCycleType_HOUR, nil
	case "FreqCycleType_DAY":
		return FreqCycleType_DAY, nil
	case "FreqCycleType_WEEK":
		return FreqCycleType_WEEK, nil
	case "FreqCycleType_MONTH":
		return FreqCycleType_MONTH, nil
	}
	return FreqCycleType(math.MinInt32 - 1), fmt.Errorf("not a valid FreqCycleType string")
}

//资源物料类型
type ResourceType int64

const (
	ResourceType_STREAMING_VIDEO ResourceType = 1
	ResourceType_MP4_VIDEO       ResourceType = 2
	ResourceType_AUDIO           ResourceType = 3
	ResourceType_IMAGE           ResourceType = 4
	ResourceType_APK             ResourceType = 5
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_STREAMING_VIDEO:
		return "ResourceType_STREAMING_VIDEO"
	case ResourceType_MP4_VIDEO:
		return "ResourceType_MP4_VIDEO"
	case ResourceType_AUDIO:
		return "ResourceType_AUDIO"
	case ResourceType_IMAGE:
		return "ResourceType_IMAGE"
	case ResourceType_APK:
		return "ResourceType_APK"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "ResourceType_STREAMING_VIDEO":
		return ResourceType_STREAMING_VIDEO, nil
	case "ResourceType_MP4_VIDEO":
		return ResourceType_MP4_VIDEO, nil
	case "ResourceType_AUDIO":
		return ResourceType_AUDIO, nil
	case "ResourceType_IMAGE":
		return ResourceType_IMAGE, nil
	case "ResourceType_APK":
		return ResourceType_APK, nil
	}
	return ResourceType(math.MinInt32 - 1), fmt.Errorf("not a valid ResourceType string")
}

//广告未填充原因
type AdEmptyType int64

const (
	AdEmptyType_NO_EMPTY             AdEmptyType = 0
	AdEmptyType_UNKONW_ERROR         AdEmptyType = 1
	AdEmptyType_REQUEST_ERROR        AdEmptyType = 2
	AdEmptyType_MEIDA_ERROR          AdEmptyType = 13
	AdEmptyType_APP_WALL_EMPTY       AdEmptyType = 3
	AdEmptyType_OLD_APP_WALL_EMPTY   AdEmptyType = 4
	AdEmptyType_HOUSE_AD_FILTER      AdEmptyType = 5
	AdEmptyType_STRATEGY_EMPTY       AdEmptyType = 6
	AdEmptyType_STRATEGY_TAG_FILTER  AdEmptyType = 7
	AdEmptyType_CREATIVE_EMPTY       AdEmptyType = 8
	AdEmptyType_PACKAGE_FILTER       AdEmptyType = 9
	AdEmptyType_OPTIMIZE_FILTER      AdEmptyType = 10
	AdEmptyType_CLICK_FILTER         AdEmptyType = 11
	AdEmptyType_STRATEGY_POST_FILTER AdEmptyType = 12
)

func (p AdEmptyType) String() string {
	switch p {
	case AdEmptyType_NO_EMPTY:
		return "AdEmptyType_NO_EMPTY"
	case AdEmptyType_UNKONW_ERROR:
		return "AdEmptyType_UNKONW_ERROR"
	case AdEmptyType_REQUEST_ERROR:
		return "AdEmptyType_REQUEST_ERROR"
	case AdEmptyType_MEIDA_ERROR:
		return "AdEmptyType_MEIDA_ERROR"
	case AdEmptyType_APP_WALL_EMPTY:
		return "AdEmptyType_APP_WALL_EMPTY"
	case AdEmptyType_OLD_APP_WALL_EMPTY:
		return "AdEmptyType_OLD_APP_WALL_EMPTY"
	case AdEmptyType_HOUSE_AD_FILTER:
		return "AdEmptyType_HOUSE_AD_FILTER"
	case AdEmptyType_STRATEGY_EMPTY:
		return "AdEmptyType_STRATEGY_EMPTY"
	case AdEmptyType_STRATEGY_TAG_FILTER:
		return "AdEmptyType_STRATEGY_TAG_FILTER"
	case AdEmptyType_CREATIVE_EMPTY:
		return "AdEmptyType_CREATIVE_EMPTY"
	case AdEmptyType_PACKAGE_FILTER:
		return "AdEmptyType_PACKAGE_FILTER"
	case AdEmptyType_OPTIMIZE_FILTER:
		return "AdEmptyType_OPTIMIZE_FILTER"
	case AdEmptyType_CLICK_FILTER:
		return "AdEmptyType_CLICK_FILTER"
	case AdEmptyType_STRATEGY_POST_FILTER:
		return "AdEmptyType_STRATEGY_POST_FILTER"
	}
	return "<UNSET>"
}

func AdEmptyTypeFromString(s string) (AdEmptyType, error) {
	switch s {
	case "AdEmptyType_NO_EMPTY":
		return AdEmptyType_NO_EMPTY, nil
	case "AdEmptyType_UNKONW_ERROR":
		return AdEmptyType_UNKONW_ERROR, nil
	case "AdEmptyType_REQUEST_ERROR":
		return AdEmptyType_REQUEST_ERROR, nil
	case "AdEmptyType_MEIDA_ERROR":
		return AdEmptyType_MEIDA_ERROR, nil
	case "AdEmptyType_APP_WALL_EMPTY":
		return AdEmptyType_APP_WALL_EMPTY, nil
	case "AdEmptyType_OLD_APP_WALL_EMPTY":
		return AdEmptyType_OLD_APP_WALL_EMPTY, nil
	case "AdEmptyType_HOUSE_AD_FILTER":
		return AdEmptyType_HOUSE_AD_FILTER, nil
	case "AdEmptyType_STRATEGY_EMPTY":
		return AdEmptyType_STRATEGY_EMPTY, nil
	case "AdEmptyType_STRATEGY_TAG_FILTER":
		return AdEmptyType_STRATEGY_TAG_FILTER, nil
	case "AdEmptyType_CREATIVE_EMPTY":
		return AdEmptyType_CREATIVE_EMPTY, nil
	case "AdEmptyType_PACKAGE_FILTER":
		return AdEmptyType_PACKAGE_FILTER, nil
	case "AdEmptyType_OPTIMIZE_FILTER":
		return AdEmptyType_OPTIMIZE_FILTER, nil
	case "AdEmptyType_CLICK_FILTER":
		return AdEmptyType_CLICK_FILTER, nil
	case "AdEmptyType_STRATEGY_POST_FILTER":
		return AdEmptyType_STRATEGY_POST_FILTER, nil
	}
	return AdEmptyType(math.MinInt32 - 1), fmt.Errorf("not a valid AdEmptyType string")
}

//合作公司，用于区分个人开发者通过哪家公司进行个税计算。目前有DOMOB和MSN两家。
type CooperateCompany int64

const (
	CooperateCompany_CCM_UNKNOWN CooperateCompany = 0
	CooperateCompany_CCM_DOMOB   CooperateCompany = 1
	CooperateCompany_CCM_MSN     CooperateCompany = 2
)

func (p CooperateCompany) String() string {
	switch p {
	case CooperateCompany_CCM_UNKNOWN:
		return "CooperateCompany_CCM_UNKNOWN"
	case CooperateCompany_CCM_DOMOB:
		return "CooperateCompany_CCM_DOMOB"
	case CooperateCompany_CCM_MSN:
		return "CooperateCompany_CCM_MSN"
	}
	return "<UNSET>"
}

func CooperateCompanyFromString(s string) (CooperateCompany, error) {
	switch s {
	case "CooperateCompany_CCM_UNKNOWN":
		return CooperateCompany_CCM_UNKNOWN, nil
	case "CooperateCompany_CCM_DOMOB":
		return CooperateCompany_CCM_DOMOB, nil
	case "CooperateCompany_CCM_MSN":
		return CooperateCompany_CCM_MSN, nil
	}
	return CooperateCompany(math.MinInt32 - 1), fmt.Errorf("not a valid CooperateCompany string")
}

//广告图标上显示的角标类型
type AdSubscriptType int64

const (
	AdSubscriptType_SUBS_NEW       AdSubscriptType = 1
	AdSubscriptType_SUBS_RECOMMEND AdSubscriptType = 2
	AdSubscriptType_SUBS_FREE      AdSubscriptType = 3
)

func (p AdSubscriptType) String() string {
	switch p {
	case AdSubscriptType_SUBS_NEW:
		return "AdSubscriptType_SUBS_NEW"
	case AdSubscriptType_SUBS_RECOMMEND:
		return "AdSubscriptType_SUBS_RECOMMEND"
	case AdSubscriptType_SUBS_FREE:
		return "AdSubscriptType_SUBS_FREE"
	}
	return "<UNSET>"
}

func AdSubscriptTypeFromString(s string) (AdSubscriptType, error) {
	switch s {
	case "AdSubscriptType_SUBS_NEW":
		return AdSubscriptType_SUBS_NEW, nil
	case "AdSubscriptType_SUBS_RECOMMEND":
		return AdSubscriptType_SUBS_RECOMMEND, nil
	case "AdSubscriptType_SUBS_FREE":
		return AdSubscriptType_SUBS_FREE, nil
	}
	return AdSubscriptType(math.MinInt32 - 1), fmt.Errorf("not a valid AdSubscriptType string")
}

type PauseStatus int64

const (
	PauseStatus_PAUS_RUNNING PauseStatus = 0
	PauseStatus_PAUS_PAUSED  PauseStatus = 1
)

func (p PauseStatus) String() string {
	switch p {
	case PauseStatus_PAUS_RUNNING:
		return "PauseStatus_PAUS_RUNNING"
	case PauseStatus_PAUS_PAUSED:
		return "PauseStatus_PAUS_PAUSED"
	}
	return "<UNSET>"
}

func PauseStatusFromString(s string) (PauseStatus, error) {
	switch s {
	case "PauseStatus_PAUS_RUNNING":
		return PauseStatus_PAUS_RUNNING, nil
	case "PauseStatus_PAUS_PAUSED":
		return PauseStatus_PAUS_PAUSED, nil
	}
	return PauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PauseStatus string")
}

//容器标识,containerFlags
type ContainerFlags int64

const (
	ContainerFlags_FLAG_DEFAULT           ContainerFlags = 0
	ContainerFlags_FLAG_APP               ContainerFlags = 1
	ContainerFlags_FLAG_GAME              ContainerFlags = 2
	ContainerFlags_FLAG_INTERVAL          ContainerFlags = 3
	ContainerFlags_FLAG_IOS_GC_DEFAULT    ContainerFlags = 4
	ContainerFlags_FLAG_IOS_GC_RECOMMEND  ContainerFlags = 5
	ContainerFlags_FLAG_IOS_GC_RELAXATION ContainerFlags = 6
	ContainerFlags_FLAG_IOS_GC_SUPERIOR   ContainerFlags = 7
)

func (p ContainerFlags) String() string {
	switch p {
	case ContainerFlags_FLAG_DEFAULT:
		return "ContainerFlags_FLAG_DEFAULT"
	case ContainerFlags_FLAG_APP:
		return "ContainerFlags_FLAG_APP"
	case ContainerFlags_FLAG_GAME:
		return "ContainerFlags_FLAG_GAME"
	case ContainerFlags_FLAG_INTERVAL:
		return "ContainerFlags_FLAG_INTERVAL"
	case ContainerFlags_FLAG_IOS_GC_DEFAULT:
		return "ContainerFlags_FLAG_IOS_GC_DEFAULT"
	case ContainerFlags_FLAG_IOS_GC_RECOMMEND:
		return "ContainerFlags_FLAG_IOS_GC_RECOMMEND"
	case ContainerFlags_FLAG_IOS_GC_RELAXATION:
		return "ContainerFlags_FLAG_IOS_GC_RELAXATION"
	case ContainerFlags_FLAG_IOS_GC_SUPERIOR:
		return "ContainerFlags_FLAG_IOS_GC_SUPERIOR"
	}
	return "<UNSET>"
}

func ContainerFlagsFromString(s string) (ContainerFlags, error) {
	switch s {
	case "ContainerFlags_FLAG_DEFAULT":
		return ContainerFlags_FLAG_DEFAULT, nil
	case "ContainerFlags_FLAG_APP":
		return ContainerFlags_FLAG_APP, nil
	case "ContainerFlags_FLAG_GAME":
		return ContainerFlags_FLAG_GAME, nil
	case "ContainerFlags_FLAG_INTERVAL":
		return ContainerFlags_FLAG_INTERVAL, nil
	case "ContainerFlags_FLAG_IOS_GC_DEFAULT":
		return ContainerFlags_FLAG_IOS_GC_DEFAULT, nil
	case "ContainerFlags_FLAG_IOS_GC_RECOMMEND":
		return ContainerFlags_FLAG_IOS_GC_RECOMMEND, nil
	case "ContainerFlags_FLAG_IOS_GC_RELAXATION":
		return ContainerFlags_FLAG_IOS_GC_RELAXATION, nil
	case "ContainerFlags_FLAG_IOS_GC_SUPERIOR":
		return ContainerFlags_FLAG_IOS_GC_SUPERIOR, nil
	}
	return ContainerFlags(math.MinInt32 - 1), fmt.Errorf("not a valid ContainerFlags string")
}

//策略拓展类型
type AdStrategyExtendType int64

const (
	AdStrategyExtendType_STRY_ET_UNKNOWN AdStrategyExtendType = 0
	AdStrategyExtendType_STRY_ET_WCOMMON AdStrategyExtendType = 1
	AdStrategyExtendType_STRY_ET_WBANNER AdStrategyExtendType = 2
)

func (p AdStrategyExtendType) String() string {
	switch p {
	case AdStrategyExtendType_STRY_ET_UNKNOWN:
		return "AdStrategyExtendType_STRY_ET_UNKNOWN"
	case AdStrategyExtendType_STRY_ET_WCOMMON:
		return "AdStrategyExtendType_STRY_ET_WCOMMON"
	case AdStrategyExtendType_STRY_ET_WBANNER:
		return "AdStrategyExtendType_STRY_ET_WBANNER"
	}
	return "<UNSET>"
}

func AdStrategyExtendTypeFromString(s string) (AdStrategyExtendType, error) {
	switch s {
	case "AdStrategyExtendType_STRY_ET_UNKNOWN":
		return AdStrategyExtendType_STRY_ET_UNKNOWN, nil
	case "AdStrategyExtendType_STRY_ET_WCOMMON":
		return AdStrategyExtendType_STRY_ET_WCOMMON, nil
	case "AdStrategyExtendType_STRY_ET_WBANNER":
		return AdStrategyExtendType_STRY_ET_WBANNER, nil
	}
	return AdStrategyExtendType(math.MinInt32 - 1), fmt.Errorf("not a valid AdStrategyExtendType string")
}

//创意模板类型
type AdCreativeTemplateType int64

const (
	AdCreativeTemplateType_ACTT_UNKNOWN AdCreativeTemplateType = 0
	AdCreativeTemplateType_ACTT_IMAGE   AdCreativeTemplateType = 1
	AdCreativeTemplateType_ACTT_ICON    AdCreativeTemplateType = 2
	AdCreativeTemplateType_ACTT_HTML    AdCreativeTemplateType = 3
	AdCreativeTemplateType_ACTT_VIDEO   AdCreativeTemplateType = 4
	AdCreativeTemplateType_ACTT_TITLE   AdCreativeTemplateType = 5
	AdCreativeTemplateType_ACTT_BRIEF   AdCreativeTemplateType = 6
	AdCreativeTemplateType_ACTT_DETAIL  AdCreativeTemplateType = 7
)

func (p AdCreativeTemplateType) String() string {
	switch p {
	case AdCreativeTemplateType_ACTT_UNKNOWN:
		return "AdCreativeTemplateType_ACTT_UNKNOWN"
	case AdCreativeTemplateType_ACTT_IMAGE:
		return "AdCreativeTemplateType_ACTT_IMAGE"
	case AdCreativeTemplateType_ACTT_ICON:
		return "AdCreativeTemplateType_ACTT_ICON"
	case AdCreativeTemplateType_ACTT_HTML:
		return "AdCreativeTemplateType_ACTT_HTML"
	case AdCreativeTemplateType_ACTT_VIDEO:
		return "AdCreativeTemplateType_ACTT_VIDEO"
	case AdCreativeTemplateType_ACTT_TITLE:
		return "AdCreativeTemplateType_ACTT_TITLE"
	case AdCreativeTemplateType_ACTT_BRIEF:
		return "AdCreativeTemplateType_ACTT_BRIEF"
	case AdCreativeTemplateType_ACTT_DETAIL:
		return "AdCreativeTemplateType_ACTT_DETAIL"
	}
	return "<UNSET>"
}

func AdCreativeTemplateTypeFromString(s string) (AdCreativeTemplateType, error) {
	switch s {
	case "AdCreativeTemplateType_ACTT_UNKNOWN":
		return AdCreativeTemplateType_ACTT_UNKNOWN, nil
	case "AdCreativeTemplateType_ACTT_IMAGE":
		return AdCreativeTemplateType_ACTT_IMAGE, nil
	case "AdCreativeTemplateType_ACTT_ICON":
		return AdCreativeTemplateType_ACTT_ICON, nil
	case "AdCreativeTemplateType_ACTT_HTML":
		return AdCreativeTemplateType_ACTT_HTML, nil
	case "AdCreativeTemplateType_ACTT_VIDEO":
		return AdCreativeTemplateType_ACTT_VIDEO, nil
	case "AdCreativeTemplateType_ACTT_TITLE":
		return AdCreativeTemplateType_ACTT_TITLE, nil
	case "AdCreativeTemplateType_ACTT_BRIEF":
		return AdCreativeTemplateType_ACTT_BRIEF, nil
	case "AdCreativeTemplateType_ACTT_DETAIL":
		return AdCreativeTemplateType_ACTT_DETAIL, nil
	}
	return AdCreativeTemplateType(math.MinInt32 - 1), fmt.Errorf("not a valid AdCreativeTemplateType string")
}

//第三方监测厂家编号
type ThirdPartyTracking int64

const (
	ThirdPartyTracking_TPT_UNKNOWN     ThirdPartyTracking = 0
	ThirdPartyTracking_TPT_MIAOZHEN    ThirdPartyTracking = 1
	ThirdPartyTracking_TPT_ADMASTER    ThirdPartyTracking = 2
	ThirdPartyTracking_TPT_DOUBLECLICK ThirdPartyTracking = 3
	ThirdPartyTracking_TPT_NIELSEN     ThirdPartyTracking = 4
	ThirdPartyTracking_TPT_CTR         ThirdPartyTracking = 5
	ThirdPartyTracking_TPT_UDBAC       ThirdPartyTracking = 6
)

func (p ThirdPartyTracking) String() string {
	switch p {
	case ThirdPartyTracking_TPT_UNKNOWN:
		return "ThirdPartyTracking_TPT_UNKNOWN"
	case ThirdPartyTracking_TPT_MIAOZHEN:
		return "ThirdPartyTracking_TPT_MIAOZHEN"
	case ThirdPartyTracking_TPT_ADMASTER:
		return "ThirdPartyTracking_TPT_ADMASTER"
	case ThirdPartyTracking_TPT_DOUBLECLICK:
		return "ThirdPartyTracking_TPT_DOUBLECLICK"
	case ThirdPartyTracking_TPT_NIELSEN:
		return "ThirdPartyTracking_TPT_NIELSEN"
	case ThirdPartyTracking_TPT_CTR:
		return "ThirdPartyTracking_TPT_CTR"
	case ThirdPartyTracking_TPT_UDBAC:
		return "ThirdPartyTracking_TPT_UDBAC"
	}
	return "<UNSET>"
}

func ThirdPartyTrackingFromString(s string) (ThirdPartyTracking, error) {
	switch s {
	case "ThirdPartyTracking_TPT_UNKNOWN":
		return ThirdPartyTracking_TPT_UNKNOWN, nil
	case "ThirdPartyTracking_TPT_MIAOZHEN":
		return ThirdPartyTracking_TPT_MIAOZHEN, nil
	case "ThirdPartyTracking_TPT_ADMASTER":
		return ThirdPartyTracking_TPT_ADMASTER, nil
	case "ThirdPartyTracking_TPT_DOUBLECLICK":
		return ThirdPartyTracking_TPT_DOUBLECLICK, nil
	case "ThirdPartyTracking_TPT_NIELSEN":
		return ThirdPartyTracking_TPT_NIELSEN, nil
	case "ThirdPartyTracking_TPT_CTR":
		return ThirdPartyTracking_TPT_CTR, nil
	case "ThirdPartyTracking_TPT_UDBAC":
		return ThirdPartyTracking_TPT_UDBAC, nil
	}
	return ThirdPartyTracking(math.MinInt32 - 1), fmt.Errorf("not a valid ThirdPartyTracking string")
}

//请求来源种类
type RequestSourceType int64

const (
	RequestSourceType_RST_UNKNOWN        RequestSourceType = 0
	RequestSourceType_RST_NORMAL_REQUEST RequestSourceType = 1
	RequestSourceType_RST_PARTNER_NOT_AD RequestSourceType = 2
)

func (p RequestSourceType) String() string {
	switch p {
	case RequestSourceType_RST_UNKNOWN:
		return "RequestSourceType_RST_UNKNOWN"
	case RequestSourceType_RST_NORMAL_REQUEST:
		return "RequestSourceType_RST_NORMAL_REQUEST"
	case RequestSourceType_RST_PARTNER_NOT_AD:
		return "RequestSourceType_RST_PARTNER_NOT_AD"
	}
	return "<UNSET>"
}

func RequestSourceTypeFromString(s string) (RequestSourceType, error) {
	switch s {
	case "RequestSourceType_RST_UNKNOWN":
		return RequestSourceType_RST_UNKNOWN, nil
	case "RequestSourceType_RST_NORMAL_REQUEST":
		return RequestSourceType_RST_NORMAL_REQUEST, nil
	case "RequestSourceType_RST_PARTNER_NOT_AD":
		return RequestSourceType_RST_PARTNER_NOT_AD, nil
	}
	return RequestSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid RequestSourceType string")
}

//应答处理方式
type ResponseProcessType int64

const (
	ResponseProcessType_RPT_UNKNOWN                 ResponseProcessType = 0
	ResponseProcessType_RPT_SHOW                    ResponseProcessType = 1
	ResponseProcessType_RPT_TO_PARTNER_NOT_AD_TO_US ResponseProcessType = 2
	ResponseProcessType_RPT_TO_PARTNER_NOT_AD_SHOW  ResponseProcessType = 3
)

func (p ResponseProcessType) String() string {
	switch p {
	case ResponseProcessType_RPT_UNKNOWN:
		return "ResponseProcessType_RPT_UNKNOWN"
	case ResponseProcessType_RPT_SHOW:
		return "ResponseProcessType_RPT_SHOW"
	case ResponseProcessType_RPT_TO_PARTNER_NOT_AD_TO_US:
		return "ResponseProcessType_RPT_TO_PARTNER_NOT_AD_TO_US"
	case ResponseProcessType_RPT_TO_PARTNER_NOT_AD_SHOW:
		return "ResponseProcessType_RPT_TO_PARTNER_NOT_AD_SHOW"
	}
	return "<UNSET>"
}

func ResponseProcessTypeFromString(s string) (ResponseProcessType, error) {
	switch s {
	case "ResponseProcessType_RPT_UNKNOWN":
		return ResponseProcessType_RPT_UNKNOWN, nil
	case "ResponseProcessType_RPT_SHOW":
		return ResponseProcessType_RPT_SHOW, nil
	case "ResponseProcessType_RPT_TO_PARTNER_NOT_AD_TO_US":
		return ResponseProcessType_RPT_TO_PARTNER_NOT_AD_TO_US, nil
	case "ResponseProcessType_RPT_TO_PARTNER_NOT_AD_SHOW":
		return ResponseProcessType_RPT_TO_PARTNER_NOT_AD_SHOW, nil
	}
	return ResponseProcessType(math.MinInt32 - 1), fmt.Errorf("not a valid ResponseProcessType string")
}

//投放标记
type ProjectTaskFlag int64

const (
	ProjectTaskFlag_TASK_FLAG_NORMAL     ProjectTaskFlag = 1
	ProjectTaskFlag_TASK_FLAG_FREE       ProjectTaskFlag = 2
	ProjectTaskFlag_TASK_FLAG_SUPPLEMENT ProjectTaskFlag = 3
	ProjectTaskFlag_TASK_FLAG_TEST       ProjectTaskFlag = 4
)

func (p ProjectTaskFlag) String() string {
	switch p {
	case ProjectTaskFlag_TASK_FLAG_NORMAL:
		return "ProjectTaskFlag_TASK_FLAG_NORMAL"
	case ProjectTaskFlag_TASK_FLAG_FREE:
		return "ProjectTaskFlag_TASK_FLAG_FREE"
	case ProjectTaskFlag_TASK_FLAG_SUPPLEMENT:
		return "ProjectTaskFlag_TASK_FLAG_SUPPLEMENT"
	case ProjectTaskFlag_TASK_FLAG_TEST:
		return "ProjectTaskFlag_TASK_FLAG_TEST"
	}
	return "<UNSET>"
}

func ProjectTaskFlagFromString(s string) (ProjectTaskFlag, error) {
	switch s {
	case "ProjectTaskFlag_TASK_FLAG_NORMAL":
		return ProjectTaskFlag_TASK_FLAG_NORMAL, nil
	case "ProjectTaskFlag_TASK_FLAG_FREE":
		return ProjectTaskFlag_TASK_FLAG_FREE, nil
	case "ProjectTaskFlag_TASK_FLAG_SUPPLEMENT":
		return ProjectTaskFlag_TASK_FLAG_SUPPLEMENT, nil
	case "ProjectTaskFlag_TASK_FLAG_TEST":
		return ProjectTaskFlag_TASK_FLAG_TEST, nil
	}
	return ProjectTaskFlag(math.MinInt32 - 1), fmt.Errorf("not a valid ProjectTaskFlag string")
}

//补量说明
type ProjectSupplementFlag int64

const (
	ProjectSupplementFlag_SUPPLEMENT_FLAG_A ProjectSupplementFlag = 1
	ProjectSupplementFlag_SUPPLEMENT_FLAG_C ProjectSupplementFlag = 2
	ProjectSupplementFlag_SUPPLEMENT_FLAG_M ProjectSupplementFlag = 3
)

func (p ProjectSupplementFlag) String() string {
	switch p {
	case ProjectSupplementFlag_SUPPLEMENT_FLAG_A:
		return "ProjectSupplementFlag_SUPPLEMENT_FLAG_A"
	case ProjectSupplementFlag_SUPPLEMENT_FLAG_C:
		return "ProjectSupplementFlag_SUPPLEMENT_FLAG_C"
	case ProjectSupplementFlag_SUPPLEMENT_FLAG_M:
		return "ProjectSupplementFlag_SUPPLEMENT_FLAG_M"
	}
	return "<UNSET>"
}

func ProjectSupplementFlagFromString(s string) (ProjectSupplementFlag, error) {
	switch s {
	case "ProjectSupplementFlag_SUPPLEMENT_FLAG_A":
		return ProjectSupplementFlag_SUPPLEMENT_FLAG_A, nil
	case "ProjectSupplementFlag_SUPPLEMENT_FLAG_C":
		return ProjectSupplementFlag_SUPPLEMENT_FLAG_C, nil
	case "ProjectSupplementFlag_SUPPLEMENT_FLAG_M":
		return ProjectSupplementFlag_SUPPLEMENT_FLAG_M, nil
	}
	return ProjectSupplementFlag(math.MinInt32 - 1), fmt.Errorf("not a valid ProjectSupplementFlag string")
}

//天气类型
type WeatherType int64

const (
	WeatherType_WT_UNKNOWN  WeatherType = 0
	WeatherType_WT_SUNNY    WeatherType = 100
	WeatherType_WT_CLOUDY   WeatherType = 200
	WeatherType_WT_OVERCAST WeatherType = 300
	WeatherType_WT_RAIN     WeatherType = 400
	WeatherType_WT_SNOW     WeatherType = 500
	WeatherType_WT_FOG      WeatherType = 600
	WeatherType_WT_HAZE     WeatherType = 700
	WeatherType_WT_HAIL     WeatherType = 800
)

func (p WeatherType) String() string {
	switch p {
	case WeatherType_WT_UNKNOWN:
		return "WeatherType_WT_UNKNOWN"
	case WeatherType_WT_SUNNY:
		return "WeatherType_WT_SUNNY"
	case WeatherType_WT_CLOUDY:
		return "WeatherType_WT_CLOUDY"
	case WeatherType_WT_OVERCAST:
		return "WeatherType_WT_OVERCAST"
	case WeatherType_WT_RAIN:
		return "WeatherType_WT_RAIN"
	case WeatherType_WT_SNOW:
		return "WeatherType_WT_SNOW"
	case WeatherType_WT_FOG:
		return "WeatherType_WT_FOG"
	case WeatherType_WT_HAZE:
		return "WeatherType_WT_HAZE"
	case WeatherType_WT_HAIL:
		return "WeatherType_WT_HAIL"
	}
	return "<UNSET>"
}

func WeatherTypeFromString(s string) (WeatherType, error) {
	switch s {
	case "WeatherType_WT_UNKNOWN":
		return WeatherType_WT_UNKNOWN, nil
	case "WeatherType_WT_SUNNY":
		return WeatherType_WT_SUNNY, nil
	case "WeatherType_WT_CLOUDY":
		return WeatherType_WT_CLOUDY, nil
	case "WeatherType_WT_OVERCAST":
		return WeatherType_WT_OVERCAST, nil
	case "WeatherType_WT_RAIN":
		return WeatherType_WT_RAIN, nil
	case "WeatherType_WT_SNOW":
		return WeatherType_WT_SNOW, nil
	case "WeatherType_WT_FOG":
		return WeatherType_WT_FOG, nil
	case "WeatherType_WT_HAZE":
		return WeatherType_WT_HAZE, nil
	case "WeatherType_WT_HAIL":
		return WeatherType_WT_HAIL, nil
	}
	return WeatherType(math.MinInt32 - 1), fmt.Errorf("not a valid WeatherType string")
}

//激活核对所支持的设备ID类型, 可以通过或的关系进行叠加, 比如3代表同时支持IDFA和IDFA MD5核对
type TrackingDeviceIdType int64

const (
	TrackingDeviceIdType_TDIT_UNKNOWN       TrackingDeviceIdType = 0
	TrackingDeviceIdType_TDIT_IDFA          TrackingDeviceIdType = 1
	TrackingDeviceIdType_TDIT_IDFA_MD5      TrackingDeviceIdType = 2
	TrackingDeviceIdType_TDIT_MAC           TrackingDeviceIdType = 4
	TrackingDeviceIdType_TDIT_IP_UA         TrackingDeviceIdType = 8
	TrackingDeviceIdType_TDIT_IMEI          TrackingDeviceIdType = 16
	TrackingDeviceIdType_TDIT_IMEI_MD5      TrackingDeviceIdType = 32
	TrackingDeviceIdType_TDIT_SEARCH_IMP_ID TrackingDeviceIdType = 64
	TrackingDeviceIdType_TDIT_OAID          TrackingDeviceIdType = 128
	TrackingDeviceIdType_TDIT_OAID_MD5      TrackingDeviceIdType = 256
)

func (p TrackingDeviceIdType) String() string {
	switch p {
	case TrackingDeviceIdType_TDIT_UNKNOWN:
		return "TrackingDeviceIdType_TDIT_UNKNOWN"
	case TrackingDeviceIdType_TDIT_IDFA:
		return "TrackingDeviceIdType_TDIT_IDFA"
	case TrackingDeviceIdType_TDIT_IDFA_MD5:
		return "TrackingDeviceIdType_TDIT_IDFA_MD5"
	case TrackingDeviceIdType_TDIT_MAC:
		return "TrackingDeviceIdType_TDIT_MAC"
	case TrackingDeviceIdType_TDIT_IP_UA:
		return "TrackingDeviceIdType_TDIT_IP_UA"
	case TrackingDeviceIdType_TDIT_IMEI:
		return "TrackingDeviceIdType_TDIT_IMEI"
	case TrackingDeviceIdType_TDIT_IMEI_MD5:
		return "TrackingDeviceIdType_TDIT_IMEI_MD5"
	case TrackingDeviceIdType_TDIT_SEARCH_IMP_ID:
		return "TrackingDeviceIdType_TDIT_SEARCH_IMP_ID"
	case TrackingDeviceIdType_TDIT_OAID:
		return "TrackingDeviceIdType_TDIT_OAID"
	case TrackingDeviceIdType_TDIT_OAID_MD5:
		return "TrackingDeviceIdType_TDIT_OAID_MD5"
	}
	return "<UNSET>"
}

func TrackingDeviceIdTypeFromString(s string) (TrackingDeviceIdType, error) {
	switch s {
	case "TrackingDeviceIdType_TDIT_UNKNOWN":
		return TrackingDeviceIdType_TDIT_UNKNOWN, nil
	case "TrackingDeviceIdType_TDIT_IDFA":
		return TrackingDeviceIdType_TDIT_IDFA, nil
	case "TrackingDeviceIdType_TDIT_IDFA_MD5":
		return TrackingDeviceIdType_TDIT_IDFA_MD5, nil
	case "TrackingDeviceIdType_TDIT_MAC":
		return TrackingDeviceIdType_TDIT_MAC, nil
	case "TrackingDeviceIdType_TDIT_IP_UA":
		return TrackingDeviceIdType_TDIT_IP_UA, nil
	case "TrackingDeviceIdType_TDIT_IMEI":
		return TrackingDeviceIdType_TDIT_IMEI, nil
	case "TrackingDeviceIdType_TDIT_IMEI_MD5":
		return TrackingDeviceIdType_TDIT_IMEI_MD5, nil
	case "TrackingDeviceIdType_TDIT_SEARCH_IMP_ID":
		return TrackingDeviceIdType_TDIT_SEARCH_IMP_ID, nil
	case "TrackingDeviceIdType_TDIT_OAID":
		return TrackingDeviceIdType_TDIT_OAID, nil
	case "TrackingDeviceIdType_TDIT_OAID_MD5":
		return TrackingDeviceIdType_TDIT_OAID_MD5, nil
	}
	return TrackingDeviceIdType(math.MinInt32 - 1), fmt.Errorf("not a valid TrackingDeviceIdType string")
}

//点击类型
type ClickType int64

const (
	ClickType_CT_UNKNOWN   ClickType = 0
	ClickType_CT_NORMAL    ClickType = 1
	ClickType_CT_DIVERSION ClickType = 2
)

func (p ClickType) String() string {
	switch p {
	case ClickType_CT_UNKNOWN:
		return "ClickType_CT_UNKNOWN"
	case ClickType_CT_NORMAL:
		return "ClickType_CT_NORMAL"
	case ClickType_CT_DIVERSION:
		return "ClickType_CT_DIVERSION"
	}
	return "<UNSET>"
}

func ClickTypeFromString(s string) (ClickType, error) {
	switch s {
	case "ClickType_CT_UNKNOWN":
		return ClickType_CT_UNKNOWN, nil
	case "ClickType_CT_NORMAL":
		return ClickType_CT_NORMAL, nil
	case "ClickType_CT_DIVERSION":
		return ClickType_CT_DIVERSION, nil
	}
	return ClickType(math.MinInt32 - 1), fmt.Errorf("not a valid ClickType string")
}
