// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package bidmaster_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/bidmaster_types"
	"rtb_model_server/common/domob_thrift/passport_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = bidmaster_types.GoUnusedProtection__
var _ = passport_types.GoUnusedProtection__
var GoUnusedProtection__ int

type BidMasterEventType int64

const (
	BidMasterEventType_ET_UNKNOWN              BidMasterEventType = 0
	BidMasterEventType_ET_ADD                  BidMasterEventType = 1
	BidMasterEventType_ET_UPDATE               BidMasterEventType = 2
	BidMasterEventType_ET_DELETE               BidMasterEventType = 3
	BidMasterEventType_ET_PAUSE                BidMasterEventType = 4
	BidMasterEventType_ET_RESUME               BidMasterEventType = 5
	BidMasterEventType_ET_INNER_APPROVE_PASS   BidMasterEventType = 10
	BidMasterEventType_ET_INNER_APPROVE_REJECT BidMasterEventType = 11
	BidMasterEventType_ET_BALANCE_OVER         BidMasterEventType = 20
	BidMasterEventType_ET_BALANCE_OK           BidMasterEventType = 21
	BidMasterEventType_ET_CHANGE_PASSWORD      BidMasterEventType = 22
	BidMasterEventType_ET_AGENT_COMMAND        BidMasterEventType = 23
)

func (p BidMasterEventType) String() string {
	switch p {
	case BidMasterEventType_ET_UNKNOWN:
		return "BidMasterEventType_ET_UNKNOWN"
	case BidMasterEventType_ET_ADD:
		return "BidMasterEventType_ET_ADD"
	case BidMasterEventType_ET_UPDATE:
		return "BidMasterEventType_ET_UPDATE"
	case BidMasterEventType_ET_DELETE:
		return "BidMasterEventType_ET_DELETE"
	case BidMasterEventType_ET_PAUSE:
		return "BidMasterEventType_ET_PAUSE"
	case BidMasterEventType_ET_RESUME:
		return "BidMasterEventType_ET_RESUME"
	case BidMasterEventType_ET_INNER_APPROVE_PASS:
		return "BidMasterEventType_ET_INNER_APPROVE_PASS"
	case BidMasterEventType_ET_INNER_APPROVE_REJECT:
		return "BidMasterEventType_ET_INNER_APPROVE_REJECT"
	case BidMasterEventType_ET_BALANCE_OVER:
		return "BidMasterEventType_ET_BALANCE_OVER"
	case BidMasterEventType_ET_BALANCE_OK:
		return "BidMasterEventType_ET_BALANCE_OK"
	case BidMasterEventType_ET_CHANGE_PASSWORD:
		return "BidMasterEventType_ET_CHANGE_PASSWORD"
	case BidMasterEventType_ET_AGENT_COMMAND:
		return "BidMasterEventType_ET_AGENT_COMMAND"
	}
	return "<UNSET>"
}

func BidMasterEventTypeFromString(s string) (BidMasterEventType, error) {
	switch s {
	case "BidMasterEventType_ET_UNKNOWN":
		return BidMasterEventType_ET_UNKNOWN, nil
	case "BidMasterEventType_ET_ADD":
		return BidMasterEventType_ET_ADD, nil
	case "BidMasterEventType_ET_UPDATE":
		return BidMasterEventType_ET_UPDATE, nil
	case "BidMasterEventType_ET_DELETE":
		return BidMasterEventType_ET_DELETE, nil
	case "BidMasterEventType_ET_PAUSE":
		return BidMasterEventType_ET_PAUSE, nil
	case "BidMasterEventType_ET_RESUME":
		return BidMasterEventType_ET_RESUME, nil
	case "BidMasterEventType_ET_INNER_APPROVE_PASS":
		return BidMasterEventType_ET_INNER_APPROVE_PASS, nil
	case "BidMasterEventType_ET_INNER_APPROVE_REJECT":
		return BidMasterEventType_ET_INNER_APPROVE_REJECT, nil
	case "BidMasterEventType_ET_BALANCE_OVER":
		return BidMasterEventType_ET_BALANCE_OVER, nil
	case "BidMasterEventType_ET_BALANCE_OK":
		return BidMasterEventType_ET_BALANCE_OK, nil
	case "BidMasterEventType_ET_CHANGE_PASSWORD":
		return BidMasterEventType_ET_CHANGE_PASSWORD, nil
	case "BidMasterEventType_ET_AGENT_COMMAND":
		return BidMasterEventType_ET_AGENT_COMMAND, nil
	}
	return BidMasterEventType(math.MinInt32 - 1), fmt.Errorf("not a valid BidMasterEventType string")
}

type BidMasterEventCategory int64

const (
	BidMasterEventCategory_EC_UNKNOWN                BidMasterEventCategory = 0
	BidMasterEventCategory_EC_SPONSOR                BidMasterEventCategory = 1
	BidMasterEventCategory_EC_AD_ORDER               BidMasterEventCategory = 2
	BidMasterEventCategory_EC_AD_CAMPAIGN            BidMasterEventCategory = 3
	BidMasterEventCategory_EC_AD_STRATEGY            BidMasterEventCategory = 4
	BidMasterEventCategory_EC_AD_CREATIVE            BidMasterEventCategory = 5
	BidMasterEventCategory_EC_AD_PROMOTION           BidMasterEventCategory = 6
	BidMasterEventCategory_EC_AD_EXCHANGE_DMP        BidMasterEventCategory = 7
	BidMasterEventCategory_EC_AD_TRACKING            BidMasterEventCategory = 8
	BidMasterEventCategory_EC_AGENT                  BidMasterEventCategory = 11
	BidMasterEventCategory_EC_AGENT_USER             BidMasterEventCategory = 12
	BidMasterEventCategory_EC_SPONSOR_USER           BidMasterEventCategory = 13
	BidMasterEventCategory_EC_AGENT_ACCOUNT          BidMasterEventCategory = 14
	BidMasterEventCategory_EC_SPONSOR_ACCOUNT        BidMasterEventCategory = 15
	BidMasterEventCategory_EC_SPONSOR_AUDIT_MATERIAL BidMasterEventCategory = 16
)

func (p BidMasterEventCategory) String() string {
	switch p {
	case BidMasterEventCategory_EC_UNKNOWN:
		return "BidMasterEventCategory_EC_UNKNOWN"
	case BidMasterEventCategory_EC_SPONSOR:
		return "BidMasterEventCategory_EC_SPONSOR"
	case BidMasterEventCategory_EC_AD_ORDER:
		return "BidMasterEventCategory_EC_AD_ORDER"
	case BidMasterEventCategory_EC_AD_CAMPAIGN:
		return "BidMasterEventCategory_EC_AD_CAMPAIGN"
	case BidMasterEventCategory_EC_AD_STRATEGY:
		return "BidMasterEventCategory_EC_AD_STRATEGY"
	case BidMasterEventCategory_EC_AD_CREATIVE:
		return "BidMasterEventCategory_EC_AD_CREATIVE"
	case BidMasterEventCategory_EC_AD_PROMOTION:
		return "BidMasterEventCategory_EC_AD_PROMOTION"
	case BidMasterEventCategory_EC_AD_EXCHANGE_DMP:
		return "BidMasterEventCategory_EC_AD_EXCHANGE_DMP"
	case BidMasterEventCategory_EC_AD_TRACKING:
		return "BidMasterEventCategory_EC_AD_TRACKING"
	case BidMasterEventCategory_EC_AGENT:
		return "BidMasterEventCategory_EC_AGENT"
	case BidMasterEventCategory_EC_AGENT_USER:
		return "BidMasterEventCategory_EC_AGENT_USER"
	case BidMasterEventCategory_EC_SPONSOR_USER:
		return "BidMasterEventCategory_EC_SPONSOR_USER"
	case BidMasterEventCategory_EC_AGENT_ACCOUNT:
		return "BidMasterEventCategory_EC_AGENT_ACCOUNT"
	case BidMasterEventCategory_EC_SPONSOR_ACCOUNT:
		return "BidMasterEventCategory_EC_SPONSOR_ACCOUNT"
	case BidMasterEventCategory_EC_SPONSOR_AUDIT_MATERIAL:
		return "BidMasterEventCategory_EC_SPONSOR_AUDIT_MATERIAL"
	}
	return "<UNSET>"
}

func BidMasterEventCategoryFromString(s string) (BidMasterEventCategory, error) {
	switch s {
	case "BidMasterEventCategory_EC_UNKNOWN":
		return BidMasterEventCategory_EC_UNKNOWN, nil
	case "BidMasterEventCategory_EC_SPONSOR":
		return BidMasterEventCategory_EC_SPONSOR, nil
	case "BidMasterEventCategory_EC_AD_ORDER":
		return BidMasterEventCategory_EC_AD_ORDER, nil
	case "BidMasterEventCategory_EC_AD_CAMPAIGN":
		return BidMasterEventCategory_EC_AD_CAMPAIGN, nil
	case "BidMasterEventCategory_EC_AD_STRATEGY":
		return BidMasterEventCategory_EC_AD_STRATEGY, nil
	case "BidMasterEventCategory_EC_AD_CREATIVE":
		return BidMasterEventCategory_EC_AD_CREATIVE, nil
	case "BidMasterEventCategory_EC_AD_PROMOTION":
		return BidMasterEventCategory_EC_AD_PROMOTION, nil
	case "BidMasterEventCategory_EC_AD_EXCHANGE_DMP":
		return BidMasterEventCategory_EC_AD_EXCHANGE_DMP, nil
	case "BidMasterEventCategory_EC_AD_TRACKING":
		return BidMasterEventCategory_EC_AD_TRACKING, nil
	case "BidMasterEventCategory_EC_AGENT":
		return BidMasterEventCategory_EC_AGENT, nil
	case "BidMasterEventCategory_EC_AGENT_USER":
		return BidMasterEventCategory_EC_AGENT_USER, nil
	case "BidMasterEventCategory_EC_SPONSOR_USER":
		return BidMasterEventCategory_EC_SPONSOR_USER, nil
	case "BidMasterEventCategory_EC_AGENT_ACCOUNT":
		return BidMasterEventCategory_EC_AGENT_ACCOUNT, nil
	case "BidMasterEventCategory_EC_SPONSOR_ACCOUNT":
		return BidMasterEventCategory_EC_SPONSOR_ACCOUNT, nil
	case "BidMasterEventCategory_EC_SPONSOR_AUDIT_MATERIAL":
		return BidMasterEventCategory_EC_SPONSOR_AUDIT_MATERIAL, nil
	}
	return BidMasterEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid BidMasterEventCategory string")
}

type BidMasterCommonEvent struct {
	TypeA1   BidMasterEventType     `thrift:"type,1" json:"type"`
	Category BidMasterEventCategory `thrift:"category,2" json:"category"`
	Ids      []int32                `thrift:"ids,3" json:"ids"`
}

func NewBidMasterCommonEvent() *BidMasterCommonEvent {
	return &BidMasterCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BidMasterCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *BidMasterCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *BidMasterCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BidMasterCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = BidMasterEventType(v)
	}
	return nil
}

func (p *BidMasterCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = BidMasterEventCategory(v)
	}
	return nil
}

func (p *BidMasterCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BidMasterCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BidMasterCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BidMasterCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BidMasterCommonEvent(%+v)", *p)
}

type BidMasterOperationEvent struct {
	TypeA1         BidMasterEventType              `thrift:"type,1" json:"type"`
	Category       BidMasterEventCategory          `thrift:"category,2" json:"category"`
	OperateTime    int64                           `thrift:"operateTime,3" json:"operateTime"`
	ServiceName    string                          `thrift:"serviceName,4" json:"serviceName"`
	Sponsor        *bidmaster_types.SponsorProfile `thrift:"sponsor,5" json:"sponsor"`
	Order          *bidmaster_types.AdOrder        `thrift:"order,6" json:"order"`
	Campaign       *bidmaster_types.AdCampaign     `thrift:"campaign,7" json:"campaign"`
	Strategy       *bidmaster_types.AdStrategy     `thrift:"strategy,8" json:"strategy"`
	Creative       *bidmaster_types.AdCreative     `thrift:"creative,9" json:"creative"`
	AgentAccount   *bidmaster_types.AgentAccount   `thrift:"agentAccount,10" json:"agentAccount"`
	SponsorAccount *bidmaster_types.SponsorAccount `thrift:"sponsorAccount,11" json:"sponsorAccount"`
	User           *passport_types.User            `thrift:"user,12" json:"user"`
	Promotion      *bidmaster_types.AdPromotion    `thrift:"promotion,13" json:"promotion"`
	AdExchangeDmp  *bidmaster_types.AdExchangeDmp  `thrift:"adExchangeDmp,14" json:"adExchangeDmp"`
	AdTracking     *bidmaster_types.AdTracking     `thrift:"adTracking,15" json:"adTracking"`
}

func NewBidMasterOperationEvent() *BidMasterOperationEvent {
	return &BidMasterOperationEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BidMasterOperationEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *BidMasterOperationEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *BidMasterOperationEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = BidMasterEventType(v)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = BidMasterEventCategory(v)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OperateTime = v
	}
	return nil
}

func (p *BidMasterOperationEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *BidMasterOperationEvent) readField5(iprot thrift.TProtocol) error {
	p.Sponsor = bidmaster_types.NewSponsorProfile()
	if err := p.Sponsor.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sponsor)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField6(iprot thrift.TProtocol) error {
	p.Order = bidmaster_types.NewAdOrder()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField7(iprot thrift.TProtocol) error {
	p.Campaign = bidmaster_types.NewAdCampaign()
	if err := p.Campaign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Campaign)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField8(iprot thrift.TProtocol) error {
	p.Strategy = bidmaster_types.NewAdStrategy()
	if err := p.Strategy.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Strategy)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField9(iprot thrift.TProtocol) error {
	p.Creative = bidmaster_types.NewAdCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField10(iprot thrift.TProtocol) error {
	p.AgentAccount = bidmaster_types.NewAgentAccount()
	if err := p.AgentAccount.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AgentAccount)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField11(iprot thrift.TProtocol) error {
	p.SponsorAccount = bidmaster_types.NewSponsorAccount()
	if err := p.SponsorAccount.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SponsorAccount)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField12(iprot thrift.TProtocol) error {
	p.User = passport_types.NewUser()
	if err := p.User.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.User)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField13(iprot thrift.TProtocol) error {
	p.Promotion = bidmaster_types.NewAdPromotion()
	if err := p.Promotion.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Promotion)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField14(iprot thrift.TProtocol) error {
	p.AdExchangeDmp = bidmaster_types.NewAdExchangeDmp()
	if err := p.AdExchangeDmp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdExchangeDmp)
	}
	return nil
}

func (p *BidMasterOperationEvent) readField15(iprot thrift.TProtocol) error {
	p.AdTracking = bidmaster_types.NewAdTracking()
	if err := p.AdTracking.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdTracking)
	}
	return nil
}

func (p *BidMasterOperationEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BidMasterOperationEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BidMasterOperationEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("operateTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:operateTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OperateTime)); err != nil {
		return fmt.Errorf("%T.operateTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:operateTime: %s", p, err)
	}
	return err
}

func (p *BidMasterOperationEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("serviceName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:serviceName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServiceName)); err != nil {
		return fmt.Errorf("%T.serviceName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:serviceName: %s", p, err)
	}
	return err
}

func (p *BidMasterOperationEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Sponsor != nil {
		if err := oprot.WriteFieldBegin("sponsor", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:sponsor: %s", p, err)
		}
		if err := p.Sponsor.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sponsor)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:sponsor: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:order: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Campaign != nil {
		if err := oprot.WriteFieldBegin("campaign", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:campaign: %s", p, err)
		}
		if err := p.Campaign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Campaign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:campaign: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Strategy != nil {
		if err := oprot.WriteFieldBegin("strategy", thrift.STRUCT, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:strategy: %s", p, err)
		}
		if err := p.Strategy.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Strategy)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:strategy: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField9(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:creative: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.AgentAccount != nil {
		if err := oprot.WriteFieldBegin("agentAccount", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:agentAccount: %s", p, err)
		}
		if err := p.AgentAccount.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AgentAccount)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:agentAccount: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.SponsorAccount != nil {
		if err := oprot.WriteFieldBegin("sponsorAccount", thrift.STRUCT, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:sponsorAccount: %s", p, err)
		}
		if err := p.SponsorAccount.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SponsorAccount)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:sponsorAccount: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField12(oprot thrift.TProtocol) (err error) {
	if p.User != nil {
		if err := oprot.WriteFieldBegin("user", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:user: %s", p, err)
		}
		if err := p.User.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.User)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:user: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField13(oprot thrift.TProtocol) (err error) {
	if p.Promotion != nil {
		if err := oprot.WriteFieldBegin("promotion", thrift.STRUCT, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:promotion: %s", p, err)
		}
		if err := p.Promotion.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Promotion)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:promotion: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField14(oprot thrift.TProtocol) (err error) {
	if p.AdExchangeDmp != nil {
		if err := oprot.WriteFieldBegin("adExchangeDmp", thrift.STRUCT, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:adExchangeDmp: %s", p, err)
		}
		if err := p.AdExchangeDmp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdExchangeDmp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:adExchangeDmp: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) writeField15(oprot thrift.TProtocol) (err error) {
	if p.AdTracking != nil {
		if err := oprot.WriteFieldBegin("adTracking", thrift.STRUCT, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:adTracking: %s", p, err)
		}
		if err := p.AdTracking.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdTracking)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:adTracking: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterOperationEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BidMasterOperationEvent(%+v)", *p)
}
