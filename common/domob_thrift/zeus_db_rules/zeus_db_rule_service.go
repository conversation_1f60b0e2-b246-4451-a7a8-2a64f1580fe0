// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_db_rules

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/zeus_common"
	"rtb_model_server/common/domob_thrift/zeus_rule_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = zeus_common.GoUnusedProtection__
var _ = zeus_rule_types.GoUnusedProtection__

type ZeusDbRuleService interface {
	dm303.DomobService

	// 获取所有不处于STOP状态的Rule
	GetNotStopRules() (r []*zeus_rule_types.Rule, err error)
}

type ZeusDbRuleServiceClient struct {
	*dm303.DomobServiceClient
}

func NewZeusDbRuleServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ZeusDbRuleServiceClient {
	return &ZeusDbRuleServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewZeusDbRuleServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ZeusDbRuleServiceClient {
	return &ZeusDbRuleServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 获取所有不处于STOP状态的Rule
func (p *ZeusDbRuleServiceClient) GetNotStopRules() (r []*zeus_rule_types.Rule, err error) {
	if err = p.sendGetNotStopRules(); err != nil {
		return
	}
	return p.recvGetNotStopRules()
}

func (p *ZeusDbRuleServiceClient) sendGetNotStopRules() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getNotStopRules", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetNotStopRulesArgs()
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ZeusDbRuleServiceClient) recvGetNotStopRules() (value []*zeus_rule_types.Rule, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetNotStopRulesResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

type ZeusDbRuleServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewZeusDbRuleServiceProcessor(handler ZeusDbRuleService) *ZeusDbRuleServiceProcessor {
	self4 := &ZeusDbRuleServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self4.AddToProcessorMap("getNotStopRules", &zeusDbRuleServiceProcessorGetNotStopRules{handler: handler})
	return self4
}

type zeusDbRuleServiceProcessorGetNotStopRules struct {
	handler ZeusDbRuleService
}

func (p *zeusDbRuleServiceProcessorGetNotStopRules) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetNotStopRulesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getNotStopRules", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetNotStopRulesResult()
	if result.Success, err = p.handler.GetNotStopRules(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNotStopRules: "+err.Error())
		oprot.WriteMessageBegin("getNotStopRules", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getNotStopRules", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetNotStopRulesArgs struct {
}

func NewGetNotStopRulesArgs() *GetNotStopRulesArgs {
	return &GetNotStopRulesArgs{}
}

func (p *GetNotStopRulesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNotStopRulesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNotStopRules_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNotStopRulesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNotStopRulesArgs(%+v)", *p)
}

type GetNotStopRulesResult struct {
	Success []*zeus_rule_types.Rule `thrift:"success,0" json:"success"`
}

func NewGetNotStopRulesResult() *GetNotStopRulesResult {
	return &GetNotStopRulesResult{}
}

func (p *GetNotStopRulesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNotStopRulesResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*zeus_rule_types.Rule, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := zeus_rule_types.NewRule()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Success = append(p.Success, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetNotStopRulesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNotStopRules_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNotStopRulesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetNotStopRulesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNotStopRulesResult(%+v)", *p)
}
