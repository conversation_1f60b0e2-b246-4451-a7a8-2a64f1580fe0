// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package uid_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type RequstType int64

const (
	RequstType_MD5_MAPPING        RequstType = 1
	RequstType_GPS_DEVICE_MAPPING RequstType = 2
)

func (p RequstType) String() string {
	switch p {
	case RequstType_MD5_MAPPING:
		return "RequstType_MD5_MAPPING"
	case RequstType_GPS_DEVICE_MAPPING:
		return "RequstType_GPS_DEVICE_MAPPING"
	}
	return "<UNSET>"
}

func RequstTypeFromString(s string) (RequstType, error) {
	switch s {
	case "RequstType_MD5_MAPPING":
		return RequstType_MD5_MAPPING, nil
	case "RequstType_GPS_DEVICE_MAPPING":
		return RequstType_GPS_DEVICE_MAPPING, nil
	}
	return RequstType(math.MinInt32 - 1), fmt.Errorf("not a valid RequstType string")
}

type UidRequest struct {
	Idfa      string     `thrift:"idfa,1" json:"idfa"`
	Imei      string     `thrift:"imei,2" json:"imei"`
	Mac       string     `thrift:"mac,3" json:"mac"`
	Aaid      string     `thrift:"aaid,4" json:"aaid"`
	VisitorId string     `thrift:"visitor_id,5" json:"visitor_id"`
	Auth      int32      `thrift:"auth,6" json:"auth"`
	IdfaMd5   string     `thrift:"idfa_md5,7" json:"idfa_md5"`
	MacMd5    string     `thrift:"mac_md5,8" json:"mac_md5"`
	ImeiMd5   string     `thrift:"imei_md5,9" json:"imei_md5"`
	AaidMd5   string     `thrift:"aaid_md5,10" json:"aaid_md5"`
	Event     int32      `thrift:"event,11" json:"event"`
	GeoLat    float64    `thrift:"geo_lat,12" json:"geo_lat"`
	GeoLon    float64    `thrift:"geo_lon,13" json:"geo_lon"`
	DeviceId  int32      `thrift:"device_id,14" json:"device_id"`
	Rt        RequstType `thrift:"rt,15" json:"rt"`
}

func NewUidRequest() *UidRequest {
	return &UidRequest{
		Rt: 1,
	}
}

func (p *UidRequest) IsSetRt() bool {
	return int64(p.Rt) != math.MinInt32-1
}

func (p *UidRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UidRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *UidRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *UidRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *UidRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Aaid = v
	}
	return nil
}

func (p *UidRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.VisitorId = v
	}
	return nil
}

func (p *UidRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Auth = v
	}
	return nil
}

func (p *UidRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.IdfaMd5 = v
	}
	return nil
}

func (p *UidRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.MacMd5 = v
	}
	return nil
}

func (p *UidRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ImeiMd5 = v
	}
	return nil
}

func (p *UidRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AaidMd5 = v
	}
	return nil
}

func (p *UidRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Event = v
	}
	return nil
}

func (p *UidRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.GeoLat = v
	}
	return nil
}

func (p *UidRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.GeoLon = v
	}
	return nil
}

func (p *UidRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DeviceId = v
	}
	return nil
}

func (p *UidRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Rt = RequstType(v)
	}
	return nil
}

func (p *UidRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UidRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UidRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:idfa: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imei: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mac: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aaid", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:aaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Aaid)); err != nil {
		return fmt.Errorf("%T.aaid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:aaid: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("visitor_id", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:visitor_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VisitorId)); err != nil {
		return fmt.Errorf("%T.visitor_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:visitor_id: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:auth: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Auth)); err != nil {
		return fmt.Errorf("%T.auth (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:auth: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa_md5", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:idfa_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IdfaMd5)); err != nil {
		return fmt.Errorf("%T.idfa_md5 (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:idfa_md5: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac_md5", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:mac_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MacMd5)); err != nil {
		return fmt.Errorf("%T.mac_md5 (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:mac_md5: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_md5", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:imei_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMd5)); err != nil {
		return fmt.Errorf("%T.imei_md5 (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:imei_md5: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aaid_md5", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:aaid_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AaidMd5)); err != nil {
		return fmt.Errorf("%T.aaid_md5 (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:aaid_md5: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:event: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Event)); err != nil {
		return fmt.Errorf("%T.event (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:event: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("geo_lat", thrift.DOUBLE, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:geo_lat: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.GeoLat)); err != nil {
		return fmt.Errorf("%T.geo_lat (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:geo_lat: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("geo_lon", thrift.DOUBLE, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:geo_lon: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.GeoLon)); err != nil {
		return fmt.Errorf("%T.geo_lon (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:geo_lon: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_id", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:device_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceId)); err != nil {
		return fmt.Errorf("%T.device_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:device_id: %s", p, err)
	}
	return err
}

func (p *UidRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetRt() {
		if err := oprot.WriteFieldBegin("rt", thrift.I32, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:rt: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Rt)); err != nil {
			return fmt.Errorf("%T.rt (15) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:rt: %s", p, err)
		}
	}
	return err
}

func (p *UidRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UidRequest(%+v)", *p)
}

type UidResponse struct {
	Errstatus string `thrift:"errstatus,1" json:"errstatus"`
	Uid       int64  `thrift:"uid,2" json:"uid"`
	Ack       int32  `thrift:"ack,3" json:"ack"`
	Idfa      string `thrift:"idfa,4" json:"idfa"`
	Imei      string `thrift:"imei,5" json:"imei"`
	IdfaMd5   string `thrift:"idfa_md5,6" json:"idfa_md5"`
	ImeiMd5   string `thrift:"imei_md5,7" json:"imei_md5"`
}

func NewUidResponse() *UidResponse {
	return &UidResponse{}
}

func (p *UidResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UidResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Errstatus = v
	}
	return nil
}

func (p *UidResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *UidResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ack = v
	}
	return nil
}

func (p *UidResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *UidResponse) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *UidResponse) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IdfaMd5 = v
	}
	return nil
}

func (p *UidResponse) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ImeiMd5 = v
	}
	return nil
}

func (p *UidResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UidResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UidResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("errstatus", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:errstatus: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Errstatus)); err != nil {
		return fmt.Errorf("%T.errstatus (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:errstatus: %s", p, err)
	}
	return err
}

func (p *UidResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *UidResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ack", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ack: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ack)); err != nil {
		return fmt.Errorf("%T.ack (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ack: %s", p, err)
	}
	return err
}

func (p *UidResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:idfa: %s", p, err)
	}
	return err
}

func (p *UidResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:imei: %s", p, err)
	}
	return err
}

func (p *UidResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa_md5", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:idfa_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IdfaMd5)); err != nil {
		return fmt.Errorf("%T.idfa_md5 (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:idfa_md5: %s", p, err)
	}
	return err
}

func (p *UidResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_md5", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:imei_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMd5)); err != nil {
		return fmt.Errorf("%T.imei_md5 (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:imei_md5: %s", p, err)
	}
	return err
}

func (p *UidResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UidResponse(%+v)", *p)
}
