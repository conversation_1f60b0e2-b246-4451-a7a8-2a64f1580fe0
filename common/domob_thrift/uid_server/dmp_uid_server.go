// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package uid_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__

type DmpUidServer interface {
	dm303.DomobService

	// Parameters:
	//  - Ids
	GetUid(ids *UidRequest) (r *UidResponse, err error)
}

type DmpUidServerClient struct {
	*dm303.DomobServiceClient
}

func NewDmpUidServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DmpUidServerClient {
	return &DmpUidServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDmpUidServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DmpUidServerClient {
	return &DmpUidServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Ids
func (p *DmpUidServerClient) GetUid(ids *UidRequest) (r *UidResponse, err error) {
	if err = p.sendGetUid(ids); err != nil {
		return
	}
	return p.recvGetUid()
}

func (p *DmpUidServerClient) sendGetUid(ids *UidRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_uid", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetUidArgs()
	args0.Ids = ids
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DmpUidServerClient) recvGetUid() (value *UidResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetUidResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

type DmpUidServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDmpUidServerProcessor(handler DmpUidServer) *DmpUidServerProcessor {
	self4 := &DmpUidServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self4.AddToProcessorMap("get_uid", &dmpUidServerProcessorGetUid{handler: handler})
	return self4
}

type dmpUidServerProcessorGetUid struct {
	handler DmpUidServer
}

func (p *dmpUidServerProcessorGetUid) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetUidArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_uid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetUidResult()
	if result.Success, err = p.handler.GetUid(args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_uid: "+err.Error())
		oprot.WriteMessageBegin("get_uid", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_uid", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetUidArgs struct {
	Ids *UidRequest `thrift:"ids,1" json:"ids"`
}

func NewGetUidArgs() *GetUidArgs {
	return &GetUidArgs{}
}

func (p *GetUidArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUidArgs) readField1(iprot thrift.TProtocol) error {
	p.Ids = NewUidRequest()
	if err := p.Ids.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ids)
	}
	return nil
}

func (p *GetUidArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_uid_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUidArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ids: %s", p, err)
		}
		if err := p.Ids.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ids)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetUidArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUidArgs(%+v)", *p)
}

type GetUidResult struct {
	Success *UidResponse `thrift:"success,0" json:"success"`
}

func NewGetUidResult() *GetUidResult {
	return &GetUidResult{}
}

func (p *GetUidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetUidResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewUidResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetUidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_uid_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetUidResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetUidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUidResult(%+v)", *p)
}
