// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/vico_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = vico_types.GoUnusedProtection__
var GoUnusedProtection__ int

type VicoEventType int64

const (
	VicoEventType_VET_UNKNOWN                 VicoEventType = 0
	VicoEventType_VET_ADD                     VicoEventType = 1
	VicoEventType_VET_UPDATE                  VicoEventType = 2
	VicoEventType_VET_DELETE                  VicoEventType = 3
	VicoEventType_VET_PAUSE                   VicoEventType = 4
	VicoEventType_VET_RESUME                  VicoEventType = 5
	VicoEventType_VET_INNER_APPROVE_PASS      VicoEventType = 10
	VicoEventType_VET_INNER_APPROVE_REJECT    VicoEventType = 11
	VicoEventType_VET_PLATFORM_APPROVE_PASS   VicoEventType = 12
	VicoEventType_VET_PLATFORM_APPROVE_REJECT VicoEventType = 13
	VicoEventType_VET_STATUS_CHANGE           VicoEventType = 14
	VicoEventType_VET_PUBLISH                 VicoEventType = 20
	VicoEventType_VET_UNPUBLISH               VicoEventType = 21
	VicoEventType_VET_CRAW_FINISH             VicoEventType = 30
)

func (p VicoEventType) String() string {
	switch p {
	case VicoEventType_VET_UNKNOWN:
		return "VicoEventType_VET_UNKNOWN"
	case VicoEventType_VET_ADD:
		return "VicoEventType_VET_ADD"
	case VicoEventType_VET_UPDATE:
		return "VicoEventType_VET_UPDATE"
	case VicoEventType_VET_DELETE:
		return "VicoEventType_VET_DELETE"
	case VicoEventType_VET_PAUSE:
		return "VicoEventType_VET_PAUSE"
	case VicoEventType_VET_RESUME:
		return "VicoEventType_VET_RESUME"
	case VicoEventType_VET_INNER_APPROVE_PASS:
		return "VicoEventType_VET_INNER_APPROVE_PASS"
	case VicoEventType_VET_INNER_APPROVE_REJECT:
		return "VicoEventType_VET_INNER_APPROVE_REJECT"
	case VicoEventType_VET_PLATFORM_APPROVE_PASS:
		return "VicoEventType_VET_PLATFORM_APPROVE_PASS"
	case VicoEventType_VET_PLATFORM_APPROVE_REJECT:
		return "VicoEventType_VET_PLATFORM_APPROVE_REJECT"
	case VicoEventType_VET_STATUS_CHANGE:
		return "VicoEventType_VET_STATUS_CHANGE"
	case VicoEventType_VET_PUBLISH:
		return "VicoEventType_VET_PUBLISH"
	case VicoEventType_VET_UNPUBLISH:
		return "VicoEventType_VET_UNPUBLISH"
	case VicoEventType_VET_CRAW_FINISH:
		return "VicoEventType_VET_CRAW_FINISH"
	}
	return "<UNSET>"
}

func VicoEventTypeFromString(s string) (VicoEventType, error) {
	switch s {
	case "VicoEventType_VET_UNKNOWN":
		return VicoEventType_VET_UNKNOWN, nil
	case "VicoEventType_VET_ADD":
		return VicoEventType_VET_ADD, nil
	case "VicoEventType_VET_UPDATE":
		return VicoEventType_VET_UPDATE, nil
	case "VicoEventType_VET_DELETE":
		return VicoEventType_VET_DELETE, nil
	case "VicoEventType_VET_PAUSE":
		return VicoEventType_VET_PAUSE, nil
	case "VicoEventType_VET_RESUME":
		return VicoEventType_VET_RESUME, nil
	case "VicoEventType_VET_INNER_APPROVE_PASS":
		return VicoEventType_VET_INNER_APPROVE_PASS, nil
	case "VicoEventType_VET_INNER_APPROVE_REJECT":
		return VicoEventType_VET_INNER_APPROVE_REJECT, nil
	case "VicoEventType_VET_PLATFORM_APPROVE_PASS":
		return VicoEventType_VET_PLATFORM_APPROVE_PASS, nil
	case "VicoEventType_VET_PLATFORM_APPROVE_REJECT":
		return VicoEventType_VET_PLATFORM_APPROVE_REJECT, nil
	case "VicoEventType_VET_STATUS_CHANGE":
		return VicoEventType_VET_STATUS_CHANGE, nil
	case "VicoEventType_VET_PUBLISH":
		return VicoEventType_VET_PUBLISH, nil
	case "VicoEventType_VET_UNPUBLISH":
		return VicoEventType_VET_UNPUBLISH, nil
	case "VicoEventType_VET_CRAW_FINISH":
		return VicoEventType_VET_CRAW_FINISH, nil
	}
	return VicoEventType(math.MinInt32 - 1), fmt.Errorf("not a valid VicoEventType string")
}

type VicoEventCategory int64

const (
	VicoEventCategory_DEC_UNKNOWN           VicoEventCategory = 0
	VicoEventCategory_DEC_AD_ORDER          VicoEventCategory = 1
	VicoEventCategory_DEC_AD_ORDER_CAMPAIGN VicoEventCategory = 2
	VicoEventCategory_DEC_PRODUCT           VicoEventCategory = 3
	VicoEventCategory_DEC_PRODUCT_PACK      VicoEventCategory = 4
	VicoEventCategory_DEC_AD_LANDING        VicoEventCategory = 5
	VicoEventCategory_DEC_ACCOUNT           VicoEventCategory = 6
	VicoEventCategory_DEC_SOCIAL_MEDIA_POST VicoEventCategory = 7
	VicoEventCategory_DEC_FINANCE_ORDER     VicoEventCategory = 8
)

func (p VicoEventCategory) String() string {
	switch p {
	case VicoEventCategory_DEC_UNKNOWN:
		return "VicoEventCategory_DEC_UNKNOWN"
	case VicoEventCategory_DEC_AD_ORDER:
		return "VicoEventCategory_DEC_AD_ORDER"
	case VicoEventCategory_DEC_AD_ORDER_CAMPAIGN:
		return "VicoEventCategory_DEC_AD_ORDER_CAMPAIGN"
	case VicoEventCategory_DEC_PRODUCT:
		return "VicoEventCategory_DEC_PRODUCT"
	case VicoEventCategory_DEC_PRODUCT_PACK:
		return "VicoEventCategory_DEC_PRODUCT_PACK"
	case VicoEventCategory_DEC_AD_LANDING:
		return "VicoEventCategory_DEC_AD_LANDING"
	case VicoEventCategory_DEC_ACCOUNT:
		return "VicoEventCategory_DEC_ACCOUNT"
	case VicoEventCategory_DEC_SOCIAL_MEDIA_POST:
		return "VicoEventCategory_DEC_SOCIAL_MEDIA_POST"
	case VicoEventCategory_DEC_FINANCE_ORDER:
		return "VicoEventCategory_DEC_FINANCE_ORDER"
	}
	return "<UNSET>"
}

func VicoEventCategoryFromString(s string) (VicoEventCategory, error) {
	switch s {
	case "VicoEventCategory_DEC_UNKNOWN":
		return VicoEventCategory_DEC_UNKNOWN, nil
	case "VicoEventCategory_DEC_AD_ORDER":
		return VicoEventCategory_DEC_AD_ORDER, nil
	case "VicoEventCategory_DEC_AD_ORDER_CAMPAIGN":
		return VicoEventCategory_DEC_AD_ORDER_CAMPAIGN, nil
	case "VicoEventCategory_DEC_PRODUCT":
		return VicoEventCategory_DEC_PRODUCT, nil
	case "VicoEventCategory_DEC_PRODUCT_PACK":
		return VicoEventCategory_DEC_PRODUCT_PACK, nil
	case "VicoEventCategory_DEC_AD_LANDING":
		return VicoEventCategory_DEC_AD_LANDING, nil
	case "VicoEventCategory_DEC_ACCOUNT":
		return VicoEventCategory_DEC_ACCOUNT, nil
	case "VicoEventCategory_DEC_SOCIAL_MEDIA_POST":
		return VicoEventCategory_DEC_SOCIAL_MEDIA_POST, nil
	case "VicoEventCategory_DEC_FINANCE_ORDER":
		return VicoEventCategory_DEC_FINANCE_ORDER, nil
	}
	return VicoEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid VicoEventCategory string")
}

type VicoCommonEvent struct {
	TypeA1   VicoEventType     `thrift:"type,1" json:"type"`
	Category VicoEventCategory `thrift:"category,2" json:"category"`
	Ids      []int64           `thrift:"ids,3" json:"ids"`
}

func NewVicoCommonEvent() *VicoCommonEvent {
	return &VicoCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *VicoCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *VicoCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *VicoCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = VicoEventType(v)
	}
	return nil
}

func (p *VicoCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = VicoEventCategory(v)
	}
	return nil
}

func (p *VicoCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VicoCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *VicoCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *VicoCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *VicoCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoCommonEvent(%+v)", *p)
}
