// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dv_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//DvServiceException中可能出现的异常代码
type DvServiceExceptionCode int64

const (
	DvServiceExceptionCode_ERROR_SYSTEM_ERROR   DvServiceExceptionCode = 1
	DvServiceExceptionCode_ERROR_TYPE_NOT_FOUND DvServiceExceptionCode = 2
)

func (p DvServiceExceptionCode) String() string {
	switch p {
	case DvServiceExceptionCode_ERROR_SYSTEM_ERROR:
		return "DvServiceExceptionCode_ERROR_SYSTEM_ERROR"
	case DvServiceExceptionCode_ERROR_TYPE_NOT_FOUND:
		return "DvServiceExceptionCode_ERROR_TYPE_NOT_FOUND"
	}
	return "<UNSET>"
}

func DvServiceExceptionCodeFromString(s string) (DvServiceExceptionCode, error) {
	switch s {
	case "DvServiceExceptionCode_ERROR_SYSTEM_ERROR":
		return DvServiceExceptionCode_ERROR_SYSTEM_ERROR, nil
	case "DvServiceExceptionCode_ERROR_TYPE_NOT_FOUND":
		return DvServiceExceptionCode_ERROR_TYPE_NOT_FOUND, nil
	}
	return DvServiceExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid DvServiceExceptionCode string")
}

type DvServiceException struct {
	Code    DvServiceExceptionCode `thrift:"code,1" json:"code"`
	Message string                 `thrift:"message,2" json:"message"`
}

func NewDvServiceException() *DvServiceException {
	return &DvServiceException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DvServiceException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DvServiceException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DvServiceException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DvServiceExceptionCode(v)
	}
	return nil
}

func (p *DvServiceException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DvServiceException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DvServiceException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DvServiceException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DvServiceException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DvServiceException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DvServiceException(%+v)", *p)
}

type DvString struct {
	Version       string `thrift:"version,1" json:"version"`
	WholeTypeName string `thrift:"wholeTypeName,2" json:"wholeTypeName"`
	Result        string `thrift:"result,3" json:"result"`
}

func NewDvString() *DvString {
	return &DvString{}
}

func (p *DvString) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DvString) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *DvString) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.WholeTypeName = v
	}
	return nil
}

func (p *DvString) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Result = v
	}
	return nil
}

func (p *DvString) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DvString"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DvString) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:version: %s", p, err)
	}
	return err
}

func (p *DvString) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wholeTypeName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:wholeTypeName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WholeTypeName)); err != nil {
		return fmt.Errorf("%T.wholeTypeName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:wholeTypeName: %s", p, err)
	}
	return err
}

func (p *DvString) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:result: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Result)); err != nil {
		return fmt.Errorf("%T.result (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:result: %s", p, err)
	}
	return err
}

func (p *DvString) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DvString(%+v)", *p)
}

type DvBinary struct {
	Version       string `thrift:"version,1" json:"version"`
	WholeTypeName string `thrift:"wholeTypeName,2" json:"wholeTypeName"`
	Result        []byte `thrift:"result,3" json:"result"`
}

func NewDvBinary() *DvBinary {
	return &DvBinary{}
}

func (p *DvBinary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DvBinary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *DvBinary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.WholeTypeName = v
	}
	return nil
}

func (p *DvBinary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Result = v
	}
	return nil
}

func (p *DvBinary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DvBinary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DvBinary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:version: %s", p, err)
	}
	return err
}

func (p *DvBinary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wholeTypeName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:wholeTypeName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WholeTypeName)); err != nil {
		return fmt.Errorf("%T.wholeTypeName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:wholeTypeName: %s", p, err)
	}
	return err
}

func (p *DvBinary) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.STRING, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:result: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Result); err != nil {
			return fmt.Errorf("%T.result (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:result: %s", p, err)
		}
	}
	return err
}

func (p *DvBinary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DvBinary(%+v)", *p)
}
