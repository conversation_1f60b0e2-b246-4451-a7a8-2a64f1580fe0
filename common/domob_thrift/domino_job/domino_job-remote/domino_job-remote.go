// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"domino_job"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  TimeSpan getJobTimeSpan(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "   getJobListByPlanId(RequestHeader header, IdInt planId, TimeInt startSchedTime, TimeInt endSchedTime)")
	fmt.Fprintln(os.Stderr, "  Job getJobById(RequestHeader header, IdInt jobId)")
	fmt.Fprintln(os.Stderr, "   getJobListByStatus(RequestHeader header, IdInt planId, JobStatus status)")
	fmt.Fprintln(os.Stderr, "   getPlansByUserHost(RequestHeader header, string user, string host)")
	fmt.Fprintln(os.Stderr, "   getDepJobs(RequestHeader header, IdInt planId, TimeInt stamp, i32 level)")
	fmt.Fprintln(os.Stderr, "   getJobsDep(RequestHeader header, IdInt planId, TimeInt stamp, i32 level)")
	fmt.Fprintln(os.Stderr, "  IdInt saveFailJob(RequestHeader header, IdInt jobId, JobOutput output, IdInt plancliThreadId)")
	fmt.Fprintln(os.Stderr, "   getFailJobByPlanId(RequestHeader header, IdInt planId)")
	fmt.Fprintln(os.Stderr, "   getFailJobByJobId(RequestHeader header, IdInt planId, TimeInt stamp)")
	fmt.Fprintln(os.Stderr, "  Job getFailJobByFailJobId(RequestHeader header, IdInt failJobId)")
	fmt.Fprintln(os.Stderr, "   getSuccedJobsByJobId(RequestHeader header, IdInt JobId, IdInt Deepth)")
	fmt.Fprintln(os.Stderr, "   getLogFilesName(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getFileContent(RequestHeader header, string fileName)")
	fmt.Fprintln(os.Stderr, "   getCreatedJobsByUserHost(RequestHeader header, string username, string hostname)")
	fmt.Fprintln(os.Stderr, "  IdInt insertEasyPlancliThreadId(RequestHeader header, IdInt jobId, IdInt plancliThreadId)")
	fmt.Fprintln(os.Stderr, "  Plan checkDepByJobId(RequestHeader header, IdInt jobId, IdInt plancliThreadId)")
	fmt.Fprintln(os.Stderr, "  IdInt updateJobStatus(RequestHeader header, IdInt jobId, JobStatus status, JobOutput output, IdInt planCliId)")
	fmt.Fprintln(os.Stderr, "  IdInt updateLastRunTime(RequestHeader header, IdInt jobId, IdInt planCliId)")
	fmt.Fprintln(os.Stderr, "  Job getJobByPlanNameStamp(RequestHeader header, string planName, TimeInt stamp)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := domino_job.NewDominoJobClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getJobTimeSpan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetJobTimeSpan requires 2 args")
			flag.Usage()
		}
		arg92 := flag.Arg(1)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		argvalue0 := domino_job.NewRequestHeader()
		err97 := argvalue0.Read(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err98 := (strconv.Atoi(flag.Arg(2)))
		if err98 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		fmt.Print(client.GetJobTimeSpan(value0, value1))
		fmt.Print("\n")
		break
	case "getJobListByPlanId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetJobListByPlanId requires 4 args")
			flag.Usage()
		}
		arg99 := flag.Arg(1)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue0 := domino_job.NewRequestHeader()
		err104 := argvalue0.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err105 := (strconv.Atoi(flag.Arg(2)))
		if err105 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		argvalue2, err106 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err106 != nil {
			Usage()
			return
		}
		value2 := domino_job.TimeInt(argvalue2)
		argvalue3, err107 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err107 != nil {
			Usage()
			return
		}
		value3 := domino_job.TimeInt(argvalue3)
		fmt.Print(client.GetJobListByPlanId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getJobById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetJobById requires 2 args")
			flag.Usage()
		}
		arg108 := flag.Arg(1)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue0 := domino_job.NewRequestHeader()
		err113 := argvalue0.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err114 := (strconv.Atoi(flag.Arg(2)))
		if err114 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		fmt.Print(client.GetJobById(value0, value1))
		fmt.Print("\n")
		break
	case "getJobListByStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetJobListByStatus requires 3 args")
			flag.Usage()
		}
		arg115 := flag.Arg(1)
		mbTrans116 := thrift.NewTMemoryBufferLen(len(arg115))
		defer mbTrans116.Close()
		_, err117 := mbTrans116.WriteString(arg115)
		if err117 != nil {
			Usage()
			return
		}
		factory118 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt119 := factory118.GetProtocol(mbTrans116)
		argvalue0 := domino_job.NewRequestHeader()
		err120 := argvalue0.Read(jsProt119)
		if err120 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err121 := (strconv.Atoi(flag.Arg(2)))
		if err121 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := domino_job.JobStatus(tmp2)
		value2 := domino_job.JobStatus(argvalue2)
		fmt.Print(client.GetJobListByStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getPlansByUserHost":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetPlansByUserHost requires 3 args")
			flag.Usage()
		}
		arg122 := flag.Arg(1)
		mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
		defer mbTrans123.Close()
		_, err124 := mbTrans123.WriteString(arg122)
		if err124 != nil {
			Usage()
			return
		}
		factory125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt126 := factory125.GetProtocol(mbTrans123)
		argvalue0 := domino_job.NewRequestHeader()
		err127 := argvalue0.Read(jsProt126)
		if err127 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.GetPlansByUserHost(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getDepJobs":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetDepJobs requires 4 args")
			flag.Usage()
		}
		arg130 := flag.Arg(1)
		mbTrans131 := thrift.NewTMemoryBufferLen(len(arg130))
		defer mbTrans131.Close()
		_, err132 := mbTrans131.WriteString(arg130)
		if err132 != nil {
			Usage()
			return
		}
		factory133 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt134 := factory133.GetProtocol(mbTrans131)
		argvalue0 := domino_job.NewRequestHeader()
		err135 := argvalue0.Read(jsProt134)
		if err135 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err136 := (strconv.Atoi(flag.Arg(2)))
		if err136 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		argvalue2, err137 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err137 != nil {
			Usage()
			return
		}
		value2 := domino_job.TimeInt(argvalue2)
		tmp3, err138 := (strconv.Atoi(flag.Arg(4)))
		if err138 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.GetDepJobs(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getJobsDep":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetJobsDep requires 4 args")
			flag.Usage()
		}
		arg139 := flag.Arg(1)
		mbTrans140 := thrift.NewTMemoryBufferLen(len(arg139))
		defer mbTrans140.Close()
		_, err141 := mbTrans140.WriteString(arg139)
		if err141 != nil {
			Usage()
			return
		}
		factory142 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt143 := factory142.GetProtocol(mbTrans140)
		argvalue0 := domino_job.NewRequestHeader()
		err144 := argvalue0.Read(jsProt143)
		if err144 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err145 := (strconv.Atoi(flag.Arg(2)))
		if err145 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		argvalue2, err146 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err146 != nil {
			Usage()
			return
		}
		value2 := domino_job.TimeInt(argvalue2)
		tmp3, err147 := (strconv.Atoi(flag.Arg(4)))
		if err147 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.GetJobsDep(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "saveFailJob":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SaveFailJob requires 4 args")
			flag.Usage()
		}
		arg148 := flag.Arg(1)
		mbTrans149 := thrift.NewTMemoryBufferLen(len(arg148))
		defer mbTrans149.Close()
		_, err150 := mbTrans149.WriteString(arg148)
		if err150 != nil {
			Usage()
			return
		}
		factory151 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt152 := factory151.GetProtocol(mbTrans149)
		argvalue0 := domino_job.NewRequestHeader()
		err153 := argvalue0.Read(jsProt152)
		if err153 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err154 := (strconv.Atoi(flag.Arg(2)))
		if err154 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		arg155 := flag.Arg(3)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		argvalue2 := domino_job.NewJobOutput()
		err160 := argvalue2.Read(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		value2 := domino_job.JobOutput(argvalue2)
		tmp3, err161 := (strconv.Atoi(flag.Arg(4)))
		if err161 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := domino_job.IdInt(argvalue3)
		fmt.Print(client.SaveFailJob(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getFailJobByPlanId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFailJobByPlanId requires 2 args")
			flag.Usage()
		}
		arg162 := flag.Arg(1)
		mbTrans163 := thrift.NewTMemoryBufferLen(len(arg162))
		defer mbTrans163.Close()
		_, err164 := mbTrans163.WriteString(arg162)
		if err164 != nil {
			Usage()
			return
		}
		factory165 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt166 := factory165.GetProtocol(mbTrans163)
		argvalue0 := domino_job.NewRequestHeader()
		err167 := argvalue0.Read(jsProt166)
		if err167 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err168 := (strconv.Atoi(flag.Arg(2)))
		if err168 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		fmt.Print(client.GetFailJobByPlanId(value0, value1))
		fmt.Print("\n")
		break
	case "getFailJobByJobId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFailJobByJobId requires 3 args")
			flag.Usage()
		}
		arg169 := flag.Arg(1)
		mbTrans170 := thrift.NewTMemoryBufferLen(len(arg169))
		defer mbTrans170.Close()
		_, err171 := mbTrans170.WriteString(arg169)
		if err171 != nil {
			Usage()
			return
		}
		factory172 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt173 := factory172.GetProtocol(mbTrans170)
		argvalue0 := domino_job.NewRequestHeader()
		err174 := argvalue0.Read(jsProt173)
		if err174 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err175 := (strconv.Atoi(flag.Arg(2)))
		if err175 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		argvalue2, err176 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err176 != nil {
			Usage()
			return
		}
		value2 := domino_job.TimeInt(argvalue2)
		fmt.Print(client.GetFailJobByJobId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFailJobByFailJobId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFailJobByFailJobId requires 2 args")
			flag.Usage()
		}
		arg177 := flag.Arg(1)
		mbTrans178 := thrift.NewTMemoryBufferLen(len(arg177))
		defer mbTrans178.Close()
		_, err179 := mbTrans178.WriteString(arg177)
		if err179 != nil {
			Usage()
			return
		}
		factory180 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt181 := factory180.GetProtocol(mbTrans178)
		argvalue0 := domino_job.NewRequestHeader()
		err182 := argvalue0.Read(jsProt181)
		if err182 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err183 := (strconv.Atoi(flag.Arg(2)))
		if err183 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		fmt.Print(client.GetFailJobByFailJobId(value0, value1))
		fmt.Print("\n")
		break
	case "getSuccedJobsByJobId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetSuccedJobsByJobId requires 3 args")
			flag.Usage()
		}
		arg184 := flag.Arg(1)
		mbTrans185 := thrift.NewTMemoryBufferLen(len(arg184))
		defer mbTrans185.Close()
		_, err186 := mbTrans185.WriteString(arg184)
		if err186 != nil {
			Usage()
			return
		}
		factory187 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt188 := factory187.GetProtocol(mbTrans185)
		argvalue0 := domino_job.NewRequestHeader()
		err189 := argvalue0.Read(jsProt188)
		if err189 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err190 := (strconv.Atoi(flag.Arg(2)))
		if err190 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		tmp2, err191 := (strconv.Atoi(flag.Arg(3)))
		if err191 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := domino_job.IdInt(argvalue2)
		fmt.Print(client.GetSuccedJobsByJobId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getLogFilesName":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetLogFilesName requires 1 args")
			flag.Usage()
		}
		arg192 := flag.Arg(1)
		mbTrans193 := thrift.NewTMemoryBufferLen(len(arg192))
		defer mbTrans193.Close()
		_, err194 := mbTrans193.WriteString(arg192)
		if err194 != nil {
			Usage()
			return
		}
		factory195 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt196 := factory195.GetProtocol(mbTrans193)
		argvalue0 := domino_job.NewRequestHeader()
		err197 := argvalue0.Read(jsProt196)
		if err197 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		fmt.Print(client.GetLogFilesName(value0))
		fmt.Print("\n")
		break
	case "getFileContent":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFileContent requires 2 args")
			flag.Usage()
		}
		arg198 := flag.Arg(1)
		mbTrans199 := thrift.NewTMemoryBufferLen(len(arg198))
		defer mbTrans199.Close()
		_, err200 := mbTrans199.WriteString(arg198)
		if err200 != nil {
			Usage()
			return
		}
		factory201 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt202 := factory201.GetProtocol(mbTrans199)
		argvalue0 := domino_job.NewRequestHeader()
		err203 := argvalue0.Read(jsProt202)
		if err203 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetFileContent(value0, value1))
		fmt.Print("\n")
		break
	case "getCreatedJobsByUserHost":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetCreatedJobsByUserHost requires 3 args")
			flag.Usage()
		}
		arg205 := flag.Arg(1)
		mbTrans206 := thrift.NewTMemoryBufferLen(len(arg205))
		defer mbTrans206.Close()
		_, err207 := mbTrans206.WriteString(arg205)
		if err207 != nil {
			Usage()
			return
		}
		factory208 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt209 := factory208.GetProtocol(mbTrans206)
		argvalue0 := domino_job.NewRequestHeader()
		err210 := argvalue0.Read(jsProt209)
		if err210 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.GetCreatedJobsByUserHost(value0, value1, value2))
		fmt.Print("\n")
		break
	case "insertEasyPlancliThreadId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InsertEasyPlancliThreadId requires 3 args")
			flag.Usage()
		}
		arg213 := flag.Arg(1)
		mbTrans214 := thrift.NewTMemoryBufferLen(len(arg213))
		defer mbTrans214.Close()
		_, err215 := mbTrans214.WriteString(arg213)
		if err215 != nil {
			Usage()
			return
		}
		factory216 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt217 := factory216.GetProtocol(mbTrans214)
		argvalue0 := domino_job.NewRequestHeader()
		err218 := argvalue0.Read(jsProt217)
		if err218 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err219 := (strconv.Atoi(flag.Arg(2)))
		if err219 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		tmp2, err220 := (strconv.Atoi(flag.Arg(3)))
		if err220 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := domino_job.IdInt(argvalue2)
		fmt.Print(client.InsertEasyPlancliThreadId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "checkDepByJobId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CheckDepByJobId requires 3 args")
			flag.Usage()
		}
		arg221 := flag.Arg(1)
		mbTrans222 := thrift.NewTMemoryBufferLen(len(arg221))
		defer mbTrans222.Close()
		_, err223 := mbTrans222.WriteString(arg221)
		if err223 != nil {
			Usage()
			return
		}
		factory224 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt225 := factory224.GetProtocol(mbTrans222)
		argvalue0 := domino_job.NewRequestHeader()
		err226 := argvalue0.Read(jsProt225)
		if err226 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err227 := (strconv.Atoi(flag.Arg(2)))
		if err227 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		tmp2, err228 := (strconv.Atoi(flag.Arg(3)))
		if err228 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := domino_job.IdInt(argvalue2)
		fmt.Print(client.CheckDepByJobId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateJobStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "UpdateJobStatus requires 5 args")
			flag.Usage()
		}
		arg229 := flag.Arg(1)
		mbTrans230 := thrift.NewTMemoryBufferLen(len(arg229))
		defer mbTrans230.Close()
		_, err231 := mbTrans230.WriteString(arg229)
		if err231 != nil {
			Usage()
			return
		}
		factory232 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt233 := factory232.GetProtocol(mbTrans230)
		argvalue0 := domino_job.NewRequestHeader()
		err234 := argvalue0.Read(jsProt233)
		if err234 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err235 := (strconv.Atoi(flag.Arg(2)))
		if err235 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := domino_job.JobStatus(tmp2)
		value2 := domino_job.JobStatus(argvalue2)
		arg236 := flag.Arg(4)
		mbTrans237 := thrift.NewTMemoryBufferLen(len(arg236))
		defer mbTrans237.Close()
		_, err238 := mbTrans237.WriteString(arg236)
		if err238 != nil {
			Usage()
			return
		}
		factory239 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt240 := factory239.GetProtocol(mbTrans237)
		argvalue3 := domino_job.NewJobOutput()
		err241 := argvalue3.Read(jsProt240)
		if err241 != nil {
			Usage()
			return
		}
		value3 := domino_job.JobOutput(argvalue3)
		tmp4, err242 := (strconv.Atoi(flag.Arg(5)))
		if err242 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := domino_job.IdInt(argvalue4)
		fmt.Print(client.UpdateJobStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "updateLastRunTime":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateLastRunTime requires 3 args")
			flag.Usage()
		}
		arg243 := flag.Arg(1)
		mbTrans244 := thrift.NewTMemoryBufferLen(len(arg243))
		defer mbTrans244.Close()
		_, err245 := mbTrans244.WriteString(arg243)
		if err245 != nil {
			Usage()
			return
		}
		factory246 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt247 := factory246.GetProtocol(mbTrans244)
		argvalue0 := domino_job.NewRequestHeader()
		err248 := argvalue0.Read(jsProt247)
		if err248 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		tmp1, err249 := (strconv.Atoi(flag.Arg(2)))
		if err249 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := domino_job.IdInt(argvalue1)
		tmp2, err250 := (strconv.Atoi(flag.Arg(3)))
		if err250 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := domino_job.IdInt(argvalue2)
		fmt.Print(client.UpdateLastRunTime(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getJobByPlanNameStamp":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetJobByPlanNameStamp requires 3 args")
			flag.Usage()
		}
		arg251 := flag.Arg(1)
		mbTrans252 := thrift.NewTMemoryBufferLen(len(arg251))
		defer mbTrans252.Close()
		_, err253 := mbTrans252.WriteString(arg251)
		if err253 != nil {
			Usage()
			return
		}
		factory254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt255 := factory254.GetProtocol(mbTrans252)
		argvalue0 := domino_job.NewRequestHeader()
		err256 := argvalue0.Read(jsProt255)
		if err256 != nil {
			Usage()
			return
		}
		value0 := domino_job.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2, err258 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err258 != nil {
			Usage()
			return
		}
		value2 := domino_job.TimeInt(argvalue2)
		fmt.Print(client.GetJobByPlanNameStamp(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err263 := (strconv.Atoi(flag.Arg(1)))
		if err263 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
