// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package domino_job

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/domino_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = domino_types.GoUnusedProtection__

type DominoJob interface {
	dm303.DomobService
	//任务运行信息
	//
	//Plan的一次运行为一个Job，每次运行可以用planName,stamp唯一确定一个Job
	//
	//很多函数抛出DominoException
	//当code是ERROR_SYSTEM_ERROR时，客户端应重试
	//否则是不可恢复的异常，客户端程序应该退出。如job被删除，状态转化不和逻辑等

	// 获取任务已经执行过的Job的时间范围
	// 在展示任务的Job列表时，按照时间进行分页
	// 通过此结果可以计算出页码数量
	//
	// 返回null表示没有结果
	//
	// Parameters:
	//  - Header
	//  - PlanId
	GetJobTimeSpan(header *common.RequestHeader, planId IdInt) (r *domino_types.TimeSpan, de *domino_types.DominoException, err error)
	// 获取某个时间范围内（闭区间）的任务的执行列表
	// 按照任务的调度时间过滤
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - StartSchedTime
	//  - EndSchedTime
	GetJobListByPlanId(header *common.RequestHeader, planId IdInt, startSchedTime TimeInt, endSchedTime TimeInt) (r []*domino_types.Job, de *domino_types.DominoException, err error)
	// 依据jobId获取job的详情
	// 详情包括stdout和stderr
	//
	// Parameters:
	//  - Header
	//  - JobId
	GetJobById(header *common.RequestHeader, jobId IdInt) (r *domino_types.Job, de *domino_types.DominoException, err error)
	// 获取某个状态的job列表
	//
	// 通常一个Plan的大部分Job都应该是成功状态，少数处于等待，运行中或者失败状态
	// 这个接口大部分用于获取非正常执行的
	//
	// 按照时间倒序，最晚的在数据最开始
	// 结果不包含任务的output
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - Status
	GetJobListByStatus(header *common.RequestHeader, planId IdInt, status JobStatus) (r []*domino_types.Job, de *domino_types.DominoException, err error)
	// 根据用户名和机器名，获取需要运行的plan列表
	//
	// Parameters:
	//  - Header
	//  - User
	//  - Host
	GetPlansByUserHost(header *common.RequestHeader, user string, host string) (r []*domino_types.Plan, de *domino_types.DominoException, err error)
	// 获取本job依赖的jobs
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - Stamp
	//  - Level
	GetDepJobs(header *common.RequestHeader, planId IdInt, stamp TimeInt, level int32) (r []*domino_types.JobDepDetail, de *domino_types.DominoException, err error)
	// 获取依赖于本job的jobs
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - Stamp
	//  - Level
	GetJobsDep(header *common.RequestHeader, planId IdInt, stamp TimeInt, level int32) (r []*domino_types.JobDepDetail, de *domino_types.DominoException, err error)
	// 接口提供给plancli，执行失败的信息写入plan_fail_job数据表
	//
	// Parameters:
	//  - Header
	//  - JobId
	//  - Output
	//  - PlancliThreadId
	SaveFailJob(header *common.RequestHeader, jobId IdInt, output *domino_types.JobOutput, plancliThreadId IdInt) (r IdInt, de *domino_types.DominoException, err error)
	// 获取plan的失败任务
	// 数据来自于plan_fail_job中数据
	//
	// Parameters:
	//  - Header
	//  - PlanId
	GetFailJobByPlanId(header *common.RequestHeader, planId IdInt) (r []*domino_types.Job, de *domino_types.DominoException, err error)
	// 获取job的失败任务
	// 数据表plan_fail_job中数据
	//
	// Parameters:
	//  - Header
	//  - PlanId
	//  - Stamp
	GetFailJobByJobId(header *common.RequestHeader, planId IdInt, stamp TimeInt) (r []*domino_types.Job, de *domino_types.DominoException, err error)
	// 获取job的某一个指定失败任务
	// 数据表plan_fail_job中数据
	//
	// Parameters:
	//  - Header
	//  - FailJobId
	GetFailJobByFailJobId(header *common.RequestHeader, failJobId IdInt) (r *domino_types.Job, de *domino_types.DominoException, err error)
	// 获取依赖于job的所有job
	// 返回依赖于此job的所有job
	//
	// Parameters:
	//  - Header
	//  - JobId
	//  - Deepth
	GetSuccedJobsByJobId(header *common.RequestHeader, JobId IdInt, Deepth IdInt) (r []*domino_types.Job, de *domino_types.DominoException, err error)
	// 获取log文件名
	//
	// Parameters:
	//  - Header
	GetLogFilesName(header *common.RequestHeader) (r []string, de *domino_types.DominoException, err error)
	// 依据文件名获取文件内容
	// 返回string的list
	//
	// Parameters:
	//  - Header
	//  - FileName
	GetFileContent(header *common.RequestHeader, fileName string) (r []string, de *domino_types.DominoException, err error)
	// 根据user和host创建job
	// 该方法调用四个创建job函数
	// 分别创建正常运行，重跑的，丢失的，僵尸的
	// 合并四次创建
	// 返回job的list
	//
	// Parameters:
	//  - Header
	//  - Username
	//  - Hostname
	GetCreatedJobsByUserHost(header *common.RequestHeader, username string, hostname string) (r []*domino_types.Job, de *domino_types.DominoException, err error)
	// 更新job的plancli进程号
	// 先检查job状态是否合法，非法则抛出异常
	//
	// Parameters:
	//  - Header
	//  - JobId
	//  - PlancliThreadId
	InsertEasyPlancliThreadId(header *common.RequestHeader, jobId IdInt, plancliThreadId IdInt) (r IdInt, de *domino_types.DominoException, err error)
	// 检查任务依赖
	// 使用job的id作为参数，需要首先获取该job的plan
	// 如果任务能够开始运行，则返回任务详情
	// 否则返回空plan
	//
	// Parameters:
	//  - Header
	//  - JobId
	//  - PlancliThreadId
	CheckDepByJobId(header *common.RequestHeader, jobId IdInt, plancliThreadId IdInt) (r *domino_types.Plan, de *domino_types.DominoException, err error)
	// 更新任务状态
	//
	// 更新成功会返回Job的id
	// 有问题返回-1，客户端退出
	//
	//
	// Parameters:
	//  - Header
	//  - JobId
	//  - Status
	//  - Output
	//  - PlanCliId
	UpdateJobStatus(header *common.RequestHeader, jobId IdInt, status JobStatus, output *domino_types.JobOutput, planCliId IdInt) (r IdInt, de *domino_types.DominoException, err error)
	// 更新最后运行时间
	//
	// Parameters:
	//  - Header
	//  - JobId
	//  - PlanCliId
	UpdateLastRunTime(header *common.RequestHeader, jobId IdInt, planCliId IdInt) (r IdInt, de *domino_types.DominoException, err error)
	// 为支持plancli的手工执行
	// 依据planname和stamp获取jobId
	// 如果没有job，创建job
	//
	// Parameters:
	//  - Header
	//  - PlanName
	//  - Stamp
	GetJobByPlanNameStamp(header *common.RequestHeader, planName string, stamp TimeInt) (r *domino_types.Job, de *domino_types.DominoException, err error)
}

//任务运行信息
//
//Plan的一次运行为一个Job，每次运行可以用planName,stamp唯一确定一个Job
//
//很多函数抛出DominoException
//当code是ERROR_SYSTEM_ERROR时，客户端应重试
//否则是不可恢复的异常，客户端程序应该退出。如job被删除，状态转化不和逻辑等
type DominoJobClient struct {
	*dm303.DomobServiceClient
}

func NewDominoJobClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DominoJobClient {
	return &DominoJobClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDominoJobClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DominoJobClient {
	return &DominoJobClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 获取任务已经执行过的Job的时间范围
// 在展示任务的Job列表时，按照时间进行分页
// 通过此结果可以计算出页码数量
//
// 返回null表示没有结果
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoJobClient) GetJobTimeSpan(header *common.RequestHeader, planId IdInt) (r *domino_types.TimeSpan, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobTimeSpan(header, planId); err != nil {
		return
	}
	return p.recvGetJobTimeSpan()
}

func (p *DominoJobClient) sendGetJobTimeSpan(header *common.RequestHeader, planId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobTimeSpan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetJobTimeSpanArgs()
	args0.Header = header
	args0.PlanId = planId
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetJobTimeSpan() (value *domino_types.TimeSpan, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetJobTimeSpanResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.De != nil {
		de = result1.De
	}
	return
}

// 获取某个时间范围内（闭区间）的任务的执行列表
// 按照任务的调度时间过滤
//
// Parameters:
//  - Header
//  - PlanId
//  - StartSchedTime
//  - EndSchedTime
func (p *DominoJobClient) GetJobListByPlanId(header *common.RequestHeader, planId IdInt, startSchedTime TimeInt, endSchedTime TimeInt) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobListByPlanId(header, planId, startSchedTime, endSchedTime); err != nil {
		return
	}
	return p.recvGetJobListByPlanId()
}

func (p *DominoJobClient) sendGetJobListByPlanId(header *common.RequestHeader, planId IdInt, startSchedTime TimeInt, endSchedTime TimeInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobListByPlanId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetJobListByPlanIdArgs()
	args4.Header = header
	args4.PlanId = planId
	args4.StartSchedTime = startSchedTime
	args4.EndSchedTime = endSchedTime
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetJobListByPlanId() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetJobListByPlanIdResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.De != nil {
		de = result5.De
	}
	return
}

// 依据jobId获取job的详情
// 详情包括stdout和stderr
//
// Parameters:
//  - Header
//  - JobId
func (p *DominoJobClient) GetJobById(header *common.RequestHeader, jobId IdInt) (r *domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobById(header, jobId); err != nil {
		return
	}
	return p.recvGetJobById()
}

func (p *DominoJobClient) sendGetJobById(header *common.RequestHeader, jobId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetJobByIdArgs()
	args8.Header = header
	args8.JobId = jobId
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetJobById() (value *domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetJobByIdResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.De != nil {
		de = result9.De
	}
	return
}

// 获取某个状态的job列表
//
// 通常一个Plan的大部分Job都应该是成功状态，少数处于等待，运行中或者失败状态
// 这个接口大部分用于获取非正常执行的
//
// 按照时间倒序，最晚的在数据最开始
// 结果不包含任务的output
//
// Parameters:
//  - Header
//  - PlanId
//  - Status
func (p *DominoJobClient) GetJobListByStatus(header *common.RequestHeader, planId IdInt, status JobStatus) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobListByStatus(header, planId, status); err != nil {
		return
	}
	return p.recvGetJobListByStatus()
}

func (p *DominoJobClient) sendGetJobListByStatus(header *common.RequestHeader, planId IdInt, status JobStatus) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobListByStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetJobListByStatusArgs()
	args12.Header = header
	args12.PlanId = planId
	args12.Status = status
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetJobListByStatus() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetJobListByStatusResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.De != nil {
		de = result13.De
	}
	return
}

// 根据用户名和机器名，获取需要运行的plan列表
//
// Parameters:
//  - Header
//  - User
//  - Host
func (p *DominoJobClient) GetPlansByUserHost(header *common.RequestHeader, user string, host string) (r []*domino_types.Plan, de *domino_types.DominoException, err error) {
	if err = p.sendGetPlansByUserHost(header, user, host); err != nil {
		return
	}
	return p.recvGetPlansByUserHost()
}

func (p *DominoJobClient) sendGetPlansByUserHost(header *common.RequestHeader, user string, host string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlansByUserHost", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetPlansByUserHostArgs()
	args16.Header = header
	args16.User = user
	args16.Host = host
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetPlansByUserHost() (value []*domino_types.Plan, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetPlansByUserHostResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.De != nil {
		de = result17.De
	}
	return
}

// 获取本job依赖的jobs
//
// Parameters:
//  - Header
//  - PlanId
//  - Stamp
//  - Level
func (p *DominoJobClient) GetDepJobs(header *common.RequestHeader, planId IdInt, stamp TimeInt, level int32) (r []*domino_types.JobDepDetail, de *domino_types.DominoException, err error) {
	if err = p.sendGetDepJobs(header, planId, stamp, level); err != nil {
		return
	}
	return p.recvGetDepJobs()
}

func (p *DominoJobClient) sendGetDepJobs(header *common.RequestHeader, planId IdInt, stamp TimeInt, level int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getDepJobs", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetDepJobsArgs()
	args20.Header = header
	args20.PlanId = planId
	args20.Stamp = stamp
	args20.Level = level
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetDepJobs() (value []*domino_types.JobDepDetail, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetDepJobsResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.De != nil {
		de = result21.De
	}
	return
}

// 获取依赖于本job的jobs
//
// Parameters:
//  - Header
//  - PlanId
//  - Stamp
//  - Level
func (p *DominoJobClient) GetJobsDep(header *common.RequestHeader, planId IdInt, stamp TimeInt, level int32) (r []*domino_types.JobDepDetail, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobsDep(header, planId, stamp, level); err != nil {
		return
	}
	return p.recvGetJobsDep()
}

func (p *DominoJobClient) sendGetJobsDep(header *common.RequestHeader, planId IdInt, stamp TimeInt, level int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobsDep", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetJobsDepArgs()
	args24.Header = header
	args24.PlanId = planId
	args24.Stamp = stamp
	args24.Level = level
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetJobsDep() (value []*domino_types.JobDepDetail, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetJobsDepResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.De != nil {
		de = result25.De
	}
	return
}

// 接口提供给plancli，执行失败的信息写入plan_fail_job数据表
//
// Parameters:
//  - Header
//  - JobId
//  - Output
//  - PlancliThreadId
func (p *DominoJobClient) SaveFailJob(header *common.RequestHeader, jobId IdInt, output *domino_types.JobOutput, plancliThreadId IdInt) (r IdInt, de *domino_types.DominoException, err error) {
	if err = p.sendSaveFailJob(header, jobId, output, plancliThreadId); err != nil {
		return
	}
	return p.recvSaveFailJob()
}

func (p *DominoJobClient) sendSaveFailJob(header *common.RequestHeader, jobId IdInt, output *domino_types.JobOutput, plancliThreadId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("saveFailJob", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewSaveFailJobArgs()
	args28.Header = header
	args28.JobId = jobId
	args28.Output = output
	args28.PlancliThreadId = plancliThreadId
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvSaveFailJob() (value IdInt, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewSaveFailJobResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.De != nil {
		de = result29.De
	}
	return
}

// 获取plan的失败任务
// 数据来自于plan_fail_job中数据
//
// Parameters:
//  - Header
//  - PlanId
func (p *DominoJobClient) GetFailJobByPlanId(header *common.RequestHeader, planId IdInt) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetFailJobByPlanId(header, planId); err != nil {
		return
	}
	return p.recvGetFailJobByPlanId()
}

func (p *DominoJobClient) sendGetFailJobByPlanId(header *common.RequestHeader, planId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFailJobByPlanId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetFailJobByPlanIdArgs()
	args32.Header = header
	args32.PlanId = planId
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetFailJobByPlanId() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetFailJobByPlanIdResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.De != nil {
		de = result33.De
	}
	return
}

// 获取job的失败任务
// 数据表plan_fail_job中数据
//
// Parameters:
//  - Header
//  - PlanId
//  - Stamp
func (p *DominoJobClient) GetFailJobByJobId(header *common.RequestHeader, planId IdInt, stamp TimeInt) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetFailJobByJobId(header, planId, stamp); err != nil {
		return
	}
	return p.recvGetFailJobByJobId()
}

func (p *DominoJobClient) sendGetFailJobByJobId(header *common.RequestHeader, planId IdInt, stamp TimeInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFailJobByJobId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetFailJobByJobIdArgs()
	args36.Header = header
	args36.PlanId = planId
	args36.Stamp = stamp
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetFailJobByJobId() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetFailJobByJobIdResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.De != nil {
		de = result37.De
	}
	return
}

// 获取job的某一个指定失败任务
// 数据表plan_fail_job中数据
//
// Parameters:
//  - Header
//  - FailJobId
func (p *DominoJobClient) GetFailJobByFailJobId(header *common.RequestHeader, failJobId IdInt) (r *domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetFailJobByFailJobId(header, failJobId); err != nil {
		return
	}
	return p.recvGetFailJobByFailJobId()
}

func (p *DominoJobClient) sendGetFailJobByFailJobId(header *common.RequestHeader, failJobId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFailJobByFailJobId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewGetFailJobByFailJobIdArgs()
	args40.Header = header
	args40.FailJobId = failJobId
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetFailJobByFailJobId() (value *domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewGetFailJobByFailJobIdResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.De != nil {
		de = result41.De
	}
	return
}

// 获取依赖于job的所有job
// 返回依赖于此job的所有job
//
// Parameters:
//  - Header
//  - JobId
//  - Deepth
func (p *DominoJobClient) GetSuccedJobsByJobId(header *common.RequestHeader, JobId IdInt, Deepth IdInt) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetSuccedJobsByJobId(header, JobId, Deepth); err != nil {
		return
	}
	return p.recvGetSuccedJobsByJobId()
}

func (p *DominoJobClient) sendGetSuccedJobsByJobId(header *common.RequestHeader, JobId IdInt, Deepth IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSuccedJobsByJobId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetSuccedJobsByJobIdArgs()
	args44.Header = header
	args44.JobId = JobId
	args44.Deepth = Deepth
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetSuccedJobsByJobId() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetSuccedJobsByJobIdResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.De != nil {
		de = result45.De
	}
	return
}

// 获取log文件名
//
// Parameters:
//  - Header
func (p *DominoJobClient) GetLogFilesName(header *common.RequestHeader) (r []string, de *domino_types.DominoException, err error) {
	if err = p.sendGetLogFilesName(header); err != nil {
		return
	}
	return p.recvGetLogFilesName()
}

func (p *DominoJobClient) sendGetLogFilesName(header *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getLogFilesName", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewGetLogFilesNameArgs()
	args48.Header = header
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetLogFilesName() (value []string, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewGetLogFilesNameResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.De != nil {
		de = result49.De
	}
	return
}

// 依据文件名获取文件内容
// 返回string的list
//
// Parameters:
//  - Header
//  - FileName
func (p *DominoJobClient) GetFileContent(header *common.RequestHeader, fileName string) (r []string, de *domino_types.DominoException, err error) {
	if err = p.sendGetFileContent(header, fileName); err != nil {
		return
	}
	return p.recvGetFileContent()
}

func (p *DominoJobClient) sendGetFileContent(header *common.RequestHeader, fileName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFileContent", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewGetFileContentArgs()
	args52.Header = header
	args52.FileName = fileName
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetFileContent() (value []string, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewGetFileContentResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.De != nil {
		de = result53.De
	}
	return
}

// 根据user和host创建job
// 该方法调用四个创建job函数
// 分别创建正常运行，重跑的，丢失的，僵尸的
// 合并四次创建
// 返回job的list
//
// Parameters:
//  - Header
//  - Username
//  - Hostname
func (p *DominoJobClient) GetCreatedJobsByUserHost(header *common.RequestHeader, username string, hostname string) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetCreatedJobsByUserHost(header, username, hostname); err != nil {
		return
	}
	return p.recvGetCreatedJobsByUserHost()
}

func (p *DominoJobClient) sendGetCreatedJobsByUserHost(header *common.RequestHeader, username string, hostname string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCreatedJobsByUserHost", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewGetCreatedJobsByUserHostArgs()
	args56.Header = header
	args56.Username = username
	args56.Hostname = hostname
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetCreatedJobsByUserHost() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewGetCreatedJobsByUserHostResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.De != nil {
		de = result57.De
	}
	return
}

// 更新job的plancli进程号
// 先检查job状态是否合法，非法则抛出异常
//
// Parameters:
//  - Header
//  - JobId
//  - PlancliThreadId
func (p *DominoJobClient) InsertEasyPlancliThreadId(header *common.RequestHeader, jobId IdInt, plancliThreadId IdInt) (r IdInt, de *domino_types.DominoException, err error) {
	if err = p.sendInsertEasyPlancliThreadId(header, jobId, plancliThreadId); err != nil {
		return
	}
	return p.recvInsertEasyPlancliThreadId()
}

func (p *DominoJobClient) sendInsertEasyPlancliThreadId(header *common.RequestHeader, jobId IdInt, plancliThreadId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("insertEasyPlancliThreadId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewInsertEasyPlancliThreadIdArgs()
	args60.Header = header
	args60.JobId = jobId
	args60.PlancliThreadId = plancliThreadId
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvInsertEasyPlancliThreadId() (value IdInt, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewInsertEasyPlancliThreadIdResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	if result61.De != nil {
		de = result61.De
	}
	return
}

// 检查任务依赖
// 使用job的id作为参数，需要首先获取该job的plan
// 如果任务能够开始运行，则返回任务详情
// 否则返回空plan
//
// Parameters:
//  - Header
//  - JobId
//  - PlancliThreadId
func (p *DominoJobClient) CheckDepByJobId(header *common.RequestHeader, jobId IdInt, plancliThreadId IdInt) (r *domino_types.Plan, de *domino_types.DominoException, err error) {
	if err = p.sendCheckDepByJobId(header, jobId, plancliThreadId); err != nil {
		return
	}
	return p.recvCheckDepByJobId()
}

func (p *DominoJobClient) sendCheckDepByJobId(header *common.RequestHeader, jobId IdInt, plancliThreadId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkDepByJobId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewCheckDepByJobIdArgs()
	args64.Header = header
	args64.JobId = jobId
	args64.PlancliThreadId = plancliThreadId
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvCheckDepByJobId() (value *domino_types.Plan, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewCheckDepByJobIdResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.De != nil {
		de = result65.De
	}
	return
}

// 更新任务状态
//
// 更新成功会返回Job的id
// 有问题返回-1，客户端退出
//
//
// Parameters:
//  - Header
//  - JobId
//  - Status
//  - Output
//  - PlanCliId
func (p *DominoJobClient) UpdateJobStatus(header *common.RequestHeader, jobId IdInt, status JobStatus, output *domino_types.JobOutput, planCliId IdInt) (r IdInt, de *domino_types.DominoException, err error) {
	if err = p.sendUpdateJobStatus(header, jobId, status, output, planCliId); err != nil {
		return
	}
	return p.recvUpdateJobStatus()
}

func (p *DominoJobClient) sendUpdateJobStatus(header *common.RequestHeader, jobId IdInt, status JobStatus, output *domino_types.JobOutput, planCliId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateJobStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewUpdateJobStatusArgs()
	args68.Header = header
	args68.JobId = jobId
	args68.Status = status
	args68.Output = output
	args68.PlanCliId = planCliId
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvUpdateJobStatus() (value IdInt, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewUpdateJobStatusResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result69.Success
	if result69.De != nil {
		de = result69.De
	}
	return
}

// 更新最后运行时间
//
// Parameters:
//  - Header
//  - JobId
//  - PlanCliId
func (p *DominoJobClient) UpdateLastRunTime(header *common.RequestHeader, jobId IdInt, planCliId IdInt) (r IdInt, de *domino_types.DominoException, err error) {
	if err = p.sendUpdateLastRunTime(header, jobId, planCliId); err != nil {
		return
	}
	return p.recvUpdateLastRunTime()
}

func (p *DominoJobClient) sendUpdateLastRunTime(header *common.RequestHeader, jobId IdInt, planCliId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateLastRunTime", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args72 := NewUpdateLastRunTimeArgs()
	args72.Header = header
	args72.JobId = jobId
	args72.PlanCliId = planCliId
	if err = args72.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvUpdateLastRunTime() (value IdInt, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error74 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error75 error
		error75, err = error74.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error75
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result73 := NewUpdateLastRunTimeResult()
	if err = result73.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result73.Success
	if result73.De != nil {
		de = result73.De
	}
	return
}

// 为支持plancli的手工执行
// 依据planname和stamp获取jobId
// 如果没有job，创建job
//
// Parameters:
//  - Header
//  - PlanName
//  - Stamp
func (p *DominoJobClient) GetJobByPlanNameStamp(header *common.RequestHeader, planName string, stamp TimeInt) (r *domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobByPlanNameStamp(header, planName, stamp); err != nil {
		return
	}
	return p.recvGetJobByPlanNameStamp()
}

func (p *DominoJobClient) sendGetJobByPlanNameStamp(header *common.RequestHeader, planName string, stamp TimeInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobByPlanNameStamp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args76 := NewGetJobByPlanNameStampArgs()
	args76.Header = header
	args76.PlanName = planName
	args76.Stamp = stamp
	if err = args76.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoJobClient) recvGetJobByPlanNameStamp() (value *domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error78 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error79 error
		error79, err = error78.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error79
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result77 := NewGetJobByPlanNameStampResult()
	if err = result77.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result77.Success
	if result77.De != nil {
		de = result77.De
	}
	return
}

type DominoJobProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDominoJobProcessor(handler DominoJob) *DominoJobProcessor {
	self80 := &DominoJobProcessor{dm303.NewDomobServiceProcessor(handler)}
	self80.AddToProcessorMap("getJobTimeSpan", &dominoJobProcessorGetJobTimeSpan{handler: handler})
	self80.AddToProcessorMap("getJobListByPlanId", &dominoJobProcessorGetJobListByPlanId{handler: handler})
	self80.AddToProcessorMap("getJobById", &dominoJobProcessorGetJobById{handler: handler})
	self80.AddToProcessorMap("getJobListByStatus", &dominoJobProcessorGetJobListByStatus{handler: handler})
	self80.AddToProcessorMap("getPlansByUserHost", &dominoJobProcessorGetPlansByUserHost{handler: handler})
	self80.AddToProcessorMap("getDepJobs", &dominoJobProcessorGetDepJobs{handler: handler})
	self80.AddToProcessorMap("getJobsDep", &dominoJobProcessorGetJobsDep{handler: handler})
	self80.AddToProcessorMap("saveFailJob", &dominoJobProcessorSaveFailJob{handler: handler})
	self80.AddToProcessorMap("getFailJobByPlanId", &dominoJobProcessorGetFailJobByPlanId{handler: handler})
	self80.AddToProcessorMap("getFailJobByJobId", &dominoJobProcessorGetFailJobByJobId{handler: handler})
	self80.AddToProcessorMap("getFailJobByFailJobId", &dominoJobProcessorGetFailJobByFailJobId{handler: handler})
	self80.AddToProcessorMap("getSuccedJobsByJobId", &dominoJobProcessorGetSuccedJobsByJobId{handler: handler})
	self80.AddToProcessorMap("getLogFilesName", &dominoJobProcessorGetLogFilesName{handler: handler})
	self80.AddToProcessorMap("getFileContent", &dominoJobProcessorGetFileContent{handler: handler})
	self80.AddToProcessorMap("getCreatedJobsByUserHost", &dominoJobProcessorGetCreatedJobsByUserHost{handler: handler})
	self80.AddToProcessorMap("insertEasyPlancliThreadId", &dominoJobProcessorInsertEasyPlancliThreadId{handler: handler})
	self80.AddToProcessorMap("checkDepByJobId", &dominoJobProcessorCheckDepByJobId{handler: handler})
	self80.AddToProcessorMap("updateJobStatus", &dominoJobProcessorUpdateJobStatus{handler: handler})
	self80.AddToProcessorMap("updateLastRunTime", &dominoJobProcessorUpdateLastRunTime{handler: handler})
	self80.AddToProcessorMap("getJobByPlanNameStamp", &dominoJobProcessorGetJobByPlanNameStamp{handler: handler})
	return self80
}

type dominoJobProcessorGetJobTimeSpan struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetJobTimeSpan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobTimeSpanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobTimeSpan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobTimeSpanResult()
	if result.Success, result.De, err = p.handler.GetJobTimeSpan(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobTimeSpan: "+err.Error())
		oprot.WriteMessageBegin("getJobTimeSpan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobTimeSpan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetJobListByPlanId struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetJobListByPlanId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobListByPlanIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobListByPlanId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobListByPlanIdResult()
	if result.Success, result.De, err = p.handler.GetJobListByPlanId(args.Header, args.PlanId, args.StartSchedTime, args.EndSchedTime); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobListByPlanId: "+err.Error())
		oprot.WriteMessageBegin("getJobListByPlanId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobListByPlanId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetJobById struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetJobById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobByIdResult()
	if result.Success, result.De, err = p.handler.GetJobById(args.Header, args.JobId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobById: "+err.Error())
		oprot.WriteMessageBegin("getJobById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetJobListByStatus struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetJobListByStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobListByStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobListByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobListByStatusResult()
	if result.Success, result.De, err = p.handler.GetJobListByStatus(args.Header, args.PlanId, args.Status); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobListByStatus: "+err.Error())
		oprot.WriteMessageBegin("getJobListByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobListByStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetPlansByUserHost struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetPlansByUserHost) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlansByUserHostArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlansByUserHost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlansByUserHostResult()
	if result.Success, result.De, err = p.handler.GetPlansByUserHost(args.Header, args.User, args.Host); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlansByUserHost: "+err.Error())
		oprot.WriteMessageBegin("getPlansByUserHost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlansByUserHost", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetDepJobs struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetDepJobs) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDepJobsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getDepJobs", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDepJobsResult()
	if result.Success, result.De, err = p.handler.GetDepJobs(args.Header, args.PlanId, args.Stamp, args.Level); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDepJobs: "+err.Error())
		oprot.WriteMessageBegin("getDepJobs", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getDepJobs", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetJobsDep struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetJobsDep) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobsDepArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobsDep", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobsDepResult()
	if result.Success, result.De, err = p.handler.GetJobsDep(args.Header, args.PlanId, args.Stamp, args.Level); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobsDep: "+err.Error())
		oprot.WriteMessageBegin("getJobsDep", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobsDep", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorSaveFailJob struct {
	handler DominoJob
}

func (p *dominoJobProcessorSaveFailJob) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSaveFailJobArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("saveFailJob", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSaveFailJobResult()
	if result.Success, result.De, err = p.handler.SaveFailJob(args.Header, args.JobId, args.Output, args.PlancliThreadId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing saveFailJob: "+err.Error())
		oprot.WriteMessageBegin("saveFailJob", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("saveFailJob", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetFailJobByPlanId struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetFailJobByPlanId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFailJobByPlanIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFailJobByPlanId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFailJobByPlanIdResult()
	if result.Success, result.De, err = p.handler.GetFailJobByPlanId(args.Header, args.PlanId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFailJobByPlanId: "+err.Error())
		oprot.WriteMessageBegin("getFailJobByPlanId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFailJobByPlanId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetFailJobByJobId struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetFailJobByJobId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFailJobByJobIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFailJobByJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFailJobByJobIdResult()
	if result.Success, result.De, err = p.handler.GetFailJobByJobId(args.Header, args.PlanId, args.Stamp); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFailJobByJobId: "+err.Error())
		oprot.WriteMessageBegin("getFailJobByJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFailJobByJobId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetFailJobByFailJobId struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetFailJobByFailJobId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFailJobByFailJobIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFailJobByFailJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFailJobByFailJobIdResult()
	if result.Success, result.De, err = p.handler.GetFailJobByFailJobId(args.Header, args.FailJobId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFailJobByFailJobId: "+err.Error())
		oprot.WriteMessageBegin("getFailJobByFailJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFailJobByFailJobId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetSuccedJobsByJobId struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetSuccedJobsByJobId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSuccedJobsByJobIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSuccedJobsByJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSuccedJobsByJobIdResult()
	if result.Success, result.De, err = p.handler.GetSuccedJobsByJobId(args.Header, args.JobId, args.Deepth); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSuccedJobsByJobId: "+err.Error())
		oprot.WriteMessageBegin("getSuccedJobsByJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSuccedJobsByJobId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetLogFilesName struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetLogFilesName) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetLogFilesNameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getLogFilesName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetLogFilesNameResult()
	if result.Success, result.De, err = p.handler.GetLogFilesName(args.Header); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getLogFilesName: "+err.Error())
		oprot.WriteMessageBegin("getLogFilesName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getLogFilesName", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetFileContent struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetFileContent) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFileContentArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFileContent", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFileContentResult()
	if result.Success, result.De, err = p.handler.GetFileContent(args.Header, args.FileName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFileContent: "+err.Error())
		oprot.WriteMessageBegin("getFileContent", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFileContent", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetCreatedJobsByUserHost struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetCreatedJobsByUserHost) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCreatedJobsByUserHostArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCreatedJobsByUserHost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCreatedJobsByUserHostResult()
	if result.Success, result.De, err = p.handler.GetCreatedJobsByUserHost(args.Header, args.Username, args.Hostname); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCreatedJobsByUserHost: "+err.Error())
		oprot.WriteMessageBegin("getCreatedJobsByUserHost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCreatedJobsByUserHost", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorInsertEasyPlancliThreadId struct {
	handler DominoJob
}

func (p *dominoJobProcessorInsertEasyPlancliThreadId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewInsertEasyPlancliThreadIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("insertEasyPlancliThreadId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewInsertEasyPlancliThreadIdResult()
	if result.Success, result.De, err = p.handler.InsertEasyPlancliThreadId(args.Header, args.JobId, args.PlancliThreadId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing insertEasyPlancliThreadId: "+err.Error())
		oprot.WriteMessageBegin("insertEasyPlancliThreadId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("insertEasyPlancliThreadId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorCheckDepByJobId struct {
	handler DominoJob
}

func (p *dominoJobProcessorCheckDepByJobId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckDepByJobIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkDepByJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckDepByJobIdResult()
	if result.Success, result.De, err = p.handler.CheckDepByJobId(args.Header, args.JobId, args.PlancliThreadId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkDepByJobId: "+err.Error())
		oprot.WriteMessageBegin("checkDepByJobId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkDepByJobId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorUpdateJobStatus struct {
	handler DominoJob
}

func (p *dominoJobProcessorUpdateJobStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateJobStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateJobStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateJobStatusResult()
	if result.Success, result.De, err = p.handler.UpdateJobStatus(args.Header, args.JobId, args.Status, args.Output, args.PlanCliId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateJobStatus: "+err.Error())
		oprot.WriteMessageBegin("updateJobStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateJobStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorUpdateLastRunTime struct {
	handler DominoJob
}

func (p *dominoJobProcessorUpdateLastRunTime) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateLastRunTimeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateLastRunTime", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateLastRunTimeResult()
	if result.Success, result.De, err = p.handler.UpdateLastRunTime(args.Header, args.JobId, args.PlanCliId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateLastRunTime: "+err.Error())
		oprot.WriteMessageBegin("updateLastRunTime", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateLastRunTime", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoJobProcessorGetJobByPlanNameStamp struct {
	handler DominoJob
}

func (p *dominoJobProcessorGetJobByPlanNameStamp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobByPlanNameStampArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobByPlanNameStamp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobByPlanNameStampResult()
	if result.Success, result.De, err = p.handler.GetJobByPlanNameStamp(args.Header, args.PlanName, args.Stamp); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobByPlanNameStamp: "+err.Error())
		oprot.WriteMessageBegin("getJobByPlanNameStamp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobByPlanNameStamp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetJobTimeSpanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
}

func NewGetJobTimeSpanArgs() *GetJobTimeSpanArgs {
	return &GetJobTimeSpanArgs{}
}

func (p *GetJobTimeSpanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobTimeSpanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobTimeSpanArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetJobTimeSpanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobTimeSpan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobTimeSpanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobTimeSpanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetJobTimeSpanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobTimeSpanArgs(%+v)", *p)
}

type GetJobTimeSpanResult struct {
	Success *domino_types.TimeSpan        `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobTimeSpanResult() *GetJobTimeSpanResult {
	return &GetJobTimeSpanResult{}
}

func (p *GetJobTimeSpanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobTimeSpanResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewTimeSpan()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetJobTimeSpanResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobTimeSpanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobTimeSpan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobTimeSpanResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobTimeSpanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobTimeSpanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobTimeSpanResult(%+v)", *p)
}

type GetJobListByPlanIdArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId         IdInt                 `thrift:"planId,2" json:"planId"`
	StartSchedTime TimeInt               `thrift:"startSchedTime,3" json:"startSchedTime"`
	EndSchedTime   TimeInt               `thrift:"endSchedTime,4" json:"endSchedTime"`
}

func NewGetJobListByPlanIdArgs() *GetJobListByPlanIdArgs {
	return &GetJobListByPlanIdArgs{}
}

func (p *GetJobListByPlanIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByPlanIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobListByPlanIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetJobListByPlanIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartSchedTime = TimeInt(v)
	}
	return nil
}

func (p *GetJobListByPlanIdArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EndSchedTime = TimeInt(v)
	}
	return nil
}

func (p *GetJobListByPlanIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobListByPlanId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByPlanIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByPlanIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetJobListByPlanIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startSchedTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:startSchedTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartSchedTime)); err != nil {
		return fmt.Errorf("%T.startSchedTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:startSchedTime: %s", p, err)
	}
	return err
}

func (p *GetJobListByPlanIdArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endSchedTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:endSchedTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndSchedTime)); err != nil {
		return fmt.Errorf("%T.endSchedTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:endSchedTime: %s", p, err)
	}
	return err
}

func (p *GetJobListByPlanIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobListByPlanIdArgs(%+v)", *p)
}

type GetJobListByPlanIdResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobListByPlanIdResult() *GetJobListByPlanIdResult {
	return &GetJobListByPlanIdResult{}
}

func (p *GetJobListByPlanIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByPlanIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem81 := domino_types.NewJob()
		if err := _elem81.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem81)
		}
		p.Success = append(p.Success, _elem81)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetJobListByPlanIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobListByPlanIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobListByPlanId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByPlanIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByPlanIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByPlanIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobListByPlanIdResult(%+v)", *p)
}

type GetJobByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	JobId  IdInt                 `thrift:"jobId,2" json:"jobId"`
}

func NewGetJobByIdArgs() *GetJobByIdArgs {
	return &GetJobByIdArgs{}
}

func (p *GetJobByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *GetJobByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:jobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.jobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:jobId: %s", p, err)
	}
	return err
}

func (p *GetJobByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobByIdArgs(%+v)", *p)
}

type GetJobByIdResult struct {
	Success *domino_types.Job             `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobByIdResult() *GetJobByIdResult {
	return &GetJobByIdResult{}
}

func (p *GetJobByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewJob()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetJobByIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobByIdResult(%+v)", *p)
}

type GetJobListByStatusArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
	Status JobStatus             `thrift:"status,3" json:"status"`
}

func NewGetJobListByStatusArgs() *GetJobListByStatusArgs {
	return &GetJobListByStatusArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetJobListByStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *GetJobListByStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobListByStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetJobListByStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = JobStatus(v)
	}
	return nil
}

func (p *GetJobListByStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobListByStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetJobListByStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *GetJobListByStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobListByStatusArgs(%+v)", *p)
}

type GetJobListByStatusResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobListByStatusResult() *GetJobListByStatusResult {
	return &GetJobListByStatusResult{}
}

func (p *GetJobListByStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem82 := domino_types.NewJob()
		if err := _elem82.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem82)
		}
		p.Success = append(p.Success, _elem82)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetJobListByStatusResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobListByStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobListByStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobListByStatusResult(%+v)", *p)
}

type GetPlansByUserHostArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	User   string                `thrift:"user,2" json:"user"`
	Host   string                `thrift:"host,3" json:"host"`
}

func NewGetPlansByUserHostArgs() *GetPlansByUserHostArgs {
	return &GetPlansByUserHostArgs{}
}

func (p *GetPlansByUserHostArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByUserHostArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPlansByUserHostArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.User = v
	}
	return nil
}

func (p *GetPlansByUserHostArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *GetPlansByUserHostArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlansByUserHost_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByUserHostArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByUserHostArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:user: %s", p, err)
	}
	if err := oprot.WriteString(string(p.User)); err != nil {
		return fmt.Errorf("%T.user (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:user: %s", p, err)
	}
	return err
}

func (p *GetPlansByUserHostArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:host: %s", p, err)
	}
	return err
}

func (p *GetPlansByUserHostArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlansByUserHostArgs(%+v)", *p)
}

type GetPlansByUserHostResult struct {
	Success []*domino_types.Plan          `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetPlansByUserHostResult() *GetPlansByUserHostResult {
	return &GetPlansByUserHostResult{}
}

func (p *GetPlansByUserHostResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByUserHostResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Plan, 0, size)
	for i := 0; i < size; i++ {
		_elem83 := domino_types.NewPlan()
		if err := _elem83.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem83)
		}
		p.Success = append(p.Success, _elem83)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPlansByUserHostResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetPlansByUserHostResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlansByUserHost_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByUserHostResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByUserHostResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByUserHostResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlansByUserHostResult(%+v)", *p)
}

type GetDepJobsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
	Stamp  TimeInt               `thrift:"stamp,3" json:"stamp"`
	// unused field # 4
	Level int32 `thrift:"level,5" json:"level"`
}

func NewGetDepJobsArgs() *GetDepJobsArgs {
	return &GetDepJobsArgs{}
}

func (p *GetDepJobsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDepJobsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetDepJobsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetDepJobsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Stamp = TimeInt(v)
	}
	return nil
}

func (p *GetDepJobsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Level = v
	}
	return nil
}

func (p *GetDepJobsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDepJobs_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDepJobsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetDepJobsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetDepJobsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stamp", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Stamp)); err != nil {
		return fmt.Errorf("%T.stamp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stamp: %s", p, err)
	}
	return err
}

func (p *GetDepJobsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("level", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:level: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Level)); err != nil {
		return fmt.Errorf("%T.level (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:level: %s", p, err)
	}
	return err
}

func (p *GetDepJobsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDepJobsArgs(%+v)", *p)
}

type GetDepJobsResult struct {
	Success []*domino_types.JobDepDetail  `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetDepJobsResult() *GetDepJobsResult {
	return &GetDepJobsResult{}
}

func (p *GetDepJobsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDepJobsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.JobDepDetail, 0, size)
	for i := 0; i < size; i++ {
		_elem84 := domino_types.NewJobDepDetail()
		if err := _elem84.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem84)
		}
		p.Success = append(p.Success, _elem84)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetDepJobsResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetDepJobsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDepJobs_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDepJobsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDepJobsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetDepJobsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDepJobsResult(%+v)", *p)
}

type GetJobsDepArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
	Stamp  TimeInt               `thrift:"stamp,3" json:"stamp"`
	// unused field # 4
	Level int32 `thrift:"level,5" json:"level"`
}

func NewGetJobsDepArgs() *GetJobsDepArgs {
	return &GetJobsDepArgs{}
}

func (p *GetJobsDepArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobsDepArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobsDepArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetJobsDepArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Stamp = TimeInt(v)
	}
	return nil
}

func (p *GetJobsDepArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Level = v
	}
	return nil
}

func (p *GetJobsDepArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobsDep_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobsDepArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobsDepArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetJobsDepArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stamp", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Stamp)); err != nil {
		return fmt.Errorf("%T.stamp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stamp: %s", p, err)
	}
	return err
}

func (p *GetJobsDepArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("level", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:level: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Level)); err != nil {
		return fmt.Errorf("%T.level (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:level: %s", p, err)
	}
	return err
}

func (p *GetJobsDepArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobsDepArgs(%+v)", *p)
}

type GetJobsDepResult struct {
	Success []*domino_types.JobDepDetail  `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobsDepResult() *GetJobsDepResult {
	return &GetJobsDepResult{}
}

func (p *GetJobsDepResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobsDepResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.JobDepDetail, 0, size)
	for i := 0; i < size; i++ {
		_elem85 := domino_types.NewJobDepDetail()
		if err := _elem85.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem85)
		}
		p.Success = append(p.Success, _elem85)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetJobsDepResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobsDepResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobsDep_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobsDepResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobsDepResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobsDepResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobsDepResult(%+v)", *p)
}

type SaveFailJobArgs struct {
	Header          *common.RequestHeader   `thrift:"header,1" json:"header"`
	JobId           IdInt                   `thrift:"jobId,2" json:"jobId"`
	Output          *domino_types.JobOutput `thrift:"output,3" json:"output"`
	PlancliThreadId IdInt                   `thrift:"plancliThreadId,4" json:"plancliThreadId"`
}

func NewSaveFailJobArgs() *SaveFailJobArgs {
	return &SaveFailJobArgs{}
}

func (p *SaveFailJobArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaveFailJobArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SaveFailJobArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *SaveFailJobArgs) readField3(iprot thrift.TProtocol) error {
	p.Output = domino_types.NewJobOutput()
	if err := p.Output.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Output)
	}
	return nil
}

func (p *SaveFailJobArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PlancliThreadId = IdInt(v)
	}
	return nil
}

func (p *SaveFailJobArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("saveFailJob_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaveFailJobArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SaveFailJobArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:jobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.jobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:jobId: %s", p, err)
	}
	return err
}

func (p *SaveFailJobArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Output != nil {
		if err := oprot.WriteFieldBegin("output", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:output: %s", p, err)
		}
		if err := p.Output.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Output)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:output: %s", p, err)
		}
	}
	return err
}

func (p *SaveFailJobArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plancliThreadId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:plancliThreadId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlancliThreadId)); err != nil {
		return fmt.Errorf("%T.plancliThreadId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:plancliThreadId: %s", p, err)
	}
	return err
}

func (p *SaveFailJobArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveFailJobArgs(%+v)", *p)
}

type SaveFailJobResult struct {
	Success IdInt                         `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewSaveFailJobResult() *SaveFailJobResult {
	return &SaveFailJobResult{}
}

func (p *SaveFailJobResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaveFailJobResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = IdInt(v)
	}
	return nil
}

func (p *SaveFailJobResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *SaveFailJobResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("saveFailJob_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaveFailJobResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *SaveFailJobResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *SaveFailJobResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveFailJobResult(%+v)", *p)
}

type GetFailJobByPlanIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
}

func NewGetFailJobByPlanIdArgs() *GetFailJobByPlanIdArgs {
	return &GetFailJobByPlanIdArgs{}
}

func (p *GetFailJobByPlanIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByPlanIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFailJobByPlanIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetFailJobByPlanIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFailJobByPlanId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByPlanIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByPlanIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetFailJobByPlanIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFailJobByPlanIdArgs(%+v)", *p)
}

type GetFailJobByPlanIdResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetFailJobByPlanIdResult() *GetFailJobByPlanIdResult {
	return &GetFailJobByPlanIdResult{}
}

func (p *GetFailJobByPlanIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByPlanIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem86 := domino_types.NewJob()
		if err := _elem86.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem86)
		}
		p.Success = append(p.Success, _elem86)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFailJobByPlanIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetFailJobByPlanIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFailJobByPlanId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByPlanIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByPlanIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByPlanIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFailJobByPlanIdResult(%+v)", *p)
}

type GetFailJobByJobIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanId IdInt                 `thrift:"planId,2" json:"planId"`
	Stamp  TimeInt               `thrift:"stamp,3" json:"stamp"`
}

func NewGetFailJobByJobIdArgs() *GetFailJobByJobIdArgs {
	return &GetFailJobByJobIdArgs{}
}

func (p *GetFailJobByJobIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByJobIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFailJobByJobIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = IdInt(v)
	}
	return nil
}

func (p *GetFailJobByJobIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Stamp = TimeInt(v)
	}
	return nil
}

func (p *GetFailJobByJobIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFailJobByJobId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByJobIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByJobIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planId: %s", p, err)
	}
	return err
}

func (p *GetFailJobByJobIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stamp", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Stamp)); err != nil {
		return fmt.Errorf("%T.stamp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stamp: %s", p, err)
	}
	return err
}

func (p *GetFailJobByJobIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFailJobByJobIdArgs(%+v)", *p)
}

type GetFailJobByJobIdResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetFailJobByJobIdResult() *GetFailJobByJobIdResult {
	return &GetFailJobByJobIdResult{}
}

func (p *GetFailJobByJobIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByJobIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem87 := domino_types.NewJob()
		if err := _elem87.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem87)
		}
		p.Success = append(p.Success, _elem87)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFailJobByJobIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetFailJobByJobIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFailJobByJobId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByJobIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByJobIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByJobIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFailJobByJobIdResult(%+v)", *p)
}

type GetFailJobByFailJobIdArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	FailJobId IdInt                 `thrift:"failJobId,2" json:"failJobId"`
}

func NewGetFailJobByFailJobIdArgs() *GetFailJobByFailJobIdArgs {
	return &GetFailJobByFailJobIdArgs{}
}

func (p *GetFailJobByFailJobIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByFailJobIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFailJobByFailJobIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FailJobId = IdInt(v)
	}
	return nil
}

func (p *GetFailJobByFailJobIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFailJobByFailJobId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByFailJobIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByFailJobIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("failJobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:failJobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FailJobId)); err != nil {
		return fmt.Errorf("%T.failJobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:failJobId: %s", p, err)
	}
	return err
}

func (p *GetFailJobByFailJobIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFailJobByFailJobIdArgs(%+v)", *p)
}

type GetFailJobByFailJobIdResult struct {
	Success *domino_types.Job             `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetFailJobByFailJobIdResult() *GetFailJobByFailJobIdResult {
	return &GetFailJobByFailJobIdResult{}
}

func (p *GetFailJobByFailJobIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByFailJobIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewJob()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFailJobByFailJobIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetFailJobByFailJobIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFailJobByFailJobId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFailJobByFailJobIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByFailJobIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetFailJobByFailJobIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFailJobByFailJobIdResult(%+v)", *p)
}

type GetSuccedJobsByJobIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	JobId  IdInt                 `thrift:"JobId,2" json:"JobId"`
	Deepth IdInt                 `thrift:"Deepth,3" json:"Deepth"`
}

func NewGetSuccedJobsByJobIdArgs() *GetSuccedJobsByJobIdArgs {
	return &GetSuccedJobsByJobIdArgs{}
}

func (p *GetSuccedJobsByJobIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Deepth = IdInt(v)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSuccedJobsByJobId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSuccedJobsByJobIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("JobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:JobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.JobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:JobId: %s", p, err)
	}
	return err
}

func (p *GetSuccedJobsByJobIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("Deepth", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:Deepth: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deepth)); err != nil {
		return fmt.Errorf("%T.Deepth (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:Deepth: %s", p, err)
	}
	return err
}

func (p *GetSuccedJobsByJobIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSuccedJobsByJobIdArgs(%+v)", *p)
}

type GetSuccedJobsByJobIdResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetSuccedJobsByJobIdResult() *GetSuccedJobsByJobIdResult {
	return &GetSuccedJobsByJobIdResult{}
}

func (p *GetSuccedJobsByJobIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem88 := domino_types.NewJob()
		if err := _elem88.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem88)
		}
		p.Success = append(p.Success, _elem88)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSuccedJobsByJobId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSuccedJobsByJobIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSuccedJobsByJobIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetSuccedJobsByJobIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSuccedJobsByJobIdResult(%+v)", *p)
}

type GetLogFilesNameArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
}

func NewGetLogFilesNameArgs() *GetLogFilesNameArgs {
	return &GetLogFilesNameArgs{}
}

func (p *GetLogFilesNameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLogFilesNameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetLogFilesNameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLogFilesName_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLogFilesNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetLogFilesNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLogFilesNameArgs(%+v)", *p)
}

type GetLogFilesNameResult struct {
	Success []string                      `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetLogFilesNameResult() *GetLogFilesNameResult {
	return &GetLogFilesNameResult{}
}

func (p *GetLogFilesNameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLogFilesNameResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem89 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem89 = v
		}
		p.Success = append(p.Success, _elem89)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetLogFilesNameResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetLogFilesNameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLogFilesName_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLogFilesNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetLogFilesNameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetLogFilesNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLogFilesNameResult(%+v)", *p)
}

type GetFileContentArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	FileName string                `thrift:"fileName,2" json:"fileName"`
}

func NewGetFileContentArgs() *GetFileContentArgs {
	return &GetFileContentArgs{}
}

func (p *GetFileContentArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileContentArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFileContentArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FileName = v
	}
	return nil
}

func (p *GetFileContentArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileContent_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileContentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFileContentArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fileName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileName)); err != nil {
		return fmt.Errorf("%T.fileName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fileName: %s", p, err)
	}
	return err
}

func (p *GetFileContentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileContentArgs(%+v)", *p)
}

type GetFileContentResult struct {
	Success []string                      `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetFileContentResult() *GetFileContentResult {
	return &GetFileContentResult{}
}

func (p *GetFileContentResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileContentResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem90 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem90 = v
		}
		p.Success = append(p.Success, _elem90)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFileContentResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetFileContentResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileContent_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileContentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFileContentResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetFileContentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileContentResult(%+v)", *p)
}

type GetCreatedJobsByUserHostArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Username string                `thrift:"username,2" json:"username"`
	Hostname string                `thrift:"hostname,3" json:"hostname"`
}

func NewGetCreatedJobsByUserHostArgs() *GetCreatedJobsByUserHostArgs {
	return &GetCreatedJobsByUserHostArgs{}
}

func (p *GetCreatedJobsByUserHostArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *GetCreatedJobsByUserHostArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Hostname = v
	}
	return nil
}

func (p *GetCreatedJobsByUserHostArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCreatedJobsByUserHost_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCreatedJobsByUserHostArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:username: %s", p, err)
	}
	return err
}

func (p *GetCreatedJobsByUserHostArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostname", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:hostname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostname)); err != nil {
		return fmt.Errorf("%T.hostname (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:hostname: %s", p, err)
	}
	return err
}

func (p *GetCreatedJobsByUserHostArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCreatedJobsByUserHostArgs(%+v)", *p)
}

type GetCreatedJobsByUserHostResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetCreatedJobsByUserHostResult() *GetCreatedJobsByUserHostResult {
	return &GetCreatedJobsByUserHostResult{}
}

func (p *GetCreatedJobsByUserHostResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem91 := domino_types.NewJob()
		if err := _elem91.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem91)
		}
		p.Success = append(p.Success, _elem91)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCreatedJobsByUserHost_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCreatedJobsByUserHostResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCreatedJobsByUserHostResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetCreatedJobsByUserHostResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCreatedJobsByUserHostResult(%+v)", *p)
}

type InsertEasyPlancliThreadIdArgs struct {
	Header          *common.RequestHeader `thrift:"header,1" json:"header"`
	JobId           IdInt                 `thrift:"jobId,2" json:"jobId"`
	PlancliThreadId IdInt                 `thrift:"plancliThreadId,3" json:"plancliThreadId"`
}

func NewInsertEasyPlancliThreadIdArgs() *InsertEasyPlancliThreadIdArgs {
	return &InsertEasyPlancliThreadIdArgs{}
}

func (p *InsertEasyPlancliThreadIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlancliThreadId = IdInt(v)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("insertEasyPlancliThreadId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *InsertEasyPlancliThreadIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:jobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.jobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:jobId: %s", p, err)
	}
	return err
}

func (p *InsertEasyPlancliThreadIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plancliThreadId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:plancliThreadId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlancliThreadId)); err != nil {
		return fmt.Errorf("%T.plancliThreadId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:plancliThreadId: %s", p, err)
	}
	return err
}

func (p *InsertEasyPlancliThreadIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InsertEasyPlancliThreadIdArgs(%+v)", *p)
}

type InsertEasyPlancliThreadIdResult struct {
	Success IdInt                         `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewInsertEasyPlancliThreadIdResult() *InsertEasyPlancliThreadIdResult {
	return &InsertEasyPlancliThreadIdResult{}
}

func (p *InsertEasyPlancliThreadIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = IdInt(v)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("insertEasyPlancliThreadId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *InsertEasyPlancliThreadIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *InsertEasyPlancliThreadIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *InsertEasyPlancliThreadIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InsertEasyPlancliThreadIdResult(%+v)", *p)
}

type CheckDepByJobIdArgs struct {
	Header          *common.RequestHeader `thrift:"header,1" json:"header"`
	JobId           IdInt                 `thrift:"jobId,2" json:"jobId"`
	PlancliThreadId IdInt                 `thrift:"plancliThreadId,3" json:"plancliThreadId"`
}

func NewCheckDepByJobIdArgs() *CheckDepByJobIdArgs {
	return &CheckDepByJobIdArgs{}
}

func (p *CheckDepByJobIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckDepByJobIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CheckDepByJobIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *CheckDepByJobIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlancliThreadId = IdInt(v)
	}
	return nil
}

func (p *CheckDepByJobIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkDepByJobId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckDepByJobIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CheckDepByJobIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:jobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.jobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:jobId: %s", p, err)
	}
	return err
}

func (p *CheckDepByJobIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plancliThreadId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:plancliThreadId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlancliThreadId)); err != nil {
		return fmt.Errorf("%T.plancliThreadId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:plancliThreadId: %s", p, err)
	}
	return err
}

func (p *CheckDepByJobIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckDepByJobIdArgs(%+v)", *p)
}

type CheckDepByJobIdResult struct {
	Success *domino_types.Plan            `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewCheckDepByJobIdResult() *CheckDepByJobIdResult {
	return &CheckDepByJobIdResult{}
}

func (p *CheckDepByJobIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckDepByJobIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewPlan()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CheckDepByJobIdResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *CheckDepByJobIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkDepByJobId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckDepByJobIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckDepByJobIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *CheckDepByJobIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckDepByJobIdResult(%+v)", *p)
}

type UpdateJobStatusArgs struct {
	Header    *common.RequestHeader   `thrift:"header,1" json:"header"`
	JobId     IdInt                   `thrift:"jobId,2" json:"jobId"`
	Status    JobStatus               `thrift:"status,3" json:"status"`
	Output    *domino_types.JobOutput `thrift:"output,4" json:"output"`
	PlanCliId IdInt                   `thrift:"planCliId,5" json:"planCliId"`
}

func NewUpdateJobStatusArgs() *UpdateJobStatusArgs {
	return &UpdateJobStatusArgs{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateJobStatusArgs) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *UpdateJobStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateJobStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateJobStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *UpdateJobStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = JobStatus(v)
	}
	return nil
}

func (p *UpdateJobStatusArgs) readField4(iprot thrift.TProtocol) error {
	p.Output = domino_types.NewJobOutput()
	if err := p.Output.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Output)
	}
	return nil
}

func (p *UpdateJobStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PlanCliId = IdInt(v)
	}
	return nil
}

func (p *UpdateJobStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateJobStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateJobStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateJobStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:jobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.jobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:jobId: %s", p, err)
	}
	return err
}

func (p *UpdateJobStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:status: %s", p, err)
	}
	return err
}

func (p *UpdateJobStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Output != nil {
		if err := oprot.WriteFieldBegin("output", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:output: %s", p, err)
		}
		if err := p.Output.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Output)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:output: %s", p, err)
		}
	}
	return err
}

func (p *UpdateJobStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planCliId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:planCliId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanCliId)); err != nil {
		return fmt.Errorf("%T.planCliId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:planCliId: %s", p, err)
	}
	return err
}

func (p *UpdateJobStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateJobStatusArgs(%+v)", *p)
}

type UpdateJobStatusResult struct {
	Success IdInt                         `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewUpdateJobStatusResult() *UpdateJobStatusResult {
	return &UpdateJobStatusResult{}
}

func (p *UpdateJobStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateJobStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = IdInt(v)
	}
	return nil
}

func (p *UpdateJobStatusResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *UpdateJobStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateJobStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateJobStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *UpdateJobStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *UpdateJobStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateJobStatusResult(%+v)", *p)
}

type UpdateLastRunTimeArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	JobId     IdInt                 `thrift:"jobId,2" json:"jobId"`
	PlanCliId IdInt                 `thrift:"planCliId,3" json:"planCliId"`
}

func NewUpdateLastRunTimeArgs() *UpdateLastRunTimeArgs {
	return &UpdateLastRunTimeArgs{}
}

func (p *UpdateLastRunTimeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateLastRunTimeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateLastRunTimeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.JobId = IdInt(v)
	}
	return nil
}

func (p *UpdateLastRunTimeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlanCliId = IdInt(v)
	}
	return nil
}

func (p *UpdateLastRunTimeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateLastRunTime_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateLastRunTimeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateLastRunTimeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jobId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:jobId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JobId)); err != nil {
		return fmt.Errorf("%T.jobId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:jobId: %s", p, err)
	}
	return err
}

func (p *UpdateLastRunTimeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planCliId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:planCliId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanCliId)); err != nil {
		return fmt.Errorf("%T.planCliId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:planCliId: %s", p, err)
	}
	return err
}

func (p *UpdateLastRunTimeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateLastRunTimeArgs(%+v)", *p)
}

type UpdateLastRunTimeResult struct {
	Success IdInt                         `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewUpdateLastRunTimeResult() *UpdateLastRunTimeResult {
	return &UpdateLastRunTimeResult{}
}

func (p *UpdateLastRunTimeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateLastRunTimeResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = IdInt(v)
	}
	return nil
}

func (p *UpdateLastRunTimeResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *UpdateLastRunTimeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateLastRunTime_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateLastRunTimeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *UpdateLastRunTimeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *UpdateLastRunTimeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateLastRunTimeResult(%+v)", *p)
}

type GetJobByPlanNameStampArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	PlanName string                `thrift:"planName,2" json:"planName"`
	Stamp    TimeInt               `thrift:"stamp,3" json:"stamp"`
}

func NewGetJobByPlanNameStampArgs() *GetJobByPlanNameStampArgs {
	return &GetJobByPlanNameStampArgs{}
}

func (p *GetJobByPlanNameStampArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobByPlanNameStampArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobByPlanNameStampArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanName = v
	}
	return nil
}

func (p *GetJobByPlanNameStampArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Stamp = TimeInt(v)
	}
	return nil
}

func (p *GetJobByPlanNameStampArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobByPlanNameStamp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobByPlanNameStampArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobByPlanNameStampArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:planName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PlanName)); err != nil {
		return fmt.Errorf("%T.planName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:planName: %s", p, err)
	}
	return err
}

func (p *GetJobByPlanNameStampArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stamp", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Stamp)); err != nil {
		return fmt.Errorf("%T.stamp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stamp: %s", p, err)
	}
	return err
}

func (p *GetJobByPlanNameStampArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobByPlanNameStampArgs(%+v)", *p)
}

type GetJobByPlanNameStampResult struct {
	Success *domino_types.Job             `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobByPlanNameStampResult() *GetJobByPlanNameStampResult {
	return &GetJobByPlanNameStampResult{}
}

func (p *GetJobByPlanNameStampResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobByPlanNameStampResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewJob()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetJobByPlanNameStampResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobByPlanNameStampResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobByPlanNameStamp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobByPlanNameStampResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobByPlanNameStampResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobByPlanNameStampResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobByPlanNameStampResult(%+v)", *p)
}
