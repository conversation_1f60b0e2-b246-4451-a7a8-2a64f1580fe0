// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"adinfo"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  AdPlanIdInt addAdPlan(RequestHeader header, AdPlan adPlan)")
	fmt.Fprintln(os.Stderr, "  void editAdPlan(RequestHeader header, AdPlan adPlan)")
	fmt.Fprintln(os.<PERSON>, "  void pauseAdPlansByPids(RequestHeader header, UidInt uid,  pids)")
	fmt.Fprintln(os.Stderr, "  void sysPauseAdPlansByPids(RequestHeader header, i32 dspCampaignId,  pids)")
	fmt.Fprintln(os.Stderr, "  void sysResumeAdPlansByPids(RequestHeader header, i32 dspCampaignId,  pids)")
	fmt.Fprintln(os.Stderr, "  void stopAdPlansByPids(RequestHeader header, UidInt uid,  pids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdPlansByPids(RequestHeader header, UidInt uid,  pids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdPlansByPids(RequestHeader header, UidInt uid,  pids)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdPlansByUid(RequestHeader header, UidInt uid, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdPlansByUidAndCostType(RequestHeader header, UidInt uid, CostType costType, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdPlansByUidOrderByPid(RequestHeader header, UidInt uid, bool excludeDeleted, AdQueryInt fromPid, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdPlansByUidAndCostTypeOrderByPid(RequestHeader header, UidInt uid, CostType costType, bool excludeDeleted, AdQueryInt fromPid, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listRunnableAdPlansByUidOrderByLastUpdate(RequestHeader header, UidInt uid, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listRunnableAdPlansByUidAndCostTypeOrderByLastUpdate(RequestHeader header, UidInt uid, CostType costType, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   listAdPlanIdsByDspCampaignId(RequestHeader header, i32 dspCampaignId, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   listAdPlanIdsByDspCampaignIdAndTaskFlag(RequestHeader header, i32 dspCampaignId, i32 taskFlag, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   getAdPlanOrderById(RequestHeader header, bool excludeDeleted, AdQueryInt fromId, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getAdStrategyOrderById(RequestHeader header, bool excludeDeleted, AdQueryInt fromId, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getAdCreativeOrderById(RequestHeader header, bool excludeDeleted, AdQueryInt fromId, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getAdPlansByPids(RequestHeader header,  pids)")
	fmt.Fprintln(os.Stderr, "  AdStrategyIdInt addAdStrategy(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void editAdStrategy(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void pauseAdStrategyBySids(RequestHeader header, UidInt uid, AdPlanIdInt pid,  sids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdStrategyBySids(RequestHeader header, UidInt uid, AdPlanIdInt pid,  sids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdStrategyBySids(RequestHeader header, UidInt uid, AdPlanIdInt pid,  sids)")
	fmt.Fprintln(os.Stderr, "  void copyAdStrategiesToAdPlan(RequestHeader header, UidInt uid,  sids, AdPlanIdInt destPid)")
	fmt.Fprintln(os.Stderr, "   getAdStrategiesBySids(RequestHeader header,  sids)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdStrategiesByPid(RequestHeader header, AdPlanIdInt pid, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdStrategiesByPidOrderBySid(RequestHeader header, AdPlanIdInt pid, bool excludeDeleted, AdQueryInt fromSid, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdStrategiesByUidAndAdStrategyType(RequestHeader header, UidInt uid, AdStrategyType adStrategyType, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdCreativeIdInt addAdCreative(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void editAdCreative(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCreativeByCids(RequestHeader header, UidInt uid, AdPlanIdInt pid, AdStrategyIdInt sid,  cids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCreativeByCids(RequestHeader header, UidInt uid, AdPlanIdInt pid, AdStrategyIdInt sid,  cids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCreativeByCids(RequestHeader header, UidInt uid, AdPlanIdInt pid, AdStrategyIdInt sid,  cids)")
	fmt.Fprintln(os.Stderr, "  void submitAdCreativeByCids(RequestHeader header, UidInt uid, AdPlanIdInt pid, AdStrategyIdInt sid,  cids)")
	fmt.Fprintln(os.Stderr, "   getAdCreativesByCids(RequestHeader header,  cids)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdCreativesBySid(RequestHeader header, AdStrategyIdInt sid, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdCreativesBySidOrderByCid(RequestHeader header, AdStrategyIdInt sid, bool excludeDeleted, AdQueryInt fromCid, AdQueryInt limit)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdCreativesByTypeAndStatus(RequestHeader header, AdCreativeType type, AdCreativeStatus status, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  void adminUpdateAdUserWaterMarkByUids(RequestHeader header,  uids,  adUserWaterMarks)")
	fmt.Fprintln(os.Stderr, "  void adminUpdateAdPlanWaterMarkByPids(RequestHeader header, UidInt uid,  pids,  adPlanWaterMarks)")
	fmt.Fprintln(os.Stderr, "  void adminApproveAdCreativesByCids(RequestHeader header,  cids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminRejectAdCreativesByCids(RequestHeader header,  cids,  comments)")
	fmt.Fprintln(os.Stderr, "   getAdPlanStatusLamp(RequestHeader header,  pids)")
	fmt.Fprintln(os.Stderr, "   getAdStrategyStatusLamp(RequestHeader header,  sids)")
	fmt.Fprintln(os.Stderr, "   getAdCreativeStatusLamp(RequestHeader header,  cids)")
	fmt.Fprintln(os.Stderr, "   listAdInfoIdsByUid(RequestHeader header, UidInt uid, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   listAdInfoIdsByUids(RequestHeader header,  uids, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   listAdInfoIdsByUidAndCostType(RequestHeader header, UidInt uid, CostType costType, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "  void adminMarkAdCreativeActionType(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void updateAdUserExtInfo(RequestHeader header, AdUser adUser)")
	fmt.Fprintln(os.Stderr, "  void deleteAdUserExtInfo(RequestHeader header, AdUser adUser)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyExtInfo(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void deleteAdStrategyExtInfo(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativeExtInfo(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativeUrlOpenType(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativeRequiredCapability(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativePackageInfo(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativeMinRequiredSDKVersion(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativeMaterialTexts(RequestHeader header,  adCreatives)")
	fmt.Fprintln(os.Stderr, "  void updateAdCreativeMediaBid(RequestHeader header,  adCreatives)")
	fmt.Fprintln(os.Stderr, "  AdCreative updateAdCreativeBid(RequestHeader header, AdCreative adCreative)")
	fmt.Fprintln(os.Stderr, "  void extendAdPlanDailyBudget(RequestHeader header, UidInt uid, AdPlanIdInt planId, CurrencyAmount originalBudget, CurrencyAmount newBudget)")
	fmt.Fprintln(os.Stderr, "  void updateAdPlanTimeSlot(RequestHeader header, UidInt uid, AdPlanIdInt planId,  timeSlot)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyDemoTagTarget(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyMediaTagTarget(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyDemoTaggings(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyDeviceGroupTarget(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyPositionTarget(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "  void updateAdPlanDailyBudget(RequestHeader header, UidInt uid, AdPlanIdInt pid, CurrencyAmount originalBudget, CurrencyAmount newBudget, DateType newBudgetEffectiveDateType)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyDailyBudget(RequestHeader header, UidInt uid, AdPlanIdInt pid, AdStrategyIdInt sid, CurrencyAmount originalBudget, bool budgetLimit, CurrencyAmount newBudget, DateType newBudgetEffectiveDateType)")
	fmt.Fprintln(os.Stderr, "   getAdUsersByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void updateAdStrategyAdLevel(RequestHeader header, AdStrategy adStrategy)")
	fmt.Fprintln(os.Stderr, "   getBriefStatusByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void editAdPlanBudgetSchedule(RequestHeader header, AdPlan adPlan)")
	fmt.Fprintln(os.Stderr, "  void changeAdInfoExtInfo(RequestHeader header, AdInfoExtInfo adInfoExtInfo)")
	fmt.Fprintln(os.Stderr, "  void updateAdPlanExtInfo(RequestHeader header, AdPlan adPlan)")
	fmt.Fprintln(os.Stderr, "  void deleteAdPlanExtInfo(RequestHeader header, AdPlan adPlan)")
	fmt.Fprintln(os.Stderr, "  AdResourceGroupId addAdResourceGroup(RequestHeader header, ResourceGroup resourceGroup)")
	fmt.Fprintln(os.Stderr, "  AdResourceId addAdResource(RequestHeader header, Resource resource)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdPlansByUidAndCostTypeAndFuzzyName(RequestHeader header, UidInt uid, CostType costType, string name, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAdPlansByUidAndFuzzyName(RequestHeader header, UidInt uid, string name, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AdQueryResult listAppIdsByUids(RequestHeader header,  uids, bool excludeDeleted, AdQueryInt offset, AdQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   listAdInfoIdsByAppIds(RequestHeader header,  appIds, bool excludeDeleted, bool ascending)")
	fmt.Fprintln(os.Stderr, "   listAdInfoIdsByChnIds(RequestHeader header,  chnIds, bool excludeDeleted, bool ascending)")
	fmt.Fprintln(os.Stderr, "  void bindAppAndChannelByPids(RequestHeader header, UidInt uid,  pids, IdInt appId, IdInt chnId)")
	fmt.Fprintln(os.Stderr, "   getBriefAdCreativesByPid(RequestHeader header, IdInt pid)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := adinfo.NewAdInfoServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addAdPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdPlan requires 2 args")
			flag.Usage()
		}
		arg428 := flag.Arg(1)
		mbTrans429 := thrift.NewTMemoryBufferLen(len(arg428))
		defer mbTrans429.Close()
		_, err430 := mbTrans429.WriteString(arg428)
		if err430 != nil {
			Usage()
			return
		}
		factory431 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt432 := factory431.GetProtocol(mbTrans429)
		argvalue0 := adinfo.NewRequestHeader()
		err433 := argvalue0.Read(jsProt432)
		if err433 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg434 := flag.Arg(2)
		mbTrans435 := thrift.NewTMemoryBufferLen(len(arg434))
		defer mbTrans435.Close()
		_, err436 := mbTrans435.WriteString(arg434)
		if err436 != nil {
			Usage()
			return
		}
		factory437 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt438 := factory437.GetProtocol(mbTrans435)
		argvalue1 := adinfo.NewAdPlan()
		err439 := argvalue1.Read(jsProt438)
		if err439 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdPlan(argvalue1)
		fmt.Print(client.AddAdPlan(value0, value1))
		fmt.Print("\n")
		break
	case "editAdPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdPlan requires 2 args")
			flag.Usage()
		}
		arg440 := flag.Arg(1)
		mbTrans441 := thrift.NewTMemoryBufferLen(len(arg440))
		defer mbTrans441.Close()
		_, err442 := mbTrans441.WriteString(arg440)
		if err442 != nil {
			Usage()
			return
		}
		factory443 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt444 := factory443.GetProtocol(mbTrans441)
		argvalue0 := adinfo.NewRequestHeader()
		err445 := argvalue0.Read(jsProt444)
		if err445 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg446 := flag.Arg(2)
		mbTrans447 := thrift.NewTMemoryBufferLen(len(arg446))
		defer mbTrans447.Close()
		_, err448 := mbTrans447.WriteString(arg446)
		if err448 != nil {
			Usage()
			return
		}
		factory449 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt450 := factory449.GetProtocol(mbTrans447)
		argvalue1 := adinfo.NewAdPlan()
		err451 := argvalue1.Read(jsProt450)
		if err451 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdPlan(argvalue1)
		fmt.Print(client.EditAdPlan(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdPlansByPids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseAdPlansByPids requires 3 args")
			flag.Usage()
		}
		arg452 := flag.Arg(1)
		mbTrans453 := thrift.NewTMemoryBufferLen(len(arg452))
		defer mbTrans453.Close()
		_, err454 := mbTrans453.WriteString(arg452)
		if err454 != nil {
			Usage()
			return
		}
		factory455 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt456 := factory455.GetProtocol(mbTrans453)
		argvalue0 := adinfo.NewRequestHeader()
		err457 := argvalue0.Read(jsProt456)
		if err457 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err458 := (strconv.Atoi(flag.Arg(2)))
		if err458 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg459 := flag.Arg(3)
		mbTrans460 := thrift.NewTMemoryBufferLen(len(arg459))
		defer mbTrans460.Close()
		_, err461 := mbTrans460.WriteString(arg459)
		if err461 != nil {
			Usage()
			return
		}
		factory462 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt463 := factory462.GetProtocol(mbTrans460)
		containerStruct2 := adinfo.NewPauseAdPlansByPidsArgs()
		err464 := containerStruct2.ReadField3(jsProt463)
		if err464 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		fmt.Print(client.PauseAdPlansByPids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "sysPauseAdPlansByPids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SysPauseAdPlansByPids requires 3 args")
			flag.Usage()
		}
		arg465 := flag.Arg(1)
		mbTrans466 := thrift.NewTMemoryBufferLen(len(arg465))
		defer mbTrans466.Close()
		_, err467 := mbTrans466.WriteString(arg465)
		if err467 != nil {
			Usage()
			return
		}
		factory468 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt469 := factory468.GetProtocol(mbTrans466)
		argvalue0 := adinfo.NewRequestHeader()
		err470 := argvalue0.Read(jsProt469)
		if err470 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err471 := (strconv.Atoi(flag.Arg(2)))
		if err471 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg472 := flag.Arg(3)
		mbTrans473 := thrift.NewTMemoryBufferLen(len(arg472))
		defer mbTrans473.Close()
		_, err474 := mbTrans473.WriteString(arg472)
		if err474 != nil {
			Usage()
			return
		}
		factory475 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt476 := factory475.GetProtocol(mbTrans473)
		containerStruct2 := adinfo.NewSysPauseAdPlansByPidsArgs()
		err477 := containerStruct2.ReadField3(jsProt476)
		if err477 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		fmt.Print(client.SysPauseAdPlansByPids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "sysResumeAdPlansByPids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SysResumeAdPlansByPids requires 3 args")
			flag.Usage()
		}
		arg478 := flag.Arg(1)
		mbTrans479 := thrift.NewTMemoryBufferLen(len(arg478))
		defer mbTrans479.Close()
		_, err480 := mbTrans479.WriteString(arg478)
		if err480 != nil {
			Usage()
			return
		}
		factory481 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt482 := factory481.GetProtocol(mbTrans479)
		argvalue0 := adinfo.NewRequestHeader()
		err483 := argvalue0.Read(jsProt482)
		if err483 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err484 := (strconv.Atoi(flag.Arg(2)))
		if err484 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg485 := flag.Arg(3)
		mbTrans486 := thrift.NewTMemoryBufferLen(len(arg485))
		defer mbTrans486.Close()
		_, err487 := mbTrans486.WriteString(arg485)
		if err487 != nil {
			Usage()
			return
		}
		factory488 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt489 := factory488.GetProtocol(mbTrans486)
		containerStruct2 := adinfo.NewSysResumeAdPlansByPidsArgs()
		err490 := containerStruct2.ReadField3(jsProt489)
		if err490 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		fmt.Print(client.SysResumeAdPlansByPids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "stopAdPlansByPids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "StopAdPlansByPids requires 3 args")
			flag.Usage()
		}
		arg491 := flag.Arg(1)
		mbTrans492 := thrift.NewTMemoryBufferLen(len(arg491))
		defer mbTrans492.Close()
		_, err493 := mbTrans492.WriteString(arg491)
		if err493 != nil {
			Usage()
			return
		}
		factory494 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt495 := factory494.GetProtocol(mbTrans492)
		argvalue0 := adinfo.NewRequestHeader()
		err496 := argvalue0.Read(jsProt495)
		if err496 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err497 := (strconv.Atoi(flag.Arg(2)))
		if err497 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg498 := flag.Arg(3)
		mbTrans499 := thrift.NewTMemoryBufferLen(len(arg498))
		defer mbTrans499.Close()
		_, err500 := mbTrans499.WriteString(arg498)
		if err500 != nil {
			Usage()
			return
		}
		factory501 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt502 := factory501.GetProtocol(mbTrans499)
		containerStruct2 := adinfo.NewStopAdPlansByPidsArgs()
		err503 := containerStruct2.ReadField3(jsProt502)
		if err503 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		fmt.Print(client.StopAdPlansByPids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeAdPlansByPids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeAdPlansByPids requires 3 args")
			flag.Usage()
		}
		arg504 := flag.Arg(1)
		mbTrans505 := thrift.NewTMemoryBufferLen(len(arg504))
		defer mbTrans505.Close()
		_, err506 := mbTrans505.WriteString(arg504)
		if err506 != nil {
			Usage()
			return
		}
		factory507 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt508 := factory507.GetProtocol(mbTrans505)
		argvalue0 := adinfo.NewRequestHeader()
		err509 := argvalue0.Read(jsProt508)
		if err509 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err510 := (strconv.Atoi(flag.Arg(2)))
		if err510 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg511 := flag.Arg(3)
		mbTrans512 := thrift.NewTMemoryBufferLen(len(arg511))
		defer mbTrans512.Close()
		_, err513 := mbTrans512.WriteString(arg511)
		if err513 != nil {
			Usage()
			return
		}
		factory514 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt515 := factory514.GetProtocol(mbTrans512)
		containerStruct2 := adinfo.NewResumeAdPlansByPidsArgs()
		err516 := containerStruct2.ReadField3(jsProt515)
		if err516 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		fmt.Print(client.ResumeAdPlansByPids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdPlansByPids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdPlansByPids requires 3 args")
			flag.Usage()
		}
		arg517 := flag.Arg(1)
		mbTrans518 := thrift.NewTMemoryBufferLen(len(arg517))
		defer mbTrans518.Close()
		_, err519 := mbTrans518.WriteString(arg517)
		if err519 != nil {
			Usage()
			return
		}
		factory520 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt521 := factory520.GetProtocol(mbTrans518)
		argvalue0 := adinfo.NewRequestHeader()
		err522 := argvalue0.Read(jsProt521)
		if err522 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err523 := (strconv.Atoi(flag.Arg(2)))
		if err523 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg524 := flag.Arg(3)
		mbTrans525 := thrift.NewTMemoryBufferLen(len(arg524))
		defer mbTrans525.Close()
		_, err526 := mbTrans525.WriteString(arg524)
		if err526 != nil {
			Usage()
			return
		}
		factory527 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt528 := factory527.GetProtocol(mbTrans525)
		containerStruct2 := adinfo.NewDeleteAdPlansByPidsArgs()
		err529 := containerStruct2.ReadField3(jsProt528)
		if err529 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		fmt.Print(client.DeleteAdPlansByPids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAdPlansByUid":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAdPlansByUid requires 6 args")
			flag.Usage()
		}
		arg530 := flag.Arg(1)
		mbTrans531 := thrift.NewTMemoryBufferLen(len(arg530))
		defer mbTrans531.Close()
		_, err532 := mbTrans531.WriteString(arg530)
		if err532 != nil {
			Usage()
			return
		}
		factory533 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt534 := factory533.GetProtocol(mbTrans531)
		argvalue0 := adinfo.NewRequestHeader()
		err535 := argvalue0.Read(jsProt534)
		if err535 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err536 := (strconv.Atoi(flag.Arg(2)))
		if err536 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err538 := (strconv.Atoi(flag.Arg(4)))
		if err538 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err539 := (strconv.Atoi(flag.Arg(5)))
		if err539 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAdPlansByUid(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listAdPlansByUidAndCostType":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListAdPlansByUidAndCostType requires 7 args")
			flag.Usage()
		}
		arg541 := flag.Arg(1)
		mbTrans542 := thrift.NewTMemoryBufferLen(len(arg541))
		defer mbTrans542.Close()
		_, err543 := mbTrans542.WriteString(arg541)
		if err543 != nil {
			Usage()
			return
		}
		factory544 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt545 := factory544.GetProtocol(mbTrans542)
		argvalue0 := adinfo.NewRequestHeader()
		err546 := argvalue0.Read(jsProt545)
		if err546 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err547 := (strconv.Atoi(flag.Arg(2)))
		if err547 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.CostType(tmp2)
		value2 := adinfo.CostType(argvalue2)
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		tmp4, err549 := (strconv.Atoi(flag.Arg(5)))
		if err549 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		tmp5, err550 := (strconv.Atoi(flag.Arg(6)))
		if err550 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := adinfo.AdQueryInt(argvalue5)
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListAdPlansByUidAndCostType(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listAdPlansByUidOrderByPid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListAdPlansByUidOrderByPid requires 5 args")
			flag.Usage()
		}
		arg552 := flag.Arg(1)
		mbTrans553 := thrift.NewTMemoryBufferLen(len(arg552))
		defer mbTrans553.Close()
		_, err554 := mbTrans553.WriteString(arg552)
		if err554 != nil {
			Usage()
			return
		}
		factory555 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt556 := factory555.GetProtocol(mbTrans553)
		argvalue0 := adinfo.NewRequestHeader()
		err557 := argvalue0.Read(jsProt556)
		if err557 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err558 := (strconv.Atoi(flag.Arg(2)))
		if err558 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err560 := (strconv.Atoi(flag.Arg(4)))
		if err560 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err561 := (strconv.Atoi(flag.Arg(5)))
		if err561 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		fmt.Print(client.ListAdPlansByUidOrderByPid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listAdPlansByUidAndCostTypeOrderByPid":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAdPlansByUidAndCostTypeOrderByPid requires 6 args")
			flag.Usage()
		}
		arg562 := flag.Arg(1)
		mbTrans563 := thrift.NewTMemoryBufferLen(len(arg562))
		defer mbTrans563.Close()
		_, err564 := mbTrans563.WriteString(arg562)
		if err564 != nil {
			Usage()
			return
		}
		factory565 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt566 := factory565.GetProtocol(mbTrans563)
		argvalue0 := adinfo.NewRequestHeader()
		err567 := argvalue0.Read(jsProt566)
		if err567 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err568 := (strconv.Atoi(flag.Arg(2)))
		if err568 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.CostType(tmp2)
		value2 := adinfo.CostType(argvalue2)
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		tmp4, err570 := (strconv.Atoi(flag.Arg(5)))
		if err570 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		tmp5, err571 := (strconv.Atoi(flag.Arg(6)))
		if err571 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := adinfo.AdQueryInt(argvalue5)
		fmt.Print(client.ListAdPlansByUidAndCostTypeOrderByPid(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listRunnableAdPlansByUidOrderByLastUpdate":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListRunnableAdPlansByUidOrderByLastUpdate requires 5 args")
			flag.Usage()
		}
		arg572 := flag.Arg(1)
		mbTrans573 := thrift.NewTMemoryBufferLen(len(arg572))
		defer mbTrans573.Close()
		_, err574 := mbTrans573.WriteString(arg572)
		if err574 != nil {
			Usage()
			return
		}
		factory575 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt576 := factory575.GetProtocol(mbTrans573)
		argvalue0 := adinfo.NewRequestHeader()
		err577 := argvalue0.Read(jsProt576)
		if err577 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err578 := (strconv.Atoi(flag.Arg(2)))
		if err578 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err579 := (strconv.Atoi(flag.Arg(3)))
		if err579 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdQueryInt(argvalue2)
		tmp3, err580 := (strconv.Atoi(flag.Arg(4)))
		if err580 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListRunnableAdPlansByUidOrderByLastUpdate(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listRunnableAdPlansByUidAndCostTypeOrderByLastUpdate":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListRunnableAdPlansByUidAndCostTypeOrderByLastUpdate requires 6 args")
			flag.Usage()
		}
		arg582 := flag.Arg(1)
		mbTrans583 := thrift.NewTMemoryBufferLen(len(arg582))
		defer mbTrans583.Close()
		_, err584 := mbTrans583.WriteString(arg582)
		if err584 != nil {
			Usage()
			return
		}
		factory585 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt586 := factory585.GetProtocol(mbTrans583)
		argvalue0 := adinfo.NewRequestHeader()
		err587 := argvalue0.Read(jsProt586)
		if err587 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err588 := (strconv.Atoi(flag.Arg(2)))
		if err588 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.CostType(tmp2)
		value2 := adinfo.CostType(argvalue2)
		tmp3, err589 := (strconv.Atoi(flag.Arg(4)))
		if err589 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err590 := (strconv.Atoi(flag.Arg(5)))
		if err590 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListRunnableAdPlansByUidAndCostTypeOrderByLastUpdate(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listAdPlanIdsByDspCampaignId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListAdPlanIdsByDspCampaignId requires 3 args")
			flag.Usage()
		}
		arg592 := flag.Arg(1)
		mbTrans593 := thrift.NewTMemoryBufferLen(len(arg592))
		defer mbTrans593.Close()
		_, err594 := mbTrans593.WriteString(arg592)
		if err594 != nil {
			Usage()
			return
		}
		factory595 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt596 := factory595.GetProtocol(mbTrans593)
		argvalue0 := adinfo.NewRequestHeader()
		err597 := argvalue0.Read(jsProt596)
		if err597 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err598 := (strconv.Atoi(flag.Arg(2)))
		if err598 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListAdPlanIdsByDspCampaignId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAdPlanIdsByDspCampaignIdAndTaskFlag":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAdPlanIdsByDspCampaignIdAndTaskFlag requires 4 args")
			flag.Usage()
		}
		arg600 := flag.Arg(1)
		mbTrans601 := thrift.NewTMemoryBufferLen(len(arg600))
		defer mbTrans601.Close()
		_, err602 := mbTrans601.WriteString(arg600)
		if err602 != nil {
			Usage()
			return
		}
		factory603 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt604 := factory603.GetProtocol(mbTrans601)
		argvalue0 := adinfo.NewRequestHeader()
		err605 := argvalue0.Read(jsProt604)
		if err605 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err606 := (strconv.Atoi(flag.Arg(2)))
		if err606 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err607 := (strconv.Atoi(flag.Arg(3)))
		if err607 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListAdPlanIdsByDspCampaignIdAndTaskFlag(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdPlanOrderById":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetAdPlanOrderById requires 4 args")
			flag.Usage()
		}
		arg609 := flag.Arg(1)
		mbTrans610 := thrift.NewTMemoryBufferLen(len(arg609))
		defer mbTrans610.Close()
		_, err611 := mbTrans610.WriteString(arg609)
		if err611 != nil {
			Usage()
			return
		}
		factory612 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt613 := factory612.GetProtocol(mbTrans610)
		argvalue0 := adinfo.NewRequestHeader()
		err614 := argvalue0.Read(jsProt613)
		if err614 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		tmp2, err616 := (strconv.Atoi(flag.Arg(3)))
		if err616 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdQueryInt(argvalue2)
		tmp3, err617 := (strconv.Atoi(flag.Arg(4)))
		if err617 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		fmt.Print(client.GetAdPlanOrderById(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdStrategyOrderById":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetAdStrategyOrderById requires 4 args")
			flag.Usage()
		}
		arg618 := flag.Arg(1)
		mbTrans619 := thrift.NewTMemoryBufferLen(len(arg618))
		defer mbTrans619.Close()
		_, err620 := mbTrans619.WriteString(arg618)
		if err620 != nil {
			Usage()
			return
		}
		factory621 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt622 := factory621.GetProtocol(mbTrans619)
		argvalue0 := adinfo.NewRequestHeader()
		err623 := argvalue0.Read(jsProt622)
		if err623 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		tmp2, err625 := (strconv.Atoi(flag.Arg(3)))
		if err625 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdQueryInt(argvalue2)
		tmp3, err626 := (strconv.Atoi(flag.Arg(4)))
		if err626 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		fmt.Print(client.GetAdStrategyOrderById(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdCreativeOrderById":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetAdCreativeOrderById requires 4 args")
			flag.Usage()
		}
		arg627 := flag.Arg(1)
		mbTrans628 := thrift.NewTMemoryBufferLen(len(arg627))
		defer mbTrans628.Close()
		_, err629 := mbTrans628.WriteString(arg627)
		if err629 != nil {
			Usage()
			return
		}
		factory630 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt631 := factory630.GetProtocol(mbTrans628)
		argvalue0 := adinfo.NewRequestHeader()
		err632 := argvalue0.Read(jsProt631)
		if err632 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		tmp2, err634 := (strconv.Atoi(flag.Arg(3)))
		if err634 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdQueryInt(argvalue2)
		tmp3, err635 := (strconv.Atoi(flag.Arg(4)))
		if err635 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		fmt.Print(client.GetAdCreativeOrderById(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdPlansByPids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPlansByPids requires 2 args")
			flag.Usage()
		}
		arg636 := flag.Arg(1)
		mbTrans637 := thrift.NewTMemoryBufferLen(len(arg636))
		defer mbTrans637.Close()
		_, err638 := mbTrans637.WriteString(arg636)
		if err638 != nil {
			Usage()
			return
		}
		factory639 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt640 := factory639.GetProtocol(mbTrans637)
		argvalue0 := adinfo.NewRequestHeader()
		err641 := argvalue0.Read(jsProt640)
		if err641 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg642 := flag.Arg(2)
		mbTrans643 := thrift.NewTMemoryBufferLen(len(arg642))
		defer mbTrans643.Close()
		_, err644 := mbTrans643.WriteString(arg642)
		if err644 != nil {
			Usage()
			return
		}
		factory645 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt646 := factory645.GetProtocol(mbTrans643)
		containerStruct1 := adinfo.NewGetAdPlansByPidsArgs()
		err647 := containerStruct1.ReadField2(jsProt646)
		if err647 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Pids
		value1 := argvalue1
		fmt.Print(client.GetAdPlansByPids(value0, value1))
		fmt.Print("\n")
		break
	case "addAdStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdStrategy requires 2 args")
			flag.Usage()
		}
		arg648 := flag.Arg(1)
		mbTrans649 := thrift.NewTMemoryBufferLen(len(arg648))
		defer mbTrans649.Close()
		_, err650 := mbTrans649.WriteString(arg648)
		if err650 != nil {
			Usage()
			return
		}
		factory651 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt652 := factory651.GetProtocol(mbTrans649)
		argvalue0 := adinfo.NewRequestHeader()
		err653 := argvalue0.Read(jsProt652)
		if err653 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg654 := flag.Arg(2)
		mbTrans655 := thrift.NewTMemoryBufferLen(len(arg654))
		defer mbTrans655.Close()
		_, err656 := mbTrans655.WriteString(arg654)
		if err656 != nil {
			Usage()
			return
		}
		factory657 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt658 := factory657.GetProtocol(mbTrans655)
		argvalue1 := adinfo.NewAdStrategy()
		err659 := argvalue1.Read(jsProt658)
		if err659 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.AddAdStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "editAdStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdStrategy requires 2 args")
			flag.Usage()
		}
		arg660 := flag.Arg(1)
		mbTrans661 := thrift.NewTMemoryBufferLen(len(arg660))
		defer mbTrans661.Close()
		_, err662 := mbTrans661.WriteString(arg660)
		if err662 != nil {
			Usage()
			return
		}
		factory663 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt664 := factory663.GetProtocol(mbTrans661)
		argvalue0 := adinfo.NewRequestHeader()
		err665 := argvalue0.Read(jsProt664)
		if err665 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg666 := flag.Arg(2)
		mbTrans667 := thrift.NewTMemoryBufferLen(len(arg666))
		defer mbTrans667.Close()
		_, err668 := mbTrans667.WriteString(arg666)
		if err668 != nil {
			Usage()
			return
		}
		factory669 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt670 := factory669.GetProtocol(mbTrans667)
		argvalue1 := adinfo.NewAdStrategy()
		err671 := argvalue1.Read(jsProt670)
		if err671 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.EditAdStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdStrategyBySids":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PauseAdStrategyBySids requires 4 args")
			flag.Usage()
		}
		arg672 := flag.Arg(1)
		mbTrans673 := thrift.NewTMemoryBufferLen(len(arg672))
		defer mbTrans673.Close()
		_, err674 := mbTrans673.WriteString(arg672)
		if err674 != nil {
			Usage()
			return
		}
		factory675 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt676 := factory675.GetProtocol(mbTrans673)
		argvalue0 := adinfo.NewRequestHeader()
		err677 := argvalue0.Read(jsProt676)
		if err677 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err678 := (strconv.Atoi(flag.Arg(2)))
		if err678 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err679 := (strconv.Atoi(flag.Arg(3)))
		if err679 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		arg680 := flag.Arg(4)
		mbTrans681 := thrift.NewTMemoryBufferLen(len(arg680))
		defer mbTrans681.Close()
		_, err682 := mbTrans681.WriteString(arg680)
		if err682 != nil {
			Usage()
			return
		}
		factory683 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt684 := factory683.GetProtocol(mbTrans681)
		containerStruct3 := adinfo.NewPauseAdStrategyBySidsArgs()
		err685 := containerStruct3.ReadField4(jsProt684)
		if err685 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Sids
		value3 := argvalue3
		fmt.Print(client.PauseAdStrategyBySids(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resumeAdStrategyBySids":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResumeAdStrategyBySids requires 4 args")
			flag.Usage()
		}
		arg686 := flag.Arg(1)
		mbTrans687 := thrift.NewTMemoryBufferLen(len(arg686))
		defer mbTrans687.Close()
		_, err688 := mbTrans687.WriteString(arg686)
		if err688 != nil {
			Usage()
			return
		}
		factory689 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt690 := factory689.GetProtocol(mbTrans687)
		argvalue0 := adinfo.NewRequestHeader()
		err691 := argvalue0.Read(jsProt690)
		if err691 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err692 := (strconv.Atoi(flag.Arg(2)))
		if err692 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err693 := (strconv.Atoi(flag.Arg(3)))
		if err693 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		arg694 := flag.Arg(4)
		mbTrans695 := thrift.NewTMemoryBufferLen(len(arg694))
		defer mbTrans695.Close()
		_, err696 := mbTrans695.WriteString(arg694)
		if err696 != nil {
			Usage()
			return
		}
		factory697 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt698 := factory697.GetProtocol(mbTrans695)
		containerStruct3 := adinfo.NewResumeAdStrategyBySidsArgs()
		err699 := containerStruct3.ReadField4(jsProt698)
		if err699 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Sids
		value3 := argvalue3
		fmt.Print(client.ResumeAdStrategyBySids(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAdStrategyBySids":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteAdStrategyBySids requires 4 args")
			flag.Usage()
		}
		arg700 := flag.Arg(1)
		mbTrans701 := thrift.NewTMemoryBufferLen(len(arg700))
		defer mbTrans701.Close()
		_, err702 := mbTrans701.WriteString(arg700)
		if err702 != nil {
			Usage()
			return
		}
		factory703 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt704 := factory703.GetProtocol(mbTrans701)
		argvalue0 := adinfo.NewRequestHeader()
		err705 := argvalue0.Read(jsProt704)
		if err705 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err706 := (strconv.Atoi(flag.Arg(2)))
		if err706 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err707 := (strconv.Atoi(flag.Arg(3)))
		if err707 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		arg708 := flag.Arg(4)
		mbTrans709 := thrift.NewTMemoryBufferLen(len(arg708))
		defer mbTrans709.Close()
		_, err710 := mbTrans709.WriteString(arg708)
		if err710 != nil {
			Usage()
			return
		}
		factory711 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt712 := factory711.GetProtocol(mbTrans709)
		containerStruct3 := adinfo.NewDeleteAdStrategyBySidsArgs()
		err713 := containerStruct3.ReadField4(jsProt712)
		if err713 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Sids
		value3 := argvalue3
		fmt.Print(client.DeleteAdStrategyBySids(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "copyAdStrategiesToAdPlan":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "CopyAdStrategiesToAdPlan requires 4 args")
			flag.Usage()
		}
		arg714 := flag.Arg(1)
		mbTrans715 := thrift.NewTMemoryBufferLen(len(arg714))
		defer mbTrans715.Close()
		_, err716 := mbTrans715.WriteString(arg714)
		if err716 != nil {
			Usage()
			return
		}
		factory717 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt718 := factory717.GetProtocol(mbTrans715)
		argvalue0 := adinfo.NewRequestHeader()
		err719 := argvalue0.Read(jsProt718)
		if err719 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err720 := (strconv.Atoi(flag.Arg(2)))
		if err720 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg721 := flag.Arg(3)
		mbTrans722 := thrift.NewTMemoryBufferLen(len(arg721))
		defer mbTrans722.Close()
		_, err723 := mbTrans722.WriteString(arg721)
		if err723 != nil {
			Usage()
			return
		}
		factory724 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt725 := factory724.GetProtocol(mbTrans722)
		containerStruct2 := adinfo.NewCopyAdStrategiesToAdPlanArgs()
		err726 := containerStruct2.ReadField3(jsProt725)
		if err726 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Sids
		value2 := argvalue2
		tmp3, err727 := (strconv.Atoi(flag.Arg(4)))
		if err727 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdPlanIdInt(argvalue3)
		fmt.Print(client.CopyAdStrategiesToAdPlan(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdStrategiesBySids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategiesBySids requires 2 args")
			flag.Usage()
		}
		arg728 := flag.Arg(1)
		mbTrans729 := thrift.NewTMemoryBufferLen(len(arg728))
		defer mbTrans729.Close()
		_, err730 := mbTrans729.WriteString(arg728)
		if err730 != nil {
			Usage()
			return
		}
		factory731 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt732 := factory731.GetProtocol(mbTrans729)
		argvalue0 := adinfo.NewRequestHeader()
		err733 := argvalue0.Read(jsProt732)
		if err733 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg734 := flag.Arg(2)
		mbTrans735 := thrift.NewTMemoryBufferLen(len(arg734))
		defer mbTrans735.Close()
		_, err736 := mbTrans735.WriteString(arg734)
		if err736 != nil {
			Usage()
			return
		}
		factory737 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt738 := factory737.GetProtocol(mbTrans735)
		containerStruct1 := adinfo.NewGetAdStrategiesBySidsArgs()
		err739 := containerStruct1.ReadField2(jsProt738)
		if err739 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Sids
		value1 := argvalue1
		fmt.Print(client.GetAdStrategiesBySids(value0, value1))
		fmt.Print("\n")
		break
	case "listAdStrategiesByPid":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAdStrategiesByPid requires 6 args")
			flag.Usage()
		}
		arg740 := flag.Arg(1)
		mbTrans741 := thrift.NewTMemoryBufferLen(len(arg740))
		defer mbTrans741.Close()
		_, err742 := mbTrans741.WriteString(arg740)
		if err742 != nil {
			Usage()
			return
		}
		factory743 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt744 := factory743.GetProtocol(mbTrans741)
		argvalue0 := adinfo.NewRequestHeader()
		err745 := argvalue0.Read(jsProt744)
		if err745 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err746 := (strconv.Atoi(flag.Arg(2)))
		if err746 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.AdPlanIdInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err748 := (strconv.Atoi(flag.Arg(4)))
		if err748 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err749 := (strconv.Atoi(flag.Arg(5)))
		if err749 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAdStrategiesByPid(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listAdStrategiesByPidOrderBySid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListAdStrategiesByPidOrderBySid requires 5 args")
			flag.Usage()
		}
		arg751 := flag.Arg(1)
		mbTrans752 := thrift.NewTMemoryBufferLen(len(arg751))
		defer mbTrans752.Close()
		_, err753 := mbTrans752.WriteString(arg751)
		if err753 != nil {
			Usage()
			return
		}
		factory754 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt755 := factory754.GetProtocol(mbTrans752)
		argvalue0 := adinfo.NewRequestHeader()
		err756 := argvalue0.Read(jsProt755)
		if err756 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err757 := (strconv.Atoi(flag.Arg(2)))
		if err757 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.AdPlanIdInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err759 := (strconv.Atoi(flag.Arg(4)))
		if err759 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err760 := (strconv.Atoi(flag.Arg(5)))
		if err760 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		fmt.Print(client.ListAdStrategiesByPidOrderBySid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listAdStrategiesByUidAndAdStrategyType":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAdStrategiesByUidAndAdStrategyType requires 6 args")
			flag.Usage()
		}
		arg761 := flag.Arg(1)
		mbTrans762 := thrift.NewTMemoryBufferLen(len(arg761))
		defer mbTrans762.Close()
		_, err763 := mbTrans762.WriteString(arg761)
		if err763 != nil {
			Usage()
			return
		}
		factory764 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt765 := factory764.GetProtocol(mbTrans762)
		argvalue0 := adinfo.NewRequestHeader()
		err766 := argvalue0.Read(jsProt765)
		if err766 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err767 := (strconv.Atoi(flag.Arg(2)))
		if err767 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.AdStrategyType(tmp2)
		value2 := adinfo.AdStrategyType(argvalue2)
		tmp3, err768 := (strconv.Atoi(flag.Arg(4)))
		if err768 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err769 := (strconv.Atoi(flag.Arg(5)))
		if err769 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAdStrategiesByUidAndAdStrategyType(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "addAdCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdCreative requires 2 args")
			flag.Usage()
		}
		arg771 := flag.Arg(1)
		mbTrans772 := thrift.NewTMemoryBufferLen(len(arg771))
		defer mbTrans772.Close()
		_, err773 := mbTrans772.WriteString(arg771)
		if err773 != nil {
			Usage()
			return
		}
		factory774 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt775 := factory774.GetProtocol(mbTrans772)
		argvalue0 := adinfo.NewRequestHeader()
		err776 := argvalue0.Read(jsProt775)
		if err776 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg777 := flag.Arg(2)
		mbTrans778 := thrift.NewTMemoryBufferLen(len(arg777))
		defer mbTrans778.Close()
		_, err779 := mbTrans778.WriteString(arg777)
		if err779 != nil {
			Usage()
			return
		}
		factory780 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt781 := factory780.GetProtocol(mbTrans778)
		argvalue1 := adinfo.NewAdCreative()
		err782 := argvalue1.Read(jsProt781)
		if err782 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.AddAdCreative(value0, value1))
		fmt.Print("\n")
		break
	case "editAdCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdCreative requires 2 args")
			flag.Usage()
		}
		arg783 := flag.Arg(1)
		mbTrans784 := thrift.NewTMemoryBufferLen(len(arg783))
		defer mbTrans784.Close()
		_, err785 := mbTrans784.WriteString(arg783)
		if err785 != nil {
			Usage()
			return
		}
		factory786 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt787 := factory786.GetProtocol(mbTrans784)
		argvalue0 := adinfo.NewRequestHeader()
		err788 := argvalue0.Read(jsProt787)
		if err788 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg789 := flag.Arg(2)
		mbTrans790 := thrift.NewTMemoryBufferLen(len(arg789))
		defer mbTrans790.Close()
		_, err791 := mbTrans790.WriteString(arg789)
		if err791 != nil {
			Usage()
			return
		}
		factory792 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt793 := factory792.GetProtocol(mbTrans790)
		argvalue1 := adinfo.NewAdCreative()
		err794 := argvalue1.Read(jsProt793)
		if err794 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.EditAdCreative(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCreativeByCids":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "PauseAdCreativeByCids requires 5 args")
			flag.Usage()
		}
		arg795 := flag.Arg(1)
		mbTrans796 := thrift.NewTMemoryBufferLen(len(arg795))
		defer mbTrans796.Close()
		_, err797 := mbTrans796.WriteString(arg795)
		if err797 != nil {
			Usage()
			return
		}
		factory798 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt799 := factory798.GetProtocol(mbTrans796)
		argvalue0 := adinfo.NewRequestHeader()
		err800 := argvalue0.Read(jsProt799)
		if err800 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err801 := (strconv.Atoi(flag.Arg(2)))
		if err801 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err802 := (strconv.Atoi(flag.Arg(3)))
		if err802 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		tmp3, err803 := (strconv.Atoi(flag.Arg(4)))
		if err803 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdStrategyIdInt(argvalue3)
		arg804 := flag.Arg(5)
		mbTrans805 := thrift.NewTMemoryBufferLen(len(arg804))
		defer mbTrans805.Close()
		_, err806 := mbTrans805.WriteString(arg804)
		if err806 != nil {
			Usage()
			return
		}
		factory807 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt808 := factory807.GetProtocol(mbTrans805)
		containerStruct4 := adinfo.NewPauseAdCreativeByCidsArgs()
		err809 := containerStruct4.ReadField5(jsProt808)
		if err809 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Cids
		value4 := argvalue4
		fmt.Print(client.PauseAdCreativeByCids(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "resumeAdCreativeByCids":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ResumeAdCreativeByCids requires 5 args")
			flag.Usage()
		}
		arg810 := flag.Arg(1)
		mbTrans811 := thrift.NewTMemoryBufferLen(len(arg810))
		defer mbTrans811.Close()
		_, err812 := mbTrans811.WriteString(arg810)
		if err812 != nil {
			Usage()
			return
		}
		factory813 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt814 := factory813.GetProtocol(mbTrans811)
		argvalue0 := adinfo.NewRequestHeader()
		err815 := argvalue0.Read(jsProt814)
		if err815 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err816 := (strconv.Atoi(flag.Arg(2)))
		if err816 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err817 := (strconv.Atoi(flag.Arg(3)))
		if err817 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		tmp3, err818 := (strconv.Atoi(flag.Arg(4)))
		if err818 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdStrategyIdInt(argvalue3)
		arg819 := flag.Arg(5)
		mbTrans820 := thrift.NewTMemoryBufferLen(len(arg819))
		defer mbTrans820.Close()
		_, err821 := mbTrans820.WriteString(arg819)
		if err821 != nil {
			Usage()
			return
		}
		factory822 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt823 := factory822.GetProtocol(mbTrans820)
		containerStruct4 := adinfo.NewResumeAdCreativeByCidsArgs()
		err824 := containerStruct4.ReadField5(jsProt823)
		if err824 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Cids
		value4 := argvalue4
		fmt.Print(client.ResumeAdCreativeByCids(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteAdCreativeByCids":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteAdCreativeByCids requires 5 args")
			flag.Usage()
		}
		arg825 := flag.Arg(1)
		mbTrans826 := thrift.NewTMemoryBufferLen(len(arg825))
		defer mbTrans826.Close()
		_, err827 := mbTrans826.WriteString(arg825)
		if err827 != nil {
			Usage()
			return
		}
		factory828 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt829 := factory828.GetProtocol(mbTrans826)
		argvalue0 := adinfo.NewRequestHeader()
		err830 := argvalue0.Read(jsProt829)
		if err830 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err831 := (strconv.Atoi(flag.Arg(2)))
		if err831 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err832 := (strconv.Atoi(flag.Arg(3)))
		if err832 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		tmp3, err833 := (strconv.Atoi(flag.Arg(4)))
		if err833 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdStrategyIdInt(argvalue3)
		arg834 := flag.Arg(5)
		mbTrans835 := thrift.NewTMemoryBufferLen(len(arg834))
		defer mbTrans835.Close()
		_, err836 := mbTrans835.WriteString(arg834)
		if err836 != nil {
			Usage()
			return
		}
		factory837 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt838 := factory837.GetProtocol(mbTrans835)
		containerStruct4 := adinfo.NewDeleteAdCreativeByCidsArgs()
		err839 := containerStruct4.ReadField5(jsProt838)
		if err839 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Cids
		value4 := argvalue4
		fmt.Print(client.DeleteAdCreativeByCids(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "submitAdCreativeByCids":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "SubmitAdCreativeByCids requires 5 args")
			flag.Usage()
		}
		arg840 := flag.Arg(1)
		mbTrans841 := thrift.NewTMemoryBufferLen(len(arg840))
		defer mbTrans841.Close()
		_, err842 := mbTrans841.WriteString(arg840)
		if err842 != nil {
			Usage()
			return
		}
		factory843 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt844 := factory843.GetProtocol(mbTrans841)
		argvalue0 := adinfo.NewRequestHeader()
		err845 := argvalue0.Read(jsProt844)
		if err845 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err846 := (strconv.Atoi(flag.Arg(2)))
		if err846 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err847 := (strconv.Atoi(flag.Arg(3)))
		if err847 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		tmp3, err848 := (strconv.Atoi(flag.Arg(4)))
		if err848 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdStrategyIdInt(argvalue3)
		arg849 := flag.Arg(5)
		mbTrans850 := thrift.NewTMemoryBufferLen(len(arg849))
		defer mbTrans850.Close()
		_, err851 := mbTrans850.WriteString(arg849)
		if err851 != nil {
			Usage()
			return
		}
		factory852 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt853 := factory852.GetProtocol(mbTrans850)
		containerStruct4 := adinfo.NewSubmitAdCreativeByCidsArgs()
		err854 := containerStruct4.ReadField5(jsProt853)
		if err854 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Cids
		value4 := argvalue4
		fmt.Print(client.SubmitAdCreativeByCids(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAdCreativesByCids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCreativesByCids requires 2 args")
			flag.Usage()
		}
		arg855 := flag.Arg(1)
		mbTrans856 := thrift.NewTMemoryBufferLen(len(arg855))
		defer mbTrans856.Close()
		_, err857 := mbTrans856.WriteString(arg855)
		if err857 != nil {
			Usage()
			return
		}
		factory858 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt859 := factory858.GetProtocol(mbTrans856)
		argvalue0 := adinfo.NewRequestHeader()
		err860 := argvalue0.Read(jsProt859)
		if err860 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg861 := flag.Arg(2)
		mbTrans862 := thrift.NewTMemoryBufferLen(len(arg861))
		defer mbTrans862.Close()
		_, err863 := mbTrans862.WriteString(arg861)
		if err863 != nil {
			Usage()
			return
		}
		factory864 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt865 := factory864.GetProtocol(mbTrans862)
		containerStruct1 := adinfo.NewGetAdCreativesByCidsArgs()
		err866 := containerStruct1.ReadField2(jsProt865)
		if err866 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Cids
		value1 := argvalue1
		fmt.Print(client.GetAdCreativesByCids(value0, value1))
		fmt.Print("\n")
		break
	case "listAdCreativesBySid":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAdCreativesBySid requires 6 args")
			flag.Usage()
		}
		arg867 := flag.Arg(1)
		mbTrans868 := thrift.NewTMemoryBufferLen(len(arg867))
		defer mbTrans868.Close()
		_, err869 := mbTrans868.WriteString(arg867)
		if err869 != nil {
			Usage()
			return
		}
		factory870 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt871 := factory870.GetProtocol(mbTrans868)
		argvalue0 := adinfo.NewRequestHeader()
		err872 := argvalue0.Read(jsProt871)
		if err872 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err873 := (strconv.Atoi(flag.Arg(2)))
		if err873 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.AdStrategyIdInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err875 := (strconv.Atoi(flag.Arg(4)))
		if err875 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err876 := (strconv.Atoi(flag.Arg(5)))
		if err876 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAdCreativesBySid(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listAdCreativesBySidOrderByCid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListAdCreativesBySidOrderByCid requires 5 args")
			flag.Usage()
		}
		arg878 := flag.Arg(1)
		mbTrans879 := thrift.NewTMemoryBufferLen(len(arg878))
		defer mbTrans879.Close()
		_, err880 := mbTrans879.WriteString(arg878)
		if err880 != nil {
			Usage()
			return
		}
		factory881 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt882 := factory881.GetProtocol(mbTrans879)
		argvalue0 := adinfo.NewRequestHeader()
		err883 := argvalue0.Read(jsProt882)
		if err883 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err884 := (strconv.Atoi(flag.Arg(2)))
		if err884 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.AdStrategyIdInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err886 := (strconv.Atoi(flag.Arg(4)))
		if err886 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err887 := (strconv.Atoi(flag.Arg(5)))
		if err887 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		fmt.Print(client.ListAdCreativesBySidOrderByCid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listAdCreativesByTypeAndStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAdCreativesByTypeAndStatus requires 6 args")
			flag.Usage()
		}
		arg888 := flag.Arg(1)
		mbTrans889 := thrift.NewTMemoryBufferLen(len(arg888))
		defer mbTrans889.Close()
		_, err890 := mbTrans889.WriteString(arg888)
		if err890 != nil {
			Usage()
			return
		}
		factory891 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt892 := factory891.GetProtocol(mbTrans889)
		argvalue0 := adinfo.NewRequestHeader()
		err893 := argvalue0.Read(jsProt892)
		if err893 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := adinfo.AdCreativeType(tmp1)
		value1 := adinfo.AdCreativeType(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.AdCreativeStatus(tmp2)
		value2 := adinfo.AdCreativeStatus(argvalue2)
		tmp3, err894 := (strconv.Atoi(flag.Arg(4)))
		if err894 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err895 := (strconv.Atoi(flag.Arg(5)))
		if err895 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAdCreativesByTypeAndStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "adminUpdateAdUserWaterMarkByUids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminUpdateAdUserWaterMarkByUids requires 3 args")
			flag.Usage()
		}
		arg897 := flag.Arg(1)
		mbTrans898 := thrift.NewTMemoryBufferLen(len(arg897))
		defer mbTrans898.Close()
		_, err899 := mbTrans898.WriteString(arg897)
		if err899 != nil {
			Usage()
			return
		}
		factory900 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt901 := factory900.GetProtocol(mbTrans898)
		argvalue0 := adinfo.NewRequestHeader()
		err902 := argvalue0.Read(jsProt901)
		if err902 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg903 := flag.Arg(2)
		mbTrans904 := thrift.NewTMemoryBufferLen(len(arg903))
		defer mbTrans904.Close()
		_, err905 := mbTrans904.WriteString(arg903)
		if err905 != nil {
			Usage()
			return
		}
		factory906 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt907 := factory906.GetProtocol(mbTrans904)
		containerStruct1 := adinfo.NewAdminUpdateAdUserWaterMarkByUidsArgs()
		err908 := containerStruct1.ReadField2(jsProt907)
		if err908 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		arg909 := flag.Arg(3)
		mbTrans910 := thrift.NewTMemoryBufferLen(len(arg909))
		defer mbTrans910.Close()
		_, err911 := mbTrans910.WriteString(arg909)
		if err911 != nil {
			Usage()
			return
		}
		factory912 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt913 := factory912.GetProtocol(mbTrans910)
		containerStruct2 := adinfo.NewAdminUpdateAdUserWaterMarkByUidsArgs()
		err914 := containerStruct2.ReadField3(jsProt913)
		if err914 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AdUserWaterMarks
		value2 := argvalue2
		fmt.Print(client.AdminUpdateAdUserWaterMarkByUids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminUpdateAdPlanWaterMarkByPids":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "AdminUpdateAdPlanWaterMarkByPids requires 4 args")
			flag.Usage()
		}
		arg915 := flag.Arg(1)
		mbTrans916 := thrift.NewTMemoryBufferLen(len(arg915))
		defer mbTrans916.Close()
		_, err917 := mbTrans916.WriteString(arg915)
		if err917 != nil {
			Usage()
			return
		}
		factory918 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt919 := factory918.GetProtocol(mbTrans916)
		argvalue0 := adinfo.NewRequestHeader()
		err920 := argvalue0.Read(jsProt919)
		if err920 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err921 := (strconv.Atoi(flag.Arg(2)))
		if err921 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg922 := flag.Arg(3)
		mbTrans923 := thrift.NewTMemoryBufferLen(len(arg922))
		defer mbTrans923.Close()
		_, err924 := mbTrans923.WriteString(arg922)
		if err924 != nil {
			Usage()
			return
		}
		factory925 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt926 := factory925.GetProtocol(mbTrans923)
		containerStruct2 := adinfo.NewAdminUpdateAdPlanWaterMarkByPidsArgs()
		err927 := containerStruct2.ReadField3(jsProt926)
		if err927 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		arg928 := flag.Arg(4)
		mbTrans929 := thrift.NewTMemoryBufferLen(len(arg928))
		defer mbTrans929.Close()
		_, err930 := mbTrans929.WriteString(arg928)
		if err930 != nil {
			Usage()
			return
		}
		factory931 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt932 := factory931.GetProtocol(mbTrans929)
		containerStruct3 := adinfo.NewAdminUpdateAdPlanWaterMarkByPidsArgs()
		err933 := containerStruct3.ReadField4(jsProt932)
		if err933 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.AdPlanWaterMarks
		value3 := argvalue3
		fmt.Print(client.AdminUpdateAdPlanWaterMarkByPids(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "adminApproveAdCreativesByCids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminApproveAdCreativesByCids requires 3 args")
			flag.Usage()
		}
		arg934 := flag.Arg(1)
		mbTrans935 := thrift.NewTMemoryBufferLen(len(arg934))
		defer mbTrans935.Close()
		_, err936 := mbTrans935.WriteString(arg934)
		if err936 != nil {
			Usage()
			return
		}
		factory937 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt938 := factory937.GetProtocol(mbTrans935)
		argvalue0 := adinfo.NewRequestHeader()
		err939 := argvalue0.Read(jsProt938)
		if err939 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg940 := flag.Arg(2)
		mbTrans941 := thrift.NewTMemoryBufferLen(len(arg940))
		defer mbTrans941.Close()
		_, err942 := mbTrans941.WriteString(arg940)
		if err942 != nil {
			Usage()
			return
		}
		factory943 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt944 := factory943.GetProtocol(mbTrans941)
		containerStruct1 := adinfo.NewAdminApproveAdCreativesByCidsArgs()
		err945 := containerStruct1.ReadField2(jsProt944)
		if err945 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Cids
		value1 := argvalue1
		arg946 := flag.Arg(3)
		mbTrans947 := thrift.NewTMemoryBufferLen(len(arg946))
		defer mbTrans947.Close()
		_, err948 := mbTrans947.WriteString(arg946)
		if err948 != nil {
			Usage()
			return
		}
		factory949 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt950 := factory949.GetProtocol(mbTrans947)
		containerStruct2 := adinfo.NewAdminApproveAdCreativesByCidsArgs()
		err951 := containerStruct2.ReadField3(jsProt950)
		if err951 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminApproveAdCreativesByCids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminRejectAdCreativesByCids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminRejectAdCreativesByCids requires 3 args")
			flag.Usage()
		}
		arg952 := flag.Arg(1)
		mbTrans953 := thrift.NewTMemoryBufferLen(len(arg952))
		defer mbTrans953.Close()
		_, err954 := mbTrans953.WriteString(arg952)
		if err954 != nil {
			Usage()
			return
		}
		factory955 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt956 := factory955.GetProtocol(mbTrans953)
		argvalue0 := adinfo.NewRequestHeader()
		err957 := argvalue0.Read(jsProt956)
		if err957 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg958 := flag.Arg(2)
		mbTrans959 := thrift.NewTMemoryBufferLen(len(arg958))
		defer mbTrans959.Close()
		_, err960 := mbTrans959.WriteString(arg958)
		if err960 != nil {
			Usage()
			return
		}
		factory961 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt962 := factory961.GetProtocol(mbTrans959)
		containerStruct1 := adinfo.NewAdminRejectAdCreativesByCidsArgs()
		err963 := containerStruct1.ReadField2(jsProt962)
		if err963 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Cids
		value1 := argvalue1
		arg964 := flag.Arg(3)
		mbTrans965 := thrift.NewTMemoryBufferLen(len(arg964))
		defer mbTrans965.Close()
		_, err966 := mbTrans965.WriteString(arg964)
		if err966 != nil {
			Usage()
			return
		}
		factory967 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt968 := factory967.GetProtocol(mbTrans965)
		containerStruct2 := adinfo.NewAdminRejectAdCreativesByCidsArgs()
		err969 := containerStruct2.ReadField3(jsProt968)
		if err969 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminRejectAdCreativesByCids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdPlanStatusLamp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPlanStatusLamp requires 2 args")
			flag.Usage()
		}
		arg970 := flag.Arg(1)
		mbTrans971 := thrift.NewTMemoryBufferLen(len(arg970))
		defer mbTrans971.Close()
		_, err972 := mbTrans971.WriteString(arg970)
		if err972 != nil {
			Usage()
			return
		}
		factory973 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt974 := factory973.GetProtocol(mbTrans971)
		argvalue0 := adinfo.NewRequestHeader()
		err975 := argvalue0.Read(jsProt974)
		if err975 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg976 := flag.Arg(2)
		mbTrans977 := thrift.NewTMemoryBufferLen(len(arg976))
		defer mbTrans977.Close()
		_, err978 := mbTrans977.WriteString(arg976)
		if err978 != nil {
			Usage()
			return
		}
		factory979 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt980 := factory979.GetProtocol(mbTrans977)
		containerStruct1 := adinfo.NewGetAdPlanStatusLampArgs()
		err981 := containerStruct1.ReadField2(jsProt980)
		if err981 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Pids
		value1 := argvalue1
		fmt.Print(client.GetAdPlanStatusLamp(value0, value1))
		fmt.Print("\n")
		break
	case "getAdStrategyStatusLamp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategyStatusLamp requires 2 args")
			flag.Usage()
		}
		arg982 := flag.Arg(1)
		mbTrans983 := thrift.NewTMemoryBufferLen(len(arg982))
		defer mbTrans983.Close()
		_, err984 := mbTrans983.WriteString(arg982)
		if err984 != nil {
			Usage()
			return
		}
		factory985 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt986 := factory985.GetProtocol(mbTrans983)
		argvalue0 := adinfo.NewRequestHeader()
		err987 := argvalue0.Read(jsProt986)
		if err987 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg988 := flag.Arg(2)
		mbTrans989 := thrift.NewTMemoryBufferLen(len(arg988))
		defer mbTrans989.Close()
		_, err990 := mbTrans989.WriteString(arg988)
		if err990 != nil {
			Usage()
			return
		}
		factory991 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt992 := factory991.GetProtocol(mbTrans989)
		containerStruct1 := adinfo.NewGetAdStrategyStatusLampArgs()
		err993 := containerStruct1.ReadField2(jsProt992)
		if err993 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Sids
		value1 := argvalue1
		fmt.Print(client.GetAdStrategyStatusLamp(value0, value1))
		fmt.Print("\n")
		break
	case "getAdCreativeStatusLamp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCreativeStatusLamp requires 2 args")
			flag.Usage()
		}
		arg994 := flag.Arg(1)
		mbTrans995 := thrift.NewTMemoryBufferLen(len(arg994))
		defer mbTrans995.Close()
		_, err996 := mbTrans995.WriteString(arg994)
		if err996 != nil {
			Usage()
			return
		}
		factory997 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt998 := factory997.GetProtocol(mbTrans995)
		argvalue0 := adinfo.NewRequestHeader()
		err999 := argvalue0.Read(jsProt998)
		if err999 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1000 := flag.Arg(2)
		mbTrans1001 := thrift.NewTMemoryBufferLen(len(arg1000))
		defer mbTrans1001.Close()
		_, err1002 := mbTrans1001.WriteString(arg1000)
		if err1002 != nil {
			Usage()
			return
		}
		factory1003 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1004 := factory1003.GetProtocol(mbTrans1001)
		containerStruct1 := adinfo.NewGetAdCreativeStatusLampArgs()
		err1005 := containerStruct1.ReadField2(jsProt1004)
		if err1005 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Cids
		value1 := argvalue1
		fmt.Print(client.GetAdCreativeStatusLamp(value0, value1))
		fmt.Print("\n")
		break
	case "listAdInfoIdsByUid":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListAdInfoIdsByUid requires 3 args")
			flag.Usage()
		}
		arg1006 := flag.Arg(1)
		mbTrans1007 := thrift.NewTMemoryBufferLen(len(arg1006))
		defer mbTrans1007.Close()
		_, err1008 := mbTrans1007.WriteString(arg1006)
		if err1008 != nil {
			Usage()
			return
		}
		factory1009 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1010 := factory1009.GetProtocol(mbTrans1007)
		argvalue0 := adinfo.NewRequestHeader()
		err1011 := argvalue0.Read(jsProt1010)
		if err1011 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1012 := (strconv.Atoi(flag.Arg(2)))
		if err1012 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListAdInfoIdsByUid(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAdInfoIdsByUids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListAdInfoIdsByUids requires 3 args")
			flag.Usage()
		}
		arg1014 := flag.Arg(1)
		mbTrans1015 := thrift.NewTMemoryBufferLen(len(arg1014))
		defer mbTrans1015.Close()
		_, err1016 := mbTrans1015.WriteString(arg1014)
		if err1016 != nil {
			Usage()
			return
		}
		factory1017 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1018 := factory1017.GetProtocol(mbTrans1015)
		argvalue0 := adinfo.NewRequestHeader()
		err1019 := argvalue0.Read(jsProt1018)
		if err1019 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1020 := flag.Arg(2)
		mbTrans1021 := thrift.NewTMemoryBufferLen(len(arg1020))
		defer mbTrans1021.Close()
		_, err1022 := mbTrans1021.WriteString(arg1020)
		if err1022 != nil {
			Usage()
			return
		}
		factory1023 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1024 := factory1023.GetProtocol(mbTrans1021)
		containerStruct1 := adinfo.NewListAdInfoIdsByUidsArgs()
		err1025 := containerStruct1.ReadField2(jsProt1024)
		if err1025 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListAdInfoIdsByUids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAdInfoIdsByUidAndCostType":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAdInfoIdsByUidAndCostType requires 4 args")
			flag.Usage()
		}
		arg1027 := flag.Arg(1)
		mbTrans1028 := thrift.NewTMemoryBufferLen(len(arg1027))
		defer mbTrans1028.Close()
		_, err1029 := mbTrans1028.WriteString(arg1027)
		if err1029 != nil {
			Usage()
			return
		}
		factory1030 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1031 := factory1030.GetProtocol(mbTrans1028)
		argvalue0 := adinfo.NewRequestHeader()
		err1032 := argvalue0.Read(jsProt1031)
		if err1032 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1033 := (strconv.Atoi(flag.Arg(2)))
		if err1033 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.CostType(tmp2)
		value2 := adinfo.CostType(argvalue2)
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListAdInfoIdsByUidAndCostType(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "adminMarkAdCreativeActionType":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AdminMarkAdCreativeActionType requires 2 args")
			flag.Usage()
		}
		arg1035 := flag.Arg(1)
		mbTrans1036 := thrift.NewTMemoryBufferLen(len(arg1035))
		defer mbTrans1036.Close()
		_, err1037 := mbTrans1036.WriteString(arg1035)
		if err1037 != nil {
			Usage()
			return
		}
		factory1038 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1039 := factory1038.GetProtocol(mbTrans1036)
		argvalue0 := adinfo.NewRequestHeader()
		err1040 := argvalue0.Read(jsProt1039)
		if err1040 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1041 := flag.Arg(2)
		mbTrans1042 := thrift.NewTMemoryBufferLen(len(arg1041))
		defer mbTrans1042.Close()
		_, err1043 := mbTrans1042.WriteString(arg1041)
		if err1043 != nil {
			Usage()
			return
		}
		factory1044 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1045 := factory1044.GetProtocol(mbTrans1042)
		argvalue1 := adinfo.NewAdCreative()
		err1046 := argvalue1.Read(jsProt1045)
		if err1046 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.AdminMarkAdCreativeActionType(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdUserExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdUserExtInfo requires 2 args")
			flag.Usage()
		}
		arg1047 := flag.Arg(1)
		mbTrans1048 := thrift.NewTMemoryBufferLen(len(arg1047))
		defer mbTrans1048.Close()
		_, err1049 := mbTrans1048.WriteString(arg1047)
		if err1049 != nil {
			Usage()
			return
		}
		factory1050 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1051 := factory1050.GetProtocol(mbTrans1048)
		argvalue0 := adinfo.NewRequestHeader()
		err1052 := argvalue0.Read(jsProt1051)
		if err1052 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1053 := flag.Arg(2)
		mbTrans1054 := thrift.NewTMemoryBufferLen(len(arg1053))
		defer mbTrans1054.Close()
		_, err1055 := mbTrans1054.WriteString(arg1053)
		if err1055 != nil {
			Usage()
			return
		}
		factory1056 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1057 := factory1056.GetProtocol(mbTrans1054)
		argvalue1 := adinfo.NewAdUser()
		err1058 := argvalue1.Read(jsProt1057)
		if err1058 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdUser(argvalue1)
		fmt.Print(client.UpdateAdUserExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdUserExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdUserExtInfo requires 2 args")
			flag.Usage()
		}
		arg1059 := flag.Arg(1)
		mbTrans1060 := thrift.NewTMemoryBufferLen(len(arg1059))
		defer mbTrans1060.Close()
		_, err1061 := mbTrans1060.WriteString(arg1059)
		if err1061 != nil {
			Usage()
			return
		}
		factory1062 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1063 := factory1062.GetProtocol(mbTrans1060)
		argvalue0 := adinfo.NewRequestHeader()
		err1064 := argvalue0.Read(jsProt1063)
		if err1064 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1065 := flag.Arg(2)
		mbTrans1066 := thrift.NewTMemoryBufferLen(len(arg1065))
		defer mbTrans1066.Close()
		_, err1067 := mbTrans1066.WriteString(arg1065)
		if err1067 != nil {
			Usage()
			return
		}
		factory1068 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1069 := factory1068.GetProtocol(mbTrans1066)
		argvalue1 := adinfo.NewAdUser()
		err1070 := argvalue1.Read(jsProt1069)
		if err1070 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdUser(argvalue1)
		fmt.Print(client.DeleteAdUserExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdStrategyExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyExtInfo requires 2 args")
			flag.Usage()
		}
		arg1071 := flag.Arg(1)
		mbTrans1072 := thrift.NewTMemoryBufferLen(len(arg1071))
		defer mbTrans1072.Close()
		_, err1073 := mbTrans1072.WriteString(arg1071)
		if err1073 != nil {
			Usage()
			return
		}
		factory1074 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1075 := factory1074.GetProtocol(mbTrans1072)
		argvalue0 := adinfo.NewRequestHeader()
		err1076 := argvalue0.Read(jsProt1075)
		if err1076 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1077 := flag.Arg(2)
		mbTrans1078 := thrift.NewTMemoryBufferLen(len(arg1077))
		defer mbTrans1078.Close()
		_, err1079 := mbTrans1078.WriteString(arg1077)
		if err1079 != nil {
			Usage()
			return
		}
		factory1080 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1081 := factory1080.GetProtocol(mbTrans1078)
		argvalue1 := adinfo.NewAdStrategy()
		err1082 := argvalue1.Read(jsProt1081)
		if err1082 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdStrategyExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdStrategyExtInfo requires 2 args")
			flag.Usage()
		}
		arg1083 := flag.Arg(1)
		mbTrans1084 := thrift.NewTMemoryBufferLen(len(arg1083))
		defer mbTrans1084.Close()
		_, err1085 := mbTrans1084.WriteString(arg1083)
		if err1085 != nil {
			Usage()
			return
		}
		factory1086 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1087 := factory1086.GetProtocol(mbTrans1084)
		argvalue0 := adinfo.NewRequestHeader()
		err1088 := argvalue0.Read(jsProt1087)
		if err1088 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1089 := flag.Arg(2)
		mbTrans1090 := thrift.NewTMemoryBufferLen(len(arg1089))
		defer mbTrans1090.Close()
		_, err1091 := mbTrans1090.WriteString(arg1089)
		if err1091 != nil {
			Usage()
			return
		}
		factory1092 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1093 := factory1092.GetProtocol(mbTrans1090)
		argvalue1 := adinfo.NewAdStrategy()
		err1094 := argvalue1.Read(jsProt1093)
		if err1094 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.DeleteAdStrategyExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeExtInfo requires 2 args")
			flag.Usage()
		}
		arg1095 := flag.Arg(1)
		mbTrans1096 := thrift.NewTMemoryBufferLen(len(arg1095))
		defer mbTrans1096.Close()
		_, err1097 := mbTrans1096.WriteString(arg1095)
		if err1097 != nil {
			Usage()
			return
		}
		factory1098 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1099 := factory1098.GetProtocol(mbTrans1096)
		argvalue0 := adinfo.NewRequestHeader()
		err1100 := argvalue0.Read(jsProt1099)
		if err1100 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1101 := flag.Arg(2)
		mbTrans1102 := thrift.NewTMemoryBufferLen(len(arg1101))
		defer mbTrans1102.Close()
		_, err1103 := mbTrans1102.WriteString(arg1101)
		if err1103 != nil {
			Usage()
			return
		}
		factory1104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1105 := factory1104.GetProtocol(mbTrans1102)
		argvalue1 := adinfo.NewAdCreative()
		err1106 := argvalue1.Read(jsProt1105)
		if err1106 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.UpdateAdCreativeExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeUrlOpenType":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeUrlOpenType requires 2 args")
			flag.Usage()
		}
		arg1107 := flag.Arg(1)
		mbTrans1108 := thrift.NewTMemoryBufferLen(len(arg1107))
		defer mbTrans1108.Close()
		_, err1109 := mbTrans1108.WriteString(arg1107)
		if err1109 != nil {
			Usage()
			return
		}
		factory1110 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1111 := factory1110.GetProtocol(mbTrans1108)
		argvalue0 := adinfo.NewRequestHeader()
		err1112 := argvalue0.Read(jsProt1111)
		if err1112 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1113 := flag.Arg(2)
		mbTrans1114 := thrift.NewTMemoryBufferLen(len(arg1113))
		defer mbTrans1114.Close()
		_, err1115 := mbTrans1114.WriteString(arg1113)
		if err1115 != nil {
			Usage()
			return
		}
		factory1116 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1117 := factory1116.GetProtocol(mbTrans1114)
		argvalue1 := adinfo.NewAdCreative()
		err1118 := argvalue1.Read(jsProt1117)
		if err1118 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.UpdateAdCreativeUrlOpenType(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeRequiredCapability":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeRequiredCapability requires 2 args")
			flag.Usage()
		}
		arg1119 := flag.Arg(1)
		mbTrans1120 := thrift.NewTMemoryBufferLen(len(arg1119))
		defer mbTrans1120.Close()
		_, err1121 := mbTrans1120.WriteString(arg1119)
		if err1121 != nil {
			Usage()
			return
		}
		factory1122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1123 := factory1122.GetProtocol(mbTrans1120)
		argvalue0 := adinfo.NewRequestHeader()
		err1124 := argvalue0.Read(jsProt1123)
		if err1124 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1125 := flag.Arg(2)
		mbTrans1126 := thrift.NewTMemoryBufferLen(len(arg1125))
		defer mbTrans1126.Close()
		_, err1127 := mbTrans1126.WriteString(arg1125)
		if err1127 != nil {
			Usage()
			return
		}
		factory1128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1129 := factory1128.GetProtocol(mbTrans1126)
		argvalue1 := adinfo.NewAdCreative()
		err1130 := argvalue1.Read(jsProt1129)
		if err1130 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.UpdateAdCreativeRequiredCapability(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativePackageInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativePackageInfo requires 2 args")
			flag.Usage()
		}
		arg1131 := flag.Arg(1)
		mbTrans1132 := thrift.NewTMemoryBufferLen(len(arg1131))
		defer mbTrans1132.Close()
		_, err1133 := mbTrans1132.WriteString(arg1131)
		if err1133 != nil {
			Usage()
			return
		}
		factory1134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1135 := factory1134.GetProtocol(mbTrans1132)
		argvalue0 := adinfo.NewRequestHeader()
		err1136 := argvalue0.Read(jsProt1135)
		if err1136 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1137 := flag.Arg(2)
		mbTrans1138 := thrift.NewTMemoryBufferLen(len(arg1137))
		defer mbTrans1138.Close()
		_, err1139 := mbTrans1138.WriteString(arg1137)
		if err1139 != nil {
			Usage()
			return
		}
		factory1140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1141 := factory1140.GetProtocol(mbTrans1138)
		argvalue1 := adinfo.NewAdCreative()
		err1142 := argvalue1.Read(jsProt1141)
		if err1142 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.UpdateAdCreativePackageInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeMinRequiredSDKVersion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeMinRequiredSDKVersion requires 2 args")
			flag.Usage()
		}
		arg1143 := flag.Arg(1)
		mbTrans1144 := thrift.NewTMemoryBufferLen(len(arg1143))
		defer mbTrans1144.Close()
		_, err1145 := mbTrans1144.WriteString(arg1143)
		if err1145 != nil {
			Usage()
			return
		}
		factory1146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1147 := factory1146.GetProtocol(mbTrans1144)
		argvalue0 := adinfo.NewRequestHeader()
		err1148 := argvalue0.Read(jsProt1147)
		if err1148 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1149 := flag.Arg(2)
		mbTrans1150 := thrift.NewTMemoryBufferLen(len(arg1149))
		defer mbTrans1150.Close()
		_, err1151 := mbTrans1150.WriteString(arg1149)
		if err1151 != nil {
			Usage()
			return
		}
		factory1152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1153 := factory1152.GetProtocol(mbTrans1150)
		argvalue1 := adinfo.NewAdCreative()
		err1154 := argvalue1.Read(jsProt1153)
		if err1154 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.UpdateAdCreativeMinRequiredSDKVersion(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeMaterialTexts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeMaterialTexts requires 2 args")
			flag.Usage()
		}
		arg1155 := flag.Arg(1)
		mbTrans1156 := thrift.NewTMemoryBufferLen(len(arg1155))
		defer mbTrans1156.Close()
		_, err1157 := mbTrans1156.WriteString(arg1155)
		if err1157 != nil {
			Usage()
			return
		}
		factory1158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1159 := factory1158.GetProtocol(mbTrans1156)
		argvalue0 := adinfo.NewRequestHeader()
		err1160 := argvalue0.Read(jsProt1159)
		if err1160 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1161 := flag.Arg(2)
		mbTrans1162 := thrift.NewTMemoryBufferLen(len(arg1161))
		defer mbTrans1162.Close()
		_, err1163 := mbTrans1162.WriteString(arg1161)
		if err1163 != nil {
			Usage()
			return
		}
		factory1164 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1165 := factory1164.GetProtocol(mbTrans1162)
		containerStruct1 := adinfo.NewUpdateAdCreativeMaterialTextsArgs()
		err1166 := containerStruct1.ReadField2(jsProt1165)
		if err1166 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdCreatives
		value1 := argvalue1
		fmt.Print(client.UpdateAdCreativeMaterialTexts(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeMediaBid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeMediaBid requires 2 args")
			flag.Usage()
		}
		arg1167 := flag.Arg(1)
		mbTrans1168 := thrift.NewTMemoryBufferLen(len(arg1167))
		defer mbTrans1168.Close()
		_, err1169 := mbTrans1168.WriteString(arg1167)
		if err1169 != nil {
			Usage()
			return
		}
		factory1170 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1171 := factory1170.GetProtocol(mbTrans1168)
		argvalue0 := adinfo.NewRequestHeader()
		err1172 := argvalue0.Read(jsProt1171)
		if err1172 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1173 := flag.Arg(2)
		mbTrans1174 := thrift.NewTMemoryBufferLen(len(arg1173))
		defer mbTrans1174.Close()
		_, err1175 := mbTrans1174.WriteString(arg1173)
		if err1175 != nil {
			Usage()
			return
		}
		factory1176 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1177 := factory1176.GetProtocol(mbTrans1174)
		containerStruct1 := adinfo.NewUpdateAdCreativeMediaBidArgs()
		err1178 := containerStruct1.ReadField2(jsProt1177)
		if err1178 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdCreatives
		value1 := argvalue1
		fmt.Print(client.UpdateAdCreativeMediaBid(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdCreativeBid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdCreativeBid requires 2 args")
			flag.Usage()
		}
		arg1179 := flag.Arg(1)
		mbTrans1180 := thrift.NewTMemoryBufferLen(len(arg1179))
		defer mbTrans1180.Close()
		_, err1181 := mbTrans1180.WriteString(arg1179)
		if err1181 != nil {
			Usage()
			return
		}
		factory1182 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1183 := factory1182.GetProtocol(mbTrans1180)
		argvalue0 := adinfo.NewRequestHeader()
		err1184 := argvalue0.Read(jsProt1183)
		if err1184 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1185 := flag.Arg(2)
		mbTrans1186 := thrift.NewTMemoryBufferLen(len(arg1185))
		defer mbTrans1186.Close()
		_, err1187 := mbTrans1186.WriteString(arg1185)
		if err1187 != nil {
			Usage()
			return
		}
		factory1188 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1189 := factory1188.GetProtocol(mbTrans1186)
		argvalue1 := adinfo.NewAdCreative()
		err1190 := argvalue1.Read(jsProt1189)
		if err1190 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdCreative(argvalue1)
		fmt.Print(client.UpdateAdCreativeBid(value0, value1))
		fmt.Print("\n")
		break
	case "extendAdPlanDailyBudget":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ExtendAdPlanDailyBudget requires 5 args")
			flag.Usage()
		}
		arg1191 := flag.Arg(1)
		mbTrans1192 := thrift.NewTMemoryBufferLen(len(arg1191))
		defer mbTrans1192.Close()
		_, err1193 := mbTrans1192.WriteString(arg1191)
		if err1193 != nil {
			Usage()
			return
		}
		factory1194 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1195 := factory1194.GetProtocol(mbTrans1192)
		argvalue0 := adinfo.NewRequestHeader()
		err1196 := argvalue0.Read(jsProt1195)
		if err1196 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1197 := (strconv.Atoi(flag.Arg(2)))
		if err1197 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err1198 := (strconv.Atoi(flag.Arg(3)))
		if err1198 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		argvalue3, err1199 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1199 != nil {
			Usage()
			return
		}
		value3 := adinfo.CurrencyAmount(argvalue3)
		argvalue4, err1200 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1200 != nil {
			Usage()
			return
		}
		value4 := adinfo.CurrencyAmount(argvalue4)
		fmt.Print(client.ExtendAdPlanDailyBudget(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "updateAdPlanTimeSlot":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateAdPlanTimeSlot requires 4 args")
			flag.Usage()
		}
		arg1201 := flag.Arg(1)
		mbTrans1202 := thrift.NewTMemoryBufferLen(len(arg1201))
		defer mbTrans1202.Close()
		_, err1203 := mbTrans1202.WriteString(arg1201)
		if err1203 != nil {
			Usage()
			return
		}
		factory1204 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1205 := factory1204.GetProtocol(mbTrans1202)
		argvalue0 := adinfo.NewRequestHeader()
		err1206 := argvalue0.Read(jsProt1205)
		if err1206 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1207 := (strconv.Atoi(flag.Arg(2)))
		if err1207 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err1208 := (strconv.Atoi(flag.Arg(3)))
		if err1208 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		arg1209 := flag.Arg(4)
		mbTrans1210 := thrift.NewTMemoryBufferLen(len(arg1209))
		defer mbTrans1210.Close()
		_, err1211 := mbTrans1210.WriteString(arg1209)
		if err1211 != nil {
			Usage()
			return
		}
		factory1212 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1213 := factory1212.GetProtocol(mbTrans1210)
		containerStruct3 := adinfo.NewUpdateAdPlanTimeSlotArgs()
		err1214 := containerStruct3.ReadField4(jsProt1213)
		if err1214 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.TimeSlot
		value3 := argvalue3
		fmt.Print(client.UpdateAdPlanTimeSlot(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateAdStrategyDemoTagTarget":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyDemoTagTarget requires 2 args")
			flag.Usage()
		}
		arg1215 := flag.Arg(1)
		mbTrans1216 := thrift.NewTMemoryBufferLen(len(arg1215))
		defer mbTrans1216.Close()
		_, err1217 := mbTrans1216.WriteString(arg1215)
		if err1217 != nil {
			Usage()
			return
		}
		factory1218 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1219 := factory1218.GetProtocol(mbTrans1216)
		argvalue0 := adinfo.NewRequestHeader()
		err1220 := argvalue0.Read(jsProt1219)
		if err1220 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1221 := flag.Arg(2)
		mbTrans1222 := thrift.NewTMemoryBufferLen(len(arg1221))
		defer mbTrans1222.Close()
		_, err1223 := mbTrans1222.WriteString(arg1221)
		if err1223 != nil {
			Usage()
			return
		}
		factory1224 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1225 := factory1224.GetProtocol(mbTrans1222)
		argvalue1 := adinfo.NewAdStrategy()
		err1226 := argvalue1.Read(jsProt1225)
		if err1226 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyDemoTagTarget(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdStrategyMediaTagTarget":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyMediaTagTarget requires 2 args")
			flag.Usage()
		}
		arg1227 := flag.Arg(1)
		mbTrans1228 := thrift.NewTMemoryBufferLen(len(arg1227))
		defer mbTrans1228.Close()
		_, err1229 := mbTrans1228.WriteString(arg1227)
		if err1229 != nil {
			Usage()
			return
		}
		factory1230 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1231 := factory1230.GetProtocol(mbTrans1228)
		argvalue0 := adinfo.NewRequestHeader()
		err1232 := argvalue0.Read(jsProt1231)
		if err1232 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1233 := flag.Arg(2)
		mbTrans1234 := thrift.NewTMemoryBufferLen(len(arg1233))
		defer mbTrans1234.Close()
		_, err1235 := mbTrans1234.WriteString(arg1233)
		if err1235 != nil {
			Usage()
			return
		}
		factory1236 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1237 := factory1236.GetProtocol(mbTrans1234)
		argvalue1 := adinfo.NewAdStrategy()
		err1238 := argvalue1.Read(jsProt1237)
		if err1238 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyMediaTagTarget(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdStrategyDemoTaggings":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyDemoTaggings requires 2 args")
			flag.Usage()
		}
		arg1239 := flag.Arg(1)
		mbTrans1240 := thrift.NewTMemoryBufferLen(len(arg1239))
		defer mbTrans1240.Close()
		_, err1241 := mbTrans1240.WriteString(arg1239)
		if err1241 != nil {
			Usage()
			return
		}
		factory1242 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1243 := factory1242.GetProtocol(mbTrans1240)
		argvalue0 := adinfo.NewRequestHeader()
		err1244 := argvalue0.Read(jsProt1243)
		if err1244 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1245 := flag.Arg(2)
		mbTrans1246 := thrift.NewTMemoryBufferLen(len(arg1245))
		defer mbTrans1246.Close()
		_, err1247 := mbTrans1246.WriteString(arg1245)
		if err1247 != nil {
			Usage()
			return
		}
		factory1248 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1249 := factory1248.GetProtocol(mbTrans1246)
		argvalue1 := adinfo.NewAdStrategy()
		err1250 := argvalue1.Read(jsProt1249)
		if err1250 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyDemoTaggings(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdStrategyDeviceGroupTarget":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyDeviceGroupTarget requires 2 args")
			flag.Usage()
		}
		arg1251 := flag.Arg(1)
		mbTrans1252 := thrift.NewTMemoryBufferLen(len(arg1251))
		defer mbTrans1252.Close()
		_, err1253 := mbTrans1252.WriteString(arg1251)
		if err1253 != nil {
			Usage()
			return
		}
		factory1254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1255 := factory1254.GetProtocol(mbTrans1252)
		argvalue0 := adinfo.NewRequestHeader()
		err1256 := argvalue0.Read(jsProt1255)
		if err1256 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1257 := flag.Arg(2)
		mbTrans1258 := thrift.NewTMemoryBufferLen(len(arg1257))
		defer mbTrans1258.Close()
		_, err1259 := mbTrans1258.WriteString(arg1257)
		if err1259 != nil {
			Usage()
			return
		}
		factory1260 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1261 := factory1260.GetProtocol(mbTrans1258)
		argvalue1 := adinfo.NewAdStrategy()
		err1262 := argvalue1.Read(jsProt1261)
		if err1262 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyDeviceGroupTarget(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdStrategyPositionTarget":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyPositionTarget requires 2 args")
			flag.Usage()
		}
		arg1263 := flag.Arg(1)
		mbTrans1264 := thrift.NewTMemoryBufferLen(len(arg1263))
		defer mbTrans1264.Close()
		_, err1265 := mbTrans1264.WriteString(arg1263)
		if err1265 != nil {
			Usage()
			return
		}
		factory1266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1267 := factory1266.GetProtocol(mbTrans1264)
		argvalue0 := adinfo.NewRequestHeader()
		err1268 := argvalue0.Read(jsProt1267)
		if err1268 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1269 := flag.Arg(2)
		mbTrans1270 := thrift.NewTMemoryBufferLen(len(arg1269))
		defer mbTrans1270.Close()
		_, err1271 := mbTrans1270.WriteString(arg1269)
		if err1271 != nil {
			Usage()
			return
		}
		factory1272 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1273 := factory1272.GetProtocol(mbTrans1270)
		argvalue1 := adinfo.NewAdStrategy()
		err1274 := argvalue1.Read(jsProt1273)
		if err1274 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyPositionTarget(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdPlanDailyBudget":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "UpdateAdPlanDailyBudget requires 6 args")
			flag.Usage()
		}
		arg1275 := flag.Arg(1)
		mbTrans1276 := thrift.NewTMemoryBufferLen(len(arg1275))
		defer mbTrans1276.Close()
		_, err1277 := mbTrans1276.WriteString(arg1275)
		if err1277 != nil {
			Usage()
			return
		}
		factory1278 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1279 := factory1278.GetProtocol(mbTrans1276)
		argvalue0 := adinfo.NewRequestHeader()
		err1280 := argvalue0.Read(jsProt1279)
		if err1280 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1281 := (strconv.Atoi(flag.Arg(2)))
		if err1281 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err1282 := (strconv.Atoi(flag.Arg(3)))
		if err1282 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		argvalue3, err1283 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1283 != nil {
			Usage()
			return
		}
		value3 := adinfo.CurrencyAmount(argvalue3)
		argvalue4, err1284 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1284 != nil {
			Usage()
			return
		}
		value4 := adinfo.CurrencyAmount(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := adinfo.DateType(tmp5)
		value5 := adinfo.DateType(argvalue5)
		fmt.Print(client.UpdateAdPlanDailyBudget(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "updateAdStrategyDailyBudget":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyDailyBudget requires 8 args")
			flag.Usage()
		}
		arg1285 := flag.Arg(1)
		mbTrans1286 := thrift.NewTMemoryBufferLen(len(arg1285))
		defer mbTrans1286.Close()
		_, err1287 := mbTrans1286.WriteString(arg1285)
		if err1287 != nil {
			Usage()
			return
		}
		factory1288 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1289 := factory1288.GetProtocol(mbTrans1286)
		argvalue0 := adinfo.NewRequestHeader()
		err1290 := argvalue0.Read(jsProt1289)
		if err1290 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1291 := (strconv.Atoi(flag.Arg(2)))
		if err1291 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err1292 := (strconv.Atoi(flag.Arg(3)))
		if err1292 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := adinfo.AdPlanIdInt(argvalue2)
		tmp3, err1293 := (strconv.Atoi(flag.Arg(4)))
		if err1293 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdStrategyIdInt(argvalue3)
		argvalue4, err1294 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1294 != nil {
			Usage()
			return
		}
		value4 := adinfo.CurrencyAmount(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		argvalue6, err1296 := (strconv.ParseInt(flag.Arg(7), 10, 64))
		if err1296 != nil {
			Usage()
			return
		}
		value6 := adinfo.CurrencyAmount(argvalue6)
		tmp7, err := (strconv.Atoi(flag.Arg(8)))
		if err != nil {
			Usage()
			return
		}
		argvalue7 := adinfo.DateType(tmp7)
		value7 := adinfo.DateType(argvalue7)
		fmt.Print(client.UpdateAdStrategyDailyBudget(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getAdUsersByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdUsersByUids requires 2 args")
			flag.Usage()
		}
		arg1297 := flag.Arg(1)
		mbTrans1298 := thrift.NewTMemoryBufferLen(len(arg1297))
		defer mbTrans1298.Close()
		_, err1299 := mbTrans1298.WriteString(arg1297)
		if err1299 != nil {
			Usage()
			return
		}
		factory1300 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1301 := factory1300.GetProtocol(mbTrans1298)
		argvalue0 := adinfo.NewRequestHeader()
		err1302 := argvalue0.Read(jsProt1301)
		if err1302 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1303 := flag.Arg(2)
		mbTrans1304 := thrift.NewTMemoryBufferLen(len(arg1303))
		defer mbTrans1304.Close()
		_, err1305 := mbTrans1304.WriteString(arg1303)
		if err1305 != nil {
			Usage()
			return
		}
		factory1306 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1307 := factory1306.GetProtocol(mbTrans1304)
		containerStruct1 := adinfo.NewGetAdUsersByUidsArgs()
		err1308 := containerStruct1.ReadField2(jsProt1307)
		if err1308 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetAdUsersByUids(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdStrategyAdLevel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdStrategyAdLevel requires 2 args")
			flag.Usage()
		}
		arg1309 := flag.Arg(1)
		mbTrans1310 := thrift.NewTMemoryBufferLen(len(arg1309))
		defer mbTrans1310.Close()
		_, err1311 := mbTrans1310.WriteString(arg1309)
		if err1311 != nil {
			Usage()
			return
		}
		factory1312 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1313 := factory1312.GetProtocol(mbTrans1310)
		argvalue0 := adinfo.NewRequestHeader()
		err1314 := argvalue0.Read(jsProt1313)
		if err1314 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1315 := flag.Arg(2)
		mbTrans1316 := thrift.NewTMemoryBufferLen(len(arg1315))
		defer mbTrans1316.Close()
		_, err1317 := mbTrans1316.WriteString(arg1315)
		if err1317 != nil {
			Usage()
			return
		}
		factory1318 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1319 := factory1318.GetProtocol(mbTrans1316)
		argvalue1 := adinfo.NewAdStrategy()
		err1320 := argvalue1.Read(jsProt1319)
		if err1320 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdStrategy(argvalue1)
		fmt.Print(client.UpdateAdStrategyAdLevel(value0, value1))
		fmt.Print("\n")
		break
	case "getBriefStatusByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetBriefStatusByUids requires 2 args")
			flag.Usage()
		}
		arg1321 := flag.Arg(1)
		mbTrans1322 := thrift.NewTMemoryBufferLen(len(arg1321))
		defer mbTrans1322.Close()
		_, err1323 := mbTrans1322.WriteString(arg1321)
		if err1323 != nil {
			Usage()
			return
		}
		factory1324 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1325 := factory1324.GetProtocol(mbTrans1322)
		argvalue0 := adinfo.NewRequestHeader()
		err1326 := argvalue0.Read(jsProt1325)
		if err1326 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1327 := flag.Arg(2)
		mbTrans1328 := thrift.NewTMemoryBufferLen(len(arg1327))
		defer mbTrans1328.Close()
		_, err1329 := mbTrans1328.WriteString(arg1327)
		if err1329 != nil {
			Usage()
			return
		}
		factory1330 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1331 := factory1330.GetProtocol(mbTrans1328)
		containerStruct1 := adinfo.NewGetBriefStatusByUidsArgs()
		err1332 := containerStruct1.ReadField2(jsProt1331)
		if err1332 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetBriefStatusByUids(value0, value1))
		fmt.Print("\n")
		break
	case "editAdPlanBudgetSchedule":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdPlanBudgetSchedule requires 2 args")
			flag.Usage()
		}
		arg1333 := flag.Arg(1)
		mbTrans1334 := thrift.NewTMemoryBufferLen(len(arg1333))
		defer mbTrans1334.Close()
		_, err1335 := mbTrans1334.WriteString(arg1333)
		if err1335 != nil {
			Usage()
			return
		}
		factory1336 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1337 := factory1336.GetProtocol(mbTrans1334)
		argvalue0 := adinfo.NewRequestHeader()
		err1338 := argvalue0.Read(jsProt1337)
		if err1338 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1339 := flag.Arg(2)
		mbTrans1340 := thrift.NewTMemoryBufferLen(len(arg1339))
		defer mbTrans1340.Close()
		_, err1341 := mbTrans1340.WriteString(arg1339)
		if err1341 != nil {
			Usage()
			return
		}
		factory1342 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1343 := factory1342.GetProtocol(mbTrans1340)
		argvalue1 := adinfo.NewAdPlan()
		err1344 := argvalue1.Read(jsProt1343)
		if err1344 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdPlan(argvalue1)
		fmt.Print(client.EditAdPlanBudgetSchedule(value0, value1))
		fmt.Print("\n")
		break
	case "changeAdInfoExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ChangeAdInfoExtInfo requires 2 args")
			flag.Usage()
		}
		arg1345 := flag.Arg(1)
		mbTrans1346 := thrift.NewTMemoryBufferLen(len(arg1345))
		defer mbTrans1346.Close()
		_, err1347 := mbTrans1346.WriteString(arg1345)
		if err1347 != nil {
			Usage()
			return
		}
		factory1348 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1349 := factory1348.GetProtocol(mbTrans1346)
		argvalue0 := adinfo.NewRequestHeader()
		err1350 := argvalue0.Read(jsProt1349)
		if err1350 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1351 := flag.Arg(2)
		mbTrans1352 := thrift.NewTMemoryBufferLen(len(arg1351))
		defer mbTrans1352.Close()
		_, err1353 := mbTrans1352.WriteString(arg1351)
		if err1353 != nil {
			Usage()
			return
		}
		factory1354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1355 := factory1354.GetProtocol(mbTrans1352)
		argvalue1 := adinfo.NewAdInfoExtInfo()
		err1356 := argvalue1.Read(jsProt1355)
		if err1356 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdInfoExtInfo(argvalue1)
		fmt.Print(client.ChangeAdInfoExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdPlanExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdPlanExtInfo requires 2 args")
			flag.Usage()
		}
		arg1357 := flag.Arg(1)
		mbTrans1358 := thrift.NewTMemoryBufferLen(len(arg1357))
		defer mbTrans1358.Close()
		_, err1359 := mbTrans1358.WriteString(arg1357)
		if err1359 != nil {
			Usage()
			return
		}
		factory1360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1361 := factory1360.GetProtocol(mbTrans1358)
		argvalue0 := adinfo.NewRequestHeader()
		err1362 := argvalue0.Read(jsProt1361)
		if err1362 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1363 := flag.Arg(2)
		mbTrans1364 := thrift.NewTMemoryBufferLen(len(arg1363))
		defer mbTrans1364.Close()
		_, err1365 := mbTrans1364.WriteString(arg1363)
		if err1365 != nil {
			Usage()
			return
		}
		factory1366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1367 := factory1366.GetProtocol(mbTrans1364)
		argvalue1 := adinfo.NewAdPlan()
		err1368 := argvalue1.Read(jsProt1367)
		if err1368 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdPlan(argvalue1)
		fmt.Print(client.UpdateAdPlanExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdPlanExtInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdPlanExtInfo requires 2 args")
			flag.Usage()
		}
		arg1369 := flag.Arg(1)
		mbTrans1370 := thrift.NewTMemoryBufferLen(len(arg1369))
		defer mbTrans1370.Close()
		_, err1371 := mbTrans1370.WriteString(arg1369)
		if err1371 != nil {
			Usage()
			return
		}
		factory1372 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1373 := factory1372.GetProtocol(mbTrans1370)
		argvalue0 := adinfo.NewRequestHeader()
		err1374 := argvalue0.Read(jsProt1373)
		if err1374 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1375 := flag.Arg(2)
		mbTrans1376 := thrift.NewTMemoryBufferLen(len(arg1375))
		defer mbTrans1376.Close()
		_, err1377 := mbTrans1376.WriteString(arg1375)
		if err1377 != nil {
			Usage()
			return
		}
		factory1378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1379 := factory1378.GetProtocol(mbTrans1376)
		argvalue1 := adinfo.NewAdPlan()
		err1380 := argvalue1.Read(jsProt1379)
		if err1380 != nil {
			Usage()
			return
		}
		value1 := adinfo.AdPlan(argvalue1)
		fmt.Print(client.DeleteAdPlanExtInfo(value0, value1))
		fmt.Print("\n")
		break
	case "addAdResourceGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdResourceGroup requires 2 args")
			flag.Usage()
		}
		arg1381 := flag.Arg(1)
		mbTrans1382 := thrift.NewTMemoryBufferLen(len(arg1381))
		defer mbTrans1382.Close()
		_, err1383 := mbTrans1382.WriteString(arg1381)
		if err1383 != nil {
			Usage()
			return
		}
		factory1384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1385 := factory1384.GetProtocol(mbTrans1382)
		argvalue0 := adinfo.NewRequestHeader()
		err1386 := argvalue0.Read(jsProt1385)
		if err1386 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1387 := flag.Arg(2)
		mbTrans1388 := thrift.NewTMemoryBufferLen(len(arg1387))
		defer mbTrans1388.Close()
		_, err1389 := mbTrans1388.WriteString(arg1387)
		if err1389 != nil {
			Usage()
			return
		}
		factory1390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1391 := factory1390.GetProtocol(mbTrans1388)
		argvalue1 := adinfo.NewResourceGroup()
		err1392 := argvalue1.Read(jsProt1391)
		if err1392 != nil {
			Usage()
			return
		}
		value1 := adinfo.ResourceGroup(argvalue1)
		fmt.Print(client.AddAdResourceGroup(value0, value1))
		fmt.Print("\n")
		break
	case "addAdResource":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdResource requires 2 args")
			flag.Usage()
		}
		arg1393 := flag.Arg(1)
		mbTrans1394 := thrift.NewTMemoryBufferLen(len(arg1393))
		defer mbTrans1394.Close()
		_, err1395 := mbTrans1394.WriteString(arg1393)
		if err1395 != nil {
			Usage()
			return
		}
		factory1396 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1397 := factory1396.GetProtocol(mbTrans1394)
		argvalue0 := adinfo.NewRequestHeader()
		err1398 := argvalue0.Read(jsProt1397)
		if err1398 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1399 := flag.Arg(2)
		mbTrans1400 := thrift.NewTMemoryBufferLen(len(arg1399))
		defer mbTrans1400.Close()
		_, err1401 := mbTrans1400.WriteString(arg1399)
		if err1401 != nil {
			Usage()
			return
		}
		factory1402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1403 := factory1402.GetProtocol(mbTrans1400)
		argvalue1 := adinfo.NewResource()
		err1404 := argvalue1.Read(jsProt1403)
		if err1404 != nil {
			Usage()
			return
		}
		value1 := adinfo.Resource(argvalue1)
		fmt.Print(client.AddAdResource(value0, value1))
		fmt.Print("\n")
		break
	case "listAdPlansByUidAndCostTypeAndFuzzyName":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "ListAdPlansByUidAndCostTypeAndFuzzyName requires 8 args")
			flag.Usage()
		}
		arg1405 := flag.Arg(1)
		mbTrans1406 := thrift.NewTMemoryBufferLen(len(arg1405))
		defer mbTrans1406.Close()
		_, err1407 := mbTrans1406.WriteString(arg1405)
		if err1407 != nil {
			Usage()
			return
		}
		factory1408 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1409 := factory1408.GetProtocol(mbTrans1406)
		argvalue0 := adinfo.NewRequestHeader()
		err1410 := argvalue0.Read(jsProt1409)
		if err1410 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1411 := (strconv.Atoi(flag.Arg(2)))
		if err1411 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := adinfo.CostType(tmp2)
		value2 := adinfo.CostType(argvalue2)
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		tmp5, err1414 := (strconv.Atoi(flag.Arg(6)))
		if err1414 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := adinfo.AdQueryInt(argvalue5)
		tmp6, err1415 := (strconv.Atoi(flag.Arg(7)))
		if err1415 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := adinfo.AdQueryInt(argvalue6)
		argvalue7 := flag.Arg(8) == "true"
		value7 := argvalue7
		fmt.Print(client.ListAdPlansByUidAndCostTypeAndFuzzyName(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "listAdPlansByUidAndFuzzyName":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListAdPlansByUidAndFuzzyName requires 7 args")
			flag.Usage()
		}
		arg1417 := flag.Arg(1)
		mbTrans1418 := thrift.NewTMemoryBufferLen(len(arg1417))
		defer mbTrans1418.Close()
		_, err1419 := mbTrans1418.WriteString(arg1417)
		if err1419 != nil {
			Usage()
			return
		}
		factory1420 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1421 := factory1420.GetProtocol(mbTrans1418)
		argvalue0 := adinfo.NewRequestHeader()
		err1422 := argvalue0.Read(jsProt1421)
		if err1422 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1423 := (strconv.Atoi(flag.Arg(2)))
		if err1423 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		tmp4, err1426 := (strconv.Atoi(flag.Arg(5)))
		if err1426 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		tmp5, err1427 := (strconv.Atoi(flag.Arg(6)))
		if err1427 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := adinfo.AdQueryInt(argvalue5)
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListAdPlansByUidAndFuzzyName(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listAppIdsByUids":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAppIdsByUids requires 6 args")
			flag.Usage()
		}
		arg1429 := flag.Arg(1)
		mbTrans1430 := thrift.NewTMemoryBufferLen(len(arg1429))
		defer mbTrans1430.Close()
		_, err1431 := mbTrans1430.WriteString(arg1429)
		if err1431 != nil {
			Usage()
			return
		}
		factory1432 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1433 := factory1432.GetProtocol(mbTrans1430)
		argvalue0 := adinfo.NewRequestHeader()
		err1434 := argvalue0.Read(jsProt1433)
		if err1434 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1435 := flag.Arg(2)
		mbTrans1436 := thrift.NewTMemoryBufferLen(len(arg1435))
		defer mbTrans1436.Close()
		_, err1437 := mbTrans1436.WriteString(arg1435)
		if err1437 != nil {
			Usage()
			return
		}
		factory1438 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1439 := factory1438.GetProtocol(mbTrans1436)
		containerStruct1 := adinfo.NewListAppIdsByUidsArgs()
		err1440 := containerStruct1.ReadField2(jsProt1439)
		if err1440 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err1442 := (strconv.Atoi(flag.Arg(4)))
		if err1442 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.AdQueryInt(argvalue3)
		tmp4, err1443 := (strconv.Atoi(flag.Arg(5)))
		if err1443 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.AdQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAppIdsByUids(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listAdInfoIdsByAppIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAdInfoIdsByAppIds requires 4 args")
			flag.Usage()
		}
		arg1445 := flag.Arg(1)
		mbTrans1446 := thrift.NewTMemoryBufferLen(len(arg1445))
		defer mbTrans1446.Close()
		_, err1447 := mbTrans1446.WriteString(arg1445)
		if err1447 != nil {
			Usage()
			return
		}
		factory1448 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1449 := factory1448.GetProtocol(mbTrans1446)
		argvalue0 := adinfo.NewRequestHeader()
		err1450 := argvalue0.Read(jsProt1449)
		if err1450 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1451 := flag.Arg(2)
		mbTrans1452 := thrift.NewTMemoryBufferLen(len(arg1451))
		defer mbTrans1452.Close()
		_, err1453 := mbTrans1452.WriteString(arg1451)
		if err1453 != nil {
			Usage()
			return
		}
		factory1454 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1455 := factory1454.GetProtocol(mbTrans1452)
		containerStruct1 := adinfo.NewListAdInfoIdsByAppIdsArgs()
		err1456 := containerStruct1.ReadField2(jsProt1455)
		if err1456 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AppIds
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListAdInfoIdsByAppIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listAdInfoIdsByChnIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAdInfoIdsByChnIds requires 4 args")
			flag.Usage()
		}
		arg1459 := flag.Arg(1)
		mbTrans1460 := thrift.NewTMemoryBufferLen(len(arg1459))
		defer mbTrans1460.Close()
		_, err1461 := mbTrans1460.WriteString(arg1459)
		if err1461 != nil {
			Usage()
			return
		}
		factory1462 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1463 := factory1462.GetProtocol(mbTrans1460)
		argvalue0 := adinfo.NewRequestHeader()
		err1464 := argvalue0.Read(jsProt1463)
		if err1464 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		arg1465 := flag.Arg(2)
		mbTrans1466 := thrift.NewTMemoryBufferLen(len(arg1465))
		defer mbTrans1466.Close()
		_, err1467 := mbTrans1466.WriteString(arg1465)
		if err1467 != nil {
			Usage()
			return
		}
		factory1468 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1469 := factory1468.GetProtocol(mbTrans1466)
		containerStruct1 := adinfo.NewListAdInfoIdsByChnIdsArgs()
		err1470 := containerStruct1.ReadField2(jsProt1469)
		if err1470 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ChnIds
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListAdInfoIdsByChnIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "bindAppAndChannelByPids":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "BindAppAndChannelByPids requires 5 args")
			flag.Usage()
		}
		arg1473 := flag.Arg(1)
		mbTrans1474 := thrift.NewTMemoryBufferLen(len(arg1473))
		defer mbTrans1474.Close()
		_, err1475 := mbTrans1474.WriteString(arg1473)
		if err1475 != nil {
			Usage()
			return
		}
		factory1476 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1477 := factory1476.GetProtocol(mbTrans1474)
		argvalue0 := adinfo.NewRequestHeader()
		err1478 := argvalue0.Read(jsProt1477)
		if err1478 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1479 := (strconv.Atoi(flag.Arg(2)))
		if err1479 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.UidInt(argvalue1)
		arg1480 := flag.Arg(3)
		mbTrans1481 := thrift.NewTMemoryBufferLen(len(arg1480))
		defer mbTrans1481.Close()
		_, err1482 := mbTrans1481.WriteString(arg1480)
		if err1482 != nil {
			Usage()
			return
		}
		factory1483 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1484 := factory1483.GetProtocol(mbTrans1481)
		containerStruct2 := adinfo.NewBindAppAndChannelByPidsArgs()
		err1485 := containerStruct2.ReadField3(jsProt1484)
		if err1485 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		tmp3, err1486 := (strconv.Atoi(flag.Arg(4)))
		if err1486 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := adinfo.IdInt(argvalue3)
		tmp4, err1487 := (strconv.Atoi(flag.Arg(5)))
		if err1487 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := adinfo.IdInt(argvalue4)
		fmt.Print(client.BindAppAndChannelByPids(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getBriefAdCreativesByPid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetBriefAdCreativesByPid requires 2 args")
			flag.Usage()
		}
		arg1488 := flag.Arg(1)
		mbTrans1489 := thrift.NewTMemoryBufferLen(len(arg1488))
		defer mbTrans1489.Close()
		_, err1490 := mbTrans1489.WriteString(arg1488)
		if err1490 != nil {
			Usage()
			return
		}
		factory1491 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1492 := factory1491.GetProtocol(mbTrans1489)
		argvalue0 := adinfo.NewRequestHeader()
		err1493 := argvalue0.Read(jsProt1492)
		if err1493 != nil {
			Usage()
			return
		}
		value0 := adinfo.RequestHeader(argvalue0)
		tmp1, err1494 := (strconv.Atoi(flag.Arg(2)))
		if err1494 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := adinfo.IdInt(argvalue1)
		fmt.Print(client.GetBriefAdCreativesByPid(value0, value1))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err1499 := (strconv.Atoi(flag.Arg(1)))
		if err1499 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
