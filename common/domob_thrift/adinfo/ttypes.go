// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adinfo

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = adinfo_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

//AdInfoException中可能出现的异常代码
type AdInfoServiceCode int64

const (
	AdInfoServiceCode_ERROR_AD_USER_NOT_EXIST        AdInfoServiceCode = 21301
	AdInfoServiceCode_ERROR_AD_USER_ADER_ROLE_BANNED AdInfoServiceCode = 21302
	AdInfoServiceCode_ERROR_AD_USER_NOT_MATCH        AdInfoServiceCode = 21303
	AdInfoServiceCode_ERROR_AD_PLAN_NOT_EXIST        AdInfoServiceCode = 21310
	AdInfoServiceCode_ERROR_AD_PLAN_NOT_MATCH        AdInfoServiceCode = 21311
	AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_EXIST    AdInfoServiceCode = 21320
	AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_MATCH    AdInfoServiceCode = 21321
	AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_EXIST    AdInfoServiceCode = 21330
	AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_PAUSED   AdInfoServiceCode = 21331
	AdInfoServiceCode_ERROR_AD_BID_INCORRECT         AdInfoServiceCode = 21340
	AdInfoServiceCode_ERROR_AD_PARAM_INVALID         AdInfoServiceCode = 21401
	AdInfoServiceCode_ERROR_AD_SYSTEM_ERROR          AdInfoServiceCode = 21501
)

func (p AdInfoServiceCode) String() string {
	switch p {
	case AdInfoServiceCode_ERROR_AD_USER_NOT_EXIST:
		return "AdInfoServiceCode_ERROR_AD_USER_NOT_EXIST"
	case AdInfoServiceCode_ERROR_AD_USER_ADER_ROLE_BANNED:
		return "AdInfoServiceCode_ERROR_AD_USER_ADER_ROLE_BANNED"
	case AdInfoServiceCode_ERROR_AD_USER_NOT_MATCH:
		return "AdInfoServiceCode_ERROR_AD_USER_NOT_MATCH"
	case AdInfoServiceCode_ERROR_AD_PLAN_NOT_EXIST:
		return "AdInfoServiceCode_ERROR_AD_PLAN_NOT_EXIST"
	case AdInfoServiceCode_ERROR_AD_PLAN_NOT_MATCH:
		return "AdInfoServiceCode_ERROR_AD_PLAN_NOT_MATCH"
	case AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_EXIST:
		return "AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_EXIST"
	case AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_MATCH:
		return "AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_MATCH"
	case AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_EXIST:
		return "AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_EXIST"
	case AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_PAUSED:
		return "AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_PAUSED"
	case AdInfoServiceCode_ERROR_AD_BID_INCORRECT:
		return "AdInfoServiceCode_ERROR_AD_BID_INCORRECT"
	case AdInfoServiceCode_ERROR_AD_PARAM_INVALID:
		return "AdInfoServiceCode_ERROR_AD_PARAM_INVALID"
	case AdInfoServiceCode_ERROR_AD_SYSTEM_ERROR:
		return "AdInfoServiceCode_ERROR_AD_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func AdInfoServiceCodeFromString(s string) (AdInfoServiceCode, error) {
	switch s {
	case "AdInfoServiceCode_ERROR_AD_USER_NOT_EXIST":
		return AdInfoServiceCode_ERROR_AD_USER_NOT_EXIST, nil
	case "AdInfoServiceCode_ERROR_AD_USER_ADER_ROLE_BANNED":
		return AdInfoServiceCode_ERROR_AD_USER_ADER_ROLE_BANNED, nil
	case "AdInfoServiceCode_ERROR_AD_USER_NOT_MATCH":
		return AdInfoServiceCode_ERROR_AD_USER_NOT_MATCH, nil
	case "AdInfoServiceCode_ERROR_AD_PLAN_NOT_EXIST":
		return AdInfoServiceCode_ERROR_AD_PLAN_NOT_EXIST, nil
	case "AdInfoServiceCode_ERROR_AD_PLAN_NOT_MATCH":
		return AdInfoServiceCode_ERROR_AD_PLAN_NOT_MATCH, nil
	case "AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_EXIST":
		return AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_EXIST, nil
	case "AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_MATCH":
		return AdInfoServiceCode_ERROR_AD_STRATEGY_NOT_MATCH, nil
	case "AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_EXIST":
		return AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_EXIST, nil
	case "AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_PAUSED":
		return AdInfoServiceCode_ERROR_AD_CREATIVE_NOT_PAUSED, nil
	case "AdInfoServiceCode_ERROR_AD_BID_INCORRECT":
		return AdInfoServiceCode_ERROR_AD_BID_INCORRECT, nil
	case "AdInfoServiceCode_ERROR_AD_PARAM_INVALID":
		return AdInfoServiceCode_ERROR_AD_PARAM_INVALID, nil
	case "AdInfoServiceCode_ERROR_AD_SYSTEM_ERROR":
		return AdInfoServiceCode_ERROR_AD_SYSTEM_ERROR, nil
	}
	return AdInfoServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid AdInfoServiceCode string")
}

type UidInt adinfo_types.UidInt

type AdPlanIdInt adinfo_types.AdPlanIdInt

type AdStrategyIdInt adinfo_types.AdStrategyIdInt

type AdCreativeIdInt adinfo_types.AdCreativeIdInt

type TimeInt adinfo_types.TimeInt

type AdQueryResult *common.QueryResult

type AdQueryInt adinfo_types.AdQueryInt

type RequestHeader *common.RequestHeader

type AdStrategyType adinfo_types.AdStrategyType

type AdCreativeType adinfo_types.AdCreativeType

type AdActionType adinfo_types.AdActionType

type AdPlan *adinfo_types.AdPlan

type AdStrategy *adinfo_types.AdStrategy

type AdCreative *adinfo_types.AdCreative

type AdPlanStatus adinfo_types.AdPlanStatus

type AdStrategyStatus adinfo_types.AdStrategyStatus

type AdCreativeStatus adinfo_types.AdCreativeStatus

type AdPauseStatus adinfo_types.AdPauseStatus

type AdStatusLamp *adinfo_types.AdStatusLamp

type AdInfoPidResult *adinfo_types.AdInfoPidResult

type CurrencyAmount adinfo_types.CurrencyAmount

type DateType adinfo_types.DateType

type CostType adinfo_types.CostType

type AdWaterMarkStatus adinfo_types.AdWaterMarkStatus

type AdUser *adinfo_types.AdUser

type AdUserBriefStatus adinfo_types.AdUserBriefStatus

type AdInfoExtInfo *adinfo_types.AdInfoExtInfo

type AdResourceGroupId adinfo_types.AdResourceGroupId

type AdResourceId adinfo_types.AdResourceId

type AdAppIdInt adinfo_types.AdAppIdInt

type AdApp *adinfo_types.AdApp

type IdInt adinfo_types.IdInt

type ResourceGroup *common.ResourceGroup

type Resource *common.Resource

type AdInfoException struct {
	Code    AdInfoServiceCode `thrift:"code,1" json:"code"`
	Message string            `thrift:"message,2" json:"message"`
}

func NewAdInfoException() *AdInfoException {
	return &AdInfoException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdInfoException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *AdInfoException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdInfoException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = AdInfoServiceCode(v)
	}
	return nil
}

func (p *AdInfoException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *AdInfoException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdInfoException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdInfoException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *AdInfoException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdInfoException(%+v)", *p)
}
