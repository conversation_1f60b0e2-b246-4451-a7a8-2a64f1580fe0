// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type Market int64

const (
	Market_ALL      Market = 0
	Market_GFAN     Market = 1
	Market_GOAPK    Market = 2
	Market_APPCHINA Market = 3
	Market_EOE      Market = 4
	Market_NDUOA    Market = 5
	Market_MUMAYI   Market = 6
	Market_DCN      Market = 7
	Market_GOOGLE   Market = 8
	Market_HIAPK    Market = 9
	Market_DOMOB    Market = 10
	Market_MYAPP    Market = 11
)

func (p Market) String() string {
	switch p {
	case Market_ALL:
		return "Market_ALL"
	case Market_GFAN:
		return "Market_GFAN"
	case Market_GOAPK:
		return "Market_GOAPK"
	case Market_APPCHINA:
		return "Market_APPCHINA"
	case Market_EOE:
		return "Market_EOE"
	case Market_NDUOA:
		return "Market_NDUOA"
	case Market_MUMAYI:
		return "Market_MUMAYI"
	case Market_DCN:
		return "Market_DCN"
	case Market_GOOGLE:
		return "Market_GOOGLE"
	case Market_HIAPK:
		return "Market_HIAPK"
	case Market_DOMOB:
		return "Market_DOMOB"
	case Market_MYAPP:
		return "Market_MYAPP"
	}
	return "<UNSET>"
}

func MarketFromString(s string) (Market, error) {
	switch s {
	case "Market_ALL":
		return Market_ALL, nil
	case "Market_GFAN":
		return Market_GFAN, nil
	case "Market_GOAPK":
		return Market_GOAPK, nil
	case "Market_APPCHINA":
		return Market_APPCHINA, nil
	case "Market_EOE":
		return Market_EOE, nil
	case "Market_NDUOA":
		return Market_NDUOA, nil
	case "Market_MUMAYI":
		return Market_MUMAYI, nil
	case "Market_DCN":
		return Market_DCN, nil
	case "Market_GOOGLE":
		return Market_GOOGLE, nil
	case "Market_HIAPK":
		return Market_HIAPK, nil
	case "Market_DOMOB":
		return Market_DOMOB, nil
	case "Market_MYAPP":
		return Market_MYAPP, nil
	}
	return Market(math.MinInt32 - 1), fmt.Errorf("not a valid Market string")
}

type Category int64

const (
	Category_ALL       Category = 0
	Category_RELAX     Category = 1
	Category_ACTION    Category = 2
	Category_ONLINE    Category = 3
	Category_RPG       Category = 4
	Category_FLY_SHOOT Category = 5
	Category_RACE      Category = 6
	Category_CHESS     Category = 7
	Category_SPORTS    Category = 8
	Category_OTHER     Category = 9
)

func (p Category) String() string {
	switch p {
	case Category_ALL:
		return "Category_ALL"
	case Category_RELAX:
		return "Category_RELAX"
	case Category_ACTION:
		return "Category_ACTION"
	case Category_ONLINE:
		return "Category_ONLINE"
	case Category_RPG:
		return "Category_RPG"
	case Category_FLY_SHOOT:
		return "Category_FLY_SHOOT"
	case Category_RACE:
		return "Category_RACE"
	case Category_CHESS:
		return "Category_CHESS"
	case Category_SPORTS:
		return "Category_SPORTS"
	case Category_OTHER:
		return "Category_OTHER"
	}
	return "<UNSET>"
}

func CategoryFromString(s string) (Category, error) {
	switch s {
	case "Category_ALL":
		return Category_ALL, nil
	case "Category_RELAX":
		return Category_RELAX, nil
	case "Category_ACTION":
		return Category_ACTION, nil
	case "Category_ONLINE":
		return Category_ONLINE, nil
	case "Category_RPG":
		return Category_RPG, nil
	case "Category_FLY_SHOOT":
		return Category_FLY_SHOOT, nil
	case "Category_RACE":
		return Category_RACE, nil
	case "Category_CHESS":
		return Category_CHESS, nil
	case "Category_SPORTS":
		return Category_SPORTS, nil
	case "Category_OTHER":
		return Category_OTHER, nil
	}
	return Category(math.MinInt32 - 1), fmt.Errorf("not a valid Category string")
}

//一个资源只有一个类型，all用于查询，错误情况等
type ResourceType int64

const (
	ResourceType_ALL      ResourceType = 0
	ResourceType_APK      ResourceType = 1
	ResourceType_ICON     ResourceType = 2
	ResourceType_SNAPSHOT ResourceType = 3
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_ALL:
		return "ResourceType_ALL"
	case ResourceType_APK:
		return "ResourceType_APK"
	case ResourceType_ICON:
		return "ResourceType_ICON"
	case ResourceType_SNAPSHOT:
		return "ResourceType_SNAPSHOT"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "ResourceType_ALL":
		return ResourceType_ALL, nil
	case "ResourceType_APK":
		return ResourceType_APK, nil
	case "ResourceType_ICON":
		return ResourceType_ICON, nil
	case "ResourceType_SNAPSHOT":
		return ResourceType_SNAPSHOT, nil
	}
	return ResourceType(math.MinInt32 - 1), fmt.Errorf("not a valid ResourceType string")
}

//下载事件的类型
type DownloadCounterType int64

const (
	DownloadCounterType_DOWN_BEGIN DownloadCounterType = 1
	DownloadCounterType_DOWN_END   DownloadCounterType = 2
	DownloadCounterType_INSTALLED  DownloadCounterType = 3
)

func (p DownloadCounterType) String() string {
	switch p {
	case DownloadCounterType_DOWN_BEGIN:
		return "DownloadCounterType_DOWN_BEGIN"
	case DownloadCounterType_DOWN_END:
		return "DownloadCounterType_DOWN_END"
	case DownloadCounterType_INSTALLED:
		return "DownloadCounterType_INSTALLED"
	}
	return "<UNSET>"
}

func DownloadCounterTypeFromString(s string) (DownloadCounterType, error) {
	switch s {
	case "DownloadCounterType_DOWN_BEGIN":
		return DownloadCounterType_DOWN_BEGIN, nil
	case "DownloadCounterType_DOWN_END":
		return DownloadCounterType_DOWN_END, nil
	case "DownloadCounterType_INSTALLED":
		return DownloadCounterType_INSTALLED, nil
	}
	return DownloadCounterType(math.MinInt32 - 1), fmt.Errorf("not a valid DownloadCounterType string")
}

type BannerType int64

const (
	BannerType_BT_TOPIC       BannerType = 1
	BannerType_BT_DUOYOU_APP  BannerType = 2
	BannerType_BT_APPWALL_APP BannerType = 3
)

func (p BannerType) String() string {
	switch p {
	case BannerType_BT_TOPIC:
		return "BannerType_BT_TOPIC"
	case BannerType_BT_DUOYOU_APP:
		return "BannerType_BT_DUOYOU_APP"
	case BannerType_BT_APPWALL_APP:
		return "BannerType_BT_APPWALL_APP"
	}
	return "<UNSET>"
}

func BannerTypeFromString(s string) (BannerType, error) {
	switch s {
	case "BannerType_BT_TOPIC":
		return BannerType_BT_TOPIC, nil
	case "BannerType_BT_DUOYOU_APP":
		return BannerType_BT_DUOYOU_APP, nil
	case "BannerType_BT_APPWALL_APP":
		return BannerType_BT_APPWALL_APP, nil
	}
	return BannerType(math.MinInt32 - 1), fmt.Errorf("not a valid BannerType string")
}

type TimeInt common.TimeInt

type RequestHeader *common.RequestHeader

type AdCategory common.AdCategory

type AppWallProt map[string]string

type DeviceCode common.DeviceCode

type OSCode common.OSCode

type CarrierCode common.CarrierCode

type AccessTypeCode common.AccessTypeCode

type AppDownloadCounter struct {
	Today       int32 `thrift:"today,1" json:"today"`
	Yesterday   int32 `thrift:"yesterday,2" json:"yesterday"`
	Last_7days  int32 `thrift:"last_7days,3" json:"last_7days"`
	LastWeek    int32 `thrift:"last_week,4" json:"last_week"`
	Last_30days int32 `thrift:"last_30days,5" json:"last_30days"`
	LastMonth   int32 `thrift:"last_month,6" json:"last_month"`
	Total       int32 `thrift:"total,7" json:"total"`
	// unused field # 8
	// unused field # 9
	CounterType DownloadCounterType `thrift:"counter_type,10" json:"counter_type"`
}

func NewAppDownloadCounter() *AppDownloadCounter {
	return &AppDownloadCounter{
		CounterType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppDownloadCounter) IsSetCounterType() bool {
	return int64(p.CounterType) != math.MinInt32-1
}

func (p *AppDownloadCounter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppDownloadCounter) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Today = v
	}
	return nil
}

func (p *AppDownloadCounter) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Yesterday = v
	}
	return nil
}

func (p *AppDownloadCounter) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Last_7days = v
	}
	return nil
}

func (p *AppDownloadCounter) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.LastWeek = v
	}
	return nil
}

func (p *AppDownloadCounter) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Last_30days = v
	}
	return nil
}

func (p *AppDownloadCounter) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.LastMonth = v
	}
	return nil
}

func (p *AppDownloadCounter) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *AppDownloadCounter) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CounterType = DownloadCounterType(v)
	}
	return nil
}

func (p *AppDownloadCounter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppDownloadCounter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppDownloadCounter) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("today", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:today: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Today)); err != nil {
		return fmt.Errorf("%T.today (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:today: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("yesterday", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:yesterday: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Yesterday)); err != nil {
		return fmt.Errorf("%T.yesterday (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:yesterday: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_7days", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:last_7days: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Last_7days)); err != nil {
		return fmt.Errorf("%T.last_7days (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:last_7days: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_week", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:last_week: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastWeek)); err != nil {
		return fmt.Errorf("%T.last_week (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:last_week: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_30days", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:last_30days: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Last_30days)); err != nil {
		return fmt.Errorf("%T.last_30days (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:last_30days: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_month", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:last_month: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastMonth)); err != nil {
		return fmt.Errorf("%T.last_month (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:last_month: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:total: %s", p, err)
	}
	return err
}

func (p *AppDownloadCounter) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCounterType() {
		if err := oprot.WriteFieldBegin("counter_type", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:counter_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CounterType)); err != nil {
			return fmt.Errorf("%T.counter_type (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:counter_type: %s", p, err)
		}
	}
	return err
}

func (p *AppDownloadCounter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDownloadCounter(%+v)", *p)
}

type AppMain struct {
	Appid              int32                                       `thrift:"appid,1" json:"appid"`
	PackageName        string                                      `thrift:"package_name,2" json:"package_name"`
	Appname            string                                      `thrift:"appname,3" json:"appname"`
	Version            string                                      `thrift:"version,4" json:"version"`
	Author             string                                      `thrift:"author,5" json:"author"`
	Pubtime            TimeInt                                     `thrift:"pubtime,6" json:"pubtime"`
	Appsize            int32                                       `thrift:"appsize,7" json:"appsize"`
	Category           Category                                    `thrift:"category,8" json:"category"`
	DownloadCount      int32                                       `thrift:"download_count,9" json:"download_count"`
	Platform           string                                      `thrift:"platform,10" json:"platform"`
	Description        string                                      `thrift:"description,11" json:"description"`
	IconUrl            string                                      `thrift:"icon_url,12" json:"icon_url"`
	SnapshotUrls       []string                                    `thrift:"snapshot_urls,13" json:"snapshot_urls"`
	DownloadUrl        string                                      `thrift:"download_url,14" json:"download_url"`
	Rate               int32                                       `thrift:"rate,15" json:"rate"`
	AuthorId           string                                      `thrift:"author_id,16" json:"author_id"`
	Ads                string                                      `thrift:"ads,17" json:"ads"`
	Permission         string                                      `thrift:"permission,18" json:"permission"`
	CreateTime         TimeInt                                     `thrift:"create_time,19" json:"create_time"`
	UpdateTime         TimeInt                                     `thrift:"update_time,20" json:"update_time"`
	VersionCode        int32                                       `thrift:"version_code,21" json:"version_code"`
	SourceMarket       Market                                      `thrift:"source_market,22" json:"source_market"`
	SourceDomain       string                                      `thrift:"source_domain,23" json:"source_domain"`
	DetailUrl          string                                      `thrift:"detail_url,24" json:"detail_url"`
	SrcIconUrl         string                                      `thrift:"src_icon_url,25" json:"src_icon_url"`
	SrcSnapshotUrls    []string                                    `thrift:"src_snapshot_urls,26" json:"src_snapshot_urls"`
	SrcDownloadUrl     string                                      `thrift:"src_download_url,27" json:"src_download_url"`
	Rank               int32                                       `thrift:"rank,28" json:"rank"`
	Deleted            bool                                        `thrift:"deleted,29" json:"deleted"`
	Edited             bool                                        `thrift:"edited,30" json:"edited"`
	HasUpdate          bool                                        `thrift:"has_update,31" json:"has_update"`
	Lang               string                                      `thrift:"lang,32" json:"lang"`
	DomobDownloadCount int32                                       `thrift:"domob_download_count,33" json:"domob_download_count"`
	DomobRate          int32                                       `thrift:"domob_rate,34" json:"domob_rate"`
	ParentAppid        int32                                       `thrift:"parent_appid,35" json:"parent_appid"`
	IsNew              int32                                       `thrift:"is_new,36" json:"is_new"`
	ApkAppname         string                                      `thrift:"apk_appname,37" json:"apk_appname"`
	TargetCarrier      common.CarrierCode                          `thrift:"target_carrier,38" json:"target_carrier"`
	DownloadCounter    map[DownloadCounterType]*AppDownloadCounter `thrift:"download_counter,39" json:"download_counter"`
	AdUserId           int32                                       `thrift:"ad_user_id,40" json:"ad_user_id"`
	BriefIntro         string                                      `thrift:"brief_intro,41" json:"brief_intro"`
	ShortName          string                                      `thrift:"short_name,42" json:"short_name"`
	AppCategory        AdCategory                                  `thrift:"app_category,43" json:"app_category"`
	IsPaused           bool                                        `thrift:"is_paused,44" json:"is_paused"`
	Tag                string                                      `thrift:"tag,45" json:"tag"`
	TagCorner          string                                      `thrift:"tag_corner,46" json:"tag_corner"`
	TagOblique         string                                      `thrift:"tag_oblique,47" json:"tag_oblique"`
	ObtMsg             string                                      `thrift:"obt_msg,48" json:"obt_msg"`
	ReopeningMsg       string                                      `thrift:"reopening_msg,49" json:"reopening_msg"`
	OnlineStatus       string                                      `thrift:"online_status,50" json:"online_status"`
}

func NewAppMain() *AppMain {
	return &AppMain{
		Category: math.MinInt32 - 1, // unset sentinal value

		SourceMarket: math.MinInt32 - 1, // unset sentinal value

		TargetCarrier: math.MinInt32 - 1, // unset sentinal value

		AppCategory: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppMain) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *AppMain) IsSetSourceMarket() bool {
	return int64(p.SourceMarket) != math.MinInt32-1
}

func (p *AppMain) IsSetTargetCarrier() bool {
	return int64(p.TargetCarrier) != math.MinInt32-1
}

func (p *AppMain) IsSetAppCategory() bool {
	return int64(p.AppCategory) != math.MinInt32-1
}

func (p *AppMain) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.LIST {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.MAP {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I32 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.STRING {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.STRING {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.STRING {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.STRING {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppMain) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AppMain) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AppMain) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Appname = v
	}
	return nil
}

func (p *AppMain) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *AppMain) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Author = v
	}
	return nil
}

func (p *AppMain) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Pubtime = TimeInt(v)
	}
	return nil
}

func (p *AppMain) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Appsize = v
	}
	return nil
}

func (p *AppMain) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *AppMain) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.DownloadCount = v
	}
	return nil
}

func (p *AppMain) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *AppMain) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *AppMain) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.IconUrl = v
	}
	return nil
}

func (p *AppMain) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SnapshotUrls = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.SnapshotUrls = append(p.SnapshotUrls, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppMain) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DownloadUrl = v
	}
	return nil
}

func (p *AppMain) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Rate = v
	}
	return nil
}

func (p *AppMain) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.AuthorId = v
	}
	return nil
}

func (p *AppMain) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Ads = v
	}
	return nil
}

func (p *AppMain) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Permission = v
	}
	return nil
}

func (p *AppMain) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AppMain) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.UpdateTime = TimeInt(v)
	}
	return nil
}

func (p *AppMain) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *AppMain) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.SourceMarket = Market(v)
	}
	return nil
}

func (p *AppMain) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.SourceDomain = v
	}
	return nil
}

func (p *AppMain) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.DetailUrl = v
	}
	return nil
}

func (p *AppMain) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.SrcIconUrl = v
	}
	return nil
}

func (p *AppMain) readField26(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SrcSnapshotUrls = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.SrcSnapshotUrls = append(p.SrcSnapshotUrls, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppMain) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.SrcDownloadUrl = v
	}
	return nil
}

func (p *AppMain) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AppMain) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AppMain) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Edited = v
	}
	return nil
}

func (p *AppMain) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.HasUpdate = v
	}
	return nil
}

func (p *AppMain) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Lang = v
	}
	return nil
}

func (p *AppMain) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.DomobDownloadCount = v
	}
	return nil
}

func (p *AppMain) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.DomobRate = v
	}
	return nil
}

func (p *AppMain) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.ParentAppid = v
	}
	return nil
}

func (p *AppMain) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.IsNew = v
	}
	return nil
}

func (p *AppMain) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.ApkAppname = v
	}
	return nil
}

func (p *AppMain) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.TargetCarrier = common.CarrierCode(v)
	}
	return nil
}

func (p *AppMain) readField39(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.DownloadCounter = make(map[DownloadCounterType]*AppDownloadCounter, size)
	for i := 0; i < size; i++ {
		var _key2 DownloadCounterType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = DownloadCounterType(v)
		}
		_val3 := NewAppDownloadCounter()
		if err := _val3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val3)
		}
		p.DownloadCounter[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AppMain) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *AppMain) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.BriefIntro = v
	}
	return nil
}

func (p *AppMain) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.ShortName = v
	}
	return nil
}

func (p *AppMain) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.AppCategory = AdCategory(v)
	}
	return nil
}

func (p *AppMain) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.IsPaused = v
	}
	return nil
}

func (p *AppMain) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.Tag = v
	}
	return nil
}

func (p *AppMain) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.TagCorner = v
	}
	return nil
}

func (p *AppMain) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.TagOblique = v
	}
	return nil
}

func (p *AppMain) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.ObtMsg = v
	}
	return nil
}

func (p *AppMain) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.ReopeningMsg = v
	}
	return nil
}

func (p *AppMain) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.OnlineStatus = v
	}
	return nil
}

func (p *AppMain) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppMain"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppMain) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appid: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:package_name: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appname", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appname)); err != nil {
		return fmt.Errorf("%T.appname (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appname: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:version: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:author: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Author)); err != nil {
		return fmt.Errorf("%T.author (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:author: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubtime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pubtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Pubtime)); err != nil {
		return fmt.Errorf("%T.pubtime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pubtime: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appsize", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:appsize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appsize)); err != nil {
		return fmt.Errorf("%T.appsize (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:appsize: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:category: %s", p, err)
		}
	}
	return err
}

func (p *AppMain) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_count", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:download_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DownloadCount)); err != nil {
		return fmt.Errorf("%T.download_count (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:download_count: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:platform: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:platform: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:description: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon_url", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:icon_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IconUrl)); err != nil {
		return fmt.Errorf("%T.icon_url (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:icon_url: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField13(oprot thrift.TProtocol) (err error) {
	if p.SnapshotUrls != nil {
		if err := oprot.WriteFieldBegin("snapshot_urls", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:snapshot_urls: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SnapshotUrls)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SnapshotUrls {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:snapshot_urls: %s", p, err)
		}
	}
	return err
}

func (p *AppMain) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_url", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:download_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DownloadUrl)); err != nil {
		return fmt.Errorf("%T.download_url (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:download_url: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rate", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rate)); err != nil {
		return fmt.Errorf("%T.rate (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:rate: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author_id", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:author_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthorId)); err != nil {
		return fmt.Errorf("%T.author_id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:author_id: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ads", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:ads: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ads)); err != nil {
		return fmt.Errorf("%T.ads (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:ads: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("permission", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:permission: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Permission)); err != nil {
		return fmt.Errorf("%T.permission (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:permission: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:create_time: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:update_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:update_time: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version_code", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.version_code (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:version_code: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetSourceMarket() {
		if err := oprot.WriteFieldBegin("source_market", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:source_market: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SourceMarket)); err != nil {
			return fmt.Errorf("%T.source_market (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:source_market: %s", p, err)
		}
	}
	return err
}

func (p *AppMain) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_domain", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:source_domain: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceDomain)); err != nil {
		return fmt.Errorf("%T.source_domain (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:source_domain: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail_url", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:detail_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DetailUrl)); err != nil {
		return fmt.Errorf("%T.detail_url (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:detail_url: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("src_icon_url", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:src_icon_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SrcIconUrl)); err != nil {
		return fmt.Errorf("%T.src_icon_url (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:src_icon_url: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField26(oprot thrift.TProtocol) (err error) {
	if p.SrcSnapshotUrls != nil {
		if err := oprot.WriteFieldBegin("src_snapshot_urls", thrift.LIST, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:src_snapshot_urls: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SrcSnapshotUrls)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SrcSnapshotUrls {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:src_snapshot_urls: %s", p, err)
		}
	}
	return err
}

func (p *AppMain) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("src_download_url", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:src_download_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SrcDownloadUrl)); err != nil {
		return fmt.Errorf("%T.src_download_url (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:src_download_url: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:rank: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.BOOL, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:deleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:deleted: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("edited", thrift.BOOL, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:edited: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Edited)); err != nil {
		return fmt.Errorf("%T.edited (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:edited: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("has_update", thrift.BOOL, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:has_update: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.HasUpdate)); err != nil {
		return fmt.Errorf("%T.has_update (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:has_update: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lang", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:lang: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Lang)); err != nil {
		return fmt.Errorf("%T.lang (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:lang: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("domob_download_count", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:domob_download_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DomobDownloadCount)); err != nil {
		return fmt.Errorf("%T.domob_download_count (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:domob_download_count: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("domob_rate", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:domob_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DomobRate)); err != nil {
		return fmt.Errorf("%T.domob_rate (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:domob_rate: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_appid", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:parent_appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ParentAppid)); err != nil {
		return fmt.Errorf("%T.parent_appid (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:parent_appid: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_new", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:is_new: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsNew)); err != nil {
		return fmt.Errorf("%T.is_new (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:is_new: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("apk_appname", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:apk_appname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ApkAppname)); err != nil {
		return fmt.Errorf("%T.apk_appname (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:apk_appname: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("target_carrier", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:target_carrier: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetCarrier)); err != nil {
		return fmt.Errorf("%T.target_carrier (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:target_carrier: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField39(oprot thrift.TProtocol) (err error) {
	if p.DownloadCounter != nil {
		if err := oprot.WriteFieldBegin("download_counter", thrift.MAP, 39); err != nil {
			return fmt.Errorf("%T write field begin error 39:download_counter: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.DownloadCounter)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.DownloadCounter {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 39:download_counter: %s", p, err)
		}
	}
	return err
}

func (p *AppMain) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:ad_user_id: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief_intro", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:brief_intro: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BriefIntro)); err != nil {
		return fmt.Errorf("%T.brief_intro (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:brief_intro: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("short_name", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:short_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShortName)); err != nil {
		return fmt.Errorf("%T.short_name (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:short_name: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_category", thrift.I32, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:app_category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppCategory)); err != nil {
		return fmt.Errorf("%T.app_category (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:app_category: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_paused", thrift.BOOL, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:is_paused: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsPaused)); err != nil {
		return fmt.Errorf("%T.is_paused (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:is_paused: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:tag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tag)); err != nil {
		return fmt.Errorf("%T.tag (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:tag: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_corner", thrift.STRING, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:tag_corner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagCorner)); err != nil {
		return fmt.Errorf("%T.tag_corner (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:tag_corner: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_oblique", thrift.STRING, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:tag_oblique: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagOblique)); err != nil {
		return fmt.Errorf("%T.tag_oblique (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:tag_oblique: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("obt_msg", thrift.STRING, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:obt_msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ObtMsg)); err != nil {
		return fmt.Errorf("%T.obt_msg (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:obt_msg: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reopening_msg", thrift.STRING, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:reopening_msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReopeningMsg)); err != nil {
		return fmt.Errorf("%T.reopening_msg (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:reopening_msg: %s", p, err)
	}
	return err
}

func (p *AppMain) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("online_status", thrift.STRING, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:online_status: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OnlineStatus)); err != nil {
		return fmt.Errorf("%T.online_status (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:online_status: %s", p, err)
	}
	return err
}

func (p *AppMain) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppMain(%+v)", *p)
}

type AppWallGame struct {
	Id           int32      `thrift:"id,1" json:"id"`
	TypeA1       int32      `thrift:"type,2" json:"type"`
	Pos          int32      `thrift:"pos,3" json:"pos"`
	Logo         string     `thrift:"logo,4" json:"logo"`
	Image        string     `thrift:"image,5" json:"image"`
	Name         string     `thrift:"name,6" json:"name"`
	Texts        string     `thrift:"texts,7" json:"texts"`
	Provider     string     `thrift:"provider,8" json:"provider"`
	Desc         string     `thrift:"desc,9" json:"desc"`
	Screenshot   []string   `thrift:"screenshot,10" json:"screenshot"`
	Identifier   string     `thrift:"identifier,11" json:"identifier"`
	Vc           int32      `thrift:"vc,12" json:"vc"`
	Vn           string     `thrift:"vn,13" json:"vn"`
	ActionUrl    string     `thrift:"action_url,14" json:"action_url"`
	Size         int32      `thrift:"size,15" json:"size"`
	ReleaseTime  int64      `thrift:"release_time,16" json:"release_time"`
	ActionType   int32      `thrift:"action_type,17" json:"action_type"`
	Tr           string     `thrift:"tr,18" json:"tr"`
	Showd        bool       `thrift:"showd,19" json:"showd"`
	ClickTracker string     `thrift:"click_tracker,20" json:"click_tracker"`
	Thumbnail    string     `thrift:"thumbnail,21" json:"thumbnail"`
	AppCategory  AdCategory `thrift:"app_category,22" json:"app_category"`
	Searchid     int64      `thrift:"searchid,23" json:"searchid"`
	Title        string     `thrift:"title,24" json:"title"`
	Tag          string     `thrift:"tag,25" json:"tag"`
	TagCorner    string     `thrift:"tag_corner,26" json:"tag_corner"`
	TagOblique   string     `thrift:"tag_oblique,27" json:"tag_oblique"`
	ObtMsg       string     `thrift:"obt_msg,28" json:"obt_msg"`
	ReopeningMsg string     `thrift:"reopening_msg,29" json:"reopening_msg"`
	OnlineStatus string     `thrift:"online_status,30" json:"online_status"`
}

func NewAppWallGame() *AppWallGame {
	return &AppWallGame{
		AppCategory: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppWallGame) IsSetAppCategory() bool {
	return int64(p.AppCategory) != math.MinInt32-1
}

func (p *AppWallGame) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppWallGame) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AppWallGame) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *AppWallGame) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pos = v
	}
	return nil
}

func (p *AppWallGame) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *AppWallGame) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Image = v
	}
	return nil
}

func (p *AppWallGame) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppWallGame) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Texts = v
	}
	return nil
}

func (p *AppWallGame) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Provider = v
	}
	return nil
}

func (p *AppWallGame) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *AppWallGame) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screenshot = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.Screenshot = append(p.Screenshot, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppWallGame) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Identifier = v
	}
	return nil
}

func (p *AppWallGame) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Vc = v
	}
	return nil
}

func (p *AppWallGame) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Vn = v
	}
	return nil
}

func (p *AppWallGame) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *AppWallGame) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *AppWallGame) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ReleaseTime = v
	}
	return nil
}

func (p *AppWallGame) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ActionType = v
	}
	return nil
}

func (p *AppWallGame) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Tr = v
	}
	return nil
}

func (p *AppWallGame) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Showd = v
	}
	return nil
}

func (p *AppWallGame) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ClickTracker = v
	}
	return nil
}

func (p *AppWallGame) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Thumbnail = v
	}
	return nil
}

func (p *AppWallGame) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AppCategory = AdCategory(v)
	}
	return nil
}

func (p *AppWallGame) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AppWallGame) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *AppWallGame) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Tag = v
	}
	return nil
}

func (p *AppWallGame) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.TagCorner = v
	}
	return nil
}

func (p *AppWallGame) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.TagOblique = v
	}
	return nil
}

func (p *AppWallGame) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.ObtMsg = v
	}
	return nil
}

func (p *AppWallGame) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.ReopeningMsg = v
	}
	return nil
}

func (p *AppWallGame) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.OnlineStatus = v
	}
	return nil
}

func (p *AppWallGame) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppWallGame"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppWallGame) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:type: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pos: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pos)); err != nil {
		return fmt.Errorf("%T.pos (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pos: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:logo: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:image: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Image)); err != nil {
		return fmt.Errorf("%T.image (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:image: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:name: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("texts", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:texts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Texts)); err != nil {
		return fmt.Errorf("%T.texts (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:texts: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("provider", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:provider: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Provider)); err != nil {
		return fmt.Errorf("%T.provider (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:provider: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:desc: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Screenshot != nil {
		if err := oprot.WriteFieldBegin("screenshot", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:screenshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Screenshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screenshot {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:screenshot: %s", p, err)
		}
	}
	return err
}

func (p *AppWallGame) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("identifier", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Identifier)); err != nil {
		return fmt.Errorf("%T.identifier (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:identifier: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vc", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:vc: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Vc)); err != nil {
		return fmt.Errorf("%T.vc (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:vc: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vn", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:vn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vn)); err != nil {
		return fmt.Errorf("%T.vn (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:vn: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_url", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:action_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.action_url (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:action_url: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:size: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("release_time", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:release_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReleaseTime)); err != nil {
		return fmt.Errorf("%T.release_time (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:release_time: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_type", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.action_type (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:action_type: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tr", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:tr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tr)); err != nil {
		return fmt.Errorf("%T.tr (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:tr: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("showd", thrift.BOOL, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:showd: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Showd)); err != nil {
		return fmt.Errorf("%T.showd (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:showd: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_tracker", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:click_tracker: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickTracker)); err != nil {
		return fmt.Errorf("%T.click_tracker (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:click_tracker: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumbnail", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:thumbnail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumbnail)); err != nil {
		return fmt.Errorf("%T.thumbnail (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:thumbnail: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_category", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:app_category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppCategory)); err != nil {
		return fmt.Errorf("%T.app_category (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:app_category: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:searchid: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:title: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:tag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tag)); err != nil {
		return fmt.Errorf("%T.tag (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:tag: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_corner", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:tag_corner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagCorner)); err != nil {
		return fmt.Errorf("%T.tag_corner (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:tag_corner: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_oblique", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:tag_oblique: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagOblique)); err != nil {
		return fmt.Errorf("%T.tag_oblique (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:tag_oblique: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("obt_msg", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:obt_msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ObtMsg)); err != nil {
		return fmt.Errorf("%T.obt_msg (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:obt_msg: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reopening_msg", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:reopening_msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReopeningMsg)); err != nil {
		return fmt.Errorf("%T.reopening_msg (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:reopening_msg: %s", p, err)
	}
	return err
}

func (p *AppWallGame) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("online_status", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:online_status: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OnlineStatus)); err != nil {
		return fmt.Errorf("%T.online_status (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:online_status: %s", p, err)
	}
	return err
}

func (p *AppWallGame) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppWallGame(%+v)", *p)
}

type Game struct {
	DuoyouGame *AppMain     `thrift:"duoyou_game,1" json:"duoyou_game"`
	WallGame   *AppWallGame `thrift:"wall_game,2" json:"wall_game"`
}

func NewGame() *Game {
	return &Game{}
}

func (p *Game) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Game) readField1(iprot thrift.TProtocol) error {
	p.DuoyouGame = NewAppMain()
	if err := p.DuoyouGame.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DuoyouGame)
	}
	return nil
}

func (p *Game) readField2(iprot thrift.TProtocol) error {
	p.WallGame = NewAppWallGame()
	if err := p.WallGame.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.WallGame)
	}
	return nil
}

func (p *Game) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Game"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Game) writeField1(oprot thrift.TProtocol) (err error) {
	if p.DuoyouGame != nil {
		if err := oprot.WriteFieldBegin("duoyou_game", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:duoyou_game: %s", p, err)
		}
		if err := p.DuoyouGame.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DuoyouGame)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:duoyou_game: %s", p, err)
		}
	}
	return err
}

func (p *Game) writeField2(oprot thrift.TProtocol) (err error) {
	if p.WallGame != nil {
		if err := oprot.WriteFieldBegin("wall_game", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:wall_game: %s", p, err)
		}
		if err := p.WallGame.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.WallGame)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:wall_game: %s", p, err)
		}
	}
	return err
}

func (p *Game) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Game(%+v)", *p)
}

type Gift struct {
	Id         int32  `thrift:"id,1" json:"id"`
	GameId     int32  `thrift:"game_id,2" json:"game_id"`
	IsFromWall int32  `thrift:"is_from_wall,3" json:"is_from_wall"`
	BeginDate  int32  `thrift:"begin_date,4" json:"begin_date"`
	EndDate    int32  `thrift:"end_date,5" json:"end_date"`
	Name       string `thrift:"name,6" json:"name"`
	Scope      string `thrift:"scope,7" json:"scope"`
	Cat        string `thrift:"cat,8" json:"cat"`
	Intro      string `thrift:"intro,9" json:"intro"`
	Tip        string `thrift:"tip,10" json:"tip"`
	Content    string `thrift:"content,11" json:"content"`
	CreateTime int32  `thrift:"create_time,12" json:"create_time"`
	Total      string `thrift:"total,13" json:"total"`
	Residue    string `thrift:"residue,14" json:"residue"`
	Card       string `thrift:"card,15" json:"card"`
	Game       *Game  `thrift:"game,16" json:"game"`
}

func NewGift() *Gift {
	return &Gift{}
}

func (p *Gift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Gift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Gift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.GameId = v
	}
	return nil
}

func (p *Gift) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IsFromWall = v
	}
	return nil
}

func (p *Gift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.BeginDate = v
	}
	return nil
}

func (p *Gift) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndDate = v
	}
	return nil
}

func (p *Gift) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Gift) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Scope = v
	}
	return nil
}

func (p *Gift) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Cat = v
	}
	return nil
}

func (p *Gift) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Intro = v
	}
	return nil
}

func (p *Gift) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Tip = v
	}
	return nil
}

func (p *Gift) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *Gift) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Gift) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *Gift) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Residue = v
	}
	return nil
}

func (p *Gift) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Card = v
	}
	return nil
}

func (p *Gift) readField16(iprot thrift.TProtocol) error {
	p.Game = NewGame()
	if err := p.Game.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Game)
	}
	return nil
}

func (p *Gift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Gift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Gift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Gift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("game_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:game_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GameId)); err != nil {
		return fmt.Errorf("%T.game_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:game_id: %s", p, err)
	}
	return err
}

func (p *Gift) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_from_wall", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:is_from_wall: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsFromWall)); err != nil {
		return fmt.Errorf("%T.is_from_wall (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:is_from_wall: %s", p, err)
	}
	return err
}

func (p *Gift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("begin_date", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:begin_date: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BeginDate)); err != nil {
		return fmt.Errorf("%T.begin_date (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:begin_date: %s", p, err)
	}
	return err
}

func (p *Gift) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_date", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:end_date: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndDate)); err != nil {
		return fmt.Errorf("%T.end_date (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:end_date: %s", p, err)
	}
	return err
}

func (p *Gift) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:name: %s", p, err)
	}
	return err
}

func (p *Gift) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("scope", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:scope: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Scope)); err != nil {
		return fmt.Errorf("%T.scope (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:scope: %s", p, err)
	}
	return err
}

func (p *Gift) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cat", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:cat: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cat)); err != nil {
		return fmt.Errorf("%T.cat (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:cat: %s", p, err)
	}
	return err
}

func (p *Gift) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("intro", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:intro: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Intro)); err != nil {
		return fmt.Errorf("%T.intro (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:intro: %s", p, err)
	}
	return err
}

func (p *Gift) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tip", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:tip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tip)); err != nil {
		return fmt.Errorf("%T.tip (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:tip: %s", p, err)
	}
	return err
}

func (p *Gift) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:content: %s", p, err)
	}
	return err
}

func (p *Gift) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:create_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:create_time: %s", p, err)
	}
	return err
}

func (p *Gift) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:total: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Total)); err != nil {
		return fmt.Errorf("%T.total (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:total: %s", p, err)
	}
	return err
}

func (p *Gift) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("residue", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:residue: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Residue)); err != nil {
		return fmt.Errorf("%T.residue (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:residue: %s", p, err)
	}
	return err
}

func (p *Gift) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("card", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:card: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Card)); err != nil {
		return fmt.Errorf("%T.card (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:card: %s", p, err)
	}
	return err
}

func (p *Gift) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Game != nil {
		if err := oprot.WriteFieldBegin("game", thrift.STRUCT, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:game: %s", p, err)
		}
		if err := p.Game.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Game)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:game: %s", p, err)
		}
	}
	return err
}

func (p *Gift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Gift(%+v)", *p)
}

type GiftResult struct {
	Gifts  []*Gift `thrift:"gifts,1" json:"gifts"`
	IsLast bool    `thrift:"is_last,2" json:"is_last"`
	Total  int32   `thrift:"total,3" json:"total"`
}

func NewGiftResult() *GiftResult {
	return &GiftResult{}
}

func (p *GiftResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GiftResult) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Gifts = make([]*Gift, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewGift()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Gifts = append(p.Gifts, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GiftResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsLast = v
	}
	return nil
}

func (p *GiftResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *GiftResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GiftResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GiftResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Gifts != nil {
		if err := oprot.WriteFieldBegin("gifts", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:gifts: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Gifts)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Gifts {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:gifts: %s", p, err)
		}
	}
	return err
}

func (p *GiftResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_last", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:is_last: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsLast)); err != nil {
		return fmt.Errorf("%T.is_last (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:is_last: %s", p, err)
	}
	return err
}

func (p *GiftResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:total: %s", p, err)
	}
	return err
}

func (p *GiftResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GiftResult(%+v)", *p)
}

type AppUpdateSummary struct {
	Pubtime         TimeInt `thrift:"pubtime,1" json:"pubtime"`
	NewAppCount     int32   `thrift:"new_app_count,2" json:"new_app_count"`
	UpdatedAppCount int32   `thrift:"updated_app_count,3" json:"updated_app_count"`
}

func NewAppUpdateSummary() *AppUpdateSummary {
	return &AppUpdateSummary{}
}

func (p *AppUpdateSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppUpdateSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pubtime = TimeInt(v)
	}
	return nil
}

func (p *AppUpdateSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.NewAppCount = v
	}
	return nil
}

func (p *AppUpdateSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UpdatedAppCount = v
	}
	return nil
}

func (p *AppUpdateSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppUpdateSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppUpdateSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubtime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pubtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Pubtime)); err != nil {
		return fmt.Errorf("%T.pubtime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pubtime: %s", p, err)
	}
	return err
}

func (p *AppUpdateSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("new_app_count", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:new_app_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NewAppCount)); err != nil {
		return fmt.Errorf("%T.new_app_count (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:new_app_count: %s", p, err)
	}
	return err
}

func (p *AppUpdateSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updated_app_count", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:updated_app_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpdatedAppCount)); err != nil {
		return fmt.Errorf("%T.updated_app_count (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:updated_app_count: %s", p, err)
	}
	return err
}

func (p *AppUpdateSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppUpdateSummary(%+v)", *p)
}

type CategorySummary struct {
	Category    Category `thrift:"category,1" json:"category"`
	Name        string   `thrift:"name,2" json:"name"`
	TotalApps   int32    `thrift:"total_apps,3" json:"total_apps"`
	Description string   `thrift:"description,4" json:"description"`
	ImgUrl      string   `thrift:"img_url,5" json:"img_url"`
}

func NewCategorySummary() *CategorySummary {
	return &CategorySummary{
		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CategorySummary) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *CategorySummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CategorySummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *CategorySummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CategorySummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalApps = v
	}
	return nil
}

func (p *CategorySummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *CategorySummary) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *CategorySummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CategorySummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CategorySummary) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:category: %s", p, err)
		}
	}
	return err
}

func (p *CategorySummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *CategorySummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_apps", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:total_apps: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalApps)); err != nil {
		return fmt.Errorf("%T.total_apps (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:total_apps: %s", p, err)
	}
	return err
}

func (p *CategorySummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:description: %s", p, err)
	}
	return err
}

func (p *CategorySummary) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:img_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.img_url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:img_url: %s", p, err)
	}
	return err
}

func (p *CategorySummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CategorySummary(%+v)", *p)
}

type HotApp struct {
	App      *AppMain `thrift:"app,1" json:"app"`
	RankInfo []string `thrift:"rank_info,2" json:"rank_info"`
	HotType  int32    `thrift:"hot_type,3" json:"hot_type"`
}

func NewHotApp() *HotApp {
	return &HotApp{}
}

func (p *HotApp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *HotApp) readField1(iprot thrift.TProtocol) error {
	p.App = NewAppMain()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *HotApp) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RankInfo = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.RankInfo = append(p.RankInfo, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *HotApp) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.HotType = v
	}
	return nil
}

func (p *HotApp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("HotApp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *HotApp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:app: %s", p, err)
		}
	}
	return err
}

func (p *HotApp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RankInfo != nil {
		if err := oprot.WriteFieldBegin("rank_info", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:rank_info: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.RankInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RankInfo {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:rank_info: %s", p, err)
		}
	}
	return err
}

func (p *HotApp) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hot_type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:hot_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HotType)); err != nil {
		return fmt.Errorf("%T.hot_type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:hot_type: %s", p, err)
	}
	return err
}

func (p *HotApp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HotApp(%+v)", *p)
}

type AppResource struct {
	Appid      int32        `thrift:"appid,1" json:"appid"`
	TypeA1     ResourceType `thrift:"type,2" json:"type"`
	MimeType   string       `thrift:"mime_type,3" json:"mime_type"`
	Length     int32        `thrift:"length,4" json:"length"`
	SourceUrl  string       `thrift:"source_url,5" json:"source_url"`
	ContentMd5 string       `thrift:"content_md5,6" json:"content_md5"`
	Seqid      int32        `thrift:"seqid,7" json:"seqid"`
	UpdateTime int32        `thrift:"update_time,8" json:"update_time"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Content []byte `thrift:"content,20" json:"content"`
}

func NewAppResource() *AppResource {
	return &AppResource{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppResource) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AppResource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppResource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AppResource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = ResourceType(v)
	}
	return nil
}

func (p *AppResource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MimeType = v
	}
	return nil
}

func (p *AppResource) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Length = v
	}
	return nil
}

func (p *AppResource) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SourceUrl = v
	}
	return nil
}

func (p *AppResource) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ContentMd5 = v
	}
	return nil
}

func (p *AppResource) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Seqid = v
	}
	return nil
}

func (p *AppResource) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *AppResource) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppResource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppResource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppResource) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appid: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *AppResource) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mime_type", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mime_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MimeType)); err != nil {
		return fmt.Errorf("%T.mime_type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mime_type: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("length", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:length: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Length)); err != nil {
		return fmt.Errorf("%T.length (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:length: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:source_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceUrl)); err != nil {
		return fmt.Errorf("%T.source_url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:source_url: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content_md5", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:content_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ContentMd5)); err != nil {
		return fmt.Errorf("%T.content_md5 (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:content_md5: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seqid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:seqid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Seqid)); err != nil {
		return fmt.Errorf("%T.seqid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:seqid: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:update_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:update_time: %s", p, err)
	}
	return err
}

func (p *AppResource) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Content != nil {
		if err := oprot.WriteFieldBegin("content", thrift.STRING, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:content: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Content); err != nil {
			return fmt.Errorf("%T.content (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:content: %s", p, err)
		}
	}
	return err
}

func (p *AppResource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppResource(%+v)", *p)
}

type Topic struct {
	Id          int32   `thrift:"id,1" json:"id"`
	Name        string  `thrift:"name,2" json:"name"`
	CreateTime  TimeInt `thrift:"create_time,3" json:"create_time"`
	Description string  `thrift:"description,4" json:"description"`
	TotalApps   int32   `thrift:"total_apps,5" json:"total_apps"`
	Disabled    bool    `thrift:"disabled,6" json:"disabled"`
	ImgUrl      string  `thrift:"img_url,7" json:"img_url"`
}

func NewTopic() *Topic {
	return &Topic{}
}

func (p *Topic) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Topic) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Topic) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Topic) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Topic) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Topic) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TotalApps = v
	}
	return nil
}

func (p *Topic) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Disabled = v
	}
	return nil
}

func (p *Topic) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *Topic) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Topic"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Topic) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Topic) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Topic) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:create_time: %s", p, err)
	}
	return err
}

func (p *Topic) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:description: %s", p, err)
	}
	return err
}

func (p *Topic) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_apps", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:total_apps: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalApps)); err != nil {
		return fmt.Errorf("%T.total_apps (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:total_apps: %s", p, err)
	}
	return err
}

func (p *Topic) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disabled", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:disabled: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Disabled)); err != nil {
		return fmt.Errorf("%T.disabled (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:disabled: %s", p, err)
	}
	return err
}

func (p *Topic) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:img_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.img_url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:img_url: %s", p, err)
	}
	return err
}

func (p *Topic) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Topic(%+v)", *p)
}

type Banner struct {
	Id          int32      `thrift:"id,1" json:"id"`
	Name        string     `thrift:"name,2" json:"name"`
	Description string     `thrift:"description,3" json:"description"`
	TypeA1      BannerType `thrift:"type,4" json:"type"`
	ObjId       int32      `thrift:"obj_id,5" json:"obj_id"`
	ImgUrl      string     `thrift:"img_url,6" json:"img_url"`
	Seqid       int32      `thrift:"seqid,7" json:"seqid"`
	Disabled    bool       `thrift:"disabled,8" json:"disabled"`
	CreateTime  TimeInt    `thrift:"create_time,9" json:"create_time"`
}

func NewBanner() *Banner {
	return &Banner{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Banner) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Banner) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Banner) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Banner) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Banner) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Banner) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = BannerType(v)
	}
	return nil
}

func (p *Banner) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ObjId = v
	}
	return nil
}

func (p *Banner) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *Banner) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Seqid = v
	}
	return nil
}

func (p *Banner) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Disabled = v
	}
	return nil
}

func (p *Banner) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Banner) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Banner"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Banner) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Banner) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Banner) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:description: %s", p, err)
	}
	return err
}

func (p *Banner) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *Banner) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("obj_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:obj_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ObjId)); err != nil {
		return fmt.Errorf("%T.obj_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:obj_id: %s", p, err)
	}
	return err
}

func (p *Banner) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_url", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:img_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.img_url (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:img_url: %s", p, err)
	}
	return err
}

func (p *Banner) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seqid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:seqid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Seqid)); err != nil {
		return fmt.Errorf("%T.seqid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:seqid: %s", p, err)
	}
	return err
}

func (p *Banner) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disabled", thrift.BOOL, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:disabled: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Disabled)); err != nil {
		return fmt.Errorf("%T.disabled (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:disabled: %s", p, err)
	}
	return err
}

func (p *Banner) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:create_time: %s", p, err)
	}
	return err
}

func (p *Banner) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Banner(%+v)", *p)
}

type MainList struct {
	TotalCount int32      `thrift:"total_count,1" json:"total_count"`
	Data       []*AppMain `thrift:"data,2" json:"data"`
}

func NewMainList() *MainList {
	return &MainList{}
}

func (p *MainList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MainList) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *MainList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Data = make([]*AppMain, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewAppMain()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.Data = append(p.Data, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MainList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MainList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MainList) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total_count: %s", p, err)
	}
	return err
}

func (p *MainList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:data: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Data {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:data: %s", p, err)
		}
	}
	return err
}

func (p *MainList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MainList(%+v)", *p)
}

type GameList struct {
	TotalCount int32   `thrift:"total_count,1" json:"total_count"`
	Data       []*Game `thrift:"data,2" json:"data"`
}

func NewGameList() *GameList {
	return &GameList{}
}

func (p *GameList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GameList) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *GameList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Data = make([]*Game, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewGame()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.Data = append(p.Data, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GameList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GameList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GameList) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total_count: %s", p, err)
	}
	return err
}

func (p *GameList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:data: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Data {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:data: %s", p, err)
		}
	}
	return err
}

func (p *GameList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GameList(%+v)", *p)
}

type AppTracker struct {
	Appid       int32  `thrift:"appid,1" json:"appid"`
	VersionCode int32  `thrift:"version_code,2" json:"version_code"`
	Refer       string `thrift:"refer,3" json:"refer"`
	AdUserId    int32  `thrift:"ad_user_id,4" json:"ad_user_id"`
	ChannelId   string `thrift:"channel_id,5" json:"channel_id"`
	ReqTimeMs   int32  `thrift:"req_time_ms,6" json:"req_time_ms"`
	Ipb         string `thrift:"ipb,7" json:"ipb"`
	Ppid        string `thrift:"ppid,8" json:"ppid"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Searchid    int64          `thrift:"searchid,30" json:"searchid"`
	Expid       int32          `thrift:"expid,31" json:"expid"`
	Device      DeviceCode     `thrift:"device,32" json:"device"`
	Os          OSCode         `thrift:"os,33" json:"os"`
	Region      int32          `thrift:"region,34" json:"region"`
	CityCode    int32          `thrift:"city_code,35" json:"city_code"`
	CarrierCode CarrierCode    `thrift:"carrier_code,36" json:"carrier_code"`
	AccessCode  AccessTypeCode `thrift:"access_code,37" json:"access_code"`
	Duid        int64          `thrift:"duid,38" json:"duid"`
	Imei        string         `thrift:"imei,39" json:"imei"`
	Imsi        string         `thrift:"imsi,40" json:"imsi"`
	Dmac        string         `thrift:"dmac,41" json:"dmac"`
}

func NewAppTracker() *AppTracker {
	return &AppTracker{
		CarrierCode: math.MinInt32 - 1, // unset sentinal value

		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppTracker) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *AppTracker) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *AppTracker) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppTracker) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AppTracker) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *AppTracker) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *AppTracker) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *AppTracker) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *AppTracker) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ReqTimeMs = v
	}
	return nil
}

func (p *AppTracker) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *AppTracker) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Ppid = v
	}
	return nil
}

func (p *AppTracker) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AppTracker) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *AppTracker) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *AppTracker) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *AppTracker) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Region = v
	}
	return nil
}

func (p *AppTracker) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CityCode = v
	}
	return nil
}

func (p *AppTracker) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.CarrierCode = CarrierCode(v)
	}
	return nil
}

func (p *AppTracker) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AccessCode = AccessTypeCode(v)
	}
	return nil
}

func (p *AppTracker) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *AppTracker) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AppTracker) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *AppTracker) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AppTracker) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppTracker"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppTracker) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appid: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version_code", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.version_code (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:version_code: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:refer: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ad_user_id: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel_id", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:channel_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channel_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:channel_id: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_time_ms", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:req_time_ms: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTimeMs)); err != nil {
		return fmt.Errorf("%T.req_time_ms (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:req_time_ms: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ipb: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ppid", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ppid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ppid)); err != nil {
		return fmt.Errorf("%T.ppid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ppid: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:searchid: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:expid: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:device: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:os: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:region: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:city_code: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:carrier_code: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:access_code: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:duid: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:imei: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:imsi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:imsi: %s", p, err)
	}
	return err
}

func (p *AppTracker) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:dmac: %s", p, err)
	}
	return err
}

func (p *AppTracker) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppTracker(%+v)", *p)
}

type AppEventReport struct {
	Action          string `thrift:"action,1" json:"action"`
	Appid           int32  `thrift:"appid,2" json:"appid"`
	VersionCode     int32  `thrift:"version_code,3" json:"version_code"`
	Refer           string `thrift:"refer,4" json:"refer"`
	AdUserId        int32  `thrift:"ad_user_id,5" json:"ad_user_id"`
	ChannelId       string `thrift:"channel_id,6" json:"channel_id"`
	ReqTimeMs       int64  `thrift:"req_time_ms,7" json:"req_time_ms"`
	Ipb             string `thrift:"ipb,8" json:"ipb"`
	ReportTimeMs    int64  `thrift:"report_time_ms,9" json:"report_time_ms"`
	Imei            string `thrift:"imei,10" json:"imei"`
	Imsi            string `thrift:"imsi,11" json:"imsi"`
	Uuid            string `thrift:"uuid,12" json:"uuid"`
	XScreen         string `thrift:"x_screen,13" json:"x_screen"`
	XPinfo          string `thrift:"x_pinfo,14" json:"x_pinfo"`
	Network         string `thrift:"network,15" json:"network"`
	Carrier         string `thrift:"carrier,16" json:"carrier"`
	AndroidVersion  string `thrift:"android_version,17" json:"android_version"`
	FromPackageName string `thrift:"from_package_name,18" json:"from_package_name"`
	FromVersionCode string `thrift:"from_version_code,19" json:"from_version_code"`
	XUa             string `thrift:"x_ua,20" json:"x_ua"`
	Ip              string `thrift:"ip,21" json:"ip"`
	XUid            string `thrift:"x_uid,22" json:"x_uid"`
	PhoneModel      string `thrift:"phone_model,23" json:"phone_model"`
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Searchid    int64          `thrift:"searchid,30" json:"searchid"`
	Expid       int32          `thrift:"expid,31" json:"expid"`
	Device      DeviceCode     `thrift:"device,32" json:"device"`
	Os          OSCode         `thrift:"os,33" json:"os"`
	Region      int32          `thrift:"region,34" json:"region"`
	CityCode    int32          `thrift:"city_code,35" json:"city_code"`
	CarrierCode CarrierCode    `thrift:"carrier_code,36" json:"carrier_code"`
	AccessCode  AccessTypeCode `thrift:"access_code,37" json:"access_code"`
	Duid        int64          `thrift:"duid,38" json:"duid"`
	// unused field # 39
	// unused field # 40
	Dmac string `thrift:"dmac,41" json:"dmac"`
}

func NewAppEventReport() *AppEventReport {
	return &AppEventReport{
		CarrierCode: math.MinInt32 - 1, // unset sentinal value

		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppEventReport) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *AppEventReport) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *AppEventReport) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppEventReport) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AppEventReport) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AppEventReport) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *AppEventReport) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *AppEventReport) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *AppEventReport) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *AppEventReport) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ReqTimeMs = v
	}
	return nil
}

func (p *AppEventReport) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *AppEventReport) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ReportTimeMs = v
	}
	return nil
}

func (p *AppEventReport) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AppEventReport) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *AppEventReport) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *AppEventReport) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.XScreen = v
	}
	return nil
}

func (p *AppEventReport) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.XPinfo = v
	}
	return nil
}

func (p *AppEventReport) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *AppEventReport) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *AppEventReport) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AndroidVersion = v
	}
	return nil
}

func (p *AppEventReport) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.FromPackageName = v
	}
	return nil
}

func (p *AppEventReport) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.FromVersionCode = v
	}
	return nil
}

func (p *AppEventReport) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.XUa = v
	}
	return nil
}

func (p *AppEventReport) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *AppEventReport) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.XUid = v
	}
	return nil
}

func (p *AppEventReport) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.PhoneModel = v
	}
	return nil
}

func (p *AppEventReport) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AppEventReport) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *AppEventReport) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *AppEventReport) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *AppEventReport) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Region = v
	}
	return nil
}

func (p *AppEventReport) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CityCode = v
	}
	return nil
}

func (p *AppEventReport) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.CarrierCode = CarrierCode(v)
	}
	return nil
}

func (p *AppEventReport) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AccessCode = AccessTypeCode(v)
	}
	return nil
}

func (p *AppEventReport) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *AppEventReport) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AppEventReport) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppEventReport"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppEventReport) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:action: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Action)); err != nil {
		return fmt.Errorf("%T.action (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:action: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version_code", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.version_code (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:version_code: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:refer: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ad_user_id: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel_id", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:channel_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channel_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:channel_id: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_time_ms", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:req_time_ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReqTimeMs)); err != nil {
		return fmt.Errorf("%T.req_time_ms (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:req_time_ms: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ipb: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("report_time_ms", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:report_time_ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReportTimeMs)); err != nil {
		return fmt.Errorf("%T.report_time_ms (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:report_time_ms: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:imei: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:imsi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:imsi: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:uuid: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_screen", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:x_screen: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XScreen)); err != nil {
		return fmt.Errorf("%T.x_screen (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:x_screen: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_pinfo", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:x_pinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XPinfo)); err != nil {
		return fmt.Errorf("%T.x_pinfo (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:x_pinfo: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:network: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:carrier: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_version", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:android_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidVersion)); err != nil {
		return fmt.Errorf("%T.android_version (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:android_version: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("from_package_name", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:from_package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FromPackageName)); err != nil {
		return fmt.Errorf("%T.from_package_name (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:from_package_name: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("from_version_code", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:from_version_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FromVersionCode)); err != nil {
		return fmt.Errorf("%T.from_version_code (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:from_version_code: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_ua", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:x_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUa)); err != nil {
		return fmt.Errorf("%T.x_ua (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:x_ua: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:ip: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_uid", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:x_uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUid)); err != nil {
		return fmt.Errorf("%T.x_uid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:x_uid: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_model", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:phone_model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneModel)); err != nil {
		return fmt.Errorf("%T.phone_model (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:phone_model: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:searchid: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:expid: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:device: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:os: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:region: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:city_code: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:carrier_code: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:access_code: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:duid: %s", p, err)
	}
	return err
}

func (p *AppEventReport) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:dmac: %s", p, err)
	}
	return err
}

func (p *AppEventReport) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppEventReport(%+v)", *p)
}

type Request struct {
	Searchid           int64              `thrift:"searchid,1" json:"searchid"`
	XUid               string             `thrift:"x_uid,2" json:"x_uid"`
	XPinfo             string             `thrift:"x_pinfo,3" json:"x_pinfo"`
	XUa                string             `thrift:"x_ua,4" json:"x_ua"`
	XScreen            string             `thrift:"x_screen,5" json:"x_screen"`
	Pubid              string             `thrift:"pubid,6" json:"pubid"`
	Ppid               string             `thrift:"ppid,7" json:"ppid"`
	Refer              string             `thrift:"refer,8" json:"refer"`
	ReqTimeMs          int64              `thrift:"req_time_ms,9" json:"req_time_ms"`
	ClientIp           string             `thrift:"client_ip,10" json:"client_ip"`
	PackageName        string             `thrift:"package_name,11" json:"package_name"`
	PackageVersionCode int32              `thrift:"package_version_code,12" json:"package_version_code"`
	AndroidVersion     string             `thrift:"android_version,13" json:"android_version"`
	ChannelId          string             `thrift:"channel_id,14" json:"channel_id"`
	Carrier            string             `thrift:"carrier,15" json:"carrier"`
	Network            string             `thrift:"network,16" json:"network"`
	PhoneModel         string             `thrift:"phone_model,17" json:"phone_model"`
	CarrierCode        common.CarrierCode `thrift:"carrier_code,18" json:"carrier_code"`
	DuoyouVersionCode  int32              `thrift:"duoyou_version_code,19" json:"duoyou_version_code"`
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Expid    int32      `thrift:"expid,31" json:"expid"`
	Device   DeviceCode `thrift:"device,32" json:"device"`
	Os       OSCode     `thrift:"os,33" json:"os"`
	Region   int32      `thrift:"region,34" json:"region"`
	CityCode int32      `thrift:"city_code,35" json:"city_code"`
	// unused field # 36
	AccessCode AccessTypeCode `thrift:"access_code,37" json:"access_code"`
	Duid       int64          `thrift:"duid,38" json:"duid"`
	Imei       string         `thrift:"imei,39" json:"imei"`
	Imsi       string         `thrift:"imsi,40" json:"imsi"`
	Dmac       string         `thrift:"dmac,41" json:"dmac"`
	MediaSpec  bool           `thrift:"media_spec,42" json:"media_spec"`
}

func NewRequest() *Request {
	return &Request{
		CarrierCode: math.MinInt32 - 1, // unset sentinal value

		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Request) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *Request) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *Request) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Request) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *Request) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.XUid = v
	}
	return nil
}

func (p *Request) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.XPinfo = v
	}
	return nil
}

func (p *Request) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.XUa = v
	}
	return nil
}

func (p *Request) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.XScreen = v
	}
	return nil
}

func (p *Request) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *Request) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ppid = v
	}
	return nil
}

func (p *Request) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *Request) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ReqTimeMs = v
	}
	return nil
}

func (p *Request) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *Request) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *Request) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PackageVersionCode = v
	}
	return nil
}

func (p *Request) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AndroidVersion = v
	}
	return nil
}

func (p *Request) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *Request) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *Request) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *Request) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.PhoneModel = v
	}
	return nil
}

func (p *Request) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.CarrierCode = common.CarrierCode(v)
	}
	return nil
}

func (p *Request) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.DuoyouVersionCode = v
	}
	return nil
}

func (p *Request) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *Request) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *Request) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *Request) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Region = v
	}
	return nil
}

func (p *Request) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CityCode = v
	}
	return nil
}

func (p *Request) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AccessCode = AccessTypeCode(v)
	}
	return nil
}

func (p *Request) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *Request) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *Request) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *Request) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *Request) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.MediaSpec = v
	}
	return nil
}

func (p *Request) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Request"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Request) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchid: %s", p, err)
	}
	return err
}

func (p *Request) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:x_uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUid)); err != nil {
		return fmt.Errorf("%T.x_uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:x_uid: %s", p, err)
	}
	return err
}

func (p *Request) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_pinfo", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:x_pinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XPinfo)); err != nil {
		return fmt.Errorf("%T.x_pinfo (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:x_pinfo: %s", p, err)
	}
	return err
}

func (p *Request) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_ua", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:x_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUa)); err != nil {
		return fmt.Errorf("%T.x_ua (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:x_ua: %s", p, err)
	}
	return err
}

func (p *Request) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_screen", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:x_screen: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XScreen)); err != nil {
		return fmt.Errorf("%T.x_screen (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:x_screen: %s", p, err)
	}
	return err
}

func (p *Request) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pubid: %s", p, err)
	}
	return err
}

func (p *Request) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ppid", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ppid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ppid)); err != nil {
		return fmt.Errorf("%T.ppid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ppid: %s", p, err)
	}
	return err
}

func (p *Request) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:refer: %s", p, err)
	}
	return err
}

func (p *Request) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_time_ms", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:req_time_ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReqTimeMs)); err != nil {
		return fmt.Errorf("%T.req_time_ms (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:req_time_ms: %s", p, err)
	}
	return err
}

func (p *Request) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:client_ip: %s", p, err)
	}
	return err
}

func (p *Request) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:package_name: %s", p, err)
	}
	return err
}

func (p *Request) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_version_code", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:package_version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageVersionCode)); err != nil {
		return fmt.Errorf("%T.package_version_code (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:package_version_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_version", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:android_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidVersion)); err != nil {
		return fmt.Errorf("%T.android_version (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:android_version: %s", p, err)
	}
	return err
}

func (p *Request) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel_id", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:channel_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channel_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:channel_id: %s", p, err)
	}
	return err
}

func (p *Request) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:carrier: %s", p, err)
	}
	return err
}

func (p *Request) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:network: %s", p, err)
	}
	return err
}

func (p *Request) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_model", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:phone_model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneModel)); err != nil {
		return fmt.Errorf("%T.phone_model (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:phone_model: %s", p, err)
	}
	return err
}

func (p *Request) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:carrier_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duoyou_version_code", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:duoyou_version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DuoyouVersionCode)); err != nil {
		return fmt.Errorf("%T.duoyou_version_code (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:duoyou_version_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:expid: %s", p, err)
	}
	return err
}

func (p *Request) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:device: %s", p, err)
	}
	return err
}

func (p *Request) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:os: %s", p, err)
	}
	return err
}

func (p *Request) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:region: %s", p, err)
	}
	return err
}

func (p *Request) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:city_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:access_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:duid: %s", p, err)
	}
	return err
}

func (p *Request) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:imei: %s", p, err)
	}
	return err
}

func (p *Request) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:imsi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:imsi: %s", p, err)
	}
	return err
}

func (p *Request) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:dmac: %s", p, err)
	}
	return err
}

func (p *Request) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_spec", thrift.BOOL, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:media_spec: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaSpec)); err != nil {
		return fmt.Errorf("%T.media_spec (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:media_spec: %s", p, err)
	}
	return err
}

func (p *Request) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Request(%+v)", *p)
}
