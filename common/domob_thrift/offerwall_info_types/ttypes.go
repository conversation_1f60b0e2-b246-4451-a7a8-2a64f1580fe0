// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_info_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/offerwall_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = offerwall_types.GoUnusedProtection__
var GoUnusedProtection__ int

//操作系统
type Platform int64

const (
	Platform_ANDROID Platform = 1
	Platform_IOS     Platform = 2
)

func (p Platform) String() string {
	switch p {
	case Platform_ANDROID:
		return "Platform_ANDROID"
	case Platform_IOS:
		return "Platform_IOS"
	}
	return "<UNSET>"
}

func PlatformFromString(s string) (Platform, error) {
	switch s {
	case "Platform_ANDROID":
		return Platform_ANDROID, nil
	case "Platform_IOS":
		return Platform_IOS, nil
	}
	return Platform(math.MinInt32 - 1), fmt.Errorf("not a valid Platform string")
}

//频次控制的一个周期定义
type FreqCycle int64

const (
	FreqCycle_FOREVER FreqCycle = 0
	FreqCycle_HOUR    FreqCycle = 1
	FreqCycle_DAY     FreqCycle = 2
	FreqCycle_WEEK    FreqCycle = 3
	FreqCycle_MONTH   FreqCycle = 4
)

func (p FreqCycle) String() string {
	switch p {
	case FreqCycle_FOREVER:
		return "FreqCycle_FOREVER"
	case FreqCycle_HOUR:
		return "FreqCycle_HOUR"
	case FreqCycle_DAY:
		return "FreqCycle_DAY"
	case FreqCycle_WEEK:
		return "FreqCycle_WEEK"
	case FreqCycle_MONTH:
		return "FreqCycle_MONTH"
	}
	return "<UNSET>"
}

func FreqCycleFromString(s string) (FreqCycle, error) {
	switch s {
	case "FreqCycle_FOREVER":
		return FreqCycle_FOREVER, nil
	case "FreqCycle_HOUR":
		return FreqCycle_HOUR, nil
	case "FreqCycle_DAY":
		return FreqCycle_DAY, nil
	case "FreqCycle_WEEK":
		return FreqCycle_WEEK, nil
	case "FreqCycle_MONTH":
		return FreqCycle_MONTH, nil
	}
	return FreqCycle(math.MinInt32 - 1), fmt.Errorf("not a valid FreqCycle string")
}

//
type FreqType int64

const (
	FreqType_IMP FreqType = 0
	FreqType_CLK FreqType = 1
)

func (p FreqType) String() string {
	switch p {
	case FreqType_IMP:
		return "FreqType_IMP"
	case FreqType_CLK:
		return "FreqType_CLK"
	}
	return "<UNSET>"
}

func FreqTypeFromString(s string) (FreqType, error) {
	switch s {
	case "FreqType_IMP":
		return FreqType_IMP, nil
	case "FreqType_CLK":
		return FreqType_CLK, nil
	}
	return FreqType(math.MinInt32 - 1), fmt.Errorf("not a valid FreqType string")
}

//资源物料类型
type ResourceType int64

const (
	ResourceType_STREAMING_VIDEO ResourceType = 1
	ResourceType_MP4_VIDEO       ResourceType = 2
	ResourceType_AUDIO           ResourceType = 3
	ResourceType_IMAGE           ResourceType = 4
	ResourceType_APK             ResourceType = 5
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_STREAMING_VIDEO:
		return "ResourceType_STREAMING_VIDEO"
	case ResourceType_MP4_VIDEO:
		return "ResourceType_MP4_VIDEO"
	case ResourceType_AUDIO:
		return "ResourceType_AUDIO"
	case ResourceType_IMAGE:
		return "ResourceType_IMAGE"
	case ResourceType_APK:
		return "ResourceType_APK"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "ResourceType_STREAMING_VIDEO":
		return ResourceType_STREAMING_VIDEO, nil
	case "ResourceType_MP4_VIDEO":
		return ResourceType_MP4_VIDEO, nil
	case "ResourceType_AUDIO":
		return ResourceType_AUDIO, nil
	case "ResourceType_IMAGE":
		return ResourceType_IMAGE, nil
	case "ResourceType_APK":
		return ResourceType_APK, nil
	}
	return ResourceType(math.MinInt32 - 1), fmt.Errorf("not a valid ResourceType string")
}

//计费类型
type CostType int64

const ()

func (p CostType) String() string {
	switch p {
	}
	return "<UNSET>"
}

func CostTypeFromString(s string) (CostType, error) {
	switch s {
	}
	return CostType(math.MinInt32 - 1), fmt.Errorf("not a valid CostType string")
}

//iOS行业内部的产品线渠道分类
//保持与帷幄的定义一致
type ChannelType int64

const (
	ChannelType_WALL  ChannelType = 3
	ChannelType_VIDEO ChannelType = 6
	ChannelType_CEO   ChannelType = 5
)

func (p ChannelType) String() string {
	switch p {
	case ChannelType_WALL:
		return "ChannelType_WALL"
	case ChannelType_VIDEO:
		return "ChannelType_VIDEO"
	case ChannelType_CEO:
		return "ChannelType_CEO"
	}
	return "<UNSET>"
}

func ChannelTypeFromString(s string) (ChannelType, error) {
	switch s {
	case "ChannelType_WALL":
		return ChannelType_WALL, nil
	case "ChannelType_VIDEO":
		return ChannelType_VIDEO, nil
	case "ChannelType_CEO":
		return ChannelType_CEO, nil
	}
	return ChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid ChannelType string")
}

type LargeIdInt common.LargeIdInt

type UidInt common.UidInt

type MediaIdInt common.IdInt

type PlanIdInt common.IdInt

type CreativeIdInt common.IdInt

type ImgIdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type JailBreakCode common.JailBreakCode

type IdInt common.IdInt

type AccessTypeCode common.AccessTypeCode

type OwFeedback offerwall_types.OwFeedback

type OwAdActionType offerwall_types.OwAdActionType

type FreqInfo struct {
	FreqId    int32     `thrift:"freqId,1" json:"freqId"`
	Frequency int32     `thrift:"frequency,2" json:"frequency"`
	Cycle     FreqCycle `thrift:"cycle,3" json:"cycle"`
	TypeA1    FreqType  `thrift:"type,4" json:"type"`
}

func NewFreqInfo() *FreqInfo {
	return &FreqInfo{
		Cycle: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FreqInfo) IsSetCycle() bool {
	return int64(p.Cycle) != math.MinInt32-1
}

func (p *FreqInfo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *FreqInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FreqInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FreqId = v
	}
	return nil
}

func (p *FreqInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Frequency = v
	}
	return nil
}

func (p *FreqInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cycle = FreqCycle(v)
	}
	return nil
}

func (p *FreqInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = FreqType(v)
	}
	return nil
}

func (p *FreqInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FreqInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FreqInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("freqId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:freqId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FreqId)); err != nil {
		return fmt.Errorf("%T.freqId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:freqId: %s", p, err)
	}
	return err
}

func (p *FreqInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("frequency", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:frequency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Frequency)); err != nil {
		return fmt.Errorf("%T.frequency (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:frequency: %s", p, err)
	}
	return err
}

func (p *FreqInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCycle() {
		if err := oprot.WriteFieldBegin("cycle", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:cycle: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Cycle)); err != nil {
			return fmt.Errorf("%T.cycle (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:cycle: %s", p, err)
		}
	}
	return err
}

func (p *FreqInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *FreqInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FreqInfo(%+v)", *p)
}

type Resource struct {
	Rcid      int32        `thrift:"rcid,1" json:"rcid"`
	Url       string       `thrift:"url,2" json:"url"`
	Rctype    ResourceType `thrift:"rctype,3" json:"rctype"`
	Rgid      int32        `thrift:"rgid,4" json:"rgid"`
	PixelSize int32        `thrift:"pixelSize,5" json:"pixelSize"`
}

func NewResource() *Resource {
	return &Resource{
		Rctype: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Resource) IsSetRctype() bool {
	return int64(p.Rctype) != math.MinInt32-1
}

func (p *Resource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Resource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Rcid = v
	}
	return nil
}

func (p *Resource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Resource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rctype = ResourceType(v)
	}
	return nil
}

func (p *Resource) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Rgid = v
	}
	return nil
}

func (p *Resource) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PixelSize = v
	}
	return nil
}

func (p *Resource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Resource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Resource) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rcid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rcid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rcid)); err != nil {
		return fmt.Errorf("%T.rcid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rcid: %s", p, err)
	}
	return err
}

func (p *Resource) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:url: %s", p, err)
	}
	return err
}

func (p *Resource) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRctype() {
		if err := oprot.WriteFieldBegin("rctype", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:rctype: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Rctype)); err != nil {
			return fmt.Errorf("%T.rctype (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:rctype: %s", p, err)
		}
	}
	return err
}

func (p *Resource) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:rgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rgid)); err != nil {
		return fmt.Errorf("%T.rgid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:rgid: %s", p, err)
	}
	return err
}

func (p *Resource) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pixelSize", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pixelSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PixelSize)); err != nil {
		return fmt.Errorf("%T.pixelSize (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pixelSize: %s", p, err)
	}
	return err
}

func (p *Resource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Resource(%+v)", *p)
}

type ResourceGroup struct {
	Rgid         int32       `thrift:"rgid,1" json:"rgid"`
	ResourceList []*Resource `thrift:"resourceList,2" json:"resourceList"`
	RgName       string      `thrift:"rgName,3" json:"rgName"`
	ExpireTime   TimeInt     `thrift:"expireTime,4" json:"expireTime"`
}

func NewResourceGroup() *ResourceGroup {
	return &ResourceGroup{}
}

func (p *ResourceGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResourceGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Rgid = v
	}
	return nil
}

func (p *ResourceGroup) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResourceList = make([]*Resource, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewResource()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.ResourceList = append(p.ResourceList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResourceGroup) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RgName = v
	}
	return nil
}

func (p *ResourceGroup) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExpireTime = TimeInt(v)
	}
	return nil
}

func (p *ResourceGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResourceGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResourceGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:rgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rgid)); err != nil {
		return fmt.Errorf("%T.rgid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:rgid: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ResourceList != nil {
		if err := oprot.WriteFieldBegin("resourceList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:resourceList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResourceList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:resourceList: %s", p, err)
		}
	}
	return err
}

func (p *ResourceGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rgName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RgName)); err != nil {
		return fmt.Errorf("%T.rgName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rgName: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expireTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:expireTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExpireTime)); err != nil {
		return fmt.Errorf("%T.expireTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:expireTime: %s", p, err)
	}
	return err
}

func (p *ResourceGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceGroup(%+v)", *p)
}

type OwAdActionStage struct {
	Aid               IdInt  `thrift:"aid,1" json:"aid"`
	Action            int16  `thrift:"action,2" json:"action"`
	Rate              int16  `thrift:"rate,3" json:"rate"`
	Description       string `thrift:"description,4" json:"description"`
	StartTimeInterval int32  `thrift:"start_time_interval,5" json:"start_time_interval"`
	Duration          int32  `thrift:"duration,6" json:"duration"`
	StayTimeInterval  int32  `thrift:"stay_time_interval,7" json:"stay_time_interval"`
}

func NewOwAdActionStage() *OwAdActionStage {
	return &OwAdActionStage{}
}

func (p *OwAdActionStage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I16 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdActionStage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Aid = IdInt(v)
	}
	return nil
}

func (p *OwAdActionStage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *OwAdActionStage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rate = v
	}
	return nil
}

func (p *OwAdActionStage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *OwAdActionStage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTimeInterval = v
	}
	return nil
}

func (p *OwAdActionStage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *OwAdActionStage) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.StayTimeInterval = v
	}
	return nil
}

func (p *OwAdActionStage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdActionStage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdActionStage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:aid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Aid)); err != nil {
		return fmt.Errorf("%T.aid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:aid: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I16, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:action: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Action)); err != nil {
		return fmt.Errorf("%T.action (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:action: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rate", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rate: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Rate)); err != nil {
		return fmt.Errorf("%T.rate (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rate: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:description: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time_interval", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:start_time_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTimeInterval)); err != nil {
		return fmt.Errorf("%T.start_time_interval (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:start_time_interval: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:duration: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stay_time_interval", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:stay_time_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StayTimeInterval)); err != nil {
		return fmt.Errorf("%T.stay_time_interval (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:stay_time_interval: %s", p, err)
	}
	return err
}

func (p *OwAdActionStage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdActionStage(%+v)", *p)
}

type OwAdPlan struct {
	Sr                  int32       `thrift:"sr,1" json:"sr"`
	Mid                 MediaIdInt  `thrift:"mid,2" json:"mid"`
	AccessTarget        string      `thrift:"access_target,3" json:"access_target"`
	DeviceTarget        string      `thrift:"device_target,4" json:"device_target"`
	MediaTagTarget      string      `thrift:"media_tag_target,5" json:"media_tag_target"`
	BlackMediaTagTarget string      `thrift:"black_media_tag_target,6" json:"black_media_tag_target"`
	RegionTarget        string      `thrift:"region_target,7" json:"region_target"`
	OsTarget            string      `thrift:"os_target,8" json:"os_target"`
	Timeslots           string      `thrift:"timeslots,9" json:"timeslots"`
	Gamma               int32       `thrift:"gamma,10" json:"gamma"`
	Starttime           TimeInt     `thrift:"starttime,11" json:"starttime"`
	ClkNum              int32       `thrift:"clk_num,12" json:"clk_num"`
	ImpFreqList         []*FreqInfo `thrift:"impFreqList,13" json:"impFreqList"`
	ClkFreqList         []*FreqInfo `thrift:"clkFreqList,14" json:"clkFreqList"`
	OfferType           int16       `thrift:"offer_type,15" json:"offer_type"`
	CostType            int16       `thrift:"cost_type,16" json:"cost_type"`
	Platform            Platform    `thrift:"platform,17" json:"platform"`
	Dailybudget         int32       `thrift:"dailybudget,18" json:"dailybudget"`
	Dailyconsumed       int32       `thrift:"dailyconsumed,19" json:"dailyconsumed"`
	Endtime             TimeInt     `thrift:"endtime,20" json:"endtime"`
	MinSvTarget         int32       `thrift:"min_sv_target,21" json:"min_sv_target"`
	MaxSvTarget         int32       `thrift:"max_sv_target,22" json:"max_sv_target"`
	UniformUseBudget    bool        `thrift:"uniform_use_budget,23" json:"uniform_use_budget"`
	Pid                 int32       `thrift:"pid,24" json:"pid"`
	Name                string      `thrift:"name,25" json:"name"`
	Uid                 int32       `thrift:"uid,26" json:"uid"`
	Budget              int64       `thrift:"budget,27" json:"budget"`
	Status              int8        `thrift:"status,28" json:"status"`
	Paused              bool        `thrift:"paused,29" json:"paused"`
	TaskFlag            int16       `thrift:"task_flag,30" json:"task_flag"`
	SupplementFlag      int16       `thrift:"supplement_flag,31" json:"supplement_flag"`
	DeployStatus        int16       `thrift:"deploy_status,32" json:"deploy_status"`
	WhiteDeliveryTarget int16       `thrift:"white_delivery_target,33" json:"white_delivery_target"`
	CarrierTarget       string      `thrift:"carrier_target,34" json:"carrier_target"`
	ChannelType         ChannelType `thrift:"channelType,35" json:"channelType"`
	ClickLimit          int32       `thrift:"click_limit,36" json:"click_limit"`
	UserQualityTarget   string      `thrift:"user_quality_target,37" json:"user_quality_target"`
	UserPaymentTarget   string      `thrift:"user_payment_target,38" json:"user_payment_target"`
	ClickLimitType      int8        `thrift:"click_limit_type,39" json:"click_limit_type"`
	ClickLimitHourly    string      `thrift:"click_limit_hourly,40" json:"click_limit_hourly"`
	Chnid               int32       `thrift:"chnid,41" json:"chnid"`
	BlackDeliveryTarget int16       `thrift:"black_delivery_target,42" json:"black_delivery_target"`
	UserSourceTarget    string      `thrift:"user_source_target,43" json:"user_source_target"`
	OrderId             int32       `thrift:"order_id,44" json:"order_id"`
	ProjectSdId         int32       `thrift:"project_sd_id,45" json:"project_sd_id"`
}

func NewOwAdPlan() *OwAdPlan {
	return &OwAdPlan{
		Platform: math.MinInt32 - 1, // unset sentinal value

		ChannelType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAdPlan) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *OwAdPlan) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *OwAdPlan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I16 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I16 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I64 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I16 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I16 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I16 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I16 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I16 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I32 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.I32 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *OwAdPlan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *OwAdPlan) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccessTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DeviceTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MediaTagTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.BlackMediaTagTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.RegionTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OsTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Timeslots = v
	}
	return nil
}

func (p *OwAdPlan) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Gamma = v
	}
	return nil
}

func (p *OwAdPlan) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Starttime = TimeInt(v)
	}
	return nil
}

func (p *OwAdPlan) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ClkNum = v
	}
	return nil
}

func (p *OwAdPlan) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpFreqList = make([]*FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewFreqInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ImpFreqList = append(p.ImpFreqList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdPlan) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkFreqList = make([]*FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewFreqInfo()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.ClkFreqList = append(p.ClkFreqList, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdPlan) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *OwAdPlan) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwAdPlan) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *OwAdPlan) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Dailybudget = v
	}
	return nil
}

func (p *OwAdPlan) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Dailyconsumed = v
	}
	return nil
}

func (p *OwAdPlan) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Endtime = TimeInt(v)
	}
	return nil
}

func (p *OwAdPlan) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.MinSvTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.MaxSvTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.UniformUseBudget = v
	}
	return nil
}

func (p *OwAdPlan) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *OwAdPlan) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *OwAdPlan) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwAdPlan) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *OwAdPlan) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Status = int8(v)
	}
	return nil
}

func (p *OwAdPlan) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *OwAdPlan) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.TaskFlag = v
	}
	return nil
}

func (p *OwAdPlan) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.SupplementFlag = v
	}
	return nil
}

func (p *OwAdPlan) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.DeployStatus = v
	}
	return nil
}

func (p *OwAdPlan) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.WhiteDeliveryTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.CarrierTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.ChannelType = ChannelType(v)
	}
	return nil
}

func (p *OwAdPlan) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.ClickLimit = v
	}
	return nil
}

func (p *OwAdPlan) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.UserQualityTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.UserPaymentTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.ClickLimitType = int8(v)
	}
	return nil
}

func (p *OwAdPlan) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.ClickLimitHourly = v
	}
	return nil
}

func (p *OwAdPlan) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Chnid = v
	}
	return nil
}

func (p *OwAdPlan) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.BlackDeliveryTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.UserSourceTarget = v
	}
	return nil
}

func (p *OwAdPlan) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *OwAdPlan) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.ProjectSdId = v
	}
	return nil
}

func (p *OwAdPlan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdPlan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sr: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_target", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:access_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessTarget)); err != nil {
		return fmt.Errorf("%T.access_target (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:access_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_target", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:device_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceTarget)); err != nil {
		return fmt.Errorf("%T.device_target (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:device_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_tag_target", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:media_tag_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaTagTarget)); err != nil {
		return fmt.Errorf("%T.media_tag_target (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:media_tag_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("black_media_tag_target", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:black_media_tag_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BlackMediaTagTarget)); err != nil {
		return fmt.Errorf("%T.black_media_tag_target (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:black_media_tag_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_target", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:region_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RegionTarget)); err != nil {
		return fmt.Errorf("%T.region_target (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:region_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_target", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:os_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsTarget)); err != nil {
		return fmt.Errorf("%T.os_target (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:os_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timeslots", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:timeslots: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Timeslots)); err != nil {
		return fmt.Errorf("%T.timeslots (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:timeslots: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gamma", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:gamma: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Gamma)); err != nil {
		return fmt.Errorf("%T.gamma (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:gamma: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("starttime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:starttime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Starttime)); err != nil {
		return fmt.Errorf("%T.starttime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:starttime: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_num", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:clk_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkNum)); err != nil {
		return fmt.Errorf("%T.clk_num (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:clk_num: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField13(oprot thrift.TProtocol) (err error) {
	if p.ImpFreqList != nil {
		if err := oprot.WriteFieldBegin("impFreqList", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:impFreqList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImpFreqList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpFreqList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:impFreqList: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlan) writeField14(oprot thrift.TProtocol) (err error) {
	if p.ClkFreqList != nil {
		if err := oprot.WriteFieldBegin("clkFreqList", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:clkFreqList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ClkFreqList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkFreqList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:clkFreqList: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlan) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I16, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:offer_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:offer_type: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I16, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:cost_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:cost_type: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (17) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:platform: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlan) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailybudget", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:dailybudget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dailybudget)); err != nil {
		return fmt.Errorf("%T.dailybudget (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:dailybudget: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyconsumed", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:dailyconsumed: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dailyconsumed)); err != nil {
		return fmt.Errorf("%T.dailyconsumed (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:dailyconsumed: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endtime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:endtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Endtime)); err != nil {
		return fmt.Errorf("%T.endtime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:endtime: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("min_sv_target", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:min_sv_target: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinSvTarget)); err != nil {
		return fmt.Errorf("%T.min_sv_target (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:min_sv_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_sv_target", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:max_sv_target: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxSvTarget)); err != nil {
		return fmt.Errorf("%T.max_sv_target (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:max_sv_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uniform_use_budget", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:uniform_use_budget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UniformUseBudget)); err != nil {
		return fmt.Errorf("%T.uniform_use_budget (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:uniform_use_budget: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:pid: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:name: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:uid: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:budget: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BYTE, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:status: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Status)); err != nil {
		return fmt.Errorf("%T.status (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:status: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.BOOL, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:paused: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:paused: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_flag", thrift.I16, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:task_flag: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.TaskFlag)); err != nil {
		return fmt.Errorf("%T.task_flag (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:task_flag: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("supplement_flag", thrift.I16, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:supplement_flag: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.SupplementFlag)); err != nil {
		return fmt.Errorf("%T.supplement_flag (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:supplement_flag: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deploy_status", thrift.I16, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:deploy_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.DeployStatus)); err != nil {
		return fmt.Errorf("%T.deploy_status (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:deploy_status: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("white_delivery_target", thrift.I16, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:white_delivery_target: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.WhiteDeliveryTarget)); err != nil {
		return fmt.Errorf("%T.white_delivery_target (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:white_delivery_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_target", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:carrier_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CarrierTarget)); err != nil {
		return fmt.Errorf("%T.carrier_target (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:carrier_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField35(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannelType() {
		if err := oprot.WriteFieldBegin("channelType", thrift.I32, 35); err != nil {
			return fmt.Errorf("%T write field begin error 35:channelType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
			return fmt.Errorf("%T.channelType (35) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 35:channelType: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlan) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_limit", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:click_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClickLimit)); err != nil {
		return fmt.Errorf("%T.click_limit (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:click_limit: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_quality_target", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:user_quality_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserQualityTarget)); err != nil {
		return fmt.Errorf("%T.user_quality_target (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:user_quality_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_payment_target", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:user_payment_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserPaymentTarget)); err != nil {
		return fmt.Errorf("%T.user_payment_target (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:user_payment_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_limit_type", thrift.BYTE, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:click_limit_type: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.ClickLimitType)); err != nil {
		return fmt.Errorf("%T.click_limit_type (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:click_limit_type: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_limit_hourly", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:click_limit_hourly: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickLimitHourly)); err != nil {
		return fmt.Errorf("%T.click_limit_hourly (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:click_limit_hourly: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnid", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:chnid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Chnid)); err != nil {
		return fmt.Errorf("%T.chnid (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:chnid: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("black_delivery_target", thrift.I16, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:black_delivery_target: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.BlackDeliveryTarget)); err != nil {
		return fmt.Errorf("%T.black_delivery_target (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:black_delivery_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_source_target", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:user_source_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserSourceTarget)); err != nil {
		return fmt.Errorf("%T.user_source_target (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:user_source_target: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("order_id", thrift.I32, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:order_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.order_id (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:order_id: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("project_sd_id", thrift.I32, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:project_sd_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProjectSdId)); err != nil {
		return fmt.Errorf("%T.project_sd_id (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:project_sd_id: %s", p, err)
	}
	return err
}

func (p *OwAdPlan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdPlan(%+v)", *p)
}

type OwAdCountdown struct {
	Starttime TimeInt `thrift:"starttime,1" json:"starttime"`
	Endtime   TimeInt `thrift:"endtime,2" json:"endtime"`
	Price     Amount  `thrift:"price,3" json:"price"`
	Status    int16   `thrift:"status,4" json:"status"`
}

func NewOwAdCountdown() *OwAdCountdown {
	return &OwAdCountdown{}
}

func (p *OwAdCountdown) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdCountdown) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Starttime = TimeInt(v)
	}
	return nil
}

func (p *OwAdCountdown) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Endtime = TimeInt(v)
	}
	return nil
}

func (p *OwAdCountdown) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *OwAdCountdown) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwAdCountdown) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdCountdown"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdCountdown) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("starttime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:starttime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Starttime)); err != nil {
		return fmt.Errorf("%T.starttime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:starttime: %s", p, err)
	}
	return err
}

func (p *OwAdCountdown) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endtime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Endtime)); err != nil {
		return fmt.Errorf("%T.endtime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endtime: %s", p, err)
	}
	return err
}

func (p *OwAdCountdown) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:price: %s", p, err)
	}
	return err
}

func (p *OwAdCountdown) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:status: %s", p, err)
	}
	return err
}

func (p *OwAdCountdown) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdCountdown(%+v)", *p)
}

type OwAdCreative struct {
	Cid                  CreativeIdInt      `thrift:"cid,1" json:"cid"`
	Pid                  PlanIdInt          `thrift:"pid,2" json:"pid"`
	Uid                  UidInt             `thrift:"uid,3" json:"uid"`
	Pkgid                IdInt              `thrift:"pkgid,4" json:"pkgid"`
	Name                 string             `thrift:"name,5" json:"name"`
	Logo                 ImgIdInt           `thrift:"logo,6" json:"logo"`
	Description          string             `thrift:"description,7" json:"description"`
	Price                Amount             `thrift:"price,8" json:"price"`
	Point                Amount             `thrift:"point,9" json:"point"`
	Feedback             OwFeedback         `thrift:"feedback,10" json:"feedback"`
	Url                  string             `thrift:"url,11" json:"url"`
	Detail               string             `thrift:"detail,12" json:"detail"`
	Nopoint              bool               `thrift:"nopoint,13" json:"nopoint"`
	ActionType           OwAdActionType     `thrift:"action_type,14" json:"action_type"`
	Text                 string             `thrift:"text,15" json:"text"`
	SpPrice              Amount             `thrift:"sp_price,16" json:"sp_price"`
	ResourceGroup        *ResourceGroup     `thrift:"resourceGroup,17" json:"resourceGroup"`
	Actions              []*OwAdActionStage `thrift:"actions,18" json:"actions"`
	DisplayName          string             `thrift:"display_name,19" json:"display_name"`
	ClickButtonName      string             `thrift:"click_button_name,20" json:"click_button_name"`
	UrlActionType        int8               `thrift:"url_action_type,21" json:"url_action_type"`
	Screenshots          string             `thrift:"screenshots,22" json:"screenshots"`
	CornerMark           int32              `thrift:"corner_mark,23" json:"corner_mark"`
	AppDescription       string             `thrift:"app_description,24" json:"app_description"`
	LaunchPrompt         string             `thrift:"launch_prompt,25" json:"launch_prompt"`
	VideoMinImpTime      int32              `thrift:"video_min_imp_time,26" json:"video_min_imp_time"`
	VideoShowOnList      int8               `thrift:"video_show_on_list,27" json:"video_show_on_list"`
	VideoImpTracker      string             `thrift:"video_imp_tracker,28" json:"video_imp_tracker"`
	RatingLevel          int32              `thrift:"rating_level,29" json:"rating_level"`
	RatingCount          int32              `thrift:"rating_count,30" json:"rating_count"`
	TemplateId           int16              `thrift:"template_id,31" json:"template_id"`
	InterstitialStatus   int16              `thrift:"interstitial_status,32" json:"interstitial_status"`
	InterstitialTemplate int32              `thrift:"interstitial_template,33" json:"interstitial_template"`
	Countdowns           []*OwAdCountdown   `thrift:"countdowns,34" json:"countdowns"`
	CreativeType         int8               `thrift:"creative_type,35" json:"creative_type"`
	SearchQuery          string             `thrift:"search_query,36" json:"search_query"`
	SearchRank           int16              `thrift:"search_rank,37" json:"search_rank"`
	VideoAdImg           int32              `thrift:"video_ad_img,38" json:"video_ad_img"`
	ManualTags           string             `thrift:"manual_tags,39" json:"manual_tags"`
	VideoTemplateId      int32              `thrift:"video_template_id,40" json:"video_template_id"`
	VideoPosterId        string             `thrift:"video_poster_id,41" json:"video_poster_id"`
	VideoCostTarget      int64              `thrift:"video_cost_target,42" json:"video_cost_target"`
	SubTaskEnable        bool               `thrift:"sub_task_enable,43" json:"sub_task_enable"`
}

func NewOwAdCreative() *OwAdCreative {
	return &OwAdCreative{
		Feedback: math.MinInt32 - 1, // unset sentinal value

		ActionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwAdCreative) IsSetFeedback() bool {
	return int64(p.Feedback) != math.MinInt32-1
}

func (p *OwAdCreative) IsSetActionType() bool {
	return int64(p.ActionType) != math.MinInt32-1
}

func (p *OwAdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I16 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I16 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.LIST {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I16 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Cid = CreativeIdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = PlanIdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Pkgid = IdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *OwAdCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Logo = ImgIdInt(v)
	}
	return nil
}

func (p *OwAdCreative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *OwAdCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Point = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Feedback = OwFeedback(v)
	}
	return nil
}

func (p *OwAdCreative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *OwAdCreative) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *OwAdCreative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Nopoint = v
	}
	return nil
}

func (p *OwAdCreative) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActionType = OwAdActionType(v)
	}
	return nil
}

func (p *OwAdCreative) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *OwAdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.SpPrice = Amount(v)
	}
	return nil
}

func (p *OwAdCreative) readField17(iprot thrift.TProtocol) error {
	p.ResourceGroup = NewResourceGroup()
	if err := p.ResourceGroup.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ResourceGroup)
	}
	return nil
}

func (p *OwAdCreative) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Actions = make([]*OwAdActionStage, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewOwAdActionStage()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Actions = append(p.Actions, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdCreative) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *OwAdCreative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ClickButtonName = v
	}
	return nil
}

func (p *OwAdCreative) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.UrlActionType = int8(v)
	}
	return nil
}

func (p *OwAdCreative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Screenshots = v
	}
	return nil
}

func (p *OwAdCreative) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.CornerMark = v
	}
	return nil
}

func (p *OwAdCreative) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.AppDescription = v
	}
	return nil
}

func (p *OwAdCreative) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.LaunchPrompt = v
	}
	return nil
}

func (p *OwAdCreative) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.VideoMinImpTime = v
	}
	return nil
}

func (p *OwAdCreative) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.VideoShowOnList = int8(v)
	}
	return nil
}

func (p *OwAdCreative) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.VideoImpTracker = v
	}
	return nil
}

func (p *OwAdCreative) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.RatingLevel = v
	}
	return nil
}

func (p *OwAdCreative) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.RatingCount = v
	}
	return nil
}

func (p *OwAdCreative) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *OwAdCreative) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.InterstitialStatus = v
	}
	return nil
}

func (p *OwAdCreative) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.InterstitialTemplate = v
	}
	return nil
}

func (p *OwAdCreative) readField34(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Countdowns = make([]*OwAdCountdown, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewOwAdCountdown()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.Countdowns = append(p.Countdowns, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdCreative) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CreativeType = int8(v)
	}
	return nil
}

func (p *OwAdCreative) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.SearchQuery = v
	}
	return nil
}

func (p *OwAdCreative) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.SearchRank = v
	}
	return nil
}

func (p *OwAdCreative) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.VideoAdImg = v
	}
	return nil
}

func (p *OwAdCreative) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.ManualTags = v
	}
	return nil
}

func (p *OwAdCreative) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.VideoTemplateId = v
	}
	return nil
}

func (p *OwAdCreative) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.VideoPosterId = v
	}
	return nil
}

func (p *OwAdCreative) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.VideoCostTarget = v
	}
	return nil
}

func (p *OwAdCreative) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.SubTaskEnable = v
	}
	return nil
}

func (p *OwAdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pkgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pkgid)); err != nil {
		return fmt.Errorf("%T.pkgid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pkgid: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:logo: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:description: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:price: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:point: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("feedback", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:feedback: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Feedback)); err != nil {
		return fmt.Errorf("%T.feedback (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:feedback: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:url: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:detail: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nopoint", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:nopoint: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Nopoint)); err != nil {
		return fmt.Errorf("%T.nopoint (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:nopoint: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_type", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.action_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:action_type: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:text: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sp_price: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField17(oprot thrift.TProtocol) (err error) {
	if p.ResourceGroup != nil {
		if err := oprot.WriteFieldBegin("resourceGroup", thrift.STRUCT, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:resourceGroup: %s", p, err)
		}
		if err := p.ResourceGroup.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ResourceGroup)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:resourceGroup: %s", p, err)
		}
	}
	return err
}

func (p *OwAdCreative) writeField18(oprot thrift.TProtocol) (err error) {
	if p.Actions != nil {
		if err := oprot.WriteFieldBegin("actions", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:actions: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Actions)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Actions {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:actions: %s", p, err)
		}
	}
	return err
}

func (p *OwAdCreative) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_name", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:display_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.display_name (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:display_name: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_button_name", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:click_button_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickButtonName)); err != nil {
		return fmt.Errorf("%T.click_button_name (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:click_button_name: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url_action_type", thrift.BYTE, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:url_action_type: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.UrlActionType)); err != nil {
		return fmt.Errorf("%T.url_action_type (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:url_action_type: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenshots", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:screenshots: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Screenshots)); err != nil {
		return fmt.Errorf("%T.screenshots (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:screenshots: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("corner_mark", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:corner_mark: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CornerMark)); err != nil {
		return fmt.Errorf("%T.corner_mark (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:corner_mark: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_description", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:app_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppDescription)); err != nil {
		return fmt.Errorf("%T.app_description (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:app_description: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("launch_prompt", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:launch_prompt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LaunchPrompt)); err != nil {
		return fmt.Errorf("%T.launch_prompt (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:launch_prompt: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_min_imp_time", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:video_min_imp_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoMinImpTime)); err != nil {
		return fmt.Errorf("%T.video_min_imp_time (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:video_min_imp_time: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_show_on_list", thrift.BYTE, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:video_show_on_list: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.VideoShowOnList)); err != nil {
		return fmt.Errorf("%T.video_show_on_list (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:video_show_on_list: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_imp_tracker", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:video_imp_tracker: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoImpTracker)); err != nil {
		return fmt.Errorf("%T.video_imp_tracker (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:video_imp_tracker: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rating_level", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:rating_level: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RatingLevel)); err != nil {
		return fmt.Errorf("%T.rating_level (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:rating_level: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rating_count", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:rating_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RatingCount)); err != nil {
		return fmt.Errorf("%T.rating_count (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:rating_count: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I16, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:template_id: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:template_id: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("interstitial_status", thrift.I16, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:interstitial_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.InterstitialStatus)); err != nil {
		return fmt.Errorf("%T.interstitial_status (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:interstitial_status: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("interstitial_template", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:interstitial_template: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.InterstitialTemplate)); err != nil {
		return fmt.Errorf("%T.interstitial_template (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:interstitial_template: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField34(oprot thrift.TProtocol) (err error) {
	if p.Countdowns != nil {
		if err := oprot.WriteFieldBegin("countdowns", thrift.LIST, 34); err != nil {
			return fmt.Errorf("%T write field begin error 34:countdowns: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Countdowns)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Countdowns {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 34:countdowns: %s", p, err)
		}
	}
	return err
}

func (p *OwAdCreative) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_type", thrift.BYTE, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:creative_type: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.CreativeType)); err != nil {
		return fmt.Errorf("%T.creative_type (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:creative_type: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_query", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:search_query: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SearchQuery)); err != nil {
		return fmt.Errorf("%T.search_query (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:search_query: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_rank", thrift.I16, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:search_rank: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.SearchRank)); err != nil {
		return fmt.Errorf("%T.search_rank (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:search_rank: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_ad_img", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:video_ad_img: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoAdImg)); err != nil {
		return fmt.Errorf("%T.video_ad_img (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:video_ad_img: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("manual_tags", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:manual_tags: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ManualTags)); err != nil {
		return fmt.Errorf("%T.manual_tags (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:manual_tags: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_template_id", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:video_template_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoTemplateId)); err != nil {
		return fmt.Errorf("%T.video_template_id (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:video_template_id: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_poster_id", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:video_poster_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoPosterId)); err != nil {
		return fmt.Errorf("%T.video_poster_id (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:video_poster_id: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_cost_target", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:video_cost_target: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.VideoCostTarget)); err != nil {
		return fmt.Errorf("%T.video_cost_target (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:video_cost_target: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sub_task_enable", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:sub_task_enable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SubTaskEnable)); err != nil {
		return fmt.Errorf("%T.sub_task_enable (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:sub_task_enable: %s", p, err)
	}
	return err
}

func (p *OwAdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdCreative(%+v)", *p)
}

type OwPackage struct {
	Name           string `thrift:"name,1" json:"name"`
	Version        string `thrift:"version,2" json:"version"`
	Appid          string `thrift:"appid,3" json:"appid"`
	Size           int64  `thrift:"size,4" json:"size"`
	Url            string `thrift:"url,5" json:"url"`
	ItunesId       int64  `thrift:"itunes_id,6" json:"itunes_id"`
	UrlSchemes     string `thrift:"url_schemes,7" json:"url_schemes"`
	ExecutableFile string `thrift:"executable_file,8" json:"executable_file"`
	AppName        string `thrift:"app_name,9" json:"app_name"`
}

func NewOwPackage() *OwPackage {
	return &OwPackage{}
}

func (p *OwPackage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwPackage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *OwPackage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *OwPackage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *OwPackage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *OwPackage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *OwPackage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *OwPackage) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.UrlSchemes = v
	}
	return nil
}

func (p *OwPackage) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ExecutableFile = v
	}
	return nil
}

func (p *OwPackage) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *OwPackage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwPackage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwPackage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:version: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appid: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:url: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunes_id", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:itunes_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunes_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:itunes_id: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url_schemes", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:url_schemes: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UrlSchemes)); err != nil {
		return fmt.Errorf("%T.url_schemes (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:url_schemes: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("executable_file", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:executable_file: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExecutableFile)); err != nil {
		return fmt.Errorf("%T.executable_file (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:executable_file: %s", p, err)
	}
	return err
}

func (p *OwPackage) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_name", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.app_name (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:app_name: %s", p, err)
	}
	return err
}

func (p *OwPackage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwPackage(%+v)", *p)
}

type OwAdSubTask struct {
	Id               int32  `thrift:"id,1" json:"id"`
	TaskType         int16  `thrift:"task_type,2" json:"task_type"`
	Price            int64  `thrift:"price,3" json:"price"`
	StartDayInterval int32  `thrift:"start_day_interval,4" json:"start_day_interval"`
	Duration         int32  `thrift:"duration,5" json:"duration"`
	Description      string `thrift:"description,6" json:"description"`
	Play             string `thrift:"play,7" json:"play"`
	Status           int16  `thrift:"status,8" json:"status"`
	ClickLimit       int32  `thrift:"click_limit,9" json:"click_limit"`
}

func NewOwAdSubTask() *OwAdSubTask {
	return &OwAdSubTask{}
}

func (p *OwAdSubTask) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I16 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I16 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdSubTask) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *OwAdSubTask) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskType = v
	}
	return nil
}

func (p *OwAdSubTask) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *OwAdSubTask) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartDayInterval = v
	}
	return nil
}

func (p *OwAdSubTask) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *OwAdSubTask) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *OwAdSubTask) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Play = v
	}
	return nil
}

func (p *OwAdSubTask) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwAdSubTask) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClickLimit = v
	}
	return nil
}

func (p *OwAdSubTask) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdSubTask"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdSubTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_type", thrift.I16, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.TaskType)); err != nil {
		return fmt.Errorf("%T.task_type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_type: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:price: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_day_interval", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:start_day_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartDayInterval)); err != nil {
		return fmt.Errorf("%T.start_day_interval (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:start_day_interval: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:duration: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:description: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("play", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:play: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Play)); err != nil {
		return fmt.Errorf("%T.play (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:play: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:status: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:click_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClickLimit)); err != nil {
		return fmt.Errorf("%T.click_limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:click_limit: %s", p, err)
	}
	return err
}

func (p *OwAdSubTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdSubTask(%+v)", *p)
}

type OwOffer struct {
	Plan       *OwAdPlan      `thrift:"plan,1" json:"plan"`
	Creative   *OwAdCreative  `thrift:"creative,2" json:"creative"`
	Pkg        *OwPackage     `thrift:"pkg,3" json:"pkg"`
	ClkSendurl string         `thrift:"clk_sendurl,4" json:"clk_sendurl"`
	SubTasks   []*OwAdSubTask `thrift:"subTasks,5" json:"subTasks"`
}

func NewOwOffer() *OwOffer {
	return &OwOffer{}
}

func (p *OwOffer) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwOffer) readField1(iprot thrift.TProtocol) error {
	p.Plan = NewOwAdPlan()
	if err := p.Plan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Plan)
	}
	return nil
}

func (p *OwOffer) readField2(iprot thrift.TProtocol) error {
	p.Creative = NewOwAdCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *OwOffer) readField3(iprot thrift.TProtocol) error {
	p.Pkg = NewOwPackage()
	if err := p.Pkg.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pkg)
	}
	return nil
}

func (p *OwOffer) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ClkSendurl = v
	}
	return nil
}

func (p *OwOffer) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SubTasks = make([]*OwAdSubTask, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewOwAdSubTask()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.SubTasks = append(p.SubTasks, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwOffer) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwOffer"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwOffer) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Plan != nil {
		if err := oprot.WriteFieldBegin("plan", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:plan: %s", p, err)
		}
		if err := p.Plan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Plan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:plan: %s", p, err)
		}
	}
	return err
}

func (p *OwOffer) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:creative: %s", p, err)
		}
	}
	return err
}

func (p *OwOffer) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Pkg != nil {
		if err := oprot.WriteFieldBegin("pkg", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:pkg: %s", p, err)
		}
		if err := p.Pkg.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pkg)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:pkg: %s", p, err)
		}
	}
	return err
}

func (p *OwOffer) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_sendurl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:clk_sendurl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkSendurl)); err != nil {
		return fmt.Errorf("%T.clk_sendurl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:clk_sendurl: %s", p, err)
	}
	return err
}

func (p *OwOffer) writeField5(oprot thrift.TProtocol) (err error) {
	if p.SubTasks != nil {
		if err := oprot.WriteFieldBegin("subTasks", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:subTasks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubTasks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SubTasks {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:subTasks: %s", p, err)
		}
	}
	return err
}

func (p *OwOffer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwOffer(%+v)", *p)
}

type OwMediaInfo struct {
	Mid                    IdInt    `thrift:"mid,1" json:"mid"`
	Uid                    IdInt    `thrift:"uid,2" json:"uid"`
	Name                   string   `thrift:"name,3" json:"name"`
	Status                 int16    `thrift:"status,4" json:"status"`
	Paused                 int16    `thrift:"paused,5" json:"paused"`
	Platform               Platform `thrift:"platform,6" json:"platform"`
	CurrencyName           string   `thrift:"currency_name,7" json:"currency_name"`
	ExchangeRate           int32    `thrift:"exchange_rate,8" json:"exchange_rate"`
	MediashareRate         int32    `thrift:"mediashare_rate,9" json:"mediashare_rate"`
	Sr                     int16    `thrift:"sr,10" json:"sr"`
	CidSequence            string   `thrift:"cid_sequence,11" json:"cid_sequence"`
	Placement              int32    `thrift:"placement,12" json:"placement"`
	InterstitialPriority   int16    `thrift:"interstitial_priority,13" json:"interstitial_priority"`
	PackageUgcId           int32    `thrift:"package_ugc_id,14" json:"package_ugc_id"`
	PackageName            string   `thrift:"package_name,15" json:"package_name"`
	ShowVideoOnList        int16    `thrift:"show_video_on_list,16" json:"show_video_on_list"`
	MediaTagGroup          []int32  `thrift:"mediaTagGroup,17" json:"mediaTagGroup"`
	VideoStatus            int16    `thrift:"video_status,18" json:"video_status"`
	IsLocation             int16    `thrift:"is_location,19" json:"is_location"`
	IsVideoToList          int16    `thrift:"is_video_to_list,20" json:"is_video_to_list"`
	IaiEnable              int16    `thrift:"iai_enable,21" json:"iai_enable"`
	AdFilter               string   `thrift:"ad_filter,22" json:"ad_filter"`
	AdFilterStatus         bool     `thrift:"ad_filter_status,23" json:"ad_filter_status"`
	ScoreBalance           bool     `thrift:"score_balance,24" json:"score_balance"`
	MediaType              int16    `thrift:"media_type,25" json:"media_type"`
	VideoEcpm              Amount   `thrift:"video_ecpm,26" json:"video_ecpm"`
	VideoFinishPoint       int32    `thrift:"video_finish_point,27" json:"video_finish_point"`
	ActProportion          int16    `thrift:"act_proportion,28" json:"act_proportion"`
	UserBanned             bool     `thrift:"user_banned,29" json:"user_banned"`
	WxUserRate             int16    `thrift:"wx_user_rate,30" json:"wx_user_rate"`
	WxMediaType            int16    `thrift:"wx_media_type,31" json:"wx_media_type"`
	WxMediaAuthenticate    bool     `thrift:"wx_media_authenticate,32" json:"wx_media_authenticate"`
	WxLottery              bool     `thrift:"wx_lottery,33" json:"wx_lottery"`
	WxCustomMall           bool     `thrift:"wx_custom_mall,34" json:"wx_custom_mall"`
	VideoChargeType        int8     `thrift:"video_charge_type,35" json:"video_charge_type"`
	VideoFloorPrice        Amount   `thrift:"video_floor_price,36" json:"video_floor_price"`
	VideoProfitProtectRate int16    `thrift:"video_profit_protect_rate,37" json:"video_profit_protect_rate"`
}

func NewOwMediaInfo() *OwMediaInfo {
	return &OwMediaInfo{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwMediaInfo) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *OwMediaInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I16 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I16 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I16 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I16 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I16 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I16 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I16 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I16 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I16 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I16 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I16 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I16 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I16 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMediaInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = IdInt(v)
	}
	return nil
}

func (p *OwMediaInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = IdInt(v)
	}
	return nil
}

func (p *OwMediaInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *OwMediaInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwMediaInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *OwMediaInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *OwMediaInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CurrencyName = v
	}
	return nil
}

func (p *OwMediaInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ExchangeRate = v
	}
	return nil
}

func (p *OwMediaInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.MediashareRate = v
	}
	return nil
}

func (p *OwMediaInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *OwMediaInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CidSequence = v
	}
	return nil
}

func (p *OwMediaInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Placement = v
	}
	return nil
}

func (p *OwMediaInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.InterstitialPriority = v
	}
	return nil
}

func (p *OwMediaInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PackageUgcId = v
	}
	return nil
}

func (p *OwMediaInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *OwMediaInfo) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ShowVideoOnList = v
	}
	return nil
}

func (p *OwMediaInfo) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaTagGroup = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.MediaTagGroup = append(p.MediaTagGroup, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwMediaInfo) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.VideoStatus = v
	}
	return nil
}

func (p *OwMediaInfo) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.IsLocation = v
	}
	return nil
}

func (p *OwMediaInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.IsVideoToList = v
	}
	return nil
}

func (p *OwMediaInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.IaiEnable = v
	}
	return nil
}

func (p *OwMediaInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AdFilter = v
	}
	return nil
}

func (p *OwMediaInfo) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.AdFilterStatus = v
	}
	return nil
}

func (p *OwMediaInfo) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.ScoreBalance = v
	}
	return nil
}

func (p *OwMediaInfo) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.MediaType = v
	}
	return nil
}

func (p *OwMediaInfo) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.VideoEcpm = Amount(v)
	}
	return nil
}

func (p *OwMediaInfo) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.VideoFinishPoint = v
	}
	return nil
}

func (p *OwMediaInfo) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.ActProportion = v
	}
	return nil
}

func (p *OwMediaInfo) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.UserBanned = v
	}
	return nil
}

func (p *OwMediaInfo) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.WxUserRate = v
	}
	return nil
}

func (p *OwMediaInfo) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.WxMediaType = v
	}
	return nil
}

func (p *OwMediaInfo) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.WxMediaAuthenticate = v
	}
	return nil
}

func (p *OwMediaInfo) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.WxLottery = v
	}
	return nil
}

func (p *OwMediaInfo) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.WxCustomMall = v
	}
	return nil
}

func (p *OwMediaInfo) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.VideoChargeType = int8(v)
	}
	return nil
}

func (p *OwMediaInfo) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.VideoFloorPrice = Amount(v)
	}
	return nil
}

func (p *OwMediaInfo) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.VideoProfitProtectRate = v
	}
	return nil
}

func (p *OwMediaInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMediaInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMediaInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:status: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I16, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:paused: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:paused: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:platform: %s", p, err)
		}
	}
	return err
}

func (p *OwMediaInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currency_name", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:currency_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrencyName)); err != nil {
		return fmt.Errorf("%T.currency_name (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:currency_name: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_rate", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:exchange_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeRate)); err != nil {
		return fmt.Errorf("%T.exchange_rate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:exchange_rate: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare_rate", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:mediashare_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediashareRate)); err != nil {
		return fmt.Errorf("%T.mediashare_rate (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:mediashare_rate: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:sr: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid_sequence", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:cid_sequence: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CidSequence)); err != nil {
		return fmt.Errorf("%T.cid_sequence (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:cid_sequence: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placement", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:placement: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Placement)); err != nil {
		return fmt.Errorf("%T.placement (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:placement: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("interstitial_priority", thrift.I16, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:interstitial_priority: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.InterstitialPriority)); err != nil {
		return fmt.Errorf("%T.interstitial_priority (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:interstitial_priority: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_ugc_id", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:package_ugc_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageUgcId)); err != nil {
		return fmt.Errorf("%T.package_ugc_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:package_ugc_id: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:package_name: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("show_video_on_list", thrift.I16, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:show_video_on_list: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ShowVideoOnList)); err != nil {
		return fmt.Errorf("%T.show_video_on_list (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:show_video_on_list: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if p.MediaTagGroup != nil {
		if err := oprot.WriteFieldBegin("mediaTagGroup", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:mediaTagGroup: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaTagGroup)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaTagGroup {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:mediaTagGroup: %s", p, err)
		}
	}
	return err
}

func (p *OwMediaInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_status", thrift.I16, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:video_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.VideoStatus)); err != nil {
		return fmt.Errorf("%T.video_status (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:video_status: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_location", thrift.I16, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:is_location: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.IsLocation)); err != nil {
		return fmt.Errorf("%T.is_location (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:is_location: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_video_to_list", thrift.I16, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:is_video_to_list: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.IsVideoToList)); err != nil {
		return fmt.Errorf("%T.is_video_to_list (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:is_video_to_list: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iai_enable", thrift.I16, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:iai_enable: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.IaiEnable)); err != nil {
		return fmt.Errorf("%T.iai_enable (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:iai_enable: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_filter", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:ad_filter: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdFilter)); err != nil {
		return fmt.Errorf("%T.ad_filter (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:ad_filter: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_filter_status", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:ad_filter_status: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdFilterStatus)); err != nil {
		return fmt.Errorf("%T.ad_filter_status (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:ad_filter_status: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("score_balance", thrift.BOOL, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:score_balance: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ScoreBalance)); err != nil {
		return fmt.Errorf("%T.score_balance (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:score_balance: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_type", thrift.I16, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:media_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.MediaType)); err != nil {
		return fmt.Errorf("%T.media_type (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:media_type: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_ecpm", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:video_ecpm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.VideoEcpm)); err != nil {
		return fmt.Errorf("%T.video_ecpm (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:video_ecpm: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_finish_point", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:video_finish_point: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoFinishPoint)); err != nil {
		return fmt.Errorf("%T.video_finish_point (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:video_finish_point: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_proportion", thrift.I16, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:act_proportion: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ActProportion)); err != nil {
		return fmt.Errorf("%T.act_proportion (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:act_proportion: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_banned", thrift.BOOL, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:user_banned: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UserBanned)); err != nil {
		return fmt.Errorf("%T.user_banned (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:user_banned: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wx_user_rate", thrift.I16, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:wx_user_rate: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.WxUserRate)); err != nil {
		return fmt.Errorf("%T.wx_user_rate (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:wx_user_rate: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wx_media_type", thrift.I16, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:wx_media_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.WxMediaType)); err != nil {
		return fmt.Errorf("%T.wx_media_type (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:wx_media_type: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wx_media_authenticate", thrift.BOOL, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:wx_media_authenticate: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WxMediaAuthenticate)); err != nil {
		return fmt.Errorf("%T.wx_media_authenticate (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:wx_media_authenticate: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wx_lottery", thrift.BOOL, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:wx_lottery: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WxLottery)); err != nil {
		return fmt.Errorf("%T.wx_lottery (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:wx_lottery: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wx_custom_mall", thrift.BOOL, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:wx_custom_mall: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WxCustomMall)); err != nil {
		return fmt.Errorf("%T.wx_custom_mall (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:wx_custom_mall: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_charge_type", thrift.BYTE, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:video_charge_type: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.VideoChargeType)); err != nil {
		return fmt.Errorf("%T.video_charge_type (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:video_charge_type: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_floor_price", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:video_floor_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.VideoFloorPrice)); err != nil {
		return fmt.Errorf("%T.video_floor_price (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:video_floor_price: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_profit_protect_rate", thrift.I16, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:video_profit_protect_rate: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.VideoProfitProtectRate)); err != nil {
		return fmt.Errorf("%T.video_profit_protect_rate (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:video_profit_protect_rate: %s", p, err)
	}
	return err
}

func (p *OwMediaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMediaInfo(%+v)", *p)
}

type OwStatInfo struct {
	Dt            int32 `thrift:"dt,1" json:"dt"`
	Hr            int8  `thrift:"hr,2" json:"hr"`
	PlacementType int16 `thrift:"placementType,3" json:"placementType"`
	CostType      int16 `thrift:"costType,4" json:"costType"`
	ImpCount      int64 `thrift:"impCount,5" json:"impCount"`
	ClkCount      int64 `thrift:"clkCount,6" json:"clkCount"`
	ActCount      int64 `thrift:"actCount,7" json:"actCount"`
	SettledCount  int64 `thrift:"settledCount,8" json:"settledCount"`
	AdConsumed    int64 `thrift:"adConsumed,9" json:"adConsumed"`
	MediaShare    int64 `thrift:"mediaShare,10" json:"mediaShare"`
}

func NewOwStatInfo() *OwStatInfo {
	return &OwStatInfo{}
}

func (p *OwStatInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwStatInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *OwStatInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = int8(v)
	}
	return nil
}

func (p *OwStatInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlacementType = v
	}
	return nil
}

func (p *OwStatInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *OwStatInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImpCount = v
	}
	return nil
}

func (p *OwStatInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ClkCount = v
	}
	return nil
}

func (p *OwStatInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ActCount = v
	}
	return nil
}

func (p *OwStatInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SettledCount = v
	}
	return nil
}

func (p *OwStatInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AdConsumed = v
	}
	return nil
}

func (p *OwStatInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MediaShare = v
	}
	return nil
}

func (p *OwStatInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwStatInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwStatInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BYTE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:placementType: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:placementType: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:costType: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:costType: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impCount", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:impCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpCount)); err != nil {
		return fmt.Errorf("%T.impCount (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:impCount: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkCount", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:clkCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClkCount)); err != nil {
		return fmt.Errorf("%T.clkCount (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:clkCount: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actCount", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:actCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ActCount)); err != nil {
		return fmt.Errorf("%T.actCount (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:actCount: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settledCount", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:settledCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettledCount)); err != nil {
		return fmt.Errorf("%T.settledCount (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:settledCount: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adConsumed", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:adConsumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdConsumed)); err != nil {
		return fmt.Errorf("%T.adConsumed (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:adConsumed: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaShare", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:mediaShare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaShare)); err != nil {
		return fmt.Errorf("%T.mediaShare (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:mediaShare: %s", p, err)
	}
	return err
}

func (p *OwStatInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwStatInfo(%+v)", *p)
}

type OwAdPlanStatInfo struct {
	Pid        int32       `thrift:"pid,1" json:"pid"`
	CampaignID int32       `thrift:"campaignID,2" json:"campaignID"`
	Stat       *OwStatInfo `thrift:"stat,3" json:"stat"`
}

func NewOwAdPlanStatInfo() *OwAdPlanStatInfo {
	return &OwAdPlanStatInfo{}
}

func (p *OwAdPlanStatInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlanStatInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *OwAdPlanStatInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignID = v
	}
	return nil
}

func (p *OwAdPlanStatInfo) readField3(iprot thrift.TProtocol) error {
	p.Stat = NewOwStatInfo()
	if err := p.Stat.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Stat)
	}
	return nil
}

func (p *OwAdPlanStatInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdPlanStatInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlanStatInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pid: %s", p, err)
	}
	return err
}

func (p *OwAdPlanStatInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignID", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaignID: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignID)); err != nil {
		return fmt.Errorf("%T.campaignID (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaignID: %s", p, err)
	}
	return err
}

func (p *OwAdPlanStatInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Stat != nil {
		if err := oprot.WriteFieldBegin("stat", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:stat: %s", p, err)
		}
		if err := p.Stat.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Stat)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:stat: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlanStatInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdPlanStatInfo(%+v)", *p)
}
