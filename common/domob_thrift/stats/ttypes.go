// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//统计数据时间分组方式
type StatsDateGrouping int64

const (
	StatsDateGrouping_STATS_PER_HOUR  StatsDateGrouping = 0
	StatsDateGrouping_STATS_PER_DAY   StatsDateGrouping = 1
	StatsDateGrouping_STATS_PER_WEEK  StatsDateGrouping = 2
	StatsDateGrouping_STATS_PER_MONTH StatsDateGrouping = 3
	StatsDateGrouping_CUMULATIVE      StatsDateGrouping = 4
)

func (p StatsDateGrouping) String() string {
	switch p {
	case StatsDateGrouping_STATS_PER_HOUR:
		return "StatsDateGrouping_STATS_PER_HOUR"
	case StatsDateGrouping_STATS_PER_DAY:
		return "StatsDateGrouping_STATS_PER_DAY"
	case StatsDateGrouping_STATS_PER_WEEK:
		return "StatsDateGrouping_STATS_PER_WEEK"
	case StatsDateGrouping_STATS_PER_MONTH:
		return "StatsDateGrouping_STATS_PER_MONTH"
	case StatsDateGrouping_CUMULATIVE:
		return "StatsDateGrouping_CUMULATIVE"
	}
	return "<UNSET>"
}

func StatsDateGroupingFromString(s string) (StatsDateGrouping, error) {
	switch s {
	case "StatsDateGrouping_STATS_PER_HOUR":
		return StatsDateGrouping_STATS_PER_HOUR, nil
	case "StatsDateGrouping_STATS_PER_DAY":
		return StatsDateGrouping_STATS_PER_DAY, nil
	case "StatsDateGrouping_STATS_PER_WEEK":
		return StatsDateGrouping_STATS_PER_WEEK, nil
	case "StatsDateGrouping_STATS_PER_MONTH":
		return StatsDateGrouping_STATS_PER_MONTH, nil
	case "StatsDateGrouping_CUMULATIVE":
		return StatsDateGrouping_CUMULATIVE, nil
	}
	return StatsDateGrouping(math.MinInt32 - 1), fmt.Errorf("not a valid StatsDateGrouping string")
}

//广告统计信息分组方式
//用于标识是否按照ID聚合数据
type AderStatsGrouping int64

const (
	AderStatsGrouping_GROUP_BY_ALL         AderStatsGrouping = 0
	AderStatsGrouping_GROUP_BY_AD_PLAN     AderStatsGrouping = 1
	AderStatsGrouping_GROUP_BY_AD_STRATEGY AderStatsGrouping = 2
	AderStatsGrouping_GROUP_BY_AD_CREATIVE AderStatsGrouping = 3
	AderStatsGrouping_GROUP_BY_ID          AderStatsGrouping = 4
)

func (p AderStatsGrouping) String() string {
	switch p {
	case AderStatsGrouping_GROUP_BY_ALL:
		return "AderStatsGrouping_GROUP_BY_ALL"
	case AderStatsGrouping_GROUP_BY_AD_PLAN:
		return "AderStatsGrouping_GROUP_BY_AD_PLAN"
	case AderStatsGrouping_GROUP_BY_AD_STRATEGY:
		return "AderStatsGrouping_GROUP_BY_AD_STRATEGY"
	case AderStatsGrouping_GROUP_BY_AD_CREATIVE:
		return "AderStatsGrouping_GROUP_BY_AD_CREATIVE"
	case AderStatsGrouping_GROUP_BY_ID:
		return "AderStatsGrouping_GROUP_BY_ID"
	}
	return "<UNSET>"
}

func AderStatsGroupingFromString(s string) (AderStatsGrouping, error) {
	switch s {
	case "AderStatsGrouping_GROUP_BY_ALL":
		return AderStatsGrouping_GROUP_BY_ALL, nil
	case "AderStatsGrouping_GROUP_BY_AD_PLAN":
		return AderStatsGrouping_GROUP_BY_AD_PLAN, nil
	case "AderStatsGrouping_GROUP_BY_AD_STRATEGY":
		return AderStatsGrouping_GROUP_BY_AD_STRATEGY, nil
	case "AderStatsGrouping_GROUP_BY_AD_CREATIVE":
		return AderStatsGrouping_GROUP_BY_AD_CREATIVE, nil
	case "AderStatsGrouping_GROUP_BY_ID":
		return AderStatsGrouping_GROUP_BY_ID, nil
	}
	return AderStatsGrouping(math.MinInt32 - 1), fmt.Errorf("not a valid AderStatsGrouping string")
}

//广告ID类型
type AderIdType int64

const (
	AderIdType_AD_PLAN_ID     AderIdType = 1
	AderIdType_AD_STRATEGY_ID AderIdType = 2
	AderIdType_AD_CREATIVE_ID AderIdType = 3
)

func (p AderIdType) String() string {
	switch p {
	case AderIdType_AD_PLAN_ID:
		return "AderIdType_AD_PLAN_ID"
	case AderIdType_AD_STRATEGY_ID:
		return "AderIdType_AD_STRATEGY_ID"
	case AderIdType_AD_CREATIVE_ID:
		return "AderIdType_AD_CREATIVE_ID"
	}
	return "<UNSET>"
}

func AderIdTypeFromString(s string) (AderIdType, error) {
	switch s {
	case "AderIdType_AD_PLAN_ID":
		return AderIdType_AD_PLAN_ID, nil
	case "AderIdType_AD_STRATEGY_ID":
		return AderIdType_AD_STRATEGY_ID, nil
	case "AderIdType_AD_CREATIVE_ID":
		return AderIdType_AD_CREATIVE_ID, nil
	}
	return AderIdType(math.MinInt32 - 1), fmt.Errorf("not a valid AderIdType string")
}

//媒体ID类型
type DeverIdType int64

const (
	DeverIdType_MEDIA_ID           DeverIdType = 1
	DeverIdType_MEDIA_PLACEMENT_ID DeverIdType = 2
)

func (p DeverIdType) String() string {
	switch p {
	case DeverIdType_MEDIA_ID:
		return "DeverIdType_MEDIA_ID"
	case DeverIdType_MEDIA_PLACEMENT_ID:
		return "DeverIdType_MEDIA_PLACEMENT_ID"
	}
	return "<UNSET>"
}

func DeverIdTypeFromString(s string) (DeverIdType, error) {
	switch s {
	case "DeverIdType_MEDIA_ID":
		return DeverIdType_MEDIA_ID, nil
	case "DeverIdType_MEDIA_PLACEMENT_ID":
		return DeverIdType_MEDIA_PLACEMENT_ID, nil
	}
	return DeverIdType(math.MinInt32 - 1), fmt.Errorf("not a valid DeverIdType string")
}

//扩展统计类型
type ExtStatsType int64

const (
	ExtStatsType_REGION ExtStatsType = 1
	ExtStatsType_BRAND  ExtStatsType = 2
	ExtStatsType_DEVICE ExtStatsType = 3
)

func (p ExtStatsType) String() string {
	switch p {
	case ExtStatsType_REGION:
		return "ExtStatsType_REGION"
	case ExtStatsType_BRAND:
		return "ExtStatsType_BRAND"
	case ExtStatsType_DEVICE:
		return "ExtStatsType_DEVICE"
	}
	return "<UNSET>"
}

func ExtStatsTypeFromString(s string) (ExtStatsType, error) {
	switch s {
	case "ExtStatsType_REGION":
		return ExtStatsType_REGION, nil
	case "ExtStatsType_BRAND":
		return ExtStatsType_BRAND, nil
	case "ExtStatsType_DEVICE":
		return ExtStatsType_DEVICE, nil
	}
	return ExtStatsType(math.MinInt32 - 1), fmt.Errorf("not a valid ExtStatsType string")
}

type DateParam struct {
	StartDate         common.TimeInt    `thrift:"startDate,1" json:"startDate"`
	EndDate           common.TimeInt    `thrift:"endDate,2" json:"endDate"`
	StatsDateGrouping StatsDateGrouping `thrift:"statsDateGrouping,3" json:"statsDateGrouping"`
}

func NewDateParam() *DateParam {
	return &DateParam{
		StatsDateGrouping: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DateParam) IsSetStatsDateGrouping() bool {
	return int64(p.StatsDateGrouping) != math.MinInt32-1
}

func (p *DateParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DateParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartDate = common.TimeInt(v)
	}
	return nil
}

func (p *DateParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndDate = common.TimeInt(v)
	}
	return nil
}

func (p *DateParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StatsDateGrouping = StatsDateGrouping(v)
	}
	return nil
}

func (p *DateParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DateParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DateParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startDate", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartDate)); err != nil {
		return fmt.Errorf("%T.startDate (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startDate: %s", p, err)
	}
	return err
}

func (p *DateParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endDate", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endDate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndDate)); err != nil {
		return fmt.Errorf("%T.endDate (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endDate: %s", p, err)
	}
	return err
}

func (p *DateParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatsDateGrouping() {
		if err := oprot.WriteFieldBegin("statsDateGrouping", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:statsDateGrouping: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatsDateGrouping)); err != nil {
			return fmt.Errorf("%T.statsDateGrouping (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:statsDateGrouping: %s", p, err)
		}
	}
	return err
}

func (p *DateParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DateParam(%+v)", *p)
}

type AderStatsGroupingParam struct {
	AderStatsGrouping AderStatsGrouping `thrift:"aderStatsGrouping,1" json:"aderStatsGrouping"`
	IdList            []common.IdInt    `thrift:"idList,2" json:"idList"`
	IdType            AderIdType        `thrift:"idType,3" json:"idType"`
}

func NewAderStatsGroupingParam() *AderStatsGroupingParam {
	return &AderStatsGroupingParam{
		AderStatsGrouping: math.MinInt32 - 1, // unset sentinal value

		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AderStatsGroupingParam) IsSetAderStatsGrouping() bool {
	return int64(p.AderStatsGrouping) != math.MinInt32-1
}

func (p *AderStatsGroupingParam) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *AderStatsGroupingParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AderStatsGroupingParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AderStatsGrouping = AderStatsGrouping(v)
	}
	return nil
}

func (p *AderStatsGroupingParam) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = common.IdInt(v)
		}
		p.IdList = append(p.IdList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AderStatsGroupingParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdType = AderIdType(v)
	}
	return nil
}

func (p *AderStatsGroupingParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AderStatsGroupingParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AderStatsGroupingParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAderStatsGrouping() {
		if err := oprot.WriteFieldBegin("aderStatsGrouping", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:aderStatsGrouping: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AderStatsGrouping)); err != nil {
			return fmt.Errorf("%T.aderStatsGrouping (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:aderStatsGrouping: %s", p, err)
		}
	}
	return err
}

func (p *AderStatsGroupingParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idList: %s", p, err)
		}
	}
	return err
}

func (p *AderStatsGroupingParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idType: %s", p, err)
		}
	}
	return err
}

func (p *AderStatsGroupingParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AderStatsGroupingParam(%+v)", *p)
}

type DeverStatsGroupingParam struct {
	GroupById bool           `thrift:"groupById,1" json:"groupById"`
	IdList    []common.IdInt `thrift:"idList,2" json:"idList"`
	IdType    DeverIdType    `thrift:"idType,3" json:"idType"`
}

func NewDeverStatsGroupingParam() *DeverStatsGroupingParam {
	return &DeverStatsGroupingParam{
		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeverStatsGroupingParam) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *DeverStatsGroupingParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverStatsGroupingParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.GroupById = v
	}
	return nil
}

func (p *DeverStatsGroupingParam) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = common.IdInt(v)
		}
		p.IdList = append(p.IdList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeverStatsGroupingParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdType = DeverIdType(v)
	}
	return nil
}

func (p *DeverStatsGroupingParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverStatsGroupingParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverStatsGroupingParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("groupById", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:groupById: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.GroupById)); err != nil {
		return fmt.Errorf("%T.groupById (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:groupById: %s", p, err)
	}
	return err
}

func (p *DeverStatsGroupingParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:idList: %s", p, err)
		}
	}
	return err
}

func (p *DeverStatsGroupingParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idType: %s", p, err)
		}
	}
	return err
}

func (p *DeverStatsGroupingParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverStatsGroupingParam(%+v)", *p)
}

type AderStatsData struct {
	Id        common.IdInt     `thrift:"id,1" json:"id"`
	DateParam *DateParam       `thrift:"dateParam,2" json:"dateParam"`
	MediaType common.MediaType `thrift:"mediaType,3" json:"mediaType"`
	// unused field # 4
	// unused field # 5
	Cost         common.Amount `thrift:"cost,6" json:"cost"`
	UseDate      string        `thrift:"useDate,7" json:"useDate"`
	MediaTypeInt int32         `thrift:"mediaTypeInt,8" json:"mediaTypeInt"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	ImpressionCount          int64 `thrift:"impressionCount,51" json:"impressionCount"`
	ClickCount               int64 `thrift:"clickCount,52" json:"clickCount"`
	ImpressionReportCount    int64 `thrift:"impressionReportCount,53" json:"impressionReportCount"`
	EffectiveImpressionCount int64 `thrift:"effectiveImpressionCount,54" json:"effectiveImpressionCount"`
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	Downloads int64 `thrift:"downloads,60" json:"downloads"`
	Installs  int64 `thrift:"installs,61" json:"installs"`
}

func NewAderStatsData() *AderStatsData {
	return &AderStatsData{
		MediaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AderStatsData) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *AderStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I64 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I64 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AderStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = common.IdInt(v)
	}
	return nil
}

func (p *AderStatsData) readField2(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *AderStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaType = common.MediaType(v)
	}
	return nil
}

func (p *AderStatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Cost = common.Amount(v)
	}
	return nil
}

func (p *AderStatsData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.UseDate = v
	}
	return nil
}

func (p *AderStatsData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.MediaTypeInt = v
	}
	return nil
}

func (p *AderStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.ImpressionCount = v
	}
	return nil
}

func (p *AderStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.ClickCount = v
	}
	return nil
}

func (p *AderStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ImpressionReportCount = v
	}
	return nil
}

func (p *AderStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.EffectiveImpressionCount = v
	}
	return nil
}

func (p *AderStatsData) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Downloads = v
	}
	return nil
}

func (p *AderStatsData) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Installs = v
	}
	return nil
}

func (p *AderStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AderStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AderStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *AderStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaType", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mediaType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
		return fmt.Errorf("%T.mediaType (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mediaType: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:cost: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cost)); err != nil {
		return fmt.Errorf("%T.cost (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:cost: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useDate", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:useDate: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UseDate)); err != nil {
		return fmt.Errorf("%T.useDate (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:useDate: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaTypeInt", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:mediaTypeInt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaTypeInt)); err != nil {
		return fmt.Errorf("%T.mediaTypeInt (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:mediaTypeInt: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionCount", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:impressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionCount)); err != nil {
		return fmt.Errorf("%T.impressionCount (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:impressionCount: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickCount", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:clickCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickCount)); err != nil {
		return fmt.Errorf("%T.clickCount (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:clickCount: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionReportCount", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:impressionReportCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionReportCount)); err != nil {
		return fmt.Errorf("%T.impressionReportCount (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:impressionReportCount: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveImpressionCount", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:effectiveImpressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EffectiveImpressionCount)); err != nil {
		return fmt.Errorf("%T.effectiveImpressionCount (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:effectiveImpressionCount: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("downloads", thrift.I64, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:downloads: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Downloads)); err != nil {
		return fmt.Errorf("%T.downloads (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:downloads: %s", p, err)
	}
	return err
}

func (p *AderStatsData) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("installs", thrift.I64, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:installs: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Installs)); err != nil {
		return fmt.Errorf("%T.installs (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:installs: %s", p, err)
	}
	return err
}

func (p *AderStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AderStatsData(%+v)", *p)
}

type DeverStatsData struct {
	MediaId   common.IdInt `thrift:"mediaId,1" json:"mediaId"`
	DateParam *DateParam   `thrift:"dateParam,2" json:"dateParam"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	Revenue common.Amount `thrift:"revenue,7" json:"revenue"`
	UseDate string        `thrift:"useDate,8" json:"useDate"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	RequestCount             int64 `thrift:"requestCount,51" json:"requestCount"`
	RequestSuccessCount      int64 `thrift:"requestSuccessCount,52" json:"requestSuccessCount"`
	ImpressionCount          int64 `thrift:"impressionCount,53" json:"impressionCount"`
	ClickCount               int64 `thrift:"clickCount,54" json:"clickCount"`
	ImpressionReportCount    int64 `thrift:"impressionReportCount,55" json:"impressionReportCount"`
	EffectiveImpressionCount int64 `thrift:"effectiveImpressionCount,56" json:"effectiveImpressionCount"`
	// unused field # 57
	// unused field # 58
	// unused field # 59
	Downloads       int64 `thrift:"downloads,60" json:"downloads"`
	Installs        int64 `thrift:"installs,61" json:"installs"`
	EntryClickCount int64 `thrift:"entryClickCount,62" json:"entryClickCount"`
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	PlacementId common.IdInt `thrift:"placementId,100" json:"placementId"`
}

func NewDeverStatsData() *DeverStatsData {
	return &DeverStatsData{}
}

func (p *DeverStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.I64 {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I64 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I64 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I64 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 100:
			if fieldTypeId == thrift.I32 {
				if err := p.readField100(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaId = common.IdInt(v)
	}
	return nil
}

func (p *DeverStatsData) readField2(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *DeverStatsData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Revenue = common.Amount(v)
	}
	return nil
}

func (p *DeverStatsData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.UseDate = v
	}
	return nil
}

func (p *DeverStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.RequestCount = v
	}
	return nil
}

func (p *DeverStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.RequestSuccessCount = v
	}
	return nil
}

func (p *DeverStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ImpressionCount = v
	}
	return nil
}

func (p *DeverStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.ClickCount = v
	}
	return nil
}

func (p *DeverStatsData) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.ImpressionReportCount = v
	}
	return nil
}

func (p *DeverStatsData) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.EffectiveImpressionCount = v
	}
	return nil
}

func (p *DeverStatsData) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Downloads = v
	}
	return nil
}

func (p *DeverStatsData) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Installs = v
	}
	return nil
}

func (p *DeverStatsData) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.EntryClickCount = v
	}
	return nil
}

func (p *DeverStatsData) readField100(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 100: %s", err)
	} else {
		p.PlacementId = common.IdInt(v)
	}
	return nil
}

func (p *DeverStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField100(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mediaId: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *DeverStatsData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("revenue", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:revenue: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Revenue)); err != nil {
		return fmt.Errorf("%T.revenue (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:revenue: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useDate", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:useDate: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UseDate)); err != nil {
		return fmt.Errorf("%T.useDate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:useDate: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestCount", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:requestCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestCount)); err != nil {
		return fmt.Errorf("%T.requestCount (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:requestCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestSuccessCount", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:requestSuccessCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestSuccessCount)); err != nil {
		return fmt.Errorf("%T.requestSuccessCount (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:requestSuccessCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionCount", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:impressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionCount)); err != nil {
		return fmt.Errorf("%T.impressionCount (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:impressionCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickCount", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:clickCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickCount)); err != nil {
		return fmt.Errorf("%T.clickCount (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:clickCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionReportCount", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:impressionReportCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionReportCount)); err != nil {
		return fmt.Errorf("%T.impressionReportCount (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:impressionReportCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveImpressionCount", thrift.I64, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:effectiveImpressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EffectiveImpressionCount)); err != nil {
		return fmt.Errorf("%T.effectiveImpressionCount (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:effectiveImpressionCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("downloads", thrift.I64, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:downloads: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Downloads)); err != nil {
		return fmt.Errorf("%T.downloads (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:downloads: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("installs", thrift.I64, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:installs: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Installs)); err != nil {
		return fmt.Errorf("%T.installs (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:installs: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entryClickCount", thrift.I64, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:entryClickCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EntryClickCount)); err != nil {
		return fmt.Errorf("%T.entryClickCount (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:entryClickCount: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) writeField100(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementId", thrift.I32, 100); err != nil {
		return fmt.Errorf("%T write field begin error 100:placementId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementId)); err != nil {
		return fmt.Errorf("%T.placementId (100) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 100:placementId: %s", p, err)
	}
	return err
}

func (p *DeverStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverStatsData(%+v)", *p)
}

type DeverExtStatsData struct {
	MediaId   common.IdInt `thrift:"mediaId,1" json:"mediaId"`
	DateParam *DateParam   `thrift:"dateParam,2" json:"dateParam"`
	Region    common.IdInt `thrift:"region,3" json:"region"`
	Brand     common.IdInt `thrift:"brand,4" json:"brand"`
	Device    common.IdInt `thrift:"device,5" json:"device"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Revenue common.Amount `thrift:"revenue,10" json:"revenue"`
	UseDate string        `thrift:"useDate,11" json:"useDate"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	RequestCount        int64 `thrift:"requestCount,51" json:"requestCount"`
	RequestSuccessCount int64 `thrift:"requestSuccessCount,52" json:"requestSuccessCount"`
	ImpressionCount     int64 `thrift:"impressionCount,53" json:"impressionCount"`
	ClickCount          int64 `thrift:"clickCount,54" json:"clickCount"`
}

func NewDeverExtStatsData() *DeverExtStatsData {
	return &DeverExtStatsData{}
}

func (p *DeverExtStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverExtStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaId = common.IdInt(v)
	}
	return nil
}

func (p *DeverExtStatsData) readField2(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *DeverExtStatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Region = common.IdInt(v)
	}
	return nil
}

func (p *DeverExtStatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Brand = common.IdInt(v)
	}
	return nil
}

func (p *DeverExtStatsData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Device = common.IdInt(v)
	}
	return nil
}

func (p *DeverExtStatsData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Revenue = common.Amount(v)
	}
	return nil
}

func (p *DeverExtStatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.UseDate = v
	}
	return nil
}

func (p *DeverExtStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.RequestCount = v
	}
	return nil
}

func (p *DeverExtStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.RequestSuccessCount = v
	}
	return nil
}

func (p *DeverExtStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ImpressionCount = v
	}
	return nil
}

func (p *DeverExtStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.ClickCount = v
	}
	return nil
}

func (p *DeverExtStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverExtStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverExtStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mediaId: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *DeverExtStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:region: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brand", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:brand: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Brand)); err != nil {
		return fmt.Errorf("%T.brand (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:brand: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:device: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("revenue", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:revenue: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Revenue)); err != nil {
		return fmt.Errorf("%T.revenue (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:revenue: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useDate", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:useDate: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UseDate)); err != nil {
		return fmt.Errorf("%T.useDate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:useDate: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestCount", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:requestCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestCount)); err != nil {
		return fmt.Errorf("%T.requestCount (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:requestCount: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestSuccessCount", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:requestSuccessCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestSuccessCount)); err != nil {
		return fmt.Errorf("%T.requestSuccessCount (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:requestSuccessCount: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionCount", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:impressionCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionCount)); err != nil {
		return fmt.Errorf("%T.impressionCount (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:impressionCount: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickCount", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:clickCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickCount)); err != nil {
		return fmt.Errorf("%T.clickCount (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:clickCount: %s", p, err)
	}
	return err
}

func (p *DeverExtStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverExtStatsData(%+v)", *p)
}

type DeverAppExStatsData struct {
	MediaId   common.IdInt `thrift:"mediaId,1" json:"mediaId"`
	DateParam *DateParam   `thrift:"dateParam,2" json:"dateParam"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	Revenue common.Amount `thrift:"revenue,8" json:"revenue"`
	UseDate string        `thrift:"useDate,9" json:"useDate"`
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	RequestCount        int64 `thrift:"requestCount,51" json:"requestCount"`
	ShowListCount       int64 `thrift:"showListCount,52" json:"showListCount"`
	ShowDetailCount     int64 `thrift:"showDetailCount,53" json:"showDetailCount"`
	DownloadCount       int64 `thrift:"downloadCount,54" json:"downloadCount"`
	DownloadFinishCount int64 `thrift:"downloadFinishCount,55" json:"downloadFinishCount"`
}

func NewDeverAppExStatsData() *DeverAppExStatsData {
	return &DeverAppExStatsData{}
}

func (p *DeverAppExStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeverAppExStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaId = common.IdInt(v)
	}
	return nil
}

func (p *DeverAppExStatsData) readField2(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *DeverAppExStatsData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Revenue = common.Amount(v)
	}
	return nil
}

func (p *DeverAppExStatsData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UseDate = v
	}
	return nil
}

func (p *DeverAppExStatsData) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.RequestCount = v
	}
	return nil
}

func (p *DeverAppExStatsData) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.ShowListCount = v
	}
	return nil
}

func (p *DeverAppExStatsData) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ShowDetailCount = v
	}
	return nil
}

func (p *DeverAppExStatsData) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.DownloadCount = v
	}
	return nil
}

func (p *DeverAppExStatsData) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.DownloadFinishCount = v
	}
	return nil
}

func (p *DeverAppExStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeverAppExStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeverAppExStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mediaId: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *DeverAppExStatsData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("revenue", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:revenue: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Revenue)); err != nil {
		return fmt.Errorf("%T.revenue (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:revenue: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useDate", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:useDate: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UseDate)); err != nil {
		return fmt.Errorf("%T.useDate (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:useDate: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestCount", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:requestCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestCount)); err != nil {
		return fmt.Errorf("%T.requestCount (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:requestCount: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("showListCount", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:showListCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ShowListCount)); err != nil {
		return fmt.Errorf("%T.showListCount (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:showListCount: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("showDetailCount", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:showDetailCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ShowDetailCount)); err != nil {
		return fmt.Errorf("%T.showDetailCount (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:showDetailCount: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("downloadCount", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:downloadCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DownloadCount)); err != nil {
		return fmt.Errorf("%T.downloadCount (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:downloadCount: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("downloadFinishCount", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:downloadFinishCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DownloadFinishCount)); err != nil {
		return fmt.Errorf("%T.downloadFinishCount (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:downloadFinishCount: %s", p, err)
	}
	return err
}

func (p *DeverAppExStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeverAppExStatsData(%+v)", *p)
}

type StatsDataQueryException struct {
	Message string `thrift:"message,1" json:"message"`
}

func NewStatsDataQueryException() *StatsDataQueryException {
	return &StatsDataQueryException{}
}

func (p *StatsDataQueryException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsDataQueryException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *StatsDataQueryException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsDataQueryException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsDataQueryException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:message: %s", p, err)
	}
	return err
}

func (p *StatsDataQueryException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsDataQueryException(%+v)", *p)
}
