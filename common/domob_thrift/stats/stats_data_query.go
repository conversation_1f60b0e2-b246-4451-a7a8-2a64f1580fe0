// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__

type StatsDataQuery interface { //统计数据查询接口

	// 查询广告主统计数据
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - AderId: 广告主ID, 必填
	//  - AderStatsGroupingParam: 广告分组方式参数, 如果为Null, 则认为是按照广告主的全部广告进行累计
	// 注意：如果参数中的List为空,则认为没有要统计的项目
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - GroupByMediaType: 是否按照媒体类型分组, 默认false
	//  - CostType: 广告消费类型。
	// 如果为Null，则认为是按照全部广告消费类型进行累计，效果与CT_ALL相同
	// 如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	QueryAderStatsData(requestHeader *common.RequestHeader, aderId common.IdInt, aderStatsGroupingParam *AderStatsGroupingParam, dateParam *DateParam, groupByMediaType bool, costType common.CostType) (r []*AderStatsData, sdqe *StatsDataQueryException, err error)
	//  * @since HouseAd
	// * 查询开发者统计数据, 为保持向下兼容，新定义getDeverStatsData接口，供HouseAd使用
	//  * 原官网/MIS的统计模块，仍可继续使用queryDeverStatsData接口
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverId: 开发者ID, 必填
	//  - MediaIdList: 即将废弃，请使用deverStatsGroupingParam。应用程序ID列表, 如果为Null, 则认为对全部应用进行汇总。注意：如果是空List则认为是没有应用
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - CostType: 广告消费类型。
	// 如果为Null，则认为是按照全部广告消费类型进行累计，效果与CT_ALL相同
	// 如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	//  - PlacementType: 广告位类型。
	// 如果为Null，则认为是按照全部广告位类型进行累计，效果与APT_ALL相同
	// 如果为APT_UNKNOWN，则服务端不予处理，抛出异常
	//  - DeverStatsGroupingParam:   *  @since HouseAd
	// *	开发者分组方式参数, 如果为Null, 则认为是按照开发者的全部媒体、广告位进行累计
	// *	注意：如果参数中的List为空,则认为没有要统计的项目
	//   *  计划逐步将mediaIdList字段废弃掉（即请求者应将应用程序ID列表也归入deverStatsGroupingParam）
	//   *  StatsQuery会优先采用deverStatsGroupingParam，若为null，才会看mediaIdList
	GetDeverStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, costType common.CostType, placementType common.AdPlacementType, deverStatsGroupingParam *DeverStatsGroupingParam) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error)
	// 查询开发者统计数据
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverId: 开发者ID, 必填
	//  - MediaIdList: 应用程序ID列表, 如果为Null, 则认为对全部应用进行汇总。注意：如果是空List则认为是没有应用
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - CostType: 广告消费类型。
	// 如果为Null，则认为是按照全部广告消费类型进行累计，效果与CT_ALL相同
	// 如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	//  - PlacementType: 广告位类型。
	// 如果为Null，则认为是按照全部广告位类型进行累计，效果与APT_ALL相同
	// 如果为APT_UNKNOWN，则服务端不予处理，抛出异常
	QueryDeverStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, costType common.CostType, placementType common.AdPlacementType) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error)
	// 查询开发者扩展统计数据
	//
	// Parameters:
	//  - RequestHeader: 请求头
	//  - DeverId: 开发者ID
	//  - ExtStatsType: 扩展统计报告类型
	//  - MediaIdList: 媒体ID列表。如果为Null或为空列表, 则认为对全部媒体进行汇总。
	//  - MergeById: 是否按照指定的IdList进行聚合，如果不指定(为false)则不会聚合，会返回每一个ID所对应的信息
	//  - DateParam: 日期参数, 如果是Null,则认为是全部时间范围
	QueryDeverExtStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, extStatsType ExtStatsType, mediaIdList []common.IdInt, mergeById bool, dateParam *DateParam) (r []*DeverExtStatsData, sdqe *StatsDataQueryException, err error)
	// 查询媒体应用互换统计数据
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverId: 开发者ID, 必填
	//  - MediaIdList: 应用程序ID列表, 如果为Null, 则认为对全部应用进行汇总。注意：如果是空List则认为是没有应用
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - MergeByMediaId: 是否按照媒体ID汇总
	QueryDeverAppExStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, mergeByMediaId bool) (r []*DeverAppExStatsData, sdqe *StatsDataQueryException, err error)
	// 查询开发者统计数据, 扩展, 支持广告位
	// @since 2.0.1
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverId: 开发者ID, 必填
	//  - MediaIdList: 应用程序ID列表, 如果为空数组, 则认为对全部应用进行汇总
	//  - PlacementIdList: 广告位ID列表, 如果为空数组, 则认为对全部广告位进行汇总
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - CostTypeList: 广告消费类型列表, 如果为空数组, 则认为对全部消费类型进行汇总
	//  - PlacementTypeList: 广告位类型列表, 如果为空数组, 则认为对全部广告位类型进行汇总
	QueryAppBasicStats(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, placementIdList []common.IdInt, dateParam *DateParam, costTypeList []common.CostType, placementTypeList []common.AdPlacementType) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error)
	// 查询开发者明细统计数据, 只会返回指定的应用/广告位数据, 并且不做聚合
	// @since 2.0.2
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverId: 开发者ID, 必填
	//  - IdList: ID列表
	//  - IdType: 查询的ID类型
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	QueryAppDetailStats(requestHeader *common.RequestHeader, deverId common.IdInt, idList []common.IdInt, idType DeverIdType, dateParam *DateParam) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error)
}

//统计数据查询接口
type StatsDataQueryClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewStatsDataQueryClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *StatsDataQueryClient {
	return &StatsDataQueryClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewStatsDataQueryClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *StatsDataQueryClient {
	return &StatsDataQueryClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询广告主统计数据
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - AderId: 广告主ID, 必填
//  - AderStatsGroupingParam: 广告分组方式参数, 如果为Null, 则认为是按照广告主的全部广告进行累计
// 注意：如果参数中的List为空,则认为没有要统计的项目
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - GroupByMediaType: 是否按照媒体类型分组, 默认false
//  - CostType: 广告消费类型。
// 如果为Null，则认为是按照全部广告消费类型进行累计，效果与CT_ALL相同
// 如果为CT_UNKNOWN，则服务端不予处理，抛出异常
func (p *StatsDataQueryClient) QueryAderStatsData(requestHeader *common.RequestHeader, aderId common.IdInt, aderStatsGroupingParam *AderStatsGroupingParam, dateParam *DateParam, groupByMediaType bool, costType common.CostType) (r []*AderStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendQueryAderStatsData(requestHeader, aderId, aderStatsGroupingParam, dateParam, groupByMediaType, costType); err != nil {
		return
	}
	return p.recvQueryAderStatsData()
}

func (p *StatsDataQueryClient) sendQueryAderStatsData(requestHeader *common.RequestHeader, aderId common.IdInt, aderStatsGroupingParam *AderStatsGroupingParam, dateParam *DateParam, groupByMediaType bool, costType common.CostType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryAderStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewQueryAderStatsDataArgs()
	args2.RequestHeader = requestHeader
	args2.AderId = aderId
	args2.AderStatsGroupingParam = aderStatsGroupingParam
	args2.DateParam = dateParam
	args2.GroupByMediaType = groupByMediaType
	args2.CostType = costType
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvQueryAderStatsData() (value []*AderStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewQueryAderStatsDataResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	if result3.Sdqe != nil {
		sdqe = result3.Sdqe
	}
	return
}

//  * @since HouseAd
// * 查询开发者统计数据, 为保持向下兼容，新定义getDeverStatsData接口，供HouseAd使用
//  * 原官网/MIS的统计模块，仍可继续使用queryDeverStatsData接口
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverId: 开发者ID, 必填
//  - MediaIdList: 即将废弃，请使用deverStatsGroupingParam。应用程序ID列表, 如果为Null, 则认为对全部应用进行汇总。注意：如果是空List则认为是没有应用
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - CostType: 广告消费类型。
// 如果为Null，则认为是按照全部广告消费类型进行累计，效果与CT_ALL相同
// 如果为CT_UNKNOWN，则服务端不予处理，抛出异常
//  - PlacementType: 广告位类型。
// 如果为Null，则认为是按照全部广告位类型进行累计，效果与APT_ALL相同
// 如果为APT_UNKNOWN，则服务端不予处理，抛出异常
//  - DeverStatsGroupingParam:   *  @since HouseAd
// *	开发者分组方式参数, 如果为Null, 则认为是按照开发者的全部媒体、广告位进行累计
// *	注意：如果参数中的List为空,则认为没有要统计的项目
//   *  计划逐步将mediaIdList字段废弃掉（即请求者应将应用程序ID列表也归入deverStatsGroupingParam）
//   *  StatsQuery会优先采用deverStatsGroupingParam，若为null，才会看mediaIdList
func (p *StatsDataQueryClient) GetDeverStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, costType common.CostType, placementType common.AdPlacementType, deverStatsGroupingParam *DeverStatsGroupingParam) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendGetDeverStatsData(requestHeader, deverId, mediaIdList, dateParam, costType, placementType, deverStatsGroupingParam); err != nil {
		return
	}
	return p.recvGetDeverStatsData()
}

func (p *StatsDataQueryClient) sendGetDeverStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, costType common.CostType, placementType common.AdPlacementType, deverStatsGroupingParam *DeverStatsGroupingParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getDeverStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args6 := NewGetDeverStatsDataArgs()
	args6.RequestHeader = requestHeader
	args6.DeverId = deverId
	args6.MediaIdList = mediaIdList
	args6.DateParam = dateParam
	args6.CostType = costType
	args6.PlacementType = placementType
	args6.DeverStatsGroupingParam = deverStatsGroupingParam
	if err = args6.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvGetDeverStatsData() (value []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error8 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error9 error
		error9, err = error8.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error9
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result7 := NewGetDeverStatsDataResult()
	if err = result7.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result7.Success
	if result7.Sdqe != nil {
		sdqe = result7.Sdqe
	}
	return
}

// 查询开发者统计数据
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverId: 开发者ID, 必填
//  - MediaIdList: 应用程序ID列表, 如果为Null, 则认为对全部应用进行汇总。注意：如果是空List则认为是没有应用
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - CostType: 广告消费类型。
// 如果为Null，则认为是按照全部广告消费类型进行累计，效果与CT_ALL相同
// 如果为CT_UNKNOWN，则服务端不予处理，抛出异常
//  - PlacementType: 广告位类型。
// 如果为Null，则认为是按照全部广告位类型进行累计，效果与APT_ALL相同
// 如果为APT_UNKNOWN，则服务端不予处理，抛出异常
func (p *StatsDataQueryClient) QueryDeverStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, costType common.CostType, placementType common.AdPlacementType) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendQueryDeverStatsData(requestHeader, deverId, mediaIdList, dateParam, costType, placementType); err != nil {
		return
	}
	return p.recvQueryDeverStatsData()
}

func (p *StatsDataQueryClient) sendQueryDeverStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, costType common.CostType, placementType common.AdPlacementType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryDeverStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args10 := NewQueryDeverStatsDataArgs()
	args10.RequestHeader = requestHeader
	args10.DeverId = deverId
	args10.MediaIdList = mediaIdList
	args10.DateParam = dateParam
	args10.CostType = costType
	args10.PlacementType = placementType
	if err = args10.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvQueryDeverStatsData() (value []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error12 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error13 error
		error13, err = error12.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error13
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result11 := NewQueryDeverStatsDataResult()
	if err = result11.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result11.Success
	if result11.Sdqe != nil {
		sdqe = result11.Sdqe
	}
	return
}

// 查询开发者扩展统计数据
//
// Parameters:
//  - RequestHeader: 请求头
//  - DeverId: 开发者ID
//  - ExtStatsType: 扩展统计报告类型
//  - MediaIdList: 媒体ID列表。如果为Null或为空列表, 则认为对全部媒体进行汇总。
//  - MergeById: 是否按照指定的IdList进行聚合，如果不指定(为false)则不会聚合，会返回每一个ID所对应的信息
//  - DateParam: 日期参数, 如果是Null,则认为是全部时间范围
func (p *StatsDataQueryClient) QueryDeverExtStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, extStatsType ExtStatsType, mediaIdList []common.IdInt, mergeById bool, dateParam *DateParam) (r []*DeverExtStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendQueryDeverExtStatsData(requestHeader, deverId, extStatsType, mediaIdList, mergeById, dateParam); err != nil {
		return
	}
	return p.recvQueryDeverExtStatsData()
}

func (p *StatsDataQueryClient) sendQueryDeverExtStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, extStatsType ExtStatsType, mediaIdList []common.IdInt, mergeById bool, dateParam *DateParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryDeverExtStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args14 := NewQueryDeverExtStatsDataArgs()
	args14.RequestHeader = requestHeader
	args14.DeverId = deverId
	args14.ExtStatsType = extStatsType
	args14.MediaIdList = mediaIdList
	args14.MergeById = mergeById
	args14.DateParam = dateParam
	if err = args14.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvQueryDeverExtStatsData() (value []*DeverExtStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error16 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error17 error
		error17, err = error16.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error17
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result15 := NewQueryDeverExtStatsDataResult()
	if err = result15.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result15.Success
	if result15.Sdqe != nil {
		sdqe = result15.Sdqe
	}
	return
}

// 查询媒体应用互换统计数据
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverId: 开发者ID, 必填
//  - MediaIdList: 应用程序ID列表, 如果为Null, 则认为对全部应用进行汇总。注意：如果是空List则认为是没有应用
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - MergeByMediaId: 是否按照媒体ID汇总
func (p *StatsDataQueryClient) QueryDeverAppExStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, mergeByMediaId bool) (r []*DeverAppExStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendQueryDeverAppExStatsData(requestHeader, deverId, mediaIdList, dateParam, mergeByMediaId); err != nil {
		return
	}
	return p.recvQueryDeverAppExStatsData()
}

func (p *StatsDataQueryClient) sendQueryDeverAppExStatsData(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, dateParam *DateParam, mergeByMediaId bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryDeverAppExStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args18 := NewQueryDeverAppExStatsDataArgs()
	args18.RequestHeader = requestHeader
	args18.DeverId = deverId
	args18.MediaIdList = mediaIdList
	args18.DateParam = dateParam
	args18.MergeByMediaId = mergeByMediaId
	if err = args18.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvQueryDeverAppExStatsData() (value []*DeverAppExStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error20 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error21 error
		error21, err = error20.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error21
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result19 := NewQueryDeverAppExStatsDataResult()
	if err = result19.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result19.Success
	if result19.Sdqe != nil {
		sdqe = result19.Sdqe
	}
	return
}

// 查询开发者统计数据, 扩展, 支持广告位
// @since 2.0.1
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverId: 开发者ID, 必填
//  - MediaIdList: 应用程序ID列表, 如果为空数组, 则认为对全部应用进行汇总
//  - PlacementIdList: 广告位ID列表, 如果为空数组, 则认为对全部广告位进行汇总
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - CostTypeList: 广告消费类型列表, 如果为空数组, 则认为对全部消费类型进行汇总
//  - PlacementTypeList: 广告位类型列表, 如果为空数组, 则认为对全部广告位类型进行汇总
func (p *StatsDataQueryClient) QueryAppBasicStats(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, placementIdList []common.IdInt, dateParam *DateParam, costTypeList []common.CostType, placementTypeList []common.AdPlacementType) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendQueryAppBasicStats(requestHeader, deverId, mediaIdList, placementIdList, dateParam, costTypeList, placementTypeList); err != nil {
		return
	}
	return p.recvQueryAppBasicStats()
}

func (p *StatsDataQueryClient) sendQueryAppBasicStats(requestHeader *common.RequestHeader, deverId common.IdInt, mediaIdList []common.IdInt, placementIdList []common.IdInt, dateParam *DateParam, costTypeList []common.CostType, placementTypeList []common.AdPlacementType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryAppBasicStats", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args22 := NewQueryAppBasicStatsArgs()
	args22.RequestHeader = requestHeader
	args22.DeverId = deverId
	args22.MediaIdList = mediaIdList
	args22.PlacementIdList = placementIdList
	args22.DateParam = dateParam
	args22.CostTypeList = costTypeList
	args22.PlacementTypeList = placementTypeList
	if err = args22.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvQueryAppBasicStats() (value []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error24 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error25 error
		error25, err = error24.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error25
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result23 := NewQueryAppBasicStatsResult()
	if err = result23.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result23.Success
	if result23.Sdqe != nil {
		sdqe = result23.Sdqe
	}
	return
}

// 查询开发者明细统计数据, 只会返回指定的应用/广告位数据, 并且不做聚合
// @since 2.0.2
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverId: 开发者ID, 必填
//  - IdList: ID列表
//  - IdType: 查询的ID类型
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
func (p *StatsDataQueryClient) QueryAppDetailStats(requestHeader *common.RequestHeader, deverId common.IdInt, idList []common.IdInt, idType DeverIdType, dateParam *DateParam) (r []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	if err = p.sendQueryAppDetailStats(requestHeader, deverId, idList, idType, dateParam); err != nil {
		return
	}
	return p.recvQueryAppDetailStats()
}

func (p *StatsDataQueryClient) sendQueryAppDetailStats(requestHeader *common.RequestHeader, deverId common.IdInt, idList []common.IdInt, idType DeverIdType, dateParam *DateParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryAppDetailStats", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args26 := NewQueryAppDetailStatsArgs()
	args26.RequestHeader = requestHeader
	args26.DeverId = deverId
	args26.IdList = idList
	args26.IdType = idType
	args26.DateParam = dateParam
	if err = args26.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *StatsDataQueryClient) recvQueryAppDetailStats() (value []*DeverStatsData, sdqe *StatsDataQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error28 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error29 error
		error29, err = error28.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error29
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result27 := NewQueryAppDetailStatsResult()
	if err = result27.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result27.Success
	if result27.Sdqe != nil {
		sdqe = result27.Sdqe
	}
	return
}

type StatsDataQueryProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      StatsDataQuery
}

func (p *StatsDataQueryProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *StatsDataQueryProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *StatsDataQueryProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewStatsDataQueryProcessor(handler StatsDataQuery) *StatsDataQueryProcessor {

	self30 := &StatsDataQueryProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self30.processorMap["queryAderStatsData"] = &statsDataQueryProcessorQueryAderStatsData{handler: handler}
	self30.processorMap["getDeverStatsData"] = &statsDataQueryProcessorGetDeverStatsData{handler: handler}
	self30.processorMap["queryDeverStatsData"] = &statsDataQueryProcessorQueryDeverStatsData{handler: handler}
	self30.processorMap["queryDeverExtStatsData"] = &statsDataQueryProcessorQueryDeverExtStatsData{handler: handler}
	self30.processorMap["queryDeverAppExStatsData"] = &statsDataQueryProcessorQueryDeverAppExStatsData{handler: handler}
	self30.processorMap["queryAppBasicStats"] = &statsDataQueryProcessorQueryAppBasicStats{handler: handler}
	self30.processorMap["queryAppDetailStats"] = &statsDataQueryProcessorQueryAppDetailStats{handler: handler}
	return self30
}

func (p *StatsDataQueryProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x31 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x31.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x31

}

type statsDataQueryProcessorQueryAderStatsData struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorQueryAderStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryAderStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryAderStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryAderStatsDataResult()
	if result.Success, result.Sdqe, err = p.handler.QueryAderStatsData(args.RequestHeader, args.AderId, args.AderStatsGroupingParam, args.DateParam, args.GroupByMediaType, args.CostType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryAderStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryAderStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryAderStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsDataQueryProcessorGetDeverStatsData struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorGetDeverStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDeverStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDeverStatsDataResult()
	if result.Success, result.Sdqe, err = p.handler.GetDeverStatsData(args.RequestHeader, args.DeverId, args.MediaIdList, args.DateParam, args.CostType, args.PlacementType, args.DeverStatsGroupingParam); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDeverStatsData: "+err.Error())
		oprot.WriteMessageBegin("getDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getDeverStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsDataQueryProcessorQueryDeverStatsData struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorQueryDeverStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryDeverStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryDeverStatsDataResult()
	if result.Success, result.Sdqe, err = p.handler.QueryDeverStatsData(args.RequestHeader, args.DeverId, args.MediaIdList, args.DateParam, args.CostType, args.PlacementType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryDeverStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryDeverStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsDataQueryProcessorQueryDeverExtStatsData struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorQueryDeverExtStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryDeverExtStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryDeverExtStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryDeverExtStatsDataResult()
	if result.Success, result.Sdqe, err = p.handler.QueryDeverExtStatsData(args.RequestHeader, args.DeverId, args.ExtStatsType, args.MediaIdList, args.MergeById, args.DateParam); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryDeverExtStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryDeverExtStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryDeverExtStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsDataQueryProcessorQueryDeverAppExStatsData struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorQueryDeverAppExStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryDeverAppExStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryDeverAppExStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryDeverAppExStatsDataResult()
	if result.Success, result.Sdqe, err = p.handler.QueryDeverAppExStatsData(args.RequestHeader, args.DeverId, args.MediaIdList, args.DateParam, args.MergeByMediaId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryDeverAppExStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryDeverAppExStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryDeverAppExStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsDataQueryProcessorQueryAppBasicStats struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorQueryAppBasicStats) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryAppBasicStatsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryAppBasicStats", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryAppBasicStatsResult()
	if result.Success, result.Sdqe, err = p.handler.QueryAppBasicStats(args.RequestHeader, args.DeverId, args.MediaIdList, args.PlacementIdList, args.DateParam, args.CostTypeList, args.PlacementTypeList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryAppBasicStats: "+err.Error())
		oprot.WriteMessageBegin("queryAppBasicStats", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryAppBasicStats", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type statsDataQueryProcessorQueryAppDetailStats struct {
	handler StatsDataQuery
}

func (p *statsDataQueryProcessorQueryAppDetailStats) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryAppDetailStatsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryAppDetailStats", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryAppDetailStatsResult()
	if result.Success, result.Sdqe, err = p.handler.QueryAppDetailStats(args.RequestHeader, args.DeverId, args.IdList, args.IdType, args.DateParam); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryAppDetailStats: "+err.Error())
		oprot.WriteMessageBegin("queryAppDetailStats", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryAppDetailStats", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type QueryAderStatsDataArgs struct {
	RequestHeader          *common.RequestHeader   `thrift:"requestHeader,1" json:"requestHeader"`
	AderId                 common.IdInt            `thrift:"aderId,2" json:"aderId"`
	AderStatsGroupingParam *AderStatsGroupingParam `thrift:"aderStatsGroupingParam,3" json:"aderStatsGroupingParam"`
	DateParam              *DateParam              `thrift:"dateParam,4" json:"dateParam"`
	GroupByMediaType       bool                    `thrift:"groupByMediaType,5" json:"groupByMediaType"`
	CostType               common.CostType         `thrift:"costType,6" json:"costType"`
}

func NewQueryAderStatsDataArgs() *QueryAderStatsDataArgs {
	return &QueryAderStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryAderStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *QueryAderStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AderId = common.IdInt(v)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.AderStatsGroupingParam = NewAderStatsGroupingParam()
	if err := p.AderStatsGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AderStatsGroupingParam)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.GroupByMediaType = v
	}
	return nil
}

func (p *QueryAderStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CostType = common.CostType(v)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAderStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAderStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aderId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:aderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AderId)); err != nil {
		return fmt.Errorf("%T.aderId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:aderId: %s", p, err)
	}
	return err
}

func (p *QueryAderStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AderStatsGroupingParam != nil {
		if err := oprot.WriteFieldBegin("aderStatsGroupingParam", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:aderStatsGroupingParam: %s", p, err)
		}
		if err := p.AderStatsGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AderStatsGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:aderStatsGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("groupByMediaType", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:groupByMediaType: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.GroupByMediaType)); err != nil {
		return fmt.Errorf("%T.groupByMediaType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:groupByMediaType: %s", p, err)
	}
	return err
}

func (p *QueryAderStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:costType: %s", p, err)
	}
	return err
}

func (p *QueryAderStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAderStatsDataArgs(%+v)", *p)
}

type QueryAderStatsDataResult struct {
	Success []*AderStatsData         `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewQueryAderStatsDataResult() *QueryAderStatsDataResult {
	return &QueryAderStatsDataResult{}
}

func (p *QueryAderStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAderStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*AderStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem32 := NewAderStatsData()
		if err := _elem32.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem32)
		}
		p.Success = append(p.Success, _elem32)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAderStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *QueryAderStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAderStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAderStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAderStatsDataResult(%+v)", *p)
}

type GetDeverStatsDataArgs struct {
	RequestHeader           *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	DeverId                 common.IdInt             `thrift:"deverId,2" json:"deverId"`
	MediaIdList             []common.IdInt           `thrift:"mediaIdList,3" json:"mediaIdList"`
	DateParam               *DateParam               `thrift:"dateParam,4" json:"dateParam"`
	CostType                common.CostType          `thrift:"costType,5" json:"costType"`
	PlacementType           common.AdPlacementType   `thrift:"placementType,6" json:"placementType"`
	DeverStatsGroupingParam *DeverStatsGroupingParam `thrift:"deverStatsGroupingParam,7" json:"deverStatsGroupingParam"`
}

func NewGetDeverStatsDataArgs() *GetDeverStatsDataArgs {
	return &GetDeverStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetDeverStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *GetDeverStatsDataArgs) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *GetDeverStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = common.IdInt(v)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem33 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem33 = common.IdInt(v)
		}
		p.MediaIdList = append(p.MediaIdList, _elem33)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CostType = common.CostType(v)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PlacementType = common.AdPlacementType(v)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) readField7(iprot thrift.TProtocol) error {
	p.DeverStatsGroupingParam = NewDeverStatsGroupingParam()
	if err := p.DeverStatsGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverStatsGroupingParam)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDeverStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDeverStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetDeverStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *GetDeverStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaIdList != nil {
		if err := oprot.WriteFieldBegin("mediaIdList", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaIdList: %s", p, err)
		}
	}
	return err
}

func (p *GetDeverStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *GetDeverStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:costType: %s", p, err)
	}
	return err
}

func (p *GetDeverStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:placementType: %s", p, err)
	}
	return err
}

func (p *GetDeverStatsDataArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.DeverStatsGroupingParam != nil {
		if err := oprot.WriteFieldBegin("deverStatsGroupingParam", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:deverStatsGroupingParam: %s", p, err)
		}
		if err := p.DeverStatsGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverStatsGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:deverStatsGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *GetDeverStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeverStatsDataArgs(%+v)", *p)
}

type GetDeverStatsDataResult struct {
	Success []*DeverStatsData        `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewGetDeverStatsDataResult() *GetDeverStatsDataResult {
	return &GetDeverStatsDataResult{}
}

func (p *GetDeverStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDeverStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DeverStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem34 := NewDeverStatsData()
		if err := _elem34.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem34)
		}
		p.Success = append(p.Success, _elem34)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetDeverStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *GetDeverStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDeverStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDeverStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDeverStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *GetDeverStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeverStatsDataResult(%+v)", *p)
}

type QueryDeverStatsDataArgs struct {
	RequestHeader *common.RequestHeader  `thrift:"requestHeader,1" json:"requestHeader"`
	DeverId       common.IdInt           `thrift:"deverId,2" json:"deverId"`
	MediaIdList   []common.IdInt         `thrift:"mediaIdList,3" json:"mediaIdList"`
	DateParam     *DateParam             `thrift:"dateParam,4" json:"dateParam"`
	CostType      common.CostType        `thrift:"costType,5" json:"costType"`
	PlacementType common.AdPlacementType `thrift:"placementType,6" json:"placementType"`
}

func NewQueryDeverStatsDataArgs() *QueryDeverStatsDataArgs {
	return &QueryDeverStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryDeverStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *QueryDeverStatsDataArgs) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *QueryDeverStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = common.IdInt(v)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem35 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem35 = common.IdInt(v)
		}
		p.MediaIdList = append(p.MediaIdList, _elem35)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CostType = common.CostType(v)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PlacementType = common.AdPlacementType(v)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaIdList != nil {
		if err := oprot.WriteFieldBegin("mediaIdList", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaIdList: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:costType: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:placementType: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverStatsDataArgs(%+v)", *p)
}

type QueryDeverStatsDataResult struct {
	Success []*DeverStatsData        `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewQueryDeverStatsDataResult() *QueryDeverStatsDataResult {
	return &QueryDeverStatsDataResult{}
}

func (p *QueryDeverStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DeverStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem36 := NewDeverStatsData()
		if err := _elem36.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem36)
		}
		p.Success = append(p.Success, _elem36)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverStatsDataResult(%+v)", *p)
}

type QueryDeverExtStatsDataArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	DeverId       common.IdInt          `thrift:"deverId,2" json:"deverId"`
	ExtStatsType  ExtStatsType          `thrift:"extStatsType,3" json:"extStatsType"`
	MediaIdList   []common.IdInt        `thrift:"mediaIdList,4" json:"mediaIdList"`
	MergeById     bool                  `thrift:"mergeById,5" json:"mergeById"`
	DateParam     *DateParam            `thrift:"dateParam,6" json:"dateParam"`
}

func NewQueryDeverExtStatsDataArgs() *QueryDeverExtStatsDataArgs {
	return &QueryDeverExtStatsDataArgs{
		ExtStatsType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryDeverExtStatsDataArgs) IsSetExtStatsType() bool {
	return int64(p.ExtStatsType) != math.MinInt32-1
}

func (p *QueryDeverExtStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = common.IdInt(v)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExtStatsType = ExtStatsType(v)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem37 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem37 = common.IdInt(v)
		}
		p.MediaIdList = append(p.MediaIdList, _elem37)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MergeById = v
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverExtStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverExtStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverExtStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *QueryDeverExtStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtStatsType() {
		if err := oprot.WriteFieldBegin("extStatsType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:extStatsType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ExtStatsType)); err != nil {
			return fmt.Errorf("%T.extStatsType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:extStatsType: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverExtStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.MediaIdList != nil {
		if err := oprot.WriteFieldBegin("mediaIdList", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:mediaIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:mediaIdList: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverExtStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mergeById", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mergeById: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MergeById)); err != nil {
		return fmt.Errorf("%T.mergeById (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mergeById: %s", p, err)
	}
	return err
}

func (p *QueryDeverExtStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverExtStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverExtStatsDataArgs(%+v)", *p)
}

type QueryDeverExtStatsDataResult struct {
	Success []*DeverExtStatsData     `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewQueryDeverExtStatsDataResult() *QueryDeverExtStatsDataResult {
	return &QueryDeverExtStatsDataResult{}
}

func (p *QueryDeverExtStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverExtStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DeverExtStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem38 := NewDeverExtStatsData()
		if err := _elem38.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem38)
		}
		p.Success = append(p.Success, _elem38)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverExtStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *QueryDeverExtStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverExtStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverExtStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverExtStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverExtStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverExtStatsDataResult(%+v)", *p)
}

type QueryDeverAppExStatsDataArgs struct {
	RequestHeader  *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	DeverId        common.IdInt          `thrift:"deverId,2" json:"deverId"`
	MediaIdList    []common.IdInt        `thrift:"mediaIdList,3" json:"mediaIdList"`
	DateParam      *DateParam            `thrift:"dateParam,4" json:"dateParam"`
	MergeByMediaId bool                  `thrift:"mergeByMediaId,5" json:"mergeByMediaId"`
}

func NewQueryDeverAppExStatsDataArgs() *QueryDeverAppExStatsDataArgs {
	return &QueryDeverAppExStatsDataArgs{}
}

func (p *QueryDeverAppExStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = common.IdInt(v)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem39 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem39 = common.IdInt(v)
		}
		p.MediaIdList = append(p.MediaIdList, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MergeByMediaId = v
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverAppExStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverAppExStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *QueryDeverAppExStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaIdList != nil {
		if err := oprot.WriteFieldBegin("mediaIdList", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaIdList: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverAppExStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverAppExStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mergeByMediaId", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mergeByMediaId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MergeByMediaId)); err != nil {
		return fmt.Errorf("%T.mergeByMediaId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mergeByMediaId: %s", p, err)
	}
	return err
}

func (p *QueryDeverAppExStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverAppExStatsDataArgs(%+v)", *p)
}

type QueryDeverAppExStatsDataResult struct {
	Success []*DeverAppExStatsData   `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewQueryDeverAppExStatsDataResult() *QueryDeverAppExStatsDataResult {
	return &QueryDeverAppExStatsDataResult{}
}

func (p *QueryDeverAppExStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DeverAppExStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem40 := NewDeverAppExStatsData()
		if err := _elem40.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem40)
		}
		p.Success = append(p.Success, _elem40)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverAppExStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverAppExStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverAppExStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverAppExStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverAppExStatsDataResult(%+v)", *p)
}

type QueryAppBasicStatsArgs struct {
	RequestHeader     *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	DeverId           common.IdInt             `thrift:"deverId,2" json:"deverId"`
	MediaIdList       []common.IdInt           `thrift:"mediaIdList,3" json:"mediaIdList"`
	PlacementIdList   []common.IdInt           `thrift:"placementIdList,4" json:"placementIdList"`
	DateParam         *DateParam               `thrift:"dateParam,5" json:"dateParam"`
	CostTypeList      []common.CostType        `thrift:"costTypeList,6" json:"costTypeList"`
	PlacementTypeList []common.AdPlacementType `thrift:"placementTypeList,7" json:"placementTypeList"`
}

func NewQueryAppBasicStatsArgs() *QueryAppBasicStatsArgs {
	return &QueryAppBasicStatsArgs{}
}

func (p *QueryAppBasicStatsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = common.IdInt(v)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem41 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem41 = common.IdInt(v)
		}
		p.MediaIdList = append(p.MediaIdList, _elem41)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlacementIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem42 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem42 = common.IdInt(v)
		}
		p.PlacementIdList = append(p.PlacementIdList, _elem42)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField5(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CostTypeList = make([]common.CostType, 0, size)
	for i := 0; i < size; i++ {
		var _elem43 common.CostType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem43 = common.CostType(v)
		}
		p.CostTypeList = append(p.CostTypeList, _elem43)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlacementTypeList = make([]common.AdPlacementType, 0, size)
	for i := 0; i < size; i++ {
		var _elem44 common.AdPlacementType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem44 = common.AdPlacementType(v)
		}
		p.PlacementTypeList = append(p.PlacementTypeList, _elem44)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAppBasicStats_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAppBasicStatsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *QueryAppBasicStatsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaIdList != nil {
		if err := oprot.WriteFieldBegin("mediaIdList", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaIdList: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.PlacementIdList != nil {
		if err := oprot.WriteFieldBegin("placementIdList", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:placementIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlacementIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlacementIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:placementIdList: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.CostTypeList != nil {
		if err := oprot.WriteFieldBegin("costTypeList", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:costTypeList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CostTypeList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CostTypeList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:costTypeList: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.PlacementTypeList != nil {
		if err := oprot.WriteFieldBegin("placementTypeList", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:placementTypeList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlacementTypeList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlacementTypeList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:placementTypeList: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAppBasicStatsArgs(%+v)", *p)
}

type QueryAppBasicStatsResult struct {
	Success []*DeverStatsData        `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewQueryAppBasicStatsResult() *QueryAppBasicStatsResult {
	return &QueryAppBasicStatsResult{}
}

func (p *QueryAppBasicStatsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAppBasicStatsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DeverStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem45 := NewDeverStatsData()
		if err := _elem45.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem45)
		}
		p.Success = append(p.Success, _elem45)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppBasicStatsResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *QueryAppBasicStatsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAppBasicStats_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAppBasicStatsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppBasicStatsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAppBasicStatsResult(%+v)", *p)
}

type QueryAppDetailStatsArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	DeverId       common.IdInt          `thrift:"deverId,2" json:"deverId"`
	IdList        []common.IdInt        `thrift:"idList,3" json:"idList"`
	IdType        DeverIdType           `thrift:"idType,4" json:"idType"`
	DateParam     *DateParam            `thrift:"dateParam,5" json:"dateParam"`
}

func NewQueryAppDetailStatsArgs() *QueryAppDetailStatsArgs {
	return &QueryAppDetailStatsArgs{
		IdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryAppDetailStatsArgs) IsSetIdType() bool {
	return int64(p.IdType) != math.MinInt32-1
}

func (p *QueryAppDetailStatsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = common.IdInt(v)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem46 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem46 = common.IdInt(v)
		}
		p.IdList = append(p.IdList, _elem46)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IdType = DeverIdType(v)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) readField5(iprot thrift.TProtocol) error {
	p.DateParam = NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAppDetailStats_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAppDetailStatsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppDetailStatsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *QueryAppDetailStatsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IdList != nil {
		if err := oprot.WriteFieldBegin("idList", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:idList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:idList: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppDetailStatsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdType() {
		if err := oprot.WriteFieldBegin("idType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:idType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IdType)); err != nil {
			return fmt.Errorf("%T.idType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:idType: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppDetailStatsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppDetailStatsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAppDetailStatsArgs(%+v)", *p)
}

type QueryAppDetailStatsResult struct {
	Success []*DeverStatsData        `thrift:"success,0" json:"success"`
	Sdqe    *StatsDataQueryException `thrift:"sdqe,1" json:"sdqe"`
}

func NewQueryAppDetailStatsResult() *QueryAppDetailStatsResult {
	return &QueryAppDetailStatsResult{}
}

func (p *QueryAppDetailStatsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAppDetailStatsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DeverStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem47 := NewDeverStatsData()
		if err := _elem47.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem47)
		}
		p.Success = append(p.Success, _elem47)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAppDetailStatsResult) readField1(iprot thrift.TProtocol) error {
	p.Sdqe = NewStatsDataQueryException()
	if err := p.Sdqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sdqe)
	}
	return nil
}

func (p *QueryAppDetailStatsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAppDetailStats_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Sdqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAppDetailStatsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppDetailStatsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sdqe != nil {
		if err := oprot.WriteFieldBegin("sdqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdqe: %s", p, err)
		}
		if err := p.Sdqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sdqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryAppDetailStatsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAppDetailStatsResult(%+v)", *p)
}
