// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"stats"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   queryAderStatsData(RequestHeader requestHeader, IdInt aderId, AderStatsGroupingParam aderStatsGroupingParam, DateParam dateParam, bool groupByMediaType, CostType costType)")
	fmt.Fprintln(os.<PERSON>derr, "   getDeverStatsData(RequestHeader requestHeader, IdInt deverId,  mediaIdList, DateParam dateParam, CostType costType, AdPlacementType placementType, DeverStatsGroupingParam deverStatsGroupingParam)")
	fmt.Fprintln(os.Stderr, "   queryDeverStatsData(RequestHeader requestHeader, IdInt deverId,  mediaIdList, DateParam dateParam, CostType costType, AdPlacementType placementType)")
	fmt.Fprintln(os.Stderr, "   queryDeverExtStatsData(RequestHeader requestHeader, IdInt deverId, ExtStatsType extStatsType,  mediaIdList, bool mergeById, DateParam dateParam)")
	fmt.Fprintln(os.Stderr, "   queryDeverAppExStatsData(RequestHeader requestHeader, IdInt deverId,  mediaIdList, DateParam dateParam, bool mergeByMediaId)")
	fmt.Fprintln(os.Stderr, "   queryAppBasicStats(RequestHeader requestHeader, IdInt deverId,  mediaIdList,  placementIdList, DateParam dateParam,  costTypeList,  placementTypeList)")
	fmt.Fprintln(os.Stderr, "   queryAppDetailStats(RequestHeader requestHeader, IdInt deverId,  idList, DeverIdType idType, DateParam dateParam)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := stats.NewStatsDataQueryClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryAderStatsData":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryAderStatsData requires 6 args")
			flag.Usage()
		}
		arg48 := flag.Arg(1)
		mbTrans49 := thrift.NewTMemoryBufferLen(len(arg48))
		defer mbTrans49.Close()
		_, err50 := mbTrans49.WriteString(arg48)
		if err50 != nil {
			Usage()
			return
		}
		factory51 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt52 := factory51.GetProtocol(mbTrans49)
		argvalue0 := stats.NewRequestHeader()
		err53 := argvalue0.Read(jsProt52)
		if err53 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err54 := (strconv.Atoi(flag.Arg(2)))
		if err54 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		arg55 := flag.Arg(3)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue2 := stats.NewAderStatsGroupingParam()
		err60 := argvalue2.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg61 := flag.Arg(4)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue3 := stats.NewDateParam()
		err66 := argvalue3.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := stats.CostType(tmp5)
		value5 := stats.CostType(argvalue5)
		fmt.Print(client.QueryAderStatsData(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getDeverStatsData":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetDeverStatsData requires 7 args")
			flag.Usage()
		}
		arg68 := flag.Arg(1)
		mbTrans69 := thrift.NewTMemoryBufferLen(len(arg68))
		defer mbTrans69.Close()
		_, err70 := mbTrans69.WriteString(arg68)
		if err70 != nil {
			Usage()
			return
		}
		factory71 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt72 := factory71.GetProtocol(mbTrans69)
		argvalue0 := stats.NewRequestHeader()
		err73 := argvalue0.Read(jsProt72)
		if err73 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err74 := (strconv.Atoi(flag.Arg(2)))
		if err74 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		arg75 := flag.Arg(3)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		containerStruct2 := stats.NewGetDeverStatsDataArgs()
		err80 := containerStruct2.ReadField3(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MediaIdList
		value2 := argvalue2
		arg81 := flag.Arg(4)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue3 := stats.NewDateParam()
		err86 := argvalue3.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := stats.CostType(tmp4)
		value4 := stats.CostType(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := stats.AdPlacementType(tmp5)
		value5 := stats.AdPlacementType(argvalue5)
		arg87 := flag.Arg(7)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue6 := stats.NewDeverStatsGroupingParam()
		err92 := argvalue6.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		fmt.Print(client.GetDeverStatsData(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "queryDeverStatsData":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryDeverStatsData requires 6 args")
			flag.Usage()
		}
		arg93 := flag.Arg(1)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue0 := stats.NewRequestHeader()
		err98 := argvalue0.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err99 := (strconv.Atoi(flag.Arg(2)))
		if err99 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		arg100 := flag.Arg(3)
		mbTrans101 := thrift.NewTMemoryBufferLen(len(arg100))
		defer mbTrans101.Close()
		_, err102 := mbTrans101.WriteString(arg100)
		if err102 != nil {
			Usage()
			return
		}
		factory103 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt104 := factory103.GetProtocol(mbTrans101)
		containerStruct2 := stats.NewQueryDeverStatsDataArgs()
		err105 := containerStruct2.ReadField3(jsProt104)
		if err105 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MediaIdList
		value2 := argvalue2
		arg106 := flag.Arg(4)
		mbTrans107 := thrift.NewTMemoryBufferLen(len(arg106))
		defer mbTrans107.Close()
		_, err108 := mbTrans107.WriteString(arg106)
		if err108 != nil {
			Usage()
			return
		}
		factory109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt110 := factory109.GetProtocol(mbTrans107)
		argvalue3 := stats.NewDateParam()
		err111 := argvalue3.Read(jsProt110)
		if err111 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := stats.CostType(tmp4)
		value4 := stats.CostType(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := stats.AdPlacementType(tmp5)
		value5 := stats.AdPlacementType(argvalue5)
		fmt.Print(client.QueryDeverStatsData(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "queryDeverExtStatsData":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryDeverExtStatsData requires 6 args")
			flag.Usage()
		}
		arg112 := flag.Arg(1)
		mbTrans113 := thrift.NewTMemoryBufferLen(len(arg112))
		defer mbTrans113.Close()
		_, err114 := mbTrans113.WriteString(arg112)
		if err114 != nil {
			Usage()
			return
		}
		factory115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt116 := factory115.GetProtocol(mbTrans113)
		argvalue0 := stats.NewRequestHeader()
		err117 := argvalue0.Read(jsProt116)
		if err117 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err118 := (strconv.Atoi(flag.Arg(2)))
		if err118 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := stats.ExtStatsType(tmp2)
		value2 := argvalue2
		arg119 := flag.Arg(4)
		mbTrans120 := thrift.NewTMemoryBufferLen(len(arg119))
		defer mbTrans120.Close()
		_, err121 := mbTrans120.WriteString(arg119)
		if err121 != nil {
			Usage()
			return
		}
		factory122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt123 := factory122.GetProtocol(mbTrans120)
		containerStruct3 := stats.NewQueryDeverExtStatsDataArgs()
		err124 := containerStruct3.ReadField4(jsProt123)
		if err124 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.MediaIdList
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		arg126 := flag.Arg(6)
		mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
		defer mbTrans127.Close()
		_, err128 := mbTrans127.WriteString(arg126)
		if err128 != nil {
			Usage()
			return
		}
		factory129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt130 := factory129.GetProtocol(mbTrans127)
		argvalue5 := stats.NewDateParam()
		err131 := argvalue5.Read(jsProt130)
		if err131 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		fmt.Print(client.QueryDeverExtStatsData(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "queryDeverAppExStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryDeverAppExStatsData requires 5 args")
			flag.Usage()
		}
		arg132 := flag.Arg(1)
		mbTrans133 := thrift.NewTMemoryBufferLen(len(arg132))
		defer mbTrans133.Close()
		_, err134 := mbTrans133.WriteString(arg132)
		if err134 != nil {
			Usage()
			return
		}
		factory135 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt136 := factory135.GetProtocol(mbTrans133)
		argvalue0 := stats.NewRequestHeader()
		err137 := argvalue0.Read(jsProt136)
		if err137 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err138 := (strconv.Atoi(flag.Arg(2)))
		if err138 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		arg139 := flag.Arg(3)
		mbTrans140 := thrift.NewTMemoryBufferLen(len(arg139))
		defer mbTrans140.Close()
		_, err141 := mbTrans140.WriteString(arg139)
		if err141 != nil {
			Usage()
			return
		}
		factory142 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt143 := factory142.GetProtocol(mbTrans140)
		containerStruct2 := stats.NewQueryDeverAppExStatsDataArgs()
		err144 := containerStruct2.ReadField3(jsProt143)
		if err144 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MediaIdList
		value2 := argvalue2
		arg145 := flag.Arg(4)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		argvalue3 := stats.NewDateParam()
		err150 := argvalue3.Read(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.QueryDeverAppExStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryAppBasicStats":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryAppBasicStats requires 7 args")
			flag.Usage()
		}
		arg152 := flag.Arg(1)
		mbTrans153 := thrift.NewTMemoryBufferLen(len(arg152))
		defer mbTrans153.Close()
		_, err154 := mbTrans153.WriteString(arg152)
		if err154 != nil {
			Usage()
			return
		}
		factory155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt156 := factory155.GetProtocol(mbTrans153)
		argvalue0 := stats.NewRequestHeader()
		err157 := argvalue0.Read(jsProt156)
		if err157 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err158 := (strconv.Atoi(flag.Arg(2)))
		if err158 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		arg159 := flag.Arg(3)
		mbTrans160 := thrift.NewTMemoryBufferLen(len(arg159))
		defer mbTrans160.Close()
		_, err161 := mbTrans160.WriteString(arg159)
		if err161 != nil {
			Usage()
			return
		}
		factory162 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt163 := factory162.GetProtocol(mbTrans160)
		containerStruct2 := stats.NewQueryAppBasicStatsArgs()
		err164 := containerStruct2.ReadField3(jsProt163)
		if err164 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MediaIdList
		value2 := argvalue2
		arg165 := flag.Arg(4)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		containerStruct3 := stats.NewQueryAppBasicStatsArgs()
		err170 := containerStruct3.ReadField4(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.PlacementIdList
		value3 := argvalue3
		arg171 := flag.Arg(5)
		mbTrans172 := thrift.NewTMemoryBufferLen(len(arg171))
		defer mbTrans172.Close()
		_, err173 := mbTrans172.WriteString(arg171)
		if err173 != nil {
			Usage()
			return
		}
		factory174 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt175 := factory174.GetProtocol(mbTrans172)
		argvalue4 := stats.NewDateParam()
		err176 := argvalue4.Read(jsProt175)
		if err176 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg177 := flag.Arg(6)
		mbTrans178 := thrift.NewTMemoryBufferLen(len(arg177))
		defer mbTrans178.Close()
		_, err179 := mbTrans178.WriteString(arg177)
		if err179 != nil {
			Usage()
			return
		}
		factory180 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt181 := factory180.GetProtocol(mbTrans178)
		containerStruct5 := stats.NewQueryAppBasicStatsArgs()
		err182 := containerStruct5.ReadField6(jsProt181)
		if err182 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.CostTypeList
		value5 := argvalue5
		arg183 := flag.Arg(7)
		mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
		defer mbTrans184.Close()
		_, err185 := mbTrans184.WriteString(arg183)
		if err185 != nil {
			Usage()
			return
		}
		factory186 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt187 := factory186.GetProtocol(mbTrans184)
		containerStruct6 := stats.NewQueryAppBasicStatsArgs()
		err188 := containerStruct6.ReadField7(jsProt187)
		if err188 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.PlacementTypeList
		value6 := argvalue6
		fmt.Print(client.QueryAppBasicStats(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "queryAppDetailStats":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryAppDetailStats requires 5 args")
			flag.Usage()
		}
		arg189 := flag.Arg(1)
		mbTrans190 := thrift.NewTMemoryBufferLen(len(arg189))
		defer mbTrans190.Close()
		_, err191 := mbTrans190.WriteString(arg189)
		if err191 != nil {
			Usage()
			return
		}
		factory192 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt193 := factory192.GetProtocol(mbTrans190)
		argvalue0 := stats.NewRequestHeader()
		err194 := argvalue0.Read(jsProt193)
		if err194 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err195 := (strconv.Atoi(flag.Arg(2)))
		if err195 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := stats.IdInt(argvalue1)
		arg196 := flag.Arg(3)
		mbTrans197 := thrift.NewTMemoryBufferLen(len(arg196))
		defer mbTrans197.Close()
		_, err198 := mbTrans197.WriteString(arg196)
		if err198 != nil {
			Usage()
			return
		}
		factory199 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt200 := factory199.GetProtocol(mbTrans197)
		containerStruct2 := stats.NewQueryAppDetailStatsArgs()
		err201 := containerStruct2.ReadField3(jsProt200)
		if err201 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.IdList
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := stats.DeverIdType(tmp3)
		value3 := argvalue3
		arg202 := flag.Arg(5)
		mbTrans203 := thrift.NewTMemoryBufferLen(len(arg202))
		defer mbTrans203.Close()
		_, err204 := mbTrans203.WriteString(arg202)
		if err204 != nil {
			Usage()
			return
		}
		factory205 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt206 := factory205.GetProtocol(mbTrans203)
		argvalue4 := stats.NewDateParam()
		err207 := argvalue4.Read(jsProt206)
		if err207 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		fmt.Print(client.QueryAppDetailStats(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
