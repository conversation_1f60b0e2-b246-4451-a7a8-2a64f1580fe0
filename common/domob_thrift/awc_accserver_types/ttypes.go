// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package awc_accserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//加入不同家的积分墙，标记积分来源
type Channel int64

const (
	Channel_AOW       Channel = 1
	Channel_DIANJOY   Channel = 2
	Channel_ANDROIDH5 Channel = 3
)

func (p Channel) String() string {
	switch p {
	case Channel_AOW:
		return "Channel_AOW"
	case Channel_DIANJOY:
		return "Channel_DIANJOY"
	case Channel_ANDROIDH5:
		return "Channel_ANDROIDH5"
	}
	return "<UNSET>"
}

func ChannelFromString(s string) (Channel, error) {
	switch s {
	case "Channel_AOW":
		return Channel_AOW, nil
	case "Channel_DIANJOY":
		return Channel_DIANJOY, nil
	case "Channel_ANDROIDH5":
		return Channel_ANDROIDH5, nil
	}
	return Channel(math.MinInt32 - 1), fmt.Errorf("not a valid Channel string")
}

type WechatUser struct {
	Uid      int32  `thrift:"uid,1" json:"uid"`
	Balance  int32  `thrift:"balance,2" json:"balance"`
	Used     int32  `thrift:"used,3" json:"used"`
	Total    int32  `thrift:"total,4" json:"total"`
	Fee      int32  `thrift:"fee,5" json:"fee"`
	Withdraw bool   `thrift:"withdraw,6" json:"withdraw"`
	Avatar   string `thrift:"avatar,7" json:"avatar"`
}

func NewWechatUser() *WechatUser {
	return &WechatUser{}
}

func (p *WechatUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WechatUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *WechatUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Balance = v
	}
	return nil
}

func (p *WechatUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Used = v
	}
	return nil
}

func (p *WechatUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *WechatUser) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Fee = v
	}
	return nil
}

func (p *WechatUser) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Withdraw = v
	}
	return nil
}

func (p *WechatUser) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Avatar = v
	}
	return nil
}

func (p *WechatUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("WechatUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WechatUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *WechatUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:balance: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:balance: %s", p, err)
	}
	return err
}

func (p *WechatUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("used", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:used: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Used)); err != nil {
		return fmt.Errorf("%T.used (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:used: %s", p, err)
	}
	return err
}

func (p *WechatUser) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:total: %s", p, err)
	}
	return err
}

func (p *WechatUser) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fee", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:fee: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Fee)); err != nil {
		return fmt.Errorf("%T.fee (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:fee: %s", p, err)
	}
	return err
}

func (p *WechatUser) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("withdraw", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:withdraw: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Withdraw)); err != nil {
		return fmt.Errorf("%T.withdraw (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:withdraw: %s", p, err)
	}
	return err
}

func (p *WechatUser) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("avatar", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:avatar: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Avatar)); err != nil {
		return fmt.Errorf("%T.avatar (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:avatar: %s", p, err)
	}
	return err
}

func (p *WechatUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WechatUser(%+v)", *p)
}

type TaskRecord struct {
	Name      string `thrift:"name,1" json:"name"`
	TaskUid   int32  `thrift:"task_uid,2" json:"task_uid"`
	Action    int16  `thrift:"action,3" json:"action"`
	Point     int32  `thrift:"point,4" json:"point"`
	Timestamp int32  `thrift:"timestamp,5" json:"timestamp"`
	Logo      string `thrift:"logo,6" json:"logo"`
}

func NewTaskRecord() *TaskRecord {
	return &TaskRecord{}
}

func (p *TaskRecord) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskRecord) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *TaskRecord) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskUid = v
	}
	return nil
}

func (p *TaskRecord) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *TaskRecord) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *TaskRecord) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *TaskRecord) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *TaskRecord) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskRecord"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *TaskRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskUid)); err != nil {
		return fmt.Errorf("%T.task_uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_uid: %s", p, err)
	}
	return err
}

func (p *TaskRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:action: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Action)); err != nil {
		return fmt.Errorf("%T.action (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:action: %s", p, err)
	}
	return err
}

func (p *TaskRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:point: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Point)); err != nil {
		return fmt.Errorf("%T.point (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:point: %s", p, err)
	}
	return err
}

func (p *TaskRecord) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *TaskRecord) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:logo: %s", p, err)
	}
	return err
}

func (p *TaskRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskRecord(%+v)", *p)
}

type Invitee struct {
	Name      string `thrift:"name,1" json:"name"`
	Logo      string `thrift:"logo,2" json:"logo"`
	Timestamp int32  `thrift:"timestamp,3" json:"timestamp"`
	Status    int16  `thrift:"status,4" json:"status"`
}

func NewInvitee() *Invitee {
	return &Invitee{}
}

func (p *Invitee) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Invitee) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Invitee) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *Invitee) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *Invitee) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Invitee) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Invitee"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Invitee) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Invitee) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:logo: %s", p, err)
	}
	return err
}

func (p *Invitee) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:timestamp: %s", p, err)
	}
	return err
}

func (p *Invitee) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:status: %s", p, err)
	}
	return err
}

func (p *Invitee) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Invitee(%+v)", *p)
}

type ExchRequest struct {
	Openid  string `thrift:"openid,1" json:"openid"`
	Consume int32  `thrift:"consume,2" json:"consume"`
}

func NewExchRequest() *ExchRequest {
	return &ExchRequest{}
}

func (p *ExchRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExchRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Openid = v
	}
	return nil
}

func (p *ExchRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Consume = v
	}
	return nil
}

func (p *ExchRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ExchRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExchRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openid", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:openid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Openid)); err != nil {
		return fmt.Errorf("%T.openid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:openid: %s", p, err)
	}
	return err
}

func (p *ExchRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consume", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:consume: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Consume)); err != nil {
		return fmt.Errorf("%T.consume (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:consume: %s", p, err)
	}
	return err
}

func (p *ExchRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExchRequest(%+v)", *p)
}

type ExchResponse struct {
	Status bool   `thrift:"status,1" json:"status"`
	Msg    string `thrift:"msg,2" json:"msg"`
}

func NewExchResponse() *ExchResponse {
	return &ExchResponse{}
}

func (p *ExchResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExchResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *ExchResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *ExchResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ExchResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExchResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *ExchResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *ExchResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExchResponse(%+v)", *p)
}

type DomobCallBack struct {
	Uid     int32   `thrift:"uid,1" json:"uid"`
	Orderid int64   `thrift:"orderid,2" json:"orderid"`
	Adid    int32   `thrift:"adid,3" json:"adid"`
	Adname  string  `thrift:"adname,4" json:"adname"`
	Action  int16   `thrift:"action,5" json:"action"`
	Price   int32   `thrift:"price,6" json:"price"`
	Point   int32   `thrift:"point,7" json:"point"`
	Channel Channel `thrift:"channel,8" json:"channel"`
}

func NewDomobCallBack() *DomobCallBack {
	return &DomobCallBack{
		Channel: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DomobCallBack) IsSetChannel() bool {
	return int64(p.Channel) != math.MinInt32-1
}

func (p *DomobCallBack) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I16 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DomobCallBack) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *DomobCallBack) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Orderid = v
	}
	return nil
}

func (p *DomobCallBack) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Adid = v
	}
	return nil
}

func (p *DomobCallBack) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Adname = v
	}
	return nil
}

func (p *DomobCallBack) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *DomobCallBack) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *DomobCallBack) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *DomobCallBack) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Channel = Channel(v)
	}
	return nil
}

func (p *DomobCallBack) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DomobCallBack"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DomobCallBack) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:orderid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Orderid)); err != nil {
		return fmt.Errorf("%T.orderid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:orderid: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:adid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Adid)); err != nil {
		return fmt.Errorf("%T.adid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:adid: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adname", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:adname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Adname)); err != nil {
		return fmt.Errorf("%T.adname (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:adname: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I16, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:action: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Action)); err != nil {
		return fmt.Errorf("%T.action (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:action: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:price: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Price)); err != nil {
		return fmt.Errorf("%T.price (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:price: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:point: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Point)); err != nil {
		return fmt.Errorf("%T.point (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:point: %s", p, err)
	}
	return err
}

func (p *DomobCallBack) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannel() {
		if err := oprot.WriteFieldBegin("channel", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:channel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Channel)); err != nil {
			return fmt.Errorf("%T.channel (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:channel: %s", p, err)
		}
	}
	return err
}

func (p *DomobCallBack) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DomobCallBack(%+v)", *p)
}
