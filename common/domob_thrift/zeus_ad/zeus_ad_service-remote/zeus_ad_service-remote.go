// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"zeus_ad"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>der<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i64 addCampaign(RequestHeader header, ZeusCampaign campaign)")
	fmt.Fprintln(os.Stderr, "  i64 updateCampaign(RequestHeader header, ZeusCampaign campaign)")
	fmt.Fprintln(os.<PERSON>, "  i64 updateCampaignInfo(RequestHeader header, ZeusCampaign campaign)")
	fmt.Fprintln(os.<PERSON>derr, "  ZeusMultiReturn updateCampaignInfoStatus(RequestHeader header,  campaigns_status)")
	fmt.Fprintln(os.Stderr, "  i64 updateCampaignFbAuditStatus(RequestHeader header, i64 zeus_campaign_id, ObjectAuditStatus fb_audit_status)")
	fmt.Fprintln(os.Stderr, "  i64 updateCampaignFields(RequestHeader header, i64 zeus_campaign_id,  field_to_value)")
	fmt.Fprintln(os.Stderr, "  i64 operateCampaigns(RequestHeader header,  ids, ObjectOperateType operate_type)")
	fmt.Fprintln(os.Stderr, "  i64 operateCampaignInfos(RequestHeader header,  ids, ObjectOperateType operate_type)")
	fmt.Fprintln(os.Stderr, "   getZeusCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getIdMappingCampaignByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getNoDeletedCampaignRules(RequestHeader header, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   fetchCampaignAdMappingInfos(RequestHeader header, i64 begin_time, i64 end_time)")
	fmt.Fprintln(os.Stderr, "   getAccountWithCampaigns(RequestHeader header, i64 begin_time, i64 end_time)")
	fmt.Fprintln(os.Stderr, "   getAccountWithCampaignsForEndtimeLtNow(RequestHeader header, i64 now)")
	fmt.Fprintln(os.Stderr, "   getCampaignsByFbAccountId(RequestHeader header, i64 zeus_user_id, i64 fb_account_id, i64 fb_user_id, i64 offset, i64 limit,  order_columns, string search)")
	fmt.Fprintln(os.Stderr, "  i64 getCampaignCountByFbAccountId(RequestHeader header, i64 zeus_user_id, i64 fb_account_id, i64 fb_user_id, string search)")
	fmt.Fprintln(os.Stderr, "   getAccountWithCampaignsInSystemStatus(RequestHeader header, i64 begin_time, i64 end_time)")
	fmt.Fprintln(os.Stderr, "  i64 preDelCampaigns(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 deleteCampaign(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateFbAuditStatus(RequestHeader header,  audit_infos)")
	fmt.Fprintln(os.Stderr, "   getIdWithFbAuditStatusByCampaignIds(RequestHeader header,  campaign_ids)")
	fmt.Fprintln(os.Stderr, "   getIdWithFbAuditStatusInfoByCampaignIds(RequestHeader header,  campaign_ids)")
	fmt.Fprintln(os.Stderr, "   getZeusCampaignsByAuditStatus(RequestHeader header, ObjectAuditStatus status, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  i64 addPromotion(RequestHeader header, ZeusPromotion promotions)")
	fmt.Fprintln(os.Stderr, "   getPromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getPromotionsByFbAccountId(RequestHeader header, i64 zeus_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "  i64 deletePromotions(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addAdsets(RequestHeader header,  ad_sets)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateAdsets(RequestHeader header,  ad_sets)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateAdsetInfos(RequestHeader header,  ad_sets)")
	fmt.Fprintln(os.Stderr, "  i64 operateAdsetInfos(RequestHeader header,  adset_ids, ObjectOperateType operate_type)")
	fmt.Fprintln(os.Stderr, "   getAdsetIdWithFbUserIdByStatus(RequestHeader header, ObjectRunningStatus status, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  i64 deleteAdsets(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getAdsetsByCampaignId(RequestHeader header, i64 zeus_campaign_id)")
	fmt.Fprintln(os.Stderr, "   getAdsetsByGroupIds(RequestHeader header,  group_ids)")
	fmt.Fprintln(os.Stderr, "   getAdsetsByIds(RequestHeader header,  zeus_adset_ids)")
	fmt.Fprintln(os.Stderr, "   getLocationMapByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "   getLocationMapByCampaignId(RequestHeader header, i64 zeus_campaign_id)")
	fmt.Fprintln(os.Stderr, "   getLocationMapByFbPromotionId(RequestHeader header, i64 zeus_user_id, i64 fb_promotion_id)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addCreatives(RequestHeader header,  creatives)")
	fmt.Fprintln(os.Stderr, "   getCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusImagesAndTexts getImagesAndTextsByCreativeIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateCreatives(RequestHeader header,  creatives)")
	fmt.Fprintln(os.Stderr, "  i64 deleteCreatives(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addAds(RequestHeader header,  ads)")
	fmt.Fprintln(os.Stderr, "   getAdsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateAdInfos(RequestHeader header,  ads)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateAds(RequestHeader header,  ads)")
	fmt.Fprintln(os.Stderr, "  i64 operateAdInfos(RequestHeader header,  ad_ids, ObjectOperateType operate_type)")
	fmt.Fprintln(os.Stderr, "  i64 deleteAds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusAd getAdByAdsetId(RequestHeader header, i64 zeus_adset_id)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addImages(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i64 group_id,  images)")
	fmt.Fprintln(os.Stderr, "   getImagesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getImagesByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i16 image_ratio_type)")
	fmt.Fprintln(os.Stderr, "  i64 getImageCountByFbAccountId(RequestHeader header, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "   getImagesByFbAccountId(RequestHeader header, i64 fb_account_id, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  i64 deleteImages(RequestHeader header,  image_ids, i64 zeus_user_id, i64 fb_account_id, i64 group_id)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addVideos(RequestHeader header,  videos)")
	fmt.Fprintln(os.Stderr, "   getVideosByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusVideoInfo getVideoInfoByFbVideoId(RequestHeader header, i64 fb_video_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteVideos(RequestHeader header,  video_ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addVideoThumbs(RequestHeader header,  video_thumbs)")
	fmt.Fprintln(os.Stderr, "   getVideoThumbsByZeusVideoId(RequestHeader header, i64 zeus_video_id)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addCreativeTexts(RequestHeader header,  creative_texts)")
	fmt.Fprintln(os.Stderr, "   getCreativeTextsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getCreativeTextsBySourceLanguageId(RequestHeader header, i64 source_language_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteCreativeTexts(RequestHeader header,  creative_text_ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addRulesTexts(RequestHeader header,  rules_texts)")
	fmt.Fprintln(os.Stderr, "   getRulesTextsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getRulesTextsByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteRulesTexts(RequestHeader header,  rules_text_ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addInterestGroups(RequestHeader header,  interest_groups)")
	fmt.Fprintln(os.Stderr, "  i64 updateInterestGroup(RequestHeader header, ZeusInterestGroupLib interest_group)")
	fmt.Fprintln(os.Stderr, "   getInterestGroupsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getInterestGroupsByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteInterestGroups(RequestHeader header,  interest_group_ids)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addCustomAudiences(RequestHeader header,  custom_audiences)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn updateCustomAudiences(RequestHeader header,  custom_audiences)")
	fmt.Fprintln(os.Stderr, "   getCustomAudiencesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getCustomAudiencesByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteCustomAudiences(RequestHeader header,  custom_audience_ids)")
	fmt.Fprintln(os.Stderr, "  i64 addAutoAdvertiseGroup(RequestHeader header, ZeusAutoAdvertiseGroup group)")
	fmt.Fprintln(os.Stderr, "  i64 deleteAutoAdvertiseGroup(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  i64 updateAutoAdvertiseGroupInfo(RequestHeader header, ZeusAutoAdvertiseGroup group)")
	fmt.Fprintln(os.Stderr, "   getAutoAdvertiseGroupsByCampaignIds(RequestHeader header,  campaign_ids)")
	fmt.Fprintln(os.Stderr, "  i64 operateAutoAdvertiseGroupInfos(RequestHeader header,  group_ids, ObjectOperateType operate_type)")
	fmt.Fprintln(os.Stderr, "  ZeusMultiReturn addAutoAdvertiseLogs(RequestHeader header,  zeus_logs)")
	fmt.Fprintln(os.Stderr, "  i64 addLibGroup(RequestHeader header, ZeusLibGroup lib_group)")
	fmt.Fprintln(os.Stderr, "  ZeusLibsReturn getLibsByParentId(RequestHeader header, i64 parent_id, i64 lib_type, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "   getLibsByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i64 lib_type)")
	fmt.Fprintln(os.Stderr, "  ZeusLibGroup getLibGroupById(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "   getParentLibGroupsByChildId(RequestHeader header, i64 child_id, i64 lib_type)")
	fmt.Fprintln(os.Stderr, "  i64 updateLibGroup(RequestHeader header, ZeusLibGroup lib_group)")
	fmt.Fprintln(os.Stderr, "  i64 moveToGroup(RequestHeader header,  lib_group_ids,  lib_ids, i64 lib_type, i64 target_group_id)")
	fmt.Fprintln(os.Stderr, "  i64 moveImageLibToGroup(RequestHeader header,  lib_group_ids,  lib_ids, i64 zeus_user_id, i64 fb_account_id, i64 lib_group_id, i64 target_group_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteLibGroup(RequestHeader header, i64 lib_group_id, i64 lib_type)")
	fmt.Fprintln(os.Stderr, "  i64 deleteImageLibGroup(RequestHeader header, i64 lib_group_id, i64 zeus_user_id, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "  i64 addTranslationSchedule(RequestHeader header, ZeusTranslationSchedule translation_schedule)")
	fmt.Fprintln(os.Stderr, "  i64 updateTranslationSchedule(RequestHeader header, ZeusTranslationSchedule translation_schedule)")
	fmt.Fprintln(os.Stderr, "   getTranslationSchedulesBySourceLanguageId(RequestHeader header, i64 source_language_id)")
	fmt.Fprintln(os.Stderr, "   getTranslationSchedulesByStatusCodes(RequestHeader header,  status_codes)")
	fmt.Fprintln(os.Stderr, "  i64 deleteTranslationSchedules(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addTranslationText(RequestHeader header, ZeusTranslationTextLib translation_text)")
	fmt.Fprintln(os.Stderr, "  i64 updateTranslationText(RequestHeader header, ZeusTranslationTextLib translation_text)")
	fmt.Fprintln(os.Stderr, "   getTranslationTextsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  ZeusTranslationTextLib getTranslationTextByProjectId(RequestHeader header, i64 project_id)")
	fmt.Fprintln(os.Stderr, "   getTranslationTextsByProjectStatusCodes(RequestHeader header,  project_status_codes)")
	fmt.Fprintln(os.Stderr, "  ZeusTranslationTextLib getTranslationTextLibByResource(RequestHeader header, string source_text, string source_language, string target_language)")
	fmt.Fprintln(os.Stderr, "  i64 deleteTranslationTextsByProjectIds(RequestHeader header,  project_ids)")
	fmt.Fprintln(os.Stderr, "  i64 deleteTranslationTexts(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addTranslationResource(RequestHeader header, ZeusTranslationResource translation_resource)")
	fmt.Fprintln(os.Stderr, "   getTranslationResourcesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getTranslationResourcesByProjectId(RequestHeader header, i64 project_id)")
	fmt.Fprintln(os.Stderr, "   getTranslationResourcesByResourceKey(RequestHeader header, string resource_key)")
	fmt.Fprintln(os.Stderr, "  i64 deleteTranslationResources(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addZeusInterest(RequestHeader header, ZeusInterest zeus_interest)")
	fmt.Fprintln(os.Stderr, "  i64 updateZeusInterest(RequestHeader header, ZeusInterest zeus_interest)")
	fmt.Fprintln(os.Stderr, "   getAllZeusInterests(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getZeusInterestsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 deleteZeusInterestsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addTag(RequestHeader header, ZeusTag tag)")
	fmt.Fprintln(os.Stderr, "  i64 updateTag(RequestHeader header, ZeusTag tag)")
	fmt.Fprintln(os.Stderr, "   getTagsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getAllTags(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getTagsByParentId(RequestHeader header, i64 parent_id)")
	fmt.Fprintln(os.Stderr, "  i64 deleteTagsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addZeusGameWarehouse(RequestHeader header, ZeusGameWarehouse gameWarehouse)")
	fmt.Fprintln(os.Stderr, "   getZeusGameWarehousesByTagId(RequestHeader header, i64 tag_id, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  ZeusGameWarehouse getZeusGameWarehouseById(RequestHeader header, i64 id)")
	fmt.Fprintln(os.Stderr, "  ZeusGameWarehouse getZeusGameWarehouseByGameId(RequestHeader header, string game_id, i16 game_store_id)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := zeus_ad.NewZeusAdServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCampaign requires 2 args")
			flag.Usage()
		}
		arg661 := flag.Arg(1)
		mbTrans662 := thrift.NewTMemoryBufferLen(len(arg661))
		defer mbTrans662.Close()
		_, err663 := mbTrans662.WriteString(arg661)
		if err663 != nil {
			Usage()
			return
		}
		factory664 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt665 := factory664.GetProtocol(mbTrans662)
		argvalue0 := zeus_ad.NewRequestHeader()
		err666 := argvalue0.Read(jsProt665)
		if err666 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg667 := flag.Arg(2)
		mbTrans668 := thrift.NewTMemoryBufferLen(len(arg667))
		defer mbTrans668.Close()
		_, err669 := mbTrans668.WriteString(arg667)
		if err669 != nil {
			Usage()
			return
		}
		factory670 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt671 := factory670.GetProtocol(mbTrans668)
		argvalue1 := zeus_ad.NewZeusCampaign()
		err672 := argvalue1.Read(jsProt671)
		if err672 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "updateCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateCampaign requires 2 args")
			flag.Usage()
		}
		arg673 := flag.Arg(1)
		mbTrans674 := thrift.NewTMemoryBufferLen(len(arg673))
		defer mbTrans674.Close()
		_, err675 := mbTrans674.WriteString(arg673)
		if err675 != nil {
			Usage()
			return
		}
		factory676 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt677 := factory676.GetProtocol(mbTrans674)
		argvalue0 := zeus_ad.NewRequestHeader()
		err678 := argvalue0.Read(jsProt677)
		if err678 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg679 := flag.Arg(2)
		mbTrans680 := thrift.NewTMemoryBufferLen(len(arg679))
		defer mbTrans680.Close()
		_, err681 := mbTrans680.WriteString(arg679)
		if err681 != nil {
			Usage()
			return
		}
		factory682 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt683 := factory682.GetProtocol(mbTrans680)
		argvalue1 := zeus_ad.NewZeusCampaign()
		err684 := argvalue1.Read(jsProt683)
		if err684 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "updateCampaignInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateCampaignInfo requires 2 args")
			flag.Usage()
		}
		arg685 := flag.Arg(1)
		mbTrans686 := thrift.NewTMemoryBufferLen(len(arg685))
		defer mbTrans686.Close()
		_, err687 := mbTrans686.WriteString(arg685)
		if err687 != nil {
			Usage()
			return
		}
		factory688 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt689 := factory688.GetProtocol(mbTrans686)
		argvalue0 := zeus_ad.NewRequestHeader()
		err690 := argvalue0.Read(jsProt689)
		if err690 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg691 := flag.Arg(2)
		mbTrans692 := thrift.NewTMemoryBufferLen(len(arg691))
		defer mbTrans692.Close()
		_, err693 := mbTrans692.WriteString(arg691)
		if err693 != nil {
			Usage()
			return
		}
		factory694 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt695 := factory694.GetProtocol(mbTrans692)
		argvalue1 := zeus_ad.NewZeusCampaign()
		err696 := argvalue1.Read(jsProt695)
		if err696 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateCampaignInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updateCampaignInfoStatus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateCampaignInfoStatus requires 2 args")
			flag.Usage()
		}
		arg697 := flag.Arg(1)
		mbTrans698 := thrift.NewTMemoryBufferLen(len(arg697))
		defer mbTrans698.Close()
		_, err699 := mbTrans698.WriteString(arg697)
		if err699 != nil {
			Usage()
			return
		}
		factory700 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt701 := factory700.GetProtocol(mbTrans698)
		argvalue0 := zeus_ad.NewRequestHeader()
		err702 := argvalue0.Read(jsProt701)
		if err702 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg703 := flag.Arg(2)
		mbTrans704 := thrift.NewTMemoryBufferLen(len(arg703))
		defer mbTrans704.Close()
		_, err705 := mbTrans704.WriteString(arg703)
		if err705 != nil {
			Usage()
			return
		}
		factory706 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt707 := factory706.GetProtocol(mbTrans704)
		containerStruct1 := zeus_ad.NewUpdateCampaignInfoStatusArgs()
		err708 := containerStruct1.ReadField2(jsProt707)
		if err708 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignsStatus
		value1 := argvalue1
		fmt.Print(client.UpdateCampaignInfoStatus(value0, value1))
		fmt.Print("\n")
		break
	case "updateCampaignFbAuditStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateCampaignFbAuditStatus requires 3 args")
			flag.Usage()
		}
		arg709 := flag.Arg(1)
		mbTrans710 := thrift.NewTMemoryBufferLen(len(arg709))
		defer mbTrans710.Close()
		_, err711 := mbTrans710.WriteString(arg709)
		if err711 != nil {
			Usage()
			return
		}
		factory712 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt713 := factory712.GetProtocol(mbTrans710)
		argvalue0 := zeus_ad.NewRequestHeader()
		err714 := argvalue0.Read(jsProt713)
		if err714 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err715 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err715 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_ad.ObjectAuditStatus(tmp2)
		value2 := argvalue2
		fmt.Print(client.UpdateCampaignFbAuditStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateCampaignFields":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateCampaignFields requires 3 args")
			flag.Usage()
		}
		arg716 := flag.Arg(1)
		mbTrans717 := thrift.NewTMemoryBufferLen(len(arg716))
		defer mbTrans717.Close()
		_, err718 := mbTrans717.WriteString(arg716)
		if err718 != nil {
			Usage()
			return
		}
		factory719 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt720 := factory719.GetProtocol(mbTrans717)
		argvalue0 := zeus_ad.NewRequestHeader()
		err721 := argvalue0.Read(jsProt720)
		if err721 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err722 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err722 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg723 := flag.Arg(3)
		mbTrans724 := thrift.NewTMemoryBufferLen(len(arg723))
		defer mbTrans724.Close()
		_, err725 := mbTrans724.WriteString(arg723)
		if err725 != nil {
			Usage()
			return
		}
		factory726 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt727 := factory726.GetProtocol(mbTrans724)
		containerStruct2 := zeus_ad.NewUpdateCampaignFieldsArgs()
		err728 := containerStruct2.ReadField3(jsProt727)
		if err728 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.FieldToValue
		value2 := argvalue2
		fmt.Print(client.UpdateCampaignFields(value0, value1, value2))
		fmt.Print("\n")
		break
	case "operateCampaigns":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "OperateCampaigns requires 3 args")
			flag.Usage()
		}
		arg729 := flag.Arg(1)
		mbTrans730 := thrift.NewTMemoryBufferLen(len(arg729))
		defer mbTrans730.Close()
		_, err731 := mbTrans730.WriteString(arg729)
		if err731 != nil {
			Usage()
			return
		}
		factory732 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt733 := factory732.GetProtocol(mbTrans730)
		argvalue0 := zeus_ad.NewRequestHeader()
		err734 := argvalue0.Read(jsProt733)
		if err734 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg735 := flag.Arg(2)
		mbTrans736 := thrift.NewTMemoryBufferLen(len(arg735))
		defer mbTrans736.Close()
		_, err737 := mbTrans736.WriteString(arg735)
		if err737 != nil {
			Usage()
			return
		}
		factory738 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt739 := factory738.GetProtocol(mbTrans736)
		containerStruct1 := zeus_ad.NewOperateCampaignsArgs()
		err740 := containerStruct1.ReadField2(jsProt739)
		if err740 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_ad.ObjectOperateType(tmp2)
		value2 := argvalue2
		fmt.Print(client.OperateCampaigns(value0, value1, value2))
		fmt.Print("\n")
		break
	case "operateCampaignInfos":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "OperateCampaignInfos requires 3 args")
			flag.Usage()
		}
		arg741 := flag.Arg(1)
		mbTrans742 := thrift.NewTMemoryBufferLen(len(arg741))
		defer mbTrans742.Close()
		_, err743 := mbTrans742.WriteString(arg741)
		if err743 != nil {
			Usage()
			return
		}
		factory744 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt745 := factory744.GetProtocol(mbTrans742)
		argvalue0 := zeus_ad.NewRequestHeader()
		err746 := argvalue0.Read(jsProt745)
		if err746 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg747 := flag.Arg(2)
		mbTrans748 := thrift.NewTMemoryBufferLen(len(arg747))
		defer mbTrans748.Close()
		_, err749 := mbTrans748.WriteString(arg747)
		if err749 != nil {
			Usage()
			return
		}
		factory750 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt751 := factory750.GetProtocol(mbTrans748)
		containerStruct1 := zeus_ad.NewOperateCampaignInfosArgs()
		err752 := containerStruct1.ReadField2(jsProt751)
		if err752 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_ad.ObjectOperateType(tmp2)
		value2 := argvalue2
		fmt.Print(client.OperateCampaignInfos(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getZeusCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetZeusCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg753 := flag.Arg(1)
		mbTrans754 := thrift.NewTMemoryBufferLen(len(arg753))
		defer mbTrans754.Close()
		_, err755 := mbTrans754.WriteString(arg753)
		if err755 != nil {
			Usage()
			return
		}
		factory756 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt757 := factory756.GetProtocol(mbTrans754)
		argvalue0 := zeus_ad.NewRequestHeader()
		err758 := argvalue0.Read(jsProt757)
		if err758 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg759 := flag.Arg(2)
		mbTrans760 := thrift.NewTMemoryBufferLen(len(arg759))
		defer mbTrans760.Close()
		_, err761 := mbTrans760.WriteString(arg759)
		if err761 != nil {
			Usage()
			return
		}
		factory762 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt763 := factory762.GetProtocol(mbTrans760)
		containerStruct1 := zeus_ad.NewGetZeusCampaignsByIdsArgs()
		err764 := containerStruct1.ReadField2(jsProt763)
		if err764 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetZeusCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getIdMappingCampaignByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetIdMappingCampaignByIds requires 2 args")
			flag.Usage()
		}
		arg765 := flag.Arg(1)
		mbTrans766 := thrift.NewTMemoryBufferLen(len(arg765))
		defer mbTrans766.Close()
		_, err767 := mbTrans766.WriteString(arg765)
		if err767 != nil {
			Usage()
			return
		}
		factory768 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt769 := factory768.GetProtocol(mbTrans766)
		argvalue0 := zeus_ad.NewRequestHeader()
		err770 := argvalue0.Read(jsProt769)
		if err770 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg771 := flag.Arg(2)
		mbTrans772 := thrift.NewTMemoryBufferLen(len(arg771))
		defer mbTrans772.Close()
		_, err773 := mbTrans772.WriteString(arg771)
		if err773 != nil {
			Usage()
			return
		}
		factory774 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt775 := factory774.GetProtocol(mbTrans772)
		containerStruct1 := zeus_ad.NewGetIdMappingCampaignByIdsArgs()
		err776 := containerStruct1.ReadField2(jsProt775)
		if err776 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetIdMappingCampaignByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getNoDeletedCampaignRules":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetNoDeletedCampaignRules requires 3 args")
			flag.Usage()
		}
		arg777 := flag.Arg(1)
		mbTrans778 := thrift.NewTMemoryBufferLen(len(arg777))
		defer mbTrans778.Close()
		_, err779 := mbTrans778.WriteString(arg777)
		if err779 != nil {
			Usage()
			return
		}
		factory780 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt781 := factory780.GetProtocol(mbTrans778)
		argvalue0 := zeus_ad.NewRequestHeader()
		err782 := argvalue0.Read(jsProt781)
		if err782 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err783 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err783 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err784 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err784 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetNoDeletedCampaignRules(value0, value1, value2))
		fmt.Print("\n")
		break
	case "fetchCampaignAdMappingInfos":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "FetchCampaignAdMappingInfos requires 3 args")
			flag.Usage()
		}
		arg785 := flag.Arg(1)
		mbTrans786 := thrift.NewTMemoryBufferLen(len(arg785))
		defer mbTrans786.Close()
		_, err787 := mbTrans786.WriteString(arg785)
		if err787 != nil {
			Usage()
			return
		}
		factory788 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt789 := factory788.GetProtocol(mbTrans786)
		argvalue0 := zeus_ad.NewRequestHeader()
		err790 := argvalue0.Read(jsProt789)
		if err790 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err791 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err791 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err792 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err792 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.FetchCampaignAdMappingInfos(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAccountWithCampaigns":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAccountWithCampaigns requires 3 args")
			flag.Usage()
		}
		arg793 := flag.Arg(1)
		mbTrans794 := thrift.NewTMemoryBufferLen(len(arg793))
		defer mbTrans794.Close()
		_, err795 := mbTrans794.WriteString(arg793)
		if err795 != nil {
			Usage()
			return
		}
		factory796 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt797 := factory796.GetProtocol(mbTrans794)
		argvalue0 := zeus_ad.NewRequestHeader()
		err798 := argvalue0.Read(jsProt797)
		if err798 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err799 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err799 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err800 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err800 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetAccountWithCampaigns(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAccountWithCampaignsForEndtimeLtNow":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAccountWithCampaignsForEndtimeLtNow requires 2 args")
			flag.Usage()
		}
		arg801 := flag.Arg(1)
		mbTrans802 := thrift.NewTMemoryBufferLen(len(arg801))
		defer mbTrans802.Close()
		_, err803 := mbTrans802.WriteString(arg801)
		if err803 != nil {
			Usage()
			return
		}
		factory804 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt805 := factory804.GetProtocol(mbTrans802)
		argvalue0 := zeus_ad.NewRequestHeader()
		err806 := argvalue0.Read(jsProt805)
		if err806 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err807 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err807 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAccountWithCampaignsForEndtimeLtNow(value0, value1))
		fmt.Print("\n")
		break
	case "getCampaignsByFbAccountId":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "GetCampaignsByFbAccountId requires 8 args")
			flag.Usage()
		}
		arg808 := flag.Arg(1)
		mbTrans809 := thrift.NewTMemoryBufferLen(len(arg808))
		defer mbTrans809.Close()
		_, err810 := mbTrans809.WriteString(arg808)
		if err810 != nil {
			Usage()
			return
		}
		factory811 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt812 := factory811.GetProtocol(mbTrans809)
		argvalue0 := zeus_ad.NewRequestHeader()
		err813 := argvalue0.Read(jsProt812)
		if err813 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err814 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err814 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err815 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err815 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err816 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err816 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err817 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err817 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err818 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err818 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		arg819 := flag.Arg(7)
		mbTrans820 := thrift.NewTMemoryBufferLen(len(arg819))
		defer mbTrans820.Close()
		_, err821 := mbTrans820.WriteString(arg819)
		if err821 != nil {
			Usage()
			return
		}
		factory822 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt823 := factory822.GetProtocol(mbTrans820)
		containerStruct6 := zeus_ad.NewGetCampaignsByFbAccountIdArgs()
		err824 := containerStruct6.ReadField7(jsProt823)
		if err824 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.OrderColumns
		value6 := argvalue6
		argvalue7 := flag.Arg(8)
		value7 := argvalue7
		fmt.Print(client.GetCampaignsByFbAccountId(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getCampaignCountByFbAccountId":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetCampaignCountByFbAccountId requires 5 args")
			flag.Usage()
		}
		arg826 := flag.Arg(1)
		mbTrans827 := thrift.NewTMemoryBufferLen(len(arg826))
		defer mbTrans827.Close()
		_, err828 := mbTrans827.WriteString(arg826)
		if err828 != nil {
			Usage()
			return
		}
		factory829 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt830 := factory829.GetProtocol(mbTrans827)
		argvalue0 := zeus_ad.NewRequestHeader()
		err831 := argvalue0.Read(jsProt830)
		if err831 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err832 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err832 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err833 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err833 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err834 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err834 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.GetCampaignCountByFbAccountId(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAccountWithCampaignsInSystemStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAccountWithCampaignsInSystemStatus requires 3 args")
			flag.Usage()
		}
		arg836 := flag.Arg(1)
		mbTrans837 := thrift.NewTMemoryBufferLen(len(arg836))
		defer mbTrans837.Close()
		_, err838 := mbTrans837.WriteString(arg836)
		if err838 != nil {
			Usage()
			return
		}
		factory839 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt840 := factory839.GetProtocol(mbTrans837)
		argvalue0 := zeus_ad.NewRequestHeader()
		err841 := argvalue0.Read(jsProt840)
		if err841 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err842 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err842 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err843 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err843 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetAccountWithCampaignsInSystemStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "preDelCampaigns":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PreDelCampaigns requires 2 args")
			flag.Usage()
		}
		arg844 := flag.Arg(1)
		mbTrans845 := thrift.NewTMemoryBufferLen(len(arg844))
		defer mbTrans845.Close()
		_, err846 := mbTrans845.WriteString(arg844)
		if err846 != nil {
			Usage()
			return
		}
		factory847 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt848 := factory847.GetProtocol(mbTrans845)
		argvalue0 := zeus_ad.NewRequestHeader()
		err849 := argvalue0.Read(jsProt848)
		if err849 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg850 := flag.Arg(2)
		mbTrans851 := thrift.NewTMemoryBufferLen(len(arg850))
		defer mbTrans851.Close()
		_, err852 := mbTrans851.WriteString(arg850)
		if err852 != nil {
			Usage()
			return
		}
		factory853 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt854 := factory853.GetProtocol(mbTrans851)
		containerStruct1 := zeus_ad.NewPreDelCampaignsArgs()
		err855 := containerStruct1.ReadField2(jsProt854)
		if err855 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PreDelCampaigns(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCampaign requires 2 args")
			flag.Usage()
		}
		arg856 := flag.Arg(1)
		mbTrans857 := thrift.NewTMemoryBufferLen(len(arg856))
		defer mbTrans857.Close()
		_, err858 := mbTrans857.WriteString(arg856)
		if err858 != nil {
			Usage()
			return
		}
		factory859 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt860 := factory859.GetProtocol(mbTrans857)
		argvalue0 := zeus_ad.NewRequestHeader()
		err861 := argvalue0.Read(jsProt860)
		if err861 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err862 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err862 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DeleteCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "updateFbAuditStatus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateFbAuditStatus requires 2 args")
			flag.Usage()
		}
		arg863 := flag.Arg(1)
		mbTrans864 := thrift.NewTMemoryBufferLen(len(arg863))
		defer mbTrans864.Close()
		_, err865 := mbTrans864.WriteString(arg863)
		if err865 != nil {
			Usage()
			return
		}
		factory866 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt867 := factory866.GetProtocol(mbTrans864)
		argvalue0 := zeus_ad.NewRequestHeader()
		err868 := argvalue0.Read(jsProt867)
		if err868 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg869 := flag.Arg(2)
		mbTrans870 := thrift.NewTMemoryBufferLen(len(arg869))
		defer mbTrans870.Close()
		_, err871 := mbTrans870.WriteString(arg869)
		if err871 != nil {
			Usage()
			return
		}
		factory872 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt873 := factory872.GetProtocol(mbTrans870)
		containerStruct1 := zeus_ad.NewUpdateFbAuditStatusArgs()
		err874 := containerStruct1.ReadField2(jsProt873)
		if err874 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AuditInfos
		value1 := argvalue1
		fmt.Print(client.UpdateFbAuditStatus(value0, value1))
		fmt.Print("\n")
		break
	case "getIdWithFbAuditStatusByCampaignIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetIdWithFbAuditStatusByCampaignIds requires 2 args")
			flag.Usage()
		}
		arg875 := flag.Arg(1)
		mbTrans876 := thrift.NewTMemoryBufferLen(len(arg875))
		defer mbTrans876.Close()
		_, err877 := mbTrans876.WriteString(arg875)
		if err877 != nil {
			Usage()
			return
		}
		factory878 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt879 := factory878.GetProtocol(mbTrans876)
		argvalue0 := zeus_ad.NewRequestHeader()
		err880 := argvalue0.Read(jsProt879)
		if err880 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg881 := flag.Arg(2)
		mbTrans882 := thrift.NewTMemoryBufferLen(len(arg881))
		defer mbTrans882.Close()
		_, err883 := mbTrans882.WriteString(arg881)
		if err883 != nil {
			Usage()
			return
		}
		factory884 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt885 := factory884.GetProtocol(mbTrans882)
		containerStruct1 := zeus_ad.NewGetIdWithFbAuditStatusByCampaignIdsArgs()
		err886 := containerStruct1.ReadField2(jsProt885)
		if err886 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIds
		value1 := argvalue1
		fmt.Print(client.GetIdWithFbAuditStatusByCampaignIds(value0, value1))
		fmt.Print("\n")
		break
	case "getIdWithFbAuditStatusInfoByCampaignIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetIdWithFbAuditStatusInfoByCampaignIds requires 2 args")
			flag.Usage()
		}
		arg887 := flag.Arg(1)
		mbTrans888 := thrift.NewTMemoryBufferLen(len(arg887))
		defer mbTrans888.Close()
		_, err889 := mbTrans888.WriteString(arg887)
		if err889 != nil {
			Usage()
			return
		}
		factory890 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt891 := factory890.GetProtocol(mbTrans888)
		argvalue0 := zeus_ad.NewRequestHeader()
		err892 := argvalue0.Read(jsProt891)
		if err892 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg893 := flag.Arg(2)
		mbTrans894 := thrift.NewTMemoryBufferLen(len(arg893))
		defer mbTrans894.Close()
		_, err895 := mbTrans894.WriteString(arg893)
		if err895 != nil {
			Usage()
			return
		}
		factory896 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt897 := factory896.GetProtocol(mbTrans894)
		containerStruct1 := zeus_ad.NewGetIdWithFbAuditStatusInfoByCampaignIdsArgs()
		err898 := containerStruct1.ReadField2(jsProt897)
		if err898 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIds
		value1 := argvalue1
		fmt.Print(client.GetIdWithFbAuditStatusInfoByCampaignIds(value0, value1))
		fmt.Print("\n")
		break
	case "getZeusCampaignsByAuditStatus":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetZeusCampaignsByAuditStatus requires 4 args")
			flag.Usage()
		}
		arg899 := flag.Arg(1)
		mbTrans900 := thrift.NewTMemoryBufferLen(len(arg899))
		defer mbTrans900.Close()
		_, err901 := mbTrans900.WriteString(arg899)
		if err901 != nil {
			Usage()
			return
		}
		factory902 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt903 := factory902.GetProtocol(mbTrans900)
		argvalue0 := zeus_ad.NewRequestHeader()
		err904 := argvalue0.Read(jsProt903)
		if err904 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := zeus_ad.ObjectAuditStatus(tmp1)
		value1 := argvalue1
		argvalue2, err905 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err905 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err906 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err906 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetZeusCampaignsByAuditStatus(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotion requires 2 args")
			flag.Usage()
		}
		arg907 := flag.Arg(1)
		mbTrans908 := thrift.NewTMemoryBufferLen(len(arg907))
		defer mbTrans908.Close()
		_, err909 := mbTrans908.WriteString(arg907)
		if err909 != nil {
			Usage()
			return
		}
		factory910 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt911 := factory910.GetProtocol(mbTrans908)
		argvalue0 := zeus_ad.NewRequestHeader()
		err912 := argvalue0.Read(jsProt911)
		if err912 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg913 := flag.Arg(2)
		mbTrans914 := thrift.NewTMemoryBufferLen(len(arg913))
		defer mbTrans914.Close()
		_, err915 := mbTrans914.WriteString(arg913)
		if err915 != nil {
			Usage()
			return
		}
		factory916 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt917 := factory916.GetProtocol(mbTrans914)
		argvalue1 := zeus_ad.NewZeusPromotion()
		err918 := argvalue1.Read(jsProt917)
		if err918 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg919 := flag.Arg(1)
		mbTrans920 := thrift.NewTMemoryBufferLen(len(arg919))
		defer mbTrans920.Close()
		_, err921 := mbTrans920.WriteString(arg919)
		if err921 != nil {
			Usage()
			return
		}
		factory922 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt923 := factory922.GetProtocol(mbTrans920)
		argvalue0 := zeus_ad.NewRequestHeader()
		err924 := argvalue0.Read(jsProt923)
		if err924 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg925 := flag.Arg(2)
		mbTrans926 := thrift.NewTMemoryBufferLen(len(arg925))
		defer mbTrans926.Close()
		_, err927 := mbTrans926.WriteString(arg925)
		if err927 != nil {
			Usage()
			return
		}
		factory928 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt929 := factory928.GetProtocol(mbTrans926)
		containerStruct1 := zeus_ad.NewGetPromotionsByIdsArgs()
		err930 := containerStruct1.ReadField2(jsProt929)
		if err930 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionsByFbAccountId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetPromotionsByFbAccountId requires 3 args")
			flag.Usage()
		}
		arg931 := flag.Arg(1)
		mbTrans932 := thrift.NewTMemoryBufferLen(len(arg931))
		defer mbTrans932.Close()
		_, err933 := mbTrans932.WriteString(arg931)
		if err933 != nil {
			Usage()
			return
		}
		factory934 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt935 := factory934.GetProtocol(mbTrans932)
		argvalue0 := zeus_ad.NewRequestHeader()
		err936 := argvalue0.Read(jsProt935)
		if err936 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err937 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err937 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err938 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err938 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetPromotionsByFbAccountId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deletePromotions":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeletePromotions requires 2 args")
			flag.Usage()
		}
		arg939 := flag.Arg(1)
		mbTrans940 := thrift.NewTMemoryBufferLen(len(arg939))
		defer mbTrans940.Close()
		_, err941 := mbTrans940.WriteString(arg939)
		if err941 != nil {
			Usage()
			return
		}
		factory942 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt943 := factory942.GetProtocol(mbTrans940)
		argvalue0 := zeus_ad.NewRequestHeader()
		err944 := argvalue0.Read(jsProt943)
		if err944 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg945 := flag.Arg(2)
		mbTrans946 := thrift.NewTMemoryBufferLen(len(arg945))
		defer mbTrans946.Close()
		_, err947 := mbTrans946.WriteString(arg945)
		if err947 != nil {
			Usage()
			return
		}
		factory948 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt949 := factory948.GetProtocol(mbTrans946)
		containerStruct1 := zeus_ad.NewDeletePromotionsArgs()
		err950 := containerStruct1.ReadField2(jsProt949)
		if err950 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeletePromotions(value0, value1))
		fmt.Print("\n")
		break
	case "addAdsets":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdsets requires 2 args")
			flag.Usage()
		}
		arg951 := flag.Arg(1)
		mbTrans952 := thrift.NewTMemoryBufferLen(len(arg951))
		defer mbTrans952.Close()
		_, err953 := mbTrans952.WriteString(arg951)
		if err953 != nil {
			Usage()
			return
		}
		factory954 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt955 := factory954.GetProtocol(mbTrans952)
		argvalue0 := zeus_ad.NewRequestHeader()
		err956 := argvalue0.Read(jsProt955)
		if err956 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg957 := flag.Arg(2)
		mbTrans958 := thrift.NewTMemoryBufferLen(len(arg957))
		defer mbTrans958.Close()
		_, err959 := mbTrans958.WriteString(arg957)
		if err959 != nil {
			Usage()
			return
		}
		factory960 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt961 := factory960.GetProtocol(mbTrans958)
		containerStruct1 := zeus_ad.NewAddAdsetsArgs()
		err962 := containerStruct1.ReadField2(jsProt961)
		if err962 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdSets
		value1 := argvalue1
		fmt.Print(client.AddAdsets(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdsets":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdsets requires 2 args")
			flag.Usage()
		}
		arg963 := flag.Arg(1)
		mbTrans964 := thrift.NewTMemoryBufferLen(len(arg963))
		defer mbTrans964.Close()
		_, err965 := mbTrans964.WriteString(arg963)
		if err965 != nil {
			Usage()
			return
		}
		factory966 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt967 := factory966.GetProtocol(mbTrans964)
		argvalue0 := zeus_ad.NewRequestHeader()
		err968 := argvalue0.Read(jsProt967)
		if err968 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg969 := flag.Arg(2)
		mbTrans970 := thrift.NewTMemoryBufferLen(len(arg969))
		defer mbTrans970.Close()
		_, err971 := mbTrans970.WriteString(arg969)
		if err971 != nil {
			Usage()
			return
		}
		factory972 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt973 := factory972.GetProtocol(mbTrans970)
		containerStruct1 := zeus_ad.NewUpdateAdsetsArgs()
		err974 := containerStruct1.ReadField2(jsProt973)
		if err974 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdSets
		value1 := argvalue1
		fmt.Print(client.UpdateAdsets(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdsetInfos":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdsetInfos requires 2 args")
			flag.Usage()
		}
		arg975 := flag.Arg(1)
		mbTrans976 := thrift.NewTMemoryBufferLen(len(arg975))
		defer mbTrans976.Close()
		_, err977 := mbTrans976.WriteString(arg975)
		if err977 != nil {
			Usage()
			return
		}
		factory978 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt979 := factory978.GetProtocol(mbTrans976)
		argvalue0 := zeus_ad.NewRequestHeader()
		err980 := argvalue0.Read(jsProt979)
		if err980 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg981 := flag.Arg(2)
		mbTrans982 := thrift.NewTMemoryBufferLen(len(arg981))
		defer mbTrans982.Close()
		_, err983 := mbTrans982.WriteString(arg981)
		if err983 != nil {
			Usage()
			return
		}
		factory984 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt985 := factory984.GetProtocol(mbTrans982)
		containerStruct1 := zeus_ad.NewUpdateAdsetInfosArgs()
		err986 := containerStruct1.ReadField2(jsProt985)
		if err986 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdSets
		value1 := argvalue1
		fmt.Print(client.UpdateAdsetInfos(value0, value1))
		fmt.Print("\n")
		break
	case "operateAdsetInfos":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "OperateAdsetInfos requires 3 args")
			flag.Usage()
		}
		arg987 := flag.Arg(1)
		mbTrans988 := thrift.NewTMemoryBufferLen(len(arg987))
		defer mbTrans988.Close()
		_, err989 := mbTrans988.WriteString(arg987)
		if err989 != nil {
			Usage()
			return
		}
		factory990 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt991 := factory990.GetProtocol(mbTrans988)
		argvalue0 := zeus_ad.NewRequestHeader()
		err992 := argvalue0.Read(jsProt991)
		if err992 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg993 := flag.Arg(2)
		mbTrans994 := thrift.NewTMemoryBufferLen(len(arg993))
		defer mbTrans994.Close()
		_, err995 := mbTrans994.WriteString(arg993)
		if err995 != nil {
			Usage()
			return
		}
		factory996 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt997 := factory996.GetProtocol(mbTrans994)
		containerStruct1 := zeus_ad.NewOperateAdsetInfosArgs()
		err998 := containerStruct1.ReadField2(jsProt997)
		if err998 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdsetIds
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_ad.ObjectOperateType(tmp2)
		value2 := argvalue2
		fmt.Print(client.OperateAdsetInfos(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdsetIdWithFbUserIdByStatus":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetAdsetIdWithFbUserIdByStatus requires 4 args")
			flag.Usage()
		}
		arg999 := flag.Arg(1)
		mbTrans1000 := thrift.NewTMemoryBufferLen(len(arg999))
		defer mbTrans1000.Close()
		_, err1001 := mbTrans1000.WriteString(arg999)
		if err1001 != nil {
			Usage()
			return
		}
		factory1002 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1003 := factory1002.GetProtocol(mbTrans1000)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1004 := argvalue0.Read(jsProt1003)
		if err1004 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := zeus_ad.ObjectRunningStatus(tmp1)
		value1 := argvalue1
		argvalue2, err1005 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1005 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1006 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1006 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetAdsetIdWithFbUserIdByStatus(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAdsets":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdsets requires 2 args")
			flag.Usage()
		}
		arg1007 := flag.Arg(1)
		mbTrans1008 := thrift.NewTMemoryBufferLen(len(arg1007))
		defer mbTrans1008.Close()
		_, err1009 := mbTrans1008.WriteString(arg1007)
		if err1009 != nil {
			Usage()
			return
		}
		factory1010 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1011 := factory1010.GetProtocol(mbTrans1008)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1012 := argvalue0.Read(jsProt1011)
		if err1012 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1013 := flag.Arg(2)
		mbTrans1014 := thrift.NewTMemoryBufferLen(len(arg1013))
		defer mbTrans1014.Close()
		_, err1015 := mbTrans1014.WriteString(arg1013)
		if err1015 != nil {
			Usage()
			return
		}
		factory1016 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1017 := factory1016.GetProtocol(mbTrans1014)
		containerStruct1 := zeus_ad.NewDeleteAdsetsArgs()
		err1018 := containerStruct1.ReadField2(jsProt1017)
		if err1018 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAdsets(value0, value1))
		fmt.Print("\n")
		break
	case "getAdsetsByCampaignId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdsetsByCampaignId requires 2 args")
			flag.Usage()
		}
		arg1019 := flag.Arg(1)
		mbTrans1020 := thrift.NewTMemoryBufferLen(len(arg1019))
		defer mbTrans1020.Close()
		_, err1021 := mbTrans1020.WriteString(arg1019)
		if err1021 != nil {
			Usage()
			return
		}
		factory1022 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1023 := factory1022.GetProtocol(mbTrans1020)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1024 := argvalue0.Read(jsProt1023)
		if err1024 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1025 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1025 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAdsetsByCampaignId(value0, value1))
		fmt.Print("\n")
		break
	case "getAdsetsByGroupIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdsetsByGroupIds requires 2 args")
			flag.Usage()
		}
		arg1026 := flag.Arg(1)
		mbTrans1027 := thrift.NewTMemoryBufferLen(len(arg1026))
		defer mbTrans1027.Close()
		_, err1028 := mbTrans1027.WriteString(arg1026)
		if err1028 != nil {
			Usage()
			return
		}
		factory1029 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1030 := factory1029.GetProtocol(mbTrans1027)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1031 := argvalue0.Read(jsProt1030)
		if err1031 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1032 := flag.Arg(2)
		mbTrans1033 := thrift.NewTMemoryBufferLen(len(arg1032))
		defer mbTrans1033.Close()
		_, err1034 := mbTrans1033.WriteString(arg1032)
		if err1034 != nil {
			Usage()
			return
		}
		factory1035 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1036 := factory1035.GetProtocol(mbTrans1033)
		containerStruct1 := zeus_ad.NewGetAdsetsByGroupIdsArgs()
		err1037 := containerStruct1.ReadField2(jsProt1036)
		if err1037 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.GroupIds
		value1 := argvalue1
		fmt.Print(client.GetAdsetsByGroupIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAdsetsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdsetsByIds requires 2 args")
			flag.Usage()
		}
		arg1038 := flag.Arg(1)
		mbTrans1039 := thrift.NewTMemoryBufferLen(len(arg1038))
		defer mbTrans1039.Close()
		_, err1040 := mbTrans1039.WriteString(arg1038)
		if err1040 != nil {
			Usage()
			return
		}
		factory1041 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1042 := factory1041.GetProtocol(mbTrans1039)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1043 := argvalue0.Read(jsProt1042)
		if err1043 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1044 := flag.Arg(2)
		mbTrans1045 := thrift.NewTMemoryBufferLen(len(arg1044))
		defer mbTrans1045.Close()
		_, err1046 := mbTrans1045.WriteString(arg1044)
		if err1046 != nil {
			Usage()
			return
		}
		factory1047 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1048 := factory1047.GetProtocol(mbTrans1045)
		containerStruct1 := zeus_ad.NewGetAdsetsByIdsArgs()
		err1049 := containerStruct1.ReadField2(jsProt1048)
		if err1049 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ZeusAdsetIds
		value1 := argvalue1
		fmt.Print(client.GetAdsetsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getLocationMapByUserInfo":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetLocationMapByUserInfo requires 4 args")
			flag.Usage()
		}
		arg1050 := flag.Arg(1)
		mbTrans1051 := thrift.NewTMemoryBufferLen(len(arg1050))
		defer mbTrans1051.Close()
		_, err1052 := mbTrans1051.WriteString(arg1050)
		if err1052 != nil {
			Usage()
			return
		}
		factory1053 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1054 := factory1053.GetProtocol(mbTrans1051)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1055 := argvalue0.Read(jsProt1054)
		if err1055 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1056 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1056 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1057 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1057 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1058 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1058 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetLocationMapByUserInfo(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getLocationMapByCampaignId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLocationMapByCampaignId requires 2 args")
			flag.Usage()
		}
		arg1059 := flag.Arg(1)
		mbTrans1060 := thrift.NewTMemoryBufferLen(len(arg1059))
		defer mbTrans1060.Close()
		_, err1061 := mbTrans1060.WriteString(arg1059)
		if err1061 != nil {
			Usage()
			return
		}
		factory1062 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1063 := factory1062.GetProtocol(mbTrans1060)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1064 := argvalue0.Read(jsProt1063)
		if err1064 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1065 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1065 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetLocationMapByCampaignId(value0, value1))
		fmt.Print("\n")
		break
	case "getLocationMapByFbPromotionId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetLocationMapByFbPromotionId requires 3 args")
			flag.Usage()
		}
		arg1066 := flag.Arg(1)
		mbTrans1067 := thrift.NewTMemoryBufferLen(len(arg1066))
		defer mbTrans1067.Close()
		_, err1068 := mbTrans1067.WriteString(arg1066)
		if err1068 != nil {
			Usage()
			return
		}
		factory1069 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1070 := factory1069.GetProtocol(mbTrans1067)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1071 := argvalue0.Read(jsProt1070)
		if err1071 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1072 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1072 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1073 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1073 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetLocationMapByFbPromotionId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addCreatives":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreatives requires 2 args")
			flag.Usage()
		}
		arg1074 := flag.Arg(1)
		mbTrans1075 := thrift.NewTMemoryBufferLen(len(arg1074))
		defer mbTrans1075.Close()
		_, err1076 := mbTrans1075.WriteString(arg1074)
		if err1076 != nil {
			Usage()
			return
		}
		factory1077 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1078 := factory1077.GetProtocol(mbTrans1075)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1079 := argvalue0.Read(jsProt1078)
		if err1079 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1080 := flag.Arg(2)
		mbTrans1081 := thrift.NewTMemoryBufferLen(len(arg1080))
		defer mbTrans1081.Close()
		_, err1082 := mbTrans1081.WriteString(arg1080)
		if err1082 != nil {
			Usage()
			return
		}
		factory1083 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1084 := factory1083.GetProtocol(mbTrans1081)
		containerStruct1 := zeus_ad.NewAddCreativesArgs()
		err1085 := containerStruct1.ReadField2(jsProt1084)
		if err1085 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Creatives
		value1 := argvalue1
		fmt.Print(client.AddCreatives(value0, value1))
		fmt.Print("\n")
		break
	case "getCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg1086 := flag.Arg(1)
		mbTrans1087 := thrift.NewTMemoryBufferLen(len(arg1086))
		defer mbTrans1087.Close()
		_, err1088 := mbTrans1087.WriteString(arg1086)
		if err1088 != nil {
			Usage()
			return
		}
		factory1089 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1090 := factory1089.GetProtocol(mbTrans1087)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1091 := argvalue0.Read(jsProt1090)
		if err1091 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1092 := flag.Arg(2)
		mbTrans1093 := thrift.NewTMemoryBufferLen(len(arg1092))
		defer mbTrans1093.Close()
		_, err1094 := mbTrans1093.WriteString(arg1092)
		if err1094 != nil {
			Usage()
			return
		}
		factory1095 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1096 := factory1095.GetProtocol(mbTrans1093)
		containerStruct1 := zeus_ad.NewGetCreativesByIdsArgs()
		err1097 := containerStruct1.ReadField2(jsProt1096)
		if err1097 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getImagesAndTextsByCreativeIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetImagesAndTextsByCreativeIds requires 2 args")
			flag.Usage()
		}
		arg1098 := flag.Arg(1)
		mbTrans1099 := thrift.NewTMemoryBufferLen(len(arg1098))
		defer mbTrans1099.Close()
		_, err1100 := mbTrans1099.WriteString(arg1098)
		if err1100 != nil {
			Usage()
			return
		}
		factory1101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1102 := factory1101.GetProtocol(mbTrans1099)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1103 := argvalue0.Read(jsProt1102)
		if err1103 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1104 := flag.Arg(2)
		mbTrans1105 := thrift.NewTMemoryBufferLen(len(arg1104))
		defer mbTrans1105.Close()
		_, err1106 := mbTrans1105.WriteString(arg1104)
		if err1106 != nil {
			Usage()
			return
		}
		factory1107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1108 := factory1107.GetProtocol(mbTrans1105)
		containerStruct1 := zeus_ad.NewGetImagesAndTextsByCreativeIdsArgs()
		err1109 := containerStruct1.ReadField2(jsProt1108)
		if err1109 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetImagesAndTextsByCreativeIds(value0, value1))
		fmt.Print("\n")
		break
	case "updateCreatives":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateCreatives requires 2 args")
			flag.Usage()
		}
		arg1110 := flag.Arg(1)
		mbTrans1111 := thrift.NewTMemoryBufferLen(len(arg1110))
		defer mbTrans1111.Close()
		_, err1112 := mbTrans1111.WriteString(arg1110)
		if err1112 != nil {
			Usage()
			return
		}
		factory1113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1114 := factory1113.GetProtocol(mbTrans1111)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1115 := argvalue0.Read(jsProt1114)
		if err1115 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1116 := flag.Arg(2)
		mbTrans1117 := thrift.NewTMemoryBufferLen(len(arg1116))
		defer mbTrans1117.Close()
		_, err1118 := mbTrans1117.WriteString(arg1116)
		if err1118 != nil {
			Usage()
			return
		}
		factory1119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1120 := factory1119.GetProtocol(mbTrans1117)
		containerStruct1 := zeus_ad.NewUpdateCreativesArgs()
		err1121 := containerStruct1.ReadField2(jsProt1120)
		if err1121 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Creatives
		value1 := argvalue1
		fmt.Print(client.UpdateCreatives(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCreatives":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCreatives requires 2 args")
			flag.Usage()
		}
		arg1122 := flag.Arg(1)
		mbTrans1123 := thrift.NewTMemoryBufferLen(len(arg1122))
		defer mbTrans1123.Close()
		_, err1124 := mbTrans1123.WriteString(arg1122)
		if err1124 != nil {
			Usage()
			return
		}
		factory1125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1126 := factory1125.GetProtocol(mbTrans1123)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1127 := argvalue0.Read(jsProt1126)
		if err1127 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1128 := flag.Arg(2)
		mbTrans1129 := thrift.NewTMemoryBufferLen(len(arg1128))
		defer mbTrans1129.Close()
		_, err1130 := mbTrans1129.WriteString(arg1128)
		if err1130 != nil {
			Usage()
			return
		}
		factory1131 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1132 := factory1131.GetProtocol(mbTrans1129)
		containerStruct1 := zeus_ad.NewDeleteCreativesArgs()
		err1133 := containerStruct1.ReadField2(jsProt1132)
		if err1133 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteCreatives(value0, value1))
		fmt.Print("\n")
		break
	case "addAds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAds requires 2 args")
			flag.Usage()
		}
		arg1134 := flag.Arg(1)
		mbTrans1135 := thrift.NewTMemoryBufferLen(len(arg1134))
		defer mbTrans1135.Close()
		_, err1136 := mbTrans1135.WriteString(arg1134)
		if err1136 != nil {
			Usage()
			return
		}
		factory1137 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1138 := factory1137.GetProtocol(mbTrans1135)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1139 := argvalue0.Read(jsProt1138)
		if err1139 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1140 := flag.Arg(2)
		mbTrans1141 := thrift.NewTMemoryBufferLen(len(arg1140))
		defer mbTrans1141.Close()
		_, err1142 := mbTrans1141.WriteString(arg1140)
		if err1142 != nil {
			Usage()
			return
		}
		factory1143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1144 := factory1143.GetProtocol(mbTrans1141)
		containerStruct1 := zeus_ad.NewAddAdsArgs()
		err1145 := containerStruct1.ReadField2(jsProt1144)
		if err1145 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ads
		value1 := argvalue1
		fmt.Print(client.AddAds(value0, value1))
		fmt.Print("\n")
		break
	case "getAdsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdsByIds requires 2 args")
			flag.Usage()
		}
		arg1146 := flag.Arg(1)
		mbTrans1147 := thrift.NewTMemoryBufferLen(len(arg1146))
		defer mbTrans1147.Close()
		_, err1148 := mbTrans1147.WriteString(arg1146)
		if err1148 != nil {
			Usage()
			return
		}
		factory1149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1150 := factory1149.GetProtocol(mbTrans1147)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1151 := argvalue0.Read(jsProt1150)
		if err1151 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1152 := flag.Arg(2)
		mbTrans1153 := thrift.NewTMemoryBufferLen(len(arg1152))
		defer mbTrans1153.Close()
		_, err1154 := mbTrans1153.WriteString(arg1152)
		if err1154 != nil {
			Usage()
			return
		}
		factory1155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1156 := factory1155.GetProtocol(mbTrans1153)
		containerStruct1 := zeus_ad.NewGetAdsByIdsArgs()
		err1157 := containerStruct1.ReadField2(jsProt1156)
		if err1157 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "updateAdInfos":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAdInfos requires 2 args")
			flag.Usage()
		}
		arg1158 := flag.Arg(1)
		mbTrans1159 := thrift.NewTMemoryBufferLen(len(arg1158))
		defer mbTrans1159.Close()
		_, err1160 := mbTrans1159.WriteString(arg1158)
		if err1160 != nil {
			Usage()
			return
		}
		factory1161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1162 := factory1161.GetProtocol(mbTrans1159)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1163 := argvalue0.Read(jsProt1162)
		if err1163 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1164 := flag.Arg(2)
		mbTrans1165 := thrift.NewTMemoryBufferLen(len(arg1164))
		defer mbTrans1165.Close()
		_, err1166 := mbTrans1165.WriteString(arg1164)
		if err1166 != nil {
			Usage()
			return
		}
		factory1167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1168 := factory1167.GetProtocol(mbTrans1165)
		containerStruct1 := zeus_ad.NewUpdateAdInfosArgs()
		err1169 := containerStruct1.ReadField2(jsProt1168)
		if err1169 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ads
		value1 := argvalue1
		fmt.Print(client.UpdateAdInfos(value0, value1))
		fmt.Print("\n")
		break
	case "updateAds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAds requires 2 args")
			flag.Usage()
		}
		arg1170 := flag.Arg(1)
		mbTrans1171 := thrift.NewTMemoryBufferLen(len(arg1170))
		defer mbTrans1171.Close()
		_, err1172 := mbTrans1171.WriteString(arg1170)
		if err1172 != nil {
			Usage()
			return
		}
		factory1173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1174 := factory1173.GetProtocol(mbTrans1171)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1175 := argvalue0.Read(jsProt1174)
		if err1175 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1176 := flag.Arg(2)
		mbTrans1177 := thrift.NewTMemoryBufferLen(len(arg1176))
		defer mbTrans1177.Close()
		_, err1178 := mbTrans1177.WriteString(arg1176)
		if err1178 != nil {
			Usage()
			return
		}
		factory1179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1180 := factory1179.GetProtocol(mbTrans1177)
		containerStruct1 := zeus_ad.NewUpdateAdsArgs()
		err1181 := containerStruct1.ReadField2(jsProt1180)
		if err1181 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ads
		value1 := argvalue1
		fmt.Print(client.UpdateAds(value0, value1))
		fmt.Print("\n")
		break
	case "operateAdInfos":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "OperateAdInfos requires 3 args")
			flag.Usage()
		}
		arg1182 := flag.Arg(1)
		mbTrans1183 := thrift.NewTMemoryBufferLen(len(arg1182))
		defer mbTrans1183.Close()
		_, err1184 := mbTrans1183.WriteString(arg1182)
		if err1184 != nil {
			Usage()
			return
		}
		factory1185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1186 := factory1185.GetProtocol(mbTrans1183)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1187 := argvalue0.Read(jsProt1186)
		if err1187 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1188 := flag.Arg(2)
		mbTrans1189 := thrift.NewTMemoryBufferLen(len(arg1188))
		defer mbTrans1189.Close()
		_, err1190 := mbTrans1189.WriteString(arg1188)
		if err1190 != nil {
			Usage()
			return
		}
		factory1191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1192 := factory1191.GetProtocol(mbTrans1189)
		containerStruct1 := zeus_ad.NewOperateAdInfosArgs()
		err1193 := containerStruct1.ReadField2(jsProt1192)
		if err1193 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AdIds
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_ad.ObjectOperateType(tmp2)
		value2 := argvalue2
		fmt.Print(client.OperateAdInfos(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAds requires 2 args")
			flag.Usage()
		}
		arg1194 := flag.Arg(1)
		mbTrans1195 := thrift.NewTMemoryBufferLen(len(arg1194))
		defer mbTrans1195.Close()
		_, err1196 := mbTrans1195.WriteString(arg1194)
		if err1196 != nil {
			Usage()
			return
		}
		factory1197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1198 := factory1197.GetProtocol(mbTrans1195)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1199 := argvalue0.Read(jsProt1198)
		if err1199 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1200 := flag.Arg(2)
		mbTrans1201 := thrift.NewTMemoryBufferLen(len(arg1200))
		defer mbTrans1201.Close()
		_, err1202 := mbTrans1201.WriteString(arg1200)
		if err1202 != nil {
			Usage()
			return
		}
		factory1203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1204 := factory1203.GetProtocol(mbTrans1201)
		containerStruct1 := zeus_ad.NewDeleteAdsArgs()
		err1205 := containerStruct1.ReadField2(jsProt1204)
		if err1205 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAds(value0, value1))
		fmt.Print("\n")
		break
	case "getAdByAdsetId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdByAdsetId requires 2 args")
			flag.Usage()
		}
		arg1206 := flag.Arg(1)
		mbTrans1207 := thrift.NewTMemoryBufferLen(len(arg1206))
		defer mbTrans1207.Close()
		_, err1208 := mbTrans1207.WriteString(arg1206)
		if err1208 != nil {
			Usage()
			return
		}
		factory1209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1210 := factory1209.GetProtocol(mbTrans1207)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1211 := argvalue0.Read(jsProt1210)
		if err1211 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1212 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1212 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAdByAdsetId(value0, value1))
		fmt.Print("\n")
		break
	case "addImages":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "AddImages requires 6 args")
			flag.Usage()
		}
		arg1213 := flag.Arg(1)
		mbTrans1214 := thrift.NewTMemoryBufferLen(len(arg1213))
		defer mbTrans1214.Close()
		_, err1215 := mbTrans1214.WriteString(arg1213)
		if err1215 != nil {
			Usage()
			return
		}
		factory1216 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1217 := factory1216.GetProtocol(mbTrans1214)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1218 := argvalue0.Read(jsProt1217)
		if err1218 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1219 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1219 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1220 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1220 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1221 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1221 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err1222 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1222 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg1223 := flag.Arg(6)
		mbTrans1224 := thrift.NewTMemoryBufferLen(len(arg1223))
		defer mbTrans1224.Close()
		_, err1225 := mbTrans1224.WriteString(arg1223)
		if err1225 != nil {
			Usage()
			return
		}
		factory1226 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1227 := factory1226.GetProtocol(mbTrans1224)
		containerStruct5 := zeus_ad.NewAddImagesArgs()
		err1228 := containerStruct5.ReadField6(jsProt1227)
		if err1228 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Images
		value5 := argvalue5
		fmt.Print(client.AddImages(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getImagesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetImagesByIds requires 2 args")
			flag.Usage()
		}
		arg1229 := flag.Arg(1)
		mbTrans1230 := thrift.NewTMemoryBufferLen(len(arg1229))
		defer mbTrans1230.Close()
		_, err1231 := mbTrans1230.WriteString(arg1229)
		if err1231 != nil {
			Usage()
			return
		}
		factory1232 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1233 := factory1232.GetProtocol(mbTrans1230)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1234 := argvalue0.Read(jsProt1233)
		if err1234 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1235 := flag.Arg(2)
		mbTrans1236 := thrift.NewTMemoryBufferLen(len(arg1235))
		defer mbTrans1236.Close()
		_, err1237 := mbTrans1236.WriteString(arg1235)
		if err1237 != nil {
			Usage()
			return
		}
		factory1238 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1239 := factory1238.GetProtocol(mbTrans1236)
		containerStruct1 := zeus_ad.NewGetImagesByIdsArgs()
		err1240 := containerStruct1.ReadField2(jsProt1239)
		if err1240 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetImagesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getImagesByUserInfo":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetImagesByUserInfo requires 5 args")
			flag.Usage()
		}
		arg1241 := flag.Arg(1)
		mbTrans1242 := thrift.NewTMemoryBufferLen(len(arg1241))
		defer mbTrans1242.Close()
		_, err1243 := mbTrans1242.WriteString(arg1241)
		if err1243 != nil {
			Usage()
			return
		}
		factory1244 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1245 := factory1244.GetProtocol(mbTrans1242)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1246 := argvalue0.Read(jsProt1245)
		if err1246 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1247 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1247 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1248 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1248 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1249 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1249 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err1250 := (strconv.Atoi(flag.Arg(5)))
		if err1250 != nil {
			Usage()
			return
		}
		argvalue4 := byte(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetImagesByUserInfo(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getImageCountByFbAccountId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetImageCountByFbAccountId requires 2 args")
			flag.Usage()
		}
		arg1251 := flag.Arg(1)
		mbTrans1252 := thrift.NewTMemoryBufferLen(len(arg1251))
		defer mbTrans1252.Close()
		_, err1253 := mbTrans1252.WriteString(arg1251)
		if err1253 != nil {
			Usage()
			return
		}
		factory1254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1255 := factory1254.GetProtocol(mbTrans1252)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1256 := argvalue0.Read(jsProt1255)
		if err1256 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1257 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1257 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetImageCountByFbAccountId(value0, value1))
		fmt.Print("\n")
		break
	case "getImagesByFbAccountId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetImagesByFbAccountId requires 4 args")
			flag.Usage()
		}
		arg1258 := flag.Arg(1)
		mbTrans1259 := thrift.NewTMemoryBufferLen(len(arg1258))
		defer mbTrans1259.Close()
		_, err1260 := mbTrans1259.WriteString(arg1258)
		if err1260 != nil {
			Usage()
			return
		}
		factory1261 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1262 := factory1261.GetProtocol(mbTrans1259)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1263 := argvalue0.Read(jsProt1262)
		if err1263 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1264 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1264 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1265 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1265 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1266 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1266 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetImagesByFbAccountId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteImages":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteImages requires 5 args")
			flag.Usage()
		}
		arg1267 := flag.Arg(1)
		mbTrans1268 := thrift.NewTMemoryBufferLen(len(arg1267))
		defer mbTrans1268.Close()
		_, err1269 := mbTrans1268.WriteString(arg1267)
		if err1269 != nil {
			Usage()
			return
		}
		factory1270 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1271 := factory1270.GetProtocol(mbTrans1268)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1272 := argvalue0.Read(jsProt1271)
		if err1272 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1273 := flag.Arg(2)
		mbTrans1274 := thrift.NewTMemoryBufferLen(len(arg1273))
		defer mbTrans1274.Close()
		_, err1275 := mbTrans1274.WriteString(arg1273)
		if err1275 != nil {
			Usage()
			return
		}
		factory1276 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1277 := factory1276.GetProtocol(mbTrans1274)
		containerStruct1 := zeus_ad.NewDeleteImagesArgs()
		err1278 := containerStruct1.ReadField2(jsProt1277)
		if err1278 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ImageIds
		value1 := argvalue1
		argvalue2, err1279 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1279 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1280 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1280 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err1281 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1281 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		fmt.Print(client.DeleteImages(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addVideos":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddVideos requires 2 args")
			flag.Usage()
		}
		arg1282 := flag.Arg(1)
		mbTrans1283 := thrift.NewTMemoryBufferLen(len(arg1282))
		defer mbTrans1283.Close()
		_, err1284 := mbTrans1283.WriteString(arg1282)
		if err1284 != nil {
			Usage()
			return
		}
		factory1285 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1286 := factory1285.GetProtocol(mbTrans1283)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1287 := argvalue0.Read(jsProt1286)
		if err1287 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1288 := flag.Arg(2)
		mbTrans1289 := thrift.NewTMemoryBufferLen(len(arg1288))
		defer mbTrans1289.Close()
		_, err1290 := mbTrans1289.WriteString(arg1288)
		if err1290 != nil {
			Usage()
			return
		}
		factory1291 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1292 := factory1291.GetProtocol(mbTrans1289)
		containerStruct1 := zeus_ad.NewAddVideosArgs()
		err1293 := containerStruct1.ReadField2(jsProt1292)
		if err1293 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Videos
		value1 := argvalue1
		fmt.Print(client.AddVideos(value0, value1))
		fmt.Print("\n")
		break
	case "getVideosByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideosByIds requires 2 args")
			flag.Usage()
		}
		arg1294 := flag.Arg(1)
		mbTrans1295 := thrift.NewTMemoryBufferLen(len(arg1294))
		defer mbTrans1295.Close()
		_, err1296 := mbTrans1295.WriteString(arg1294)
		if err1296 != nil {
			Usage()
			return
		}
		factory1297 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1298 := factory1297.GetProtocol(mbTrans1295)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1299 := argvalue0.Read(jsProt1298)
		if err1299 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1300 := flag.Arg(2)
		mbTrans1301 := thrift.NewTMemoryBufferLen(len(arg1300))
		defer mbTrans1301.Close()
		_, err1302 := mbTrans1301.WriteString(arg1300)
		if err1302 != nil {
			Usage()
			return
		}
		factory1303 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1304 := factory1303.GetProtocol(mbTrans1301)
		containerStruct1 := zeus_ad.NewGetVideosByIdsArgs()
		err1305 := containerStruct1.ReadField2(jsProt1304)
		if err1305 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetVideosByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getVideoInfoByFbVideoId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideoInfoByFbVideoId requires 2 args")
			flag.Usage()
		}
		arg1306 := flag.Arg(1)
		mbTrans1307 := thrift.NewTMemoryBufferLen(len(arg1306))
		defer mbTrans1307.Close()
		_, err1308 := mbTrans1307.WriteString(arg1306)
		if err1308 != nil {
			Usage()
			return
		}
		factory1309 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1310 := factory1309.GetProtocol(mbTrans1307)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1311 := argvalue0.Read(jsProt1310)
		if err1311 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1312 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1312 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetVideoInfoByFbVideoId(value0, value1))
		fmt.Print("\n")
		break
	case "deleteVideos":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteVideos requires 2 args")
			flag.Usage()
		}
		arg1313 := flag.Arg(1)
		mbTrans1314 := thrift.NewTMemoryBufferLen(len(arg1313))
		defer mbTrans1314.Close()
		_, err1315 := mbTrans1314.WriteString(arg1313)
		if err1315 != nil {
			Usage()
			return
		}
		factory1316 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1317 := factory1316.GetProtocol(mbTrans1314)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1318 := argvalue0.Read(jsProt1317)
		if err1318 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1319 := flag.Arg(2)
		mbTrans1320 := thrift.NewTMemoryBufferLen(len(arg1319))
		defer mbTrans1320.Close()
		_, err1321 := mbTrans1320.WriteString(arg1319)
		if err1321 != nil {
			Usage()
			return
		}
		factory1322 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1323 := factory1322.GetProtocol(mbTrans1320)
		containerStruct1 := zeus_ad.NewDeleteVideosArgs()
		err1324 := containerStruct1.ReadField2(jsProt1323)
		if err1324 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.VideoIds
		value1 := argvalue1
		fmt.Print(client.DeleteVideos(value0, value1))
		fmt.Print("\n")
		break
	case "addVideoThumbs":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddVideoThumbs requires 2 args")
			flag.Usage()
		}
		arg1325 := flag.Arg(1)
		mbTrans1326 := thrift.NewTMemoryBufferLen(len(arg1325))
		defer mbTrans1326.Close()
		_, err1327 := mbTrans1326.WriteString(arg1325)
		if err1327 != nil {
			Usage()
			return
		}
		factory1328 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1329 := factory1328.GetProtocol(mbTrans1326)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1330 := argvalue0.Read(jsProt1329)
		if err1330 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1331 := flag.Arg(2)
		mbTrans1332 := thrift.NewTMemoryBufferLen(len(arg1331))
		defer mbTrans1332.Close()
		_, err1333 := mbTrans1332.WriteString(arg1331)
		if err1333 != nil {
			Usage()
			return
		}
		factory1334 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1335 := factory1334.GetProtocol(mbTrans1332)
		containerStruct1 := zeus_ad.NewAddVideoThumbsArgs()
		err1336 := containerStruct1.ReadField2(jsProt1335)
		if err1336 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.VideoThumbs
		value1 := argvalue1
		fmt.Print(client.AddVideoThumbs(value0, value1))
		fmt.Print("\n")
		break
	case "getVideoThumbsByZeusVideoId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideoThumbsByZeusVideoId requires 2 args")
			flag.Usage()
		}
		arg1337 := flag.Arg(1)
		mbTrans1338 := thrift.NewTMemoryBufferLen(len(arg1337))
		defer mbTrans1338.Close()
		_, err1339 := mbTrans1338.WriteString(arg1337)
		if err1339 != nil {
			Usage()
			return
		}
		factory1340 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1341 := factory1340.GetProtocol(mbTrans1338)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1342 := argvalue0.Read(jsProt1341)
		if err1342 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1343 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1343 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetVideoThumbsByZeusVideoId(value0, value1))
		fmt.Print("\n")
		break
	case "addCreativeTexts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreativeTexts requires 2 args")
			flag.Usage()
		}
		arg1344 := flag.Arg(1)
		mbTrans1345 := thrift.NewTMemoryBufferLen(len(arg1344))
		defer mbTrans1345.Close()
		_, err1346 := mbTrans1345.WriteString(arg1344)
		if err1346 != nil {
			Usage()
			return
		}
		factory1347 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1348 := factory1347.GetProtocol(mbTrans1345)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1349 := argvalue0.Read(jsProt1348)
		if err1349 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1350 := flag.Arg(2)
		mbTrans1351 := thrift.NewTMemoryBufferLen(len(arg1350))
		defer mbTrans1351.Close()
		_, err1352 := mbTrans1351.WriteString(arg1350)
		if err1352 != nil {
			Usage()
			return
		}
		factory1353 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1354 := factory1353.GetProtocol(mbTrans1351)
		containerStruct1 := zeus_ad.NewAddCreativeTextsArgs()
		err1355 := containerStruct1.ReadField2(jsProt1354)
		if err1355 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CreativeTexts
		value1 := argvalue1
		fmt.Print(client.AddCreativeTexts(value0, value1))
		fmt.Print("\n")
		break
	case "getCreativeTextsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativeTextsByIds requires 2 args")
			flag.Usage()
		}
		arg1356 := flag.Arg(1)
		mbTrans1357 := thrift.NewTMemoryBufferLen(len(arg1356))
		defer mbTrans1357.Close()
		_, err1358 := mbTrans1357.WriteString(arg1356)
		if err1358 != nil {
			Usage()
			return
		}
		factory1359 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1360 := factory1359.GetProtocol(mbTrans1357)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1361 := argvalue0.Read(jsProt1360)
		if err1361 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1362 := flag.Arg(2)
		mbTrans1363 := thrift.NewTMemoryBufferLen(len(arg1362))
		defer mbTrans1363.Close()
		_, err1364 := mbTrans1363.WriteString(arg1362)
		if err1364 != nil {
			Usage()
			return
		}
		factory1365 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1366 := factory1365.GetProtocol(mbTrans1363)
		containerStruct1 := zeus_ad.NewGetCreativeTextsByIdsArgs()
		err1367 := containerStruct1.ReadField2(jsProt1366)
		if err1367 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCreativeTextsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getCreativeTextsBySourceLanguageId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativeTextsBySourceLanguageId requires 2 args")
			flag.Usage()
		}
		arg1368 := flag.Arg(1)
		mbTrans1369 := thrift.NewTMemoryBufferLen(len(arg1368))
		defer mbTrans1369.Close()
		_, err1370 := mbTrans1369.WriteString(arg1368)
		if err1370 != nil {
			Usage()
			return
		}
		factory1371 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1372 := factory1371.GetProtocol(mbTrans1369)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1373 := argvalue0.Read(jsProt1372)
		if err1373 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1374 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1374 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetCreativeTextsBySourceLanguageId(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCreativeTexts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCreativeTexts requires 2 args")
			flag.Usage()
		}
		arg1375 := flag.Arg(1)
		mbTrans1376 := thrift.NewTMemoryBufferLen(len(arg1375))
		defer mbTrans1376.Close()
		_, err1377 := mbTrans1376.WriteString(arg1375)
		if err1377 != nil {
			Usage()
			return
		}
		factory1378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1379 := factory1378.GetProtocol(mbTrans1376)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1380 := argvalue0.Read(jsProt1379)
		if err1380 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1381 := flag.Arg(2)
		mbTrans1382 := thrift.NewTMemoryBufferLen(len(arg1381))
		defer mbTrans1382.Close()
		_, err1383 := mbTrans1382.WriteString(arg1381)
		if err1383 != nil {
			Usage()
			return
		}
		factory1384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1385 := factory1384.GetProtocol(mbTrans1382)
		containerStruct1 := zeus_ad.NewDeleteCreativeTextsArgs()
		err1386 := containerStruct1.ReadField2(jsProt1385)
		if err1386 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CreativeTextIds
		value1 := argvalue1
		fmt.Print(client.DeleteCreativeTexts(value0, value1))
		fmt.Print("\n")
		break
	case "addRulesTexts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddRulesTexts requires 2 args")
			flag.Usage()
		}
		arg1387 := flag.Arg(1)
		mbTrans1388 := thrift.NewTMemoryBufferLen(len(arg1387))
		defer mbTrans1388.Close()
		_, err1389 := mbTrans1388.WriteString(arg1387)
		if err1389 != nil {
			Usage()
			return
		}
		factory1390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1391 := factory1390.GetProtocol(mbTrans1388)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1392 := argvalue0.Read(jsProt1391)
		if err1392 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1393 := flag.Arg(2)
		mbTrans1394 := thrift.NewTMemoryBufferLen(len(arg1393))
		defer mbTrans1394.Close()
		_, err1395 := mbTrans1394.WriteString(arg1393)
		if err1395 != nil {
			Usage()
			return
		}
		factory1396 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1397 := factory1396.GetProtocol(mbTrans1394)
		containerStruct1 := zeus_ad.NewAddRulesTextsArgs()
		err1398 := containerStruct1.ReadField2(jsProt1397)
		if err1398 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.RulesTexts
		value1 := argvalue1
		fmt.Print(client.AddRulesTexts(value0, value1))
		fmt.Print("\n")
		break
	case "getRulesTextsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetRulesTextsByIds requires 2 args")
			flag.Usage()
		}
		arg1399 := flag.Arg(1)
		mbTrans1400 := thrift.NewTMemoryBufferLen(len(arg1399))
		defer mbTrans1400.Close()
		_, err1401 := mbTrans1400.WriteString(arg1399)
		if err1401 != nil {
			Usage()
			return
		}
		factory1402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1403 := factory1402.GetProtocol(mbTrans1400)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1404 := argvalue0.Read(jsProt1403)
		if err1404 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1405 := flag.Arg(2)
		mbTrans1406 := thrift.NewTMemoryBufferLen(len(arg1405))
		defer mbTrans1406.Close()
		_, err1407 := mbTrans1406.WriteString(arg1405)
		if err1407 != nil {
			Usage()
			return
		}
		factory1408 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1409 := factory1408.GetProtocol(mbTrans1406)
		containerStruct1 := zeus_ad.NewGetRulesTextsByIdsArgs()
		err1410 := containerStruct1.ReadField2(jsProt1409)
		if err1410 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetRulesTextsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getRulesTextsByUserInfo":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetRulesTextsByUserInfo requires 4 args")
			flag.Usage()
		}
		arg1411 := flag.Arg(1)
		mbTrans1412 := thrift.NewTMemoryBufferLen(len(arg1411))
		defer mbTrans1412.Close()
		_, err1413 := mbTrans1412.WriteString(arg1411)
		if err1413 != nil {
			Usage()
			return
		}
		factory1414 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1415 := factory1414.GetProtocol(mbTrans1412)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1416 := argvalue0.Read(jsProt1415)
		if err1416 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1417 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1417 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1418 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1418 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1419 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1419 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetRulesTextsByUserInfo(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteRulesTexts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteRulesTexts requires 2 args")
			flag.Usage()
		}
		arg1420 := flag.Arg(1)
		mbTrans1421 := thrift.NewTMemoryBufferLen(len(arg1420))
		defer mbTrans1421.Close()
		_, err1422 := mbTrans1421.WriteString(arg1420)
		if err1422 != nil {
			Usage()
			return
		}
		factory1423 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1424 := factory1423.GetProtocol(mbTrans1421)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1425 := argvalue0.Read(jsProt1424)
		if err1425 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1426 := flag.Arg(2)
		mbTrans1427 := thrift.NewTMemoryBufferLen(len(arg1426))
		defer mbTrans1427.Close()
		_, err1428 := mbTrans1427.WriteString(arg1426)
		if err1428 != nil {
			Usage()
			return
		}
		factory1429 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1430 := factory1429.GetProtocol(mbTrans1427)
		containerStruct1 := zeus_ad.NewDeleteRulesTextsArgs()
		err1431 := containerStruct1.ReadField2(jsProt1430)
		if err1431 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.RulesTextIds
		value1 := argvalue1
		fmt.Print(client.DeleteRulesTexts(value0, value1))
		fmt.Print("\n")
		break
	case "addInterestGroups":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddInterestGroups requires 2 args")
			flag.Usage()
		}
		arg1432 := flag.Arg(1)
		mbTrans1433 := thrift.NewTMemoryBufferLen(len(arg1432))
		defer mbTrans1433.Close()
		_, err1434 := mbTrans1433.WriteString(arg1432)
		if err1434 != nil {
			Usage()
			return
		}
		factory1435 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1436 := factory1435.GetProtocol(mbTrans1433)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1437 := argvalue0.Read(jsProt1436)
		if err1437 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1438 := flag.Arg(2)
		mbTrans1439 := thrift.NewTMemoryBufferLen(len(arg1438))
		defer mbTrans1439.Close()
		_, err1440 := mbTrans1439.WriteString(arg1438)
		if err1440 != nil {
			Usage()
			return
		}
		factory1441 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1442 := factory1441.GetProtocol(mbTrans1439)
		containerStruct1 := zeus_ad.NewAddInterestGroupsArgs()
		err1443 := containerStruct1.ReadField2(jsProt1442)
		if err1443 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.InterestGroups
		value1 := argvalue1
		fmt.Print(client.AddInterestGroups(value0, value1))
		fmt.Print("\n")
		break
	case "updateInterestGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateInterestGroup requires 2 args")
			flag.Usage()
		}
		arg1444 := flag.Arg(1)
		mbTrans1445 := thrift.NewTMemoryBufferLen(len(arg1444))
		defer mbTrans1445.Close()
		_, err1446 := mbTrans1445.WriteString(arg1444)
		if err1446 != nil {
			Usage()
			return
		}
		factory1447 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1448 := factory1447.GetProtocol(mbTrans1445)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1449 := argvalue0.Read(jsProt1448)
		if err1449 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1450 := flag.Arg(2)
		mbTrans1451 := thrift.NewTMemoryBufferLen(len(arg1450))
		defer mbTrans1451.Close()
		_, err1452 := mbTrans1451.WriteString(arg1450)
		if err1452 != nil {
			Usage()
			return
		}
		factory1453 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1454 := factory1453.GetProtocol(mbTrans1451)
		argvalue1 := zeus_ad.NewZeusInterestGroupLib()
		err1455 := argvalue1.Read(jsProt1454)
		if err1455 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateInterestGroup(value0, value1))
		fmt.Print("\n")
		break
	case "getInterestGroupsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetInterestGroupsByIds requires 2 args")
			flag.Usage()
		}
		arg1456 := flag.Arg(1)
		mbTrans1457 := thrift.NewTMemoryBufferLen(len(arg1456))
		defer mbTrans1457.Close()
		_, err1458 := mbTrans1457.WriteString(arg1456)
		if err1458 != nil {
			Usage()
			return
		}
		factory1459 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1460 := factory1459.GetProtocol(mbTrans1457)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1461 := argvalue0.Read(jsProt1460)
		if err1461 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1462 := flag.Arg(2)
		mbTrans1463 := thrift.NewTMemoryBufferLen(len(arg1462))
		defer mbTrans1463.Close()
		_, err1464 := mbTrans1463.WriteString(arg1462)
		if err1464 != nil {
			Usage()
			return
		}
		factory1465 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1466 := factory1465.GetProtocol(mbTrans1463)
		containerStruct1 := zeus_ad.NewGetInterestGroupsByIdsArgs()
		err1467 := containerStruct1.ReadField2(jsProt1466)
		if err1467 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetInterestGroupsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getInterestGroupsByUserInfo":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetInterestGroupsByUserInfo requires 4 args")
			flag.Usage()
		}
		arg1468 := flag.Arg(1)
		mbTrans1469 := thrift.NewTMemoryBufferLen(len(arg1468))
		defer mbTrans1469.Close()
		_, err1470 := mbTrans1469.WriteString(arg1468)
		if err1470 != nil {
			Usage()
			return
		}
		factory1471 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1472 := factory1471.GetProtocol(mbTrans1469)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1473 := argvalue0.Read(jsProt1472)
		if err1473 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1474 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1474 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1475 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1475 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1476 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1476 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetInterestGroupsByUserInfo(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteInterestGroups":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteInterestGroups requires 2 args")
			flag.Usage()
		}
		arg1477 := flag.Arg(1)
		mbTrans1478 := thrift.NewTMemoryBufferLen(len(arg1477))
		defer mbTrans1478.Close()
		_, err1479 := mbTrans1478.WriteString(arg1477)
		if err1479 != nil {
			Usage()
			return
		}
		factory1480 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1481 := factory1480.GetProtocol(mbTrans1478)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1482 := argvalue0.Read(jsProt1481)
		if err1482 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1483 := flag.Arg(2)
		mbTrans1484 := thrift.NewTMemoryBufferLen(len(arg1483))
		defer mbTrans1484.Close()
		_, err1485 := mbTrans1484.WriteString(arg1483)
		if err1485 != nil {
			Usage()
			return
		}
		factory1486 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1487 := factory1486.GetProtocol(mbTrans1484)
		containerStruct1 := zeus_ad.NewDeleteInterestGroupsArgs()
		err1488 := containerStruct1.ReadField2(jsProt1487)
		if err1488 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.InterestGroupIds
		value1 := argvalue1
		fmt.Print(client.DeleteInterestGroups(value0, value1))
		fmt.Print("\n")
		break
	case "addCustomAudiences":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCustomAudiences requires 2 args")
			flag.Usage()
		}
		arg1489 := flag.Arg(1)
		mbTrans1490 := thrift.NewTMemoryBufferLen(len(arg1489))
		defer mbTrans1490.Close()
		_, err1491 := mbTrans1490.WriteString(arg1489)
		if err1491 != nil {
			Usage()
			return
		}
		factory1492 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1493 := factory1492.GetProtocol(mbTrans1490)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1494 := argvalue0.Read(jsProt1493)
		if err1494 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1495 := flag.Arg(2)
		mbTrans1496 := thrift.NewTMemoryBufferLen(len(arg1495))
		defer mbTrans1496.Close()
		_, err1497 := mbTrans1496.WriteString(arg1495)
		if err1497 != nil {
			Usage()
			return
		}
		factory1498 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1499 := factory1498.GetProtocol(mbTrans1496)
		containerStruct1 := zeus_ad.NewAddCustomAudiencesArgs()
		err1500 := containerStruct1.ReadField2(jsProt1499)
		if err1500 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CustomAudiences
		value1 := argvalue1
		fmt.Print(client.AddCustomAudiences(value0, value1))
		fmt.Print("\n")
		break
	case "updateCustomAudiences":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateCustomAudiences requires 2 args")
			flag.Usage()
		}
		arg1501 := flag.Arg(1)
		mbTrans1502 := thrift.NewTMemoryBufferLen(len(arg1501))
		defer mbTrans1502.Close()
		_, err1503 := mbTrans1502.WriteString(arg1501)
		if err1503 != nil {
			Usage()
			return
		}
		factory1504 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1505 := factory1504.GetProtocol(mbTrans1502)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1506 := argvalue0.Read(jsProt1505)
		if err1506 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1507 := flag.Arg(2)
		mbTrans1508 := thrift.NewTMemoryBufferLen(len(arg1507))
		defer mbTrans1508.Close()
		_, err1509 := mbTrans1508.WriteString(arg1507)
		if err1509 != nil {
			Usage()
			return
		}
		factory1510 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1511 := factory1510.GetProtocol(mbTrans1508)
		containerStruct1 := zeus_ad.NewUpdateCustomAudiencesArgs()
		err1512 := containerStruct1.ReadField2(jsProt1511)
		if err1512 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CustomAudiences
		value1 := argvalue1
		fmt.Print(client.UpdateCustomAudiences(value0, value1))
		fmt.Print("\n")
		break
	case "getCustomAudiencesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCustomAudiencesByIds requires 2 args")
			flag.Usage()
		}
		arg1513 := flag.Arg(1)
		mbTrans1514 := thrift.NewTMemoryBufferLen(len(arg1513))
		defer mbTrans1514.Close()
		_, err1515 := mbTrans1514.WriteString(arg1513)
		if err1515 != nil {
			Usage()
			return
		}
		factory1516 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1517 := factory1516.GetProtocol(mbTrans1514)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1518 := argvalue0.Read(jsProt1517)
		if err1518 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1519 := flag.Arg(2)
		mbTrans1520 := thrift.NewTMemoryBufferLen(len(arg1519))
		defer mbTrans1520.Close()
		_, err1521 := mbTrans1520.WriteString(arg1519)
		if err1521 != nil {
			Usage()
			return
		}
		factory1522 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1523 := factory1522.GetProtocol(mbTrans1520)
		containerStruct1 := zeus_ad.NewGetCustomAudiencesByIdsArgs()
		err1524 := containerStruct1.ReadField2(jsProt1523)
		if err1524 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCustomAudiencesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getCustomAudiencesByUserInfo":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetCustomAudiencesByUserInfo requires 4 args")
			flag.Usage()
		}
		arg1525 := flag.Arg(1)
		mbTrans1526 := thrift.NewTMemoryBufferLen(len(arg1525))
		defer mbTrans1526.Close()
		_, err1527 := mbTrans1526.WriteString(arg1525)
		if err1527 != nil {
			Usage()
			return
		}
		factory1528 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1529 := factory1528.GetProtocol(mbTrans1526)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1530 := argvalue0.Read(jsProt1529)
		if err1530 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1531 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1531 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1532 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1532 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1533 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1533 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetCustomAudiencesByUserInfo(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteCustomAudiences":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCustomAudiences requires 2 args")
			flag.Usage()
		}
		arg1534 := flag.Arg(1)
		mbTrans1535 := thrift.NewTMemoryBufferLen(len(arg1534))
		defer mbTrans1535.Close()
		_, err1536 := mbTrans1535.WriteString(arg1534)
		if err1536 != nil {
			Usage()
			return
		}
		factory1537 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1538 := factory1537.GetProtocol(mbTrans1535)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1539 := argvalue0.Read(jsProt1538)
		if err1539 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1540 := flag.Arg(2)
		mbTrans1541 := thrift.NewTMemoryBufferLen(len(arg1540))
		defer mbTrans1541.Close()
		_, err1542 := mbTrans1541.WriteString(arg1540)
		if err1542 != nil {
			Usage()
			return
		}
		factory1543 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1544 := factory1543.GetProtocol(mbTrans1541)
		containerStruct1 := zeus_ad.NewDeleteCustomAudiencesArgs()
		err1545 := containerStruct1.ReadField2(jsProt1544)
		if err1545 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CustomAudienceIds
		value1 := argvalue1
		fmt.Print(client.DeleteCustomAudiences(value0, value1))
		fmt.Print("\n")
		break
	case "addAutoAdvertiseGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAutoAdvertiseGroup requires 2 args")
			flag.Usage()
		}
		arg1546 := flag.Arg(1)
		mbTrans1547 := thrift.NewTMemoryBufferLen(len(arg1546))
		defer mbTrans1547.Close()
		_, err1548 := mbTrans1547.WriteString(arg1546)
		if err1548 != nil {
			Usage()
			return
		}
		factory1549 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1550 := factory1549.GetProtocol(mbTrans1547)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1551 := argvalue0.Read(jsProt1550)
		if err1551 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1552 := flag.Arg(2)
		mbTrans1553 := thrift.NewTMemoryBufferLen(len(arg1552))
		defer mbTrans1553.Close()
		_, err1554 := mbTrans1553.WriteString(arg1552)
		if err1554 != nil {
			Usage()
			return
		}
		factory1555 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1556 := factory1555.GetProtocol(mbTrans1553)
		argvalue1 := zeus_ad.NewZeusAutoAdvertiseGroup()
		err1557 := argvalue1.Read(jsProt1556)
		if err1557 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAutoAdvertiseGroup(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAutoAdvertiseGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAutoAdvertiseGroup requires 2 args")
			flag.Usage()
		}
		arg1558 := flag.Arg(1)
		mbTrans1559 := thrift.NewTMemoryBufferLen(len(arg1558))
		defer mbTrans1559.Close()
		_, err1560 := mbTrans1559.WriteString(arg1558)
		if err1560 != nil {
			Usage()
			return
		}
		factory1561 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1562 := factory1561.GetProtocol(mbTrans1559)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1563 := argvalue0.Read(jsProt1562)
		if err1563 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1564 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1564 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DeleteAutoAdvertiseGroup(value0, value1))
		fmt.Print("\n")
		break
	case "updateAutoAdvertiseGroupInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAutoAdvertiseGroupInfo requires 2 args")
			flag.Usage()
		}
		arg1565 := flag.Arg(1)
		mbTrans1566 := thrift.NewTMemoryBufferLen(len(arg1565))
		defer mbTrans1566.Close()
		_, err1567 := mbTrans1566.WriteString(arg1565)
		if err1567 != nil {
			Usage()
			return
		}
		factory1568 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1569 := factory1568.GetProtocol(mbTrans1566)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1570 := argvalue0.Read(jsProt1569)
		if err1570 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1571 := flag.Arg(2)
		mbTrans1572 := thrift.NewTMemoryBufferLen(len(arg1571))
		defer mbTrans1572.Close()
		_, err1573 := mbTrans1572.WriteString(arg1571)
		if err1573 != nil {
			Usage()
			return
		}
		factory1574 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1575 := factory1574.GetProtocol(mbTrans1572)
		argvalue1 := zeus_ad.NewZeusAutoAdvertiseGroup()
		err1576 := argvalue1.Read(jsProt1575)
		if err1576 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateAutoAdvertiseGroupInfo(value0, value1))
		fmt.Print("\n")
		break
	case "getAutoAdvertiseGroupsByCampaignIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAutoAdvertiseGroupsByCampaignIds requires 2 args")
			flag.Usage()
		}
		arg1577 := flag.Arg(1)
		mbTrans1578 := thrift.NewTMemoryBufferLen(len(arg1577))
		defer mbTrans1578.Close()
		_, err1579 := mbTrans1578.WriteString(arg1577)
		if err1579 != nil {
			Usage()
			return
		}
		factory1580 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1581 := factory1580.GetProtocol(mbTrans1578)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1582 := argvalue0.Read(jsProt1581)
		if err1582 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1583 := flag.Arg(2)
		mbTrans1584 := thrift.NewTMemoryBufferLen(len(arg1583))
		defer mbTrans1584.Close()
		_, err1585 := mbTrans1584.WriteString(arg1583)
		if err1585 != nil {
			Usage()
			return
		}
		factory1586 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1587 := factory1586.GetProtocol(mbTrans1584)
		containerStruct1 := zeus_ad.NewGetAutoAdvertiseGroupsByCampaignIdsArgs()
		err1588 := containerStruct1.ReadField2(jsProt1587)
		if err1588 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIds
		value1 := argvalue1
		fmt.Print(client.GetAutoAdvertiseGroupsByCampaignIds(value0, value1))
		fmt.Print("\n")
		break
	case "operateAutoAdvertiseGroupInfos":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "OperateAutoAdvertiseGroupInfos requires 3 args")
			flag.Usage()
		}
		arg1589 := flag.Arg(1)
		mbTrans1590 := thrift.NewTMemoryBufferLen(len(arg1589))
		defer mbTrans1590.Close()
		_, err1591 := mbTrans1590.WriteString(arg1589)
		if err1591 != nil {
			Usage()
			return
		}
		factory1592 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1593 := factory1592.GetProtocol(mbTrans1590)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1594 := argvalue0.Read(jsProt1593)
		if err1594 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1595 := flag.Arg(2)
		mbTrans1596 := thrift.NewTMemoryBufferLen(len(arg1595))
		defer mbTrans1596.Close()
		_, err1597 := mbTrans1596.WriteString(arg1595)
		if err1597 != nil {
			Usage()
			return
		}
		factory1598 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1599 := factory1598.GetProtocol(mbTrans1596)
		containerStruct1 := zeus_ad.NewOperateAutoAdvertiseGroupInfosArgs()
		err1600 := containerStruct1.ReadField2(jsProt1599)
		if err1600 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.GroupIds
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := zeus_ad.ObjectOperateType(tmp2)
		value2 := argvalue2
		fmt.Print(client.OperateAutoAdvertiseGroupInfos(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAutoAdvertiseLogs":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAutoAdvertiseLogs requires 2 args")
			flag.Usage()
		}
		arg1601 := flag.Arg(1)
		mbTrans1602 := thrift.NewTMemoryBufferLen(len(arg1601))
		defer mbTrans1602.Close()
		_, err1603 := mbTrans1602.WriteString(arg1601)
		if err1603 != nil {
			Usage()
			return
		}
		factory1604 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1605 := factory1604.GetProtocol(mbTrans1602)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1606 := argvalue0.Read(jsProt1605)
		if err1606 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1607 := flag.Arg(2)
		mbTrans1608 := thrift.NewTMemoryBufferLen(len(arg1607))
		defer mbTrans1608.Close()
		_, err1609 := mbTrans1608.WriteString(arg1607)
		if err1609 != nil {
			Usage()
			return
		}
		factory1610 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1611 := factory1610.GetProtocol(mbTrans1608)
		containerStruct1 := zeus_ad.NewAddAutoAdvertiseLogsArgs()
		err1612 := containerStruct1.ReadField2(jsProt1611)
		if err1612 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ZeusLogs
		value1 := argvalue1
		fmt.Print(client.AddAutoAdvertiseLogs(value0, value1))
		fmt.Print("\n")
		break
	case "addLibGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddLibGroup requires 2 args")
			flag.Usage()
		}
		arg1613 := flag.Arg(1)
		mbTrans1614 := thrift.NewTMemoryBufferLen(len(arg1613))
		defer mbTrans1614.Close()
		_, err1615 := mbTrans1614.WriteString(arg1613)
		if err1615 != nil {
			Usage()
			return
		}
		factory1616 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1617 := factory1616.GetProtocol(mbTrans1614)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1618 := argvalue0.Read(jsProt1617)
		if err1618 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1619 := flag.Arg(2)
		mbTrans1620 := thrift.NewTMemoryBufferLen(len(arg1619))
		defer mbTrans1620.Close()
		_, err1621 := mbTrans1620.WriteString(arg1619)
		if err1621 != nil {
			Usage()
			return
		}
		factory1622 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1623 := factory1622.GetProtocol(mbTrans1620)
		argvalue1 := zeus_ad.NewZeusLibGroup()
		err1624 := argvalue1.Read(jsProt1623)
		if err1624 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddLibGroup(value0, value1))
		fmt.Print("\n")
		break
	case "getLibsByParentId":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetLibsByParentId requires 6 args")
			flag.Usage()
		}
		arg1625 := flag.Arg(1)
		mbTrans1626 := thrift.NewTMemoryBufferLen(len(arg1625))
		defer mbTrans1626.Close()
		_, err1627 := mbTrans1626.WriteString(arg1625)
		if err1627 != nil {
			Usage()
			return
		}
		factory1628 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1629 := factory1628.GetProtocol(mbTrans1626)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1630 := argvalue0.Read(jsProt1629)
		if err1630 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1631 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1631 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1632 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1632 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1633 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1633 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err1634 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1634 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err1635 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err1635 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		fmt.Print(client.GetLibsByParentId(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getLibsByUserInfo":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetLibsByUserInfo requires 5 args")
			flag.Usage()
		}
		arg1636 := flag.Arg(1)
		mbTrans1637 := thrift.NewTMemoryBufferLen(len(arg1636))
		defer mbTrans1637.Close()
		_, err1638 := mbTrans1637.WriteString(arg1636)
		if err1638 != nil {
			Usage()
			return
		}
		factory1639 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1640 := factory1639.GetProtocol(mbTrans1637)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1641 := argvalue0.Read(jsProt1640)
		if err1641 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1642 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1642 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1643 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1643 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1644 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1644 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err1645 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1645 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		fmt.Print(client.GetLibsByUserInfo(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getLibGroupById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLibGroupById requires 2 args")
			flag.Usage()
		}
		arg1646 := flag.Arg(1)
		mbTrans1647 := thrift.NewTMemoryBufferLen(len(arg1646))
		defer mbTrans1647.Close()
		_, err1648 := mbTrans1647.WriteString(arg1646)
		if err1648 != nil {
			Usage()
			return
		}
		factory1649 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1650 := factory1649.GetProtocol(mbTrans1647)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1651 := argvalue0.Read(jsProt1650)
		if err1651 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1652 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1652 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetLibGroupById(value0, value1))
		fmt.Print("\n")
		break
	case "getParentLibGroupsByChildId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetParentLibGroupsByChildId requires 3 args")
			flag.Usage()
		}
		arg1653 := flag.Arg(1)
		mbTrans1654 := thrift.NewTMemoryBufferLen(len(arg1653))
		defer mbTrans1654.Close()
		_, err1655 := mbTrans1654.WriteString(arg1653)
		if err1655 != nil {
			Usage()
			return
		}
		factory1656 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1657 := factory1656.GetProtocol(mbTrans1654)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1658 := argvalue0.Read(jsProt1657)
		if err1658 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1659 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1659 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1660 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1660 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetParentLibGroupsByChildId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateLibGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateLibGroup requires 2 args")
			flag.Usage()
		}
		arg1661 := flag.Arg(1)
		mbTrans1662 := thrift.NewTMemoryBufferLen(len(arg1661))
		defer mbTrans1662.Close()
		_, err1663 := mbTrans1662.WriteString(arg1661)
		if err1663 != nil {
			Usage()
			return
		}
		factory1664 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1665 := factory1664.GetProtocol(mbTrans1662)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1666 := argvalue0.Read(jsProt1665)
		if err1666 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1667 := flag.Arg(2)
		mbTrans1668 := thrift.NewTMemoryBufferLen(len(arg1667))
		defer mbTrans1668.Close()
		_, err1669 := mbTrans1668.WriteString(arg1667)
		if err1669 != nil {
			Usage()
			return
		}
		factory1670 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1671 := factory1670.GetProtocol(mbTrans1668)
		argvalue1 := zeus_ad.NewZeusLibGroup()
		err1672 := argvalue1.Read(jsProt1671)
		if err1672 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateLibGroup(value0, value1))
		fmt.Print("\n")
		break
	case "moveToGroup":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "MoveToGroup requires 5 args")
			flag.Usage()
		}
		arg1673 := flag.Arg(1)
		mbTrans1674 := thrift.NewTMemoryBufferLen(len(arg1673))
		defer mbTrans1674.Close()
		_, err1675 := mbTrans1674.WriteString(arg1673)
		if err1675 != nil {
			Usage()
			return
		}
		factory1676 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1677 := factory1676.GetProtocol(mbTrans1674)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1678 := argvalue0.Read(jsProt1677)
		if err1678 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1679 := flag.Arg(2)
		mbTrans1680 := thrift.NewTMemoryBufferLen(len(arg1679))
		defer mbTrans1680.Close()
		_, err1681 := mbTrans1680.WriteString(arg1679)
		if err1681 != nil {
			Usage()
			return
		}
		factory1682 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1683 := factory1682.GetProtocol(mbTrans1680)
		containerStruct1 := zeus_ad.NewMoveToGroupArgs()
		err1684 := containerStruct1.ReadField2(jsProt1683)
		if err1684 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.LibGroupIds
		value1 := argvalue1
		arg1685 := flag.Arg(3)
		mbTrans1686 := thrift.NewTMemoryBufferLen(len(arg1685))
		defer mbTrans1686.Close()
		_, err1687 := mbTrans1686.WriteString(arg1685)
		if err1687 != nil {
			Usage()
			return
		}
		factory1688 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1689 := factory1688.GetProtocol(mbTrans1686)
		containerStruct2 := zeus_ad.NewMoveToGroupArgs()
		err1690 := containerStruct2.ReadField3(jsProt1689)
		if err1690 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.LibIds
		value2 := argvalue2
		argvalue3, err1691 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1691 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err1692 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1692 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		fmt.Print(client.MoveToGroup(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "moveImageLibToGroup":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "MoveImageLibToGroup requires 7 args")
			flag.Usage()
		}
		arg1693 := flag.Arg(1)
		mbTrans1694 := thrift.NewTMemoryBufferLen(len(arg1693))
		defer mbTrans1694.Close()
		_, err1695 := mbTrans1694.WriteString(arg1693)
		if err1695 != nil {
			Usage()
			return
		}
		factory1696 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1697 := factory1696.GetProtocol(mbTrans1694)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1698 := argvalue0.Read(jsProt1697)
		if err1698 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1699 := flag.Arg(2)
		mbTrans1700 := thrift.NewTMemoryBufferLen(len(arg1699))
		defer mbTrans1700.Close()
		_, err1701 := mbTrans1700.WriteString(arg1699)
		if err1701 != nil {
			Usage()
			return
		}
		factory1702 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1703 := factory1702.GetProtocol(mbTrans1700)
		containerStruct1 := zeus_ad.NewMoveImageLibToGroupArgs()
		err1704 := containerStruct1.ReadField2(jsProt1703)
		if err1704 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.LibGroupIds
		value1 := argvalue1
		arg1705 := flag.Arg(3)
		mbTrans1706 := thrift.NewTMemoryBufferLen(len(arg1705))
		defer mbTrans1706.Close()
		_, err1707 := mbTrans1706.WriteString(arg1705)
		if err1707 != nil {
			Usage()
			return
		}
		factory1708 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1709 := factory1708.GetProtocol(mbTrans1706)
		containerStruct2 := zeus_ad.NewMoveImageLibToGroupArgs()
		err1710 := containerStruct2.ReadField3(jsProt1709)
		if err1710 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.LibIds
		value2 := argvalue2
		argvalue3, err1711 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1711 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err1712 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1712 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err1713 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err1713 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		argvalue6, err1714 := (strconv.ParseInt(flag.Arg(7), 10, 64))
		if err1714 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		fmt.Print(client.MoveImageLibToGroup(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "deleteLibGroup":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteLibGroup requires 3 args")
			flag.Usage()
		}
		arg1715 := flag.Arg(1)
		mbTrans1716 := thrift.NewTMemoryBufferLen(len(arg1715))
		defer mbTrans1716.Close()
		_, err1717 := mbTrans1716.WriteString(arg1715)
		if err1717 != nil {
			Usage()
			return
		}
		factory1718 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1719 := factory1718.GetProtocol(mbTrans1716)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1720 := argvalue0.Read(jsProt1719)
		if err1720 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1721 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1721 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1722 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1722 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.DeleteLibGroup(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteImageLibGroup":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteImageLibGroup requires 4 args")
			flag.Usage()
		}
		arg1723 := flag.Arg(1)
		mbTrans1724 := thrift.NewTMemoryBufferLen(len(arg1723))
		defer mbTrans1724.Close()
		_, err1725 := mbTrans1724.WriteString(arg1723)
		if err1725 != nil {
			Usage()
			return
		}
		factory1726 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1727 := factory1726.GetProtocol(mbTrans1724)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1728 := argvalue0.Read(jsProt1727)
		if err1728 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1729 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1729 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err1730 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1730 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err1731 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1731 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.DeleteImageLibGroup(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addTranslationSchedule":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTranslationSchedule requires 2 args")
			flag.Usage()
		}
		arg1732 := flag.Arg(1)
		mbTrans1733 := thrift.NewTMemoryBufferLen(len(arg1732))
		defer mbTrans1733.Close()
		_, err1734 := mbTrans1733.WriteString(arg1732)
		if err1734 != nil {
			Usage()
			return
		}
		factory1735 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1736 := factory1735.GetProtocol(mbTrans1733)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1737 := argvalue0.Read(jsProt1736)
		if err1737 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1738 := flag.Arg(2)
		mbTrans1739 := thrift.NewTMemoryBufferLen(len(arg1738))
		defer mbTrans1739.Close()
		_, err1740 := mbTrans1739.WriteString(arg1738)
		if err1740 != nil {
			Usage()
			return
		}
		factory1741 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1742 := factory1741.GetProtocol(mbTrans1739)
		argvalue1 := zeus_ad.NewZeusTranslationSchedule()
		err1743 := argvalue1.Read(jsProt1742)
		if err1743 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTranslationSchedule(value0, value1))
		fmt.Print("\n")
		break
	case "updateTranslationSchedule":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateTranslationSchedule requires 2 args")
			flag.Usage()
		}
		arg1744 := flag.Arg(1)
		mbTrans1745 := thrift.NewTMemoryBufferLen(len(arg1744))
		defer mbTrans1745.Close()
		_, err1746 := mbTrans1745.WriteString(arg1744)
		if err1746 != nil {
			Usage()
			return
		}
		factory1747 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1748 := factory1747.GetProtocol(mbTrans1745)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1749 := argvalue0.Read(jsProt1748)
		if err1749 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1750 := flag.Arg(2)
		mbTrans1751 := thrift.NewTMemoryBufferLen(len(arg1750))
		defer mbTrans1751.Close()
		_, err1752 := mbTrans1751.WriteString(arg1750)
		if err1752 != nil {
			Usage()
			return
		}
		factory1753 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1754 := factory1753.GetProtocol(mbTrans1751)
		argvalue1 := zeus_ad.NewZeusTranslationSchedule()
		err1755 := argvalue1.Read(jsProt1754)
		if err1755 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateTranslationSchedule(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationSchedulesBySourceLanguageId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationSchedulesBySourceLanguageId requires 2 args")
			flag.Usage()
		}
		arg1756 := flag.Arg(1)
		mbTrans1757 := thrift.NewTMemoryBufferLen(len(arg1756))
		defer mbTrans1757.Close()
		_, err1758 := mbTrans1757.WriteString(arg1756)
		if err1758 != nil {
			Usage()
			return
		}
		factory1759 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1760 := factory1759.GetProtocol(mbTrans1757)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1761 := argvalue0.Read(jsProt1760)
		if err1761 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1762 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1762 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTranslationSchedulesBySourceLanguageId(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationSchedulesByStatusCodes":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationSchedulesByStatusCodes requires 2 args")
			flag.Usage()
		}
		arg1763 := flag.Arg(1)
		mbTrans1764 := thrift.NewTMemoryBufferLen(len(arg1763))
		defer mbTrans1764.Close()
		_, err1765 := mbTrans1764.WriteString(arg1763)
		if err1765 != nil {
			Usage()
			return
		}
		factory1766 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1767 := factory1766.GetProtocol(mbTrans1764)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1768 := argvalue0.Read(jsProt1767)
		if err1768 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1769 := flag.Arg(2)
		mbTrans1770 := thrift.NewTMemoryBufferLen(len(arg1769))
		defer mbTrans1770.Close()
		_, err1771 := mbTrans1770.WriteString(arg1769)
		if err1771 != nil {
			Usage()
			return
		}
		factory1772 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1773 := factory1772.GetProtocol(mbTrans1770)
		containerStruct1 := zeus_ad.NewGetTranslationSchedulesByStatusCodesArgs()
		err1774 := containerStruct1.ReadField2(jsProt1773)
		if err1774 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.StatusCodes
		value1 := argvalue1
		fmt.Print(client.GetTranslationSchedulesByStatusCodes(value0, value1))
		fmt.Print("\n")
		break
	case "deleteTranslationSchedules":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteTranslationSchedules requires 2 args")
			flag.Usage()
		}
		arg1775 := flag.Arg(1)
		mbTrans1776 := thrift.NewTMemoryBufferLen(len(arg1775))
		defer mbTrans1776.Close()
		_, err1777 := mbTrans1776.WriteString(arg1775)
		if err1777 != nil {
			Usage()
			return
		}
		factory1778 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1779 := factory1778.GetProtocol(mbTrans1776)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1780 := argvalue0.Read(jsProt1779)
		if err1780 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1781 := flag.Arg(2)
		mbTrans1782 := thrift.NewTMemoryBufferLen(len(arg1781))
		defer mbTrans1782.Close()
		_, err1783 := mbTrans1782.WriteString(arg1781)
		if err1783 != nil {
			Usage()
			return
		}
		factory1784 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1785 := factory1784.GetProtocol(mbTrans1782)
		containerStruct1 := zeus_ad.NewDeleteTranslationSchedulesArgs()
		err1786 := containerStruct1.ReadField2(jsProt1785)
		if err1786 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteTranslationSchedules(value0, value1))
		fmt.Print("\n")
		break
	case "addTranslationText":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTranslationText requires 2 args")
			flag.Usage()
		}
		arg1787 := flag.Arg(1)
		mbTrans1788 := thrift.NewTMemoryBufferLen(len(arg1787))
		defer mbTrans1788.Close()
		_, err1789 := mbTrans1788.WriteString(arg1787)
		if err1789 != nil {
			Usage()
			return
		}
		factory1790 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1791 := factory1790.GetProtocol(mbTrans1788)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1792 := argvalue0.Read(jsProt1791)
		if err1792 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1793 := flag.Arg(2)
		mbTrans1794 := thrift.NewTMemoryBufferLen(len(arg1793))
		defer mbTrans1794.Close()
		_, err1795 := mbTrans1794.WriteString(arg1793)
		if err1795 != nil {
			Usage()
			return
		}
		factory1796 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1797 := factory1796.GetProtocol(mbTrans1794)
		argvalue1 := zeus_ad.NewZeusTranslationTextLib()
		err1798 := argvalue1.Read(jsProt1797)
		if err1798 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTranslationText(value0, value1))
		fmt.Print("\n")
		break
	case "updateTranslationText":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateTranslationText requires 2 args")
			flag.Usage()
		}
		arg1799 := flag.Arg(1)
		mbTrans1800 := thrift.NewTMemoryBufferLen(len(arg1799))
		defer mbTrans1800.Close()
		_, err1801 := mbTrans1800.WriteString(arg1799)
		if err1801 != nil {
			Usage()
			return
		}
		factory1802 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1803 := factory1802.GetProtocol(mbTrans1800)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1804 := argvalue0.Read(jsProt1803)
		if err1804 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1805 := flag.Arg(2)
		mbTrans1806 := thrift.NewTMemoryBufferLen(len(arg1805))
		defer mbTrans1806.Close()
		_, err1807 := mbTrans1806.WriteString(arg1805)
		if err1807 != nil {
			Usage()
			return
		}
		factory1808 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1809 := factory1808.GetProtocol(mbTrans1806)
		argvalue1 := zeus_ad.NewZeusTranslationTextLib()
		err1810 := argvalue1.Read(jsProt1809)
		if err1810 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateTranslationText(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationTextsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationTextsByIds requires 2 args")
			flag.Usage()
		}
		arg1811 := flag.Arg(1)
		mbTrans1812 := thrift.NewTMemoryBufferLen(len(arg1811))
		defer mbTrans1812.Close()
		_, err1813 := mbTrans1812.WriteString(arg1811)
		if err1813 != nil {
			Usage()
			return
		}
		factory1814 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1815 := factory1814.GetProtocol(mbTrans1812)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1816 := argvalue0.Read(jsProt1815)
		if err1816 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1817 := flag.Arg(2)
		mbTrans1818 := thrift.NewTMemoryBufferLen(len(arg1817))
		defer mbTrans1818.Close()
		_, err1819 := mbTrans1818.WriteString(arg1817)
		if err1819 != nil {
			Usage()
			return
		}
		factory1820 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1821 := factory1820.GetProtocol(mbTrans1818)
		containerStruct1 := zeus_ad.NewGetTranslationTextsByIdsArgs()
		err1822 := containerStruct1.ReadField2(jsProt1821)
		if err1822 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetTranslationTextsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationTextByProjectId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationTextByProjectId requires 2 args")
			flag.Usage()
		}
		arg1823 := flag.Arg(1)
		mbTrans1824 := thrift.NewTMemoryBufferLen(len(arg1823))
		defer mbTrans1824.Close()
		_, err1825 := mbTrans1824.WriteString(arg1823)
		if err1825 != nil {
			Usage()
			return
		}
		factory1826 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1827 := factory1826.GetProtocol(mbTrans1824)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1828 := argvalue0.Read(jsProt1827)
		if err1828 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1829 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1829 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTranslationTextByProjectId(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationTextsByProjectStatusCodes":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationTextsByProjectStatusCodes requires 2 args")
			flag.Usage()
		}
		arg1830 := flag.Arg(1)
		mbTrans1831 := thrift.NewTMemoryBufferLen(len(arg1830))
		defer mbTrans1831.Close()
		_, err1832 := mbTrans1831.WriteString(arg1830)
		if err1832 != nil {
			Usage()
			return
		}
		factory1833 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1834 := factory1833.GetProtocol(mbTrans1831)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1835 := argvalue0.Read(jsProt1834)
		if err1835 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1836 := flag.Arg(2)
		mbTrans1837 := thrift.NewTMemoryBufferLen(len(arg1836))
		defer mbTrans1837.Close()
		_, err1838 := mbTrans1837.WriteString(arg1836)
		if err1838 != nil {
			Usage()
			return
		}
		factory1839 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1840 := factory1839.GetProtocol(mbTrans1837)
		containerStruct1 := zeus_ad.NewGetTranslationTextsByProjectStatusCodesArgs()
		err1841 := containerStruct1.ReadField2(jsProt1840)
		if err1841 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ProjectStatusCodes
		value1 := argvalue1
		fmt.Print(client.GetTranslationTextsByProjectStatusCodes(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationTextLibByResource":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetTranslationTextLibByResource requires 4 args")
			flag.Usage()
		}
		arg1842 := flag.Arg(1)
		mbTrans1843 := thrift.NewTMemoryBufferLen(len(arg1842))
		defer mbTrans1843.Close()
		_, err1844 := mbTrans1843.WriteString(arg1842)
		if err1844 != nil {
			Usage()
			return
		}
		factory1845 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1846 := factory1845.GetProtocol(mbTrans1843)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1847 := argvalue0.Read(jsProt1846)
		if err1847 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.GetTranslationTextLibByResource(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteTranslationTextsByProjectIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteTranslationTextsByProjectIds requires 2 args")
			flag.Usage()
		}
		arg1851 := flag.Arg(1)
		mbTrans1852 := thrift.NewTMemoryBufferLen(len(arg1851))
		defer mbTrans1852.Close()
		_, err1853 := mbTrans1852.WriteString(arg1851)
		if err1853 != nil {
			Usage()
			return
		}
		factory1854 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1855 := factory1854.GetProtocol(mbTrans1852)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1856 := argvalue0.Read(jsProt1855)
		if err1856 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1857 := flag.Arg(2)
		mbTrans1858 := thrift.NewTMemoryBufferLen(len(arg1857))
		defer mbTrans1858.Close()
		_, err1859 := mbTrans1858.WriteString(arg1857)
		if err1859 != nil {
			Usage()
			return
		}
		factory1860 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1861 := factory1860.GetProtocol(mbTrans1858)
		containerStruct1 := zeus_ad.NewDeleteTranslationTextsByProjectIdsArgs()
		err1862 := containerStruct1.ReadField2(jsProt1861)
		if err1862 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ProjectIds
		value1 := argvalue1
		fmt.Print(client.DeleteTranslationTextsByProjectIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteTranslationTexts":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteTranslationTexts requires 2 args")
			flag.Usage()
		}
		arg1863 := flag.Arg(1)
		mbTrans1864 := thrift.NewTMemoryBufferLen(len(arg1863))
		defer mbTrans1864.Close()
		_, err1865 := mbTrans1864.WriteString(arg1863)
		if err1865 != nil {
			Usage()
			return
		}
		factory1866 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1867 := factory1866.GetProtocol(mbTrans1864)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1868 := argvalue0.Read(jsProt1867)
		if err1868 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1869 := flag.Arg(2)
		mbTrans1870 := thrift.NewTMemoryBufferLen(len(arg1869))
		defer mbTrans1870.Close()
		_, err1871 := mbTrans1870.WriteString(arg1869)
		if err1871 != nil {
			Usage()
			return
		}
		factory1872 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1873 := factory1872.GetProtocol(mbTrans1870)
		containerStruct1 := zeus_ad.NewDeleteTranslationTextsArgs()
		err1874 := containerStruct1.ReadField2(jsProt1873)
		if err1874 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteTranslationTexts(value0, value1))
		fmt.Print("\n")
		break
	case "addTranslationResource":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTranslationResource requires 2 args")
			flag.Usage()
		}
		arg1875 := flag.Arg(1)
		mbTrans1876 := thrift.NewTMemoryBufferLen(len(arg1875))
		defer mbTrans1876.Close()
		_, err1877 := mbTrans1876.WriteString(arg1875)
		if err1877 != nil {
			Usage()
			return
		}
		factory1878 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1879 := factory1878.GetProtocol(mbTrans1876)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1880 := argvalue0.Read(jsProt1879)
		if err1880 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1881 := flag.Arg(2)
		mbTrans1882 := thrift.NewTMemoryBufferLen(len(arg1881))
		defer mbTrans1882.Close()
		_, err1883 := mbTrans1882.WriteString(arg1881)
		if err1883 != nil {
			Usage()
			return
		}
		factory1884 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1885 := factory1884.GetProtocol(mbTrans1882)
		argvalue1 := zeus_ad.NewZeusTranslationResource()
		err1886 := argvalue1.Read(jsProt1885)
		if err1886 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTranslationResource(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationResourcesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationResourcesByIds requires 2 args")
			flag.Usage()
		}
		arg1887 := flag.Arg(1)
		mbTrans1888 := thrift.NewTMemoryBufferLen(len(arg1887))
		defer mbTrans1888.Close()
		_, err1889 := mbTrans1888.WriteString(arg1887)
		if err1889 != nil {
			Usage()
			return
		}
		factory1890 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1891 := factory1890.GetProtocol(mbTrans1888)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1892 := argvalue0.Read(jsProt1891)
		if err1892 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1893 := flag.Arg(2)
		mbTrans1894 := thrift.NewTMemoryBufferLen(len(arg1893))
		defer mbTrans1894.Close()
		_, err1895 := mbTrans1894.WriteString(arg1893)
		if err1895 != nil {
			Usage()
			return
		}
		factory1896 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1897 := factory1896.GetProtocol(mbTrans1894)
		containerStruct1 := zeus_ad.NewGetTranslationResourcesByIdsArgs()
		err1898 := containerStruct1.ReadField2(jsProt1897)
		if err1898 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetTranslationResourcesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationResourcesByProjectId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationResourcesByProjectId requires 2 args")
			flag.Usage()
		}
		arg1899 := flag.Arg(1)
		mbTrans1900 := thrift.NewTMemoryBufferLen(len(arg1899))
		defer mbTrans1900.Close()
		_, err1901 := mbTrans1900.WriteString(arg1899)
		if err1901 != nil {
			Usage()
			return
		}
		factory1902 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1903 := factory1902.GetProtocol(mbTrans1900)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1904 := argvalue0.Read(jsProt1903)
		if err1904 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err1905 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1905 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTranslationResourcesByProjectId(value0, value1))
		fmt.Print("\n")
		break
	case "getTranslationResourcesByResourceKey":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTranslationResourcesByResourceKey requires 2 args")
			flag.Usage()
		}
		arg1906 := flag.Arg(1)
		mbTrans1907 := thrift.NewTMemoryBufferLen(len(arg1906))
		defer mbTrans1907.Close()
		_, err1908 := mbTrans1907.WriteString(arg1906)
		if err1908 != nil {
			Usage()
			return
		}
		factory1909 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1910 := factory1909.GetProtocol(mbTrans1907)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1911 := argvalue0.Read(jsProt1910)
		if err1911 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetTranslationResourcesByResourceKey(value0, value1))
		fmt.Print("\n")
		break
	case "deleteTranslationResources":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteTranslationResources requires 2 args")
			flag.Usage()
		}
		arg1913 := flag.Arg(1)
		mbTrans1914 := thrift.NewTMemoryBufferLen(len(arg1913))
		defer mbTrans1914.Close()
		_, err1915 := mbTrans1914.WriteString(arg1913)
		if err1915 != nil {
			Usage()
			return
		}
		factory1916 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1917 := factory1916.GetProtocol(mbTrans1914)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1918 := argvalue0.Read(jsProt1917)
		if err1918 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1919 := flag.Arg(2)
		mbTrans1920 := thrift.NewTMemoryBufferLen(len(arg1919))
		defer mbTrans1920.Close()
		_, err1921 := mbTrans1920.WriteString(arg1919)
		if err1921 != nil {
			Usage()
			return
		}
		factory1922 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1923 := factory1922.GetProtocol(mbTrans1920)
		containerStruct1 := zeus_ad.NewDeleteTranslationResourcesArgs()
		err1924 := containerStruct1.ReadField2(jsProt1923)
		if err1924 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteTranslationResources(value0, value1))
		fmt.Print("\n")
		break
	case "addZeusInterest":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddZeusInterest requires 2 args")
			flag.Usage()
		}
		arg1925 := flag.Arg(1)
		mbTrans1926 := thrift.NewTMemoryBufferLen(len(arg1925))
		defer mbTrans1926.Close()
		_, err1927 := mbTrans1926.WriteString(arg1925)
		if err1927 != nil {
			Usage()
			return
		}
		factory1928 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1929 := factory1928.GetProtocol(mbTrans1926)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1930 := argvalue0.Read(jsProt1929)
		if err1930 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1931 := flag.Arg(2)
		mbTrans1932 := thrift.NewTMemoryBufferLen(len(arg1931))
		defer mbTrans1932.Close()
		_, err1933 := mbTrans1932.WriteString(arg1931)
		if err1933 != nil {
			Usage()
			return
		}
		factory1934 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1935 := factory1934.GetProtocol(mbTrans1932)
		argvalue1 := zeus_ad.NewZeusInterest()
		err1936 := argvalue1.Read(jsProt1935)
		if err1936 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddZeusInterest(value0, value1))
		fmt.Print("\n")
		break
	case "updateZeusInterest":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateZeusInterest requires 2 args")
			flag.Usage()
		}
		arg1937 := flag.Arg(1)
		mbTrans1938 := thrift.NewTMemoryBufferLen(len(arg1937))
		defer mbTrans1938.Close()
		_, err1939 := mbTrans1938.WriteString(arg1937)
		if err1939 != nil {
			Usage()
			return
		}
		factory1940 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1941 := factory1940.GetProtocol(mbTrans1938)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1942 := argvalue0.Read(jsProt1941)
		if err1942 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1943 := flag.Arg(2)
		mbTrans1944 := thrift.NewTMemoryBufferLen(len(arg1943))
		defer mbTrans1944.Close()
		_, err1945 := mbTrans1944.WriteString(arg1943)
		if err1945 != nil {
			Usage()
			return
		}
		factory1946 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1947 := factory1946.GetProtocol(mbTrans1944)
		argvalue1 := zeus_ad.NewZeusInterest()
		err1948 := argvalue1.Read(jsProt1947)
		if err1948 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateZeusInterest(value0, value1))
		fmt.Print("\n")
		break
	case "getAllZeusInterests":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllZeusInterests requires 1 args")
			flag.Usage()
		}
		arg1949 := flag.Arg(1)
		mbTrans1950 := thrift.NewTMemoryBufferLen(len(arg1949))
		defer mbTrans1950.Close()
		_, err1951 := mbTrans1950.WriteString(arg1949)
		if err1951 != nil {
			Usage()
			return
		}
		factory1952 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1953 := factory1952.GetProtocol(mbTrans1950)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1954 := argvalue0.Read(jsProt1953)
		if err1954 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetAllZeusInterests(value0))
		fmt.Print("\n")
		break
	case "getZeusInterestsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetZeusInterestsByIds requires 2 args")
			flag.Usage()
		}
		arg1955 := flag.Arg(1)
		mbTrans1956 := thrift.NewTMemoryBufferLen(len(arg1955))
		defer mbTrans1956.Close()
		_, err1957 := mbTrans1956.WriteString(arg1955)
		if err1957 != nil {
			Usage()
			return
		}
		factory1958 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1959 := factory1958.GetProtocol(mbTrans1956)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1960 := argvalue0.Read(jsProt1959)
		if err1960 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1961 := flag.Arg(2)
		mbTrans1962 := thrift.NewTMemoryBufferLen(len(arg1961))
		defer mbTrans1962.Close()
		_, err1963 := mbTrans1962.WriteString(arg1961)
		if err1963 != nil {
			Usage()
			return
		}
		factory1964 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1965 := factory1964.GetProtocol(mbTrans1962)
		containerStruct1 := zeus_ad.NewGetZeusInterestsByIdsArgs()
		err1966 := containerStruct1.ReadField2(jsProt1965)
		if err1966 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetZeusInterestsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteZeusInterestsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteZeusInterestsByIds requires 2 args")
			flag.Usage()
		}
		arg1967 := flag.Arg(1)
		mbTrans1968 := thrift.NewTMemoryBufferLen(len(arg1967))
		defer mbTrans1968.Close()
		_, err1969 := mbTrans1968.WriteString(arg1967)
		if err1969 != nil {
			Usage()
			return
		}
		factory1970 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1971 := factory1970.GetProtocol(mbTrans1968)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1972 := argvalue0.Read(jsProt1971)
		if err1972 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1973 := flag.Arg(2)
		mbTrans1974 := thrift.NewTMemoryBufferLen(len(arg1973))
		defer mbTrans1974.Close()
		_, err1975 := mbTrans1974.WriteString(arg1973)
		if err1975 != nil {
			Usage()
			return
		}
		factory1976 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1977 := factory1976.GetProtocol(mbTrans1974)
		containerStruct1 := zeus_ad.NewDeleteZeusInterestsByIdsArgs()
		err1978 := containerStruct1.ReadField2(jsProt1977)
		if err1978 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteZeusInterestsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addTag":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTag requires 2 args")
			flag.Usage()
		}
		arg1979 := flag.Arg(1)
		mbTrans1980 := thrift.NewTMemoryBufferLen(len(arg1979))
		defer mbTrans1980.Close()
		_, err1981 := mbTrans1980.WriteString(arg1979)
		if err1981 != nil {
			Usage()
			return
		}
		factory1982 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1983 := factory1982.GetProtocol(mbTrans1980)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1984 := argvalue0.Read(jsProt1983)
		if err1984 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1985 := flag.Arg(2)
		mbTrans1986 := thrift.NewTMemoryBufferLen(len(arg1985))
		defer mbTrans1986.Close()
		_, err1987 := mbTrans1986.WriteString(arg1985)
		if err1987 != nil {
			Usage()
			return
		}
		factory1988 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1989 := factory1988.GetProtocol(mbTrans1986)
		argvalue1 := zeus_ad.NewZeusTag()
		err1990 := argvalue1.Read(jsProt1989)
		if err1990 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTag(value0, value1))
		fmt.Print("\n")
		break
	case "updateTag":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateTag requires 2 args")
			flag.Usage()
		}
		arg1991 := flag.Arg(1)
		mbTrans1992 := thrift.NewTMemoryBufferLen(len(arg1991))
		defer mbTrans1992.Close()
		_, err1993 := mbTrans1992.WriteString(arg1991)
		if err1993 != nil {
			Usage()
			return
		}
		factory1994 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1995 := factory1994.GetProtocol(mbTrans1992)
		argvalue0 := zeus_ad.NewRequestHeader()
		err1996 := argvalue0.Read(jsProt1995)
		if err1996 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1997 := flag.Arg(2)
		mbTrans1998 := thrift.NewTMemoryBufferLen(len(arg1997))
		defer mbTrans1998.Close()
		_, err1999 := mbTrans1998.WriteString(arg1997)
		if err1999 != nil {
			Usage()
			return
		}
		factory2000 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2001 := factory2000.GetProtocol(mbTrans1998)
		argvalue1 := zeus_ad.NewZeusTag()
		err2002 := argvalue1.Read(jsProt2001)
		if err2002 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateTag(value0, value1))
		fmt.Print("\n")
		break
	case "getTagsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTagsByIds requires 2 args")
			flag.Usage()
		}
		arg2003 := flag.Arg(1)
		mbTrans2004 := thrift.NewTMemoryBufferLen(len(arg2003))
		defer mbTrans2004.Close()
		_, err2005 := mbTrans2004.WriteString(arg2003)
		if err2005 != nil {
			Usage()
			return
		}
		factory2006 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2007 := factory2006.GetProtocol(mbTrans2004)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2008 := argvalue0.Read(jsProt2007)
		if err2008 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg2009 := flag.Arg(2)
		mbTrans2010 := thrift.NewTMemoryBufferLen(len(arg2009))
		defer mbTrans2010.Close()
		_, err2011 := mbTrans2010.WriteString(arg2009)
		if err2011 != nil {
			Usage()
			return
		}
		factory2012 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2013 := factory2012.GetProtocol(mbTrans2010)
		containerStruct1 := zeus_ad.NewGetTagsByIdsArgs()
		err2014 := containerStruct1.ReadField2(jsProt2013)
		if err2014 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetTagsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAllTags":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllTags requires 1 args")
			flag.Usage()
		}
		arg2015 := flag.Arg(1)
		mbTrans2016 := thrift.NewTMemoryBufferLen(len(arg2015))
		defer mbTrans2016.Close()
		_, err2017 := mbTrans2016.WriteString(arg2015)
		if err2017 != nil {
			Usage()
			return
		}
		factory2018 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2019 := factory2018.GetProtocol(mbTrans2016)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2020 := argvalue0.Read(jsProt2019)
		if err2020 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetAllTags(value0))
		fmt.Print("\n")
		break
	case "getTagsByParentId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTagsByParentId requires 2 args")
			flag.Usage()
		}
		arg2021 := flag.Arg(1)
		mbTrans2022 := thrift.NewTMemoryBufferLen(len(arg2021))
		defer mbTrans2022.Close()
		_, err2023 := mbTrans2022.WriteString(arg2021)
		if err2023 != nil {
			Usage()
			return
		}
		factory2024 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2025 := factory2024.GetProtocol(mbTrans2022)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2026 := argvalue0.Read(jsProt2025)
		if err2026 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err2027 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err2027 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTagsByParentId(value0, value1))
		fmt.Print("\n")
		break
	case "deleteTagsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteTagsByIds requires 2 args")
			flag.Usage()
		}
		arg2028 := flag.Arg(1)
		mbTrans2029 := thrift.NewTMemoryBufferLen(len(arg2028))
		defer mbTrans2029.Close()
		_, err2030 := mbTrans2029.WriteString(arg2028)
		if err2030 != nil {
			Usage()
			return
		}
		factory2031 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2032 := factory2031.GetProtocol(mbTrans2029)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2033 := argvalue0.Read(jsProt2032)
		if err2033 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg2034 := flag.Arg(2)
		mbTrans2035 := thrift.NewTMemoryBufferLen(len(arg2034))
		defer mbTrans2035.Close()
		_, err2036 := mbTrans2035.WriteString(arg2034)
		if err2036 != nil {
			Usage()
			return
		}
		factory2037 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2038 := factory2037.GetProtocol(mbTrans2035)
		containerStruct1 := zeus_ad.NewDeleteTagsByIdsArgs()
		err2039 := containerStruct1.ReadField2(jsProt2038)
		if err2039 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteTagsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addZeusGameWarehouse":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddZeusGameWarehouse requires 2 args")
			flag.Usage()
		}
		arg2040 := flag.Arg(1)
		mbTrans2041 := thrift.NewTMemoryBufferLen(len(arg2040))
		defer mbTrans2041.Close()
		_, err2042 := mbTrans2041.WriteString(arg2040)
		if err2042 != nil {
			Usage()
			return
		}
		factory2043 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2044 := factory2043.GetProtocol(mbTrans2041)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2045 := argvalue0.Read(jsProt2044)
		if err2045 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg2046 := flag.Arg(2)
		mbTrans2047 := thrift.NewTMemoryBufferLen(len(arg2046))
		defer mbTrans2047.Close()
		_, err2048 := mbTrans2047.WriteString(arg2046)
		if err2048 != nil {
			Usage()
			return
		}
		factory2049 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2050 := factory2049.GetProtocol(mbTrans2047)
		argvalue1 := zeus_ad.NewZeusGameWarehouse()
		err2051 := argvalue1.Read(jsProt2050)
		if err2051 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddZeusGameWarehouse(value0, value1))
		fmt.Print("\n")
		break
	case "getZeusGameWarehousesByTagId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetZeusGameWarehousesByTagId requires 4 args")
			flag.Usage()
		}
		arg2052 := flag.Arg(1)
		mbTrans2053 := thrift.NewTMemoryBufferLen(len(arg2052))
		defer mbTrans2053.Close()
		_, err2054 := mbTrans2053.WriteString(arg2052)
		if err2054 != nil {
			Usage()
			return
		}
		factory2055 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2056 := factory2055.GetProtocol(mbTrans2053)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2057 := argvalue0.Read(jsProt2056)
		if err2057 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err2058 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err2058 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err2059 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err2059 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err2060 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err2060 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.GetZeusGameWarehousesByTagId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getZeusGameWarehouseById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetZeusGameWarehouseById requires 2 args")
			flag.Usage()
		}
		arg2061 := flag.Arg(1)
		mbTrans2062 := thrift.NewTMemoryBufferLen(len(arg2061))
		defer mbTrans2062.Close()
		_, err2063 := mbTrans2062.WriteString(arg2061)
		if err2063 != nil {
			Usage()
			return
		}
		factory2064 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2065 := factory2064.GetProtocol(mbTrans2062)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2066 := argvalue0.Read(jsProt2065)
		if err2066 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err2067 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err2067 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetZeusGameWarehouseById(value0, value1))
		fmt.Print("\n")
		break
	case "getZeusGameWarehouseByGameId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetZeusGameWarehouseByGameId requires 3 args")
			flag.Usage()
		}
		arg2068 := flag.Arg(1)
		mbTrans2069 := thrift.NewTMemoryBufferLen(len(arg2068))
		defer mbTrans2069.Close()
		_, err2070 := mbTrans2069.WriteString(arg2068)
		if err2070 != nil {
			Usage()
			return
		}
		factory2071 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2072 := factory2071.GetProtocol(mbTrans2069)
		argvalue0 := zeus_ad.NewRequestHeader()
		err2073 := argvalue0.Read(jsProt2072)
		if err2073 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err2075 := (strconv.Atoi(flag.Arg(3)))
		if err2075 != nil {
			Usage()
			return
		}
		argvalue2 := byte(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetZeusGameWarehouseByGameId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err2080 := (strconv.Atoi(flag.Arg(1)))
		if err2080 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
