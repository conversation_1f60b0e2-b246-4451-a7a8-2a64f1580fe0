// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"mediainfo"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  MediaIdInt addMedia(RequestHeader header, Media media)")
	fmt.Fprintln(os.Stderr, "  void editMedia(RequestHeader header, Media media)")
	fmt.Fprintln(os.<PERSON>r, "  void pauseMediaByMids(RequestHeader header, UidInt uid,  mids)")
	fmt.Fprintln(os.Stderr, "  void resumeMediaByMids(RequestHeader header, UidInt uid,  mids)")
	fmt.Fprintln(os.Stderr, "  void deleteMediaByMids(RequestHeader header, UidInt uid,  mids)")
	fmt.Fprintln(os.Stderr, "  void submitMediaByMids(RequestHeader header, UidInt uid,  mids)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaByUid(RequestHeader header, UidInt uid, bool excludeDeleted, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaByUidOrderByMid(RequestHeader header, UidInt uid, bool excludeDeleted, MediaQueryInt fromMid, MediaQueryInt limit)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listRunnableMediaByUidOrderByLastUpdate(RequestHeader header, UidInt uid, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getMediaByMids(RequestHeader header,  mids, bool withoutSettings, bool withoutPackageInfo)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaByTypeAndStatus(RequestHeader header, MediaType type, MediaStatus status, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  void adminApproveMediaByMids(RequestHeader header,  mids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminRejectMediaByMids(RequestHeader header,  mids,  comments)")
	fmt.Fprintln(os.Stderr, "  void editMediaSetting(RequestHeader header, MediaSetting mediaSetting)")
	fmt.Fprintln(os.Stderr, "   getMediaStatusLamp(RequestHeader header,  mids)")
	fmt.Fprintln(os.Stderr, "  void uploadAppMedia(RequestHeader header, Media media)")
	fmt.Fprintln(os.Stderr, "  void uploadDummyAppMedia(RequestHeader header, Media media)")
	fmt.Fprintln(os.Stderr, "  MediaSummary getMediaSummaryByUid(RequestHeader header, UidInt uid)")
	fmt.Fprintln(os.Stderr, "  void updateMediaPackageInfo(RequestHeader header, MediaPackageInfo packageInfo)")
	fmt.Fprintln(os.Stderr, "  MediaAppUpdateIdInt addMediaAppUpdate(RequestHeader header, MediaAppUpdate mediaAppUpdate)")
	fmt.Fprintln(os.Stderr, "   getMediaAppUpdatesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaAppUpdateByMid(RequestHeader header, MediaIdInt mid, bool includeDeleted, string orderBy, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaAppUpdateByStatus(RequestHeader header, MediaAppUpdateStatus status, string orderBy, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  void adminApproveMediaAppUpdatesByIds(RequestHeader header,  ids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminRejectMediaAppUpdatesByIds(RequestHeader header,  ids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminForbidMediaByIds(RequestHeader header,  ids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminUnforbidMediaByIds(RequestHeader header,  ids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminResumeMediaAppUpdatesByMids(RequestHeader header,  mids,  comments)")
	fmt.Fprintln(os.Stderr, "  void adminPauseMediaAppUpdatesByMids(RequestHeader header,  mids,  comments)")
	fmt.Fprintln(os.Stderr, "  void userResumeMediaAppUpdatesByMids(RequestHeader header,  mids)")
	fmt.Fprintln(os.Stderr, "  void userPauseMediaAppUpdatesByMids(RequestHeader header,  mids)")
	fmt.Fprintln(os.Stderr, "  MediaAppUpdateWrapper getLatestMediaAppUpdate(RequestHeader header, UidInt uid, MediaIdInt mid, string packageName, SDKPlatform sdkPlatform)")
	fmt.Fprintln(os.Stderr, "  MediaDetectInfo getMediaDetectInfo(RequestHeader header, MediaIdInt mid, PlacementIdInt pmid)")
	fmt.Fprintln(os.Stderr, "  void updateMediaAppRate(RequestHeader header, MediaAppRate mediaAppRate)")
	fmt.Fprintln(os.Stderr, "  MediaAppRate getMediaAppRateByUidAndMid(RequestHeader header, UidInt uid, MediaIdInt mid)")
	fmt.Fprintln(os.Stderr, "  void userPauseMediaAppRateByMid(RequestHeader header, UidInt uid, MediaIdInt mid)")
	fmt.Fprintln(os.Stderr, "  void userResumeMediaAppRateByMid(RequestHeader header, UidInt uid, MediaIdInt mid)")
	fmt.Fprintln(os.Stderr, "  void pausePlacementByIds(RequestHeader header, UidInt uid, MediaIdInt mid,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumePlacementByIds(RequestHeader header, UidInt uid, MediaIdInt mid,  ids)")
	fmt.Fprintln(os.Stderr, "  void deletePlacementByIds(RequestHeader header, UidInt uid, MediaIdInt mid,  ids)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listPlacementByMid(RequestHeader header, MediaIdInt mid, bool excludeDeleted, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getPlacementByUid(RequestHeader header, UidInt uid, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   getPlacementByIds(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "   getMediaOrderById(RequestHeader header, bool excludeDeleted, MediaQueryInt fromId, MediaQueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getPlacementsOrderById(RequestHeader header, bool excludeDeleted, MediaQueryInt fromId, MediaQueryInt limit)")
	fmt.Fprintln(os.Stderr, "  void editPlacementSetting(RequestHeader header, PlacementSetting setting)")
	fmt.Fprintln(os.Stderr, "  void editPlacementSettingExact(RequestHeader header, PlacementSetting setting)")
	fmt.Fprintln(os.Stderr, "  void editPremiumAdSequence(RequestHeader header, UidInt uid, MediaIdInt mid, PlacementIdInt pmid,  sequence)")
	fmt.Fprintln(os.Stderr, "  void attachToHouseAd(RequestHeader header, UidInt uid, MediaIdInt mid, PlacementIdInt pmid, AdCreativeIdInt cid, HouseAdPriorityType type, bool addToHead)")
	fmt.Fprintln(os.Stderr, "  void detachFromHouseAd(RequestHeader header, UidInt uid, MediaIdInt mid, PlacementIdInt pmid, AdCreativeIdInt cid, HouseAdPriorityType type)")
	fmt.Fprintln(os.Stderr, "   getAttachedPmidsByHouseAdCid(RequestHeader header, AdCreativeIdInt cid, HouseAdPriorityType type)")
	fmt.Fprintln(os.Stderr, "  MediaIdInt addPlacement(RequestHeader header, Placement placement)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaByUidAndNameFuzzyQuery(RequestHeader header, UidInt uid, string name, bool excludeDeleted, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  MediaQueryResult listMediaByUidAndTypeAndPauseAndStatus(RequestHeader header, UidInt uid,  type,  paused, bool excludeDeleted,  status, MediaQueryInt offset, MediaQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  Container addContainer(RequestHeader header, IdInt uid, IdInt mid, Container container)")
	fmt.Fprintln(os.Stderr, "  Container editContainer(RequestHeader header, IdInt uid, IdInt mid, Container container)")
	fmt.Fprintln(os.Stderr, "   getContainersByIdList(RequestHeader header,  idList)")
	fmt.Fprintln(os.Stderr, "   getContainersByPmid(RequestHeader header, IdInt pmid)")
	fmt.Fprintln(os.Stderr, "  void disableContainersByIdList(RequestHeader header, IdInt uid, IdInt mid, IdInt pmid,  idList)")
	fmt.Fprintln(os.Stderr, "  void enableContainersByIdList(RequestHeader header, IdInt uid, IdInt mid, IdInt pmid,  idList)")
	fmt.Fprintln(os.Stderr, "  void deleteContainersByIdList(RequestHeader header, IdInt uid, IdInt mid, IdInt pmid,  idList)")
	fmt.Fprintln(os.Stderr, "  IdInt addChannel(RequestHeader header, Channel channel)")
	fmt.Fprintln(os.Stderr, "   getChannelsByIds(RequestHeader header,  chnIds)")
	fmt.Fprintln(os.Stderr, "  void editChannel(RequestHeader header, Channel channel)")
	fmt.Fprintln(os.Stderr, "   getChannelsByPmid(RequestHeader header, PlacementIdInt pmid)")
	fmt.Fprintln(os.Stderr, "  void disableChannelsByIds(RequestHeader header,  chnIds)")
	fmt.Fprintln(os.Stderr, "  void enableChannelsByIds(RequestHeader header,  chnIds)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := mediainfo.NewMediaInfoServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMedia requires 2 args")
			flag.Usage()
		}
		arg342 := flag.Arg(1)
		mbTrans343 := thrift.NewTMemoryBufferLen(len(arg342))
		defer mbTrans343.Close()
		_, err344 := mbTrans343.WriteString(arg342)
		if err344 != nil {
			Usage()
			return
		}
		factory345 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt346 := factory345.GetProtocol(mbTrans343)
		argvalue0 := mediainfo.NewRequestHeader()
		err347 := argvalue0.Read(jsProt346)
		if err347 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg348 := flag.Arg(2)
		mbTrans349 := thrift.NewTMemoryBufferLen(len(arg348))
		defer mbTrans349.Close()
		_, err350 := mbTrans349.WriteString(arg348)
		if err350 != nil {
			Usage()
			return
		}
		factory351 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt352 := factory351.GetProtocol(mbTrans349)
		argvalue1 := mediainfo.NewMedia()
		err353 := argvalue1.Read(jsProt352)
		if err353 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Media(argvalue1)
		fmt.Print(client.AddMedia(value0, value1))
		fmt.Print("\n")
		break
	case "editMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditMedia requires 2 args")
			flag.Usage()
		}
		arg354 := flag.Arg(1)
		mbTrans355 := thrift.NewTMemoryBufferLen(len(arg354))
		defer mbTrans355.Close()
		_, err356 := mbTrans355.WriteString(arg354)
		if err356 != nil {
			Usage()
			return
		}
		factory357 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt358 := factory357.GetProtocol(mbTrans355)
		argvalue0 := mediainfo.NewRequestHeader()
		err359 := argvalue0.Read(jsProt358)
		if err359 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg360 := flag.Arg(2)
		mbTrans361 := thrift.NewTMemoryBufferLen(len(arg360))
		defer mbTrans361.Close()
		_, err362 := mbTrans361.WriteString(arg360)
		if err362 != nil {
			Usage()
			return
		}
		factory363 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt364 := factory363.GetProtocol(mbTrans361)
		argvalue1 := mediainfo.NewMedia()
		err365 := argvalue1.Read(jsProt364)
		if err365 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Media(argvalue1)
		fmt.Print(client.EditMedia(value0, value1))
		fmt.Print("\n")
		break
	case "pauseMediaByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseMediaByMids requires 3 args")
			flag.Usage()
		}
		arg366 := flag.Arg(1)
		mbTrans367 := thrift.NewTMemoryBufferLen(len(arg366))
		defer mbTrans367.Close()
		_, err368 := mbTrans367.WriteString(arg366)
		if err368 != nil {
			Usage()
			return
		}
		factory369 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt370 := factory369.GetProtocol(mbTrans367)
		argvalue0 := mediainfo.NewRequestHeader()
		err371 := argvalue0.Read(jsProt370)
		if err371 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err372 := (strconv.Atoi(flag.Arg(2)))
		if err372 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		arg373 := flag.Arg(3)
		mbTrans374 := thrift.NewTMemoryBufferLen(len(arg373))
		defer mbTrans374.Close()
		_, err375 := mbTrans374.WriteString(arg373)
		if err375 != nil {
			Usage()
			return
		}
		factory376 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt377 := factory376.GetProtocol(mbTrans374)
		containerStruct2 := mediainfo.NewPauseMediaByMidsArgs()
		err378 := containerStruct2.ReadField3(jsProt377)
		if err378 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Mids
		value2 := argvalue2
		fmt.Print(client.PauseMediaByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeMediaByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeMediaByMids requires 3 args")
			flag.Usage()
		}
		arg379 := flag.Arg(1)
		mbTrans380 := thrift.NewTMemoryBufferLen(len(arg379))
		defer mbTrans380.Close()
		_, err381 := mbTrans380.WriteString(arg379)
		if err381 != nil {
			Usage()
			return
		}
		factory382 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt383 := factory382.GetProtocol(mbTrans380)
		argvalue0 := mediainfo.NewRequestHeader()
		err384 := argvalue0.Read(jsProt383)
		if err384 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err385 := (strconv.Atoi(flag.Arg(2)))
		if err385 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		arg386 := flag.Arg(3)
		mbTrans387 := thrift.NewTMemoryBufferLen(len(arg386))
		defer mbTrans387.Close()
		_, err388 := mbTrans387.WriteString(arg386)
		if err388 != nil {
			Usage()
			return
		}
		factory389 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt390 := factory389.GetProtocol(mbTrans387)
		containerStruct2 := mediainfo.NewResumeMediaByMidsArgs()
		err391 := containerStruct2.ReadField3(jsProt390)
		if err391 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Mids
		value2 := argvalue2
		fmt.Print(client.ResumeMediaByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteMediaByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteMediaByMids requires 3 args")
			flag.Usage()
		}
		arg392 := flag.Arg(1)
		mbTrans393 := thrift.NewTMemoryBufferLen(len(arg392))
		defer mbTrans393.Close()
		_, err394 := mbTrans393.WriteString(arg392)
		if err394 != nil {
			Usage()
			return
		}
		factory395 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt396 := factory395.GetProtocol(mbTrans393)
		argvalue0 := mediainfo.NewRequestHeader()
		err397 := argvalue0.Read(jsProt396)
		if err397 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err398 := (strconv.Atoi(flag.Arg(2)))
		if err398 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		arg399 := flag.Arg(3)
		mbTrans400 := thrift.NewTMemoryBufferLen(len(arg399))
		defer mbTrans400.Close()
		_, err401 := mbTrans400.WriteString(arg399)
		if err401 != nil {
			Usage()
			return
		}
		factory402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt403 := factory402.GetProtocol(mbTrans400)
		containerStruct2 := mediainfo.NewDeleteMediaByMidsArgs()
		err404 := containerStruct2.ReadField3(jsProt403)
		if err404 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Mids
		value2 := argvalue2
		fmt.Print(client.DeleteMediaByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "submitMediaByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SubmitMediaByMids requires 3 args")
			flag.Usage()
		}
		arg405 := flag.Arg(1)
		mbTrans406 := thrift.NewTMemoryBufferLen(len(arg405))
		defer mbTrans406.Close()
		_, err407 := mbTrans406.WriteString(arg405)
		if err407 != nil {
			Usage()
			return
		}
		factory408 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt409 := factory408.GetProtocol(mbTrans406)
		argvalue0 := mediainfo.NewRequestHeader()
		err410 := argvalue0.Read(jsProt409)
		if err410 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err411 := (strconv.Atoi(flag.Arg(2)))
		if err411 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		arg412 := flag.Arg(3)
		mbTrans413 := thrift.NewTMemoryBufferLen(len(arg412))
		defer mbTrans413.Close()
		_, err414 := mbTrans413.WriteString(arg412)
		if err414 != nil {
			Usage()
			return
		}
		factory415 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt416 := factory415.GetProtocol(mbTrans413)
		containerStruct2 := mediainfo.NewSubmitMediaByMidsArgs()
		err417 := containerStruct2.ReadField3(jsProt416)
		if err417 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Mids
		value2 := argvalue2
		fmt.Print(client.SubmitMediaByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listMediaByUid":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListMediaByUid requires 6 args")
			flag.Usage()
		}
		arg418 := flag.Arg(1)
		mbTrans419 := thrift.NewTMemoryBufferLen(len(arg418))
		defer mbTrans419.Close()
		_, err420 := mbTrans419.WriteString(arg418)
		if err420 != nil {
			Usage()
			return
		}
		factory421 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt422 := factory421.GetProtocol(mbTrans419)
		argvalue0 := mediainfo.NewRequestHeader()
		err423 := argvalue0.Read(jsProt422)
		if err423 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err424 := (strconv.Atoi(flag.Arg(2)))
		if err424 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err426 := (strconv.Atoi(flag.Arg(4)))
		if err426 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		tmp4, err427 := (strconv.Atoi(flag.Arg(5)))
		if err427 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListMediaByUid(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listMediaByUidOrderByMid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListMediaByUidOrderByMid requires 5 args")
			flag.Usage()
		}
		arg429 := flag.Arg(1)
		mbTrans430 := thrift.NewTMemoryBufferLen(len(arg429))
		defer mbTrans430.Close()
		_, err431 := mbTrans430.WriteString(arg429)
		if err431 != nil {
			Usage()
			return
		}
		factory432 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt433 := factory432.GetProtocol(mbTrans430)
		argvalue0 := mediainfo.NewRequestHeader()
		err434 := argvalue0.Read(jsProt433)
		if err434 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err435 := (strconv.Atoi(flag.Arg(2)))
		if err435 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err437 := (strconv.Atoi(flag.Arg(4)))
		if err437 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		tmp4, err438 := (strconv.Atoi(flag.Arg(5)))
		if err438 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		fmt.Print(client.ListMediaByUidOrderByMid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listRunnableMediaByUidOrderByLastUpdate":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListRunnableMediaByUidOrderByLastUpdate requires 5 args")
			flag.Usage()
		}
		arg439 := flag.Arg(1)
		mbTrans440 := thrift.NewTMemoryBufferLen(len(arg439))
		defer mbTrans440.Close()
		_, err441 := mbTrans440.WriteString(arg439)
		if err441 != nil {
			Usage()
			return
		}
		factory442 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt443 := factory442.GetProtocol(mbTrans440)
		argvalue0 := mediainfo.NewRequestHeader()
		err444 := argvalue0.Read(jsProt443)
		if err444 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err445 := (strconv.Atoi(flag.Arg(2)))
		if err445 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err446 := (strconv.Atoi(flag.Arg(3)))
		if err446 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaQueryInt(argvalue2)
		tmp3, err447 := (strconv.Atoi(flag.Arg(4)))
		if err447 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.ListRunnableMediaByUidOrderByLastUpdate(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getMediaByMids":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetMediaByMids requires 4 args")
			flag.Usage()
		}
		arg449 := flag.Arg(1)
		mbTrans450 := thrift.NewTMemoryBufferLen(len(arg449))
		defer mbTrans450.Close()
		_, err451 := mbTrans450.WriteString(arg449)
		if err451 != nil {
			Usage()
			return
		}
		factory452 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt453 := factory452.GetProtocol(mbTrans450)
		argvalue0 := mediainfo.NewRequestHeader()
		err454 := argvalue0.Read(jsProt453)
		if err454 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg455 := flag.Arg(2)
		mbTrans456 := thrift.NewTMemoryBufferLen(len(arg455))
		defer mbTrans456.Close()
		_, err457 := mbTrans456.WriteString(arg455)
		if err457 != nil {
			Usage()
			return
		}
		factory458 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt459 := factory458.GetProtocol(mbTrans456)
		containerStruct1 := mediainfo.NewGetMediaByMidsArgs()
		err460 := containerStruct1.ReadField2(jsProt459)
		if err460 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.GetMediaByMids(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listMediaByTypeAndStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListMediaByTypeAndStatus requires 6 args")
			flag.Usage()
		}
		arg463 := flag.Arg(1)
		mbTrans464 := thrift.NewTMemoryBufferLen(len(arg463))
		defer mbTrans464.Close()
		_, err465 := mbTrans464.WriteString(arg463)
		if err465 != nil {
			Usage()
			return
		}
		factory466 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt467 := factory466.GetProtocol(mbTrans464)
		argvalue0 := mediainfo.NewRequestHeader()
		err468 := argvalue0.Read(jsProt467)
		if err468 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := mediainfo.MediaType(tmp1)
		value1 := mediainfo.MediaType(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := mediainfo.MediaStatus(tmp2)
		value2 := mediainfo.MediaStatus(argvalue2)
		tmp3, err469 := (strconv.Atoi(flag.Arg(4)))
		if err469 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		tmp4, err470 := (strconv.Atoi(flag.Arg(5)))
		if err470 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListMediaByTypeAndStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "adminApproveMediaByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminApproveMediaByMids requires 3 args")
			flag.Usage()
		}
		arg472 := flag.Arg(1)
		mbTrans473 := thrift.NewTMemoryBufferLen(len(arg472))
		defer mbTrans473.Close()
		_, err474 := mbTrans473.WriteString(arg472)
		if err474 != nil {
			Usage()
			return
		}
		factory475 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt476 := factory475.GetProtocol(mbTrans473)
		argvalue0 := mediainfo.NewRequestHeader()
		err477 := argvalue0.Read(jsProt476)
		if err477 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg478 := flag.Arg(2)
		mbTrans479 := thrift.NewTMemoryBufferLen(len(arg478))
		defer mbTrans479.Close()
		_, err480 := mbTrans479.WriteString(arg478)
		if err480 != nil {
			Usage()
			return
		}
		factory481 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt482 := factory481.GetProtocol(mbTrans479)
		containerStruct1 := mediainfo.NewAdminApproveMediaByMidsArgs()
		err483 := containerStruct1.ReadField2(jsProt482)
		if err483 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		arg484 := flag.Arg(3)
		mbTrans485 := thrift.NewTMemoryBufferLen(len(arg484))
		defer mbTrans485.Close()
		_, err486 := mbTrans485.WriteString(arg484)
		if err486 != nil {
			Usage()
			return
		}
		factory487 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt488 := factory487.GetProtocol(mbTrans485)
		containerStruct2 := mediainfo.NewAdminApproveMediaByMidsArgs()
		err489 := containerStruct2.ReadField3(jsProt488)
		if err489 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminApproveMediaByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminRejectMediaByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminRejectMediaByMids requires 3 args")
			flag.Usage()
		}
		arg490 := flag.Arg(1)
		mbTrans491 := thrift.NewTMemoryBufferLen(len(arg490))
		defer mbTrans491.Close()
		_, err492 := mbTrans491.WriteString(arg490)
		if err492 != nil {
			Usage()
			return
		}
		factory493 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt494 := factory493.GetProtocol(mbTrans491)
		argvalue0 := mediainfo.NewRequestHeader()
		err495 := argvalue0.Read(jsProt494)
		if err495 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg496 := flag.Arg(2)
		mbTrans497 := thrift.NewTMemoryBufferLen(len(arg496))
		defer mbTrans497.Close()
		_, err498 := mbTrans497.WriteString(arg496)
		if err498 != nil {
			Usage()
			return
		}
		factory499 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt500 := factory499.GetProtocol(mbTrans497)
		containerStruct1 := mediainfo.NewAdminRejectMediaByMidsArgs()
		err501 := containerStruct1.ReadField2(jsProt500)
		if err501 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		arg502 := flag.Arg(3)
		mbTrans503 := thrift.NewTMemoryBufferLen(len(arg502))
		defer mbTrans503.Close()
		_, err504 := mbTrans503.WriteString(arg502)
		if err504 != nil {
			Usage()
			return
		}
		factory505 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt506 := factory505.GetProtocol(mbTrans503)
		containerStruct2 := mediainfo.NewAdminRejectMediaByMidsArgs()
		err507 := containerStruct2.ReadField3(jsProt506)
		if err507 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminRejectMediaByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editMediaSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditMediaSetting requires 2 args")
			flag.Usage()
		}
		arg508 := flag.Arg(1)
		mbTrans509 := thrift.NewTMemoryBufferLen(len(arg508))
		defer mbTrans509.Close()
		_, err510 := mbTrans509.WriteString(arg508)
		if err510 != nil {
			Usage()
			return
		}
		factory511 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt512 := factory511.GetProtocol(mbTrans509)
		argvalue0 := mediainfo.NewRequestHeader()
		err513 := argvalue0.Read(jsProt512)
		if err513 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg514 := flag.Arg(2)
		mbTrans515 := thrift.NewTMemoryBufferLen(len(arg514))
		defer mbTrans515.Close()
		_, err516 := mbTrans515.WriteString(arg514)
		if err516 != nil {
			Usage()
			return
		}
		factory517 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt518 := factory517.GetProtocol(mbTrans515)
		argvalue1 := mediainfo.NewMediaSetting()
		err519 := argvalue1.Read(jsProt518)
		if err519 != nil {
			Usage()
			return
		}
		value1 := mediainfo.MediaSetting(argvalue1)
		fmt.Print(client.EditMediaSetting(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaStatusLamp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaStatusLamp requires 2 args")
			flag.Usage()
		}
		arg520 := flag.Arg(1)
		mbTrans521 := thrift.NewTMemoryBufferLen(len(arg520))
		defer mbTrans521.Close()
		_, err522 := mbTrans521.WriteString(arg520)
		if err522 != nil {
			Usage()
			return
		}
		factory523 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt524 := factory523.GetProtocol(mbTrans521)
		argvalue0 := mediainfo.NewRequestHeader()
		err525 := argvalue0.Read(jsProt524)
		if err525 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg526 := flag.Arg(2)
		mbTrans527 := thrift.NewTMemoryBufferLen(len(arg526))
		defer mbTrans527.Close()
		_, err528 := mbTrans527.WriteString(arg526)
		if err528 != nil {
			Usage()
			return
		}
		factory529 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt530 := factory529.GetProtocol(mbTrans527)
		containerStruct1 := mediainfo.NewGetMediaStatusLampArgs()
		err531 := containerStruct1.ReadField2(jsProt530)
		if err531 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		fmt.Print(client.GetMediaStatusLamp(value0, value1))
		fmt.Print("\n")
		break
	case "uploadAppMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UploadAppMedia requires 2 args")
			flag.Usage()
		}
		arg532 := flag.Arg(1)
		mbTrans533 := thrift.NewTMemoryBufferLen(len(arg532))
		defer mbTrans533.Close()
		_, err534 := mbTrans533.WriteString(arg532)
		if err534 != nil {
			Usage()
			return
		}
		factory535 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt536 := factory535.GetProtocol(mbTrans533)
		argvalue0 := mediainfo.NewRequestHeader()
		err537 := argvalue0.Read(jsProt536)
		if err537 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg538 := flag.Arg(2)
		mbTrans539 := thrift.NewTMemoryBufferLen(len(arg538))
		defer mbTrans539.Close()
		_, err540 := mbTrans539.WriteString(arg538)
		if err540 != nil {
			Usage()
			return
		}
		factory541 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt542 := factory541.GetProtocol(mbTrans539)
		argvalue1 := mediainfo.NewMedia()
		err543 := argvalue1.Read(jsProt542)
		if err543 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Media(argvalue1)
		fmt.Print(client.UploadAppMedia(value0, value1))
		fmt.Print("\n")
		break
	case "uploadDummyAppMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UploadDummyAppMedia requires 2 args")
			flag.Usage()
		}
		arg544 := flag.Arg(1)
		mbTrans545 := thrift.NewTMemoryBufferLen(len(arg544))
		defer mbTrans545.Close()
		_, err546 := mbTrans545.WriteString(arg544)
		if err546 != nil {
			Usage()
			return
		}
		factory547 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt548 := factory547.GetProtocol(mbTrans545)
		argvalue0 := mediainfo.NewRequestHeader()
		err549 := argvalue0.Read(jsProt548)
		if err549 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg550 := flag.Arg(2)
		mbTrans551 := thrift.NewTMemoryBufferLen(len(arg550))
		defer mbTrans551.Close()
		_, err552 := mbTrans551.WriteString(arg550)
		if err552 != nil {
			Usage()
			return
		}
		factory553 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt554 := factory553.GetProtocol(mbTrans551)
		argvalue1 := mediainfo.NewMedia()
		err555 := argvalue1.Read(jsProt554)
		if err555 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Media(argvalue1)
		fmt.Print(client.UploadDummyAppMedia(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaSummaryByUid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaSummaryByUid requires 2 args")
			flag.Usage()
		}
		arg556 := flag.Arg(1)
		mbTrans557 := thrift.NewTMemoryBufferLen(len(arg556))
		defer mbTrans557.Close()
		_, err558 := mbTrans557.WriteString(arg556)
		if err558 != nil {
			Usage()
			return
		}
		factory559 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt560 := factory559.GetProtocol(mbTrans557)
		argvalue0 := mediainfo.NewRequestHeader()
		err561 := argvalue0.Read(jsProt560)
		if err561 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err562 := (strconv.Atoi(flag.Arg(2)))
		if err562 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		fmt.Print(client.GetMediaSummaryByUid(value0, value1))
		fmt.Print("\n")
		break
	case "updateMediaPackageInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateMediaPackageInfo requires 2 args")
			flag.Usage()
		}
		arg563 := flag.Arg(1)
		mbTrans564 := thrift.NewTMemoryBufferLen(len(arg563))
		defer mbTrans564.Close()
		_, err565 := mbTrans564.WriteString(arg563)
		if err565 != nil {
			Usage()
			return
		}
		factory566 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt567 := factory566.GetProtocol(mbTrans564)
		argvalue0 := mediainfo.NewRequestHeader()
		err568 := argvalue0.Read(jsProt567)
		if err568 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg569 := flag.Arg(2)
		mbTrans570 := thrift.NewTMemoryBufferLen(len(arg569))
		defer mbTrans570.Close()
		_, err571 := mbTrans570.WriteString(arg569)
		if err571 != nil {
			Usage()
			return
		}
		factory572 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt573 := factory572.GetProtocol(mbTrans570)
		argvalue1 := mediainfo.NewMediaPackageInfo()
		err574 := argvalue1.Read(jsProt573)
		if err574 != nil {
			Usage()
			return
		}
		value1 := mediainfo.MediaPackageInfo(argvalue1)
		fmt.Print(client.UpdateMediaPackageInfo(value0, value1))
		fmt.Print("\n")
		break
	case "addMediaAppUpdate":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMediaAppUpdate requires 2 args")
			flag.Usage()
		}
		arg575 := flag.Arg(1)
		mbTrans576 := thrift.NewTMemoryBufferLen(len(arg575))
		defer mbTrans576.Close()
		_, err577 := mbTrans576.WriteString(arg575)
		if err577 != nil {
			Usage()
			return
		}
		factory578 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt579 := factory578.GetProtocol(mbTrans576)
		argvalue0 := mediainfo.NewRequestHeader()
		err580 := argvalue0.Read(jsProt579)
		if err580 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg581 := flag.Arg(2)
		mbTrans582 := thrift.NewTMemoryBufferLen(len(arg581))
		defer mbTrans582.Close()
		_, err583 := mbTrans582.WriteString(arg581)
		if err583 != nil {
			Usage()
			return
		}
		factory584 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt585 := factory584.GetProtocol(mbTrans582)
		argvalue1 := mediainfo.NewMediaAppUpdate()
		err586 := argvalue1.Read(jsProt585)
		if err586 != nil {
			Usage()
			return
		}
		value1 := mediainfo.MediaAppUpdate(argvalue1)
		fmt.Print(client.AddMediaAppUpdate(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaAppUpdatesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaAppUpdatesByIds requires 2 args")
			flag.Usage()
		}
		arg587 := flag.Arg(1)
		mbTrans588 := thrift.NewTMemoryBufferLen(len(arg587))
		defer mbTrans588.Close()
		_, err589 := mbTrans588.WriteString(arg587)
		if err589 != nil {
			Usage()
			return
		}
		factory590 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt591 := factory590.GetProtocol(mbTrans588)
		argvalue0 := mediainfo.NewRequestHeader()
		err592 := argvalue0.Read(jsProt591)
		if err592 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg593 := flag.Arg(2)
		mbTrans594 := thrift.NewTMemoryBufferLen(len(arg593))
		defer mbTrans594.Close()
		_, err595 := mbTrans594.WriteString(arg593)
		if err595 != nil {
			Usage()
			return
		}
		factory596 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt597 := factory596.GetProtocol(mbTrans594)
		containerStruct1 := mediainfo.NewGetMediaAppUpdatesByIdsArgs()
		err598 := containerStruct1.ReadField2(jsProt597)
		if err598 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetMediaAppUpdatesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listMediaAppUpdateByMid":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListMediaAppUpdateByMid requires 7 args")
			flag.Usage()
		}
		arg599 := flag.Arg(1)
		mbTrans600 := thrift.NewTMemoryBufferLen(len(arg599))
		defer mbTrans600.Close()
		_, err601 := mbTrans600.WriteString(arg599)
		if err601 != nil {
			Usage()
			return
		}
		factory602 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt603 := factory602.GetProtocol(mbTrans600)
		argvalue0 := mediainfo.NewRequestHeader()
		err604 := argvalue0.Read(jsProt603)
		if err604 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err605 := (strconv.Atoi(flag.Arg(2)))
		if err605 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.MediaIdInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err608 := (strconv.Atoi(flag.Arg(5)))
		if err608 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		tmp5, err609 := (strconv.Atoi(flag.Arg(6)))
		if err609 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := mediainfo.MediaQueryInt(argvalue5)
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListMediaAppUpdateByMid(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listMediaAppUpdateByStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListMediaAppUpdateByStatus requires 6 args")
			flag.Usage()
		}
		arg611 := flag.Arg(1)
		mbTrans612 := thrift.NewTMemoryBufferLen(len(arg611))
		defer mbTrans612.Close()
		_, err613 := mbTrans612.WriteString(arg611)
		if err613 != nil {
			Usage()
			return
		}
		factory614 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt615 := factory614.GetProtocol(mbTrans612)
		argvalue0 := mediainfo.NewRequestHeader()
		err616 := argvalue0.Read(jsProt615)
		if err616 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := mediainfo.MediaAppUpdateStatus(tmp1)
		value1 := mediainfo.MediaAppUpdateStatus(argvalue1)
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		tmp3, err618 := (strconv.Atoi(flag.Arg(4)))
		if err618 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		tmp4, err619 := (strconv.Atoi(flag.Arg(5)))
		if err619 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListMediaAppUpdateByStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "adminApproveMediaAppUpdatesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminApproveMediaAppUpdatesByIds requires 3 args")
			flag.Usage()
		}
		arg621 := flag.Arg(1)
		mbTrans622 := thrift.NewTMemoryBufferLen(len(arg621))
		defer mbTrans622.Close()
		_, err623 := mbTrans622.WriteString(arg621)
		if err623 != nil {
			Usage()
			return
		}
		factory624 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt625 := factory624.GetProtocol(mbTrans622)
		argvalue0 := mediainfo.NewRequestHeader()
		err626 := argvalue0.Read(jsProt625)
		if err626 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg627 := flag.Arg(2)
		mbTrans628 := thrift.NewTMemoryBufferLen(len(arg627))
		defer mbTrans628.Close()
		_, err629 := mbTrans628.WriteString(arg627)
		if err629 != nil {
			Usage()
			return
		}
		factory630 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt631 := factory630.GetProtocol(mbTrans628)
		containerStruct1 := mediainfo.NewAdminApproveMediaAppUpdatesByIdsArgs()
		err632 := containerStruct1.ReadField2(jsProt631)
		if err632 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		arg633 := flag.Arg(3)
		mbTrans634 := thrift.NewTMemoryBufferLen(len(arg633))
		defer mbTrans634.Close()
		_, err635 := mbTrans634.WriteString(arg633)
		if err635 != nil {
			Usage()
			return
		}
		factory636 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt637 := factory636.GetProtocol(mbTrans634)
		containerStruct2 := mediainfo.NewAdminApproveMediaAppUpdatesByIdsArgs()
		err638 := containerStruct2.ReadField3(jsProt637)
		if err638 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminApproveMediaAppUpdatesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminRejectMediaAppUpdatesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminRejectMediaAppUpdatesByIds requires 3 args")
			flag.Usage()
		}
		arg639 := flag.Arg(1)
		mbTrans640 := thrift.NewTMemoryBufferLen(len(arg639))
		defer mbTrans640.Close()
		_, err641 := mbTrans640.WriteString(arg639)
		if err641 != nil {
			Usage()
			return
		}
		factory642 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt643 := factory642.GetProtocol(mbTrans640)
		argvalue0 := mediainfo.NewRequestHeader()
		err644 := argvalue0.Read(jsProt643)
		if err644 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg645 := flag.Arg(2)
		mbTrans646 := thrift.NewTMemoryBufferLen(len(arg645))
		defer mbTrans646.Close()
		_, err647 := mbTrans646.WriteString(arg645)
		if err647 != nil {
			Usage()
			return
		}
		factory648 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt649 := factory648.GetProtocol(mbTrans646)
		containerStruct1 := mediainfo.NewAdminRejectMediaAppUpdatesByIdsArgs()
		err650 := containerStruct1.ReadField2(jsProt649)
		if err650 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		arg651 := flag.Arg(3)
		mbTrans652 := thrift.NewTMemoryBufferLen(len(arg651))
		defer mbTrans652.Close()
		_, err653 := mbTrans652.WriteString(arg651)
		if err653 != nil {
			Usage()
			return
		}
		factory654 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt655 := factory654.GetProtocol(mbTrans652)
		containerStruct2 := mediainfo.NewAdminRejectMediaAppUpdatesByIdsArgs()
		err656 := containerStruct2.ReadField3(jsProt655)
		if err656 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminRejectMediaAppUpdatesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminForbidMediaByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminForbidMediaByIds requires 3 args")
			flag.Usage()
		}
		arg657 := flag.Arg(1)
		mbTrans658 := thrift.NewTMemoryBufferLen(len(arg657))
		defer mbTrans658.Close()
		_, err659 := mbTrans658.WriteString(arg657)
		if err659 != nil {
			Usage()
			return
		}
		factory660 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt661 := factory660.GetProtocol(mbTrans658)
		argvalue0 := mediainfo.NewRequestHeader()
		err662 := argvalue0.Read(jsProt661)
		if err662 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg663 := flag.Arg(2)
		mbTrans664 := thrift.NewTMemoryBufferLen(len(arg663))
		defer mbTrans664.Close()
		_, err665 := mbTrans664.WriteString(arg663)
		if err665 != nil {
			Usage()
			return
		}
		factory666 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt667 := factory666.GetProtocol(mbTrans664)
		containerStruct1 := mediainfo.NewAdminForbidMediaByIdsArgs()
		err668 := containerStruct1.ReadField2(jsProt667)
		if err668 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		arg669 := flag.Arg(3)
		mbTrans670 := thrift.NewTMemoryBufferLen(len(arg669))
		defer mbTrans670.Close()
		_, err671 := mbTrans670.WriteString(arg669)
		if err671 != nil {
			Usage()
			return
		}
		factory672 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt673 := factory672.GetProtocol(mbTrans670)
		containerStruct2 := mediainfo.NewAdminForbidMediaByIdsArgs()
		err674 := containerStruct2.ReadField3(jsProt673)
		if err674 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminForbidMediaByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminUnforbidMediaByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminUnforbidMediaByIds requires 3 args")
			flag.Usage()
		}
		arg675 := flag.Arg(1)
		mbTrans676 := thrift.NewTMemoryBufferLen(len(arg675))
		defer mbTrans676.Close()
		_, err677 := mbTrans676.WriteString(arg675)
		if err677 != nil {
			Usage()
			return
		}
		factory678 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt679 := factory678.GetProtocol(mbTrans676)
		argvalue0 := mediainfo.NewRequestHeader()
		err680 := argvalue0.Read(jsProt679)
		if err680 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg681 := flag.Arg(2)
		mbTrans682 := thrift.NewTMemoryBufferLen(len(arg681))
		defer mbTrans682.Close()
		_, err683 := mbTrans682.WriteString(arg681)
		if err683 != nil {
			Usage()
			return
		}
		factory684 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt685 := factory684.GetProtocol(mbTrans682)
		containerStruct1 := mediainfo.NewAdminUnforbidMediaByIdsArgs()
		err686 := containerStruct1.ReadField2(jsProt685)
		if err686 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		arg687 := flag.Arg(3)
		mbTrans688 := thrift.NewTMemoryBufferLen(len(arg687))
		defer mbTrans688.Close()
		_, err689 := mbTrans688.WriteString(arg687)
		if err689 != nil {
			Usage()
			return
		}
		factory690 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt691 := factory690.GetProtocol(mbTrans688)
		containerStruct2 := mediainfo.NewAdminUnforbidMediaByIdsArgs()
		err692 := containerStruct2.ReadField3(jsProt691)
		if err692 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminUnforbidMediaByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminResumeMediaAppUpdatesByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminResumeMediaAppUpdatesByMids requires 3 args")
			flag.Usage()
		}
		arg693 := flag.Arg(1)
		mbTrans694 := thrift.NewTMemoryBufferLen(len(arg693))
		defer mbTrans694.Close()
		_, err695 := mbTrans694.WriteString(arg693)
		if err695 != nil {
			Usage()
			return
		}
		factory696 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt697 := factory696.GetProtocol(mbTrans694)
		argvalue0 := mediainfo.NewRequestHeader()
		err698 := argvalue0.Read(jsProt697)
		if err698 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg699 := flag.Arg(2)
		mbTrans700 := thrift.NewTMemoryBufferLen(len(arg699))
		defer mbTrans700.Close()
		_, err701 := mbTrans700.WriteString(arg699)
		if err701 != nil {
			Usage()
			return
		}
		factory702 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt703 := factory702.GetProtocol(mbTrans700)
		containerStruct1 := mediainfo.NewAdminResumeMediaAppUpdatesByMidsArgs()
		err704 := containerStruct1.ReadField2(jsProt703)
		if err704 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		arg705 := flag.Arg(3)
		mbTrans706 := thrift.NewTMemoryBufferLen(len(arg705))
		defer mbTrans706.Close()
		_, err707 := mbTrans706.WriteString(arg705)
		if err707 != nil {
			Usage()
			return
		}
		factory708 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt709 := factory708.GetProtocol(mbTrans706)
		containerStruct2 := mediainfo.NewAdminResumeMediaAppUpdatesByMidsArgs()
		err710 := containerStruct2.ReadField3(jsProt709)
		if err710 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminResumeMediaAppUpdatesByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "adminPauseMediaAppUpdatesByMids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AdminPauseMediaAppUpdatesByMids requires 3 args")
			flag.Usage()
		}
		arg711 := flag.Arg(1)
		mbTrans712 := thrift.NewTMemoryBufferLen(len(arg711))
		defer mbTrans712.Close()
		_, err713 := mbTrans712.WriteString(arg711)
		if err713 != nil {
			Usage()
			return
		}
		factory714 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt715 := factory714.GetProtocol(mbTrans712)
		argvalue0 := mediainfo.NewRequestHeader()
		err716 := argvalue0.Read(jsProt715)
		if err716 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg717 := flag.Arg(2)
		mbTrans718 := thrift.NewTMemoryBufferLen(len(arg717))
		defer mbTrans718.Close()
		_, err719 := mbTrans718.WriteString(arg717)
		if err719 != nil {
			Usage()
			return
		}
		factory720 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt721 := factory720.GetProtocol(mbTrans718)
		containerStruct1 := mediainfo.NewAdminPauseMediaAppUpdatesByMidsArgs()
		err722 := containerStruct1.ReadField2(jsProt721)
		if err722 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		arg723 := flag.Arg(3)
		mbTrans724 := thrift.NewTMemoryBufferLen(len(arg723))
		defer mbTrans724.Close()
		_, err725 := mbTrans724.WriteString(arg723)
		if err725 != nil {
			Usage()
			return
		}
		factory726 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt727 := factory726.GetProtocol(mbTrans724)
		containerStruct2 := mediainfo.NewAdminPauseMediaAppUpdatesByMidsArgs()
		err728 := containerStruct2.ReadField3(jsProt727)
		if err728 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AdminPauseMediaAppUpdatesByMids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "userResumeMediaAppUpdatesByMids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UserResumeMediaAppUpdatesByMids requires 2 args")
			flag.Usage()
		}
		arg729 := flag.Arg(1)
		mbTrans730 := thrift.NewTMemoryBufferLen(len(arg729))
		defer mbTrans730.Close()
		_, err731 := mbTrans730.WriteString(arg729)
		if err731 != nil {
			Usage()
			return
		}
		factory732 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt733 := factory732.GetProtocol(mbTrans730)
		argvalue0 := mediainfo.NewRequestHeader()
		err734 := argvalue0.Read(jsProt733)
		if err734 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg735 := flag.Arg(2)
		mbTrans736 := thrift.NewTMemoryBufferLen(len(arg735))
		defer mbTrans736.Close()
		_, err737 := mbTrans736.WriteString(arg735)
		if err737 != nil {
			Usage()
			return
		}
		factory738 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt739 := factory738.GetProtocol(mbTrans736)
		containerStruct1 := mediainfo.NewUserResumeMediaAppUpdatesByMidsArgs()
		err740 := containerStruct1.ReadField2(jsProt739)
		if err740 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		fmt.Print(client.UserResumeMediaAppUpdatesByMids(value0, value1))
		fmt.Print("\n")
		break
	case "userPauseMediaAppUpdatesByMids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UserPauseMediaAppUpdatesByMids requires 2 args")
			flag.Usage()
		}
		arg741 := flag.Arg(1)
		mbTrans742 := thrift.NewTMemoryBufferLen(len(arg741))
		defer mbTrans742.Close()
		_, err743 := mbTrans742.WriteString(arg741)
		if err743 != nil {
			Usage()
			return
		}
		factory744 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt745 := factory744.GetProtocol(mbTrans742)
		argvalue0 := mediainfo.NewRequestHeader()
		err746 := argvalue0.Read(jsProt745)
		if err746 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg747 := flag.Arg(2)
		mbTrans748 := thrift.NewTMemoryBufferLen(len(arg747))
		defer mbTrans748.Close()
		_, err749 := mbTrans748.WriteString(arg747)
		if err749 != nil {
			Usage()
			return
		}
		factory750 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt751 := factory750.GetProtocol(mbTrans748)
		containerStruct1 := mediainfo.NewUserPauseMediaAppUpdatesByMidsArgs()
		err752 := containerStruct1.ReadField2(jsProt751)
		if err752 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Mids
		value1 := argvalue1
		fmt.Print(client.UserPauseMediaAppUpdatesByMids(value0, value1))
		fmt.Print("\n")
		break
	case "getLatestMediaAppUpdate":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetLatestMediaAppUpdate requires 5 args")
			flag.Usage()
		}
		arg753 := flag.Arg(1)
		mbTrans754 := thrift.NewTMemoryBufferLen(len(arg753))
		defer mbTrans754.Close()
		_, err755 := mbTrans754.WriteString(arg753)
		if err755 != nil {
			Usage()
			return
		}
		factory756 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt757 := factory756.GetProtocol(mbTrans754)
		argvalue0 := mediainfo.NewRequestHeader()
		err758 := argvalue0.Read(jsProt757)
		if err758 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err759 := (strconv.Atoi(flag.Arg(2)))
		if err759 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err760 := (strconv.Atoi(flag.Arg(3)))
		if err760 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := mediainfo.SDKPlatform(tmp4)
		value4 := mediainfo.SDKPlatform(argvalue4)
		fmt.Print(client.GetLatestMediaAppUpdate(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getMediaDetectInfo":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetMediaDetectInfo requires 3 args")
			flag.Usage()
		}
		arg762 := flag.Arg(1)
		mbTrans763 := thrift.NewTMemoryBufferLen(len(arg762))
		defer mbTrans763.Close()
		_, err764 := mbTrans763.WriteString(arg762)
		if err764 != nil {
			Usage()
			return
		}
		factory765 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt766 := factory765.GetProtocol(mbTrans763)
		argvalue0 := mediainfo.NewRequestHeader()
		err767 := argvalue0.Read(jsProt766)
		if err767 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err768 := (strconv.Atoi(flag.Arg(2)))
		if err768 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.MediaIdInt(argvalue1)
		tmp2, err769 := (strconv.Atoi(flag.Arg(3)))
		if err769 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.PlacementIdInt(argvalue2)
		fmt.Print(client.GetMediaDetectInfo(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateMediaAppRate":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateMediaAppRate requires 2 args")
			flag.Usage()
		}
		arg770 := flag.Arg(1)
		mbTrans771 := thrift.NewTMemoryBufferLen(len(arg770))
		defer mbTrans771.Close()
		_, err772 := mbTrans771.WriteString(arg770)
		if err772 != nil {
			Usage()
			return
		}
		factory773 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt774 := factory773.GetProtocol(mbTrans771)
		argvalue0 := mediainfo.NewRequestHeader()
		err775 := argvalue0.Read(jsProt774)
		if err775 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg776 := flag.Arg(2)
		mbTrans777 := thrift.NewTMemoryBufferLen(len(arg776))
		defer mbTrans777.Close()
		_, err778 := mbTrans777.WriteString(arg776)
		if err778 != nil {
			Usage()
			return
		}
		factory779 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt780 := factory779.GetProtocol(mbTrans777)
		argvalue1 := mediainfo.NewMediaAppRate()
		err781 := argvalue1.Read(jsProt780)
		if err781 != nil {
			Usage()
			return
		}
		value1 := mediainfo.MediaAppRate(argvalue1)
		fmt.Print(client.UpdateMediaAppRate(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaAppRateByUidAndMid":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetMediaAppRateByUidAndMid requires 3 args")
			flag.Usage()
		}
		arg782 := flag.Arg(1)
		mbTrans783 := thrift.NewTMemoryBufferLen(len(arg782))
		defer mbTrans783.Close()
		_, err784 := mbTrans783.WriteString(arg782)
		if err784 != nil {
			Usage()
			return
		}
		factory785 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt786 := factory785.GetProtocol(mbTrans783)
		argvalue0 := mediainfo.NewRequestHeader()
		err787 := argvalue0.Read(jsProt786)
		if err787 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err788 := (strconv.Atoi(flag.Arg(2)))
		if err788 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err789 := (strconv.Atoi(flag.Arg(3)))
		if err789 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		fmt.Print(client.GetMediaAppRateByUidAndMid(value0, value1, value2))
		fmt.Print("\n")
		break
	case "userPauseMediaAppRateByMid":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UserPauseMediaAppRateByMid requires 3 args")
			flag.Usage()
		}
		arg790 := flag.Arg(1)
		mbTrans791 := thrift.NewTMemoryBufferLen(len(arg790))
		defer mbTrans791.Close()
		_, err792 := mbTrans791.WriteString(arg790)
		if err792 != nil {
			Usage()
			return
		}
		factory793 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt794 := factory793.GetProtocol(mbTrans791)
		argvalue0 := mediainfo.NewRequestHeader()
		err795 := argvalue0.Read(jsProt794)
		if err795 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err796 := (strconv.Atoi(flag.Arg(2)))
		if err796 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err797 := (strconv.Atoi(flag.Arg(3)))
		if err797 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		fmt.Print(client.UserPauseMediaAppRateByMid(value0, value1, value2))
		fmt.Print("\n")
		break
	case "userResumeMediaAppRateByMid":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UserResumeMediaAppRateByMid requires 3 args")
			flag.Usage()
		}
		arg798 := flag.Arg(1)
		mbTrans799 := thrift.NewTMemoryBufferLen(len(arg798))
		defer mbTrans799.Close()
		_, err800 := mbTrans799.WriteString(arg798)
		if err800 != nil {
			Usage()
			return
		}
		factory801 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt802 := factory801.GetProtocol(mbTrans799)
		argvalue0 := mediainfo.NewRequestHeader()
		err803 := argvalue0.Read(jsProt802)
		if err803 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err804 := (strconv.Atoi(flag.Arg(2)))
		if err804 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err805 := (strconv.Atoi(flag.Arg(3)))
		if err805 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		fmt.Print(client.UserResumeMediaAppRateByMid(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pausePlacementByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PausePlacementByIds requires 4 args")
			flag.Usage()
		}
		arg806 := flag.Arg(1)
		mbTrans807 := thrift.NewTMemoryBufferLen(len(arg806))
		defer mbTrans807.Close()
		_, err808 := mbTrans807.WriteString(arg806)
		if err808 != nil {
			Usage()
			return
		}
		factory809 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt810 := factory809.GetProtocol(mbTrans807)
		argvalue0 := mediainfo.NewRequestHeader()
		err811 := argvalue0.Read(jsProt810)
		if err811 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err812 := (strconv.Atoi(flag.Arg(2)))
		if err812 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err813 := (strconv.Atoi(flag.Arg(3)))
		if err813 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		arg814 := flag.Arg(4)
		mbTrans815 := thrift.NewTMemoryBufferLen(len(arg814))
		defer mbTrans815.Close()
		_, err816 := mbTrans815.WriteString(arg814)
		if err816 != nil {
			Usage()
			return
		}
		factory817 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt818 := factory817.GetProtocol(mbTrans815)
		containerStruct3 := mediainfo.NewPausePlacementByIdsArgs()
		err819 := containerStruct3.ReadField4(jsProt818)
		if err819 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.PausePlacementByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resumePlacementByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResumePlacementByIds requires 4 args")
			flag.Usage()
		}
		arg820 := flag.Arg(1)
		mbTrans821 := thrift.NewTMemoryBufferLen(len(arg820))
		defer mbTrans821.Close()
		_, err822 := mbTrans821.WriteString(arg820)
		if err822 != nil {
			Usage()
			return
		}
		factory823 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt824 := factory823.GetProtocol(mbTrans821)
		argvalue0 := mediainfo.NewRequestHeader()
		err825 := argvalue0.Read(jsProt824)
		if err825 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err826 := (strconv.Atoi(flag.Arg(2)))
		if err826 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err827 := (strconv.Atoi(flag.Arg(3)))
		if err827 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		arg828 := flag.Arg(4)
		mbTrans829 := thrift.NewTMemoryBufferLen(len(arg828))
		defer mbTrans829.Close()
		_, err830 := mbTrans829.WriteString(arg828)
		if err830 != nil {
			Usage()
			return
		}
		factory831 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt832 := factory831.GetProtocol(mbTrans829)
		containerStruct3 := mediainfo.NewResumePlacementByIdsArgs()
		err833 := containerStruct3.ReadField4(jsProt832)
		if err833 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.ResumePlacementByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deletePlacementByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeletePlacementByIds requires 4 args")
			flag.Usage()
		}
		arg834 := flag.Arg(1)
		mbTrans835 := thrift.NewTMemoryBufferLen(len(arg834))
		defer mbTrans835.Close()
		_, err836 := mbTrans835.WriteString(arg834)
		if err836 != nil {
			Usage()
			return
		}
		factory837 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt838 := factory837.GetProtocol(mbTrans835)
		argvalue0 := mediainfo.NewRequestHeader()
		err839 := argvalue0.Read(jsProt838)
		if err839 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err840 := (strconv.Atoi(flag.Arg(2)))
		if err840 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err841 := (strconv.Atoi(flag.Arg(3)))
		if err841 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		arg842 := flag.Arg(4)
		mbTrans843 := thrift.NewTMemoryBufferLen(len(arg842))
		defer mbTrans843.Close()
		_, err844 := mbTrans843.WriteString(arg842)
		if err844 != nil {
			Usage()
			return
		}
		factory845 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt846 := factory845.GetProtocol(mbTrans843)
		containerStruct3 := mediainfo.NewDeletePlacementByIdsArgs()
		err847 := containerStruct3.ReadField4(jsProt846)
		if err847 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.DeletePlacementByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listPlacementByMid":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListPlacementByMid requires 6 args")
			flag.Usage()
		}
		arg848 := flag.Arg(1)
		mbTrans849 := thrift.NewTMemoryBufferLen(len(arg848))
		defer mbTrans849.Close()
		_, err850 := mbTrans849.WriteString(arg848)
		if err850 != nil {
			Usage()
			return
		}
		factory851 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt852 := factory851.GetProtocol(mbTrans849)
		argvalue0 := mediainfo.NewRequestHeader()
		err853 := argvalue0.Read(jsProt852)
		if err853 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err854 := (strconv.Atoi(flag.Arg(2)))
		if err854 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.MediaIdInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err856 := (strconv.Atoi(flag.Arg(4)))
		if err856 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		tmp4, err857 := (strconv.Atoi(flag.Arg(5)))
		if err857 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListPlacementByMid(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getPlacementByUid":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetPlacementByUid requires 3 args")
			flag.Usage()
		}
		arg859 := flag.Arg(1)
		mbTrans860 := thrift.NewTMemoryBufferLen(len(arg859))
		defer mbTrans860.Close()
		_, err861 := mbTrans860.WriteString(arg859)
		if err861 != nil {
			Usage()
			return
		}
		factory862 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt863 := factory862.GetProtocol(mbTrans860)
		argvalue0 := mediainfo.NewRequestHeader()
		err864 := argvalue0.Read(jsProt863)
		if err864 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err865 := (strconv.Atoi(flag.Arg(2)))
		if err865 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetPlacementByUid(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getPlacementByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetPlacementByIds requires 3 args")
			flag.Usage()
		}
		arg867 := flag.Arg(1)
		mbTrans868 := thrift.NewTMemoryBufferLen(len(arg867))
		defer mbTrans868.Close()
		_, err869 := mbTrans868.WriteString(arg867)
		if err869 != nil {
			Usage()
			return
		}
		factory870 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt871 := factory870.GetProtocol(mbTrans868)
		argvalue0 := mediainfo.NewRequestHeader()
		err872 := argvalue0.Read(jsProt871)
		if err872 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err873 := (strconv.Atoi(flag.Arg(2)))
		if err873 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		arg874 := flag.Arg(3)
		mbTrans875 := thrift.NewTMemoryBufferLen(len(arg874))
		defer mbTrans875.Close()
		_, err876 := mbTrans875.WriteString(arg874)
		if err876 != nil {
			Usage()
			return
		}
		factory877 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt878 := factory877.GetProtocol(mbTrans875)
		containerStruct2 := mediainfo.NewGetPlacementByIdsArgs()
		err879 := containerStruct2.ReadField3(jsProt878)
		if err879 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetPlacementByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getMediaOrderById":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetMediaOrderById requires 4 args")
			flag.Usage()
		}
		arg880 := flag.Arg(1)
		mbTrans881 := thrift.NewTMemoryBufferLen(len(arg880))
		defer mbTrans881.Close()
		_, err882 := mbTrans881.WriteString(arg880)
		if err882 != nil {
			Usage()
			return
		}
		factory883 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt884 := factory883.GetProtocol(mbTrans881)
		argvalue0 := mediainfo.NewRequestHeader()
		err885 := argvalue0.Read(jsProt884)
		if err885 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		tmp2, err887 := (strconv.Atoi(flag.Arg(3)))
		if err887 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaQueryInt(argvalue2)
		tmp3, err888 := (strconv.Atoi(flag.Arg(4)))
		if err888 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		fmt.Print(client.GetMediaOrderById(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getPlacementsOrderById":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetPlacementsOrderById requires 4 args")
			flag.Usage()
		}
		arg889 := flag.Arg(1)
		mbTrans890 := thrift.NewTMemoryBufferLen(len(arg889))
		defer mbTrans890.Close()
		_, err891 := mbTrans890.WriteString(arg889)
		if err891 != nil {
			Usage()
			return
		}
		factory892 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt893 := factory892.GetProtocol(mbTrans890)
		argvalue0 := mediainfo.NewRequestHeader()
		err894 := argvalue0.Read(jsProt893)
		if err894 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		tmp2, err896 := (strconv.Atoi(flag.Arg(3)))
		if err896 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaQueryInt(argvalue2)
		tmp3, err897 := (strconv.Atoi(flag.Arg(4)))
		if err897 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.MediaQueryInt(argvalue3)
		fmt.Print(client.GetPlacementsOrderById(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "editPlacementSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPlacementSetting requires 2 args")
			flag.Usage()
		}
		arg898 := flag.Arg(1)
		mbTrans899 := thrift.NewTMemoryBufferLen(len(arg898))
		defer mbTrans899.Close()
		_, err900 := mbTrans899.WriteString(arg898)
		if err900 != nil {
			Usage()
			return
		}
		factory901 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt902 := factory901.GetProtocol(mbTrans899)
		argvalue0 := mediainfo.NewRequestHeader()
		err903 := argvalue0.Read(jsProt902)
		if err903 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg904 := flag.Arg(2)
		mbTrans905 := thrift.NewTMemoryBufferLen(len(arg904))
		defer mbTrans905.Close()
		_, err906 := mbTrans905.WriteString(arg904)
		if err906 != nil {
			Usage()
			return
		}
		factory907 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt908 := factory907.GetProtocol(mbTrans905)
		argvalue1 := mediainfo.NewPlacementSetting()
		err909 := argvalue1.Read(jsProt908)
		if err909 != nil {
			Usage()
			return
		}
		value1 := mediainfo.PlacementSetting(argvalue1)
		fmt.Print(client.EditPlacementSetting(value0, value1))
		fmt.Print("\n")
		break
	case "editPlacementSettingExact":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPlacementSettingExact requires 2 args")
			flag.Usage()
		}
		arg910 := flag.Arg(1)
		mbTrans911 := thrift.NewTMemoryBufferLen(len(arg910))
		defer mbTrans911.Close()
		_, err912 := mbTrans911.WriteString(arg910)
		if err912 != nil {
			Usage()
			return
		}
		factory913 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt914 := factory913.GetProtocol(mbTrans911)
		argvalue0 := mediainfo.NewRequestHeader()
		err915 := argvalue0.Read(jsProt914)
		if err915 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg916 := flag.Arg(2)
		mbTrans917 := thrift.NewTMemoryBufferLen(len(arg916))
		defer mbTrans917.Close()
		_, err918 := mbTrans917.WriteString(arg916)
		if err918 != nil {
			Usage()
			return
		}
		factory919 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt920 := factory919.GetProtocol(mbTrans917)
		argvalue1 := mediainfo.NewPlacementSetting()
		err921 := argvalue1.Read(jsProt920)
		if err921 != nil {
			Usage()
			return
		}
		value1 := mediainfo.PlacementSetting(argvalue1)
		fmt.Print(client.EditPlacementSettingExact(value0, value1))
		fmt.Print("\n")
		break
	case "editPremiumAdSequence":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "EditPremiumAdSequence requires 5 args")
			flag.Usage()
		}
		arg922 := flag.Arg(1)
		mbTrans923 := thrift.NewTMemoryBufferLen(len(arg922))
		defer mbTrans923.Close()
		_, err924 := mbTrans923.WriteString(arg922)
		if err924 != nil {
			Usage()
			return
		}
		factory925 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt926 := factory925.GetProtocol(mbTrans923)
		argvalue0 := mediainfo.NewRequestHeader()
		err927 := argvalue0.Read(jsProt926)
		if err927 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err928 := (strconv.Atoi(flag.Arg(2)))
		if err928 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err929 := (strconv.Atoi(flag.Arg(3)))
		if err929 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		tmp3, err930 := (strconv.Atoi(flag.Arg(4)))
		if err930 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.PlacementIdInt(argvalue3)
		arg931 := flag.Arg(5)
		mbTrans932 := thrift.NewTMemoryBufferLen(len(arg931))
		defer mbTrans932.Close()
		_, err933 := mbTrans932.WriteString(arg931)
		if err933 != nil {
			Usage()
			return
		}
		factory934 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt935 := factory934.GetProtocol(mbTrans932)
		containerStruct4 := mediainfo.NewEditPremiumAdSequenceArgs()
		err936 := containerStruct4.ReadField5(jsProt935)
		if err936 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Sequence
		value4 := argvalue4
		fmt.Print(client.EditPremiumAdSequence(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "attachToHouseAd":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "AttachToHouseAd requires 7 args")
			flag.Usage()
		}
		arg937 := flag.Arg(1)
		mbTrans938 := thrift.NewTMemoryBufferLen(len(arg937))
		defer mbTrans938.Close()
		_, err939 := mbTrans938.WriteString(arg937)
		if err939 != nil {
			Usage()
			return
		}
		factory940 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt941 := factory940.GetProtocol(mbTrans938)
		argvalue0 := mediainfo.NewRequestHeader()
		err942 := argvalue0.Read(jsProt941)
		if err942 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err943 := (strconv.Atoi(flag.Arg(2)))
		if err943 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err944 := (strconv.Atoi(flag.Arg(3)))
		if err944 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		tmp3, err945 := (strconv.Atoi(flag.Arg(4)))
		if err945 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.PlacementIdInt(argvalue3)
		tmp4, err946 := (strconv.Atoi(flag.Arg(5)))
		if err946 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.AdCreativeIdInt(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := mediainfo.HouseAdPriorityType(tmp5)
		value5 := mediainfo.HouseAdPriorityType(argvalue5)
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.AttachToHouseAd(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "detachFromHouseAd":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "DetachFromHouseAd requires 6 args")
			flag.Usage()
		}
		arg948 := flag.Arg(1)
		mbTrans949 := thrift.NewTMemoryBufferLen(len(arg948))
		defer mbTrans949.Close()
		_, err950 := mbTrans949.WriteString(arg948)
		if err950 != nil {
			Usage()
			return
		}
		factory951 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt952 := factory951.GetProtocol(mbTrans949)
		argvalue0 := mediainfo.NewRequestHeader()
		err953 := argvalue0.Read(jsProt952)
		if err953 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err954 := (strconv.Atoi(flag.Arg(2)))
		if err954 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		tmp2, err955 := (strconv.Atoi(flag.Arg(3)))
		if err955 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.MediaIdInt(argvalue2)
		tmp3, err956 := (strconv.Atoi(flag.Arg(4)))
		if err956 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.PlacementIdInt(argvalue3)
		tmp4, err957 := (strconv.Atoi(flag.Arg(5)))
		if err957 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.AdCreativeIdInt(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := mediainfo.HouseAdPriorityType(tmp5)
		value5 := mediainfo.HouseAdPriorityType(argvalue5)
		fmt.Print(client.DetachFromHouseAd(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getAttachedPmidsByHouseAdCid":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAttachedPmidsByHouseAdCid requires 3 args")
			flag.Usage()
		}
		arg958 := flag.Arg(1)
		mbTrans959 := thrift.NewTMemoryBufferLen(len(arg958))
		defer mbTrans959.Close()
		_, err960 := mbTrans959.WriteString(arg958)
		if err960 != nil {
			Usage()
			return
		}
		factory961 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt962 := factory961.GetProtocol(mbTrans959)
		argvalue0 := mediainfo.NewRequestHeader()
		err963 := argvalue0.Read(jsProt962)
		if err963 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err964 := (strconv.Atoi(flag.Arg(2)))
		if err964 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.AdCreativeIdInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := mediainfo.HouseAdPriorityType(tmp2)
		value2 := mediainfo.HouseAdPriorityType(argvalue2)
		fmt.Print(client.GetAttachedPmidsByHouseAdCid(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addPlacement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPlacement requires 2 args")
			flag.Usage()
		}
		arg965 := flag.Arg(1)
		mbTrans966 := thrift.NewTMemoryBufferLen(len(arg965))
		defer mbTrans966.Close()
		_, err967 := mbTrans966.WriteString(arg965)
		if err967 != nil {
			Usage()
			return
		}
		factory968 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt969 := factory968.GetProtocol(mbTrans966)
		argvalue0 := mediainfo.NewRequestHeader()
		err970 := argvalue0.Read(jsProt969)
		if err970 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg971 := flag.Arg(2)
		mbTrans972 := thrift.NewTMemoryBufferLen(len(arg971))
		defer mbTrans972.Close()
		_, err973 := mbTrans972.WriteString(arg971)
		if err973 != nil {
			Usage()
			return
		}
		factory974 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt975 := factory974.GetProtocol(mbTrans972)
		argvalue1 := mediainfo.NewPlacement()
		err976 := argvalue1.Read(jsProt975)
		if err976 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Placement(argvalue1)
		fmt.Print(client.AddPlacement(value0, value1))
		fmt.Print("\n")
		break
	case "listMediaByUidAndNameFuzzyQuery":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListMediaByUidAndNameFuzzyQuery requires 7 args")
			flag.Usage()
		}
		arg977 := flag.Arg(1)
		mbTrans978 := thrift.NewTMemoryBufferLen(len(arg977))
		defer mbTrans978.Close()
		_, err979 := mbTrans978.WriteString(arg977)
		if err979 != nil {
			Usage()
			return
		}
		factory980 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt981 := factory980.GetProtocol(mbTrans978)
		argvalue0 := mediainfo.NewRequestHeader()
		err982 := argvalue0.Read(jsProt981)
		if err982 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err983 := (strconv.Atoi(flag.Arg(2)))
		if err983 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		tmp4, err986 := (strconv.Atoi(flag.Arg(5)))
		if err986 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := mediainfo.MediaQueryInt(argvalue4)
		tmp5, err987 := (strconv.Atoi(flag.Arg(6)))
		if err987 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := mediainfo.MediaQueryInt(argvalue5)
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.ListMediaByUidAndNameFuzzyQuery(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listMediaByUidAndTypeAndPauseAndStatus":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "ListMediaByUidAndTypeAndPauseAndStatus requires 9 args")
			flag.Usage()
		}
		arg989 := flag.Arg(1)
		mbTrans990 := thrift.NewTMemoryBufferLen(len(arg989))
		defer mbTrans990.Close()
		_, err991 := mbTrans990.WriteString(arg989)
		if err991 != nil {
			Usage()
			return
		}
		factory992 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt993 := factory992.GetProtocol(mbTrans990)
		argvalue0 := mediainfo.NewRequestHeader()
		err994 := argvalue0.Read(jsProt993)
		if err994 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err995 := (strconv.Atoi(flag.Arg(2)))
		if err995 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.UidInt(argvalue1)
		arg996 := flag.Arg(3)
		mbTrans997 := thrift.NewTMemoryBufferLen(len(arg996))
		defer mbTrans997.Close()
		_, err998 := mbTrans997.WriteString(arg996)
		if err998 != nil {
			Usage()
			return
		}
		factory999 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1000 := factory999.GetProtocol(mbTrans997)
		containerStruct2 := mediainfo.NewListMediaByUidAndTypeAndPauseAndStatusArgs()
		err1001 := containerStruct2.ReadField3(jsProt1000)
		if err1001 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Type
		value2 := argvalue2
		arg1002 := flag.Arg(4)
		mbTrans1003 := thrift.NewTMemoryBufferLen(len(arg1002))
		defer mbTrans1003.Close()
		_, err1004 := mbTrans1003.WriteString(arg1002)
		if err1004 != nil {
			Usage()
			return
		}
		factory1005 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1006 := factory1005.GetProtocol(mbTrans1003)
		containerStruct3 := mediainfo.NewListMediaByUidAndTypeAndPauseAndStatusArgs()
		err1007 := containerStruct3.ReadField4(jsProt1006)
		if err1007 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Paused
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		arg1009 := flag.Arg(6)
		mbTrans1010 := thrift.NewTMemoryBufferLen(len(arg1009))
		defer mbTrans1010.Close()
		_, err1011 := mbTrans1010.WriteString(arg1009)
		if err1011 != nil {
			Usage()
			return
		}
		factory1012 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1013 := factory1012.GetProtocol(mbTrans1010)
		containerStruct5 := mediainfo.NewListMediaByUidAndTypeAndPauseAndStatusArgs()
		err1014 := containerStruct5.ReadField6(jsProt1013)
		if err1014 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Status
		value5 := argvalue5
		tmp6, err1015 := (strconv.Atoi(flag.Arg(7)))
		if err1015 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := mediainfo.MediaQueryInt(argvalue6)
		tmp7, err1016 := (strconv.Atoi(flag.Arg(8)))
		if err1016 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := mediainfo.MediaQueryInt(argvalue7)
		argvalue8 := flag.Arg(9) == "true"
		value8 := argvalue8
		fmt.Print(client.ListMediaByUidAndTypeAndPauseAndStatus(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "addContainer":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "AddContainer requires 4 args")
			flag.Usage()
		}
		arg1018 := flag.Arg(1)
		mbTrans1019 := thrift.NewTMemoryBufferLen(len(arg1018))
		defer mbTrans1019.Close()
		_, err1020 := mbTrans1019.WriteString(arg1018)
		if err1020 != nil {
			Usage()
			return
		}
		factory1021 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1022 := factory1021.GetProtocol(mbTrans1019)
		argvalue0 := mediainfo.NewRequestHeader()
		err1023 := argvalue0.Read(jsProt1022)
		if err1023 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1024 := (strconv.Atoi(flag.Arg(2)))
		if err1024 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.IdInt(argvalue1)
		tmp2, err1025 := (strconv.Atoi(flag.Arg(3)))
		if err1025 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.IdInt(argvalue2)
		arg1026 := flag.Arg(4)
		mbTrans1027 := thrift.NewTMemoryBufferLen(len(arg1026))
		defer mbTrans1027.Close()
		_, err1028 := mbTrans1027.WriteString(arg1026)
		if err1028 != nil {
			Usage()
			return
		}
		factory1029 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1030 := factory1029.GetProtocol(mbTrans1027)
		argvalue3 := mediainfo.NewContainer()
		err1031 := argvalue3.Read(jsProt1030)
		if err1031 != nil {
			Usage()
			return
		}
		value3 := mediainfo.Container(argvalue3)
		fmt.Print(client.AddContainer(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "editContainer":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "EditContainer requires 4 args")
			flag.Usage()
		}
		arg1032 := flag.Arg(1)
		mbTrans1033 := thrift.NewTMemoryBufferLen(len(arg1032))
		defer mbTrans1033.Close()
		_, err1034 := mbTrans1033.WriteString(arg1032)
		if err1034 != nil {
			Usage()
			return
		}
		factory1035 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1036 := factory1035.GetProtocol(mbTrans1033)
		argvalue0 := mediainfo.NewRequestHeader()
		err1037 := argvalue0.Read(jsProt1036)
		if err1037 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1038 := (strconv.Atoi(flag.Arg(2)))
		if err1038 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.IdInt(argvalue1)
		tmp2, err1039 := (strconv.Atoi(flag.Arg(3)))
		if err1039 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.IdInt(argvalue2)
		arg1040 := flag.Arg(4)
		mbTrans1041 := thrift.NewTMemoryBufferLen(len(arg1040))
		defer mbTrans1041.Close()
		_, err1042 := mbTrans1041.WriteString(arg1040)
		if err1042 != nil {
			Usage()
			return
		}
		factory1043 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1044 := factory1043.GetProtocol(mbTrans1041)
		argvalue3 := mediainfo.NewContainer()
		err1045 := argvalue3.Read(jsProt1044)
		if err1045 != nil {
			Usage()
			return
		}
		value3 := mediainfo.Container(argvalue3)
		fmt.Print(client.EditContainer(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getContainersByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetContainersByIdList requires 2 args")
			flag.Usage()
		}
		arg1046 := flag.Arg(1)
		mbTrans1047 := thrift.NewTMemoryBufferLen(len(arg1046))
		defer mbTrans1047.Close()
		_, err1048 := mbTrans1047.WriteString(arg1046)
		if err1048 != nil {
			Usage()
			return
		}
		factory1049 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1050 := factory1049.GetProtocol(mbTrans1047)
		argvalue0 := mediainfo.NewRequestHeader()
		err1051 := argvalue0.Read(jsProt1050)
		if err1051 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg1052 := flag.Arg(2)
		mbTrans1053 := thrift.NewTMemoryBufferLen(len(arg1052))
		defer mbTrans1053.Close()
		_, err1054 := mbTrans1053.WriteString(arg1052)
		if err1054 != nil {
			Usage()
			return
		}
		factory1055 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1056 := factory1055.GetProtocol(mbTrans1053)
		containerStruct1 := mediainfo.NewGetContainersByIdListArgs()
		err1057 := containerStruct1.ReadField2(jsProt1056)
		if err1057 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		fmt.Print(client.GetContainersByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "getContainersByPmid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetContainersByPmid requires 2 args")
			flag.Usage()
		}
		arg1058 := flag.Arg(1)
		mbTrans1059 := thrift.NewTMemoryBufferLen(len(arg1058))
		defer mbTrans1059.Close()
		_, err1060 := mbTrans1059.WriteString(arg1058)
		if err1060 != nil {
			Usage()
			return
		}
		factory1061 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1062 := factory1061.GetProtocol(mbTrans1059)
		argvalue0 := mediainfo.NewRequestHeader()
		err1063 := argvalue0.Read(jsProt1062)
		if err1063 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1064 := (strconv.Atoi(flag.Arg(2)))
		if err1064 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.IdInt(argvalue1)
		fmt.Print(client.GetContainersByPmid(value0, value1))
		fmt.Print("\n")
		break
	case "disableContainersByIdList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DisableContainersByIdList requires 5 args")
			flag.Usage()
		}
		arg1065 := flag.Arg(1)
		mbTrans1066 := thrift.NewTMemoryBufferLen(len(arg1065))
		defer mbTrans1066.Close()
		_, err1067 := mbTrans1066.WriteString(arg1065)
		if err1067 != nil {
			Usage()
			return
		}
		factory1068 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1069 := factory1068.GetProtocol(mbTrans1066)
		argvalue0 := mediainfo.NewRequestHeader()
		err1070 := argvalue0.Read(jsProt1069)
		if err1070 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1071 := (strconv.Atoi(flag.Arg(2)))
		if err1071 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.IdInt(argvalue1)
		tmp2, err1072 := (strconv.Atoi(flag.Arg(3)))
		if err1072 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.IdInt(argvalue2)
		tmp3, err1073 := (strconv.Atoi(flag.Arg(4)))
		if err1073 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.IdInt(argvalue3)
		arg1074 := flag.Arg(5)
		mbTrans1075 := thrift.NewTMemoryBufferLen(len(arg1074))
		defer mbTrans1075.Close()
		_, err1076 := mbTrans1075.WriteString(arg1074)
		if err1076 != nil {
			Usage()
			return
		}
		factory1077 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1078 := factory1077.GetProtocol(mbTrans1075)
		containerStruct4 := mediainfo.NewDisableContainersByIdListArgs()
		err1079 := containerStruct4.ReadField5(jsProt1078)
		if err1079 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.IdList
		value4 := argvalue4
		fmt.Print(client.DisableContainersByIdList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "enableContainersByIdList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "EnableContainersByIdList requires 5 args")
			flag.Usage()
		}
		arg1080 := flag.Arg(1)
		mbTrans1081 := thrift.NewTMemoryBufferLen(len(arg1080))
		defer mbTrans1081.Close()
		_, err1082 := mbTrans1081.WriteString(arg1080)
		if err1082 != nil {
			Usage()
			return
		}
		factory1083 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1084 := factory1083.GetProtocol(mbTrans1081)
		argvalue0 := mediainfo.NewRequestHeader()
		err1085 := argvalue0.Read(jsProt1084)
		if err1085 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1086 := (strconv.Atoi(flag.Arg(2)))
		if err1086 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.IdInt(argvalue1)
		tmp2, err1087 := (strconv.Atoi(flag.Arg(3)))
		if err1087 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.IdInt(argvalue2)
		tmp3, err1088 := (strconv.Atoi(flag.Arg(4)))
		if err1088 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.IdInt(argvalue3)
		arg1089 := flag.Arg(5)
		mbTrans1090 := thrift.NewTMemoryBufferLen(len(arg1089))
		defer mbTrans1090.Close()
		_, err1091 := mbTrans1090.WriteString(arg1089)
		if err1091 != nil {
			Usage()
			return
		}
		factory1092 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1093 := factory1092.GetProtocol(mbTrans1090)
		containerStruct4 := mediainfo.NewEnableContainersByIdListArgs()
		err1094 := containerStruct4.ReadField5(jsProt1093)
		if err1094 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.IdList
		value4 := argvalue4
		fmt.Print(client.EnableContainersByIdList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteContainersByIdList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteContainersByIdList requires 5 args")
			flag.Usage()
		}
		arg1095 := flag.Arg(1)
		mbTrans1096 := thrift.NewTMemoryBufferLen(len(arg1095))
		defer mbTrans1096.Close()
		_, err1097 := mbTrans1096.WriteString(arg1095)
		if err1097 != nil {
			Usage()
			return
		}
		factory1098 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1099 := factory1098.GetProtocol(mbTrans1096)
		argvalue0 := mediainfo.NewRequestHeader()
		err1100 := argvalue0.Read(jsProt1099)
		if err1100 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1101 := (strconv.Atoi(flag.Arg(2)))
		if err1101 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.IdInt(argvalue1)
		tmp2, err1102 := (strconv.Atoi(flag.Arg(3)))
		if err1102 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := mediainfo.IdInt(argvalue2)
		tmp3, err1103 := (strconv.Atoi(flag.Arg(4)))
		if err1103 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := mediainfo.IdInt(argvalue3)
		arg1104 := flag.Arg(5)
		mbTrans1105 := thrift.NewTMemoryBufferLen(len(arg1104))
		defer mbTrans1105.Close()
		_, err1106 := mbTrans1105.WriteString(arg1104)
		if err1106 != nil {
			Usage()
			return
		}
		factory1107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1108 := factory1107.GetProtocol(mbTrans1105)
		containerStruct4 := mediainfo.NewDeleteContainersByIdListArgs()
		err1109 := containerStruct4.ReadField5(jsProt1108)
		if err1109 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.IdList
		value4 := argvalue4
		fmt.Print(client.DeleteContainersByIdList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddChannel requires 2 args")
			flag.Usage()
		}
		arg1110 := flag.Arg(1)
		mbTrans1111 := thrift.NewTMemoryBufferLen(len(arg1110))
		defer mbTrans1111.Close()
		_, err1112 := mbTrans1111.WriteString(arg1110)
		if err1112 != nil {
			Usage()
			return
		}
		factory1113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1114 := factory1113.GetProtocol(mbTrans1111)
		argvalue0 := mediainfo.NewRequestHeader()
		err1115 := argvalue0.Read(jsProt1114)
		if err1115 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg1116 := flag.Arg(2)
		mbTrans1117 := thrift.NewTMemoryBufferLen(len(arg1116))
		defer mbTrans1117.Close()
		_, err1118 := mbTrans1117.WriteString(arg1116)
		if err1118 != nil {
			Usage()
			return
		}
		factory1119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1120 := factory1119.GetProtocol(mbTrans1117)
		argvalue1 := mediainfo.NewChannel()
		err1121 := argvalue1.Read(jsProt1120)
		if err1121 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Channel(argvalue1)
		fmt.Print(client.AddChannel(value0, value1))
		fmt.Print("\n")
		break
	case "getChannelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetChannelsByIds requires 2 args")
			flag.Usage()
		}
		arg1122 := flag.Arg(1)
		mbTrans1123 := thrift.NewTMemoryBufferLen(len(arg1122))
		defer mbTrans1123.Close()
		_, err1124 := mbTrans1123.WriteString(arg1122)
		if err1124 != nil {
			Usage()
			return
		}
		factory1125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1126 := factory1125.GetProtocol(mbTrans1123)
		argvalue0 := mediainfo.NewRequestHeader()
		err1127 := argvalue0.Read(jsProt1126)
		if err1127 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg1128 := flag.Arg(2)
		mbTrans1129 := thrift.NewTMemoryBufferLen(len(arg1128))
		defer mbTrans1129.Close()
		_, err1130 := mbTrans1129.WriteString(arg1128)
		if err1130 != nil {
			Usage()
			return
		}
		factory1131 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1132 := factory1131.GetProtocol(mbTrans1129)
		containerStruct1 := mediainfo.NewGetChannelsByIdsArgs()
		err1133 := containerStruct1.ReadField2(jsProt1132)
		if err1133 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ChnIds
		value1 := argvalue1
		fmt.Print(client.GetChannelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "editChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditChannel requires 2 args")
			flag.Usage()
		}
		arg1134 := flag.Arg(1)
		mbTrans1135 := thrift.NewTMemoryBufferLen(len(arg1134))
		defer mbTrans1135.Close()
		_, err1136 := mbTrans1135.WriteString(arg1134)
		if err1136 != nil {
			Usage()
			return
		}
		factory1137 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1138 := factory1137.GetProtocol(mbTrans1135)
		argvalue0 := mediainfo.NewRequestHeader()
		err1139 := argvalue0.Read(jsProt1138)
		if err1139 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg1140 := flag.Arg(2)
		mbTrans1141 := thrift.NewTMemoryBufferLen(len(arg1140))
		defer mbTrans1141.Close()
		_, err1142 := mbTrans1141.WriteString(arg1140)
		if err1142 != nil {
			Usage()
			return
		}
		factory1143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1144 := factory1143.GetProtocol(mbTrans1141)
		argvalue1 := mediainfo.NewChannel()
		err1145 := argvalue1.Read(jsProt1144)
		if err1145 != nil {
			Usage()
			return
		}
		value1 := mediainfo.Channel(argvalue1)
		fmt.Print(client.EditChannel(value0, value1))
		fmt.Print("\n")
		break
	case "getChannelsByPmid":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetChannelsByPmid requires 2 args")
			flag.Usage()
		}
		arg1146 := flag.Arg(1)
		mbTrans1147 := thrift.NewTMemoryBufferLen(len(arg1146))
		defer mbTrans1147.Close()
		_, err1148 := mbTrans1147.WriteString(arg1146)
		if err1148 != nil {
			Usage()
			return
		}
		factory1149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1150 := factory1149.GetProtocol(mbTrans1147)
		argvalue0 := mediainfo.NewRequestHeader()
		err1151 := argvalue0.Read(jsProt1150)
		if err1151 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		tmp1, err1152 := (strconv.Atoi(flag.Arg(2)))
		if err1152 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := mediainfo.PlacementIdInt(argvalue1)
		fmt.Print(client.GetChannelsByPmid(value0, value1))
		fmt.Print("\n")
		break
	case "disableChannelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DisableChannelsByIds requires 2 args")
			flag.Usage()
		}
		arg1153 := flag.Arg(1)
		mbTrans1154 := thrift.NewTMemoryBufferLen(len(arg1153))
		defer mbTrans1154.Close()
		_, err1155 := mbTrans1154.WriteString(arg1153)
		if err1155 != nil {
			Usage()
			return
		}
		factory1156 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1157 := factory1156.GetProtocol(mbTrans1154)
		argvalue0 := mediainfo.NewRequestHeader()
		err1158 := argvalue0.Read(jsProt1157)
		if err1158 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg1159 := flag.Arg(2)
		mbTrans1160 := thrift.NewTMemoryBufferLen(len(arg1159))
		defer mbTrans1160.Close()
		_, err1161 := mbTrans1160.WriteString(arg1159)
		if err1161 != nil {
			Usage()
			return
		}
		factory1162 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1163 := factory1162.GetProtocol(mbTrans1160)
		containerStruct1 := mediainfo.NewDisableChannelsByIdsArgs()
		err1164 := containerStruct1.ReadField2(jsProt1163)
		if err1164 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ChnIds
		value1 := argvalue1
		fmt.Print(client.DisableChannelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "enableChannelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EnableChannelsByIds requires 2 args")
			flag.Usage()
		}
		arg1165 := flag.Arg(1)
		mbTrans1166 := thrift.NewTMemoryBufferLen(len(arg1165))
		defer mbTrans1166.Close()
		_, err1167 := mbTrans1166.WriteString(arg1165)
		if err1167 != nil {
			Usage()
			return
		}
		factory1168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1169 := factory1168.GetProtocol(mbTrans1166)
		argvalue0 := mediainfo.NewRequestHeader()
		err1170 := argvalue0.Read(jsProt1169)
		if err1170 != nil {
			Usage()
			return
		}
		value0 := mediainfo.RequestHeader(argvalue0)
		arg1171 := flag.Arg(2)
		mbTrans1172 := thrift.NewTMemoryBufferLen(len(arg1171))
		defer mbTrans1172.Close()
		_, err1173 := mbTrans1172.WriteString(arg1171)
		if err1173 != nil {
			Usage()
			return
		}
		factory1174 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1175 := factory1174.GetProtocol(mbTrans1172)
		containerStruct1 := mediainfo.NewEnableChannelsByIdsArgs()
		err1176 := containerStruct1.ReadField2(jsProt1175)
		if err1176 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ChnIds
		value1 := argvalue1
		fmt.Print(client.EnableChannelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
