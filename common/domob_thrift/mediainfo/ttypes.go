// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mediainfo

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/mediainfo_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = mediainfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

//MediaInfoException中可能出现的异常代码
type MediaInfoServiceCode int64

const (
	MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_EXIST         MediaInfoServiceCode = 22301
	MediaInfoServiceCode_ERROR_MEDIA_USER_DEVER_ROLE_BANNED MediaInfoServiceCode = 22302
	MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_MATCH         MediaInfoServiceCode = 22303
	MediaInfoServiceCode_ERROR_MEDIA_NOT_EXIST              MediaInfoServiceCode = 22304
	MediaInfoServiceCode_ERROR_MEDIA_PARAM_INVALID          MediaInfoServiceCode = 22401
	MediaInfoServiceCode_ERROR_PN_NOT_MATCH                 MediaInfoServiceCode = 22402
	MediaInfoServiceCode_ERROR_PN_INVALID                   MediaInfoServiceCode = 22403
	MediaInfoServiceCode_ERROR_VC_TOO_LOW                   MediaInfoServiceCode = 22404
	MediaInfoServiceCode_ERROR_VC_INVALID                   MediaInfoServiceCode = 22405
	MediaInfoServiceCode_ERROR_VN_TOO_LOW                   MediaInfoServiceCode = 22406
	MediaInfoServiceCode_ERROR_VN_INVALID                   MediaInfoServiceCode = 22407
	MediaInfoServiceCode_ERROR_UPDATE_LOG_INVALID           MediaInfoServiceCode = 22408
	MediaInfoServiceCode_ERROR_MEDIA_SYSTEM_ERROR           MediaInfoServiceCode = 22501
)

func (p MediaInfoServiceCode) String() string {
	switch p {
	case MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_EXIST:
		return "MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_EXIST"
	case MediaInfoServiceCode_ERROR_MEDIA_USER_DEVER_ROLE_BANNED:
		return "MediaInfoServiceCode_ERROR_MEDIA_USER_DEVER_ROLE_BANNED"
	case MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_MATCH:
		return "MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_MATCH"
	case MediaInfoServiceCode_ERROR_MEDIA_NOT_EXIST:
		return "MediaInfoServiceCode_ERROR_MEDIA_NOT_EXIST"
	case MediaInfoServiceCode_ERROR_MEDIA_PARAM_INVALID:
		return "MediaInfoServiceCode_ERROR_MEDIA_PARAM_INVALID"
	case MediaInfoServiceCode_ERROR_PN_NOT_MATCH:
		return "MediaInfoServiceCode_ERROR_PN_NOT_MATCH"
	case MediaInfoServiceCode_ERROR_PN_INVALID:
		return "MediaInfoServiceCode_ERROR_PN_INVALID"
	case MediaInfoServiceCode_ERROR_VC_TOO_LOW:
		return "MediaInfoServiceCode_ERROR_VC_TOO_LOW"
	case MediaInfoServiceCode_ERROR_VC_INVALID:
		return "MediaInfoServiceCode_ERROR_VC_INVALID"
	case MediaInfoServiceCode_ERROR_VN_TOO_LOW:
		return "MediaInfoServiceCode_ERROR_VN_TOO_LOW"
	case MediaInfoServiceCode_ERROR_VN_INVALID:
		return "MediaInfoServiceCode_ERROR_VN_INVALID"
	case MediaInfoServiceCode_ERROR_UPDATE_LOG_INVALID:
		return "MediaInfoServiceCode_ERROR_UPDATE_LOG_INVALID"
	case MediaInfoServiceCode_ERROR_MEDIA_SYSTEM_ERROR:
		return "MediaInfoServiceCode_ERROR_MEDIA_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func MediaInfoServiceCodeFromString(s string) (MediaInfoServiceCode, error) {
	switch s {
	case "MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_EXIST":
		return MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_EXIST, nil
	case "MediaInfoServiceCode_ERROR_MEDIA_USER_DEVER_ROLE_BANNED":
		return MediaInfoServiceCode_ERROR_MEDIA_USER_DEVER_ROLE_BANNED, nil
	case "MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_MATCH":
		return MediaInfoServiceCode_ERROR_MEDIA_USER_NOT_MATCH, nil
	case "MediaInfoServiceCode_ERROR_MEDIA_NOT_EXIST":
		return MediaInfoServiceCode_ERROR_MEDIA_NOT_EXIST, nil
	case "MediaInfoServiceCode_ERROR_MEDIA_PARAM_INVALID":
		return MediaInfoServiceCode_ERROR_MEDIA_PARAM_INVALID, nil
	case "MediaInfoServiceCode_ERROR_PN_NOT_MATCH":
		return MediaInfoServiceCode_ERROR_PN_NOT_MATCH, nil
	case "MediaInfoServiceCode_ERROR_PN_INVALID":
		return MediaInfoServiceCode_ERROR_PN_INVALID, nil
	case "MediaInfoServiceCode_ERROR_VC_TOO_LOW":
		return MediaInfoServiceCode_ERROR_VC_TOO_LOW, nil
	case "MediaInfoServiceCode_ERROR_VC_INVALID":
		return MediaInfoServiceCode_ERROR_VC_INVALID, nil
	case "MediaInfoServiceCode_ERROR_VN_TOO_LOW":
		return MediaInfoServiceCode_ERROR_VN_TOO_LOW, nil
	case "MediaInfoServiceCode_ERROR_VN_INVALID":
		return MediaInfoServiceCode_ERROR_VN_INVALID, nil
	case "MediaInfoServiceCode_ERROR_UPDATE_LOG_INVALID":
		return MediaInfoServiceCode_ERROR_UPDATE_LOG_INVALID, nil
	case "MediaInfoServiceCode_ERROR_MEDIA_SYSTEM_ERROR":
		return MediaInfoServiceCode_ERROR_MEDIA_SYSTEM_ERROR, nil
	}
	return MediaInfoServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid MediaInfoServiceCode string")
}

type MediaQueryResult *common.QueryResult

type MediaQueryInt mediainfo_types.MediaQueryInt

type RequestHeader *common.RequestHeader

type UidInt mediainfo_types.UidInt

type Media *mediainfo_types.Media

type MediaIdInt mediainfo_types.MediaIdInt

type AdCreativeIdInt mediainfo_types.AdCreativeIdInt

type MediaAppUpdateIdInt mediainfo_types.MediaAppUpdateIdInt

type MediaSetting *mediainfo_types.MediaSetting

type MediaPackageInfo *mediainfo_types.MediaPackageInfo

type TimeInt mediainfo_types.TimeInt

type MediaType mediainfo_types.MediaType

type MediaStatus mediainfo_types.MediaStatus

type MediaAppUpdateStatus mediainfo_types.MediaAppUpdateStatus

type MediaStatusLamp *mediainfo_types.MediaStatusLamp

type MediaSummary *mediainfo_types.MediaSummary

type MediaAppUpdate *mediainfo_types.MediaAppUpdate

type MediaAppUpdateWrapper *mediainfo_types.MediaAppUpdateWrapper

type SDKPlatform mediainfo_types.SDKPlatform

type MediaDetectInfo *mediainfo_types.MediaDetectInfo

type MediaAppRate *mediainfo_types.MediaAppRate

type HouseAdPriorityType mediainfo_types.HouseAdPriorityType

type PlacementIdInt mediainfo_types.PlacementIdInt

type Placement *mediainfo_types.Placement

type PlacementSetting *mediainfo_types.PlacementSetting

type TemplateSizeCodeInt mediainfo_types.TemplateSizeCodeInt

type MediaPauseStatus mediainfo_types.MediaPauseStatus

type Container *common.Container

type IdInt common.IdInt

type Channel *common.Channel

type MediaInfoException struct {
	Code    MediaInfoServiceCode `thrift:"code,1" json:"code"`
	Message string               `thrift:"message,2" json:"message"`
}

func NewMediaInfoException() *MediaInfoException {
	return &MediaInfoException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaInfoException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *MediaInfoException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaInfoException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = MediaInfoServiceCode(v)
	}
	return nil
}

func (p *MediaInfoException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *MediaInfoException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaInfoException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaInfoException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *MediaInfoException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *MediaInfoException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaInfoException(%+v)", *p)
}
