// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package distribution

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type NodeI64 struct {
	Time int64 `thrift:"time,1" json:"time"`
	Val  int64 `thrift:"val,2" json:"val"`
}

func NewNodeI64() *NodeI64 {
	return &NodeI64{}
}

func (p *NodeI64) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NodeI64) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *NodeI64) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Val = v
	}
	return nil
}

func (p *NodeI64) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("node_i64"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NodeI64) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *NodeI64) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("val", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:val: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Val)); err != nil {
		return fmt.Errorf("%T.val (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:val: %s", p, err)
	}
	return err
}

func (p *NodeI64) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NodeI64(%+v)", *p)
}

type NodeI32 struct {
	Time int64 `thrift:"time,1" json:"time"`
	Val  int32 `thrift:"val,2" json:"val"`
}

func NewNodeI32() *NodeI32 {
	return &NodeI32{}
}

func (p *NodeI32) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NodeI32) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *NodeI32) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Val = v
	}
	return nil
}

func (p *NodeI32) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("node_i32"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NodeI32) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *NodeI32) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("val", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:val: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Val)); err != nil {
		return fmt.Errorf("%T.val (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:val: %s", p, err)
	}
	return err
}

func (p *NodeI32) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NodeI32(%+v)", *p)
}

type NodeStr struct {
	Time int64  `thrift:"time,1" json:"time"`
	Val  string `thrift:"val,2" json:"val"`
}

func NewNodeStr() *NodeStr {
	return &NodeStr{}
}

func (p *NodeStr) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NodeStr) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *NodeStr) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Val = v
	}
	return nil
}

func (p *NodeStr) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("node_str"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NodeStr) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *NodeStr) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("val", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:val: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Val)); err != nil {
		return fmt.Errorf("%T.val (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:val: %s", p, err)
	}
	return err
}

func (p *NodeStr) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NodeStr(%+v)", *p)
}

type NodeAd struct {
	Time       int64 `thrift:"time,1" json:"time"`
	SponsorId  int32 `thrift:"sponsor_id,2" json:"sponsor_id"`
	PlanId     int32 `thrift:"plan_id,3" json:"plan_id"`
	StrategyId int32 `thrift:"strategy_id,4" json:"strategy_id"`
	CreatId    int32 `thrift:"creat_id,5" json:"creat_id"`
	MediaId    int32 `thrift:"media_id,6" json:"media_id"`
}

func NewNodeAd() *NodeAd {
	return &NodeAd{}
}

func (p *NodeAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NodeAd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *NodeAd) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *NodeAd) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *NodeAd) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *NodeAd) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CreatId = v
	}
	return nil
}

func (p *NodeAd) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *NodeAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("node_ad"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NodeAd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *NodeAd) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsor_id: %s", p, err)
	}
	return err
}

func (p *NodeAd) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:plan_id: %s", p, err)
	}
	return err
}

func (p *NodeAd) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:strategy_id: %s", p, err)
	}
	return err
}

func (p *NodeAd) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creat_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:creat_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreatId)); err != nil {
		return fmt.Errorf("%T.creat_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:creat_id: %s", p, err)
	}
	return err
}

func (p *NodeAd) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.media_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:media_id: %s", p, err)
	}
	return err
}

func (p *NodeAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NodeAd(%+v)", *p)
}

type ItemI64 struct {
	CookieId int64 `thrift:"cookieId,1" json:"cookieId"`
	// unused field # 2
	// unused field # 3
	Ary []*NodeI64 `thrift:"ary,4" json:"ary"`
}

func NewItemI64() *ItemI64 {
	return &ItemI64{}
}

func (p *ItemI64) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ItemI64) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *ItemI64) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ary = make([]*NodeI64, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewNodeI64()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Ary = append(p.Ary, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ItemI64) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("item_i64"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ItemI64) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cookieId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cookieId: %s", p, err)
	}
	return err
}

func (p *ItemI64) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ary != nil {
		if err := oprot.WriteFieldBegin("ary", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ary: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ary)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ary {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ary: %s", p, err)
		}
	}
	return err
}

func (p *ItemI64) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemI64(%+v)", *p)
}

type ItemI32 struct {
	CookieId int64 `thrift:"cookieId,1" json:"cookieId"`
	// unused field # 2
	// unused field # 3
	Ary []*NodeI32 `thrift:"ary,4" json:"ary"`
}

func NewItemI32() *ItemI32 {
	return &ItemI32{}
}

func (p *ItemI32) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ItemI32) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *ItemI32) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ary = make([]*NodeI32, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewNodeI32()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Ary = append(p.Ary, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ItemI32) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("item_i32"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ItemI32) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cookieId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cookieId: %s", p, err)
	}
	return err
}

func (p *ItemI32) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ary != nil {
		if err := oprot.WriteFieldBegin("ary", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ary: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ary)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ary {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ary: %s", p, err)
		}
	}
	return err
}

func (p *ItemI32) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemI32(%+v)", *p)
}

type ItemStr struct {
	CookieId int64 `thrift:"cookieId,1" json:"cookieId"`
	// unused field # 2
	// unused field # 3
	Ary []*NodeStr `thrift:"ary,4" json:"ary"`
}

func NewItemStr() *ItemStr {
	return &ItemStr{}
}

func (p *ItemStr) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ItemStr) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *ItemStr) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ary = make([]*NodeStr, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewNodeStr()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Ary = append(p.Ary, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ItemStr) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("item_str"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ItemStr) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cookieId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cookieId: %s", p, err)
	}
	return err
}

func (p *ItemStr) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ary != nil {
		if err := oprot.WriteFieldBegin("ary", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ary: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ary)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ary {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ary: %s", p, err)
		}
	}
	return err
}

func (p *ItemStr) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemStr(%+v)", *p)
}

type ItemAd struct {
	CookieId int64 `thrift:"cookieId,1" json:"cookieId"`
	// unused field # 2
	// unused field # 3
	Ary []*NodeAd `thrift:"ary,4" json:"ary"`
}

func NewItemAd() *ItemAd {
	return &ItemAd{}
}

func (p *ItemAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ItemAd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *ItemAd) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ary = make([]*NodeAd, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewNodeAd()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Ary = append(p.Ary, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ItemAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("item_ad"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ItemAd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cookieId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cookieId: %s", p, err)
	}
	return err
}

func (p *ItemAd) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ary != nil {
		if err := oprot.WriteFieldBegin("ary", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ary: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ary)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ary {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ary: %s", p, err)
		}
	}
	return err
}

func (p *ItemAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemAd(%+v)", *p)
}

type SegMsg struct {
	CookieId int64 `thrift:"cookieId,1" json:"cookieId"`
	// unused field # 2
	Val  int64 `thrift:"val,3" json:"val"`
	Time int64 `thrift:"time,4" json:"time"`
	Sid  int64 `thrift:"sid,5" json:"sid"`
}

func NewSegMsg() *SegMsg {
	return &SegMsg{}
}

func (p *SegMsg) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SegMsg) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *SegMsg) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Val = v
	}
	return nil
}

func (p *SegMsg) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *SegMsg) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *SegMsg) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("seg_msg"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SegMsg) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cookieId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cookieId: %s", p, err)
	}
	return err
}

func (p *SegMsg) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("val", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:val: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Val)); err != nil {
		return fmt.Errorf("%T.val (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:val: %s", p, err)
	}
	return err
}

func (p *SegMsg) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:time: %s", p, err)
	}
	return err
}

func (p *SegMsg) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sid: %s", p, err)
	}
	return err
}

func (p *SegMsg) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SegMsg(%+v)", *p)
}
