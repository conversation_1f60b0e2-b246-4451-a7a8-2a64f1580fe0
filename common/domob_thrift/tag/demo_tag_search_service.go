// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package tag

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = tag_types.GoUnusedProtection__

type DemoTagSearchService interface { //DemoTagSearch服务接口定义

	// 根据概念编号或标签编号获取覆盖人群总数
	// 返回的key是置信度常量之一，value是覆盖人群总数
	// 置信度低的覆盖人群数包含置信度高的人群数
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - ConceptOrTagId: 概念或标签编号
	GetCoveragebyConceptOrTagId(header *common.RequestHeader, conceptOrTagId IdInt) (r map[int32]Amount, dtse *DemoTagSearchException, err error)
	// 根据用户唯一标识ID获取已知标签的投放决定列表
	// 这里返回的map的key是查询的userId, Value是对应的ImpDecision列表
	// ImpDecision中的编号只可能是标签编号
	// 只返回实际置信度大于等于最低置信度的投放决定列表
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Guids: 用户唯一标识ID列表，批量查询接口
	//  - Confidences: 要求的最低置信度列表，与UserId列表一一对应
	GetImpDecisionsByGuids(header *common.RequestHeader, guids []int64, confidences []int32) (r map[int64][]*tag_types.ImpDecision, dtse *DemoTagSearchException, err error)
}

//DemoTagSearch服务接口定义
type DemoTagSearchServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDemoTagSearchServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DemoTagSearchServiceClient {
	return &DemoTagSearchServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDemoTagSearchServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DemoTagSearchServiceClient {
	return &DemoTagSearchServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 根据概念编号或标签编号获取覆盖人群总数
// 返回的key是置信度常量之一，value是覆盖人群总数
// 置信度低的覆盖人群数包含置信度高的人群数
//
// Parameters:
//  - Header: 请求消息头结构体
//  - ConceptOrTagId: 概念或标签编号
func (p *DemoTagSearchServiceClient) GetCoveragebyConceptOrTagId(header *common.RequestHeader, conceptOrTagId IdInt) (r map[int32]Amount, dtse *DemoTagSearchException, err error) {
	if err = p.sendGetCoveragebyConceptOrTagId(header, conceptOrTagId); err != nil {
		return
	}
	return p.recvGetCoveragebyConceptOrTagId()
}

func (p *DemoTagSearchServiceClient) sendGetCoveragebyConceptOrTagId(header *common.RequestHeader, conceptOrTagId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCoveragebyConceptOrTagId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetCoveragebyConceptOrTagIdArgs()
	args0.Header = header
	args0.ConceptOrTagId = conceptOrTagId
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DemoTagSearchServiceClient) recvGetCoveragebyConceptOrTagId() (value map[int32]Amount, dtse *DemoTagSearchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetCoveragebyConceptOrTagIdResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Dtse != nil {
		dtse = result1.Dtse
	}
	return
}

// 根据用户唯一标识ID获取已知标签的投放决定列表
// 这里返回的map的key是查询的userId, Value是对应的ImpDecision列表
// ImpDecision中的编号只可能是标签编号
// 只返回实际置信度大于等于最低置信度的投放决定列表
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Guids: 用户唯一标识ID列表，批量查询接口
//  - Confidences: 要求的最低置信度列表，与UserId列表一一对应
func (p *DemoTagSearchServiceClient) GetImpDecisionsByGuids(header *common.RequestHeader, guids []int64, confidences []int32) (r map[int64][]*tag_types.ImpDecision, dtse *DemoTagSearchException, err error) {
	if err = p.sendGetImpDecisionsByGuids(header, guids, confidences); err != nil {
		return
	}
	return p.recvGetImpDecisionsByGuids()
}

func (p *DemoTagSearchServiceClient) sendGetImpDecisionsByGuids(header *common.RequestHeader, guids []int64, confidences []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getImpDecisionsByGuids", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetImpDecisionsByGuidsArgs()
	args4.Header = header
	args4.Guids = guids
	args4.Confidences = confidences
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DemoTagSearchServiceClient) recvGetImpDecisionsByGuids() (value map[int64][]*tag_types.ImpDecision, dtse *DemoTagSearchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetImpDecisionsByGuidsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Dtse != nil {
		dtse = result5.Dtse
	}
	return
}

type DemoTagSearchServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DemoTagSearchService
}

func (p *DemoTagSearchServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DemoTagSearchServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DemoTagSearchServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDemoTagSearchServiceProcessor(handler DemoTagSearchService) *DemoTagSearchServiceProcessor {

	self8 := &DemoTagSearchServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self8.processorMap["getCoveragebyConceptOrTagId"] = &demoTagSearchServiceProcessorGetCoveragebyConceptOrTagId{handler: handler}
	self8.processorMap["getImpDecisionsByGuids"] = &demoTagSearchServiceProcessorGetImpDecisionsByGuids{handler: handler}
	return self8
}

func (p *DemoTagSearchServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x9 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x9.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x9

}

type demoTagSearchServiceProcessorGetCoveragebyConceptOrTagId struct {
	handler DemoTagSearchService
}

func (p *demoTagSearchServiceProcessorGetCoveragebyConceptOrTagId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCoveragebyConceptOrTagIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCoveragebyConceptOrTagId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCoveragebyConceptOrTagIdResult()
	if result.Success, result.Dtse, err = p.handler.GetCoveragebyConceptOrTagId(args.Header, args.ConceptOrTagId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCoveragebyConceptOrTagId: "+err.Error())
		oprot.WriteMessageBegin("getCoveragebyConceptOrTagId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCoveragebyConceptOrTagId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type demoTagSearchServiceProcessorGetImpDecisionsByGuids struct {
	handler DemoTagSearchService
}

func (p *demoTagSearchServiceProcessorGetImpDecisionsByGuids) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetImpDecisionsByGuidsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getImpDecisionsByGuids", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetImpDecisionsByGuidsResult()
	if result.Success, result.Dtse, err = p.handler.GetImpDecisionsByGuids(args.Header, args.Guids, args.Confidences); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getImpDecisionsByGuids: "+err.Error())
		oprot.WriteMessageBegin("getImpDecisionsByGuids", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getImpDecisionsByGuids", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetCoveragebyConceptOrTagIdArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	ConceptOrTagId IdInt                 `thrift:"conceptOrTagId,2" json:"conceptOrTagId"`
}

func NewGetCoveragebyConceptOrTagIdArgs() *GetCoveragebyConceptOrTagIdArgs {
	return &GetCoveragebyConceptOrTagIdArgs{}
}

func (p *GetCoveragebyConceptOrTagIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ConceptOrTagId = IdInt(v)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCoveragebyConceptOrTagId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCoveragebyConceptOrTagIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("conceptOrTagId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:conceptOrTagId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ConceptOrTagId)); err != nil {
		return fmt.Errorf("%T.conceptOrTagId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:conceptOrTagId: %s", p, err)
	}
	return err
}

func (p *GetCoveragebyConceptOrTagIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCoveragebyConceptOrTagIdArgs(%+v)", *p)
}

type GetCoveragebyConceptOrTagIdResult struct {
	Success map[int32]Amount        `thrift:"success,0" json:"success"`
	Dtse    *DemoTagSearchException `thrift:"dtse,1" json:"dtse"`
}

func NewGetCoveragebyConceptOrTagIdResult() *GetCoveragebyConceptOrTagIdResult {
	return &GetCoveragebyConceptOrTagIdResult{}
}

func (p *GetCoveragebyConceptOrTagIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]Amount, size)
	for i := 0; i < size; i++ {
		var _key10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key10 = v
		}
		var _val11 Amount
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val11 = Amount(v)
		}
		p.Success[_key10] = _val11
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdResult) readField1(iprot thrift.TProtocol) error {
	p.Dtse = NewDemoTagSearchException()
	if err := p.Dtse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dtse)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCoveragebyConceptOrTagId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Dtse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCoveragebyConceptOrTagIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I64, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCoveragebyConceptOrTagIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Dtse != nil {
		if err := oprot.WriteFieldBegin("dtse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dtse: %s", p, err)
		}
		if err := p.Dtse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dtse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dtse: %s", p, err)
		}
	}
	return err
}

func (p *GetCoveragebyConceptOrTagIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCoveragebyConceptOrTagIdResult(%+v)", *p)
}

type GetImpDecisionsByGuidsArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Guids       []int64               `thrift:"guids,2" json:"guids"`
	Confidences []int32               `thrift:"confidences,3" json:"confidences"`
}

func NewGetImpDecisionsByGuidsArgs() *GetImpDecisionsByGuidsArgs {
	return &GetImpDecisionsByGuidsArgs{}
}

func (p *GetImpDecisionsByGuidsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Guids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.Guids = append(p.Guids, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Confidences = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.Confidences = append(p.Confidences, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getImpDecisionsByGuids_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetImpDecisionsByGuidsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Guids != nil {
		if err := oprot.WriteFieldBegin("guids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:guids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Guids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Guids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:guids: %s", p, err)
		}
	}
	return err
}

func (p *GetImpDecisionsByGuidsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Confidences != nil {
		if err := oprot.WriteFieldBegin("confidences", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:confidences: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Confidences)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Confidences {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:confidences: %s", p, err)
		}
	}
	return err
}

func (p *GetImpDecisionsByGuidsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetImpDecisionsByGuidsArgs(%+v)", *p)
}

type GetImpDecisionsByGuidsResult struct {
	Success map[int64][]*tag_types.ImpDecision `thrift:"success,0" json:"success"`
	Dtse    *DemoTagSearchException            `thrift:"dtse,1" json:"dtse"`
}

func NewGetImpDecisionsByGuidsResult() *GetImpDecisionsByGuidsResult {
	return &GetImpDecisionsByGuidsResult{}
}

func (p *GetImpDecisionsByGuidsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int64][]*tag_types.ImpDecision, size)
	for i := 0; i < size; i++ {
		var _key14 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key14 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val15 := make([]*tag_types.ImpDecision, 0, size)
		for i := 0; i < size; i++ {
			_elem16 := tag_types.NewImpDecision()
			if err := _elem16.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem16)
			}
			_val15 = append(_val15, _elem16)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key14] = _val15
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsResult) readField1(iprot thrift.TProtocol) error {
	p.Dtse = NewDemoTagSearchException()
	if err := p.Dtse.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dtse)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getImpDecisionsByGuids_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Dtse != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetImpDecisionsByGuidsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI64(int64(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetImpDecisionsByGuidsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Dtse != nil {
		if err := oprot.WriteFieldBegin("dtse", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dtse: %s", p, err)
		}
		if err := p.Dtse.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dtse)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dtse: %s", p, err)
		}
	}
	return err
}

func (p *GetImpDecisionsByGuidsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetImpDecisionsByGuidsResult(%+v)", *p)
}
