// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"tag"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getCoveragebyConceptOrTagId(RequestHeader header, IdInt conceptOrTagId)")
	fmt.Fprintln(os.Stderr, "   getImpDecisionsByGuids(RequestHeader header,  guids,  confidences)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := tag.NewDemoTagSearchServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getCoveragebyConceptOrTagId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCoveragebyConceptOrTagId requires 2 args")
			flag.Usage()
		}
		arg17 := flag.Arg(1)
		mbTrans18 := thrift.NewTMemoryBufferLen(len(arg17))
		defer mbTrans18.Close()
		_, err19 := mbTrans18.WriteString(arg17)
		if err19 != nil {
			Usage()
			return
		}
		factory20 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt21 := factory20.GetProtocol(mbTrans18)
		argvalue0 := tag.NewRequestHeader()
		err22 := argvalue0.Read(jsProt21)
		if err22 != nil {
			Usage()
			return
		}
		value0 := tag.RequestHeader(argvalue0)
		tmp1, err23 := (strconv.Atoi(flag.Arg(2)))
		if err23 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := tag.IdInt(argvalue1)
		fmt.Print(client.GetCoveragebyConceptOrTagId(value0, value1))
		fmt.Print("\n")
		break
	case "getImpDecisionsByGuids":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetImpDecisionsByGuids requires 3 args")
			flag.Usage()
		}
		arg24 := flag.Arg(1)
		mbTrans25 := thrift.NewTMemoryBufferLen(len(arg24))
		defer mbTrans25.Close()
		_, err26 := mbTrans25.WriteString(arg24)
		if err26 != nil {
			Usage()
			return
		}
		factory27 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt28 := factory27.GetProtocol(mbTrans25)
		argvalue0 := tag.NewRequestHeader()
		err29 := argvalue0.Read(jsProt28)
		if err29 != nil {
			Usage()
			return
		}
		value0 := tag.RequestHeader(argvalue0)
		arg30 := flag.Arg(2)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		containerStruct1 := tag.NewGetImpDecisionsByGuidsArgs()
		err35 := containerStruct1.ReadField2(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Guids
		value1 := argvalue1
		arg36 := flag.Arg(3)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		containerStruct2 := tag.NewGetImpDecisionsByGuidsArgs()
		err41 := containerStruct2.ReadField3(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Confidences
		value2 := argvalue2
		fmt.Print(client.GetImpDecisionsByGuids(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
