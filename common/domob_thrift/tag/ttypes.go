// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package tag

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = tag_types.GoUnusedProtection__
var GoUnusedProtection__ int

//DemoTagSearchException中可能出现的异常代码
type DemoTagSearchServiceCode int64

const (
	DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_PARAM_INVALID DemoTagSearchServiceCode = 26401
	DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_SYSTEM_ERROR  DemoTagSearchServiceCode = 26501
)

func (p DemoTagSearchServiceCode) String() string {
	switch p {
	case DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_PARAM_INVALID:
		return "DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_PARAM_INVALID"
	case DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_SYSTEM_ERROR:
		return "DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func DemoTagSearchServiceCodeFromString(s string) (DemoTagSearchServiceCode, error) {
	switch s {
	case "DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_PARAM_INVALID":
		return DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_PARAM_INVALID, nil
	case "DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_SYSTEM_ERROR":
		return DemoTagSearchServiceCode_ERROR_DEMO_TAG_SEARCH_SYSTEM_ERROR, nil
	}
	return DemoTagSearchServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid DemoTagSearchServiceCode string")
}

type RequestHeader *common.RequestHeader

type ImpDecision *tag_types.ImpDecision

type Amount tag_types.Amount

type IdInt tag_types.IdInt

type DemoTagSearchException struct {
	Code    DemoTagSearchServiceCode `thrift:"code,1" json:"code"`
	Message string                   `thrift:"message,2" json:"message"`
}

func NewDemoTagSearchException() *DemoTagSearchException {
	return &DemoTagSearchException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DemoTagSearchException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DemoTagSearchException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DemoTagSearchException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DemoTagSearchServiceCode(v)
	}
	return nil
}

func (p *DemoTagSearchException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DemoTagSearchException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DemoTagSearchException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DemoTagSearchException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DemoTagSearchException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DemoTagSearchException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DemoTagSearchException(%+v)", *p)
}
