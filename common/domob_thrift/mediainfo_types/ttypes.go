// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mediainfo_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = tag_types.GoUnusedProtection__
var GoUnusedProtection__ int

//媒体暂停状态
type MediaPauseStatus int64

const (
	MediaPauseStatus_MPS_RUNNABLE MediaPauseStatus = 0
	MediaPauseStatus_MPS_PAUSED   MediaPauseStatus = 1
)

func (p MediaPauseStatus) String() string {
	switch p {
	case MediaPauseStatus_MPS_RUNNABLE:
		return "MediaPauseStatus_MPS_RUNNABLE"
	case MediaPauseStatus_MPS_PAUSED:
		return "MediaPauseStatus_MPS_PAUSED"
	}
	return "<UNSET>"
}

func MediaPauseStatusFromString(s string) (MediaPauseStatus, error) {
	switch s {
	case "MediaPauseStatus_MPS_RUNNABLE":
		return MediaPauseStatus_MPS_RUNNABLE, nil
	case "MediaPauseStatus_MPS_PAUSED":
		return MediaPauseStatus_MPS_PAUSED, nil
	}
	return MediaPauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid MediaPauseStatus string")
}

//媒体状态
type MediaStatus int64

const (
	MediaStatus_MES_DRAFT                     MediaStatus = 0
	MediaStatus_MES_PENDING_APPROVAL          MediaStatus = 1
	MediaStatus_MES_RUNNABLE                  MediaStatus = 2
	MediaStatus_MES_REJECTED                  MediaStatus = 3
	MediaStatus_MES_DELETED                   MediaStatus = 4
	MediaStatus_MES_PENDING_UPLOAD            MediaStatus = 5
	MediaStatus_MES_FORBIDDEN                 MediaStatus = 6
	MediaStatus_MES_PENDING_APPROVAL_REUPLOAD MediaStatus = 7
)

func (p MediaStatus) String() string {
	switch p {
	case MediaStatus_MES_DRAFT:
		return "MediaStatus_MES_DRAFT"
	case MediaStatus_MES_PENDING_APPROVAL:
		return "MediaStatus_MES_PENDING_APPROVAL"
	case MediaStatus_MES_RUNNABLE:
		return "MediaStatus_MES_RUNNABLE"
	case MediaStatus_MES_REJECTED:
		return "MediaStatus_MES_REJECTED"
	case MediaStatus_MES_DELETED:
		return "MediaStatus_MES_DELETED"
	case MediaStatus_MES_PENDING_UPLOAD:
		return "MediaStatus_MES_PENDING_UPLOAD"
	case MediaStatus_MES_FORBIDDEN:
		return "MediaStatus_MES_FORBIDDEN"
	case MediaStatus_MES_PENDING_APPROVAL_REUPLOAD:
		return "MediaStatus_MES_PENDING_APPROVAL_REUPLOAD"
	}
	return "<UNSET>"
}

func MediaStatusFromString(s string) (MediaStatus, error) {
	switch s {
	case "MediaStatus_MES_DRAFT":
		return MediaStatus_MES_DRAFT, nil
	case "MediaStatus_MES_PENDING_APPROVAL":
		return MediaStatus_MES_PENDING_APPROVAL, nil
	case "MediaStatus_MES_RUNNABLE":
		return MediaStatus_MES_RUNNABLE, nil
	case "MediaStatus_MES_REJECTED":
		return MediaStatus_MES_REJECTED, nil
	case "MediaStatus_MES_DELETED":
		return MediaStatus_MES_DELETED, nil
	case "MediaStatus_MES_PENDING_UPLOAD":
		return MediaStatus_MES_PENDING_UPLOAD, nil
	case "MediaStatus_MES_FORBIDDEN":
		return MediaStatus_MES_FORBIDDEN, nil
	case "MediaStatus_MES_PENDING_APPROVAL_REUPLOAD":
		return MediaStatus_MES_PENDING_APPROVAL_REUPLOAD, nil
	}
	return MediaStatus(math.MinInt32 - 1), fmt.Errorf("not a valid MediaStatus string")
}

//应用版本状态
type MediaAppUpdateStatus int64

const (
	MediaAppUpdateStatus_MAUS_PENDING_APPROVAL MediaAppUpdateStatus = 0
	MediaAppUpdateStatus_MAUS_RUNNABLE         MediaAppUpdateStatus = 2
	MediaAppUpdateStatus_MAUS_REJECTED         MediaAppUpdateStatus = 3
	MediaAppUpdateStatus_MAUS_DELETED          MediaAppUpdateStatus = 4
)

func (p MediaAppUpdateStatus) String() string {
	switch p {
	case MediaAppUpdateStatus_MAUS_PENDING_APPROVAL:
		return "MediaAppUpdateStatus_MAUS_PENDING_APPROVAL"
	case MediaAppUpdateStatus_MAUS_RUNNABLE:
		return "MediaAppUpdateStatus_MAUS_RUNNABLE"
	case MediaAppUpdateStatus_MAUS_REJECTED:
		return "MediaAppUpdateStatus_MAUS_REJECTED"
	case MediaAppUpdateStatus_MAUS_DELETED:
		return "MediaAppUpdateStatus_MAUS_DELETED"
	}
	return "<UNSET>"
}

func MediaAppUpdateStatusFromString(s string) (MediaAppUpdateStatus, error) {
	switch s {
	case "MediaAppUpdateStatus_MAUS_PENDING_APPROVAL":
		return MediaAppUpdateStatus_MAUS_PENDING_APPROVAL, nil
	case "MediaAppUpdateStatus_MAUS_RUNNABLE":
		return MediaAppUpdateStatus_MAUS_RUNNABLE, nil
	case "MediaAppUpdateStatus_MAUS_REJECTED":
		return MediaAppUpdateStatus_MAUS_REJECTED, nil
	case "MediaAppUpdateStatus_MAUS_DELETED":
		return MediaAppUpdateStatus_MAUS_DELETED, nil
	}
	return MediaAppUpdateStatus(math.MinInt32 - 1), fmt.Errorf("not a valid MediaAppUpdateStatus string")
}

//应用更新开关，仅用于媒体级别的全局设置，如果关闭，则不再对媒体相应的所有可提醒更新版本进行自动更新提醒
type MediaAppUpdatePauseStatus int64

const (
	MediaAppUpdatePauseStatus_MAUPS_RUNNABLE MediaAppUpdatePauseStatus = 0
	MediaAppUpdatePauseStatus_MAUPS_PAUSED   MediaAppUpdatePauseStatus = 1
)

func (p MediaAppUpdatePauseStatus) String() string {
	switch p {
	case MediaAppUpdatePauseStatus_MAUPS_RUNNABLE:
		return "MediaAppUpdatePauseStatus_MAUPS_RUNNABLE"
	case MediaAppUpdatePauseStatus_MAUPS_PAUSED:
		return "MediaAppUpdatePauseStatus_MAUPS_PAUSED"
	}
	return "<UNSET>"
}

func MediaAppUpdatePauseStatusFromString(s string) (MediaAppUpdatePauseStatus, error) {
	switch s {
	case "MediaAppUpdatePauseStatus_MAUPS_RUNNABLE":
		return MediaAppUpdatePauseStatus_MAUPS_RUNNABLE, nil
	case "MediaAppUpdatePauseStatus_MAUPS_PAUSED":
		return MediaAppUpdatePauseStatus_MAUPS_PAUSED, nil
	}
	return MediaAppUpdatePauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid MediaAppUpdatePauseStatus string")
}

//媒体好评提醒暂停状态
type MediaAppRatePauseStatus int64

const (
	MediaAppRatePauseStatus_MARPS_RUNNING MediaAppRatePauseStatus = 0
	MediaAppRatePauseStatus_MARPS_PAUSED  MediaAppRatePauseStatus = 1
)

func (p MediaAppRatePauseStatus) String() string {
	switch p {
	case MediaAppRatePauseStatus_MARPS_RUNNING:
		return "MediaAppRatePauseStatus_MARPS_RUNNING"
	case MediaAppRatePauseStatus_MARPS_PAUSED:
		return "MediaAppRatePauseStatus_MARPS_PAUSED"
	}
	return "<UNSET>"
}

func MediaAppRatePauseStatusFromString(s string) (MediaAppRatePauseStatus, error) {
	switch s {
	case "MediaAppRatePauseStatus_MARPS_RUNNING":
		return MediaAppRatePauseStatus_MARPS_RUNNING, nil
	case "MediaAppRatePauseStatus_MARPS_PAUSED":
		return MediaAppRatePauseStatus_MARPS_PAUSED, nil
	}
	return MediaAppRatePauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid MediaAppRatePauseStatus string")
}

//媒体广告位状态
//
//@since 1.1.0
type PlacementStatus int64

const (
	PlacementStatus_PS_RUNNABLE PlacementStatus = 0
	PlacementStatus_PS_DELETED  PlacementStatus = 1
)

func (p PlacementStatus) String() string {
	switch p {
	case PlacementStatus_PS_RUNNABLE:
		return "PlacementStatus_PS_RUNNABLE"
	case PlacementStatus_PS_DELETED:
		return "PlacementStatus_PS_DELETED"
	}
	return "<UNSET>"
}

func PlacementStatusFromString(s string) (PlacementStatus, error) {
	switch s {
	case "PlacementStatus_PS_RUNNABLE":
		return PlacementStatus_PS_RUNNABLE, nil
	case "PlacementStatus_PS_DELETED":
		return PlacementStatus_PS_DELETED, nil
	}
	return PlacementStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PlacementStatus string")
}

//媒体广告位暂停状态
//
//@since 1.1.0
type PlacementPauseStatus int64

const (
	PlacementPauseStatus_PPS_RUNNING PlacementPauseStatus = 0
	PlacementPauseStatus_PPS_PAUSED  PlacementPauseStatus = 1
)

func (p PlacementPauseStatus) String() string {
	switch p {
	case PlacementPauseStatus_PPS_RUNNING:
		return "PlacementPauseStatus_PPS_RUNNING"
	case PlacementPauseStatus_PPS_PAUSED:
		return "PlacementPauseStatus_PPS_PAUSED"
	}
	return "<UNSET>"
}

func PlacementPauseStatusFromString(s string) (PlacementPauseStatus, error) {
	switch s {
	case "PlacementPauseStatus_PPS_RUNNING":
		return PlacementPauseStatus_PPS_RUNNING, nil
	case "PlacementPauseStatus_PPS_PAUSED":
		return PlacementPauseStatus_PPS_PAUSED, nil
	}
	return PlacementPauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PlacementPauseStatus string")
}

//House Ad的投放优先级类型
//
//@since 1.1.0
type HouseAdPriorityType int64

const (
	HouseAdPriorityType_HAP_UNKNOWN   HouseAdPriorityType = 0
	HouseAdPriorityType_HAP_PREMIUM   HouseAdPriorityType = 10
	HouseAdPriorityType_HAP_BANLANCED HouseAdPriorityType = 11
)

func (p HouseAdPriorityType) String() string {
	switch p {
	case HouseAdPriorityType_HAP_UNKNOWN:
		return "HouseAdPriorityType_HAP_UNKNOWN"
	case HouseAdPriorityType_HAP_PREMIUM:
		return "HouseAdPriorityType_HAP_PREMIUM"
	case HouseAdPriorityType_HAP_BANLANCED:
		return "HouseAdPriorityType_HAP_BANLANCED"
	}
	return "<UNSET>"
}

func HouseAdPriorityTypeFromString(s string) (HouseAdPriorityType, error) {
	switch s {
	case "HouseAdPriorityType_HAP_UNKNOWN":
		return HouseAdPriorityType_HAP_UNKNOWN, nil
	case "HouseAdPriorityType_HAP_PREMIUM":
		return HouseAdPriorityType_HAP_PREMIUM, nil
	case "HouseAdPriorityType_HAP_BANLANCED":
		return HouseAdPriorityType_HAP_BANLANCED, nil
	}
	return HouseAdPriorityType(math.MinInt32 - 1), fmt.Errorf("not a valid HouseAdPriorityType string")
}

//状态灯颜色
type MediaStatusLampColor int64

const (
	MediaStatusLampColor_MSLC_GREEN               MediaStatusLampColor = 1
	MediaStatusLampColor_MSLC_YELLOW              MediaStatusLampColor = 2
	MediaStatusLampColor_MSLC_YELLOW_WITH_WARNING MediaStatusLampColor = 3
	MediaStatusLampColor_MSLC_RED                 MediaStatusLampColor = 4
)

func (p MediaStatusLampColor) String() string {
	switch p {
	case MediaStatusLampColor_MSLC_GREEN:
		return "MediaStatusLampColor_MSLC_GREEN"
	case MediaStatusLampColor_MSLC_YELLOW:
		return "MediaStatusLampColor_MSLC_YELLOW"
	case MediaStatusLampColor_MSLC_YELLOW_WITH_WARNING:
		return "MediaStatusLampColor_MSLC_YELLOW_WITH_WARNING"
	case MediaStatusLampColor_MSLC_RED:
		return "MediaStatusLampColor_MSLC_RED"
	}
	return "<UNSET>"
}

func MediaStatusLampColorFromString(s string) (MediaStatusLampColor, error) {
	switch s {
	case "MediaStatusLampColor_MSLC_GREEN":
		return MediaStatusLampColor_MSLC_GREEN, nil
	case "MediaStatusLampColor_MSLC_YELLOW":
		return MediaStatusLampColor_MSLC_YELLOW, nil
	case "MediaStatusLampColor_MSLC_YELLOW_WITH_WARNING":
		return MediaStatusLampColor_MSLC_YELLOW_WITH_WARNING, nil
	case "MediaStatusLampColor_MSLC_RED":
		return MediaStatusLampColor_MSLC_RED, nil
	}
	return MediaStatusLampColor(math.MinInt32 - 1), fmt.Errorf("not a valid MediaStatusLampColor string")
}

//媒体状态灯代码
type MediaStatusLampCode int64

const (
	MediaStatusLampCode_MSLC_RUNNING                MediaStatusLampCode = 10
	MediaStatusLampCode_MSLC_MEDIA_PENDING_APPROVAL MediaStatusLampCode = 20
	MediaStatusLampCode_MSLC_MEDIA_NOT_ACTIVATED    MediaStatusLampCode = 21
	MediaStatusLampCode_MSLC_MEDIA_REJECTED         MediaStatusLampCode = 30
	MediaStatusLampCode_MSLC_MEDIA_PAUSED           MediaStatusLampCode = 40
)

func (p MediaStatusLampCode) String() string {
	switch p {
	case MediaStatusLampCode_MSLC_RUNNING:
		return "MediaStatusLampCode_MSLC_RUNNING"
	case MediaStatusLampCode_MSLC_MEDIA_PENDING_APPROVAL:
		return "MediaStatusLampCode_MSLC_MEDIA_PENDING_APPROVAL"
	case MediaStatusLampCode_MSLC_MEDIA_NOT_ACTIVATED:
		return "MediaStatusLampCode_MSLC_MEDIA_NOT_ACTIVATED"
	case MediaStatusLampCode_MSLC_MEDIA_REJECTED:
		return "MediaStatusLampCode_MSLC_MEDIA_REJECTED"
	case MediaStatusLampCode_MSLC_MEDIA_PAUSED:
		return "MediaStatusLampCode_MSLC_MEDIA_PAUSED"
	}
	return "<UNSET>"
}

func MediaStatusLampCodeFromString(s string) (MediaStatusLampCode, error) {
	switch s {
	case "MediaStatusLampCode_MSLC_RUNNING":
		return MediaStatusLampCode_MSLC_RUNNING, nil
	case "MediaStatusLampCode_MSLC_MEDIA_PENDING_APPROVAL":
		return MediaStatusLampCode_MSLC_MEDIA_PENDING_APPROVAL, nil
	case "MediaStatusLampCode_MSLC_MEDIA_NOT_ACTIVATED":
		return MediaStatusLampCode_MSLC_MEDIA_NOT_ACTIVATED, nil
	case "MediaStatusLampCode_MSLC_MEDIA_REJECTED":
		return MediaStatusLampCode_MSLC_MEDIA_REJECTED, nil
	case "MediaStatusLampCode_MSLC_MEDIA_PAUSED":
		return MediaStatusLampCode_MSLC_MEDIA_PAUSED, nil
	}
	return MediaStatusLampCode(math.MinInt32 - 1), fmt.Errorf("not a valid MediaStatusLampCode string")
}

//媒体层级标识码
type MediaHierarchyType int64

const (
	MediaHierarchyType_EXTINFO_ID_TYPE_MEDIA                 MediaHierarchyType = 1
	MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENT        MediaHierarchyType = 2
	MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENTSETTING MediaHierarchyType = 3
	MediaHierarchyType_EXTINFO_ID_TYPE_MEIDAUSER             MediaHierarchyType = 4
	MediaHierarchyType_EXTINFO_ID_TYPE_CONTAINER             MediaHierarchyType = 5
	MediaHierarchyType_EXTINFO_ID_TYPE_CHANNEL               MediaHierarchyType = 6
)

func (p MediaHierarchyType) String() string {
	switch p {
	case MediaHierarchyType_EXTINFO_ID_TYPE_MEDIA:
		return "MediaHierarchyType_EXTINFO_ID_TYPE_MEDIA"
	case MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENT:
		return "MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENT"
	case MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENTSETTING:
		return "MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENTSETTING"
	case MediaHierarchyType_EXTINFO_ID_TYPE_MEIDAUSER:
		return "MediaHierarchyType_EXTINFO_ID_TYPE_MEIDAUSER"
	case MediaHierarchyType_EXTINFO_ID_TYPE_CONTAINER:
		return "MediaHierarchyType_EXTINFO_ID_TYPE_CONTAINER"
	case MediaHierarchyType_EXTINFO_ID_TYPE_CHANNEL:
		return "MediaHierarchyType_EXTINFO_ID_TYPE_CHANNEL"
	}
	return "<UNSET>"
}

func MediaHierarchyTypeFromString(s string) (MediaHierarchyType, error) {
	switch s {
	case "MediaHierarchyType_EXTINFO_ID_TYPE_MEDIA":
		return MediaHierarchyType_EXTINFO_ID_TYPE_MEDIA, nil
	case "MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENT":
		return MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENT, nil
	case "MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENTSETTING":
		return MediaHierarchyType_EXTINFO_ID_TYPE_MEDIAPLACEMENTSETTING, nil
	case "MediaHierarchyType_EXTINFO_ID_TYPE_MEIDAUSER":
		return MediaHierarchyType_EXTINFO_ID_TYPE_MEIDAUSER, nil
	case "MediaHierarchyType_EXTINFO_ID_TYPE_CONTAINER":
		return MediaHierarchyType_EXTINFO_ID_TYPE_CONTAINER, nil
	case "MediaHierarchyType_EXTINFO_ID_TYPE_CHANNEL":
		return MediaHierarchyType_EXTINFO_ID_TYPE_CHANNEL, nil
	}
	return MediaHierarchyType(math.MinInt32 - 1), fmt.Errorf("not a valid MediaHierarchyType string")
}

//媒体版本更新返回结果码
type MediaAppUpdateResultCode int64

const (
	MediaAppUpdateResultCode_MAURC_HAS_UPDATE         MediaAppUpdateResultCode = 200
	MediaAppUpdateResultCode_MAURC_UPDATE_PAUSED      MediaAppUpdateResultCode = 401
	MediaAppUpdateResultCode_MAURC_UPDATE_BANNED      MediaAppUpdateResultCode = 402
	MediaAppUpdateResultCode_MAURC_NO_UPDATE          MediaAppUpdateResultCode = 403
	MediaAppUpdateResultCode_MAURC_NO_VALID_UPDATE    MediaAppUpdateResultCode = 404
	MediaAppUpdateResultCode_MAURC_PN_NOT_MATCH       MediaAppUpdateResultCode = 450
	MediaAppUpdateResultCode_MAURC_PLATFORM_NOT_MATCH MediaAppUpdateResultCode = 451
	MediaAppUpdateResultCode_MAURC_UNKNOWN_ERROR      MediaAppUpdateResultCode = 501
)

func (p MediaAppUpdateResultCode) String() string {
	switch p {
	case MediaAppUpdateResultCode_MAURC_HAS_UPDATE:
		return "MediaAppUpdateResultCode_MAURC_HAS_UPDATE"
	case MediaAppUpdateResultCode_MAURC_UPDATE_PAUSED:
		return "MediaAppUpdateResultCode_MAURC_UPDATE_PAUSED"
	case MediaAppUpdateResultCode_MAURC_UPDATE_BANNED:
		return "MediaAppUpdateResultCode_MAURC_UPDATE_BANNED"
	case MediaAppUpdateResultCode_MAURC_NO_UPDATE:
		return "MediaAppUpdateResultCode_MAURC_NO_UPDATE"
	case MediaAppUpdateResultCode_MAURC_NO_VALID_UPDATE:
		return "MediaAppUpdateResultCode_MAURC_NO_VALID_UPDATE"
	case MediaAppUpdateResultCode_MAURC_PN_NOT_MATCH:
		return "MediaAppUpdateResultCode_MAURC_PN_NOT_MATCH"
	case MediaAppUpdateResultCode_MAURC_PLATFORM_NOT_MATCH:
		return "MediaAppUpdateResultCode_MAURC_PLATFORM_NOT_MATCH"
	case MediaAppUpdateResultCode_MAURC_UNKNOWN_ERROR:
		return "MediaAppUpdateResultCode_MAURC_UNKNOWN_ERROR"
	}
	return "<UNSET>"
}

func MediaAppUpdateResultCodeFromString(s string) (MediaAppUpdateResultCode, error) {
	switch s {
	case "MediaAppUpdateResultCode_MAURC_HAS_UPDATE":
		return MediaAppUpdateResultCode_MAURC_HAS_UPDATE, nil
	case "MediaAppUpdateResultCode_MAURC_UPDATE_PAUSED":
		return MediaAppUpdateResultCode_MAURC_UPDATE_PAUSED, nil
	case "MediaAppUpdateResultCode_MAURC_UPDATE_BANNED":
		return MediaAppUpdateResultCode_MAURC_UPDATE_BANNED, nil
	case "MediaAppUpdateResultCode_MAURC_NO_UPDATE":
		return MediaAppUpdateResultCode_MAURC_NO_UPDATE, nil
	case "MediaAppUpdateResultCode_MAURC_NO_VALID_UPDATE":
		return MediaAppUpdateResultCode_MAURC_NO_VALID_UPDATE, nil
	case "MediaAppUpdateResultCode_MAURC_PN_NOT_MATCH":
		return MediaAppUpdateResultCode_MAURC_PN_NOT_MATCH, nil
	case "MediaAppUpdateResultCode_MAURC_PLATFORM_NOT_MATCH":
		return MediaAppUpdateResultCode_MAURC_PLATFORM_NOT_MATCH, nil
	case "MediaAppUpdateResultCode_MAURC_UNKNOWN_ERROR":
		return MediaAppUpdateResultCode_MAURC_UNKNOWN_ERROR, nil
	}
	return MediaAppUpdateResultCode(math.MinInt32 - 1), fmt.Errorf("not a valid MediaAppUpdateResultCode string")
}

//媒体好评提醒弹出框样式
type MediaAppRateStyle int64

const (
	MediaAppRateStyle_MARS_TWO_BUTTONS   MediaAppRateStyle = 0
	MediaAppRateStyle_MARS_THREE_BUTTONS MediaAppRateStyle = 1
)

func (p MediaAppRateStyle) String() string {
	switch p {
	case MediaAppRateStyle_MARS_TWO_BUTTONS:
		return "MediaAppRateStyle_MARS_TWO_BUTTONS"
	case MediaAppRateStyle_MARS_THREE_BUTTONS:
		return "MediaAppRateStyle_MARS_THREE_BUTTONS"
	}
	return "<UNSET>"
}

func MediaAppRateStyleFromString(s string) (MediaAppRateStyle, error) {
	switch s {
	case "MediaAppRateStyle_MARS_TWO_BUTTONS":
		return MediaAppRateStyle_MARS_TWO_BUTTONS, nil
	case "MediaAppRateStyle_MARS_THREE_BUTTONS":
		return MediaAppRateStyle_MARS_THREE_BUTTONS, nil
	}
	return MediaAppRateStyle(math.MinInt32 - 1), fmt.Errorf("not a valid MediaAppRateStyle string")
}

type RefreshIntervalInt int32

type UidInt common.UidInt

type MediaIdInt common.IdInt

type MediaAppUpdateIdInt common.IdInt

type PlacementIdInt common.IdInt

type AdCreativeIdInt common.IdInt

type TemplateSizeCodeInt common.TemplateSizeCodeInt

type TimeInt common.TimeInt

type MediaQueryResult *common.QueryResult

type MediaQueryInt common.QueryInt

type RequestHeader *common.RequestHeader

type MediaType common.MediaType

type MediaCategory common.MediaCategory

type MediaTestModeSetting common.MediaTestModeSetting

type AdCategory common.AdCategory

type AdCreativeType common.AdCreativeType

type GenderCode common.GenderCode

type AgeCode common.AgeCode

type RegionCode common.RegionCode

type SDKPlatform common.SDKPlatform

type MediaTagging *tag_types.MediaTagging

type AppResourceIdInt int32

type MediaAppRateFrequencyInt int32

type HouseAdRatioInt int32

type AdPlacementType common.AdPlacementType

type StatusWhetherAvailable common.StatusWhetherAvailable

type IdTypeInt int32

type IdInt common.IdInt

type Container *common.Container

type Channel *common.Channel

type MediaStatusCount struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Count  int32            `thrift:"count,10" json:"count"`
	Status MediaStatus      `thrift:"status,11" json:"status"`
	Paused MediaPauseStatus `thrift:"paused,12" json:"paused"`
}

func NewMediaStatusCount() *MediaStatusCount {
	return &MediaStatusCount{
		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaStatusCount) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MediaStatusCount) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *MediaStatusCount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaStatusCount) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Count = v
	}
	return nil
}

func (p *MediaStatusCount) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Status = MediaStatus(v)
	}
	return nil
}

func (p *MediaStatusCount) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Paused = MediaPauseStatus(v)
	}
	return nil
}

func (p *MediaStatusCount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaStatusCount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaStatusCount) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("count", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Count)); err != nil {
		return fmt.Errorf("%T.count (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:count: %s", p, err)
	}
	return err
}

func (p *MediaStatusCount) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:status: %s", p, err)
		}
	}
	return err
}

func (p *MediaStatusCount) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:paused: %s", p, err)
		}
	}
	return err
}

func (p *MediaStatusCount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaStatusCount(%+v)", *p)
}

type MediaSummary struct {
	Uid          UidInt              `thrift:"uid,1" json:"uid"`
	StatusCounts []*MediaStatusCount `thrift:"statusCounts,2" json:"statusCounts"`
}

func NewMediaSummary() *MediaSummary {
	return &MediaSummary{}
}

func (p *MediaSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaSummary) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StatusCounts = make([]*MediaStatusCount, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewMediaStatusCount()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.StatusCounts = append(p.StatusCounts, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if p.StatusCounts != nil {
		if err := oprot.WriteFieldBegin("statusCounts", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:statusCounts: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.StatusCounts)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StatusCounts {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:statusCounts: %s", p, err)
		}
	}
	return err
}

func (p *MediaSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaSummary(%+v)", *p)
}

type AppSetting struct {
	Uid UidInt     `thrift:"uid,1" json:"uid"`
	Mid MediaIdInt `thrift:"mid,2" json:"mid"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	RefreshInterval RefreshIntervalInt   `thrift:"refreshInterval,10" json:"refreshInterval"`
	TestMode        MediaTestModeSetting `thrift:"testMode,11" json:"testMode"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewAppSetting() *AppSetting {
	return &AppSetting{
		TestMode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppSetting) IsSetTestMode() bool {
	return int64(p.TestMode) != math.MinInt32-1
}

func (p *AppSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AppSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *AppSetting) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RefreshInterval = RefreshIntervalInt(v)
	}
	return nil
}

func (p *AppSetting) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TestMode = MediaTestModeSetting(v)
	}
	return nil
}

func (p *AppSetting) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AppSetting) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AppSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AppSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *AppSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refreshInterval", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:refreshInterval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RefreshInterval)); err != nil {
		return fmt.Errorf("%T.refreshInterval (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:refreshInterval: %s", p, err)
	}
	return err
}

func (p *AppSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("testMode", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:testMode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TestMode)); err != nil {
		return fmt.Errorf("%T.testMode (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:testMode: %s", p, err)
	}
	return err
}

func (p *AppSetting) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *AppSetting) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AppSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppSetting(%+v)", *p)
}

type AdFilterSetting struct {
	Uid UidInt     `thrift:"uid,1" json:"uid"`
	Mid MediaIdInt `thrift:"mid,2" json:"mid"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	UrlFilter        []string         `thrift:"urlFilter,10" json:"urlFilter"`
	AppFilter        []string         `thrift:"appFilter,11" json:"appFilter"`
	TextFilter       []string         `thrift:"textFilter,12" json:"textFilter"`
	AdCategoryFilter []AdCategory     `thrift:"adCategoryFilter,13" json:"adCategoryFilter"`
	AdCreativeFilter []AdCreativeType `thrift:"adCreativeFilter,14" json:"adCreativeFilter"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewAdFilterSetting() *AdFilterSetting {
	return &AdFilterSetting{}
}

func (p *AdFilterSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdFilterSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdFilterSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *AdFilterSetting) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.UrlFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.UrlFilter = append(p.UrlFilter, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdFilterSetting) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AppFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.AppFilter = append(p.AppFilter, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdFilterSetting) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TextFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.TextFilter = append(p.TextFilter, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdFilterSetting) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdCategoryFilter = make([]AdCategory, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 AdCategory
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = AdCategory(v)
		}
		p.AdCategoryFilter = append(p.AdCategoryFilter, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdFilterSetting) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdCreativeFilter = make([]AdCreativeType, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 AdCreativeType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = AdCreativeType(v)
		}
		p.AdCreativeFilter = append(p.AdCreativeFilter, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdFilterSetting) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdFilterSetting) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdFilterSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdFilterSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdFilterSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdFilterSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *AdFilterSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if p.UrlFilter != nil {
		if err := oprot.WriteFieldBegin("urlFilter", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:urlFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.UrlFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.UrlFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:urlFilter: %s", p, err)
		}
	}
	return err
}

func (p *AdFilterSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AppFilter != nil {
		if err := oprot.WriteFieldBegin("appFilter", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:appFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AppFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AppFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:appFilter: %s", p, err)
		}
	}
	return err
}

func (p *AdFilterSetting) writeField12(oprot thrift.TProtocol) (err error) {
	if p.TextFilter != nil {
		if err := oprot.WriteFieldBegin("textFilter", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:textFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TextFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TextFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:textFilter: %s", p, err)
		}
	}
	return err
}

func (p *AdFilterSetting) writeField13(oprot thrift.TProtocol) (err error) {
	if p.AdCategoryFilter != nil {
		if err := oprot.WriteFieldBegin("adCategoryFilter", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:adCategoryFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdCategoryFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdCategoryFilter {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:adCategoryFilter: %s", p, err)
		}
	}
	return err
}

func (p *AdFilterSetting) writeField14(oprot thrift.TProtocol) (err error) {
	if p.AdCreativeFilter != nil {
		if err := oprot.WriteFieldBegin("adCreativeFilter", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:adCreativeFilter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdCreativeFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdCreativeFilter {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:adCreativeFilter: %s", p, err)
		}
	}
	return err
}

func (p *AdFilterSetting) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *AdFilterSetting) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdFilterSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdFilterSetting(%+v)", *p)
}

type MediaSetting struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	AdFilterSetting *AdFilterSetting `thrift:"adFilterSetting,10" json:"adFilterSetting"`
	AppSetting      *AppSetting      `thrift:"appSetting,11" json:"appSetting"`
}

func NewMediaSetting() *MediaSetting {
	return &MediaSetting{}
}

func (p *MediaSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaSetting) readField10(iprot thrift.TProtocol) error {
	p.AdFilterSetting = NewAdFilterSetting()
	if err := p.AdFilterSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdFilterSetting)
	}
	return nil
}

func (p *MediaSetting) readField11(iprot thrift.TProtocol) error {
	p.AppSetting = NewAppSetting()
	if err := p.AppSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppSetting)
	}
	return nil
}

func (p *MediaSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if p.AdFilterSetting != nil {
		if err := oprot.WriteFieldBegin("adFilterSetting", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:adFilterSetting: %s", p, err)
		}
		if err := p.AdFilterSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdFilterSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:adFilterSetting: %s", p, err)
		}
	}
	return err
}

func (p *MediaSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AppSetting != nil {
		if err := oprot.WriteFieldBegin("appSetting", thrift.STRUCT, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:appSetting: %s", p, err)
		}
		if err := p.AppSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:appSetting: %s", p, err)
		}
	}
	return err
}

func (p *MediaSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaSetting(%+v)", *p)
}

type MediaPackageInfo struct {
	Uid UidInt     `thrift:"uid,1" json:"uid"`
	Mid MediaIdInt `thrift:"mid,2" json:"mid"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	PackageName string   `thrift:"packageName,10" json:"packageName"`
	VersionName string   `thrift:"versionName,11" json:"versionName"`
	VersionCode string   `thrift:"versionCode,12" json:"versionCode"`
	AppName     string   `thrift:"appName,13" json:"appName"`
	Platforms   []string `thrift:"platforms,14" json:"platforms"`
	Screens     []int32  `thrift:"screens,15" json:"screens"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewMediaPackageInfo() *MediaPackageInfo {
	return &MediaPackageInfo{}
}

func (p *MediaPackageInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaPackageInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaPackageInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *MediaPackageInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *MediaPackageInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.VersionName = v
	}
	return nil
}

func (p *MediaPackageInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *MediaPackageInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *MediaPackageInfo) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platforms = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.Platforms = append(p.Platforms, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaPackageInfo) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screens = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.Screens = append(p.Screens, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaPackageInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *MediaPackageInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *MediaPackageInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaPackageInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaPackageInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:packageName: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionName", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:versionName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VersionName)); err != nil {
		return fmt.Errorf("%T.versionName (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:versionName: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:versionCode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:versionCode: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:appName: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Platforms != nil {
		if err := oprot.WriteFieldBegin("platforms", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:platforms: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Platforms)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platforms {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:platforms: %s", p, err)
		}
	}
	return err
}

func (p *MediaPackageInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if p.Screens != nil {
		if err := oprot.WriteFieldBegin("screens", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:screens: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Screens)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screens {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:screens: %s", p, err)
		}
	}
	return err
}

func (p *MediaPackageInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *MediaPackageInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaPackageInfo(%+v)", *p)
}

type Media struct {
	Uid                      UidInt                    `thrift:"uid,1" json:"uid"`
	Id                       MediaIdInt                `thrift:"id,2" json:"id"`
	TypeA1                   MediaType                 `thrift:"type,3" json:"type"`
	Name                     string                    `thrift:"name,4" json:"name"`
	Keywords                 string                    `thrift:"keywords,5" json:"keywords"`
	Url                      string                    `thrift:"url,6" json:"url"`
	Category                 MediaCategory             `thrift:"category,7" json:"category"`
	Note                     string                    `thrift:"note,8" json:"note"`
	Pubid                    string                    `thrift:"pubid,9" json:"pubid"`
	CreateTime               TimeInt                   `thrift:"createTime,10" json:"createTime"`
	LastUpdate               TimeInt                   `thrift:"lastUpdate,11" json:"lastUpdate"`
	Paused                   MediaPauseStatus          `thrift:"paused,12" json:"paused"`
	Status                   MediaStatus               `thrift:"status,13" json:"status"`
	Comment                  string                    `thrift:"comment,14" json:"comment"`
	AppResourceId            AppResourceIdInt          `thrift:"appResourceId,15" json:"appResourceId"`
	UserPausedUpdate         MediaAppUpdatePauseStatus `thrift:"userPausedUpdate,16" json:"userPausedUpdate"`
	AdminPausedUpdate        MediaAppUpdatePauseStatus `thrift:"adminPausedUpdate,17" json:"adminPausedUpdate"`
	AdminPausedUpdateComment string                    `thrift:"adminPausedUpdateComment,18" json:"adminPausedUpdateComment"`
	// unused field # 19
	Charge          bool                      `thrift:"charge,20" json:"charge"`
	UseLocationInfo bool                      `thrift:"useLocationInfo,21" json:"useLocationInfo"`
	Gender          GenderCode                `thrift:"gender,22" json:"gender"`
	Age             []AgeCode                 `thrift:"age,23" json:"age"`
	Geo             []RegionCode              `thrift:"geo,24" json:"geo"`
	MediaTaggings   []*tag_types.MediaTagging `thrift:"mediaTaggings,25" json:"mediaTaggings"`
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Setting *MediaSetting `thrift:"setting,30" json:"setting"`
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	PackageInfo *MediaPackageInfo `thrift:"packageInfo,40" json:"packageInfo"`
}

func NewMedia() *Media {
	return &Media{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		UserPausedUpdate: math.MinInt32 - 1, // unset sentinal value

		AdminPausedUpdate: math.MinInt32 - 1, // unset sentinal value

		Gender: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Media) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Media) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *Media) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *Media) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Media) IsSetUserPausedUpdate() bool {
	return int64(p.UserPausedUpdate) != math.MinInt32-1
}

func (p *Media) IsSetAdminPausedUpdate() bool {
	return int64(p.AdminPausedUpdate) != math.MinInt32-1
}

func (p *Media) IsSetGender() bool {
	return int64(p.Gender) != math.MinInt32-1
}

func (p *Media) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.LIST {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.LIST {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Media) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *Media) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = MediaIdInt(v)
	}
	return nil
}

func (p *Media) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = MediaType(v)
	}
	return nil
}

func (p *Media) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Media) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Keywords = v
	}
	return nil
}

func (p *Media) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Media) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Category = MediaCategory(v)
	}
	return nil
}

func (p *Media) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *Media) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *Media) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Media) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *Media) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Paused = MediaPauseStatus(v)
	}
	return nil
}

func (p *Media) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Status = MediaStatus(v)
	}
	return nil
}

func (p *Media) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *Media) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.AppResourceId = AppResourceIdInt(v)
	}
	return nil
}

func (p *Media) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.UserPausedUpdate = MediaAppUpdatePauseStatus(v)
	}
	return nil
}

func (p *Media) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AdminPausedUpdate = MediaAppUpdatePauseStatus(v)
	}
	return nil
}

func (p *Media) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.AdminPausedUpdateComment = v
	}
	return nil
}

func (p *Media) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Charge = v
	}
	return nil
}

func (p *Media) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.UseLocationInfo = v
	}
	return nil
}

func (p *Media) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Gender = GenderCode(v)
	}
	return nil
}

func (p *Media) readField23(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Age = make([]AgeCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 AgeCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = AgeCode(v)
		}
		p.Age = append(p.Age, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Media) readField24(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Geo = make([]RegionCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 RegionCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = RegionCode(v)
		}
		p.Geo = append(p.Geo, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Media) readField25(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaTaggings = make([]*tag_types.MediaTagging, 0, size)
	for i := 0; i < size; i++ {
		_elem10 := tag_types.NewMediaTagging()
		if err := _elem10.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem10)
		}
		p.MediaTaggings = append(p.MediaTaggings, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Media) readField30(iprot thrift.TProtocol) error {
	p.Setting = NewMediaSetting()
	if err := p.Setting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Setting)
	}
	return nil
}

func (p *Media) readField40(iprot thrift.TProtocol) error {
	p.PackageInfo = NewMediaPackageInfo()
	if err := p.PackageInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PackageInfo)
	}
	return nil
}

func (p *Media) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Media"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Media) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *Media) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *Media) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:type: %s", p, err)
	}
	return err
}

func (p *Media) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Media) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keywords", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:keywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keywords)); err != nil {
		return fmt.Errorf("%T.keywords (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:keywords: %s", p, err)
	}
	return err
}

func (p *Media) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:url: %s", p, err)
	}
	return err
}

func (p *Media) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:category: %s", p, err)
	}
	return err
}

func (p *Media) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:note: %s", p, err)
	}
	return err
}

func (p *Media) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:pubid: %s", p, err)
	}
	return err
}

func (p *Media) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *Media) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Media) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:paused: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (13) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:status: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:comment: %s", p, err)
	}
	return err
}

func (p *Media) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appResourceId", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:appResourceId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppResourceId)); err != nil {
		return fmt.Errorf("%T.appResourceId (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:appResourceId: %s", p, err)
	}
	return err
}

func (p *Media) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserPausedUpdate() {
		if err := oprot.WriteFieldBegin("userPausedUpdate", thrift.I32, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:userPausedUpdate: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserPausedUpdate)); err != nil {
			return fmt.Errorf("%T.userPausedUpdate (16) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:userPausedUpdate: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdminPausedUpdate() {
		if err := oprot.WriteFieldBegin("adminPausedUpdate", thrift.I32, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:adminPausedUpdate: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdminPausedUpdate)); err != nil {
			return fmt.Errorf("%T.adminPausedUpdate (17) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:adminPausedUpdate: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminPausedUpdateComment", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:adminPausedUpdateComment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdminPausedUpdateComment)); err != nil {
		return fmt.Errorf("%T.adminPausedUpdateComment (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:adminPausedUpdateComment: %s", p, err)
	}
	return err
}

func (p *Media) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("charge", thrift.BOOL, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Charge)); err != nil {
		return fmt.Errorf("%T.charge (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:charge: %s", p, err)
	}
	return err
}

func (p *Media) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("useLocationInfo", thrift.BOOL, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:useLocationInfo: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UseLocationInfo)); err != nil {
		return fmt.Errorf("%T.useLocationInfo (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:useLocationInfo: %s", p, err)
	}
	return err
}

func (p *Media) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gender", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:gender: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Gender)); err != nil {
		return fmt.Errorf("%T.gender (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:gender: %s", p, err)
	}
	return err
}

func (p *Media) writeField23(oprot thrift.TProtocol) (err error) {
	if p.Age != nil {
		if err := oprot.WriteFieldBegin("age", thrift.LIST, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:age: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Age)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Age {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:age: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField24(oprot thrift.TProtocol) (err error) {
	if p.Geo != nil {
		if err := oprot.WriteFieldBegin("geo", thrift.LIST, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:geo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Geo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Geo {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:geo: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField25(oprot thrift.TProtocol) (err error) {
	if p.MediaTaggings != nil {
		if err := oprot.WriteFieldBegin("mediaTaggings", thrift.LIST, 25); err != nil {
			return fmt.Errorf("%T write field begin error 25:mediaTaggings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MediaTaggings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaTaggings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 25:mediaTaggings: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField30(oprot thrift.TProtocol) (err error) {
	if p.Setting != nil {
		if err := oprot.WriteFieldBegin("setting", thrift.STRUCT, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:setting: %s", p, err)
		}
		if err := p.Setting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Setting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:setting: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField40(oprot thrift.TProtocol) (err error) {
	if p.PackageInfo != nil {
		if err := oprot.WriteFieldBegin("packageInfo", thrift.STRUCT, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:packageInfo: %s", p, err)
		}
		if err := p.PackageInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PackageInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:packageInfo: %s", p, err)
		}
	}
	return err
}

func (p *Media) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Media(%+v)", *p)
}

type MediaAppUpdate struct {
	Uid UidInt              `thrift:"uid,1" json:"uid"`
	Mid MediaIdInt          `thrift:"mid,2" json:"mid"`
	Id  MediaAppUpdateIdInt `thrift:"id,3" json:"id"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	PackageName   string           `thrift:"packageName,10" json:"packageName"`
	VersionName   string           `thrift:"versionName,11" json:"versionName"`
	VersionCode   string           `thrift:"versionCode,12" json:"versionCode"`
	AppName       string           `thrift:"appName,13" json:"appName"`
	AppResourceId AppResourceIdInt `thrift:"appResourceId,14" json:"appResourceId"`
	ITunesUrl     string           `thrift:"iTunesUrl,15" json:"iTunesUrl"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	Size       int32    `thrift:"size,40" json:"size"`
	PackageMd5 string   `thrift:"packageMd5,41" json:"packageMd5"`
	Platforms  []string `thrift:"platforms,42" json:"platforms"`
	Screens    []int32  `thrift:"screens,43" json:"screens"`
	UpdateLog  string   `thrift:"updateLog,44" json:"updateLog"`
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	CreateTime TimeInt              `thrift:"createTime,60" json:"createTime"`
	LastUpdate TimeInt              `thrift:"lastUpdate,61" json:"lastUpdate"`
	AdminTime  TimeInt              `thrift:"adminTime,62" json:"adminTime"`
	Status     MediaAppUpdateStatus `thrift:"status,63" json:"status"`
	Comment    string               `thrift:"comment,64" json:"comment"`
}

func NewMediaAppUpdate() *MediaAppUpdate {
	return &MediaAppUpdate{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaAppUpdate) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MediaAppUpdate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.LIST {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.LIST {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I64 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I64 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I64 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.I32 {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.STRING {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaAppUpdate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Id = MediaAppUpdateIdInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *MediaAppUpdate) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.VersionName = v
	}
	return nil
}

func (p *MediaAppUpdate) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *MediaAppUpdate) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *MediaAppUpdate) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AppResourceId = AppResourceIdInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ITunesUrl = v
	}
	return nil
}

func (p *MediaAppUpdate) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *MediaAppUpdate) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.PackageMd5 = v
	}
	return nil
}

func (p *MediaAppUpdate) readField42(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platforms = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.Platforms = append(p.Platforms, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaAppUpdate) readField43(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screens = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.Screens = append(p.Screens, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaAppUpdate) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.UpdateLog = v
	}
	return nil
}

func (p *MediaAppUpdate) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.AdminTime = TimeInt(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.Status = MediaAppUpdateStatus(v)
	}
	return nil
}

func (p *MediaAppUpdate) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *MediaAppUpdate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaAppUpdate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaAppUpdate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:id: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:packageName: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionName", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:versionName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VersionName)); err != nil {
		return fmt.Errorf("%T.versionName (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:versionName: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:versionCode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:versionCode: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:appName: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appResourceId", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:appResourceId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppResourceId)); err != nil {
		return fmt.Errorf("%T.appResourceId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:appResourceId: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iTunesUrl", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:iTunesUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ITunesUrl)); err != nil {
		return fmt.Errorf("%T.iTunesUrl (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:iTunesUrl: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:size: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageMd5", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:packageMd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageMd5)); err != nil {
		return fmt.Errorf("%T.packageMd5 (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:packageMd5: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField42(oprot thrift.TProtocol) (err error) {
	if p.Platforms != nil {
		if err := oprot.WriteFieldBegin("platforms", thrift.LIST, 42); err != nil {
			return fmt.Errorf("%T write field begin error 42:platforms: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Platforms)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platforms {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 42:platforms: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppUpdate) writeField43(oprot thrift.TProtocol) (err error) {
	if p.Screens != nil {
		if err := oprot.WriteFieldBegin("screens", thrift.LIST, 43); err != nil {
			return fmt.Errorf("%T write field begin error 43:screens: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Screens)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screens {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 43:screens: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppUpdate) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updateLog", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:updateLog: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UpdateLog)); err != nil {
		return fmt.Errorf("%T.updateLog (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:updateLog: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:createTime: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:lastUpdate: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminTime", thrift.I64, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:adminTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdminTime)); err != nil {
		return fmt.Errorf("%T.adminTime (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:adminTime: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) writeField63(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 63); err != nil {
			return fmt.Errorf("%T write field begin error 63:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (63) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 63:status: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppUpdate) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:comment: %s", p, err)
	}
	return err
}

func (p *MediaAppUpdate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaAppUpdate(%+v)", *p)
}

type MediaUser struct {
	Uid       UidInt `thrift:"uid,1" json:"uid"`
	Forbidden bool   `thrift:"forbidden,2" json:"forbidden"`
}

func NewMediaUser() *MediaUser {
	return &MediaUser{}
}

func (p *MediaUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Forbidden = v
	}
	return nil
}

func (p *MediaUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("forbidden", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:forbidden: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Forbidden)); err != nil {
		return fmt.Errorf("%T.forbidden (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:forbidden: %s", p, err)
	}
	return err
}

func (p *MediaUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaUser(%+v)", *p)
}

type MediaStatusLamp struct {
	Status       MediaStatusLampColor  `thrift:"status,1" json:"status"`
	KnownReasons []MediaStatusLampCode `thrift:"knownReasons,2" json:"knownReasons"`
}

func NewMediaStatusLamp() *MediaStatusLamp {
	return &MediaStatusLamp{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaStatusLamp) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MediaStatusLamp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaStatusLamp) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = MediaStatusLampColor(v)
	}
	return nil
}

func (p *MediaStatusLamp) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.KnownReasons = make([]MediaStatusLampCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 MediaStatusLampCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = MediaStatusLampCode(v)
		}
		p.KnownReasons = append(p.KnownReasons, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaStatusLamp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaStatusLamp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaStatusLamp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *MediaStatusLamp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.KnownReasons != nil {
		if err := oprot.WriteFieldBegin("knownReasons", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:knownReasons: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.KnownReasons)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.KnownReasons {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:knownReasons: %s", p, err)
		}
	}
	return err
}

func (p *MediaStatusLamp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaStatusLamp(%+v)", *p)
}

type PlacementSetting struct {
	Uid  UidInt         `thrift:"uid,1" json:"uid"`
	Mid  MediaIdInt     `thrift:"mid,2" json:"mid"`
	Pmid PlacementIdInt `thrift:"pmid,3" json:"pmid"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CloseDomobAd      bool              `thrift:"closeDomobAd,10" json:"closeDomobAd"`
	PremiumAds        []AdCreativeIdInt `thrift:"premiumAds,11" json:"premiumAds"`
	NormalAds         []AdCreativeIdInt `thrift:"normalAds,12" json:"normalAds"`
	HouseAdRatio      HouseAdRatioInt   `thrift:"houseAdRatio,13" json:"houseAdRatio"`
	AcceptVideoLevel  int32             `thrift:"acceptVideoLevel,14" json:"acceptVideoLevel"`
	MuteVideoSetting  int32             `thrift:"muteVideoSetting,15" json:"muteVideoSetting"`
	CreativeWhiteList []AdCreativeIdInt `thrift:"creativeWhiteList,16" json:"creativeWhiteList"`
	CreativeBlackList []AdCreativeIdInt `thrift:"creativeBlackList,17" json:"creativeBlackList"`
	// unused field # 18
	// unused field # 19
	CreateTime    TimeInt             `thrift:"createTime,20" json:"createTime"`
	LastUpdate    TimeInt             `thrift:"lastUpdate,21" json:"lastUpdate"`
	ExtInfo       string              `thrift:"extInfo,22" json:"extInfo"`
	ExtInfoMap    map[string]string   `thrift:"extInfoMap,23" json:"extInfoMap"`
	ContainerList []*common.Container `thrift:"containerList,24" json:"containerList"`
	ChannelList   []*common.Channel   `thrift:"channelList,25" json:"channelList"`
}

func NewPlacementSetting() *PlacementSetting {
	return &PlacementSetting{}
}

func (p *PlacementSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.MAP {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.LIST {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PlacementSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *PlacementSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *PlacementSetting) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pmid = PlacementIdInt(v)
	}
	return nil
}

func (p *PlacementSetting) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CloseDomobAd = v
	}
	return nil
}

func (p *PlacementSetting) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PremiumAds = make([]AdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 AdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = AdCreativeIdInt(v)
		}
		p.PremiumAds = append(p.PremiumAds, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.NormalAds = make([]AdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 AdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = AdCreativeIdInt(v)
		}
		p.NormalAds = append(p.NormalAds, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.HouseAdRatio = HouseAdRatioInt(v)
	}
	return nil
}

func (p *PlacementSetting) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AcceptVideoLevel = v
	}
	return nil
}

func (p *PlacementSetting) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.MuteVideoSetting = v
	}
	return nil
}

func (p *PlacementSetting) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeWhiteList = make([]AdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 AdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = AdCreativeIdInt(v)
		}
		p.CreativeWhiteList = append(p.CreativeWhiteList, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeBlackList = make([]AdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem17 AdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem17 = AdCreativeIdInt(v)
		}
		p.CreativeBlackList = append(p.CreativeBlackList, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *PlacementSetting) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *PlacementSetting) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *PlacementSetting) readField23(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfoMap = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key18 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key18 = v
		}
		var _val19 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val19 = v
		}
		p.ExtInfoMap[_key18] = _val19
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) readField24(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ContainerList = make([]*common.Container, 0, size)
	for i := 0; i < size; i++ {
		_elem20 := common.NewContainer()
		if err := _elem20.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem20)
		}
		p.ContainerList = append(p.ContainerList, _elem20)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) readField25(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChannelList = make([]*common.Channel, 0, size)
	for i := 0; i < size; i++ {
		_elem21 := common.NewChannel()
		if err := _elem21.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem21)
		}
		p.ChannelList = append(p.ChannelList, _elem21)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PlacementSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PlacementSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PlacementSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pmid: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closeDomobAd", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:closeDomobAd: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CloseDomobAd)); err != nil {
		return fmt.Errorf("%T.closeDomobAd (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:closeDomobAd: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if p.PremiumAds != nil {
		if err := oprot.WriteFieldBegin("premiumAds", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:premiumAds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PremiumAds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PremiumAds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:premiumAds: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) writeField12(oprot thrift.TProtocol) (err error) {
	if p.NormalAds != nil {
		if err := oprot.WriteFieldBegin("normalAds", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:normalAds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.NormalAds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.NormalAds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:normalAds: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("houseAdRatio", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:houseAdRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HouseAdRatio)); err != nil {
		return fmt.Errorf("%T.houseAdRatio (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:houseAdRatio: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("acceptVideoLevel", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:acceptVideoLevel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AcceptVideoLevel)); err != nil {
		return fmt.Errorf("%T.acceptVideoLevel (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:acceptVideoLevel: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("muteVideoSetting", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:muteVideoSetting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MuteVideoSetting)); err != nil {
		return fmt.Errorf("%T.muteVideoSetting (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:muteVideoSetting: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField16(oprot thrift.TProtocol) (err error) {
	if p.CreativeWhiteList != nil {
		if err := oprot.WriteFieldBegin("creativeWhiteList", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:creativeWhiteList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CreativeWhiteList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeWhiteList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:creativeWhiteList: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) writeField17(oprot thrift.TProtocol) (err error) {
	if p.CreativeBlackList != nil {
		if err := oprot.WriteFieldBegin("creativeBlackList", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:creativeBlackList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CreativeBlackList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeBlackList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:creativeBlackList: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:extInfo: %s", p, err)
	}
	return err
}

func (p *PlacementSetting) writeField23(oprot thrift.TProtocol) (err error) {
	if p.ExtInfoMap != nil {
		if err := oprot.WriteFieldBegin("extInfoMap", thrift.MAP, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:extInfoMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfoMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfoMap {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:extInfoMap: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) writeField24(oprot thrift.TProtocol) (err error) {
	if p.ContainerList != nil {
		if err := oprot.WriteFieldBegin("containerList", thrift.LIST, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:containerList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ContainerList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ContainerList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:containerList: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) writeField25(oprot thrift.TProtocol) (err error) {
	if p.ChannelList != nil {
		if err := oprot.WriteFieldBegin("channelList", thrift.LIST, 25); err != nil {
			return fmt.Errorf("%T write field begin error 25:channelList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChannelList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChannelList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 25:channelList: %s", p, err)
		}
	}
	return err
}

func (p *PlacementSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlacementSetting(%+v)", *p)
}

type Placement struct {
	Uid  UidInt         `thrift:"uid,1" json:"uid"`
	Mid  MediaIdInt     `thrift:"mid,2" json:"mid"`
	Id   PlacementIdInt `thrift:"id,3" json:"id"`
	Name string         `thrift:"name,4" json:"name"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	CreateTime TimeInt `thrift:"createTime,60" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,61" json:"lastUpdate"`
	// unused field # 62
	Status PlacementStatus      `thrift:"status,63" json:"status"`
	Paused PlacementPauseStatus `thrift:"paused,64" json:"paused"`
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	AdPlacementType AdPlacementType `thrift:"adPlacementType,70" json:"adPlacementType"`
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	Ppid string `thrift:"ppid,75" json:"ppid"`
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	Setting *PlacementSetting `thrift:"setting,100" json:"setting"`
}

func NewPlacement() *Placement {
	return &Placement{
		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		AdPlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Placement) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Placement) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *Placement) IsSetAdPlacementType() bool {
	return int64(p.AdPlacementType) != math.MinInt32-1
}

func (p *Placement) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I64 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I64 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.I32 {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.I32 {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.I32 {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 75:
			if fieldTypeId == thrift.STRING {
				if err := p.readField75(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 100:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField100(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Placement) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *Placement) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *Placement) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Id = PlacementIdInt(v)
	}
	return nil
}

func (p *Placement) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Placement) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *Placement) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *Placement) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.Status = PlacementStatus(v)
	}
	return nil
}

func (p *Placement) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.Paused = PlacementPauseStatus(v)
	}
	return nil
}

func (p *Placement) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.AdPlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *Placement) readField75(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 75: %s", err)
	} else {
		p.Ppid = v
	}
	return nil
}

func (p *Placement) readField100(iprot thrift.TProtocol) error {
	p.Setting = NewPlacementSetting()
	if err := p.Setting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Setting)
	}
	return nil
}

func (p *Placement) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Placement"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField75(oprot); err != nil {
		return err
	}
	if err := p.writeField100(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Placement) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *Placement) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *Placement) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:id: %s", p, err)
	}
	return err
}

func (p *Placement) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Placement) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:createTime: %s", p, err)
	}
	return err
}

func (p *Placement) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Placement) writeField63(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 63); err != nil {
			return fmt.Errorf("%T write field begin error 63:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (63) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 63:status: %s", p, err)
		}
	}
	return err
}

func (p *Placement) writeField64(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 64); err != nil {
			return fmt.Errorf("%T write field begin error 64:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (64) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 64:paused: %s", p, err)
		}
	}
	return err
}

func (p *Placement) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlacementType", thrift.I32, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:adPlacementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlacementType)); err != nil {
		return fmt.Errorf("%T.adPlacementType (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:adPlacementType: %s", p, err)
	}
	return err
}

func (p *Placement) writeField75(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ppid", thrift.STRING, 75); err != nil {
		return fmt.Errorf("%T write field begin error 75:ppid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ppid)); err != nil {
		return fmt.Errorf("%T.ppid (75) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 75:ppid: %s", p, err)
	}
	return err
}

func (p *Placement) writeField100(oprot thrift.TProtocol) (err error) {
	if p.Setting != nil {
		if err := oprot.WriteFieldBegin("setting", thrift.STRUCT, 100); err != nil {
			return fmt.Errorf("%T write field begin error 100:setting: %s", p, err)
		}
		if err := p.Setting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Setting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 100:setting: %s", p, err)
		}
	}
	return err
}

func (p *Placement) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Placement(%+v)", *p)
}

type MediaAppUpdateWrapper struct {
	ResultCode     MediaAppUpdateResultCode `thrift:"resultCode,1" json:"resultCode"`
	MediaAppUpdate *MediaAppUpdate          `thrift:"mediaAppUpdate,2" json:"mediaAppUpdate"`
}

func NewMediaAppUpdateWrapper() *MediaAppUpdateWrapper {
	return &MediaAppUpdateWrapper{
		ResultCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaAppUpdateWrapper) IsSetResultCode() bool {
	return int64(p.ResultCode) != math.MinInt32-1
}

func (p *MediaAppUpdateWrapper) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaAppUpdateWrapper) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ResultCode = MediaAppUpdateResultCode(v)
	}
	return nil
}

func (p *MediaAppUpdateWrapper) readField2(iprot thrift.TProtocol) error {
	p.MediaAppUpdate = NewMediaAppUpdate()
	if err := p.MediaAppUpdate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAppUpdate)
	}
	return nil
}

func (p *MediaAppUpdateWrapper) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaAppUpdateWrapper"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaAppUpdateWrapper) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResultCode() {
		if err := oprot.WriteFieldBegin("resultCode", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:resultCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResultCode)); err != nil {
			return fmt.Errorf("%T.resultCode (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:resultCode: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppUpdateWrapper) writeField2(oprot thrift.TProtocol) (err error) {
	if p.MediaAppUpdate != nil {
		if err := oprot.WriteFieldBegin("mediaAppUpdate", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:mediaAppUpdate: %s", p, err)
		}
		if err := p.MediaAppUpdate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAppUpdate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:mediaAppUpdate: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppUpdateWrapper) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaAppUpdateWrapper(%+v)", *p)
}

type MediaAppRate struct {
	Mid         MediaIdInt               `thrift:"mid,1" json:"mid"`
	Uid         UidInt                   `thrift:"uid,2" json:"uid"`
	PauseStatus MediaAppRatePauseStatus  `thrift:"pauseStatus,3" json:"pauseStatus"`
	Prompt      string                   `thrift:"prompt,4" json:"prompt"`
	JumpUrl     string                   `thrift:"jumpUrl,5" json:"jumpUrl"`
	Style       MediaAppRateStyle        `thrift:"style,6" json:"style"`
	Frequency   MediaAppRateFrequencyInt `thrift:"frequency,7" json:"frequency"`
	CreateTime  TimeInt                  `thrift:"createTime,8" json:"createTime"`
	UpdateTime  TimeInt                  `thrift:"updateTime,9" json:"updateTime"`
}

func NewMediaAppRate() *MediaAppRate {
	return &MediaAppRate{
		PauseStatus: math.MinInt32 - 1, // unset sentinal value

		Style: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaAppRate) IsSetPauseStatus() bool {
	return int64(p.PauseStatus) != math.MinInt32-1
}

func (p *MediaAppRate) IsSetStyle() bool {
	return int64(p.Style) != math.MinInt32-1
}

func (p *MediaAppRate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaAppRate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *MediaAppRate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MediaAppRate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PauseStatus = MediaAppRatePauseStatus(v)
	}
	return nil
}

func (p *MediaAppRate) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Prompt = v
	}
	return nil
}

func (p *MediaAppRate) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.JumpUrl = v
	}
	return nil
}

func (p *MediaAppRate) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Style = MediaAppRateStyle(v)
	}
	return nil
}

func (p *MediaAppRate) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Frequency = MediaAppRateFrequencyInt(v)
	}
	return nil
}

func (p *MediaAppRate) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *MediaAppRate) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UpdateTime = TimeInt(v)
	}
	return nil
}

func (p *MediaAppRate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaAppRate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaAppRate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPauseStatus() {
		if err := oprot.WriteFieldBegin("pauseStatus", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:pauseStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PauseStatus)); err != nil {
			return fmt.Errorf("%T.pauseStatus (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:pauseStatus: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppRate) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("prompt", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:prompt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Prompt)); err != nil {
		return fmt.Errorf("%T.prompt (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:prompt: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jumpUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:jumpUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.JumpUrl)); err != nil {
		return fmt.Errorf("%T.jumpUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:jumpUrl: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStyle() {
		if err := oprot.WriteFieldBegin("style", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:style: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Style)); err != nil {
			return fmt.Errorf("%T.style (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:style: %s", p, err)
		}
	}
	return err
}

func (p *MediaAppRate) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("frequency", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:frequency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Frequency)); err != nil {
		return fmt.Errorf("%T.frequency (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:frequency: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:createTime: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updateTime", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:updateTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.updateTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:updateTime: %s", p, err)
	}
	return err
}

func (p *MediaAppRate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaAppRate(%+v)", *p)
}

type MediaDetectInfo struct {
	User           *MediaUser      `thrift:"user,1" json:"user"`
	Media          *Media          `thrift:"media,2" json:"media"`
	MediaAppUpdate *MediaAppUpdate `thrift:"mediaAppUpdate,3" json:"mediaAppUpdate"`
	MediaAppRate   *MediaAppRate   `thrift:"mediaAppRate,4" json:"mediaAppRate"`
	Placement      *Placement      `thrift:"placement,5" json:"placement"`
}

func NewMediaDetectInfo() *MediaDetectInfo {
	return &MediaDetectInfo{}
}

func (p *MediaDetectInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaDetectInfo) readField1(iprot thrift.TProtocol) error {
	p.User = NewMediaUser()
	if err := p.User.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.User)
	}
	return nil
}

func (p *MediaDetectInfo) readField2(iprot thrift.TProtocol) error {
	p.Media = NewMedia()
	if err := p.Media.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Media)
	}
	return nil
}

func (p *MediaDetectInfo) readField3(iprot thrift.TProtocol) error {
	p.MediaAppUpdate = NewMediaAppUpdate()
	if err := p.MediaAppUpdate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAppUpdate)
	}
	return nil
}

func (p *MediaDetectInfo) readField4(iprot thrift.TProtocol) error {
	p.MediaAppRate = NewMediaAppRate()
	if err := p.MediaAppRate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaAppRate)
	}
	return nil
}

func (p *MediaDetectInfo) readField5(iprot thrift.TProtocol) error {
	p.Placement = NewPlacement()
	if err := p.Placement.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Placement)
	}
	return nil
}

func (p *MediaDetectInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaDetectInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaDetectInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.User != nil {
		if err := oprot.WriteFieldBegin("user", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:user: %s", p, err)
		}
		if err := p.User.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.User)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:user: %s", p, err)
		}
	}
	return err
}

func (p *MediaDetectInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Media != nil {
		if err := oprot.WriteFieldBegin("media", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:media: %s", p, err)
		}
		if err := p.Media.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Media)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:media: %s", p, err)
		}
	}
	return err
}

func (p *MediaDetectInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MediaAppUpdate != nil {
		if err := oprot.WriteFieldBegin("mediaAppUpdate", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:mediaAppUpdate: %s", p, err)
		}
		if err := p.MediaAppUpdate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAppUpdate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:mediaAppUpdate: %s", p, err)
		}
	}
	return err
}

func (p *MediaDetectInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.MediaAppRate != nil {
		if err := oprot.WriteFieldBegin("mediaAppRate", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:mediaAppRate: %s", p, err)
		}
		if err := p.MediaAppRate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaAppRate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:mediaAppRate: %s", p, err)
		}
	}
	return err
}

func (p *MediaDetectInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Placement != nil {
		if err := oprot.WriteFieldBegin("placement", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:placement: %s", p, err)
		}
		if err := p.Placement.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Placement)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:placement: %s", p, err)
		}
	}
	return err
}

func (p *MediaDetectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaDetectInfo(%+v)", *p)
}

type MediaInfoExtInfo struct {
	Id         IdInt                  `thrift:"id,1" json:"id"`
	IdType     IdTypeInt              `thrift:"idType,2" json:"idType"`
	KeyName    string                 `thrift:"keyName,3" json:"keyName"`
	Value      string                 `thrift:"value,4" json:"value"`
	CreateTime TimeInt                `thrift:"createTime,5" json:"createTime"`
	LastUpdate TimeInt                `thrift:"lastUpdate,6" json:"lastUpdate"`
	Status     StatusWhetherAvailable `thrift:"status,7" json:"status"`
}

func NewMediaInfoExtInfo() *MediaInfoExtInfo {
	return &MediaInfoExtInfo{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaInfoExtInfo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MediaInfoExtInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaInfoExtInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *MediaInfoExtInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IdType = IdTypeInt(v)
	}
	return nil
}

func (p *MediaInfoExtInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.KeyName = v
	}
	return nil
}

func (p *MediaInfoExtInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *MediaInfoExtInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *MediaInfoExtInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *MediaInfoExtInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = StatusWhetherAvailable(v)
	}
	return nil
}

func (p *MediaInfoExtInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaInfoExtInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaInfoExtInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idType", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:idType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IdType)); err != nil {
		return fmt.Errorf("%T.idType (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:idType: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:keyName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.KeyName)); err != nil {
		return fmt.Errorf("%T.keyName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:keyName: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("value", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:value: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Value)); err != nil {
		return fmt.Errorf("%T.value (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:value: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:createTime: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:lastUpdate: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:status: %s", p, err)
	}
	return err
}

func (p *MediaInfoExtInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaInfoExtInfo(%+v)", *p)
}
