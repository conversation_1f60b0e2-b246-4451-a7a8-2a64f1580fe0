// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mediainfo_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = tag_types.GoUnusedProtection__

const REFRESH_INTERVAL_NO_REFRESH = 0
const REFRESH_INTERVAL_SDK_DEFAULT = 1
const REFRESH_INTERVAL_MIN_VALUE = 10
const REFRESH_INTERVAL_MAX_VALUE = 86400

func init() {
}
