// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_checkserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type CheckType int64

const (
	CheckType_MOBLIE    CheckType = 1
	CheckType_QQ        CheckType = 2
	CheckType_ALIPAY    CheckType = 3
	CheckType_AMAZON    CheckType = 4
	CheckType_YIHAODIAN CheckType = 5
	CheckType_JD        CheckType = 6
	CheckType_ALIDIRECT CheckType = 7
)

func (p CheckType) String() string {
	switch p {
	case CheckType_MOBLIE:
		return "CheckType_MOBLIE"
	case CheckType_QQ:
		return "CheckType_QQ"
	case CheckType_ALIPAY:
		return "CheckType_ALIPAY"
	case CheckType_AMAZON:
		return "CheckType_AMAZON"
	case CheckType_YIHAODIAN:
		return "CheckType_YIHAODIAN"
	case CheckType_JD:
		return "CheckType_JD"
	case CheckType_ALIDIRECT:
		return "CheckType_ALIDIRECT"
	}
	return "<UNSET>"
}

func CheckTypeFromString(s string) (CheckType, error) {
	switch s {
	case "CheckType_MOBLIE":
		return CheckType_MOBLIE, nil
	case "CheckType_QQ":
		return CheckType_QQ, nil
	case "CheckType_ALIPAY":
		return CheckType_ALIPAY, nil
	case "CheckType_AMAZON":
		return CheckType_AMAZON, nil
	case "CheckType_YIHAODIAN":
		return CheckType_YIHAODIAN, nil
	case "CheckType_JD":
		return CheckType_JD, nil
	case "CheckType_ALIDIRECT":
		return CheckType_ALIDIRECT, nil
	}
	return CheckType(math.MinInt32 - 1), fmt.Errorf("not a valid CheckType string")
}

type SosCheckRequest struct {
	Userid         int32  `thrift:"userid,1" json:"userid"`
	ItemId         int32  `thrift:"item_id,2" json:"item_id"`
	Account        string `thrift:"account,3" json:"account"`
	ClientIp       string `thrift:"client_ip,4" json:"client_ip"`
	AliAccountName string `thrift:"ali_account_name,5" json:"ali_account_name"`
	Channel        string `thrift:"channel,6" json:"channel"`
}

func NewSosCheckRequest() *SosCheckRequest {
	return &SosCheckRequest{}
}

func (p *SosCheckRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosCheckRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *SosCheckRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ItemId = v
	}
	return nil
}

func (p *SosCheckRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *SosCheckRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *SosCheckRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AliAccountName = v
	}
	return nil
}

func (p *SosCheckRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *SosCheckRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosCheckRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosCheckRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:userid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:userid: %s", p, err)
	}
	return err
}

func (p *SosCheckRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("item_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:item_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ItemId)); err != nil {
		return fmt.Errorf("%T.item_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:item_id: %s", p, err)
	}
	return err
}

func (p *SosCheckRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account: %s", p, err)
	}
	return err
}

func (p *SosCheckRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:client_ip: %s", p, err)
	}
	return err
}

func (p *SosCheckRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ali_account_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ali_account_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AliAccountName)); err != nil {
		return fmt.Errorf("%T.ali_account_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ali_account_name: %s", p, err)
	}
	return err
}

func (p *SosCheckRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:channel: %s", p, err)
	}
	return err
}

func (p *SosCheckRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosCheckRequest(%+v)", *p)
}

type SosOufeiCallBack struct {
	RetCode   int16  `thrift:"ret_code,1" json:"ret_code"`
	SporderId string `thrift:"sporder_id,2" json:"sporder_id"`
	ErrMsg    string `thrift:"err_msg,3" json:"err_msg"`
}

func NewSosOufeiCallBack() *SosOufeiCallBack {
	return &SosOufeiCallBack{}
}

func (p *SosOufeiCallBack) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I16 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosOufeiCallBack) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RetCode = v
	}
	return nil
}

func (p *SosOufeiCallBack) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SporderId = v
	}
	return nil
}

func (p *SosOufeiCallBack) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ErrMsg = v
	}
	return nil
}

func (p *SosOufeiCallBack) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosOufeiCallBack"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosOufeiCallBack) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ret_code", thrift.I16, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ret_code: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.RetCode)); err != nil {
		return fmt.Errorf("%T.ret_code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ret_code: %s", p, err)
	}
	return err
}

func (p *SosOufeiCallBack) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sporder_id", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sporder_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SporderId)); err != nil {
		return fmt.Errorf("%T.sporder_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sporder_id: %s", p, err)
	}
	return err
}

func (p *SosOufeiCallBack) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("err_msg", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:err_msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrMsg)); err != nil {
		return fmt.Errorf("%T.err_msg (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:err_msg: %s", p, err)
	}
	return err
}

func (p *SosOufeiCallBack) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosOufeiCallBack(%+v)", *p)
}

type SosAliCallBack struct {
	NotifyTime     string `thrift:"notify_time,1" json:"notify_time"`
	NotifyType     string `thrift:"notify_type,2" json:"notify_type"`
	NotifyId       string `thrift:"notify_id,3" json:"notify_id"`
	SignType       string `thrift:"sign_type,4" json:"sign_type"`
	Sign           string `thrift:"sign,5" json:"sign"`
	BatchNo        string `thrift:"batch_no,6" json:"batch_no"`
	PayUserId      string `thrift:"pay_user_id,7" json:"pay_user_id"`
	PayUserName    string `thrift:"pay_user_name,8" json:"pay_user_name"`
	PayAccountNo   string `thrift:"pay_account_no,9" json:"pay_account_no"`
	SuccessDetails string `thrift:"success_details,10" json:"success_details"`
	FailDetails    string `thrift:"fail_details,11" json:"fail_details"`
}

func NewSosAliCallBack() *SosAliCallBack {
	return &SosAliCallBack{}
}

func (p *SosAliCallBack) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosAliCallBack) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.NotifyTime = v
	}
	return nil
}

func (p *SosAliCallBack) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.NotifyType = v
	}
	return nil
}

func (p *SosAliCallBack) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NotifyId = v
	}
	return nil
}

func (p *SosAliCallBack) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SignType = v
	}
	return nil
}

func (p *SosAliCallBack) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sign = v
	}
	return nil
}

func (p *SosAliCallBack) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.BatchNo = v
	}
	return nil
}

func (p *SosAliCallBack) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PayUserId = v
	}
	return nil
}

func (p *SosAliCallBack) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.PayUserName = v
	}
	return nil
}

func (p *SosAliCallBack) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PayAccountNo = v
	}
	return nil
}

func (p *SosAliCallBack) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.SuccessDetails = v
	}
	return nil
}

func (p *SosAliCallBack) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.FailDetails = v
	}
	return nil
}

func (p *SosAliCallBack) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosAliCallBack"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosAliCallBack) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("notify_time", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:notify_time: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NotifyTime)); err != nil {
		return fmt.Errorf("%T.notify_time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:notify_time: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("notify_type", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:notify_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NotifyType)); err != nil {
		return fmt.Errorf("%T.notify_type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:notify_type: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("notify_id", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:notify_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NotifyId)); err != nil {
		return fmt.Errorf("%T.notify_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:notify_id: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign_type", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sign_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SignType)); err != nil {
		return fmt.Errorf("%T.sign_type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sign_type: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sign: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sign)); err != nil {
		return fmt.Errorf("%T.sign (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sign: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("batch_no", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:batch_no: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BatchNo)); err != nil {
		return fmt.Errorf("%T.batch_no (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:batch_no: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pay_user_id", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:pay_user_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PayUserId)); err != nil {
		return fmt.Errorf("%T.pay_user_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:pay_user_id: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pay_user_name", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:pay_user_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PayUserName)); err != nil {
		return fmt.Errorf("%T.pay_user_name (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:pay_user_name: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pay_account_no", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:pay_account_no: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PayAccountNo)); err != nil {
		return fmt.Errorf("%T.pay_account_no (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:pay_account_no: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success_details", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:success_details: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SuccessDetails)); err != nil {
		return fmt.Errorf("%T.success_details (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:success_details: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fail_details", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:fail_details: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FailDetails)); err != nil {
		return fmt.Errorf("%T.fail_details (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:fail_details: %s", p, err)
	}
	return err
}

func (p *SosAliCallBack) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosAliCallBack(%+v)", *p)
}

type CheckResponse struct {
	Status bool   `thrift:"status,1" json:"status"`
	Msg    string `thrift:"msg,2" json:"msg"`
}

func NewCheckResponse() *CheckResponse {
	return &CheckResponse{}
}

func (p *CheckResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *CheckResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *CheckResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CheckResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *CheckResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *CheckResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckResponse(%+v)", *p)
}

type SosDate struct {
	Year  int16 `thrift:"year,1" json:"year"`
	Month int16 `thrift:"month,2" json:"month"`
	Day   int16 `thrift:"day,3" json:"day"`
}

func NewSosDate() *SosDate {
	return &SosDate{}
}

func (p *SosDate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I16 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I16 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosDate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Year = v
	}
	return nil
}

func (p *SosDate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *SosDate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Day = v
	}
	return nil
}

func (p *SosDate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosDate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosDate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("year", thrift.I16, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:year: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Year)); err != nil {
		return fmt.Errorf("%T.year (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:year: %s", p, err)
	}
	return err
}

func (p *SosDate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("month", thrift.I16, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:month: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Month)); err != nil {
		return fmt.Errorf("%T.month (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:month: %s", p, err)
	}
	return err
}

func (p *SosDate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("day", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:day: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Day)); err != nil {
		return fmt.Errorf("%T.day (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:day: %s", p, err)
	}
	return err
}

func (p *SosDate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosDate(%+v)", *p)
}
