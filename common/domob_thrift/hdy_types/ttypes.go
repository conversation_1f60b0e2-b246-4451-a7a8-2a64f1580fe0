// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

type LandingType int64

const (
	LandingType_LT_UNKNOWN     LandingType = 0
	LandingType_LT_ACTIVITY    LandingType = 1
	LandingType_LT_AGGREGATION LandingType = 2
)

func (p LandingType) String() string {
	switch p {
	case LandingType_LT_UNKNOWN:
		return "LandingType_LT_UNKNOWN"
	case LandingType_LT_ACTIVITY:
		return "LandingType_LT_ACTIVITY"
	case LandingType_LT_AGGREGATION:
		return "LandingType_LT_AGGREGATION"
	}
	return "<UNSET>"
}

func LandingTypeFromString(s string) (LandingType, error) {
	switch s {
	case "LandingType_LT_UNKNOWN":
		return LandingType_LT_UNKNOWN, nil
	case "LandingType_LT_ACTIVITY":
		return LandingType_LT_ACTIVITY, nil
	case "LandingType_LT_AGGREGATION":
		return LandingType_LT_AGGREGATION, nil
	}
	return LandingType(math.MinInt32 - 1), fmt.Errorf("not a valid LandingType string")
}

//操作系统定义
type OSType int64

const (
	OSType_OST_ALL     OSType = 0
	OSType_OST_IOS     OSType = 1
	OSType_OST_ANDROID OSType = 2
)

func (p OSType) String() string {
	switch p {
	case OSType_OST_ALL:
		return "OSType_OST_ALL"
	case OSType_OST_IOS:
		return "OSType_OST_IOS"
	case OSType_OST_ANDROID:
		return "OSType_OST_ANDROID"
	}
	return "<UNSET>"
}

func OSTypeFromString(s string) (OSType, error) {
	switch s {
	case "OSType_OST_ALL":
		return OSType_OST_ALL, nil
	case "OSType_OST_IOS":
		return OSType_OST_IOS, nil
	case "OSType_OST_ANDROID":
		return OSType_OST_ANDROID, nil
	}
	return OSType(math.MinInt32 - 1), fmt.Errorf("not a valid OSType string")
}

//媒体跳转类型
type MediaRedirectType int64

const (
	MediaRedirectType_MRT_UNKNOWN  MediaRedirectType = 0
	MediaRedirectType_MRT_STRATAGE MediaRedirectType = 1
	MediaRedirectType_MRT_pretend  MediaRedirectType = 2
)

func (p MediaRedirectType) String() string {
	switch p {
	case MediaRedirectType_MRT_UNKNOWN:
		return "MediaRedirectType_MRT_UNKNOWN"
	case MediaRedirectType_MRT_STRATAGE:
		return "MediaRedirectType_MRT_STRATAGE"
	case MediaRedirectType_MRT_pretend:
		return "MediaRedirectType_MRT_pretend"
	}
	return "<UNSET>"
}

func MediaRedirectTypeFromString(s string) (MediaRedirectType, error) {
	switch s {
	case "MediaRedirectType_MRT_UNKNOWN":
		return MediaRedirectType_MRT_UNKNOWN, nil
	case "MediaRedirectType_MRT_STRATAGE":
		return MediaRedirectType_MRT_STRATAGE, nil
	case "MediaRedirectType_MRT_pretend":
		return MediaRedirectType_MRT_pretend, nil
	}
	return MediaRedirectType(math.MinInt32 - 1), fmt.Errorf("not a valid MediaRedirectType string")
}

//媒体跳转目标类型
type MediaRedirectTargetType int64

const (
	MediaRedirectTargetType_MRTT_UNKNOWN     MediaRedirectTargetType = 0
	MediaRedirectTargetType_MRTT_LANDING_TAG MediaRedirectTargetType = 1
	MediaRedirectTargetType_MRTT_LANDING     MediaRedirectTargetType = 2
)

func (p MediaRedirectTargetType) String() string {
	switch p {
	case MediaRedirectTargetType_MRTT_UNKNOWN:
		return "MediaRedirectTargetType_MRTT_UNKNOWN"
	case MediaRedirectTargetType_MRTT_LANDING_TAG:
		return "MediaRedirectTargetType_MRTT_LANDING_TAG"
	case MediaRedirectTargetType_MRTT_LANDING:
		return "MediaRedirectTargetType_MRTT_LANDING"
	}
	return "<UNSET>"
}

func MediaRedirectTargetTypeFromString(s string) (MediaRedirectTargetType, error) {
	switch s {
	case "MediaRedirectTargetType_MRTT_UNKNOWN":
		return MediaRedirectTargetType_MRTT_UNKNOWN, nil
	case "MediaRedirectTargetType_MRTT_LANDING_TAG":
		return MediaRedirectTargetType_MRTT_LANDING_TAG, nil
	case "MediaRedirectTargetType_MRTT_LANDING":
		return MediaRedirectTargetType_MRTT_LANDING, nil
	}
	return MediaRedirectTargetType(math.MinInt32 - 1), fmt.Errorf("not a valid MediaRedirectTargetType string")
}

//结算方式定义
type CostType int64

const (
	CostType_CT_UNNKOWN   CostType = 0
	CostType_CT_INNER_CPC CostType = 1
	CostType_CT_INNER_CPM CostType = 2
	CostType_CT_OUTER_CPC CostType = 3
	CostType_CT_OUTER_CPM CostType = 4
	CostType_CT_OUTER_CPT CostType = 5
)

func (p CostType) String() string {
	switch p {
	case CostType_CT_UNNKOWN:
		return "CostType_CT_UNNKOWN"
	case CostType_CT_INNER_CPC:
		return "CostType_CT_INNER_CPC"
	case CostType_CT_INNER_CPM:
		return "CostType_CT_INNER_CPM"
	case CostType_CT_OUTER_CPC:
		return "CostType_CT_OUTER_CPC"
	case CostType_CT_OUTER_CPM:
		return "CostType_CT_OUTER_CPM"
	case CostType_CT_OUTER_CPT:
		return "CostType_CT_OUTER_CPT"
	}
	return "<UNSET>"
}

func CostTypeFromString(s string) (CostType, error) {
	switch s {
	case "CostType_CT_UNNKOWN":
		return CostType_CT_UNNKOWN, nil
	case "CostType_CT_INNER_CPC":
		return CostType_CT_INNER_CPC, nil
	case "CostType_CT_INNER_CPM":
		return CostType_CT_INNER_CPM, nil
	case "CostType_CT_OUTER_CPC":
		return CostType_CT_OUTER_CPC, nil
	case "CostType_CT_OUTER_CPM":
		return CostType_CT_OUTER_CPM, nil
	case "CostType_CT_OUTER_CPT":
		return CostType_CT_OUTER_CPT, nil
	}
	return CostType(math.MinInt32 - 1), fmt.Errorf("not a valid CostType string")
}

type LandingParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewLandingParams() *LandingParams {
	return &LandingParams{}
}

func (p *LandingParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LandingParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *LandingParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LandingParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LandingParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *LandingParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LandingParams(%+v)", *p)
}

type LandingTagParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewLandingTagParams() *LandingTagParams {
	return &LandingTagParams{}
}

func (p *LandingTagParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LandingTagParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *LandingTagParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LandingTagParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LandingTagParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *LandingTagParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LandingTagParams(%+v)", *p)
}

type AdPlacementParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewAdPlacementParams() *AdPlacementParams {
	return &AdPlacementParams{}
}

func (p *AdPlacementParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlacementParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *AdPlacementParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlacementParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlacementParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *AdPlacementParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlacementParams(%+v)", *p)
}

type GuidePlacementParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewGuidePlacementParams() *GuidePlacementParams {
	return &GuidePlacementParams{}
}

func (p *GuidePlacementParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GuidePlacementParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *GuidePlacementParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GuidePlacementParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GuidePlacementParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *GuidePlacementParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GuidePlacementParams(%+v)", *p)
}

type MediaSearchParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewMediaSearchParams() *MediaSearchParams {
	return &MediaSearchParams{}
}

func (p *MediaSearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaSearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *MediaSearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaSearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaSearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *MediaSearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaSearchParams(%+v)", *p)
}

type MediaRedirectSearchParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewMediaRedirectSearchParams() *MediaRedirectSearchParams {
	return &MediaRedirectSearchParams{}
}

func (p *MediaRedirectSearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaRedirectSearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *MediaRedirectSearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaRedirectSearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaRedirectSearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *MediaRedirectSearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaRedirectSearchParams(%+v)", *p)
}

type AdPlacement struct {
	Id        int32  `thrift:"id,1" json:"id"`
	Name      string `thrift:"name,2" json:"name"`
	ImgWidth  int32  `thrift:"imgWidth,3" json:"imgWidth"`
	ImgHeight int32  `thrift:"imgHeight,4" json:"imgHeight"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Rate int32 `thrift:"rate,11" json:"rate"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewAdPlacement() *AdPlacement {
	return &AdPlacement{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdPlacement) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdPlacement) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlacement) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdPlacement) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdPlacement) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ImgWidth = v
	}
	return nil
}

func (p *AdPlacement) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImgHeight = v
	}
	return nil
}

func (p *AdPlacement) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Rate = v
	}
	return nil
}

func (p *AdPlacement) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdPlacement) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *AdPlacement) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *AdPlacement) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlacement"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlacement) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgWidth", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:imgWidth: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgWidth)); err != nil {
		return fmt.Errorf("%T.imgWidth (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:imgWidth: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgHeight", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imgHeight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgHeight)); err != nil {
		return fmt.Errorf("%T.imgHeight (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imgHeight: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rate", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rate)); err != nil {
		return fmt.Errorf("%T.rate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:rate: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdPlacement) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *AdPlacement) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlacement(%+v)", *p)
}

type GuidePlacement struct {
	Id        int32  `thrift:"id,1" json:"id"`
	Name      string `thrift:"name,2" json:"name"`
	ImgWidth  int32  `thrift:"imgWidth,3" json:"imgWidth"`
	ImgHeight int32  `thrift:"imgHeight,4" json:"imgHeight"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewGuidePlacement() *GuidePlacement {
	return &GuidePlacement{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GuidePlacement) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *GuidePlacement) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GuidePlacement) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *GuidePlacement) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *GuidePlacement) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ImgWidth = v
	}
	return nil
}

func (p *GuidePlacement) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImgHeight = v
	}
	return nil
}

func (p *GuidePlacement) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *GuidePlacement) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *GuidePlacement) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *GuidePlacement) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GuidePlacement"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GuidePlacement) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *GuidePlacement) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *GuidePlacement) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgWidth", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:imgWidth: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgWidth)); err != nil {
		return fmt.Errorf("%T.imgWidth (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:imgWidth: %s", p, err)
	}
	return err
}

func (p *GuidePlacement) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgHeight", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imgHeight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgHeight)); err != nil {
		return fmt.Errorf("%T.imgHeight (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imgHeight: %s", p, err)
	}
	return err
}

func (p *GuidePlacement) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *GuidePlacement) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *GuidePlacement) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *GuidePlacement) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GuidePlacement(%+v)", *p)
}

type LandingTag struct {
	Id   int32  `thrift:"id,1" json:"id"`
	Name string `thrift:"name,2" json:"name"`
}

func NewLandingTag() *LandingTag {
	return &LandingTag{}
}

func (p *LandingTag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LandingTag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LandingTag) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *LandingTag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LandingTag"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LandingTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LandingTag) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *LandingTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LandingTag(%+v)", *p)
}

type HdyLanding struct {
	Id          int32       `thrift:"id,1" json:"id"`
	Name        string      `thrift:"name,2" json:"name"`
	Url         string      `thrift:"url,3" json:"url"`
	TypeA1      LandingType `thrift:"type,4" json:"type"`
	IsStartPage bool        `thrift:"isStartPage,5" json:"isStartPage"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	AdPlacements    []*AdPlacement    `thrift:"adPlacements,10" json:"adPlacements"`
	GuidePlacements []*GuidePlacement `thrift:"guidePlacements,11" json:"guidePlacements"`
	Tags            []*LandingTag     `thrift:"tags,12" json:"tags"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewHdyLanding() *HdyLanding {
	return &HdyLanding{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *HdyLanding) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *HdyLanding) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *HdyLanding) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *HdyLanding) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *HdyLanding) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *HdyLanding) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *HdyLanding) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = LandingType(v)
	}
	return nil
}

func (p *HdyLanding) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IsStartPage = v
	}
	return nil
}

func (p *HdyLanding) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdPlacements = make([]*AdPlacement, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewAdPlacement()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.AdPlacements = append(p.AdPlacements, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *HdyLanding) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GuidePlacements = make([]*GuidePlacement, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewGuidePlacement()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.GuidePlacements = append(p.GuidePlacements, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *HdyLanding) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tags = make([]*LandingTag, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewLandingTag()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Tags = append(p.Tags, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *HdyLanding) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *HdyLanding) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *HdyLanding) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *HdyLanding) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("HdyLanding"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *HdyLanding) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *HdyLanding) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *HdyLanding) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:url: %s", p, err)
	}
	return err
}

func (p *HdyLanding) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *HdyLanding) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isStartPage", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:isStartPage: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsStartPage)); err != nil {
		return fmt.Errorf("%T.isStartPage (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:isStartPage: %s", p, err)
	}
	return err
}

func (p *HdyLanding) writeField10(oprot thrift.TProtocol) (err error) {
	if p.AdPlacements != nil {
		if err := oprot.WriteFieldBegin("adPlacements", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:adPlacements: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdPlacements)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdPlacements {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:adPlacements: %s", p, err)
		}
	}
	return err
}

func (p *HdyLanding) writeField11(oprot thrift.TProtocol) (err error) {
	if p.GuidePlacements != nil {
		if err := oprot.WriteFieldBegin("guidePlacements", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:guidePlacements: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.GuidePlacements)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GuidePlacements {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:guidePlacements: %s", p, err)
		}
	}
	return err
}

func (p *HdyLanding) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Tags != nil {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:tags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:tags: %s", p, err)
		}
	}
	return err
}

func (p *HdyLanding) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *HdyLanding) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *HdyLanding) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *HdyLanding) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HdyLanding(%+v)", *p)
}

type MediaRedirect struct {
	Id           int32                   `thrift:"id,1" json:"id"`
	MediaId      int32                   `thrift:"mediaId,2" json:"mediaId"`
	ChannelId    int32                   `thrift:"channelId,3" json:"channelId"`
	RedirectType MediaRedirectType       `thrift:"redirectType,4" json:"redirectType"`
	Location     []string                `thrift:"location,5" json:"location"`
	Device       []string                `thrift:"device,6" json:"device"`
	Os           OSType                  `thrift:"os,7" json:"os"`
	StartTime    int64                   `thrift:"startTime,8" json:"startTime"`
	StopTime     int64                   `thrift:"stopTime,9" json:"stopTime"`
	TargetType   MediaRedirectTargetType `thrift:"targetType,10" json:"targetType"`
	RedirectId   int32                   `thrift:"redirectId,11" json:"redirectId"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewMediaRedirect() *MediaRedirect {
	return &MediaRedirect{
		RedirectType: math.MinInt32 - 1, // unset sentinal value

		Os: math.MinInt32 - 1, // unset sentinal value

		TargetType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaRedirect) IsSetRedirectType() bool {
	return int64(p.RedirectType) != math.MinInt32-1
}

func (p *MediaRedirect) IsSetOs() bool {
	return int64(p.Os) != math.MinInt32-1
}

func (p *MediaRedirect) IsSetTargetType() bool {
	return int64(p.TargetType) != math.MinInt32-1
}

func (p *MediaRedirect) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MediaRedirect) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaRedirect) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *MediaRedirect) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *MediaRedirect) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *MediaRedirect) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RedirectType = MediaRedirectType(v)
	}
	return nil
}

func (p *MediaRedirect) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Location = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Location = append(p.Location, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaRedirect) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Device = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.Device = append(p.Device, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaRedirect) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Os = OSType(v)
	}
	return nil
}

func (p *MediaRedirect) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *MediaRedirect) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.StopTime = v
	}
	return nil
}

func (p *MediaRedirect) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TargetType = MediaRedirectTargetType(v)
	}
	return nil
}

func (p *MediaRedirect) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.RedirectId = v
	}
	return nil
}

func (p *MediaRedirect) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *MediaRedirect) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *MediaRedirect) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *MediaRedirect) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaRedirect"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaRedirect) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mediaId: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channelId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channelId: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectType() {
		if err := oprot.WriteFieldBegin("redirectType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:redirectType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RedirectType)); err != nil {
			return fmt.Errorf("%T.redirectType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:redirectType: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Location != nil {
		if err := oprot.WriteFieldBegin("location", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:location: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Location)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Location {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:location: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Device != nil {
		if err := oprot.WriteFieldBegin("device", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:device: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Device)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Device {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:device: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOs() {
		if err := oprot.WriteFieldBegin("os", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:os: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Os)); err != nil {
			return fmt.Errorf("%T.os (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:os: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:startTime: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stopTime", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:stopTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StopTime)); err != nil {
		return fmt.Errorf("%T.stopTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:stopTime: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetTargetType() {
		if err := oprot.WriteFieldBegin("targetType", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:targetType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TargetType)); err != nil {
			return fmt.Errorf("%T.targetType (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:targetType: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("redirectId", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:redirectId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RedirectId)); err != nil {
		return fmt.Errorf("%T.redirectId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:redirectId: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaRedirect(%+v)", *p)
}

type MediaChannel struct {
	Id        int32            `thrift:"id,1" json:"id"`
	MediaId   int32            `thrift:"mediaId,2" json:"mediaId"`
	Name      string           `thrift:"name,3" json:"name"`
	Redirects []*MediaRedirect `thrift:"redirects,4" json:"redirects"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewMediaChannel() *MediaChannel {
	return &MediaChannel{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaChannel) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MediaChannel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaChannel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *MediaChannel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *MediaChannel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *MediaChannel) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Redirects = make([]*MediaRedirect, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewMediaRedirect()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Redirects = append(p.Redirects, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaChannel) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *MediaChannel) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *MediaChannel) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *MediaChannel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaChannel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaChannel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *MediaChannel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mediaId: %s", p, err)
	}
	return err
}

func (p *MediaChannel) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *MediaChannel) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Redirects != nil {
		if err := oprot.WriteFieldBegin("redirects", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:redirects: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Redirects)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Redirects {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:redirects: %s", p, err)
		}
	}
	return err
}

func (p *MediaChannel) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *MediaChannel) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *MediaChannel) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *MediaChannel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaChannel(%+v)", *p)
}

type Media struct {
	Id          int32           `thrift:"id,1" json:"id"`
	Name        string          `thrift:"name,2" json:"name"`
	TrafficType string          `thrift:"trafficType,3" json:"trafficType"`
	Email       string          `thrift:"email,4" json:"email"`
	DomobBd     string          `thrift:"domobBd,5" json:"domobBd"`
	CostType    CostType        `thrift:"costType,6" json:"costType"`
	CostPrice   int64           `thrift:"costPrice,7" json:"costPrice"`
	Channels    []*MediaChannel `thrift:"channels,8" json:"channels"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewMedia() *Media {
	return &Media{
		CostType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Media) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *Media) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Media) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Media) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Media) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Media) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TrafficType = v
	}
	return nil
}

func (p *Media) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *Media) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DomobBd = v
	}
	return nil
}

func (p *Media) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *Media) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CostPrice = v
	}
	return nil
}

func (p *Media) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Channels = make([]*MediaChannel, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := NewMediaChannel()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.Channels = append(p.Channels, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Media) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Media) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Media) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *Media) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Media"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Media) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Media) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Media) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trafficType", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:trafficType: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrafficType)); err != nil {
		return fmt.Errorf("%T.trafficType (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:trafficType: %s", p, err)
	}
	return err
}

func (p *Media) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:email: %s", p, err)
	}
	return err
}

func (p *Media) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("domobBd", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:domobBd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DomobBd)); err != nil {
		return fmt.Errorf("%T.domobBd (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:domobBd: %s", p, err)
	}
	return err
}

func (p *Media) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCostType() {
		if err := oprot.WriteFieldBegin("costType", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:costType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CostType)); err != nil {
			return fmt.Errorf("%T.costType (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:costType: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costPrice", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:costPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CostPrice)); err != nil {
		return fmt.Errorf("%T.costPrice (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:costPrice: %s", p, err)
	}
	return err
}

func (p *Media) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Channels != nil {
		if err := oprot.WriteFieldBegin("channels", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:channels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Channels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Channels {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:channels: %s", p, err)
		}
	}
	return err
}

func (p *Media) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Media) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Media) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *Media) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Media(%+v)", *p)
}

type Order struct {
	Id            int32  `thrift:"id,1" json:"id"`
	Name          string `thrift:"name,2" json:"name"`
	Note          string `thrift:"note,3" json:"note"`
	DailyClkLimit int64  `thrift:"dailyClkLimit,4" json:"dailyClkLimit"`
	Uid           int32  `thrift:"uid,5" json:"uid"`
	Price         int64  `thrift:"price,6" json:"price"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	Paused     int32                        `thrift:"paused,19" json:"paused"`
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewOrder() *Order {
	return &Order{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Order) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Order) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Order) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Order) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Order) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *Order) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DailyClkLimit = v
	}
	return nil
}

func (p *Order) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Order) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *Order) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *Order) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Order) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Order) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *Order) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Order"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Order) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Order) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Order) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:note: %s", p, err)
	}
	return err
}

func (p *Order) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyClkLimit", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dailyClkLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyClkLimit)); err != nil {
		return fmt.Errorf("%T.dailyClkLimit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dailyClkLimit: %s", p, err)
	}
	return err
}

func (p *Order) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:uid: %s", p, err)
	}
	return err
}

func (p *Order) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:price: %s", p, err)
	}
	return err
}

func (p *Order) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:paused: %s", p, err)
	}
	return err
}

func (p *Order) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Order) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Order) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *Order) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Order(%+v)", *p)
}

type Strategy struct {
	Id             int32         `thrift:"id,1" json:"id"`
	OrderId        int32         `thrift:"OrderId,2" json:"OrderId"`
	Name           string        `thrift:"name,3" json:"name"`
	Tags           []*LandingTag `thrift:"tags,4" json:"tags"`
	MediaBlacklist []int32       `thrift:"mediaBlacklist,5" json:"mediaBlacklist"`
	PlacementId    int32         `thrift:"placementId,6" json:"placementId"`
	DailyClkLimit  int64         `thrift:"dailyClkLimit,7" json:"dailyClkLimit"`
	Location       []string      `thrift:"location,8" json:"location"`
	Device         []string      `thrift:"device,9" json:"device"`
	Os             OSType        `thrift:"os,10" json:"os"`
	StartTime      int64         `thrift:"startTime,11" json:"startTime"`
	StopTime       int64         `thrift:"stopTime,12" json:"stopTime"`
	SponsorId      int32         `thrift:"sponsorId,13" json:"sponsorId"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	Paused     int32                        `thrift:"paused,19" json:"paused"`
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewStrategy() *Strategy {
	return &Strategy{
		Os: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Strategy) IsSetOs() bool {
	return int64(p.Os) != math.MinInt32-1
}

func (p *Strategy) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Strategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Strategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Strategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *Strategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Strategy) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tags = make([]*LandingTag, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewLandingTag()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.Tags = append(p.Tags, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaBlacklist = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.MediaBlacklist = append(p.MediaBlacklist, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PlacementId = v
	}
	return nil
}

func (p *Strategy) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.DailyClkLimit = v
	}
	return nil
}

func (p *Strategy) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Location = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.Location = append(p.Location, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Device = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.Device = append(p.Device, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Strategy) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Os = OSType(v)
	}
	return nil
}

func (p *Strategy) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *Strategy) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.StopTime = v
	}
	return nil
}

func (p *Strategy) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *Strategy) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *Strategy) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Strategy) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Strategy) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *Strategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Strategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Strategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("OrderId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:OrderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.OrderId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:OrderId: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Tags != nil {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:tags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:tags: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField5(oprot thrift.TProtocol) (err error) {
	if p.MediaBlacklist != nil {
		if err := oprot.WriteFieldBegin("mediaBlacklist", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:mediaBlacklist: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaBlacklist)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaBlacklist {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:mediaBlacklist: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:placementId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementId)); err != nil {
		return fmt.Errorf("%T.placementId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:placementId: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyClkLimit", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:dailyClkLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyClkLimit)); err != nil {
		return fmt.Errorf("%T.dailyClkLimit (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:dailyClkLimit: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Location != nil {
		if err := oprot.WriteFieldBegin("location", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:location: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Location)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Location {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:location: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.Device != nil {
		if err := oprot.WriteFieldBegin("device", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:device: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Device)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Device {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:device: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetOs() {
		if err := oprot.WriteFieldBegin("os", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:os: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Os)); err != nil {
			return fmt.Errorf("%T.os (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:os: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:startTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stopTime", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:stopTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StopTime)); err != nil {
		return fmt.Errorf("%T.stopTime (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:stopTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:sponsorId: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:paused: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *Strategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Strategy(%+v)", *p)
}

type Creative struct {
	Id          int32  `thrift:"id,1" json:"id"`
	OrderId     int32  `thrift:"orderId,2" json:"orderId"`
	StrategyId  int32  `thrift:"strategyId,3" json:"strategyId"`
	Name        string `thrift:"name,4" json:"name"`
	Action      string `thrift:"action,5" json:"action"`
	PlacementId int32  `thrift:"placementId,6" json:"placementId"`
	Title       string `thrift:"title,7" json:"title"`
	Url         string `thrift:"url,8" json:"url"`
	Promotion   string `thrift:"promotion,9" json:"promotion"`
	SponsorId   int32  `thrift:"sponsorId,10" json:"sponsorId"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	Paused     int32                        `thrift:"paused,19" json:"paused"`
	CreateTime int64                        `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     enums.StatusWhetherAvailable `thrift:"status,22" json:"status"`
}

func NewCreative() *Creative {
	return &Creative{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Creative) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Creative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Creative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Creative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *Creative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *Creative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Creative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *Creative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PlacementId = v
	}
	return nil
}

func (p *Creative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *Creative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Creative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Promotion = v
	}
	return nil
}

func (p *Creative) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *Creative) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *Creative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Creative) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Creative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *Creative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Creative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Creative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Creative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:orderId: %s", p, err)
	}
	return err
}

func (p *Creative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:strategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:strategyId: %s", p, err)
	}
	return err
}

func (p *Creative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Creative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:action: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Action)); err != nil {
		return fmt.Errorf("%T.action (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:action: %s", p, err)
	}
	return err
}

func (p *Creative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:placementId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementId)); err != nil {
		return fmt.Errorf("%T.placementId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:placementId: %s", p, err)
	}
	return err
}

func (p *Creative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:title: %s", p, err)
	}
	return err
}

func (p *Creative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:url: %s", p, err)
	}
	return err
}

func (p *Creative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotion", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:promotion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Promotion)); err != nil {
		return fmt.Errorf("%T.promotion (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:promotion: %s", p, err)
	}
	return err
}

func (p *Creative) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:sponsorId: %s", p, err)
	}
	return err
}

func (p *Creative) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:paused: %s", p, err)
	}
	return err
}

func (p *Creative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Creative) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Creative) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *Creative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Creative(%+v)", *p)
}

type OrderSearchParams struct {
	Keyword string `thrift:"keyword,1" json:"keyword"`
}

func NewOrderSearchParams() *OrderSearchParams {
	return &OrderSearchParams{}
}

func (p *OrderSearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OrderSearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *OrderSearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OrderSearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OrderSearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *OrderSearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderSearchParams(%+v)", *p)
}

type StrategySearchParams struct {
	Keyword  string  `thrift:"keyword,1" json:"keyword"`
	OrderIds []int32 `thrift:"orderIds,2" json:"orderIds"`
}

func NewStrategySearchParams() *StrategySearchParams {
	return &StrategySearchParams{}
}

func (p *StrategySearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StrategySearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *StrategySearchParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.OrderIds = append(p.OrderIds, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategySearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StrategySearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StrategySearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *StrategySearchParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *StrategySearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrategySearchParams(%+v)", *p)
}

type CreativeSearchParams struct {
	Keyword     string  `thrift:"keyword,1" json:"keyword"`
	OrderIds    []int32 `thrift:"orderIds,2" json:"orderIds"`
	StrategyIds []int32 `thrift:"strategyIds,3" json:"strategyIds"`
}

func NewCreativeSearchParams() *CreativeSearchParams {
	return &CreativeSearchParams{}
}

func (p *CreativeSearchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeSearchParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *CreativeSearchParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.OrderIds = append(p.OrderIds, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeSearchParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StrategyIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.StrategyIds = append(p.StrategyIds, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeSearchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeSearchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeSearchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:keyword: %s", p, err)
	}
	return err
}

func (p *CreativeSearchParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *CreativeSearchParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.StrategyIds != nil {
		if err := oprot.WriteFieldBegin("strategyIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:strategyIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.StrategyIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StrategyIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:strategyIds: %s", p, err)
		}
	}
	return err
}

func (p *CreativeSearchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeSearchParams(%+v)", *p)
}
