// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package qa

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_types"
	"rtb_model_server/common/domob_thrift/adserver_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/mediainfo_types"
	"rtb_model_server/common/domob_thrift/searchui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = adserver_types.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var _ = mediainfo_types.GoUnusedProtection__
var _ = adinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

type LargeIdInt common.LargeIdInt

type SDKVersion common.SDKVersion

type SDKPlatform common.SDKPlatform

type JailBreakCode common.JailBreakCode

type AdPlacementType common.AdPlacementType

type TimeInt common.TimeInt

type IdInt common.IdInt

type ResourceGroup *common.ResourceGroup

type CreativeIdInt common.IdInt

type MediaIdInt mediainfo_types.MediaIdInt

type Media *mediainfo_types.Media

type PlacementIdInt mediainfo_types.PlacementIdInt

type Placement *mediainfo_types.Placement

type AdPlanIdInt adinfo_types.AdPlanIdInt

type AdPlan *adinfo_types.AdPlan

type AdStrategyIdInt adinfo_types.AdStrategyIdInt

type AdStrategy *adinfo_types.AdStrategy

type AdCreativeIdInt adinfo_types.AdCreativeIdInt

type AdCreative *adinfo_types.AdCreative

type AdEventReport *searchui_types.AdEventReport

type AdClick *searchui_types.AdClick

type ConfWrapper struct {
	ConfStr  string            `thrift:"confStr,1" json:"confStr"`
	ConfList []string          `thrift:"confList,2" json:"confList"`
	ConfMap  map[string]string `thrift:"confMap,3" json:"confMap"`
}

func NewConfWrapper() *ConfWrapper {
	return &ConfWrapper{}
}

func (p *ConfWrapper) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConfWrapper) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ConfStr = v
	}
	return nil
}

func (p *ConfWrapper) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ConfList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ConfList = append(p.ConfList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ConfWrapper) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ConfMap = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.ConfMap[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ConfWrapper) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ConfWrapper"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConfWrapper) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confStr", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:confStr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ConfStr)); err != nil {
		return fmt.Errorf("%T.confStr (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:confStr: %s", p, err)
	}
	return err
}

func (p *ConfWrapper) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ConfList != nil {
		if err := oprot.WriteFieldBegin("confList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:confList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ConfList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ConfList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:confList: %s", p, err)
		}
	}
	return err
}

func (p *ConfWrapper) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ConfMap != nil {
		if err := oprot.WriteFieldBegin("confMap", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:confMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ConfMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ConfMap {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:confMap: %s", p, err)
		}
	}
	return err
}

func (p *ConfWrapper) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfWrapper(%+v)", *p)
}
