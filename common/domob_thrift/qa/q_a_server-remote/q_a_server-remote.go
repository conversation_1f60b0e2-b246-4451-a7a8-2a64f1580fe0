// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"qa"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getCurrentStatus()")
	fmt.Fprintln(os.Stderr, "   getNoAdMediaConf(IdInt pmid, bool isOnline)")
	fmt.Fprintln(os.<PERSON>derr, "  Media getMedia(IdInt mid, bool isOnline)")
	fmt.Fprintln(os.Stderr, "  Placement getPlacement(IdInt pmid, bool isOnline)")
	fmt.Fprintln(os.Stderr, "  AdPlan getAdPlan(IdInt pid, bool isOnline)")
	fmt.Fprintln(os.Stderr, "  AdStrategy getAdStrategy(IdInt sid, bool isOnline)")
	fmt.Fprintln(os.Stderr, "  AdCreative getAdCreative(IdInt cid, bool isOnline)")
	fmt.Fprintln(os.Stderr, "   getAllMedias(bool isOnline)")
	fmt.Fprintln(os.Stderr, "   getAllPlacements(bool isOnline)")
	fmt.Fprintln(os.Stderr, "   getAllPlans(bool isOnline)")
	fmt.Fprintln(os.Stderr, "   getAllStrategies(bool isOnline)")
	fmt.Fprintln(os.Stderr, "   getAllCreatives(bool isOnline)")
	fmt.Fprintln(os.Stderr, "  AdEventReport decodeEventTracker(string tracker)")
	fmt.Fprintln(os.Stderr, "  AdClick decodeClickTracker(string tracker)")
	fmt.Fprintln(os.Stderr, "  string decodePlacementId(string placementid)")
	fmt.Fprintln(os.Stderr, "  string decodePublishId(string publishId)")
	fmt.Fprintln(os.Stderr, "  bool sendSMS(string phonenumber, string message)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := qa.NewQAServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getCurrentStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCurrentStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCurrentStatus())
		fmt.Print("\n")
		break
	case "getNoAdMediaConf":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetNoAdMediaConf requires 2 args")
			flag.Usage()
		}
		tmp0, err87 := (strconv.Atoi(flag.Arg(1)))
		if err87 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := qa.IdInt(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetNoAdMediaConf(value0, value1))
		fmt.Print("\n")
		break
	case "getMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMedia requires 2 args")
			flag.Usage()
		}
		tmp0, err89 := (strconv.Atoi(flag.Arg(1)))
		if err89 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := qa.IdInt(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetMedia(value0, value1))
		fmt.Print("\n")
		break
	case "getPlacement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlacement requires 2 args")
			flag.Usage()
		}
		tmp0, err91 := (strconv.Atoi(flag.Arg(1)))
		if err91 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := qa.IdInt(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetPlacement(value0, value1))
		fmt.Print("\n")
		break
	case "getAdPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPlan requires 2 args")
			flag.Usage()
		}
		tmp0, err93 := (strconv.Atoi(flag.Arg(1)))
		if err93 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := qa.IdInt(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetAdPlan(value0, value1))
		fmt.Print("\n")
		break
	case "getAdStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategy requires 2 args")
			flag.Usage()
		}
		tmp0, err95 := (strconv.Atoi(flag.Arg(1)))
		if err95 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := qa.IdInt(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetAdStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "getAdCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCreative requires 2 args")
			flag.Usage()
		}
		tmp0, err97 := (strconv.Atoi(flag.Arg(1)))
		if err97 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := qa.IdInt(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetAdCreative(value0, value1))
		fmt.Print("\n")
		break
	case "getAllMedias":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllMedias requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1) == "true"
		value0 := argvalue0
		fmt.Print(client.GetAllMedias(value0))
		fmt.Print("\n")
		break
	case "getAllPlacements":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllPlacements requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1) == "true"
		value0 := argvalue0
		fmt.Print(client.GetAllPlacements(value0))
		fmt.Print("\n")
		break
	case "getAllPlans":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllPlans requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1) == "true"
		value0 := argvalue0
		fmt.Print(client.GetAllPlans(value0))
		fmt.Print("\n")
		break
	case "getAllStrategies":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllStrategies requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1) == "true"
		value0 := argvalue0
		fmt.Print(client.GetAllStrategies(value0))
		fmt.Print("\n")
		break
	case "getAllCreatives":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAllCreatives requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1) == "true"
		value0 := argvalue0
		fmt.Print(client.GetAllCreatives(value0))
		fmt.Print("\n")
		break
	case "decodeEventTracker":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DecodeEventTracker requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.DecodeEventTracker(value0))
		fmt.Print("\n")
		break
	case "decodeClickTracker":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DecodeClickTracker requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.DecodeClickTracker(value0))
		fmt.Print("\n")
		break
	case "decodePlacementId":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DecodePlacementId requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.DecodePlacementId(value0))
		fmt.Print("\n")
		break
	case "decodePublishId":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DecodePublishId requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.DecodePublishId(value0))
		fmt.Print("\n")
		break
	case "sendSMS":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SendSMS requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SendSMS(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
