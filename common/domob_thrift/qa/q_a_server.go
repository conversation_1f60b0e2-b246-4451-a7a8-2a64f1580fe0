// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package qa

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_types"
	"rtb_model_server/common/domob_thrift/adserver_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/mediainfo_types"
	"rtb_model_server/common/domob_thrift/searchui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = adserver_types.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var _ = mediainfo_types.GoUnusedProtection__
var _ = adinfo_types.GoUnusedProtection__

type QAServer interface { //QAServer服务

	//
	// 获得当前qaserver的运行状态
	//
	GetCurrentStatus() (r map[string]IdInt, err error)
	// 返回没填充的媒体配置信息
	// 目前key的取值有：
	// "houseAdRatio"
	// "closeDomobAd"
	// "adCategoryFilter"
	//
	// Parameters:
	//  - Pmid
	//  - IsOnline
	GetNoAdMediaConf(pmid IdInt, isOnline bool) (r map[string]string, err error)
	// 返回线上/线下媒体信息
	//
	//
	// Parameters:
	//  - Mid
	//  - IsOnline
	GetMedia(mid IdInt, isOnline bool) (r *mediainfo_types.Media, err error)
	// 返回线上/线下广告位信息
	//
	//
	// Parameters:
	//  - Pmid
	//  - IsOnline
	GetPlacement(pmid IdInt, isOnline bool) (r *mediainfo_types.Placement, err error)
	// 返回线上/线下广告计划信息
	//
	//
	// Parameters:
	//  - Pid
	//  - IsOnline
	GetAdPlan(pid IdInt, isOnline bool) (r *adinfo_types.AdPlan, err error)
	// 返回线上/线下广告策略信息
	//
	//
	// Parameters:
	//  - Sid
	//  - IsOnline
	GetAdStrategy(sid IdInt, isOnline bool) (r *adinfo_types.AdStrategy, err error)
	// 返回线上/线下广告创意信息
	//
	//
	// Parameters:
	//  - Cid
	//  - IsOnline
	GetAdCreative(cid IdInt, isOnline bool) (r *adinfo_types.AdCreative, err error)
	// 拿到最新的媒体数据
	//
	// Parameters:
	//  - IsOnline
	GetAllMedias(isOnline bool) (r map[MediaIdInt]*mediainfo_types.Media, err error)
	// 拿到最新的广告位数据
	//
	// Parameters:
	//  - IsOnline
	GetAllPlacements(isOnline bool) (r map[PlacementIdInt]*mediainfo_types.Placement, err error)
	// 拿到最新的广告计划数据
	//
	// Parameters:
	//  - IsOnline
	GetAllPlans(isOnline bool) (r map[AdPlanIdInt]*adinfo_types.AdPlan, err error)
	// 拿到最新的广告策略数据
	//
	// Parameters:
	//  - IsOnline
	GetAllStrategies(isOnline bool) (r map[AdStrategyIdInt]*adinfo_types.AdStrategy, err error)
	// 拿到最新的广告创意数据
	//
	// Parameters:
	//  - IsOnline
	GetAllCreatives(isOnline bool) (r map[AdCreativeIdInt]*adinfo_types.AdCreative, err error)
	// 反解事件Tracker
	//
	// Parameters:
	//  - Tracker
	DecodeEventTracker(tracker string) (r *searchui_types.AdEventReport, err error)
	// 反解点击（展现）tracker
	//
	// Parameters:
	//  - Tracker
	DecodeClickTracker(tracker string) (r *searchui_types.AdClick, err error)
	// 反解ppid
	//
	// Parameters:
	//  - Placementid
	DecodePlacementId(placementid string) (r string, err error)
	// 反解publishid
	//
	// Parameters:
	//  - PublishId
	DecodePublishId(publishId string) (r string, err error)
	// Parameters:
	//  - Phonenumber
	//  - Message
	SendSMS(phonenumber string, message string) (r bool, err error)
}

//QAServer服务
type QAServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewQAServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *QAServerClient {
	return &QAServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewQAServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *QAServerClient {
	return &QAServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

//
// 获得当前qaserver的运行状态
//
func (p *QAServerClient) GetCurrentStatus() (r map[string]IdInt, err error) {
	if err = p.sendGetCurrentStatus(); err != nil {
		return
	}
	return p.recvGetCurrentStatus()
}

func (p *QAServerClient) sendGetCurrentStatus() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCurrentStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args3 := NewGetCurrentStatusArgs()
	if err = args3.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetCurrentStatus() (value map[string]IdInt, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error5 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error6 error
		error6, err = error5.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error6
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result4 := NewGetCurrentStatusResult()
	if err = result4.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result4.Success
	return
}

// 返回没填充的媒体配置信息
// 目前key的取值有：
// "houseAdRatio"
// "closeDomobAd"
// "adCategoryFilter"
//
// Parameters:
//  - Pmid
//  - IsOnline
func (p *QAServerClient) GetNoAdMediaConf(pmid IdInt, isOnline bool) (r map[string]string, err error) {
	if err = p.sendGetNoAdMediaConf(pmid, isOnline); err != nil {
		return
	}
	return p.recvGetNoAdMediaConf()
}

func (p *QAServerClient) sendGetNoAdMediaConf(pmid IdInt, isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getNoAdMediaConf", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args7 := NewGetNoAdMediaConfArgs()
	args7.Pmid = pmid
	args7.IsOnline = isOnline
	if err = args7.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetNoAdMediaConf() (value map[string]string, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error9 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error10 error
		error10, err = error9.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error10
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result8 := NewGetNoAdMediaConfResult()
	if err = result8.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result8.Success
	return
}

// 返回线上/线下媒体信息
//
//
// Parameters:
//  - Mid
//  - IsOnline
func (p *QAServerClient) GetMedia(mid IdInt, isOnline bool) (r *mediainfo_types.Media, err error) {
	if err = p.sendGetMedia(mid, isOnline); err != nil {
		return
	}
	return p.recvGetMedia()
}

func (p *QAServerClient) sendGetMedia(mid IdInt, isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMedia", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args11 := NewGetMediaArgs()
	args11.Mid = mid
	args11.IsOnline = isOnline
	if err = args11.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetMedia() (value *mediainfo_types.Media, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error13 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error14 error
		error14, err = error13.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error14
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result12 := NewGetMediaResult()
	if err = result12.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result12.Success
	return
}

// 返回线上/线下广告位信息
//
//
// Parameters:
//  - Pmid
//  - IsOnline
func (p *QAServerClient) GetPlacement(pmid IdInt, isOnline bool) (r *mediainfo_types.Placement, err error) {
	if err = p.sendGetPlacement(pmid, isOnline); err != nil {
		return
	}
	return p.recvGetPlacement()
}

func (p *QAServerClient) sendGetPlacement(pmid IdInt, isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlacement", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args15 := NewGetPlacementArgs()
	args15.Pmid = pmid
	args15.IsOnline = isOnline
	if err = args15.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetPlacement() (value *mediainfo_types.Placement, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error17 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error18 error
		error18, err = error17.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error18
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result16 := NewGetPlacementResult()
	if err = result16.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result16.Success
	return
}

// 返回线上/线下广告计划信息
//
//
// Parameters:
//  - Pid
//  - IsOnline
func (p *QAServerClient) GetAdPlan(pid IdInt, isOnline bool) (r *adinfo_types.AdPlan, err error) {
	if err = p.sendGetAdPlan(pid, isOnline); err != nil {
		return
	}
	return p.recvGetAdPlan()
}

func (p *QAServerClient) sendGetAdPlan(pid IdInt, isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAdPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args19 := NewGetAdPlanArgs()
	args19.Pid = pid
	args19.IsOnline = isOnline
	if err = args19.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAdPlan() (value *adinfo_types.AdPlan, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error21 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error22 error
		error22, err = error21.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error22
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result20 := NewGetAdPlanResult()
	if err = result20.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result20.Success
	return
}

// 返回线上/线下广告策略信息
//
//
// Parameters:
//  - Sid
//  - IsOnline
func (p *QAServerClient) GetAdStrategy(sid IdInt, isOnline bool) (r *adinfo_types.AdStrategy, err error) {
	if err = p.sendGetAdStrategy(sid, isOnline); err != nil {
		return
	}
	return p.recvGetAdStrategy()
}

func (p *QAServerClient) sendGetAdStrategy(sid IdInt, isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAdStrategy", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args23 := NewGetAdStrategyArgs()
	args23.Sid = sid
	args23.IsOnline = isOnline
	if err = args23.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAdStrategy() (value *adinfo_types.AdStrategy, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error25 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error26 error
		error26, err = error25.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error26
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result24 := NewGetAdStrategyResult()
	if err = result24.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result24.Success
	return
}

// 返回线上/线下广告创意信息
//
//
// Parameters:
//  - Cid
//  - IsOnline
func (p *QAServerClient) GetAdCreative(cid IdInt, isOnline bool) (r *adinfo_types.AdCreative, err error) {
	if err = p.sendGetAdCreative(cid, isOnline); err != nil {
		return
	}
	return p.recvGetAdCreative()
}

func (p *QAServerClient) sendGetAdCreative(cid IdInt, isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAdCreative", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args27 := NewGetAdCreativeArgs()
	args27.Cid = cid
	args27.IsOnline = isOnline
	if err = args27.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAdCreative() (value *adinfo_types.AdCreative, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error29 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error30 error
		error30, err = error29.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error30
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result28 := NewGetAdCreativeResult()
	if err = result28.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result28.Success
	return
}

// 拿到最新的媒体数据
//
// Parameters:
//  - IsOnline
func (p *QAServerClient) GetAllMedias(isOnline bool) (r map[MediaIdInt]*mediainfo_types.Media, err error) {
	if err = p.sendGetAllMedias(isOnline); err != nil {
		return
	}
	return p.recvGetAllMedias()
}

func (p *QAServerClient) sendGetAllMedias(isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllMedias", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args31 := NewGetAllMediasArgs()
	args31.IsOnline = isOnline
	if err = args31.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAllMedias() (value map[MediaIdInt]*mediainfo_types.Media, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error33 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error34 error
		error34, err = error33.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error34
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result32 := NewGetAllMediasResult()
	if err = result32.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result32.Success
	return
}

// 拿到最新的广告位数据
//
// Parameters:
//  - IsOnline
func (p *QAServerClient) GetAllPlacements(isOnline bool) (r map[PlacementIdInt]*mediainfo_types.Placement, err error) {
	if err = p.sendGetAllPlacements(isOnline); err != nil {
		return
	}
	return p.recvGetAllPlacements()
}

func (p *QAServerClient) sendGetAllPlacements(isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllPlacements", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args35 := NewGetAllPlacementsArgs()
	args35.IsOnline = isOnline
	if err = args35.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAllPlacements() (value map[PlacementIdInt]*mediainfo_types.Placement, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error37 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error38 error
		error38, err = error37.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error38
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result36 := NewGetAllPlacementsResult()
	if err = result36.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result36.Success
	return
}

// 拿到最新的广告计划数据
//
// Parameters:
//  - IsOnline
func (p *QAServerClient) GetAllPlans(isOnline bool) (r map[AdPlanIdInt]*adinfo_types.AdPlan, err error) {
	if err = p.sendGetAllPlans(isOnline); err != nil {
		return
	}
	return p.recvGetAllPlans()
}

func (p *QAServerClient) sendGetAllPlans(isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllPlans", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args39 := NewGetAllPlansArgs()
	args39.IsOnline = isOnline
	if err = args39.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAllPlans() (value map[AdPlanIdInt]*adinfo_types.AdPlan, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error41 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error42 error
		error42, err = error41.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error42
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result40 := NewGetAllPlansResult()
	if err = result40.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result40.Success
	return
}

// 拿到最新的广告策略数据
//
// Parameters:
//  - IsOnline
func (p *QAServerClient) GetAllStrategies(isOnline bool) (r map[AdStrategyIdInt]*adinfo_types.AdStrategy, err error) {
	if err = p.sendGetAllStrategies(isOnline); err != nil {
		return
	}
	return p.recvGetAllStrategies()
}

func (p *QAServerClient) sendGetAllStrategies(isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllStrategies", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args43 := NewGetAllStrategiesArgs()
	args43.IsOnline = isOnline
	if err = args43.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAllStrategies() (value map[AdStrategyIdInt]*adinfo_types.AdStrategy, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error45 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error46 error
		error46, err = error45.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error46
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result44 := NewGetAllStrategiesResult()
	if err = result44.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result44.Success
	return
}

// 拿到最新的广告创意数据
//
// Parameters:
//  - IsOnline
func (p *QAServerClient) GetAllCreatives(isOnline bool) (r map[AdCreativeIdInt]*adinfo_types.AdCreative, err error) {
	if err = p.sendGetAllCreatives(isOnline); err != nil {
		return
	}
	return p.recvGetAllCreatives()
}

func (p *QAServerClient) sendGetAllCreatives(isOnline bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllCreatives", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args47 := NewGetAllCreativesArgs()
	args47.IsOnline = isOnline
	if err = args47.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvGetAllCreatives() (value map[AdCreativeIdInt]*adinfo_types.AdCreative, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error49 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error50 error
		error50, err = error49.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error50
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result48 := NewGetAllCreativesResult()
	if err = result48.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result48.Success
	return
}

// 反解事件Tracker
//
// Parameters:
//  - Tracker
func (p *QAServerClient) DecodeEventTracker(tracker string) (r *searchui_types.AdEventReport, err error) {
	if err = p.sendDecodeEventTracker(tracker); err != nil {
		return
	}
	return p.recvDecodeEventTracker()
}

func (p *QAServerClient) sendDecodeEventTracker(tracker string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("decodeEventTracker", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args51 := NewDecodeEventTrackerArgs()
	args51.Tracker = tracker
	if err = args51.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvDecodeEventTracker() (value *searchui_types.AdEventReport, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error53 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error54 error
		error54, err = error53.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error54
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result52 := NewDecodeEventTrackerResult()
	if err = result52.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result52.Success
	return
}

// 反解点击（展现）tracker
//
// Parameters:
//  - Tracker
func (p *QAServerClient) DecodeClickTracker(tracker string) (r *searchui_types.AdClick, err error) {
	if err = p.sendDecodeClickTracker(tracker); err != nil {
		return
	}
	return p.recvDecodeClickTracker()
}

func (p *QAServerClient) sendDecodeClickTracker(tracker string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("decodeClickTracker", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args55 := NewDecodeClickTrackerArgs()
	args55.Tracker = tracker
	if err = args55.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvDecodeClickTracker() (value *searchui_types.AdClick, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error57 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error58 error
		error58, err = error57.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error58
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result56 := NewDecodeClickTrackerResult()
	if err = result56.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result56.Success
	return
}

// 反解ppid
//
// Parameters:
//  - Placementid
func (p *QAServerClient) DecodePlacementId(placementid string) (r string, err error) {
	if err = p.sendDecodePlacementId(placementid); err != nil {
		return
	}
	return p.recvDecodePlacementId()
}

func (p *QAServerClient) sendDecodePlacementId(placementid string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("decodePlacementId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args59 := NewDecodePlacementIdArgs()
	args59.Placementid = placementid
	if err = args59.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvDecodePlacementId() (value string, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error61 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error62 error
		error62, err = error61.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error62
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result60 := NewDecodePlacementIdResult()
	if err = result60.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result60.Success
	return
}

// 反解publishid
//
// Parameters:
//  - PublishId
func (p *QAServerClient) DecodePublishId(publishId string) (r string, err error) {
	if err = p.sendDecodePublishId(publishId); err != nil {
		return
	}
	return p.recvDecodePublishId()
}

func (p *QAServerClient) sendDecodePublishId(publishId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("decodePublishId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args63 := NewDecodePublishIdArgs()
	args63.PublishId = publishId
	if err = args63.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvDecodePublishId() (value string, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error65 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error66 error
		error66, err = error65.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error66
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result64 := NewDecodePublishIdResult()
	if err = result64.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result64.Success
	return
}

// Parameters:
//  - Phonenumber
//  - Message
func (p *QAServerClient) SendSMS(phonenumber string, message string) (r bool, err error) {
	if err = p.sendSendSMS(phonenumber, message); err != nil {
		return
	}
	return p.recvSendSMS()
}

func (p *QAServerClient) sendSendSMS(phonenumber string, message string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendSMS", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args67 := NewSendSMSArgs()
	args67.Phonenumber = phonenumber
	args67.Message = message
	if err = args67.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *QAServerClient) recvSendSMS() (value bool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error69 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error70 error
		error70, err = error69.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error70
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result68 := NewSendSMSResult()
	if err = result68.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result68.Success
	return
}

type QAServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      QAServer
}

func (p *QAServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *QAServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *QAServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewQAServerProcessor(handler QAServer) *QAServerProcessor {

	self71 := &QAServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self71.processorMap["getCurrentStatus"] = &qAServerProcessorGetCurrentStatus{handler: handler}
	self71.processorMap["getNoAdMediaConf"] = &qAServerProcessorGetNoAdMediaConf{handler: handler}
	self71.processorMap["getMedia"] = &qAServerProcessorGetMedia{handler: handler}
	self71.processorMap["getPlacement"] = &qAServerProcessorGetPlacement{handler: handler}
	self71.processorMap["getAdPlan"] = &qAServerProcessorGetAdPlan{handler: handler}
	self71.processorMap["getAdStrategy"] = &qAServerProcessorGetAdStrategy{handler: handler}
	self71.processorMap["getAdCreative"] = &qAServerProcessorGetAdCreative{handler: handler}
	self71.processorMap["getAllMedias"] = &qAServerProcessorGetAllMedias{handler: handler}
	self71.processorMap["getAllPlacements"] = &qAServerProcessorGetAllPlacements{handler: handler}
	self71.processorMap["getAllPlans"] = &qAServerProcessorGetAllPlans{handler: handler}
	self71.processorMap["getAllStrategies"] = &qAServerProcessorGetAllStrategies{handler: handler}
	self71.processorMap["getAllCreatives"] = &qAServerProcessorGetAllCreatives{handler: handler}
	self71.processorMap["decodeEventTracker"] = &qAServerProcessorDecodeEventTracker{handler: handler}
	self71.processorMap["decodeClickTracker"] = &qAServerProcessorDecodeClickTracker{handler: handler}
	self71.processorMap["decodePlacementId"] = &qAServerProcessorDecodePlacementId{handler: handler}
	self71.processorMap["decodePublishId"] = &qAServerProcessorDecodePublishId{handler: handler}
	self71.processorMap["sendSMS"] = &qAServerProcessorSendSMS{handler: handler}
	return self71
}

func (p *QAServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x72 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x72.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x72

}

type qAServerProcessorGetCurrentStatus struct {
	handler QAServer
}

func (p *qAServerProcessorGetCurrentStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCurrentStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCurrentStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCurrentStatusResult()
	if result.Success, err = p.handler.GetCurrentStatus(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCurrentStatus: "+err.Error())
		oprot.WriteMessageBegin("getCurrentStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCurrentStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetNoAdMediaConf struct {
	handler QAServer
}

func (p *qAServerProcessorGetNoAdMediaConf) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetNoAdMediaConfArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getNoAdMediaConf", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetNoAdMediaConfResult()
	if result.Success, err = p.handler.GetNoAdMediaConf(args.Pmid, args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNoAdMediaConf: "+err.Error())
		oprot.WriteMessageBegin("getNoAdMediaConf", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getNoAdMediaConf", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetMedia struct {
	handler QAServer
}

func (p *qAServerProcessorGetMedia) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMediaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMedia", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMediaResult()
	if result.Success, err = p.handler.GetMedia(args.Mid, args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMedia: "+err.Error())
		oprot.WriteMessageBegin("getMedia", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMedia", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetPlacement struct {
	handler QAServer
}

func (p *qAServerProcessorGetPlacement) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlacementArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlacement", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlacementResult()
	if result.Success, err = p.handler.GetPlacement(args.Pmid, args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlacement: "+err.Error())
		oprot.WriteMessageBegin("getPlacement", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlacement", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAdPlan struct {
	handler QAServer
}

func (p *qAServerProcessorGetAdPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAdPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAdPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAdPlanResult()
	if result.Success, err = p.handler.GetAdPlan(args.Pid, args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAdPlan: "+err.Error())
		oprot.WriteMessageBegin("getAdPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAdPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAdStrategy struct {
	handler QAServer
}

func (p *qAServerProcessorGetAdStrategy) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAdStrategyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAdStrategy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAdStrategyResult()
	if result.Success, err = p.handler.GetAdStrategy(args.Sid, args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAdStrategy: "+err.Error())
		oprot.WriteMessageBegin("getAdStrategy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAdStrategy", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAdCreative struct {
	handler QAServer
}

func (p *qAServerProcessorGetAdCreative) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAdCreativeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAdCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAdCreativeResult()
	if result.Success, err = p.handler.GetAdCreative(args.Cid, args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAdCreative: "+err.Error())
		oprot.WriteMessageBegin("getAdCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAdCreative", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAllMedias struct {
	handler QAServer
}

func (p *qAServerProcessorGetAllMedias) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllMediasArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllMedias", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllMediasResult()
	if result.Success, err = p.handler.GetAllMedias(args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllMedias: "+err.Error())
		oprot.WriteMessageBegin("getAllMedias", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllMedias", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAllPlacements struct {
	handler QAServer
}

func (p *qAServerProcessorGetAllPlacements) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllPlacementsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllPlacements", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllPlacementsResult()
	if result.Success, err = p.handler.GetAllPlacements(args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllPlacements: "+err.Error())
		oprot.WriteMessageBegin("getAllPlacements", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllPlacements", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAllPlans struct {
	handler QAServer
}

func (p *qAServerProcessorGetAllPlans) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllPlansArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllPlans", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllPlansResult()
	if result.Success, err = p.handler.GetAllPlans(args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllPlans: "+err.Error())
		oprot.WriteMessageBegin("getAllPlans", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllPlans", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAllStrategies struct {
	handler QAServer
}

func (p *qAServerProcessorGetAllStrategies) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllStrategiesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllStrategies", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllStrategiesResult()
	if result.Success, err = p.handler.GetAllStrategies(args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllStrategies: "+err.Error())
		oprot.WriteMessageBegin("getAllStrategies", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllStrategies", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorGetAllCreatives struct {
	handler QAServer
}

func (p *qAServerProcessorGetAllCreatives) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllCreativesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllCreatives", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllCreativesResult()
	if result.Success, err = p.handler.GetAllCreatives(args.IsOnline); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllCreatives: "+err.Error())
		oprot.WriteMessageBegin("getAllCreatives", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllCreatives", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorDecodeEventTracker struct {
	handler QAServer
}

func (p *qAServerProcessorDecodeEventTracker) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDecodeEventTrackerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("decodeEventTracker", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDecodeEventTrackerResult()
	if result.Success, err = p.handler.DecodeEventTracker(args.Tracker); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing decodeEventTracker: "+err.Error())
		oprot.WriteMessageBegin("decodeEventTracker", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("decodeEventTracker", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorDecodeClickTracker struct {
	handler QAServer
}

func (p *qAServerProcessorDecodeClickTracker) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDecodeClickTrackerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("decodeClickTracker", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDecodeClickTrackerResult()
	if result.Success, err = p.handler.DecodeClickTracker(args.Tracker); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing decodeClickTracker: "+err.Error())
		oprot.WriteMessageBegin("decodeClickTracker", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("decodeClickTracker", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorDecodePlacementId struct {
	handler QAServer
}

func (p *qAServerProcessorDecodePlacementId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDecodePlacementIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("decodePlacementId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDecodePlacementIdResult()
	if result.Success, err = p.handler.DecodePlacementId(args.Placementid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing decodePlacementId: "+err.Error())
		oprot.WriteMessageBegin("decodePlacementId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("decodePlacementId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorDecodePublishId struct {
	handler QAServer
}

func (p *qAServerProcessorDecodePublishId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDecodePublishIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("decodePublishId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDecodePublishIdResult()
	if result.Success, err = p.handler.DecodePublishId(args.PublishId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing decodePublishId: "+err.Error())
		oprot.WriteMessageBegin("decodePublishId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("decodePublishId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type qAServerProcessorSendSMS struct {
	handler QAServer
}

func (p *qAServerProcessorSendSMS) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendSMSArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendSMS", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendSMSResult()
	if result.Success, err = p.handler.SendSMS(args.Phonenumber, args.Message); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendSMS: "+err.Error())
		oprot.WriteMessageBegin("sendSMS", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendSMS", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetCurrentStatusArgs struct {
}

func NewGetCurrentStatusArgs() *GetCurrentStatusArgs {
	return &GetCurrentStatusArgs{}
}

func (p *GetCurrentStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCurrentStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCurrentStatusArgs(%+v)", *p)
}

type GetCurrentStatusResult struct {
	Success map[string]IdInt `thrift:"success,0" json:"success"`
}

func NewGetCurrentStatusResult() *GetCurrentStatusResult {
	return &GetCurrentStatusResult{}
}

func (p *GetCurrentStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string]IdInt, size)
	for i := 0; i < size; i++ {
		var _key73 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key73 = v
		}
		var _val74 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val74 = IdInt(v)
		}
		p.Success[_key73] = _val74
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetCurrentStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCurrentStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCurrentStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCurrentStatusResult(%+v)", *p)
}

type GetNoAdMediaConfArgs struct {
	Pmid     IdInt `thrift:"pmid,1" json:"pmid"`
	IsOnline bool  `thrift:"isOnline,2" json:"isOnline"`
}

func NewGetNoAdMediaConfArgs() *GetNoAdMediaConfArgs {
	return &GetNoAdMediaConfArgs{}
}

func (p *GetNoAdMediaConfArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNoAdMediaConfArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pmid = IdInt(v)
	}
	return nil
}

func (p *GetNoAdMediaConfArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetNoAdMediaConfArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNoAdMediaConf_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNoAdMediaConfArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pmid: %s", p, err)
	}
	return err
}

func (p *GetNoAdMediaConfArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isOnline: %s", p, err)
	}
	return err
}

func (p *GetNoAdMediaConfArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNoAdMediaConfArgs(%+v)", *p)
}

type GetNoAdMediaConfResult struct {
	Success map[string]string `thrift:"success,0" json:"success"`
}

func NewGetNoAdMediaConfResult() *GetNoAdMediaConfResult {
	return &GetNoAdMediaConfResult{}
}

func (p *GetNoAdMediaConfResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNoAdMediaConfResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key75 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key75 = v
		}
		var _val76 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val76 = v
		}
		p.Success[_key75] = _val76
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetNoAdMediaConfResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNoAdMediaConf_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNoAdMediaConfResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetNoAdMediaConfResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNoAdMediaConfResult(%+v)", *p)
}

type GetMediaArgs struct {
	Mid      IdInt `thrift:"mid,1" json:"mid"`
	IsOnline bool  `thrift:"isOnline,2" json:"isOnline"`
}

func NewGetMediaArgs() *GetMediaArgs {
	return &GetMediaArgs{}
}

func (p *GetMediaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = IdInt(v)
	}
	return nil
}

func (p *GetMediaArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetMediaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMedia_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *GetMediaArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isOnline: %s", p, err)
	}
	return err
}

func (p *GetMediaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaArgs(%+v)", *p)
}

type GetMediaResult struct {
	Success *mediainfo_types.Media `thrift:"success,0" json:"success"`
}

func NewGetMediaResult() *GetMediaResult {
	return &GetMediaResult{}
}

func (p *GetMediaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = mediainfo_types.NewMedia()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetMediaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMedia_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaResult(%+v)", *p)
}

type GetPlacementArgs struct {
	Pmid     IdInt `thrift:"pmid,1" json:"pmid"`
	IsOnline bool  `thrift:"isOnline,2" json:"isOnline"`
}

func NewGetPlacementArgs() *GetPlacementArgs {
	return &GetPlacementArgs{}
}

func (p *GetPlacementArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlacementArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pmid = IdInt(v)
	}
	return nil
}

func (p *GetPlacementArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetPlacementArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlacement_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlacementArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pmid: %s", p, err)
	}
	return err
}

func (p *GetPlacementArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isOnline: %s", p, err)
	}
	return err
}

func (p *GetPlacementArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlacementArgs(%+v)", *p)
}

type GetPlacementResult struct {
	Success *mediainfo_types.Placement `thrift:"success,0" json:"success"`
}

func NewGetPlacementResult() *GetPlacementResult {
	return &GetPlacementResult{}
}

func (p *GetPlacementResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlacementResult) readField0(iprot thrift.TProtocol) error {
	p.Success = mediainfo_types.NewPlacement()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPlacementResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlacement_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlacementResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlacementResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlacementResult(%+v)", *p)
}

type GetAdPlanArgs struct {
	Pid      IdInt `thrift:"pid,1" json:"pid"`
	IsOnline bool  `thrift:"isOnline,2" json:"isOnline"`
}

func NewGetAdPlanArgs() *GetAdPlanArgs {
	return &GetAdPlanArgs{}
}

func (p *GetAdPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdPlanArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pid = IdInt(v)
	}
	return nil
}

func (p *GetAdPlanArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAdPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAdPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pid: %s", p, err)
	}
	return err
}

func (p *GetAdPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAdPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdPlanArgs(%+v)", *p)
}

type GetAdPlanResult struct {
	Success *adinfo_types.AdPlan `thrift:"success,0" json:"success"`
}

func NewGetAdPlanResult() *GetAdPlanResult {
	return &GetAdPlanResult{}
}

func (p *GetAdPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdPlanResult) readField0(iprot thrift.TProtocol) error {
	p.Success = adinfo_types.NewAdPlan()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAdPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAdPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdPlanResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAdPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdPlanResult(%+v)", *p)
}

type GetAdStrategyArgs struct {
	Sid      IdInt `thrift:"sid,1" json:"sid"`
	IsOnline bool  `thrift:"isOnline,2" json:"isOnline"`
}

func NewGetAdStrategyArgs() *GetAdStrategyArgs {
	return &GetAdStrategyArgs{}
}

func (p *GetAdStrategyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdStrategyArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Sid = IdInt(v)
	}
	return nil
}

func (p *GetAdStrategyArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAdStrategyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAdStrategy_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdStrategyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sid: %s", p, err)
	}
	return err
}

func (p *GetAdStrategyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAdStrategyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdStrategyArgs(%+v)", *p)
}

type GetAdStrategyResult struct {
	Success *adinfo_types.AdStrategy `thrift:"success,0" json:"success"`
}

func NewGetAdStrategyResult() *GetAdStrategyResult {
	return &GetAdStrategyResult{}
}

func (p *GetAdStrategyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdStrategyResult) readField0(iprot thrift.TProtocol) error {
	p.Success = adinfo_types.NewAdStrategy()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAdStrategyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAdStrategy_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdStrategyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAdStrategyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdStrategyResult(%+v)", *p)
}

type GetAdCreativeArgs struct {
	Cid      IdInt `thrift:"cid,1" json:"cid"`
	IsOnline bool  `thrift:"isOnline,2" json:"isOnline"`
}

func NewGetAdCreativeArgs() *GetAdCreativeArgs {
	return &GetAdCreativeArgs{}
}

func (p *GetAdCreativeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdCreativeArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Cid = IdInt(v)
	}
	return nil
}

func (p *GetAdCreativeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAdCreativeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAdCreative_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdCreativeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cid: %s", p, err)
	}
	return err
}

func (p *GetAdCreativeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAdCreativeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdCreativeArgs(%+v)", *p)
}

type GetAdCreativeResult struct {
	Success *adinfo_types.AdCreative `thrift:"success,0" json:"success"`
}

func NewGetAdCreativeResult() *GetAdCreativeResult {
	return &GetAdCreativeResult{}
}

func (p *GetAdCreativeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAdCreativeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = adinfo_types.NewAdCreative()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAdCreativeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAdCreative_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAdCreativeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAdCreativeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAdCreativeResult(%+v)", *p)
}

type GetAllMediasArgs struct {
	IsOnline bool `thrift:"isOnline,1" json:"isOnline"`
}

func NewGetAllMediasArgs() *GetAllMediasArgs {
	return &GetAllMediasArgs{}
}

func (p *GetAllMediasArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllMediasArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAllMediasArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllMedias_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllMediasArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAllMediasArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllMediasArgs(%+v)", *p)
}

type GetAllMediasResult struct {
	Success map[MediaIdInt]*mediainfo_types.Media `thrift:"success,0" json:"success"`
}

func NewGetAllMediasResult() *GetAllMediasResult {
	return &GetAllMediasResult{}
}

func (p *GetAllMediasResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllMediasResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[MediaIdInt]*mediainfo_types.Media, size)
	for i := 0; i < size; i++ {
		var _key77 MediaIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key77 = MediaIdInt(v)
		}
		_val78 := mediainfo_types.NewMedia()
		if err := _val78.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val78)
		}
		p.Success[_key77] = _val78
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAllMediasResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllMedias_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllMediasResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllMediasResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllMediasResult(%+v)", *p)
}

type GetAllPlacementsArgs struct {
	IsOnline bool `thrift:"isOnline,1" json:"isOnline"`
}

func NewGetAllPlacementsArgs() *GetAllPlacementsArgs {
	return &GetAllPlacementsArgs{}
}

func (p *GetAllPlacementsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlacementsArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAllPlacementsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllPlacements_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlacementsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAllPlacementsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllPlacementsArgs(%+v)", *p)
}

type GetAllPlacementsResult struct {
	Success map[PlacementIdInt]*mediainfo_types.Placement `thrift:"success,0" json:"success"`
}

func NewGetAllPlacementsResult() *GetAllPlacementsResult {
	return &GetAllPlacementsResult{}
}

func (p *GetAllPlacementsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlacementsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[PlacementIdInt]*mediainfo_types.Placement, size)
	for i := 0; i < size; i++ {
		var _key79 PlacementIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key79 = PlacementIdInt(v)
		}
		_val80 := mediainfo_types.NewPlacement()
		if err := _val80.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val80)
		}
		p.Success[_key79] = _val80
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAllPlacementsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllPlacements_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlacementsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllPlacementsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllPlacementsResult(%+v)", *p)
}

type GetAllPlansArgs struct {
	IsOnline bool `thrift:"isOnline,1" json:"isOnline"`
}

func NewGetAllPlansArgs() *GetAllPlansArgs {
	return &GetAllPlansArgs{}
}

func (p *GetAllPlansArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlansArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAllPlansArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllPlans_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlansArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAllPlansArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllPlansArgs(%+v)", *p)
}

type GetAllPlansResult struct {
	Success map[AdPlanIdInt]*adinfo_types.AdPlan `thrift:"success,0" json:"success"`
}

func NewGetAllPlansResult() *GetAllPlansResult {
	return &GetAllPlansResult{}
}

func (p *GetAllPlansResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlansResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[AdPlanIdInt]*adinfo_types.AdPlan, size)
	for i := 0; i < size; i++ {
		var _key81 AdPlanIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key81 = AdPlanIdInt(v)
		}
		_val82 := adinfo_types.NewAdPlan()
		if err := _val82.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val82)
		}
		p.Success[_key81] = _val82
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAllPlansResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllPlans_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllPlansResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllPlansResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllPlansResult(%+v)", *p)
}

type GetAllStrategiesArgs struct {
	IsOnline bool `thrift:"isOnline,1" json:"isOnline"`
}

func NewGetAllStrategiesArgs() *GetAllStrategiesArgs {
	return &GetAllStrategiesArgs{}
}

func (p *GetAllStrategiesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllStrategiesArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAllStrategiesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllStrategies_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllStrategiesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAllStrategiesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllStrategiesArgs(%+v)", *p)
}

type GetAllStrategiesResult struct {
	Success map[AdStrategyIdInt]*adinfo_types.AdStrategy `thrift:"success,0" json:"success"`
}

func NewGetAllStrategiesResult() *GetAllStrategiesResult {
	return &GetAllStrategiesResult{}
}

func (p *GetAllStrategiesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllStrategiesResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[AdStrategyIdInt]*adinfo_types.AdStrategy, size)
	for i := 0; i < size; i++ {
		var _key83 AdStrategyIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key83 = AdStrategyIdInt(v)
		}
		_val84 := adinfo_types.NewAdStrategy()
		if err := _val84.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val84)
		}
		p.Success[_key83] = _val84
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAllStrategiesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllStrategies_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllStrategiesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllStrategiesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllStrategiesResult(%+v)", *p)
}

type GetAllCreativesArgs struct {
	IsOnline bool `thrift:"isOnline,1" json:"isOnline"`
}

func NewGetAllCreativesArgs() *GetAllCreativesArgs {
	return &GetAllCreativesArgs{}
}

func (p *GetAllCreativesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllCreativesArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.IsOnline = v
	}
	return nil
}

func (p *GetAllCreativesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllCreatives_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllCreativesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOnline", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:isOnline: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOnline)); err != nil {
		return fmt.Errorf("%T.isOnline (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:isOnline: %s", p, err)
	}
	return err
}

func (p *GetAllCreativesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllCreativesArgs(%+v)", *p)
}

type GetAllCreativesResult struct {
	Success map[AdCreativeIdInt]*adinfo_types.AdCreative `thrift:"success,0" json:"success"`
}

func NewGetAllCreativesResult() *GetAllCreativesResult {
	return &GetAllCreativesResult{}
}

func (p *GetAllCreativesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllCreativesResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[AdCreativeIdInt]*adinfo_types.AdCreative, size)
	for i := 0; i < size; i++ {
		var _key85 AdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key85 = AdCreativeIdInt(v)
		}
		_val86 := adinfo_types.NewAdCreative()
		if err := _val86.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val86)
		}
		p.Success[_key85] = _val86
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAllCreativesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllCreatives_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllCreativesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllCreativesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllCreativesResult(%+v)", *p)
}

type DecodeEventTrackerArgs struct {
	Tracker string `thrift:"tracker,1" json:"tracker"`
}

func NewDecodeEventTrackerArgs() *DecodeEventTrackerArgs {
	return &DecodeEventTrackerArgs{}
}

func (p *DecodeEventTrackerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodeEventTrackerArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Tracker = v
	}
	return nil
}

func (p *DecodeEventTrackerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodeEventTracker_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodeEventTrackerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tracker", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tracker: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tracker)); err != nil {
		return fmt.Errorf("%T.tracker (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tracker: %s", p, err)
	}
	return err
}

func (p *DecodeEventTrackerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodeEventTrackerArgs(%+v)", *p)
}

type DecodeEventTrackerResult struct {
	Success *searchui_types.AdEventReport `thrift:"success,0" json:"success"`
}

func NewDecodeEventTrackerResult() *DecodeEventTrackerResult {
	return &DecodeEventTrackerResult{}
}

func (p *DecodeEventTrackerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodeEventTrackerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = searchui_types.NewAdEventReport()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DecodeEventTrackerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodeEventTracker_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodeEventTrackerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DecodeEventTrackerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodeEventTrackerResult(%+v)", *p)
}

type DecodeClickTrackerArgs struct {
	Tracker string `thrift:"tracker,1" json:"tracker"`
}

func NewDecodeClickTrackerArgs() *DecodeClickTrackerArgs {
	return &DecodeClickTrackerArgs{}
}

func (p *DecodeClickTrackerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodeClickTrackerArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Tracker = v
	}
	return nil
}

func (p *DecodeClickTrackerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodeClickTracker_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodeClickTrackerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tracker", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tracker: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tracker)); err != nil {
		return fmt.Errorf("%T.tracker (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tracker: %s", p, err)
	}
	return err
}

func (p *DecodeClickTrackerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodeClickTrackerArgs(%+v)", *p)
}

type DecodeClickTrackerResult struct {
	Success *searchui_types.AdClick `thrift:"success,0" json:"success"`
}

func NewDecodeClickTrackerResult() *DecodeClickTrackerResult {
	return &DecodeClickTrackerResult{}
}

func (p *DecodeClickTrackerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodeClickTrackerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = searchui_types.NewAdClick()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DecodeClickTrackerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodeClickTracker_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodeClickTrackerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DecodeClickTrackerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodeClickTrackerResult(%+v)", *p)
}

type DecodePlacementIdArgs struct {
	Placementid string `thrift:"placementid,1" json:"placementid"`
}

func NewDecodePlacementIdArgs() *DecodePlacementIdArgs {
	return &DecodePlacementIdArgs{}
}

func (p *DecodePlacementIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodePlacementIdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Placementid = v
	}
	return nil
}

func (p *DecodePlacementIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodePlacementId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodePlacementIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementid", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:placementid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Placementid)); err != nil {
		return fmt.Errorf("%T.placementid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:placementid: %s", p, err)
	}
	return err
}

func (p *DecodePlacementIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodePlacementIdArgs(%+v)", *p)
}

type DecodePlacementIdResult struct {
	Success string `thrift:"success,0" json:"success"`
}

func NewDecodePlacementIdResult() *DecodePlacementIdResult {
	return &DecodePlacementIdResult{}
}

func (p *DecodePlacementIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodePlacementIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *DecodePlacementIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodePlacementId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodePlacementIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *DecodePlacementIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodePlacementIdResult(%+v)", *p)
}

type DecodePublishIdArgs struct {
	PublishId string `thrift:"publishId,1" json:"publishId"`
}

func NewDecodePublishIdArgs() *DecodePublishIdArgs {
	return &DecodePublishIdArgs{}
}

func (p *DecodePublishIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodePublishIdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PublishId = v
	}
	return nil
}

func (p *DecodePublishIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodePublishId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodePublishIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publishId", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:publishId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PublishId)); err != nil {
		return fmt.Errorf("%T.publishId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:publishId: %s", p, err)
	}
	return err
}

func (p *DecodePublishIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodePublishIdArgs(%+v)", *p)
}

type DecodePublishIdResult struct {
	Success string `thrift:"success,0" json:"success"`
}

func NewDecodePublishIdResult() *DecodePublishIdResult {
	return &DecodePublishIdResult{}
}

func (p *DecodePublishIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DecodePublishIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *DecodePublishIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("decodePublishId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DecodePublishIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *DecodePublishIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DecodePublishIdResult(%+v)", *p)
}

type SendSMSArgs struct {
	Phonenumber string `thrift:"phonenumber,1" json:"phonenumber"`
	Message     string `thrift:"message,2" json:"message"`
}

func NewSendSMSArgs() *SendSMSArgs {
	return &SendSMSArgs{}
}

func (p *SendSMSArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendSMSArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Phonenumber = v
	}
	return nil
}

func (p *SendSMSArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *SendSMSArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendSMS_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendSMSArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phonenumber", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phonenumber: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Phonenumber)); err != nil {
		return fmt.Errorf("%T.phonenumber (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phonenumber: %s", p, err)
	}
	return err
}

func (p *SendSMSArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *SendSMSArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendSMSArgs(%+v)", *p)
}

type SendSMSResult struct {
	Success bool `thrift:"success,0" json:"success"`
}

func NewSendSMSResult() *SendSMSResult {
	return &SendSMSResult{}
}

func (p *SendSMSResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendSMSResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *SendSMSResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendSMS_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendSMSResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *SendSMSResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendSMSResult(%+v)", *p)
}
