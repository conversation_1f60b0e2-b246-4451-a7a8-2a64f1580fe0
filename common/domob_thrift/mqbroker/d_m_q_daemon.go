// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mqbroker

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_event"
	"rtb_model_server/common/domob_thrift/appinfo_event"
	"rtb_model_server/common/domob_thrift/bidmaster_event"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/compass_event"
	"rtb_model_server/common/domob_thrift/data_plus_event"
	"rtb_model_server/common/domob_thrift/dbm_event"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/dmp_event"
	"rtb_model_server/common/domob_thrift/dos_event"
	"rtb_model_server/common/domob_thrift/dpm_event"
	"rtb_model_server/common/domob_thrift/dsp_event"
	"rtb_model_server/common/domob_thrift/event"
	"rtb_model_server/common/domob_thrift/finance_event"
	"rtb_model_server/common/domob_thrift/mediainfo_event"
	"rtb_model_server/common/domob_thrift/mqbroker_event"
	"rtb_model_server/common/domob_thrift/offerwall_types"
	"rtb_model_server/common/domob_thrift/passport_event"
	"rtb_model_server/common/domob_thrift/project_event"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_event"
	"rtb_model_server/common/domob_thrift/searchui_types"
	"rtb_model_server/common/domob_thrift/vico_event"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__
var _ = passport_event.GoUnusedProtection__
var _ = adinfo_event.GoUnusedProtection__
var _ = finance_event.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var _ = mediainfo_event.GoUnusedProtection__
var _ = offerwall_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appinfo_event.GoUnusedProtection__
var _ = rtb_adinfo_event.GoUnusedProtection__
var _ = project_event.GoUnusedProtection__
var _ = dbm_event.GoUnusedProtection__
var _ = dmp_event.GoUnusedProtection__
var _ = dpm_event.GoUnusedProtection__
var _ = dos_event.GoUnusedProtection__
var _ = dsp_event.GoUnusedProtection__
var _ = vico_event.GoUnusedProtection__
var _ = data_plus_event.GoUnusedProtection__
var _ = compass_event.GoUnusedProtection__
var _ = bidmaster_event.GoUnusedProtection__
var _ = mqbroker_event.GoUnusedProtection__

type DMQDaemon interface {
	dm303.DomobService
	//DMQ daemon

	// @since 1.2.0
	// @Description("get &lt;subscriber, latestMessageId&gt; map")
	// @return id of latest message, of each subscriber. "latest" means last processed if no new message, or currently processing.
	GetLatestMessage() (r map[string]string, err error)
	// @since 1.2.0
	// @Description("skip specific message")
	// @param subscriberList, list of subscribers, vacant means all
	// @param messageId, id of message, should be equal to return value of "getLatestMessage"
	//
	// Parameters:
	//  - SubscriberList
	//  - MessageId
	SkipMessage(subscriberList []string, messageId string) (err error)
	// @since 1.2.0
	// @Description("reset counters")
	ResetCounters() (err error)
	// @since 2.0.0
	// @Description("get latest message id of a topic")
	//
	// Parameters:
	//  - TopicId
	GetMaxMessageId(topicId TopicId) (r int64, err error)
	// @since 2.0.0
	// @Description("get offset of a subscriber, use and only use it when subscriber got stucked")
	//
	// Parameters:
	//  - TopicId
	//  - SubscriberName
	GetOffset(topicId TopicId, subscriberName string) (r int64, err error)
	// @since 2.0.0
	// @Description("register topic to mysql mq")
	//
	// Parameters:
	//  - TopicId
	RegisterTopic(topicId TopicId) (r *MQOperationStatus, err error)
	// @since 2.0.0
	// @Description("register subscriber of topicId to mysql mq")
	// @return subid
	//
	// Parameters:
	//  - Subscriber
	RegisterSubscriber(subscriber *SubscriberInfo) (r *MQOperationStatus, err error)
	// @since 2.0.0
	// @Description("deregister topic from mysql mq")
	//
	// Parameters:
	//  - TopicId
	DeregisterTopic(topicId TopicId) (r *MQOperationStatus, err error)
	// @since 2.0.0
	// @Description("deregister topic from mysql mq")
	//
	// Parameters:
	//  - SubscriberName
	DeregisterSubscriber(subscriberName string) (r *MQOperationStatus, err error)
	// @since2.0.0
	// @Description("remove relationship between topicId and subscriber")
	//
	// Parameters:
	//  - TopicId
	//  - SubscriberName
	RemoveSubscribeRelation(topicId TopicId, subscriberName string) (r *MQOperationStatus, err error)
	// @since 2.0.0
	// @Description("get total topic list")
	GetTopicList() (r []TopicId, err error)
	// @since 2.0.0
	// @Description("add subscribe relationship between topicId and subscriber")
	//
	// Parameters:
	//  - TopicId
	//  - SubscriberName
	Subscribe(topicId TopicId, subscriberName string) (r *MQOperationStatus, err error)
	// @since 2.0.0
	// @Description("get subscriber list by topicId")
	//
	// Parameters:
	//  - TopicId
	GetSubscriberByTopic(topicId TopicId) (r []*SubscriberInfo, err error)
	// @since 2.0.0
	// @Description("get topic list by subscriberName")
	//
	// Parameters:
	//  - SubscriberName
	GetTopicBySubscriber(subscriberName string) (r []TopicId, err error)
	// @since 2.0.0
	// @Description("get all subscriber registered")
	GetSubscriberList() (r []*SubscriberInfo, err error)
}

//DMQ daemon
type DMQDaemonClient struct {
	*dm303.DomobServiceClient
}

func NewDMQDaemonClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DMQDaemonClient {
	return &DMQDaemonClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDMQDaemonClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DMQDaemonClient {
	return &DMQDaemonClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// @since 1.2.0
// @Description("get &lt;subscriber, latestMessageId&gt; map")
// @return id of latest message, of each subscriber. "latest" means last processed if no new message, or currently processing.
func (p *DMQDaemonClient) GetLatestMessage() (r map[string]string, err error) {
	if err = p.sendGetLatestMessage(); err != nil {
		return
	}
	return p.recvGetLatestMessage()
}

func (p *DMQDaemonClient) sendGetLatestMessage() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getLatestMessage", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1284 := NewGetLatestMessageArgs()
	if err = args1284.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetLatestMessage() (value map[string]string, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1286 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1287 error
		error1287, err = error1286.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1287
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1285 := NewGetLatestMessageResult()
	if err = result1285.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1285.Success
	return
}

// @since 1.2.0
// @Description("skip specific message")
// @param subscriberList, list of subscribers, vacant means all
// @param messageId, id of message, should be equal to return value of "getLatestMessage"
//
// Parameters:
//  - SubscriberList
//  - MessageId
func (p *DMQDaemonClient) SkipMessage(subscriberList []string, messageId string) (err error) {
	if err = p.sendSkipMessage(subscriberList, messageId); err != nil {
		return
	}
	return p.recvSkipMessage()
}

func (p *DMQDaemonClient) sendSkipMessage(subscriberList []string, messageId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("skipMessage", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1288 := NewSkipMessageArgs()
	args1288.SubscriberList = subscriberList
	args1288.MessageId = messageId
	if err = args1288.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvSkipMessage() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1290 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1291 error
		error1291, err = error1290.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1291
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1289 := NewSkipMessageResult()
	if err = result1289.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// @since 1.2.0
// @Description("reset counters")
func (p *DMQDaemonClient) ResetCounters() (err error) {
	if err = p.sendResetCounters(); err != nil {
		return
	}
	return p.recvResetCounters()
}

func (p *DMQDaemonClient) sendResetCounters() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resetCounters", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1292 := NewResetCountersArgs()
	if err = args1292.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvResetCounters() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1294 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1295 error
		error1295, err = error1294.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1295
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1293 := NewResetCountersResult()
	if err = result1293.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// @since 2.0.0
// @Description("get latest message id of a topic")
//
// Parameters:
//  - TopicId
func (p *DMQDaemonClient) GetMaxMessageId(topicId TopicId) (r int64, err error) {
	if err = p.sendGetMaxMessageId(topicId); err != nil {
		return
	}
	return p.recvGetMaxMessageId()
}

func (p *DMQDaemonClient) sendGetMaxMessageId(topicId TopicId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMaxMessageId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1296 := NewGetMaxMessageIdArgs()
	args1296.TopicId = topicId
	if err = args1296.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetMaxMessageId() (value int64, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1298 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1299 error
		error1299, err = error1298.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1299
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1297 := NewGetMaxMessageIdResult()
	if err = result1297.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1297.Success
	return
}

// @since 2.0.0
// @Description("get offset of a subscriber, use and only use it when subscriber got stucked")
//
// Parameters:
//  - TopicId
//  - SubscriberName
func (p *DMQDaemonClient) GetOffset(topicId TopicId, subscriberName string) (r int64, err error) {
	if err = p.sendGetOffset(topicId, subscriberName); err != nil {
		return
	}
	return p.recvGetOffset()
}

func (p *DMQDaemonClient) sendGetOffset(topicId TopicId, subscriberName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getOffset", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1300 := NewGetOffsetArgs()
	args1300.TopicId = topicId
	args1300.SubscriberName = subscriberName
	if err = args1300.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetOffset() (value int64, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1302 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1303 error
		error1303, err = error1302.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1303
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1301 := NewGetOffsetResult()
	if err = result1301.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1301.Success
	return
}

// @since 2.0.0
// @Description("register topic to mysql mq")
//
// Parameters:
//  - TopicId
func (p *DMQDaemonClient) RegisterTopic(topicId TopicId) (r *MQOperationStatus, err error) {
	if err = p.sendRegisterTopic(topicId); err != nil {
		return
	}
	return p.recvRegisterTopic()
}

func (p *DMQDaemonClient) sendRegisterTopic(topicId TopicId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("registerTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1304 := NewRegisterTopicArgs()
	args1304.TopicId = topicId
	if err = args1304.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvRegisterTopic() (value *MQOperationStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1306 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1307 error
		error1307, err = error1306.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1307
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1305 := NewRegisterTopicResult()
	if err = result1305.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1305.Success
	return
}

// @since 2.0.0
// @Description("register subscriber of topicId to mysql mq")
// @return subid
//
// Parameters:
//  - Subscriber
func (p *DMQDaemonClient) RegisterSubscriber(subscriber *SubscriberInfo) (r *MQOperationStatus, err error) {
	if err = p.sendRegisterSubscriber(subscriber); err != nil {
		return
	}
	return p.recvRegisterSubscriber()
}

func (p *DMQDaemonClient) sendRegisterSubscriber(subscriber *SubscriberInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("registerSubscriber", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1308 := NewRegisterSubscriberArgs()
	args1308.Subscriber = subscriber
	if err = args1308.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvRegisterSubscriber() (value *MQOperationStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1310 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1311 error
		error1311, err = error1310.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1311
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1309 := NewRegisterSubscriberResult()
	if err = result1309.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1309.Success
	return
}

// @since 2.0.0
// @Description("deregister topic from mysql mq")
//
// Parameters:
//  - TopicId
func (p *DMQDaemonClient) DeregisterTopic(topicId TopicId) (r *MQOperationStatus, err error) {
	if err = p.sendDeregisterTopic(topicId); err != nil {
		return
	}
	return p.recvDeregisterTopic()
}

func (p *DMQDaemonClient) sendDeregisterTopic(topicId TopicId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deregisterTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1312 := NewDeregisterTopicArgs()
	args1312.TopicId = topicId
	if err = args1312.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvDeregisterTopic() (value *MQOperationStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1314 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1315 error
		error1315, err = error1314.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1315
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1313 := NewDeregisterTopicResult()
	if err = result1313.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1313.Success
	return
}

// @since 2.0.0
// @Description("deregister topic from mysql mq")
//
// Parameters:
//  - SubscriberName
func (p *DMQDaemonClient) DeregisterSubscriber(subscriberName string) (r *MQOperationStatus, err error) {
	if err = p.sendDeregisterSubscriber(subscriberName); err != nil {
		return
	}
	return p.recvDeregisterSubscriber()
}

func (p *DMQDaemonClient) sendDeregisterSubscriber(subscriberName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deregisterSubscriber", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1316 := NewDeregisterSubscriberArgs()
	args1316.SubscriberName = subscriberName
	if err = args1316.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvDeregisterSubscriber() (value *MQOperationStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1318 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1319 error
		error1319, err = error1318.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1319
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1317 := NewDeregisterSubscriberResult()
	if err = result1317.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1317.Success
	return
}

// @since2.0.0
// @Description("remove relationship between topicId and subscriber")
//
// Parameters:
//  - TopicId
//  - SubscriberName
func (p *DMQDaemonClient) RemoveSubscribeRelation(topicId TopicId, subscriberName string) (r *MQOperationStatus, err error) {
	if err = p.sendRemoveSubscribeRelation(topicId, subscriberName); err != nil {
		return
	}
	return p.recvRemoveSubscribeRelation()
}

func (p *DMQDaemonClient) sendRemoveSubscribeRelation(topicId TopicId, subscriberName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("removeSubscribeRelation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1320 := NewRemoveSubscribeRelationArgs()
	args1320.TopicId = topicId
	args1320.SubscriberName = subscriberName
	if err = args1320.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvRemoveSubscribeRelation() (value *MQOperationStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1322 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1323 error
		error1323, err = error1322.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1323
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1321 := NewRemoveSubscribeRelationResult()
	if err = result1321.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1321.Success
	return
}

// @since 2.0.0
// @Description("get total topic list")
func (p *DMQDaemonClient) GetTopicList() (r []TopicId, err error) {
	if err = p.sendGetTopicList(); err != nil {
		return
	}
	return p.recvGetTopicList()
}

func (p *DMQDaemonClient) sendGetTopicList() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTopicList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1324 := NewGetTopicListArgs()
	if err = args1324.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetTopicList() (value []TopicId, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1326 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1327 error
		error1327, err = error1326.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1327
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1325 := NewGetTopicListResult()
	if err = result1325.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1325.Success
	return
}

// @since 2.0.0
// @Description("add subscribe relationship between topicId and subscriber")
//
// Parameters:
//  - TopicId
//  - SubscriberName
func (p *DMQDaemonClient) Subscribe(topicId TopicId, subscriberName string) (r *MQOperationStatus, err error) {
	if err = p.sendSubscribe(topicId, subscriberName); err != nil {
		return
	}
	return p.recvSubscribe()
}

func (p *DMQDaemonClient) sendSubscribe(topicId TopicId, subscriberName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("subscribe", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1328 := NewSubscribeArgs()
	args1328.TopicId = topicId
	args1328.SubscriberName = subscriberName
	if err = args1328.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvSubscribe() (value *MQOperationStatus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1330 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1331 error
		error1331, err = error1330.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1331
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1329 := NewSubscribeResult()
	if err = result1329.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1329.Success
	return
}

// @since 2.0.0
// @Description("get subscriber list by topicId")
//
// Parameters:
//  - TopicId
func (p *DMQDaemonClient) GetSubscriberByTopic(topicId TopicId) (r []*SubscriberInfo, err error) {
	if err = p.sendGetSubscriberByTopic(topicId); err != nil {
		return
	}
	return p.recvGetSubscriberByTopic()
}

func (p *DMQDaemonClient) sendGetSubscriberByTopic(topicId TopicId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSubscriberByTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1332 := NewGetSubscriberByTopicArgs()
	args1332.TopicId = topicId
	if err = args1332.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetSubscriberByTopic() (value []*SubscriberInfo, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1334 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1335 error
		error1335, err = error1334.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1335
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1333 := NewGetSubscriberByTopicResult()
	if err = result1333.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1333.Success
	return
}

// @since 2.0.0
// @Description("get topic list by subscriberName")
//
// Parameters:
//  - SubscriberName
func (p *DMQDaemonClient) GetTopicBySubscriber(subscriberName string) (r []TopicId, err error) {
	if err = p.sendGetTopicBySubscriber(subscriberName); err != nil {
		return
	}
	return p.recvGetTopicBySubscriber()
}

func (p *DMQDaemonClient) sendGetTopicBySubscriber(subscriberName string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTopicBySubscriber", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1336 := NewGetTopicBySubscriberArgs()
	args1336.SubscriberName = subscriberName
	if err = args1336.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetTopicBySubscriber() (value []TopicId, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1338 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1339 error
		error1339, err = error1338.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1339
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1337 := NewGetTopicBySubscriberResult()
	if err = result1337.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1337.Success
	return
}

// @since 2.0.0
// @Description("get all subscriber registered")
func (p *DMQDaemonClient) GetSubscriberList() (r []*SubscriberInfo, err error) {
	if err = p.sendGetSubscriberList(); err != nil {
		return
	}
	return p.recvGetSubscriberList()
}

func (p *DMQDaemonClient) sendGetSubscriberList() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSubscriberList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1340 := NewGetSubscriberListArgs()
	if err = args1340.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DMQDaemonClient) recvGetSubscriberList() (value []*SubscriberInfo, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1342 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1343 error
		error1343, err = error1342.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1343
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1341 := NewGetSubscriberListResult()
	if err = result1341.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1341.Success
	return
}

type DMQDaemonProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDMQDaemonProcessor(handler DMQDaemon) *DMQDaemonProcessor {
	self1344 := &DMQDaemonProcessor{dm303.NewDomobServiceProcessor(handler)}
	self1344.AddToProcessorMap("getLatestMessage", &dMQDaemonProcessorGetLatestMessage{handler: handler})
	self1344.AddToProcessorMap("skipMessage", &dMQDaemonProcessorSkipMessage{handler: handler})
	self1344.AddToProcessorMap("resetCounters", &dMQDaemonProcessorResetCounters{handler: handler})
	self1344.AddToProcessorMap("getMaxMessageId", &dMQDaemonProcessorGetMaxMessageId{handler: handler})
	self1344.AddToProcessorMap("getOffset", &dMQDaemonProcessorGetOffset{handler: handler})
	self1344.AddToProcessorMap("registerTopic", &dMQDaemonProcessorRegisterTopic{handler: handler})
	self1344.AddToProcessorMap("registerSubscriber", &dMQDaemonProcessorRegisterSubscriber{handler: handler})
	self1344.AddToProcessorMap("deregisterTopic", &dMQDaemonProcessorDeregisterTopic{handler: handler})
	self1344.AddToProcessorMap("deregisterSubscriber", &dMQDaemonProcessorDeregisterSubscriber{handler: handler})
	self1344.AddToProcessorMap("removeSubscribeRelation", &dMQDaemonProcessorRemoveSubscribeRelation{handler: handler})
	self1344.AddToProcessorMap("getTopicList", &dMQDaemonProcessorGetTopicList{handler: handler})
	self1344.AddToProcessorMap("subscribe", &dMQDaemonProcessorSubscribe{handler: handler})
	self1344.AddToProcessorMap("getSubscriberByTopic", &dMQDaemonProcessorGetSubscriberByTopic{handler: handler})
	self1344.AddToProcessorMap("getTopicBySubscriber", &dMQDaemonProcessorGetTopicBySubscriber{handler: handler})
	self1344.AddToProcessorMap("getSubscriberList", &dMQDaemonProcessorGetSubscriberList{handler: handler})
	return self1344
}

type dMQDaemonProcessorGetLatestMessage struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetLatestMessage) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetLatestMessageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getLatestMessage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetLatestMessageResult()
	if result.Success, err = p.handler.GetLatestMessage(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getLatestMessage: "+err.Error())
		oprot.WriteMessageBegin("getLatestMessage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getLatestMessage", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorSkipMessage struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorSkipMessage) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSkipMessageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("skipMessage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSkipMessageResult()
	if err = p.handler.SkipMessage(args.SubscriberList, args.MessageId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing skipMessage: "+err.Error())
		oprot.WriteMessageBegin("skipMessage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("skipMessage", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorResetCounters struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorResetCounters) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResetCountersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resetCounters", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResetCountersResult()
	if err = p.handler.ResetCounters(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resetCounters: "+err.Error())
		oprot.WriteMessageBegin("resetCounters", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resetCounters", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorGetMaxMessageId struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetMaxMessageId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMaxMessageIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMaxMessageId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMaxMessageIdResult()
	if result.Success, err = p.handler.GetMaxMessageId(args.TopicId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMaxMessageId: "+err.Error())
		oprot.WriteMessageBegin("getMaxMessageId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMaxMessageId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorGetOffset struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetOffset) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetOffsetArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetOffsetResult()
	if result.Success, err = p.handler.GetOffset(args.TopicId, args.SubscriberName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getOffset: "+err.Error())
		oprot.WriteMessageBegin("getOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getOffset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorRegisterTopic struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorRegisterTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRegisterTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("registerTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRegisterTopicResult()
	if result.Success, err = p.handler.RegisterTopic(args.TopicId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing registerTopic: "+err.Error())
		oprot.WriteMessageBegin("registerTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("registerTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorRegisterSubscriber struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorRegisterSubscriber) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRegisterSubscriberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("registerSubscriber", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRegisterSubscriberResult()
	if result.Success, err = p.handler.RegisterSubscriber(args.Subscriber); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing registerSubscriber: "+err.Error())
		oprot.WriteMessageBegin("registerSubscriber", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("registerSubscriber", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorDeregisterTopic struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorDeregisterTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeregisterTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deregisterTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeregisterTopicResult()
	if result.Success, err = p.handler.DeregisterTopic(args.TopicId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deregisterTopic: "+err.Error())
		oprot.WriteMessageBegin("deregisterTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deregisterTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorDeregisterSubscriber struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorDeregisterSubscriber) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeregisterSubscriberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deregisterSubscriber", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeregisterSubscriberResult()
	if result.Success, err = p.handler.DeregisterSubscriber(args.SubscriberName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deregisterSubscriber: "+err.Error())
		oprot.WriteMessageBegin("deregisterSubscriber", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deregisterSubscriber", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorRemoveSubscribeRelation struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorRemoveSubscribeRelation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRemoveSubscribeRelationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("removeSubscribeRelation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRemoveSubscribeRelationResult()
	if result.Success, err = p.handler.RemoveSubscribeRelation(args.TopicId, args.SubscriberName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing removeSubscribeRelation: "+err.Error())
		oprot.WriteMessageBegin("removeSubscribeRelation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("removeSubscribeRelation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorGetTopicList struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetTopicList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTopicListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTopicList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTopicListResult()
	if result.Success, err = p.handler.GetTopicList(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTopicList: "+err.Error())
		oprot.WriteMessageBegin("getTopicList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTopicList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorSubscribe struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorSubscribe) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubscribeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("subscribe", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubscribeResult()
	if result.Success, err = p.handler.Subscribe(args.TopicId, args.SubscriberName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing subscribe: "+err.Error())
		oprot.WriteMessageBegin("subscribe", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("subscribe", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorGetSubscriberByTopic struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetSubscriberByTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSubscriberByTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSubscriberByTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSubscriberByTopicResult()
	if result.Success, err = p.handler.GetSubscriberByTopic(args.TopicId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSubscriberByTopic: "+err.Error())
		oprot.WriteMessageBegin("getSubscriberByTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSubscriberByTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorGetTopicBySubscriber struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetTopicBySubscriber) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTopicBySubscriberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTopicBySubscriber", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTopicBySubscriberResult()
	if result.Success, err = p.handler.GetTopicBySubscriber(args.SubscriberName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTopicBySubscriber: "+err.Error())
		oprot.WriteMessageBegin("getTopicBySubscriber", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTopicBySubscriber", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dMQDaemonProcessorGetSubscriberList struct {
	handler DMQDaemon
}

func (p *dMQDaemonProcessorGetSubscriberList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSubscriberListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSubscriberList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSubscriberListResult()
	if result.Success, err = p.handler.GetSubscriberList(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSubscriberList: "+err.Error())
		oprot.WriteMessageBegin("getSubscriberList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSubscriberList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetLatestMessageArgs struct {
}

func NewGetLatestMessageArgs() *GetLatestMessageArgs {
	return &GetLatestMessageArgs{}
}

func (p *GetLatestMessageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLatestMessageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLatestMessage_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLatestMessageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLatestMessageArgs(%+v)", *p)
}

type GetLatestMessageResult struct {
	Success map[string]string `thrift:"success,0" json:"success"`
}

func NewGetLatestMessageResult() *GetLatestMessageResult {
	return &GetLatestMessageResult{}
}

func (p *GetLatestMessageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLatestMessageResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1345 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1345 = v
		}
		var _val1346 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1346 = v
		}
		p.Success[_key1345] = _val1346
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetLatestMessageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLatestMessage_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLatestMessageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetLatestMessageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLatestMessageResult(%+v)", *p)
}

type SkipMessageArgs struct {
	SubscriberList []string `thrift:"subscriberList,1" json:"subscriberList"`
	MessageId      string   `thrift:"messageId,2" json:"messageId"`
}

func NewSkipMessageArgs() *SkipMessageArgs {
	return &SkipMessageArgs{}
}

func (p *SkipMessageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SkipMessageArgs) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SubscriberList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1347 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1347 = v
		}
		p.SubscriberList = append(p.SubscriberList, _elem1347)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SkipMessageArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MessageId = v
	}
	return nil
}

func (p *SkipMessageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("skipMessage_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SkipMessageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SubscriberList != nil {
		if err := oprot.WriteFieldBegin("subscriberList", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:subscriberList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SubscriberList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SubscriberList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:subscriberList: %s", p, err)
		}
	}
	return err
}

func (p *SkipMessageArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("messageId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:messageId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MessageId)); err != nil {
		return fmt.Errorf("%T.messageId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:messageId: %s", p, err)
	}
	return err
}

func (p *SkipMessageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SkipMessageArgs(%+v)", *p)
}

type SkipMessageResult struct {
}

func NewSkipMessageResult() *SkipMessageResult {
	return &SkipMessageResult{}
}

func (p *SkipMessageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SkipMessageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("skipMessage_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SkipMessageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SkipMessageResult(%+v)", *p)
}

type ResetCountersArgs struct {
}

func NewResetCountersArgs() *ResetCountersArgs {
	return &ResetCountersArgs{}
}

func (p *ResetCountersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetCountersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetCounters_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetCountersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetCountersArgs(%+v)", *p)
}

type ResetCountersResult struct {
}

func NewResetCountersResult() *ResetCountersResult {
	return &ResetCountersResult{}
}

func (p *ResetCountersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetCountersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetCounters_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetCountersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetCountersResult(%+v)", *p)
}

type GetMaxMessageIdArgs struct {
	TopicId TopicId `thrift:"topicId,1" json:"topicId"`
}

func NewGetMaxMessageIdArgs() *GetMaxMessageIdArgs {
	return &GetMaxMessageIdArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetMaxMessageIdArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *GetMaxMessageIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMaxMessageIdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *GetMaxMessageIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMaxMessageId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMaxMessageIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *GetMaxMessageIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMaxMessageIdArgs(%+v)", *p)
}

type GetMaxMessageIdResult struct {
	Success int64 `thrift:"success,0" json:"success"`
}

func NewGetMaxMessageIdResult() *GetMaxMessageIdResult {
	return &GetMaxMessageIdResult{}
}

func (p *GetMaxMessageIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMaxMessageIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetMaxMessageIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMaxMessageId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMaxMessageIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetMaxMessageIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMaxMessageIdResult(%+v)", *p)
}

type GetOffsetArgs struct {
	TopicId        TopicId `thrift:"topicId,1" json:"topicId"`
	SubscriberName string  `thrift:"subscriberName,2" json:"subscriberName"`
}

func NewGetOffsetArgs() *GetOffsetArgs {
	return &GetOffsetArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetOffsetArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *GetOffsetArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOffsetArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *GetOffsetArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SubscriberName = v
	}
	return nil
}

func (p *GetOffsetArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOffset_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOffsetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *GetOffsetArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subscriberName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:subscriberName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubscriberName)); err != nil {
		return fmt.Errorf("%T.subscriberName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:subscriberName: %s", p, err)
	}
	return err
}

func (p *GetOffsetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOffsetArgs(%+v)", *p)
}

type GetOffsetResult struct {
	Success int64 `thrift:"success,0" json:"success"`
}

func NewGetOffsetResult() *GetOffsetResult {
	return &GetOffsetResult{}
}

func (p *GetOffsetResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOffsetResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetOffsetResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOffset_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOffsetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetOffsetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOffsetResult(%+v)", *p)
}

type RegisterTopicArgs struct {
	TopicId TopicId `thrift:"topicId,1" json:"topicId"`
}

func NewRegisterTopicArgs() *RegisterTopicArgs {
	return &RegisterTopicArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RegisterTopicArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *RegisterTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterTopicArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *RegisterTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *RegisterTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterTopicArgs(%+v)", *p)
}

type RegisterTopicResult struct {
	Success *MQOperationStatus `thrift:"success,0" json:"success"`
}

func NewRegisterTopicResult() *RegisterTopicResult {
	return &RegisterTopicResult{}
}

func (p *RegisterTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterTopicResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMQOperationStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RegisterTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RegisterTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterTopicResult(%+v)", *p)
}

type RegisterSubscriberArgs struct {
	Subscriber *SubscriberInfo `thrift:"subscriber,1" json:"subscriber"`
}

func NewRegisterSubscriberArgs() *RegisterSubscriberArgs {
	return &RegisterSubscriberArgs{}
}

func (p *RegisterSubscriberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterSubscriberArgs) readField1(iprot thrift.TProtocol) error {
	p.Subscriber = NewSubscriberInfo()
	if err := p.Subscriber.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Subscriber)
	}
	return nil
}

func (p *RegisterSubscriberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerSubscriber_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterSubscriberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Subscriber != nil {
		if err := oprot.WriteFieldBegin("subscriber", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:subscriber: %s", p, err)
		}
		if err := p.Subscriber.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Subscriber)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:subscriber: %s", p, err)
		}
	}
	return err
}

func (p *RegisterSubscriberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterSubscriberArgs(%+v)", *p)
}

type RegisterSubscriberResult struct {
	Success *MQOperationStatus `thrift:"success,0" json:"success"`
}

func NewRegisterSubscriberResult() *RegisterSubscriberResult {
	return &RegisterSubscriberResult{}
}

func (p *RegisterSubscriberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterSubscriberResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMQOperationStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RegisterSubscriberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("registerSubscriber_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterSubscriberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RegisterSubscriberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterSubscriberResult(%+v)", *p)
}

type DeregisterTopicArgs struct {
	TopicId TopicId `thrift:"topicId,1" json:"topicId"`
}

func NewDeregisterTopicArgs() *DeregisterTopicArgs {
	return &DeregisterTopicArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeregisterTopicArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *DeregisterTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeregisterTopicArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *DeregisterTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deregisterTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeregisterTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *DeregisterTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeregisterTopicArgs(%+v)", *p)
}

type DeregisterTopicResult struct {
	Success *MQOperationStatus `thrift:"success,0" json:"success"`
}

func NewDeregisterTopicResult() *DeregisterTopicResult {
	return &DeregisterTopicResult{}
}

func (p *DeregisterTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeregisterTopicResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMQOperationStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DeregisterTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deregisterTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeregisterTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DeregisterTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeregisterTopicResult(%+v)", *p)
}

type DeregisterSubscriberArgs struct {
	SubscriberName string `thrift:"subscriberName,1" json:"subscriberName"`
}

func NewDeregisterSubscriberArgs() *DeregisterSubscriberArgs {
	return &DeregisterSubscriberArgs{}
}

func (p *DeregisterSubscriberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeregisterSubscriberArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SubscriberName = v
	}
	return nil
}

func (p *DeregisterSubscriberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deregisterSubscriber_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeregisterSubscriberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subscriberName", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:subscriberName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubscriberName)); err != nil {
		return fmt.Errorf("%T.subscriberName (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:subscriberName: %s", p, err)
	}
	return err
}

func (p *DeregisterSubscriberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeregisterSubscriberArgs(%+v)", *p)
}

type DeregisterSubscriberResult struct {
	Success *MQOperationStatus `thrift:"success,0" json:"success"`
}

func NewDeregisterSubscriberResult() *DeregisterSubscriberResult {
	return &DeregisterSubscriberResult{}
}

func (p *DeregisterSubscriberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeregisterSubscriberResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMQOperationStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DeregisterSubscriberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deregisterSubscriber_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeregisterSubscriberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DeregisterSubscriberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeregisterSubscriberResult(%+v)", *p)
}

type RemoveSubscribeRelationArgs struct {
	TopicId        TopicId `thrift:"topicId,1" json:"topicId"`
	SubscriberName string  `thrift:"subscriberName,2" json:"subscriberName"`
}

func NewRemoveSubscribeRelationArgs() *RemoveSubscribeRelationArgs {
	return &RemoveSubscribeRelationArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RemoveSubscribeRelationArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *RemoveSubscribeRelationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveSubscribeRelationArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *RemoveSubscribeRelationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SubscriberName = v
	}
	return nil
}

func (p *RemoveSubscribeRelationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeSubscribeRelation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveSubscribeRelationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *RemoveSubscribeRelationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subscriberName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:subscriberName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubscriberName)); err != nil {
		return fmt.Errorf("%T.subscriberName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:subscriberName: %s", p, err)
	}
	return err
}

func (p *RemoveSubscribeRelationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveSubscribeRelationArgs(%+v)", *p)
}

type RemoveSubscribeRelationResult struct {
	Success *MQOperationStatus `thrift:"success,0" json:"success"`
}

func NewRemoveSubscribeRelationResult() *RemoveSubscribeRelationResult {
	return &RemoveSubscribeRelationResult{}
}

func (p *RemoveSubscribeRelationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveSubscribeRelationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMQOperationStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RemoveSubscribeRelationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeSubscribeRelation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveSubscribeRelationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RemoveSubscribeRelationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveSubscribeRelationResult(%+v)", *p)
}

type GetTopicListArgs struct {
}

func NewGetTopicListArgs() *GetTopicListArgs {
	return &GetTopicListArgs{}
}

func (p *GetTopicListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopicList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicListArgs(%+v)", *p)
}

type GetTopicListResult struct {
	Success []TopicId `thrift:"success,0" json:"success"`
}

func NewGetTopicListResult() *GetTopicListResult {
	return &GetTopicListResult{}
}

func (p *GetTopicListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicListResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]TopicId, 0, size)
	for i := 0; i < size; i++ {
		var _elem1348 TopicId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1348 = TopicId(v)
		}
		p.Success = append(p.Success, _elem1348)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTopicListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopicList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicListResult(%+v)", *p)
}

type SubscribeArgs struct {
	TopicId        TopicId `thrift:"topicId,1" json:"topicId"`
	SubscriberName string  `thrift:"subscriberName,2" json:"subscriberName"`
}

func NewSubscribeArgs() *SubscribeArgs {
	return &SubscribeArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SubscribeArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *SubscribeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubscribeArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *SubscribeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SubscriberName = v
	}
	return nil
}

func (p *SubscribeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("subscribe_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubscribeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *SubscribeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subscriberName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:subscriberName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubscriberName)); err != nil {
		return fmt.Errorf("%T.subscriberName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:subscriberName: %s", p, err)
	}
	return err
}

func (p *SubscribeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubscribeArgs(%+v)", *p)
}

type SubscribeResult struct {
	Success *MQOperationStatus `thrift:"success,0" json:"success"`
}

func NewSubscribeResult() *SubscribeResult {
	return &SubscribeResult{}
}

func (p *SubscribeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubscribeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewMQOperationStatus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SubscribeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("subscribe_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubscribeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubscribeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubscribeResult(%+v)", *p)
}

type GetSubscriberByTopicArgs struct {
	TopicId TopicId `thrift:"topicId,1" json:"topicId"`
}

func NewGetSubscriberByTopicArgs() *GetSubscriberByTopicArgs {
	return &GetSubscriberByTopicArgs{
		TopicId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetSubscriberByTopicArgs) IsSetTopicId() bool {
	return int64(p.TopicId) != math.MinInt32-1
}

func (p *GetSubscriberByTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberByTopicArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TopicId = TopicId(v)
	}
	return nil
}

func (p *GetSubscriberByTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSubscriberByTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberByTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topicId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topicId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topicId: %s", p, err)
	}
	return err
}

func (p *GetSubscriberByTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSubscriberByTopicArgs(%+v)", *p)
}

type GetSubscriberByTopicResult struct {
	Success []*SubscriberInfo `thrift:"success,0" json:"success"`
}

func NewGetSubscriberByTopicResult() *GetSubscriberByTopicResult {
	return &GetSubscriberByTopicResult{}
}

func (p *GetSubscriberByTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberByTopicResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*SubscriberInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1349 := NewSubscriberInfo()
		if err := _elem1349.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1349)
		}
		p.Success = append(p.Success, _elem1349)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetSubscriberByTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSubscriberByTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberByTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSubscriberByTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSubscriberByTopicResult(%+v)", *p)
}

type GetTopicBySubscriberArgs struct {
	SubscriberName string `thrift:"subscriberName,1" json:"subscriberName"`
}

func NewGetTopicBySubscriberArgs() *GetTopicBySubscriberArgs {
	return &GetTopicBySubscriberArgs{}
}

func (p *GetTopicBySubscriberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicBySubscriberArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SubscriberName = v
	}
	return nil
}

func (p *GetTopicBySubscriberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopicBySubscriber_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicBySubscriberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subscriberName", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:subscriberName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubscriberName)); err != nil {
		return fmt.Errorf("%T.subscriberName (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:subscriberName: %s", p, err)
	}
	return err
}

func (p *GetTopicBySubscriberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicBySubscriberArgs(%+v)", *p)
}

type GetTopicBySubscriberResult struct {
	Success []TopicId `thrift:"success,0" json:"success"`
}

func NewGetTopicBySubscriberResult() *GetTopicBySubscriberResult {
	return &GetTopicBySubscriberResult{}
}

func (p *GetTopicBySubscriberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicBySubscriberResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]TopicId, 0, size)
	for i := 0; i < size; i++ {
		var _elem1350 TopicId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1350 = TopicId(v)
		}
		p.Success = append(p.Success, _elem1350)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTopicBySubscriberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopicBySubscriber_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicBySubscriberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicBySubscriberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicBySubscriberResult(%+v)", *p)
}

type GetSubscriberListArgs struct {
}

func NewGetSubscriberListArgs() *GetSubscriberListArgs {
	return &GetSubscriberListArgs{}
}

func (p *GetSubscriberListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSubscriberList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSubscriberListArgs(%+v)", *p)
}

type GetSubscriberListResult struct {
	Success []*SubscriberInfo `thrift:"success,0" json:"success"`
}

func NewGetSubscriberListResult() *GetSubscriberListResult {
	return &GetSubscriberListResult{}
}

func (p *GetSubscriberListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberListResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*SubscriberInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1351 := NewSubscriberInfo()
		if err := _elem1351.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1351)
		}
		p.Success = append(p.Success, _elem1351)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetSubscriberListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSubscriberList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSubscriberListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSubscriberListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSubscriberListResult(%+v)", *p)
}
