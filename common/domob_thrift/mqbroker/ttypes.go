// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mqbroker

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_event"
	"rtb_model_server/common/domob_thrift/appinfo_event"
	"rtb_model_server/common/domob_thrift/bidmaster_event"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/compass_event"
	"rtb_model_server/common/domob_thrift/data_plus_event"
	"rtb_model_server/common/domob_thrift/dbm_event"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/dmp_event"
	"rtb_model_server/common/domob_thrift/dos_event"
	"rtb_model_server/common/domob_thrift/dpm_event"
	"rtb_model_server/common/domob_thrift/dsp_event"
	"rtb_model_server/common/domob_thrift/event"
	"rtb_model_server/common/domob_thrift/finance_event"
	"rtb_model_server/common/domob_thrift/mediainfo_event"
	"rtb_model_server/common/domob_thrift/mqbroker_event"
	"rtb_model_server/common/domob_thrift/offerwall_types"
	"rtb_model_server/common/domob_thrift/passport_event"
	"rtb_model_server/common/domob_thrift/project_event"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_event"
	"rtb_model_server/common/domob_thrift/searchui_types"
	"rtb_model_server/common/domob_thrift/vico_event"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__
var _ = passport_event.GoUnusedProtection__
var _ = adinfo_event.GoUnusedProtection__
var _ = finance_event.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var _ = mediainfo_event.GoUnusedProtection__
var _ = offerwall_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appinfo_event.GoUnusedProtection__
var _ = rtb_adinfo_event.GoUnusedProtection__
var _ = project_event.GoUnusedProtection__
var _ = dbm_event.GoUnusedProtection__
var _ = dmp_event.GoUnusedProtection__
var _ = dpm_event.GoUnusedProtection__
var _ = dos_event.GoUnusedProtection__
var _ = dsp_event.GoUnusedProtection__
var _ = vico_event.GoUnusedProtection__
var _ = data_plus_event.GoUnusedProtection__
var _ = compass_event.GoUnusedProtection__
var _ = bidmaster_event.GoUnusedProtection__
var _ = mqbroker_event.GoUnusedProtection__
var GoUnusedProtection__ int

type MQOperationStatusCode int64

const (
	MQOperationStatusCode_SUCCESS                   MQOperationStatusCode = 1000
	MQOperationStatusCode_INTERNAL_ERROR            MQOperationStatusCode = 1100
	MQOperationStatusCode_INVALID_ARGUMENT_LIST     MQOperationStatusCode = 1101
	MQOperationStatusCode_UNKNOWN_TOPIC_ID          MQOperationStatusCode = 1102
	MQOperationStatusCode_UNKOWN_SUBSCRIBER         MQOperationStatusCode = 1103
	MQOperationStatusCode_TOPIC_ID_EXISTS           MQOperationStatusCode = 1104
	MQOperationStatusCode_SUBSCRIBER_EXISTS         MQOperationStatusCode = 1105
	MQOperationStatusCode_SUBSCRIBE_RELATION_EXISTS MQOperationStatusCode = 1106
)

func (p MQOperationStatusCode) String() string {
	switch p {
	case MQOperationStatusCode_SUCCESS:
		return "MQOperationStatusCode_SUCCESS"
	case MQOperationStatusCode_INTERNAL_ERROR:
		return "MQOperationStatusCode_INTERNAL_ERROR"
	case MQOperationStatusCode_INVALID_ARGUMENT_LIST:
		return "MQOperationStatusCode_INVALID_ARGUMENT_LIST"
	case MQOperationStatusCode_UNKNOWN_TOPIC_ID:
		return "MQOperationStatusCode_UNKNOWN_TOPIC_ID"
	case MQOperationStatusCode_UNKOWN_SUBSCRIBER:
		return "MQOperationStatusCode_UNKOWN_SUBSCRIBER"
	case MQOperationStatusCode_TOPIC_ID_EXISTS:
		return "MQOperationStatusCode_TOPIC_ID_EXISTS"
	case MQOperationStatusCode_SUBSCRIBER_EXISTS:
		return "MQOperationStatusCode_SUBSCRIBER_EXISTS"
	case MQOperationStatusCode_SUBSCRIBE_RELATION_EXISTS:
		return "MQOperationStatusCode_SUBSCRIBE_RELATION_EXISTS"
	}
	return "<UNSET>"
}

func MQOperationStatusCodeFromString(s string) (MQOperationStatusCode, error) {
	switch s {
	case "MQOperationStatusCode_SUCCESS":
		return MQOperationStatusCode_SUCCESS, nil
	case "MQOperationStatusCode_INTERNAL_ERROR":
		return MQOperationStatusCode_INTERNAL_ERROR, nil
	case "MQOperationStatusCode_INVALID_ARGUMENT_LIST":
		return MQOperationStatusCode_INVALID_ARGUMENT_LIST, nil
	case "MQOperationStatusCode_UNKNOWN_TOPIC_ID":
		return MQOperationStatusCode_UNKNOWN_TOPIC_ID, nil
	case "MQOperationStatusCode_UNKOWN_SUBSCRIBER":
		return MQOperationStatusCode_UNKOWN_SUBSCRIBER, nil
	case "MQOperationStatusCode_TOPIC_ID_EXISTS":
		return MQOperationStatusCode_TOPIC_ID_EXISTS, nil
	case "MQOperationStatusCode_SUBSCRIBER_EXISTS":
		return MQOperationStatusCode_SUBSCRIBER_EXISTS, nil
	case "MQOperationStatusCode_SUBSCRIBE_RELATION_EXISTS":
		return MQOperationStatusCode_SUBSCRIBE_RELATION_EXISTS, nil
	}
	return MQOperationStatusCode(math.MinInt32 - 1), fmt.Errorf("not a valid MQOperationStatusCode string")
}

//消息发布状态码
type PublishStatusCode int64

const (
	PublishStatusCode_SUCCESS                  PublishStatusCode = 10100
	PublishStatusCode_CLIENT_EXCEPTION         PublishStatusCode = 10101
	PublishStatusCode_INVALID_EVENT_HEADER     PublishStatusCode = 10102
	PublishStatusCode_UNKNOWN_EVENT_TYPE       PublishStatusCode = 10103
	PublishStatusCode_UNKNOWN_EVENT_CODE       PublishStatusCode = 10104
	PublishStatusCode_INVALID_EVENT_ID         PublishStatusCode = 10105
	PublishStatusCode_INVALID_ARGUMENT_LIST    PublishStatusCode = 10106
	PublishStatusCode_INVALID_EVENT_DATA       PublishStatusCode = 10107
	PublishStatusCode_SERVER_EXCEPTION         PublishStatusCode = 10200
	PublishStatusCode_SERVER_BUSY              PublishStatusCode = 10201
	PublishStatusCode_SERVER_PERSISTANCE_ERROR PublishStatusCode = 10202
	PublishStatusCode_EVENT_ID_REQUEST_ERROR   PublishStatusCode = 10203
)

func (p PublishStatusCode) String() string {
	switch p {
	case PublishStatusCode_SUCCESS:
		return "PublishStatusCode_SUCCESS"
	case PublishStatusCode_CLIENT_EXCEPTION:
		return "PublishStatusCode_CLIENT_EXCEPTION"
	case PublishStatusCode_INVALID_EVENT_HEADER:
		return "PublishStatusCode_INVALID_EVENT_HEADER"
	case PublishStatusCode_UNKNOWN_EVENT_TYPE:
		return "PublishStatusCode_UNKNOWN_EVENT_TYPE"
	case PublishStatusCode_UNKNOWN_EVENT_CODE:
		return "PublishStatusCode_UNKNOWN_EVENT_CODE"
	case PublishStatusCode_INVALID_EVENT_ID:
		return "PublishStatusCode_INVALID_EVENT_ID"
	case PublishStatusCode_INVALID_ARGUMENT_LIST:
		return "PublishStatusCode_INVALID_ARGUMENT_LIST"
	case PublishStatusCode_INVALID_EVENT_DATA:
		return "PublishStatusCode_INVALID_EVENT_DATA"
	case PublishStatusCode_SERVER_EXCEPTION:
		return "PublishStatusCode_SERVER_EXCEPTION"
	case PublishStatusCode_SERVER_BUSY:
		return "PublishStatusCode_SERVER_BUSY"
	case PublishStatusCode_SERVER_PERSISTANCE_ERROR:
		return "PublishStatusCode_SERVER_PERSISTANCE_ERROR"
	case PublishStatusCode_EVENT_ID_REQUEST_ERROR:
		return "PublishStatusCode_EVENT_ID_REQUEST_ERROR"
	}
	return "<UNSET>"
}

func PublishStatusCodeFromString(s string) (PublishStatusCode, error) {
	switch s {
	case "PublishStatusCode_SUCCESS":
		return PublishStatusCode_SUCCESS, nil
	case "PublishStatusCode_CLIENT_EXCEPTION":
		return PublishStatusCode_CLIENT_EXCEPTION, nil
	case "PublishStatusCode_INVALID_EVENT_HEADER":
		return PublishStatusCode_INVALID_EVENT_HEADER, nil
	case "PublishStatusCode_UNKNOWN_EVENT_TYPE":
		return PublishStatusCode_UNKNOWN_EVENT_TYPE, nil
	case "PublishStatusCode_UNKNOWN_EVENT_CODE":
		return PublishStatusCode_UNKNOWN_EVENT_CODE, nil
	case "PublishStatusCode_INVALID_EVENT_ID":
		return PublishStatusCode_INVALID_EVENT_ID, nil
	case "PublishStatusCode_INVALID_ARGUMENT_LIST":
		return PublishStatusCode_INVALID_ARGUMENT_LIST, nil
	case "PublishStatusCode_INVALID_EVENT_DATA":
		return PublishStatusCode_INVALID_EVENT_DATA, nil
	case "PublishStatusCode_SERVER_EXCEPTION":
		return PublishStatusCode_SERVER_EXCEPTION, nil
	case "PublishStatusCode_SERVER_BUSY":
		return PublishStatusCode_SERVER_BUSY, nil
	case "PublishStatusCode_SERVER_PERSISTANCE_ERROR":
		return PublishStatusCode_SERVER_PERSISTANCE_ERROR, nil
	case "PublishStatusCode_EVENT_ID_REQUEST_ERROR":
		return PublishStatusCode_EVENT_ID_REQUEST_ERROR, nil
	}
	return PublishStatusCode(math.MinInt32 - 1), fmt.Errorf("not a valid PublishStatusCode string")
}

type RegisterStatusCode int64

const (
	RegisterStatusCode_RSC_SUCCESS RegisterStatusCode = 100
	RegisterStatusCode_RSC_FAIL    RegisterStatusCode = 500
)

func (p RegisterStatusCode) String() string {
	switch p {
	case RegisterStatusCode_RSC_SUCCESS:
		return "RegisterStatusCode_RSC_SUCCESS"
	case RegisterStatusCode_RSC_FAIL:
		return "RegisterStatusCode_RSC_FAIL"
	}
	return "<UNSET>"
}

func RegisterStatusCodeFromString(s string) (RegisterStatusCode, error) {
	switch s {
	case "RegisterStatusCode_RSC_SUCCESS":
		return RegisterStatusCode_RSC_SUCCESS, nil
	case "RegisterStatusCode_RSC_FAIL":
		return RegisterStatusCode_RSC_FAIL, nil
	}
	return RegisterStatusCode(math.MinInt32 - 1), fmt.Errorf("not a valid RegisterStatusCode string")
}

type EventHeader *event.EventHeader

type TopicId event.EventCode

type ExtEvent *event.ExtEvent

type RequestHeader *common.RequestHeader

type UserCommonEvent *passport_event.UserCommonEvent

type AdPlanUpdateEvent *adinfo_event.AdPlanUpdateEvent

type AdStrategyUpdateEvent *adinfo_event.AdStrategyUpdateEvent

type AdCreativeUpdateEvent *adinfo_event.AdCreativeUpdateEvent

type FinancialUpdateEvent *finance_event.FinancialUpdateEvent

type UserRechargedEvent *finance_event.UserRechargedEvent

type AdPlanOutOfBudgetEvent *finance_event.AdPlanOutOfBudgetEvent

type AdPlanOutOfTotalBudgetEvent *finance_event.AdPlanOutOfTotalBudgetEvent

type AdPlanBudgetOkEvent *finance_event.AdPlanBudgetOkEvent

type AdPlanTotalBudgetOkEvent *finance_event.AdPlanTotalBudgetOkEvent

type AdStrategyOutOfBudgetEvent *finance_event.AdStrategyOutOfBudgetEvent

type MediaWithdrawEvent *finance_event.MediaWithdrawEvent

type MediaInvoiceEvent *finance_event.MediaInvoiceEvent

type MediaPaymentEvent *finance_event.MediaPaymentEvent

type AdImpression *searchui_types.AdImpression

type AdClick *searchui_types.AdClick

type AdDownload *searchui_types.AdDownload

type AdInstall *searchui_types.AdInstall

type FileReadyEvent *mqbroker_event.FileReadyEvent

type MediaUpdateEvent *mediainfo_event.MediaUpdateEvent

type MediaAppUpdateEvent *mediainfo_event.MediaAppUpdateEvent

type MediaAppRateEvent *mediainfo_event.MediaAppRateEvent

type IdentityAuditEvent *finance_event.IdentityAuditEvent

type OwAct *offerwall_types.OwAct

type AppInfoUpdateEvent *appinfo_event.AppInfoUpdateEvent

type AppChannelUpdateEvent *appinfo_event.AppChannelUpdateEvent

type RTBCampaignUpdateEvent *rtb_adinfo_event.RTBCampaignUpdateEvent

type RTBCampaignTotalBudgetEvent *rtb_adinfo_event.RTBCampaignTotalBudgetEvent

type RTBCampaignDailyBudgetEvent *rtb_adinfo_event.RTBCampaignDailyBudgetEvent

type RTBStrategyDailyBudgetEvent *rtb_adinfo_event.RTBStrategyDailyBudgetEvent

type RTBCreativeUpdateEvent *rtb_adinfo_event.RTBCreativeUpdateEvent

type RTBStrategyUpdateEvent *rtb_adinfo_event.RTBStrategyUpdateEvent

type RTBSponsorUpdateEvent *rtb_adinfo_event.RTBSponsorUpdateEvent

type RTBAdTrackingUpdateEvent *rtb_adinfo_event.RTBAdTrackingUpdateEvent

type RTBPromotionUpdateEvent *rtb_adinfo_event.RTBPromotionUpdateEvent

type RTBAdInfoFlushEvent *rtb_adinfo_event.RTBAdInfoFlushEvent

type RTBPoiInfoUpdateEvent *rtb_adinfo_event.RTBPoiInfoUpdateEvent

type RTBPoiGroupUpdateEvent *rtb_adinfo_event.RTBPoiGroupUpdateEvent

type RTBAdExportCommandEvent *rtb_adinfo_event.RTBAdExportCommandEvent

type SchedulesUpdateEvent *project_event.SchedulesUpdateEvent

type DPMPromotionUpdateEvent *dpm_event.DPMPromotionUpdateEvent

type DPMPromotionPropertyUpdateEvent *dpm_event.DPMPromotionPropertyUpdateEvent

type DPMChannelUpdateEvent *dpm_event.DPMChannelUpdateEvent

type DPMTrackingUpdateEvent *dpm_event.DPMTrackingUpdateEvent

type DPMCompanyUpdateEvent *dpm_event.DPMCompanyUpdateEvent

type PublishStatus struct {
	StatusCode PublishStatusCode `thrift:"statusCode,1" json:"statusCode"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Others map[string]string `thrift:"others,10" json:"others"`
}

func NewPublishStatus() *PublishStatus {
	return &PublishStatus{
		StatusCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PublishStatus) IsSetStatusCode() bool {
	return int64(p.StatusCode) != math.MinInt32-1
}

func (p *PublishStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PublishStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StatusCode = PublishStatusCode(v)
	}
	return nil
}

func (p *PublishStatus) readField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Others = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.Others[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *PublishStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PublishStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PublishStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusCode() {
		if err := oprot.WriteFieldBegin("statusCode", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:statusCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusCode)); err != nil {
			return fmt.Errorf("%T.statusCode (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:statusCode: %s", p, err)
		}
	}
	return err
}

func (p *PublishStatus) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Others != nil {
		if err := oprot.WriteFieldBegin("others", thrift.MAP, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:others: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Others)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Others {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:others: %s", p, err)
		}
	}
	return err
}

func (p *PublishStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishStatus(%+v)", *p)
}

type MQOperationStatus struct {
	OpStatusCode MQOperationStatusCode `thrift:"opStatusCode,1" json:"opStatusCode"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Others map[string]string `thrift:"others,10" json:"others"`
}

func NewMQOperationStatus() *MQOperationStatus {
	return &MQOperationStatus{
		OpStatusCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MQOperationStatus) IsSetOpStatusCode() bool {
	return int64(p.OpStatusCode) != math.MinInt32-1
}

func (p *MQOperationStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MQOperationStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OpStatusCode = MQOperationStatusCode(v)
	}
	return nil
}

func (p *MQOperationStatus) readField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Others = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.Others[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *MQOperationStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MQOperationStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MQOperationStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOpStatusCode() {
		if err := oprot.WriteFieldBegin("opStatusCode", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:opStatusCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OpStatusCode)); err != nil {
			return fmt.Errorf("%T.opStatusCode (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:opStatusCode: %s", p, err)
		}
	}
	return err
}

func (p *MQOperationStatus) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Others != nil {
		if err := oprot.WriteFieldBegin("others", thrift.MAP, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:others: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Others)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Others {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:others: %s", p, err)
		}
	}
	return err
}

func (p *MQOperationStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MQOperationStatus(%+v)", *p)
}

type PublishException struct {
	Status  *PublishStatus `thrift:"status,1" json:"status"`
	Message string         `thrift:"message,2" json:"message"`
}

func NewPublishException() *PublishException {
	return &PublishException{}
}

func (p *PublishException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PublishException) readField1(iprot thrift.TProtocol) error {
	p.Status = NewPublishStatus()
	if err := p.Status.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Status)
	}
	return nil
}

func (p *PublishException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *PublishException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PublishException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PublishException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Status != nil {
		if err := oprot.WriteFieldBegin("status", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := p.Status.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Status)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *PublishException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *PublishException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishException(%+v)", *p)
}

type MQOperationException struct {
	OpStatus *MQOperationStatus `thrift:"opStatus,1" json:"opStatus"`
	Message  string             `thrift:"message,2" json:"message"`
}

func NewMQOperationException() *MQOperationException {
	return &MQOperationException{}
}

func (p *MQOperationException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MQOperationException) readField1(iprot thrift.TProtocol) error {
	p.OpStatus = NewMQOperationStatus()
	if err := p.OpStatus.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.OpStatus)
	}
	return nil
}

func (p *MQOperationException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *MQOperationException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MQOperationException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MQOperationException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.OpStatus != nil {
		if err := oprot.WriteFieldBegin("opStatus", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:opStatus: %s", p, err)
		}
		if err := p.OpStatus.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.OpStatus)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:opStatus: %s", p, err)
		}
	}
	return err
}

func (p *MQOperationException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *MQOperationException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MQOperationException(%+v)", *p)
}

type RegisterStatus struct {
	StatusCode RegisterStatusCode `thrift:"statusCode,1" json:"statusCode"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Others map[string]string `thrift:"others,10" json:"others"`
}

func NewRegisterStatus() *RegisterStatus {
	return &RegisterStatus{
		StatusCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RegisterStatus) IsSetStatusCode() bool {
	return int64(p.StatusCode) != math.MinInt32-1
}

func (p *RegisterStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StatusCode = RegisterStatusCode(v)
	}
	return nil
}

func (p *RegisterStatus) readField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Others = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key4 = v
		}
		var _val5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val5 = v
		}
		p.Others[_key4] = _val5
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *RegisterStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RegisterStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusCode() {
		if err := oprot.WriteFieldBegin("statusCode", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:statusCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.StatusCode)); err != nil {
			return fmt.Errorf("%T.statusCode (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:statusCode: %s", p, err)
		}
	}
	return err
}

func (p *RegisterStatus) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Others != nil {
		if err := oprot.WriteFieldBegin("others", thrift.MAP, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:others: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Others)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Others {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:others: %s", p, err)
		}
	}
	return err
}

func (p *RegisterStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterStatus(%+v)", *p)
}

type SubscriberInfo struct {
	Id            int32  `thrift:"id,1" json:"id"`
	Name          string `thrift:"name,2" json:"name"`
	RemoteAddress string `thrift:"remoteAddress,3" json:"remoteAddress"`
	// unused field # 4
	Comment string `thrift:"comment,5" json:"comment"`
	Timeout int32  `thrift:"timeout,6" json:"timeout"`
}

func NewSubscriberInfo() *SubscriberInfo {
	return &SubscriberInfo{}
}

func (p *SubscriberInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubscriberInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SubscriberInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SubscriberInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RemoteAddress = v
	}
	return nil
}

func (p *SubscriberInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *SubscriberInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Timeout = v
	}
	return nil
}

func (p *SubscriberInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SubscriberInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubscriberInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SubscriberInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *SubscriberInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remoteAddress", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:remoteAddress: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RemoteAddress)); err != nil {
		return fmt.Errorf("%T.remoteAddress (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:remoteAddress: %s", p, err)
	}
	return err
}

func (p *SubscriberInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:comment: %s", p, err)
	}
	return err
}

func (p *SubscriberInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timeout", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:timeout: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Timeout)); err != nil {
		return fmt.Errorf("%T.timeout (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:timeout: %s", p, err)
	}
	return err
}

func (p *SubscriberInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubscriberInfo(%+v)", *p)
}
