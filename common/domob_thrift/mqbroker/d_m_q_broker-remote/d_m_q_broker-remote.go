// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"mqbroker"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubUserCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, UserCommonEvent userCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubFinancialUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, FinancialUpdateEvent financialUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubMediaWithdrawEvent(RequestHeader requestHeader, EventHeader eventHeader, MediaWithdrawEvent mediaWithdrawEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubMediaInvoiceEvent(RequestHeader requestHeader, EventHeader eventHeader, MediaInvoiceEvent mediaInvoiceEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubMediaPaymentEvent(RequestHeader requestHeader, EventHeader eventHeader, MediaPaymentEvent mediaPaymentEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubUserRechargedEvent(RequestHeader requestHeader, EventHeader eventHeader, UserRechargedEvent userRechargedEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdPlanOutOfBudgetEvent(RequestHeader requestHeader, EventHeader eventHeader, AdPlanOutOfBudgetEvent adPlanOutOfBudgetEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdPlanOutOfTotalBudgetEvent(RequestHeader requestHeader, EventHeader eventHeader, AdPlanOutOfTotalBudgetEvent adPlanOutOfTotalBudgetEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdPlanBudgetOkEvent(RequestHeader requestHeader, EventHeader eventHeader, AdPlanBudgetOkEvent adPlanBudgetOkEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdPlanTotalBudgetOkEvent(RequestHeader requestHeader, EventHeader eventHeader, AdPlanTotalBudgetOkEvent adPlanTotalBudgetOkEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdStrategyOutOfBudgetEvent(RequestHeader requestHeader, EventHeader eventHeader, AdStrategyOutOfBudgetEvent adStrategyOutOfBudgetEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdPlanUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, AdPlanUpdateEvent adPlanUpdate)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdStrategyUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, AdStrategyUpdateEvent adStrategyUpdate)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdCreativeUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, AdCreativeUpdateEvent adCreativeUpdate)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBAdExportCommandEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBAdExportCommandEvent rtbAdExportCommandEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdImpression(RequestHeader requestHeader, EventHeader eventHeader, AdImpression adImpression)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdClick(RequestHeader requestHeader, EventHeader eventHeader, AdClick adClick)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdDownload(RequestHeader requestHeader, EventHeader eventHeader, AdDownload adDownload)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAdInstall(RequestHeader requestHeader, EventHeader eventHeader, AdInstall adInstall)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubOwAct(RequestHeader requestHeader, EventHeader eventHeader, OwAct owAct)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubFileReadyEvent(RequestHeader requestHeader, EventHeader eventHeader, FileReadyEvent fileReady)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubMediaUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, MediaUpdateEvent mediaUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubMediaAppUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, MediaAppUpdateEvent mediaAppUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubMediaAppRateEvent(RequestHeader requestHeader, EventHeader eventHeader, MediaAppRateEvent mediaAppRateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubIdentityAuditEvent(RequestHeader requestHeader, EventHeader eventHeader, IdentityAuditEvent identityAuditEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAppInfoUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, AppInfoUpdateEvent appInfoUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubAppChannelUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, AppChannelUpdateEvent appChannelUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBCampaignUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBCampaignUpdateEvent rtbCampaignUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBCreativeUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBCreativeUpdateEvent rtbCreativeUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBStrategyUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBStrategyUpdateEvent rtbStrategyUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBSponsorUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBSponsorUpdateEvent rtbSponsorUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBAdTrackingUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBAdTrackingUpdateEvent rtbAdTrackingUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBPromotionUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBPromotionUpdateEvent rtbPromotionUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBAdInfoFlushEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBAdInfoFlushEvent rtbAdInfoFlushEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBCampaignTotalBudgetEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBCampaignTotalBudgetEvent rtbCampaignTotalBudgetEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBCampaignDailyBudgetEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBCampaignDailyBudgetEvent rtbCampaignDailyBudgetEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBStrategyDailyBudgetEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBStrategyDailyBudgetEvent rtbStrategyDailyBudgetEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBPoiInfoUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBPoiInfoUpdateEvent rtbPoiInfoUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubRTBPoiGroupUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, RTBPoiGroupUpdateEvent rtbPoiGroupUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubProjectUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, ProjectUpdateEvent projectUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubSchedulesUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, SchedulesUpdateEvent schedulesUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDbmCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, DbmCommonEvent dbmCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDmpCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, DmpCommonEvent dmpCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDbmOperationEvent(RequestHeader requestHeader, EventHeader eventHeader, DbmOperationEvent dbmOperationEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDmpOperationEvent(RequestHeader requestHeader, EventHeader eventHeader, DmpOperationEvent dmpOperationEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDPMPromotionUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, DPMPromotionUpdateEvent dpmPromotionUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDPMPromotionPropertyUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, DPMPromotionPropertyUpdateEvent dpmPromotionPropertyUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDPMChannelUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, DPMChannelUpdateEvent dpmChannelUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDPMTrackingUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, DPMTrackingUpdateEvent dpmTrackingUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDPMCompanyUpdateEvent(RequestHeader requestHeader, EventHeader eventHeader, DPMCompanyUpdateEvent dpmCompanyUpdateEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDosCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, DosCommonEvent dosCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDosStatsEvent(RequestHeader requestHeader, EventHeader eventHeader, DosStatsEvent dosStatsEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDspStatsEvent(RequestHeader requestHeader, EventHeader eventHeader, DspStatsEvent dspStatsEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubVicoCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, VicoCommonEvent vicoCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubDataPlusCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, DataPlusCommonEvent dataPlusCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubCompassCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, CompassCommonEvent compassCommonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubBidMasterCommonEvent(RequestHeader requestHeader, EventHeader eventHeader, BidMasterCommonEvent commonEvent)")
	fmt.Fprintln(os.Stderr, "  PublishStatus pubBidMasterOperationEvent(RequestHeader requestHeader, EventHeader eventHeader, BidMasterOperationEvent operationEvent)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := mqbroker.NewDMQBrokerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "pubUserCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubUserCommonEvent requires 3 args")
			flag.Usage()
		}
		arg240 := flag.Arg(1)
		mbTrans241 := thrift.NewTMemoryBufferLen(len(arg240))
		defer mbTrans241.Close()
		_, err242 := mbTrans241.WriteString(arg240)
		if err242 != nil {
			Usage()
			return
		}
		factory243 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt244 := factory243.GetProtocol(mbTrans241)
		argvalue0 := mqbroker.NewRequestHeader()
		err245 := argvalue0.Read(jsProt244)
		if err245 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg246 := flag.Arg(2)
		mbTrans247 := thrift.NewTMemoryBufferLen(len(arg246))
		defer mbTrans247.Close()
		_, err248 := mbTrans247.WriteString(arg246)
		if err248 != nil {
			Usage()
			return
		}
		factory249 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt250 := factory249.GetProtocol(mbTrans247)
		argvalue1 := mqbroker.NewEventHeader()
		err251 := argvalue1.Read(jsProt250)
		if err251 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg252 := flag.Arg(3)
		mbTrans253 := thrift.NewTMemoryBufferLen(len(arg252))
		defer mbTrans253.Close()
		_, err254 := mbTrans253.WriteString(arg252)
		if err254 != nil {
			Usage()
			return
		}
		factory255 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt256 := factory255.GetProtocol(mbTrans253)
		argvalue2 := mqbroker.NewUserCommonEvent()
		err257 := argvalue2.Read(jsProt256)
		if err257 != nil {
			Usage()
			return
		}
		value2 := mqbroker.UserCommonEvent(argvalue2)
		fmt.Print(client.PubUserCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubFinancialUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubFinancialUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg258 := flag.Arg(1)
		mbTrans259 := thrift.NewTMemoryBufferLen(len(arg258))
		defer mbTrans259.Close()
		_, err260 := mbTrans259.WriteString(arg258)
		if err260 != nil {
			Usage()
			return
		}
		factory261 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt262 := factory261.GetProtocol(mbTrans259)
		argvalue0 := mqbroker.NewRequestHeader()
		err263 := argvalue0.Read(jsProt262)
		if err263 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg264 := flag.Arg(2)
		mbTrans265 := thrift.NewTMemoryBufferLen(len(arg264))
		defer mbTrans265.Close()
		_, err266 := mbTrans265.WriteString(arg264)
		if err266 != nil {
			Usage()
			return
		}
		factory267 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt268 := factory267.GetProtocol(mbTrans265)
		argvalue1 := mqbroker.NewEventHeader()
		err269 := argvalue1.Read(jsProt268)
		if err269 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg270 := flag.Arg(3)
		mbTrans271 := thrift.NewTMemoryBufferLen(len(arg270))
		defer mbTrans271.Close()
		_, err272 := mbTrans271.WriteString(arg270)
		if err272 != nil {
			Usage()
			return
		}
		factory273 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt274 := factory273.GetProtocol(mbTrans271)
		argvalue2 := mqbroker.NewFinancialUpdateEvent()
		err275 := argvalue2.Read(jsProt274)
		if err275 != nil {
			Usage()
			return
		}
		value2 := mqbroker.FinancialUpdateEvent(argvalue2)
		fmt.Print(client.PubFinancialUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubMediaWithdrawEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubMediaWithdrawEvent requires 3 args")
			flag.Usage()
		}
		arg276 := flag.Arg(1)
		mbTrans277 := thrift.NewTMemoryBufferLen(len(arg276))
		defer mbTrans277.Close()
		_, err278 := mbTrans277.WriteString(arg276)
		if err278 != nil {
			Usage()
			return
		}
		factory279 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt280 := factory279.GetProtocol(mbTrans277)
		argvalue0 := mqbroker.NewRequestHeader()
		err281 := argvalue0.Read(jsProt280)
		if err281 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg282 := flag.Arg(2)
		mbTrans283 := thrift.NewTMemoryBufferLen(len(arg282))
		defer mbTrans283.Close()
		_, err284 := mbTrans283.WriteString(arg282)
		if err284 != nil {
			Usage()
			return
		}
		factory285 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt286 := factory285.GetProtocol(mbTrans283)
		argvalue1 := mqbroker.NewEventHeader()
		err287 := argvalue1.Read(jsProt286)
		if err287 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg288 := flag.Arg(3)
		mbTrans289 := thrift.NewTMemoryBufferLen(len(arg288))
		defer mbTrans289.Close()
		_, err290 := mbTrans289.WriteString(arg288)
		if err290 != nil {
			Usage()
			return
		}
		factory291 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt292 := factory291.GetProtocol(mbTrans289)
		argvalue2 := mqbroker.NewMediaWithdrawEvent()
		err293 := argvalue2.Read(jsProt292)
		if err293 != nil {
			Usage()
			return
		}
		value2 := mqbroker.MediaWithdrawEvent(argvalue2)
		fmt.Print(client.PubMediaWithdrawEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubMediaInvoiceEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubMediaInvoiceEvent requires 3 args")
			flag.Usage()
		}
		arg294 := flag.Arg(1)
		mbTrans295 := thrift.NewTMemoryBufferLen(len(arg294))
		defer mbTrans295.Close()
		_, err296 := mbTrans295.WriteString(arg294)
		if err296 != nil {
			Usage()
			return
		}
		factory297 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt298 := factory297.GetProtocol(mbTrans295)
		argvalue0 := mqbroker.NewRequestHeader()
		err299 := argvalue0.Read(jsProt298)
		if err299 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg300 := flag.Arg(2)
		mbTrans301 := thrift.NewTMemoryBufferLen(len(arg300))
		defer mbTrans301.Close()
		_, err302 := mbTrans301.WriteString(arg300)
		if err302 != nil {
			Usage()
			return
		}
		factory303 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt304 := factory303.GetProtocol(mbTrans301)
		argvalue1 := mqbroker.NewEventHeader()
		err305 := argvalue1.Read(jsProt304)
		if err305 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg306 := flag.Arg(3)
		mbTrans307 := thrift.NewTMemoryBufferLen(len(arg306))
		defer mbTrans307.Close()
		_, err308 := mbTrans307.WriteString(arg306)
		if err308 != nil {
			Usage()
			return
		}
		factory309 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt310 := factory309.GetProtocol(mbTrans307)
		argvalue2 := mqbroker.NewMediaInvoiceEvent()
		err311 := argvalue2.Read(jsProt310)
		if err311 != nil {
			Usage()
			return
		}
		value2 := mqbroker.MediaInvoiceEvent(argvalue2)
		fmt.Print(client.PubMediaInvoiceEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubMediaPaymentEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubMediaPaymentEvent requires 3 args")
			flag.Usage()
		}
		arg312 := flag.Arg(1)
		mbTrans313 := thrift.NewTMemoryBufferLen(len(arg312))
		defer mbTrans313.Close()
		_, err314 := mbTrans313.WriteString(arg312)
		if err314 != nil {
			Usage()
			return
		}
		factory315 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt316 := factory315.GetProtocol(mbTrans313)
		argvalue0 := mqbroker.NewRequestHeader()
		err317 := argvalue0.Read(jsProt316)
		if err317 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg318 := flag.Arg(2)
		mbTrans319 := thrift.NewTMemoryBufferLen(len(arg318))
		defer mbTrans319.Close()
		_, err320 := mbTrans319.WriteString(arg318)
		if err320 != nil {
			Usage()
			return
		}
		factory321 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt322 := factory321.GetProtocol(mbTrans319)
		argvalue1 := mqbroker.NewEventHeader()
		err323 := argvalue1.Read(jsProt322)
		if err323 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg324 := flag.Arg(3)
		mbTrans325 := thrift.NewTMemoryBufferLen(len(arg324))
		defer mbTrans325.Close()
		_, err326 := mbTrans325.WriteString(arg324)
		if err326 != nil {
			Usage()
			return
		}
		factory327 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt328 := factory327.GetProtocol(mbTrans325)
		argvalue2 := mqbroker.NewMediaPaymentEvent()
		err329 := argvalue2.Read(jsProt328)
		if err329 != nil {
			Usage()
			return
		}
		value2 := mqbroker.MediaPaymentEvent(argvalue2)
		fmt.Print(client.PubMediaPaymentEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubUserRechargedEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubUserRechargedEvent requires 3 args")
			flag.Usage()
		}
		arg330 := flag.Arg(1)
		mbTrans331 := thrift.NewTMemoryBufferLen(len(arg330))
		defer mbTrans331.Close()
		_, err332 := mbTrans331.WriteString(arg330)
		if err332 != nil {
			Usage()
			return
		}
		factory333 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt334 := factory333.GetProtocol(mbTrans331)
		argvalue0 := mqbroker.NewRequestHeader()
		err335 := argvalue0.Read(jsProt334)
		if err335 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg336 := flag.Arg(2)
		mbTrans337 := thrift.NewTMemoryBufferLen(len(arg336))
		defer mbTrans337.Close()
		_, err338 := mbTrans337.WriteString(arg336)
		if err338 != nil {
			Usage()
			return
		}
		factory339 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt340 := factory339.GetProtocol(mbTrans337)
		argvalue1 := mqbroker.NewEventHeader()
		err341 := argvalue1.Read(jsProt340)
		if err341 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg342 := flag.Arg(3)
		mbTrans343 := thrift.NewTMemoryBufferLen(len(arg342))
		defer mbTrans343.Close()
		_, err344 := mbTrans343.WriteString(arg342)
		if err344 != nil {
			Usage()
			return
		}
		factory345 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt346 := factory345.GetProtocol(mbTrans343)
		argvalue2 := mqbroker.NewUserRechargedEvent()
		err347 := argvalue2.Read(jsProt346)
		if err347 != nil {
			Usage()
			return
		}
		value2 := mqbroker.UserRechargedEvent(argvalue2)
		fmt.Print(client.PubUserRechargedEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdPlanOutOfBudgetEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdPlanOutOfBudgetEvent requires 3 args")
			flag.Usage()
		}
		arg348 := flag.Arg(1)
		mbTrans349 := thrift.NewTMemoryBufferLen(len(arg348))
		defer mbTrans349.Close()
		_, err350 := mbTrans349.WriteString(arg348)
		if err350 != nil {
			Usage()
			return
		}
		factory351 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt352 := factory351.GetProtocol(mbTrans349)
		argvalue0 := mqbroker.NewRequestHeader()
		err353 := argvalue0.Read(jsProt352)
		if err353 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg354 := flag.Arg(2)
		mbTrans355 := thrift.NewTMemoryBufferLen(len(arg354))
		defer mbTrans355.Close()
		_, err356 := mbTrans355.WriteString(arg354)
		if err356 != nil {
			Usage()
			return
		}
		factory357 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt358 := factory357.GetProtocol(mbTrans355)
		argvalue1 := mqbroker.NewEventHeader()
		err359 := argvalue1.Read(jsProt358)
		if err359 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg360 := flag.Arg(3)
		mbTrans361 := thrift.NewTMemoryBufferLen(len(arg360))
		defer mbTrans361.Close()
		_, err362 := mbTrans361.WriteString(arg360)
		if err362 != nil {
			Usage()
			return
		}
		factory363 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt364 := factory363.GetProtocol(mbTrans361)
		argvalue2 := mqbroker.NewAdPlanOutOfBudgetEvent()
		err365 := argvalue2.Read(jsProt364)
		if err365 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdPlanOutOfBudgetEvent(argvalue2)
		fmt.Print(client.PubAdPlanOutOfBudgetEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdPlanOutOfTotalBudgetEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdPlanOutOfTotalBudgetEvent requires 3 args")
			flag.Usage()
		}
		arg366 := flag.Arg(1)
		mbTrans367 := thrift.NewTMemoryBufferLen(len(arg366))
		defer mbTrans367.Close()
		_, err368 := mbTrans367.WriteString(arg366)
		if err368 != nil {
			Usage()
			return
		}
		factory369 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt370 := factory369.GetProtocol(mbTrans367)
		argvalue0 := mqbroker.NewRequestHeader()
		err371 := argvalue0.Read(jsProt370)
		if err371 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg372 := flag.Arg(2)
		mbTrans373 := thrift.NewTMemoryBufferLen(len(arg372))
		defer mbTrans373.Close()
		_, err374 := mbTrans373.WriteString(arg372)
		if err374 != nil {
			Usage()
			return
		}
		factory375 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt376 := factory375.GetProtocol(mbTrans373)
		argvalue1 := mqbroker.NewEventHeader()
		err377 := argvalue1.Read(jsProt376)
		if err377 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg378 := flag.Arg(3)
		mbTrans379 := thrift.NewTMemoryBufferLen(len(arg378))
		defer mbTrans379.Close()
		_, err380 := mbTrans379.WriteString(arg378)
		if err380 != nil {
			Usage()
			return
		}
		factory381 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt382 := factory381.GetProtocol(mbTrans379)
		argvalue2 := mqbroker.NewAdPlanOutOfTotalBudgetEvent()
		err383 := argvalue2.Read(jsProt382)
		if err383 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdPlanOutOfTotalBudgetEvent(argvalue2)
		fmt.Print(client.PubAdPlanOutOfTotalBudgetEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdPlanBudgetOkEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdPlanBudgetOkEvent requires 3 args")
			flag.Usage()
		}
		arg384 := flag.Arg(1)
		mbTrans385 := thrift.NewTMemoryBufferLen(len(arg384))
		defer mbTrans385.Close()
		_, err386 := mbTrans385.WriteString(arg384)
		if err386 != nil {
			Usage()
			return
		}
		factory387 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt388 := factory387.GetProtocol(mbTrans385)
		argvalue0 := mqbroker.NewRequestHeader()
		err389 := argvalue0.Read(jsProt388)
		if err389 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg390 := flag.Arg(2)
		mbTrans391 := thrift.NewTMemoryBufferLen(len(arg390))
		defer mbTrans391.Close()
		_, err392 := mbTrans391.WriteString(arg390)
		if err392 != nil {
			Usage()
			return
		}
		factory393 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt394 := factory393.GetProtocol(mbTrans391)
		argvalue1 := mqbroker.NewEventHeader()
		err395 := argvalue1.Read(jsProt394)
		if err395 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg396 := flag.Arg(3)
		mbTrans397 := thrift.NewTMemoryBufferLen(len(arg396))
		defer mbTrans397.Close()
		_, err398 := mbTrans397.WriteString(arg396)
		if err398 != nil {
			Usage()
			return
		}
		factory399 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt400 := factory399.GetProtocol(mbTrans397)
		argvalue2 := mqbroker.NewAdPlanBudgetOkEvent()
		err401 := argvalue2.Read(jsProt400)
		if err401 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdPlanBudgetOkEvent(argvalue2)
		fmt.Print(client.PubAdPlanBudgetOkEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdPlanTotalBudgetOkEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdPlanTotalBudgetOkEvent requires 3 args")
			flag.Usage()
		}
		arg402 := flag.Arg(1)
		mbTrans403 := thrift.NewTMemoryBufferLen(len(arg402))
		defer mbTrans403.Close()
		_, err404 := mbTrans403.WriteString(arg402)
		if err404 != nil {
			Usage()
			return
		}
		factory405 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt406 := factory405.GetProtocol(mbTrans403)
		argvalue0 := mqbroker.NewRequestHeader()
		err407 := argvalue0.Read(jsProt406)
		if err407 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg408 := flag.Arg(2)
		mbTrans409 := thrift.NewTMemoryBufferLen(len(arg408))
		defer mbTrans409.Close()
		_, err410 := mbTrans409.WriteString(arg408)
		if err410 != nil {
			Usage()
			return
		}
		factory411 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt412 := factory411.GetProtocol(mbTrans409)
		argvalue1 := mqbroker.NewEventHeader()
		err413 := argvalue1.Read(jsProt412)
		if err413 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg414 := flag.Arg(3)
		mbTrans415 := thrift.NewTMemoryBufferLen(len(arg414))
		defer mbTrans415.Close()
		_, err416 := mbTrans415.WriteString(arg414)
		if err416 != nil {
			Usage()
			return
		}
		factory417 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt418 := factory417.GetProtocol(mbTrans415)
		argvalue2 := mqbroker.NewAdPlanTotalBudgetOkEvent()
		err419 := argvalue2.Read(jsProt418)
		if err419 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdPlanTotalBudgetOkEvent(argvalue2)
		fmt.Print(client.PubAdPlanTotalBudgetOkEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdStrategyOutOfBudgetEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdStrategyOutOfBudgetEvent requires 3 args")
			flag.Usage()
		}
		arg420 := flag.Arg(1)
		mbTrans421 := thrift.NewTMemoryBufferLen(len(arg420))
		defer mbTrans421.Close()
		_, err422 := mbTrans421.WriteString(arg420)
		if err422 != nil {
			Usage()
			return
		}
		factory423 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt424 := factory423.GetProtocol(mbTrans421)
		argvalue0 := mqbroker.NewRequestHeader()
		err425 := argvalue0.Read(jsProt424)
		if err425 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg426 := flag.Arg(2)
		mbTrans427 := thrift.NewTMemoryBufferLen(len(arg426))
		defer mbTrans427.Close()
		_, err428 := mbTrans427.WriteString(arg426)
		if err428 != nil {
			Usage()
			return
		}
		factory429 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt430 := factory429.GetProtocol(mbTrans427)
		argvalue1 := mqbroker.NewEventHeader()
		err431 := argvalue1.Read(jsProt430)
		if err431 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg432 := flag.Arg(3)
		mbTrans433 := thrift.NewTMemoryBufferLen(len(arg432))
		defer mbTrans433.Close()
		_, err434 := mbTrans433.WriteString(arg432)
		if err434 != nil {
			Usage()
			return
		}
		factory435 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt436 := factory435.GetProtocol(mbTrans433)
		argvalue2 := mqbroker.NewAdStrategyOutOfBudgetEvent()
		err437 := argvalue2.Read(jsProt436)
		if err437 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdStrategyOutOfBudgetEvent(argvalue2)
		fmt.Print(client.PubAdStrategyOutOfBudgetEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdPlanUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdPlanUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg438 := flag.Arg(1)
		mbTrans439 := thrift.NewTMemoryBufferLen(len(arg438))
		defer mbTrans439.Close()
		_, err440 := mbTrans439.WriteString(arg438)
		if err440 != nil {
			Usage()
			return
		}
		factory441 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt442 := factory441.GetProtocol(mbTrans439)
		argvalue0 := mqbroker.NewRequestHeader()
		err443 := argvalue0.Read(jsProt442)
		if err443 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg444 := flag.Arg(2)
		mbTrans445 := thrift.NewTMemoryBufferLen(len(arg444))
		defer mbTrans445.Close()
		_, err446 := mbTrans445.WriteString(arg444)
		if err446 != nil {
			Usage()
			return
		}
		factory447 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt448 := factory447.GetProtocol(mbTrans445)
		argvalue1 := mqbroker.NewEventHeader()
		err449 := argvalue1.Read(jsProt448)
		if err449 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg450 := flag.Arg(3)
		mbTrans451 := thrift.NewTMemoryBufferLen(len(arg450))
		defer mbTrans451.Close()
		_, err452 := mbTrans451.WriteString(arg450)
		if err452 != nil {
			Usage()
			return
		}
		factory453 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt454 := factory453.GetProtocol(mbTrans451)
		argvalue2 := mqbroker.NewAdPlanUpdateEvent()
		err455 := argvalue2.Read(jsProt454)
		if err455 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdPlanUpdateEvent(argvalue2)
		fmt.Print(client.PubAdPlanUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdStrategyUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdStrategyUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg456 := flag.Arg(1)
		mbTrans457 := thrift.NewTMemoryBufferLen(len(arg456))
		defer mbTrans457.Close()
		_, err458 := mbTrans457.WriteString(arg456)
		if err458 != nil {
			Usage()
			return
		}
		factory459 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt460 := factory459.GetProtocol(mbTrans457)
		argvalue0 := mqbroker.NewRequestHeader()
		err461 := argvalue0.Read(jsProt460)
		if err461 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg462 := flag.Arg(2)
		mbTrans463 := thrift.NewTMemoryBufferLen(len(arg462))
		defer mbTrans463.Close()
		_, err464 := mbTrans463.WriteString(arg462)
		if err464 != nil {
			Usage()
			return
		}
		factory465 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt466 := factory465.GetProtocol(mbTrans463)
		argvalue1 := mqbroker.NewEventHeader()
		err467 := argvalue1.Read(jsProt466)
		if err467 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg468 := flag.Arg(3)
		mbTrans469 := thrift.NewTMemoryBufferLen(len(arg468))
		defer mbTrans469.Close()
		_, err470 := mbTrans469.WriteString(arg468)
		if err470 != nil {
			Usage()
			return
		}
		factory471 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt472 := factory471.GetProtocol(mbTrans469)
		argvalue2 := mqbroker.NewAdStrategyUpdateEvent()
		err473 := argvalue2.Read(jsProt472)
		if err473 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdStrategyUpdateEvent(argvalue2)
		fmt.Print(client.PubAdStrategyUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdCreativeUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdCreativeUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg474 := flag.Arg(1)
		mbTrans475 := thrift.NewTMemoryBufferLen(len(arg474))
		defer mbTrans475.Close()
		_, err476 := mbTrans475.WriteString(arg474)
		if err476 != nil {
			Usage()
			return
		}
		factory477 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt478 := factory477.GetProtocol(mbTrans475)
		argvalue0 := mqbroker.NewRequestHeader()
		err479 := argvalue0.Read(jsProt478)
		if err479 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg480 := flag.Arg(2)
		mbTrans481 := thrift.NewTMemoryBufferLen(len(arg480))
		defer mbTrans481.Close()
		_, err482 := mbTrans481.WriteString(arg480)
		if err482 != nil {
			Usage()
			return
		}
		factory483 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt484 := factory483.GetProtocol(mbTrans481)
		argvalue1 := mqbroker.NewEventHeader()
		err485 := argvalue1.Read(jsProt484)
		if err485 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg486 := flag.Arg(3)
		mbTrans487 := thrift.NewTMemoryBufferLen(len(arg486))
		defer mbTrans487.Close()
		_, err488 := mbTrans487.WriteString(arg486)
		if err488 != nil {
			Usage()
			return
		}
		factory489 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt490 := factory489.GetProtocol(mbTrans487)
		argvalue2 := mqbroker.NewAdCreativeUpdateEvent()
		err491 := argvalue2.Read(jsProt490)
		if err491 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdCreativeUpdateEvent(argvalue2)
		fmt.Print(client.PubAdCreativeUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBAdExportCommandEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBAdExportCommandEvent requires 3 args")
			flag.Usage()
		}
		arg492 := flag.Arg(1)
		mbTrans493 := thrift.NewTMemoryBufferLen(len(arg492))
		defer mbTrans493.Close()
		_, err494 := mbTrans493.WriteString(arg492)
		if err494 != nil {
			Usage()
			return
		}
		factory495 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt496 := factory495.GetProtocol(mbTrans493)
		argvalue0 := mqbroker.NewRequestHeader()
		err497 := argvalue0.Read(jsProt496)
		if err497 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg498 := flag.Arg(2)
		mbTrans499 := thrift.NewTMemoryBufferLen(len(arg498))
		defer mbTrans499.Close()
		_, err500 := mbTrans499.WriteString(arg498)
		if err500 != nil {
			Usage()
			return
		}
		factory501 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt502 := factory501.GetProtocol(mbTrans499)
		argvalue1 := mqbroker.NewEventHeader()
		err503 := argvalue1.Read(jsProt502)
		if err503 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg504 := flag.Arg(3)
		mbTrans505 := thrift.NewTMemoryBufferLen(len(arg504))
		defer mbTrans505.Close()
		_, err506 := mbTrans505.WriteString(arg504)
		if err506 != nil {
			Usage()
			return
		}
		factory507 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt508 := factory507.GetProtocol(mbTrans505)
		argvalue2 := mqbroker.NewRTBAdExportCommandEvent()
		err509 := argvalue2.Read(jsProt508)
		if err509 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBAdExportCommandEvent(argvalue2)
		fmt.Print(client.PubRTBAdExportCommandEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdImpression":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdImpression requires 3 args")
			flag.Usage()
		}
		arg510 := flag.Arg(1)
		mbTrans511 := thrift.NewTMemoryBufferLen(len(arg510))
		defer mbTrans511.Close()
		_, err512 := mbTrans511.WriteString(arg510)
		if err512 != nil {
			Usage()
			return
		}
		factory513 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt514 := factory513.GetProtocol(mbTrans511)
		argvalue0 := mqbroker.NewRequestHeader()
		err515 := argvalue0.Read(jsProt514)
		if err515 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg516 := flag.Arg(2)
		mbTrans517 := thrift.NewTMemoryBufferLen(len(arg516))
		defer mbTrans517.Close()
		_, err518 := mbTrans517.WriteString(arg516)
		if err518 != nil {
			Usage()
			return
		}
		factory519 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt520 := factory519.GetProtocol(mbTrans517)
		argvalue1 := mqbroker.NewEventHeader()
		err521 := argvalue1.Read(jsProt520)
		if err521 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg522 := flag.Arg(3)
		mbTrans523 := thrift.NewTMemoryBufferLen(len(arg522))
		defer mbTrans523.Close()
		_, err524 := mbTrans523.WriteString(arg522)
		if err524 != nil {
			Usage()
			return
		}
		factory525 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt526 := factory525.GetProtocol(mbTrans523)
		argvalue2 := mqbroker.NewAdImpression()
		err527 := argvalue2.Read(jsProt526)
		if err527 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdImpression(argvalue2)
		fmt.Print(client.PubAdImpression(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdClick":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdClick requires 3 args")
			flag.Usage()
		}
		arg528 := flag.Arg(1)
		mbTrans529 := thrift.NewTMemoryBufferLen(len(arg528))
		defer mbTrans529.Close()
		_, err530 := mbTrans529.WriteString(arg528)
		if err530 != nil {
			Usage()
			return
		}
		factory531 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt532 := factory531.GetProtocol(mbTrans529)
		argvalue0 := mqbroker.NewRequestHeader()
		err533 := argvalue0.Read(jsProt532)
		if err533 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg534 := flag.Arg(2)
		mbTrans535 := thrift.NewTMemoryBufferLen(len(arg534))
		defer mbTrans535.Close()
		_, err536 := mbTrans535.WriteString(arg534)
		if err536 != nil {
			Usage()
			return
		}
		factory537 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt538 := factory537.GetProtocol(mbTrans535)
		argvalue1 := mqbroker.NewEventHeader()
		err539 := argvalue1.Read(jsProt538)
		if err539 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg540 := flag.Arg(3)
		mbTrans541 := thrift.NewTMemoryBufferLen(len(arg540))
		defer mbTrans541.Close()
		_, err542 := mbTrans541.WriteString(arg540)
		if err542 != nil {
			Usage()
			return
		}
		factory543 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt544 := factory543.GetProtocol(mbTrans541)
		argvalue2 := mqbroker.NewAdClick()
		err545 := argvalue2.Read(jsProt544)
		if err545 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdClick(argvalue2)
		fmt.Print(client.PubAdClick(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdDownload":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdDownload requires 3 args")
			flag.Usage()
		}
		arg546 := flag.Arg(1)
		mbTrans547 := thrift.NewTMemoryBufferLen(len(arg546))
		defer mbTrans547.Close()
		_, err548 := mbTrans547.WriteString(arg546)
		if err548 != nil {
			Usage()
			return
		}
		factory549 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt550 := factory549.GetProtocol(mbTrans547)
		argvalue0 := mqbroker.NewRequestHeader()
		err551 := argvalue0.Read(jsProt550)
		if err551 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg552 := flag.Arg(2)
		mbTrans553 := thrift.NewTMemoryBufferLen(len(arg552))
		defer mbTrans553.Close()
		_, err554 := mbTrans553.WriteString(arg552)
		if err554 != nil {
			Usage()
			return
		}
		factory555 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt556 := factory555.GetProtocol(mbTrans553)
		argvalue1 := mqbroker.NewEventHeader()
		err557 := argvalue1.Read(jsProt556)
		if err557 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg558 := flag.Arg(3)
		mbTrans559 := thrift.NewTMemoryBufferLen(len(arg558))
		defer mbTrans559.Close()
		_, err560 := mbTrans559.WriteString(arg558)
		if err560 != nil {
			Usage()
			return
		}
		factory561 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt562 := factory561.GetProtocol(mbTrans559)
		argvalue2 := mqbroker.NewAdDownload()
		err563 := argvalue2.Read(jsProt562)
		if err563 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdDownload(argvalue2)
		fmt.Print(client.PubAdDownload(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAdInstall":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAdInstall requires 3 args")
			flag.Usage()
		}
		arg564 := flag.Arg(1)
		mbTrans565 := thrift.NewTMemoryBufferLen(len(arg564))
		defer mbTrans565.Close()
		_, err566 := mbTrans565.WriteString(arg564)
		if err566 != nil {
			Usage()
			return
		}
		factory567 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt568 := factory567.GetProtocol(mbTrans565)
		argvalue0 := mqbroker.NewRequestHeader()
		err569 := argvalue0.Read(jsProt568)
		if err569 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg570 := flag.Arg(2)
		mbTrans571 := thrift.NewTMemoryBufferLen(len(arg570))
		defer mbTrans571.Close()
		_, err572 := mbTrans571.WriteString(arg570)
		if err572 != nil {
			Usage()
			return
		}
		factory573 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt574 := factory573.GetProtocol(mbTrans571)
		argvalue1 := mqbroker.NewEventHeader()
		err575 := argvalue1.Read(jsProt574)
		if err575 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg576 := flag.Arg(3)
		mbTrans577 := thrift.NewTMemoryBufferLen(len(arg576))
		defer mbTrans577.Close()
		_, err578 := mbTrans577.WriteString(arg576)
		if err578 != nil {
			Usage()
			return
		}
		factory579 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt580 := factory579.GetProtocol(mbTrans577)
		argvalue2 := mqbroker.NewAdInstall()
		err581 := argvalue2.Read(jsProt580)
		if err581 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AdInstall(argvalue2)
		fmt.Print(client.PubAdInstall(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubOwAct":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubOwAct requires 3 args")
			flag.Usage()
		}
		arg582 := flag.Arg(1)
		mbTrans583 := thrift.NewTMemoryBufferLen(len(arg582))
		defer mbTrans583.Close()
		_, err584 := mbTrans583.WriteString(arg582)
		if err584 != nil {
			Usage()
			return
		}
		factory585 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt586 := factory585.GetProtocol(mbTrans583)
		argvalue0 := mqbroker.NewRequestHeader()
		err587 := argvalue0.Read(jsProt586)
		if err587 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg588 := flag.Arg(2)
		mbTrans589 := thrift.NewTMemoryBufferLen(len(arg588))
		defer mbTrans589.Close()
		_, err590 := mbTrans589.WriteString(arg588)
		if err590 != nil {
			Usage()
			return
		}
		factory591 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt592 := factory591.GetProtocol(mbTrans589)
		argvalue1 := mqbroker.NewEventHeader()
		err593 := argvalue1.Read(jsProt592)
		if err593 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg594 := flag.Arg(3)
		mbTrans595 := thrift.NewTMemoryBufferLen(len(arg594))
		defer mbTrans595.Close()
		_, err596 := mbTrans595.WriteString(arg594)
		if err596 != nil {
			Usage()
			return
		}
		factory597 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt598 := factory597.GetProtocol(mbTrans595)
		argvalue2 := mqbroker.NewOwAct()
		err599 := argvalue2.Read(jsProt598)
		if err599 != nil {
			Usage()
			return
		}
		value2 := mqbroker.OwAct(argvalue2)
		fmt.Print(client.PubOwAct(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubFileReadyEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubFileReadyEvent requires 3 args")
			flag.Usage()
		}
		arg600 := flag.Arg(1)
		mbTrans601 := thrift.NewTMemoryBufferLen(len(arg600))
		defer mbTrans601.Close()
		_, err602 := mbTrans601.WriteString(arg600)
		if err602 != nil {
			Usage()
			return
		}
		factory603 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt604 := factory603.GetProtocol(mbTrans601)
		argvalue0 := mqbroker.NewRequestHeader()
		err605 := argvalue0.Read(jsProt604)
		if err605 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg606 := flag.Arg(2)
		mbTrans607 := thrift.NewTMemoryBufferLen(len(arg606))
		defer mbTrans607.Close()
		_, err608 := mbTrans607.WriteString(arg606)
		if err608 != nil {
			Usage()
			return
		}
		factory609 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt610 := factory609.GetProtocol(mbTrans607)
		argvalue1 := mqbroker.NewEventHeader()
		err611 := argvalue1.Read(jsProt610)
		if err611 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg612 := flag.Arg(3)
		mbTrans613 := thrift.NewTMemoryBufferLen(len(arg612))
		defer mbTrans613.Close()
		_, err614 := mbTrans613.WriteString(arg612)
		if err614 != nil {
			Usage()
			return
		}
		factory615 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt616 := factory615.GetProtocol(mbTrans613)
		argvalue2 := mqbroker.NewFileReadyEvent()
		err617 := argvalue2.Read(jsProt616)
		if err617 != nil {
			Usage()
			return
		}
		value2 := mqbroker.FileReadyEvent(argvalue2)
		fmt.Print(client.PubFileReadyEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubMediaUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubMediaUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg618 := flag.Arg(1)
		mbTrans619 := thrift.NewTMemoryBufferLen(len(arg618))
		defer mbTrans619.Close()
		_, err620 := mbTrans619.WriteString(arg618)
		if err620 != nil {
			Usage()
			return
		}
		factory621 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt622 := factory621.GetProtocol(mbTrans619)
		argvalue0 := mqbroker.NewRequestHeader()
		err623 := argvalue0.Read(jsProt622)
		if err623 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg624 := flag.Arg(2)
		mbTrans625 := thrift.NewTMemoryBufferLen(len(arg624))
		defer mbTrans625.Close()
		_, err626 := mbTrans625.WriteString(arg624)
		if err626 != nil {
			Usage()
			return
		}
		factory627 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt628 := factory627.GetProtocol(mbTrans625)
		argvalue1 := mqbroker.NewEventHeader()
		err629 := argvalue1.Read(jsProt628)
		if err629 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg630 := flag.Arg(3)
		mbTrans631 := thrift.NewTMemoryBufferLen(len(arg630))
		defer mbTrans631.Close()
		_, err632 := mbTrans631.WriteString(arg630)
		if err632 != nil {
			Usage()
			return
		}
		factory633 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt634 := factory633.GetProtocol(mbTrans631)
		argvalue2 := mqbroker.NewMediaUpdateEvent()
		err635 := argvalue2.Read(jsProt634)
		if err635 != nil {
			Usage()
			return
		}
		value2 := mqbroker.MediaUpdateEvent(argvalue2)
		fmt.Print(client.PubMediaUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubMediaAppUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubMediaAppUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg636 := flag.Arg(1)
		mbTrans637 := thrift.NewTMemoryBufferLen(len(arg636))
		defer mbTrans637.Close()
		_, err638 := mbTrans637.WriteString(arg636)
		if err638 != nil {
			Usage()
			return
		}
		factory639 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt640 := factory639.GetProtocol(mbTrans637)
		argvalue0 := mqbroker.NewRequestHeader()
		err641 := argvalue0.Read(jsProt640)
		if err641 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg642 := flag.Arg(2)
		mbTrans643 := thrift.NewTMemoryBufferLen(len(arg642))
		defer mbTrans643.Close()
		_, err644 := mbTrans643.WriteString(arg642)
		if err644 != nil {
			Usage()
			return
		}
		factory645 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt646 := factory645.GetProtocol(mbTrans643)
		argvalue1 := mqbroker.NewEventHeader()
		err647 := argvalue1.Read(jsProt646)
		if err647 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg648 := flag.Arg(3)
		mbTrans649 := thrift.NewTMemoryBufferLen(len(arg648))
		defer mbTrans649.Close()
		_, err650 := mbTrans649.WriteString(arg648)
		if err650 != nil {
			Usage()
			return
		}
		factory651 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt652 := factory651.GetProtocol(mbTrans649)
		argvalue2 := mqbroker.NewMediaAppUpdateEvent()
		err653 := argvalue2.Read(jsProt652)
		if err653 != nil {
			Usage()
			return
		}
		value2 := mqbroker.MediaAppUpdateEvent(argvalue2)
		fmt.Print(client.PubMediaAppUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubMediaAppRateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubMediaAppRateEvent requires 3 args")
			flag.Usage()
		}
		arg654 := flag.Arg(1)
		mbTrans655 := thrift.NewTMemoryBufferLen(len(arg654))
		defer mbTrans655.Close()
		_, err656 := mbTrans655.WriteString(arg654)
		if err656 != nil {
			Usage()
			return
		}
		factory657 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt658 := factory657.GetProtocol(mbTrans655)
		argvalue0 := mqbroker.NewRequestHeader()
		err659 := argvalue0.Read(jsProt658)
		if err659 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg660 := flag.Arg(2)
		mbTrans661 := thrift.NewTMemoryBufferLen(len(arg660))
		defer mbTrans661.Close()
		_, err662 := mbTrans661.WriteString(arg660)
		if err662 != nil {
			Usage()
			return
		}
		factory663 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt664 := factory663.GetProtocol(mbTrans661)
		argvalue1 := mqbroker.NewEventHeader()
		err665 := argvalue1.Read(jsProt664)
		if err665 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg666 := flag.Arg(3)
		mbTrans667 := thrift.NewTMemoryBufferLen(len(arg666))
		defer mbTrans667.Close()
		_, err668 := mbTrans667.WriteString(arg666)
		if err668 != nil {
			Usage()
			return
		}
		factory669 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt670 := factory669.GetProtocol(mbTrans667)
		argvalue2 := mqbroker.NewMediaAppRateEvent()
		err671 := argvalue2.Read(jsProt670)
		if err671 != nil {
			Usage()
			return
		}
		value2 := mqbroker.MediaAppRateEvent(argvalue2)
		fmt.Print(client.PubMediaAppRateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubIdentityAuditEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubIdentityAuditEvent requires 3 args")
			flag.Usage()
		}
		arg672 := flag.Arg(1)
		mbTrans673 := thrift.NewTMemoryBufferLen(len(arg672))
		defer mbTrans673.Close()
		_, err674 := mbTrans673.WriteString(arg672)
		if err674 != nil {
			Usage()
			return
		}
		factory675 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt676 := factory675.GetProtocol(mbTrans673)
		argvalue0 := mqbroker.NewRequestHeader()
		err677 := argvalue0.Read(jsProt676)
		if err677 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg678 := flag.Arg(2)
		mbTrans679 := thrift.NewTMemoryBufferLen(len(arg678))
		defer mbTrans679.Close()
		_, err680 := mbTrans679.WriteString(arg678)
		if err680 != nil {
			Usage()
			return
		}
		factory681 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt682 := factory681.GetProtocol(mbTrans679)
		argvalue1 := mqbroker.NewEventHeader()
		err683 := argvalue1.Read(jsProt682)
		if err683 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg684 := flag.Arg(3)
		mbTrans685 := thrift.NewTMemoryBufferLen(len(arg684))
		defer mbTrans685.Close()
		_, err686 := mbTrans685.WriteString(arg684)
		if err686 != nil {
			Usage()
			return
		}
		factory687 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt688 := factory687.GetProtocol(mbTrans685)
		argvalue2 := mqbroker.NewIdentityAuditEvent()
		err689 := argvalue2.Read(jsProt688)
		if err689 != nil {
			Usage()
			return
		}
		value2 := mqbroker.IdentityAuditEvent(argvalue2)
		fmt.Print(client.PubIdentityAuditEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAppInfoUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAppInfoUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg690 := flag.Arg(1)
		mbTrans691 := thrift.NewTMemoryBufferLen(len(arg690))
		defer mbTrans691.Close()
		_, err692 := mbTrans691.WriteString(arg690)
		if err692 != nil {
			Usage()
			return
		}
		factory693 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt694 := factory693.GetProtocol(mbTrans691)
		argvalue0 := mqbroker.NewRequestHeader()
		err695 := argvalue0.Read(jsProt694)
		if err695 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg696 := flag.Arg(2)
		mbTrans697 := thrift.NewTMemoryBufferLen(len(arg696))
		defer mbTrans697.Close()
		_, err698 := mbTrans697.WriteString(arg696)
		if err698 != nil {
			Usage()
			return
		}
		factory699 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt700 := factory699.GetProtocol(mbTrans697)
		argvalue1 := mqbroker.NewEventHeader()
		err701 := argvalue1.Read(jsProt700)
		if err701 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg702 := flag.Arg(3)
		mbTrans703 := thrift.NewTMemoryBufferLen(len(arg702))
		defer mbTrans703.Close()
		_, err704 := mbTrans703.WriteString(arg702)
		if err704 != nil {
			Usage()
			return
		}
		factory705 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt706 := factory705.GetProtocol(mbTrans703)
		argvalue2 := mqbroker.NewAppInfoUpdateEvent()
		err707 := argvalue2.Read(jsProt706)
		if err707 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AppInfoUpdateEvent(argvalue2)
		fmt.Print(client.PubAppInfoUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubAppChannelUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubAppChannelUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg708 := flag.Arg(1)
		mbTrans709 := thrift.NewTMemoryBufferLen(len(arg708))
		defer mbTrans709.Close()
		_, err710 := mbTrans709.WriteString(arg708)
		if err710 != nil {
			Usage()
			return
		}
		factory711 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt712 := factory711.GetProtocol(mbTrans709)
		argvalue0 := mqbroker.NewRequestHeader()
		err713 := argvalue0.Read(jsProt712)
		if err713 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg714 := flag.Arg(2)
		mbTrans715 := thrift.NewTMemoryBufferLen(len(arg714))
		defer mbTrans715.Close()
		_, err716 := mbTrans715.WriteString(arg714)
		if err716 != nil {
			Usage()
			return
		}
		factory717 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt718 := factory717.GetProtocol(mbTrans715)
		argvalue1 := mqbroker.NewEventHeader()
		err719 := argvalue1.Read(jsProt718)
		if err719 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg720 := flag.Arg(3)
		mbTrans721 := thrift.NewTMemoryBufferLen(len(arg720))
		defer mbTrans721.Close()
		_, err722 := mbTrans721.WriteString(arg720)
		if err722 != nil {
			Usage()
			return
		}
		factory723 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt724 := factory723.GetProtocol(mbTrans721)
		argvalue2 := mqbroker.NewAppChannelUpdateEvent()
		err725 := argvalue2.Read(jsProt724)
		if err725 != nil {
			Usage()
			return
		}
		value2 := mqbroker.AppChannelUpdateEvent(argvalue2)
		fmt.Print(client.PubAppChannelUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBCampaignUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBCampaignUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg726 := flag.Arg(1)
		mbTrans727 := thrift.NewTMemoryBufferLen(len(arg726))
		defer mbTrans727.Close()
		_, err728 := mbTrans727.WriteString(arg726)
		if err728 != nil {
			Usage()
			return
		}
		factory729 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt730 := factory729.GetProtocol(mbTrans727)
		argvalue0 := mqbroker.NewRequestHeader()
		err731 := argvalue0.Read(jsProt730)
		if err731 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg732 := flag.Arg(2)
		mbTrans733 := thrift.NewTMemoryBufferLen(len(arg732))
		defer mbTrans733.Close()
		_, err734 := mbTrans733.WriteString(arg732)
		if err734 != nil {
			Usage()
			return
		}
		factory735 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt736 := factory735.GetProtocol(mbTrans733)
		argvalue1 := mqbroker.NewEventHeader()
		err737 := argvalue1.Read(jsProt736)
		if err737 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg738 := flag.Arg(3)
		mbTrans739 := thrift.NewTMemoryBufferLen(len(arg738))
		defer mbTrans739.Close()
		_, err740 := mbTrans739.WriteString(arg738)
		if err740 != nil {
			Usage()
			return
		}
		factory741 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt742 := factory741.GetProtocol(mbTrans739)
		argvalue2 := mqbroker.NewRTBCampaignUpdateEvent()
		err743 := argvalue2.Read(jsProt742)
		if err743 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBCampaignUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBCampaignUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBCreativeUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBCreativeUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg744 := flag.Arg(1)
		mbTrans745 := thrift.NewTMemoryBufferLen(len(arg744))
		defer mbTrans745.Close()
		_, err746 := mbTrans745.WriteString(arg744)
		if err746 != nil {
			Usage()
			return
		}
		factory747 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt748 := factory747.GetProtocol(mbTrans745)
		argvalue0 := mqbroker.NewRequestHeader()
		err749 := argvalue0.Read(jsProt748)
		if err749 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg750 := flag.Arg(2)
		mbTrans751 := thrift.NewTMemoryBufferLen(len(arg750))
		defer mbTrans751.Close()
		_, err752 := mbTrans751.WriteString(arg750)
		if err752 != nil {
			Usage()
			return
		}
		factory753 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt754 := factory753.GetProtocol(mbTrans751)
		argvalue1 := mqbroker.NewEventHeader()
		err755 := argvalue1.Read(jsProt754)
		if err755 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg756 := flag.Arg(3)
		mbTrans757 := thrift.NewTMemoryBufferLen(len(arg756))
		defer mbTrans757.Close()
		_, err758 := mbTrans757.WriteString(arg756)
		if err758 != nil {
			Usage()
			return
		}
		factory759 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt760 := factory759.GetProtocol(mbTrans757)
		argvalue2 := mqbroker.NewRTBCreativeUpdateEvent()
		err761 := argvalue2.Read(jsProt760)
		if err761 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBCreativeUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBCreativeUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBStrategyUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBStrategyUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg762 := flag.Arg(1)
		mbTrans763 := thrift.NewTMemoryBufferLen(len(arg762))
		defer mbTrans763.Close()
		_, err764 := mbTrans763.WriteString(arg762)
		if err764 != nil {
			Usage()
			return
		}
		factory765 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt766 := factory765.GetProtocol(mbTrans763)
		argvalue0 := mqbroker.NewRequestHeader()
		err767 := argvalue0.Read(jsProt766)
		if err767 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg768 := flag.Arg(2)
		mbTrans769 := thrift.NewTMemoryBufferLen(len(arg768))
		defer mbTrans769.Close()
		_, err770 := mbTrans769.WriteString(arg768)
		if err770 != nil {
			Usage()
			return
		}
		factory771 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt772 := factory771.GetProtocol(mbTrans769)
		argvalue1 := mqbroker.NewEventHeader()
		err773 := argvalue1.Read(jsProt772)
		if err773 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg774 := flag.Arg(3)
		mbTrans775 := thrift.NewTMemoryBufferLen(len(arg774))
		defer mbTrans775.Close()
		_, err776 := mbTrans775.WriteString(arg774)
		if err776 != nil {
			Usage()
			return
		}
		factory777 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt778 := factory777.GetProtocol(mbTrans775)
		argvalue2 := mqbroker.NewRTBStrategyUpdateEvent()
		err779 := argvalue2.Read(jsProt778)
		if err779 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBStrategyUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBStrategyUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBSponsorUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBSponsorUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg780 := flag.Arg(1)
		mbTrans781 := thrift.NewTMemoryBufferLen(len(arg780))
		defer mbTrans781.Close()
		_, err782 := mbTrans781.WriteString(arg780)
		if err782 != nil {
			Usage()
			return
		}
		factory783 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt784 := factory783.GetProtocol(mbTrans781)
		argvalue0 := mqbroker.NewRequestHeader()
		err785 := argvalue0.Read(jsProt784)
		if err785 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg786 := flag.Arg(2)
		mbTrans787 := thrift.NewTMemoryBufferLen(len(arg786))
		defer mbTrans787.Close()
		_, err788 := mbTrans787.WriteString(arg786)
		if err788 != nil {
			Usage()
			return
		}
		factory789 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt790 := factory789.GetProtocol(mbTrans787)
		argvalue1 := mqbroker.NewEventHeader()
		err791 := argvalue1.Read(jsProt790)
		if err791 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg792 := flag.Arg(3)
		mbTrans793 := thrift.NewTMemoryBufferLen(len(arg792))
		defer mbTrans793.Close()
		_, err794 := mbTrans793.WriteString(arg792)
		if err794 != nil {
			Usage()
			return
		}
		factory795 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt796 := factory795.GetProtocol(mbTrans793)
		argvalue2 := mqbroker.NewRTBSponsorUpdateEvent()
		err797 := argvalue2.Read(jsProt796)
		if err797 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBSponsorUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBSponsorUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBAdTrackingUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBAdTrackingUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg798 := flag.Arg(1)
		mbTrans799 := thrift.NewTMemoryBufferLen(len(arg798))
		defer mbTrans799.Close()
		_, err800 := mbTrans799.WriteString(arg798)
		if err800 != nil {
			Usage()
			return
		}
		factory801 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt802 := factory801.GetProtocol(mbTrans799)
		argvalue0 := mqbroker.NewRequestHeader()
		err803 := argvalue0.Read(jsProt802)
		if err803 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg804 := flag.Arg(2)
		mbTrans805 := thrift.NewTMemoryBufferLen(len(arg804))
		defer mbTrans805.Close()
		_, err806 := mbTrans805.WriteString(arg804)
		if err806 != nil {
			Usage()
			return
		}
		factory807 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt808 := factory807.GetProtocol(mbTrans805)
		argvalue1 := mqbroker.NewEventHeader()
		err809 := argvalue1.Read(jsProt808)
		if err809 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg810 := flag.Arg(3)
		mbTrans811 := thrift.NewTMemoryBufferLen(len(arg810))
		defer mbTrans811.Close()
		_, err812 := mbTrans811.WriteString(arg810)
		if err812 != nil {
			Usage()
			return
		}
		factory813 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt814 := factory813.GetProtocol(mbTrans811)
		argvalue2 := mqbroker.NewRTBAdTrackingUpdateEvent()
		err815 := argvalue2.Read(jsProt814)
		if err815 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBAdTrackingUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBAdTrackingUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBPromotionUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBPromotionUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg816 := flag.Arg(1)
		mbTrans817 := thrift.NewTMemoryBufferLen(len(arg816))
		defer mbTrans817.Close()
		_, err818 := mbTrans817.WriteString(arg816)
		if err818 != nil {
			Usage()
			return
		}
		factory819 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt820 := factory819.GetProtocol(mbTrans817)
		argvalue0 := mqbroker.NewRequestHeader()
		err821 := argvalue0.Read(jsProt820)
		if err821 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg822 := flag.Arg(2)
		mbTrans823 := thrift.NewTMemoryBufferLen(len(arg822))
		defer mbTrans823.Close()
		_, err824 := mbTrans823.WriteString(arg822)
		if err824 != nil {
			Usage()
			return
		}
		factory825 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt826 := factory825.GetProtocol(mbTrans823)
		argvalue1 := mqbroker.NewEventHeader()
		err827 := argvalue1.Read(jsProt826)
		if err827 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg828 := flag.Arg(3)
		mbTrans829 := thrift.NewTMemoryBufferLen(len(arg828))
		defer mbTrans829.Close()
		_, err830 := mbTrans829.WriteString(arg828)
		if err830 != nil {
			Usage()
			return
		}
		factory831 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt832 := factory831.GetProtocol(mbTrans829)
		argvalue2 := mqbroker.NewRTBPromotionUpdateEvent()
		err833 := argvalue2.Read(jsProt832)
		if err833 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBPromotionUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBPromotionUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBAdInfoFlushEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBAdInfoFlushEvent requires 3 args")
			flag.Usage()
		}
		arg834 := flag.Arg(1)
		mbTrans835 := thrift.NewTMemoryBufferLen(len(arg834))
		defer mbTrans835.Close()
		_, err836 := mbTrans835.WriteString(arg834)
		if err836 != nil {
			Usage()
			return
		}
		factory837 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt838 := factory837.GetProtocol(mbTrans835)
		argvalue0 := mqbroker.NewRequestHeader()
		err839 := argvalue0.Read(jsProt838)
		if err839 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg840 := flag.Arg(2)
		mbTrans841 := thrift.NewTMemoryBufferLen(len(arg840))
		defer mbTrans841.Close()
		_, err842 := mbTrans841.WriteString(arg840)
		if err842 != nil {
			Usage()
			return
		}
		factory843 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt844 := factory843.GetProtocol(mbTrans841)
		argvalue1 := mqbroker.NewEventHeader()
		err845 := argvalue1.Read(jsProt844)
		if err845 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg846 := flag.Arg(3)
		mbTrans847 := thrift.NewTMemoryBufferLen(len(arg846))
		defer mbTrans847.Close()
		_, err848 := mbTrans847.WriteString(arg846)
		if err848 != nil {
			Usage()
			return
		}
		factory849 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt850 := factory849.GetProtocol(mbTrans847)
		argvalue2 := mqbroker.NewRTBAdInfoFlushEvent()
		err851 := argvalue2.Read(jsProt850)
		if err851 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBAdInfoFlushEvent(argvalue2)
		fmt.Print(client.PubRTBAdInfoFlushEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBCampaignTotalBudgetEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBCampaignTotalBudgetEvent requires 3 args")
			flag.Usage()
		}
		arg852 := flag.Arg(1)
		mbTrans853 := thrift.NewTMemoryBufferLen(len(arg852))
		defer mbTrans853.Close()
		_, err854 := mbTrans853.WriteString(arg852)
		if err854 != nil {
			Usage()
			return
		}
		factory855 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt856 := factory855.GetProtocol(mbTrans853)
		argvalue0 := mqbroker.NewRequestHeader()
		err857 := argvalue0.Read(jsProt856)
		if err857 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg858 := flag.Arg(2)
		mbTrans859 := thrift.NewTMemoryBufferLen(len(arg858))
		defer mbTrans859.Close()
		_, err860 := mbTrans859.WriteString(arg858)
		if err860 != nil {
			Usage()
			return
		}
		factory861 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt862 := factory861.GetProtocol(mbTrans859)
		argvalue1 := mqbroker.NewEventHeader()
		err863 := argvalue1.Read(jsProt862)
		if err863 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg864 := flag.Arg(3)
		mbTrans865 := thrift.NewTMemoryBufferLen(len(arg864))
		defer mbTrans865.Close()
		_, err866 := mbTrans865.WriteString(arg864)
		if err866 != nil {
			Usage()
			return
		}
		factory867 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt868 := factory867.GetProtocol(mbTrans865)
		argvalue2 := mqbroker.NewRTBCampaignTotalBudgetEvent()
		err869 := argvalue2.Read(jsProt868)
		if err869 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBCampaignTotalBudgetEvent(argvalue2)
		fmt.Print(client.PubRTBCampaignTotalBudgetEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBCampaignDailyBudgetEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBCampaignDailyBudgetEvent requires 3 args")
			flag.Usage()
		}
		arg870 := flag.Arg(1)
		mbTrans871 := thrift.NewTMemoryBufferLen(len(arg870))
		defer mbTrans871.Close()
		_, err872 := mbTrans871.WriteString(arg870)
		if err872 != nil {
			Usage()
			return
		}
		factory873 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt874 := factory873.GetProtocol(mbTrans871)
		argvalue0 := mqbroker.NewRequestHeader()
		err875 := argvalue0.Read(jsProt874)
		if err875 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg876 := flag.Arg(2)
		mbTrans877 := thrift.NewTMemoryBufferLen(len(arg876))
		defer mbTrans877.Close()
		_, err878 := mbTrans877.WriteString(arg876)
		if err878 != nil {
			Usage()
			return
		}
		factory879 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt880 := factory879.GetProtocol(mbTrans877)
		argvalue1 := mqbroker.NewEventHeader()
		err881 := argvalue1.Read(jsProt880)
		if err881 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg882 := flag.Arg(3)
		mbTrans883 := thrift.NewTMemoryBufferLen(len(arg882))
		defer mbTrans883.Close()
		_, err884 := mbTrans883.WriteString(arg882)
		if err884 != nil {
			Usage()
			return
		}
		factory885 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt886 := factory885.GetProtocol(mbTrans883)
		argvalue2 := mqbroker.NewRTBCampaignDailyBudgetEvent()
		err887 := argvalue2.Read(jsProt886)
		if err887 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBCampaignDailyBudgetEvent(argvalue2)
		fmt.Print(client.PubRTBCampaignDailyBudgetEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBStrategyDailyBudgetEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBStrategyDailyBudgetEvent requires 3 args")
			flag.Usage()
		}
		arg888 := flag.Arg(1)
		mbTrans889 := thrift.NewTMemoryBufferLen(len(arg888))
		defer mbTrans889.Close()
		_, err890 := mbTrans889.WriteString(arg888)
		if err890 != nil {
			Usage()
			return
		}
		factory891 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt892 := factory891.GetProtocol(mbTrans889)
		argvalue0 := mqbroker.NewRequestHeader()
		err893 := argvalue0.Read(jsProt892)
		if err893 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg894 := flag.Arg(2)
		mbTrans895 := thrift.NewTMemoryBufferLen(len(arg894))
		defer mbTrans895.Close()
		_, err896 := mbTrans895.WriteString(arg894)
		if err896 != nil {
			Usage()
			return
		}
		factory897 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt898 := factory897.GetProtocol(mbTrans895)
		argvalue1 := mqbroker.NewEventHeader()
		err899 := argvalue1.Read(jsProt898)
		if err899 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg900 := flag.Arg(3)
		mbTrans901 := thrift.NewTMemoryBufferLen(len(arg900))
		defer mbTrans901.Close()
		_, err902 := mbTrans901.WriteString(arg900)
		if err902 != nil {
			Usage()
			return
		}
		factory903 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt904 := factory903.GetProtocol(mbTrans901)
		argvalue2 := mqbroker.NewRTBStrategyDailyBudgetEvent()
		err905 := argvalue2.Read(jsProt904)
		if err905 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBStrategyDailyBudgetEvent(argvalue2)
		fmt.Print(client.PubRTBStrategyDailyBudgetEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBPoiInfoUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBPoiInfoUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg906 := flag.Arg(1)
		mbTrans907 := thrift.NewTMemoryBufferLen(len(arg906))
		defer mbTrans907.Close()
		_, err908 := mbTrans907.WriteString(arg906)
		if err908 != nil {
			Usage()
			return
		}
		factory909 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt910 := factory909.GetProtocol(mbTrans907)
		argvalue0 := mqbroker.NewRequestHeader()
		err911 := argvalue0.Read(jsProt910)
		if err911 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg912 := flag.Arg(2)
		mbTrans913 := thrift.NewTMemoryBufferLen(len(arg912))
		defer mbTrans913.Close()
		_, err914 := mbTrans913.WriteString(arg912)
		if err914 != nil {
			Usage()
			return
		}
		factory915 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt916 := factory915.GetProtocol(mbTrans913)
		argvalue1 := mqbroker.NewEventHeader()
		err917 := argvalue1.Read(jsProt916)
		if err917 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg918 := flag.Arg(3)
		mbTrans919 := thrift.NewTMemoryBufferLen(len(arg918))
		defer mbTrans919.Close()
		_, err920 := mbTrans919.WriteString(arg918)
		if err920 != nil {
			Usage()
			return
		}
		factory921 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt922 := factory921.GetProtocol(mbTrans919)
		argvalue2 := mqbroker.NewRTBPoiInfoUpdateEvent()
		err923 := argvalue2.Read(jsProt922)
		if err923 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBPoiInfoUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBPoiInfoUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubRTBPoiGroupUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubRTBPoiGroupUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg924 := flag.Arg(1)
		mbTrans925 := thrift.NewTMemoryBufferLen(len(arg924))
		defer mbTrans925.Close()
		_, err926 := mbTrans925.WriteString(arg924)
		if err926 != nil {
			Usage()
			return
		}
		factory927 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt928 := factory927.GetProtocol(mbTrans925)
		argvalue0 := mqbroker.NewRequestHeader()
		err929 := argvalue0.Read(jsProt928)
		if err929 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg930 := flag.Arg(2)
		mbTrans931 := thrift.NewTMemoryBufferLen(len(arg930))
		defer mbTrans931.Close()
		_, err932 := mbTrans931.WriteString(arg930)
		if err932 != nil {
			Usage()
			return
		}
		factory933 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt934 := factory933.GetProtocol(mbTrans931)
		argvalue1 := mqbroker.NewEventHeader()
		err935 := argvalue1.Read(jsProt934)
		if err935 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg936 := flag.Arg(3)
		mbTrans937 := thrift.NewTMemoryBufferLen(len(arg936))
		defer mbTrans937.Close()
		_, err938 := mbTrans937.WriteString(arg936)
		if err938 != nil {
			Usage()
			return
		}
		factory939 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt940 := factory939.GetProtocol(mbTrans937)
		argvalue2 := mqbroker.NewRTBPoiGroupUpdateEvent()
		err941 := argvalue2.Read(jsProt940)
		if err941 != nil {
			Usage()
			return
		}
		value2 := mqbroker.RTBPoiGroupUpdateEvent(argvalue2)
		fmt.Print(client.PubRTBPoiGroupUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubProjectUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubProjectUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg942 := flag.Arg(1)
		mbTrans943 := thrift.NewTMemoryBufferLen(len(arg942))
		defer mbTrans943.Close()
		_, err944 := mbTrans943.WriteString(arg942)
		if err944 != nil {
			Usage()
			return
		}
		factory945 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt946 := factory945.GetProtocol(mbTrans943)
		argvalue0 := mqbroker.NewRequestHeader()
		err947 := argvalue0.Read(jsProt946)
		if err947 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg948 := flag.Arg(2)
		mbTrans949 := thrift.NewTMemoryBufferLen(len(arg948))
		defer mbTrans949.Close()
		_, err950 := mbTrans949.WriteString(arg948)
		if err950 != nil {
			Usage()
			return
		}
		factory951 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt952 := factory951.GetProtocol(mbTrans949)
		argvalue1 := mqbroker.NewEventHeader()
		err953 := argvalue1.Read(jsProt952)
		if err953 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg954 := flag.Arg(3)
		mbTrans955 := thrift.NewTMemoryBufferLen(len(arg954))
		defer mbTrans955.Close()
		_, err956 := mbTrans955.WriteString(arg954)
		if err956 != nil {
			Usage()
			return
		}
		factory957 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt958 := factory957.GetProtocol(mbTrans955)
		argvalue2 := mqbroker.NewProjectUpdateEvent()
		err959 := argvalue2.Read(jsProt958)
		if err959 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubProjectUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubSchedulesUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubSchedulesUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg960 := flag.Arg(1)
		mbTrans961 := thrift.NewTMemoryBufferLen(len(arg960))
		defer mbTrans961.Close()
		_, err962 := mbTrans961.WriteString(arg960)
		if err962 != nil {
			Usage()
			return
		}
		factory963 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt964 := factory963.GetProtocol(mbTrans961)
		argvalue0 := mqbroker.NewRequestHeader()
		err965 := argvalue0.Read(jsProt964)
		if err965 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg966 := flag.Arg(2)
		mbTrans967 := thrift.NewTMemoryBufferLen(len(arg966))
		defer mbTrans967.Close()
		_, err968 := mbTrans967.WriteString(arg966)
		if err968 != nil {
			Usage()
			return
		}
		factory969 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt970 := factory969.GetProtocol(mbTrans967)
		argvalue1 := mqbroker.NewEventHeader()
		err971 := argvalue1.Read(jsProt970)
		if err971 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg972 := flag.Arg(3)
		mbTrans973 := thrift.NewTMemoryBufferLen(len(arg972))
		defer mbTrans973.Close()
		_, err974 := mbTrans973.WriteString(arg972)
		if err974 != nil {
			Usage()
			return
		}
		factory975 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt976 := factory975.GetProtocol(mbTrans973)
		argvalue2 := mqbroker.NewSchedulesUpdateEvent()
		err977 := argvalue2.Read(jsProt976)
		if err977 != nil {
			Usage()
			return
		}
		value2 := mqbroker.SchedulesUpdateEvent(argvalue2)
		fmt.Print(client.PubSchedulesUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDbmCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDbmCommonEvent requires 3 args")
			flag.Usage()
		}
		arg978 := flag.Arg(1)
		mbTrans979 := thrift.NewTMemoryBufferLen(len(arg978))
		defer mbTrans979.Close()
		_, err980 := mbTrans979.WriteString(arg978)
		if err980 != nil {
			Usage()
			return
		}
		factory981 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt982 := factory981.GetProtocol(mbTrans979)
		argvalue0 := mqbroker.NewRequestHeader()
		err983 := argvalue0.Read(jsProt982)
		if err983 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg984 := flag.Arg(2)
		mbTrans985 := thrift.NewTMemoryBufferLen(len(arg984))
		defer mbTrans985.Close()
		_, err986 := mbTrans985.WriteString(arg984)
		if err986 != nil {
			Usage()
			return
		}
		factory987 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt988 := factory987.GetProtocol(mbTrans985)
		argvalue1 := mqbroker.NewEventHeader()
		err989 := argvalue1.Read(jsProt988)
		if err989 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg990 := flag.Arg(3)
		mbTrans991 := thrift.NewTMemoryBufferLen(len(arg990))
		defer mbTrans991.Close()
		_, err992 := mbTrans991.WriteString(arg990)
		if err992 != nil {
			Usage()
			return
		}
		factory993 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt994 := factory993.GetProtocol(mbTrans991)
		argvalue2 := mqbroker.NewDbmCommonEvent()
		err995 := argvalue2.Read(jsProt994)
		if err995 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDbmCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDmpCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDmpCommonEvent requires 3 args")
			flag.Usage()
		}
		arg996 := flag.Arg(1)
		mbTrans997 := thrift.NewTMemoryBufferLen(len(arg996))
		defer mbTrans997.Close()
		_, err998 := mbTrans997.WriteString(arg996)
		if err998 != nil {
			Usage()
			return
		}
		factory999 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1000 := factory999.GetProtocol(mbTrans997)
		argvalue0 := mqbroker.NewRequestHeader()
		err1001 := argvalue0.Read(jsProt1000)
		if err1001 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1002 := flag.Arg(2)
		mbTrans1003 := thrift.NewTMemoryBufferLen(len(arg1002))
		defer mbTrans1003.Close()
		_, err1004 := mbTrans1003.WriteString(arg1002)
		if err1004 != nil {
			Usage()
			return
		}
		factory1005 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1006 := factory1005.GetProtocol(mbTrans1003)
		argvalue1 := mqbroker.NewEventHeader()
		err1007 := argvalue1.Read(jsProt1006)
		if err1007 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1008 := flag.Arg(3)
		mbTrans1009 := thrift.NewTMemoryBufferLen(len(arg1008))
		defer mbTrans1009.Close()
		_, err1010 := mbTrans1009.WriteString(arg1008)
		if err1010 != nil {
			Usage()
			return
		}
		factory1011 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1012 := factory1011.GetProtocol(mbTrans1009)
		argvalue2 := mqbroker.NewDmpCommonEvent()
		err1013 := argvalue2.Read(jsProt1012)
		if err1013 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDmpCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDbmOperationEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDbmOperationEvent requires 3 args")
			flag.Usage()
		}
		arg1014 := flag.Arg(1)
		mbTrans1015 := thrift.NewTMemoryBufferLen(len(arg1014))
		defer mbTrans1015.Close()
		_, err1016 := mbTrans1015.WriteString(arg1014)
		if err1016 != nil {
			Usage()
			return
		}
		factory1017 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1018 := factory1017.GetProtocol(mbTrans1015)
		argvalue0 := mqbroker.NewRequestHeader()
		err1019 := argvalue0.Read(jsProt1018)
		if err1019 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1020 := flag.Arg(2)
		mbTrans1021 := thrift.NewTMemoryBufferLen(len(arg1020))
		defer mbTrans1021.Close()
		_, err1022 := mbTrans1021.WriteString(arg1020)
		if err1022 != nil {
			Usage()
			return
		}
		factory1023 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1024 := factory1023.GetProtocol(mbTrans1021)
		argvalue1 := mqbroker.NewEventHeader()
		err1025 := argvalue1.Read(jsProt1024)
		if err1025 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg1026 := flag.Arg(3)
		mbTrans1027 := thrift.NewTMemoryBufferLen(len(arg1026))
		defer mbTrans1027.Close()
		_, err1028 := mbTrans1027.WriteString(arg1026)
		if err1028 != nil {
			Usage()
			return
		}
		factory1029 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1030 := factory1029.GetProtocol(mbTrans1027)
		argvalue2 := mqbroker.NewDbmOperationEvent()
		err1031 := argvalue2.Read(jsProt1030)
		if err1031 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDbmOperationEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDmpOperationEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDmpOperationEvent requires 3 args")
			flag.Usage()
		}
		arg1032 := flag.Arg(1)
		mbTrans1033 := thrift.NewTMemoryBufferLen(len(arg1032))
		defer mbTrans1033.Close()
		_, err1034 := mbTrans1033.WriteString(arg1032)
		if err1034 != nil {
			Usage()
			return
		}
		factory1035 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1036 := factory1035.GetProtocol(mbTrans1033)
		argvalue0 := mqbroker.NewRequestHeader()
		err1037 := argvalue0.Read(jsProt1036)
		if err1037 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1038 := flag.Arg(2)
		mbTrans1039 := thrift.NewTMemoryBufferLen(len(arg1038))
		defer mbTrans1039.Close()
		_, err1040 := mbTrans1039.WriteString(arg1038)
		if err1040 != nil {
			Usage()
			return
		}
		factory1041 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1042 := factory1041.GetProtocol(mbTrans1039)
		argvalue1 := mqbroker.NewEventHeader()
		err1043 := argvalue1.Read(jsProt1042)
		if err1043 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg1044 := flag.Arg(3)
		mbTrans1045 := thrift.NewTMemoryBufferLen(len(arg1044))
		defer mbTrans1045.Close()
		_, err1046 := mbTrans1045.WriteString(arg1044)
		if err1046 != nil {
			Usage()
			return
		}
		factory1047 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1048 := factory1047.GetProtocol(mbTrans1045)
		argvalue2 := mqbroker.NewDmpOperationEvent()
		err1049 := argvalue2.Read(jsProt1048)
		if err1049 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDmpOperationEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDPMPromotionUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDPMPromotionUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg1050 := flag.Arg(1)
		mbTrans1051 := thrift.NewTMemoryBufferLen(len(arg1050))
		defer mbTrans1051.Close()
		_, err1052 := mbTrans1051.WriteString(arg1050)
		if err1052 != nil {
			Usage()
			return
		}
		factory1053 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1054 := factory1053.GetProtocol(mbTrans1051)
		argvalue0 := mqbroker.NewRequestHeader()
		err1055 := argvalue0.Read(jsProt1054)
		if err1055 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1056 := flag.Arg(2)
		mbTrans1057 := thrift.NewTMemoryBufferLen(len(arg1056))
		defer mbTrans1057.Close()
		_, err1058 := mbTrans1057.WriteString(arg1056)
		if err1058 != nil {
			Usage()
			return
		}
		factory1059 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1060 := factory1059.GetProtocol(mbTrans1057)
		argvalue1 := mqbroker.NewEventHeader()
		err1061 := argvalue1.Read(jsProt1060)
		if err1061 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1062 := flag.Arg(3)
		mbTrans1063 := thrift.NewTMemoryBufferLen(len(arg1062))
		defer mbTrans1063.Close()
		_, err1064 := mbTrans1063.WriteString(arg1062)
		if err1064 != nil {
			Usage()
			return
		}
		factory1065 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1066 := factory1065.GetProtocol(mbTrans1063)
		argvalue2 := mqbroker.NewDPMPromotionUpdateEvent()
		err1067 := argvalue2.Read(jsProt1066)
		if err1067 != nil {
			Usage()
			return
		}
		value2 := mqbroker.DPMPromotionUpdateEvent(argvalue2)
		fmt.Print(client.PubDPMPromotionUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDPMPromotionPropertyUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDPMPromotionPropertyUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg1068 := flag.Arg(1)
		mbTrans1069 := thrift.NewTMemoryBufferLen(len(arg1068))
		defer mbTrans1069.Close()
		_, err1070 := mbTrans1069.WriteString(arg1068)
		if err1070 != nil {
			Usage()
			return
		}
		factory1071 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1072 := factory1071.GetProtocol(mbTrans1069)
		argvalue0 := mqbroker.NewRequestHeader()
		err1073 := argvalue0.Read(jsProt1072)
		if err1073 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1074 := flag.Arg(2)
		mbTrans1075 := thrift.NewTMemoryBufferLen(len(arg1074))
		defer mbTrans1075.Close()
		_, err1076 := mbTrans1075.WriteString(arg1074)
		if err1076 != nil {
			Usage()
			return
		}
		factory1077 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1078 := factory1077.GetProtocol(mbTrans1075)
		argvalue1 := mqbroker.NewEventHeader()
		err1079 := argvalue1.Read(jsProt1078)
		if err1079 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1080 := flag.Arg(3)
		mbTrans1081 := thrift.NewTMemoryBufferLen(len(arg1080))
		defer mbTrans1081.Close()
		_, err1082 := mbTrans1081.WriteString(arg1080)
		if err1082 != nil {
			Usage()
			return
		}
		factory1083 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1084 := factory1083.GetProtocol(mbTrans1081)
		argvalue2 := mqbroker.NewDPMPromotionPropertyUpdateEvent()
		err1085 := argvalue2.Read(jsProt1084)
		if err1085 != nil {
			Usage()
			return
		}
		value2 := mqbroker.DPMPromotionPropertyUpdateEvent(argvalue2)
		fmt.Print(client.PubDPMPromotionPropertyUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDPMChannelUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDPMChannelUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg1086 := flag.Arg(1)
		mbTrans1087 := thrift.NewTMemoryBufferLen(len(arg1086))
		defer mbTrans1087.Close()
		_, err1088 := mbTrans1087.WriteString(arg1086)
		if err1088 != nil {
			Usage()
			return
		}
		factory1089 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1090 := factory1089.GetProtocol(mbTrans1087)
		argvalue0 := mqbroker.NewRequestHeader()
		err1091 := argvalue0.Read(jsProt1090)
		if err1091 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1092 := flag.Arg(2)
		mbTrans1093 := thrift.NewTMemoryBufferLen(len(arg1092))
		defer mbTrans1093.Close()
		_, err1094 := mbTrans1093.WriteString(arg1092)
		if err1094 != nil {
			Usage()
			return
		}
		factory1095 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1096 := factory1095.GetProtocol(mbTrans1093)
		argvalue1 := mqbroker.NewEventHeader()
		err1097 := argvalue1.Read(jsProt1096)
		if err1097 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1098 := flag.Arg(3)
		mbTrans1099 := thrift.NewTMemoryBufferLen(len(arg1098))
		defer mbTrans1099.Close()
		_, err1100 := mbTrans1099.WriteString(arg1098)
		if err1100 != nil {
			Usage()
			return
		}
		factory1101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1102 := factory1101.GetProtocol(mbTrans1099)
		argvalue2 := mqbroker.NewDPMChannelUpdateEvent()
		err1103 := argvalue2.Read(jsProt1102)
		if err1103 != nil {
			Usage()
			return
		}
		value2 := mqbroker.DPMChannelUpdateEvent(argvalue2)
		fmt.Print(client.PubDPMChannelUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDPMTrackingUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDPMTrackingUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg1104 := flag.Arg(1)
		mbTrans1105 := thrift.NewTMemoryBufferLen(len(arg1104))
		defer mbTrans1105.Close()
		_, err1106 := mbTrans1105.WriteString(arg1104)
		if err1106 != nil {
			Usage()
			return
		}
		factory1107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1108 := factory1107.GetProtocol(mbTrans1105)
		argvalue0 := mqbroker.NewRequestHeader()
		err1109 := argvalue0.Read(jsProt1108)
		if err1109 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1110 := flag.Arg(2)
		mbTrans1111 := thrift.NewTMemoryBufferLen(len(arg1110))
		defer mbTrans1111.Close()
		_, err1112 := mbTrans1111.WriteString(arg1110)
		if err1112 != nil {
			Usage()
			return
		}
		factory1113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1114 := factory1113.GetProtocol(mbTrans1111)
		argvalue1 := mqbroker.NewEventHeader()
		err1115 := argvalue1.Read(jsProt1114)
		if err1115 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1116 := flag.Arg(3)
		mbTrans1117 := thrift.NewTMemoryBufferLen(len(arg1116))
		defer mbTrans1117.Close()
		_, err1118 := mbTrans1117.WriteString(arg1116)
		if err1118 != nil {
			Usage()
			return
		}
		factory1119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1120 := factory1119.GetProtocol(mbTrans1117)
		argvalue2 := mqbroker.NewDPMTrackingUpdateEvent()
		err1121 := argvalue2.Read(jsProt1120)
		if err1121 != nil {
			Usage()
			return
		}
		value2 := mqbroker.DPMTrackingUpdateEvent(argvalue2)
		fmt.Print(client.PubDPMTrackingUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDPMCompanyUpdateEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDPMCompanyUpdateEvent requires 3 args")
			flag.Usage()
		}
		arg1122 := flag.Arg(1)
		mbTrans1123 := thrift.NewTMemoryBufferLen(len(arg1122))
		defer mbTrans1123.Close()
		_, err1124 := mbTrans1123.WriteString(arg1122)
		if err1124 != nil {
			Usage()
			return
		}
		factory1125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1126 := factory1125.GetProtocol(mbTrans1123)
		argvalue0 := mqbroker.NewRequestHeader()
		err1127 := argvalue0.Read(jsProt1126)
		if err1127 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1128 := flag.Arg(2)
		mbTrans1129 := thrift.NewTMemoryBufferLen(len(arg1128))
		defer mbTrans1129.Close()
		_, err1130 := mbTrans1129.WriteString(arg1128)
		if err1130 != nil {
			Usage()
			return
		}
		factory1131 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1132 := factory1131.GetProtocol(mbTrans1129)
		argvalue1 := mqbroker.NewEventHeader()
		err1133 := argvalue1.Read(jsProt1132)
		if err1133 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1134 := flag.Arg(3)
		mbTrans1135 := thrift.NewTMemoryBufferLen(len(arg1134))
		defer mbTrans1135.Close()
		_, err1136 := mbTrans1135.WriteString(arg1134)
		if err1136 != nil {
			Usage()
			return
		}
		factory1137 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1138 := factory1137.GetProtocol(mbTrans1135)
		argvalue2 := mqbroker.NewDPMCompanyUpdateEvent()
		err1139 := argvalue2.Read(jsProt1138)
		if err1139 != nil {
			Usage()
			return
		}
		value2 := mqbroker.DPMCompanyUpdateEvent(argvalue2)
		fmt.Print(client.PubDPMCompanyUpdateEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDosCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDosCommonEvent requires 3 args")
			flag.Usage()
		}
		arg1140 := flag.Arg(1)
		mbTrans1141 := thrift.NewTMemoryBufferLen(len(arg1140))
		defer mbTrans1141.Close()
		_, err1142 := mbTrans1141.WriteString(arg1140)
		if err1142 != nil {
			Usage()
			return
		}
		factory1143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1144 := factory1143.GetProtocol(mbTrans1141)
		argvalue0 := mqbroker.NewRequestHeader()
		err1145 := argvalue0.Read(jsProt1144)
		if err1145 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1146 := flag.Arg(2)
		mbTrans1147 := thrift.NewTMemoryBufferLen(len(arg1146))
		defer mbTrans1147.Close()
		_, err1148 := mbTrans1147.WriteString(arg1146)
		if err1148 != nil {
			Usage()
			return
		}
		factory1149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1150 := factory1149.GetProtocol(mbTrans1147)
		argvalue1 := mqbroker.NewEventHeader()
		err1151 := argvalue1.Read(jsProt1150)
		if err1151 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1152 := flag.Arg(3)
		mbTrans1153 := thrift.NewTMemoryBufferLen(len(arg1152))
		defer mbTrans1153.Close()
		_, err1154 := mbTrans1153.WriteString(arg1152)
		if err1154 != nil {
			Usage()
			return
		}
		factory1155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1156 := factory1155.GetProtocol(mbTrans1153)
		argvalue2 := mqbroker.NewDosCommonEvent()
		err1157 := argvalue2.Read(jsProt1156)
		if err1157 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDosCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDosStatsEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDosStatsEvent requires 3 args")
			flag.Usage()
		}
		arg1158 := flag.Arg(1)
		mbTrans1159 := thrift.NewTMemoryBufferLen(len(arg1158))
		defer mbTrans1159.Close()
		_, err1160 := mbTrans1159.WriteString(arg1158)
		if err1160 != nil {
			Usage()
			return
		}
		factory1161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1162 := factory1161.GetProtocol(mbTrans1159)
		argvalue0 := mqbroker.NewRequestHeader()
		err1163 := argvalue0.Read(jsProt1162)
		if err1163 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1164 := flag.Arg(2)
		mbTrans1165 := thrift.NewTMemoryBufferLen(len(arg1164))
		defer mbTrans1165.Close()
		_, err1166 := mbTrans1165.WriteString(arg1164)
		if err1166 != nil {
			Usage()
			return
		}
		factory1167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1168 := factory1167.GetProtocol(mbTrans1165)
		argvalue1 := mqbroker.NewEventHeader()
		err1169 := argvalue1.Read(jsProt1168)
		if err1169 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1170 := flag.Arg(3)
		mbTrans1171 := thrift.NewTMemoryBufferLen(len(arg1170))
		defer mbTrans1171.Close()
		_, err1172 := mbTrans1171.WriteString(arg1170)
		if err1172 != nil {
			Usage()
			return
		}
		factory1173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1174 := factory1173.GetProtocol(mbTrans1171)
		argvalue2 := mqbroker.NewDosStatsEvent()
		err1175 := argvalue2.Read(jsProt1174)
		if err1175 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDosStatsEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDspStatsEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDspStatsEvent requires 3 args")
			flag.Usage()
		}
		arg1176 := flag.Arg(1)
		mbTrans1177 := thrift.NewTMemoryBufferLen(len(arg1176))
		defer mbTrans1177.Close()
		_, err1178 := mbTrans1177.WriteString(arg1176)
		if err1178 != nil {
			Usage()
			return
		}
		factory1179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1180 := factory1179.GetProtocol(mbTrans1177)
		argvalue0 := mqbroker.NewRequestHeader()
		err1181 := argvalue0.Read(jsProt1180)
		if err1181 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1182 := flag.Arg(2)
		mbTrans1183 := thrift.NewTMemoryBufferLen(len(arg1182))
		defer mbTrans1183.Close()
		_, err1184 := mbTrans1183.WriteString(arg1182)
		if err1184 != nil {
			Usage()
			return
		}
		factory1185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1186 := factory1185.GetProtocol(mbTrans1183)
		argvalue1 := mqbroker.NewEventHeader()
		err1187 := argvalue1.Read(jsProt1186)
		if err1187 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1188 := flag.Arg(3)
		mbTrans1189 := thrift.NewTMemoryBufferLen(len(arg1188))
		defer mbTrans1189.Close()
		_, err1190 := mbTrans1189.WriteString(arg1188)
		if err1190 != nil {
			Usage()
			return
		}
		factory1191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1192 := factory1191.GetProtocol(mbTrans1189)
		argvalue2 := mqbroker.NewDspStatsEvent()
		err1193 := argvalue2.Read(jsProt1192)
		if err1193 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDspStatsEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubVicoCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubVicoCommonEvent requires 3 args")
			flag.Usage()
		}
		arg1194 := flag.Arg(1)
		mbTrans1195 := thrift.NewTMemoryBufferLen(len(arg1194))
		defer mbTrans1195.Close()
		_, err1196 := mbTrans1195.WriteString(arg1194)
		if err1196 != nil {
			Usage()
			return
		}
		factory1197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1198 := factory1197.GetProtocol(mbTrans1195)
		argvalue0 := mqbroker.NewRequestHeader()
		err1199 := argvalue0.Read(jsProt1198)
		if err1199 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1200 := flag.Arg(2)
		mbTrans1201 := thrift.NewTMemoryBufferLen(len(arg1200))
		defer mbTrans1201.Close()
		_, err1202 := mbTrans1201.WriteString(arg1200)
		if err1202 != nil {
			Usage()
			return
		}
		factory1203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1204 := factory1203.GetProtocol(mbTrans1201)
		argvalue1 := mqbroker.NewEventHeader()
		err1205 := argvalue1.Read(jsProt1204)
		if err1205 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1206 := flag.Arg(3)
		mbTrans1207 := thrift.NewTMemoryBufferLen(len(arg1206))
		defer mbTrans1207.Close()
		_, err1208 := mbTrans1207.WriteString(arg1206)
		if err1208 != nil {
			Usage()
			return
		}
		factory1209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1210 := factory1209.GetProtocol(mbTrans1207)
		argvalue2 := mqbroker.NewVicoCommonEvent()
		err1211 := argvalue2.Read(jsProt1210)
		if err1211 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubVicoCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubDataPlusCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubDataPlusCommonEvent requires 3 args")
			flag.Usage()
		}
		arg1212 := flag.Arg(1)
		mbTrans1213 := thrift.NewTMemoryBufferLen(len(arg1212))
		defer mbTrans1213.Close()
		_, err1214 := mbTrans1213.WriteString(arg1212)
		if err1214 != nil {
			Usage()
			return
		}
		factory1215 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1216 := factory1215.GetProtocol(mbTrans1213)
		argvalue0 := mqbroker.NewRequestHeader()
		err1217 := argvalue0.Read(jsProt1216)
		if err1217 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1218 := flag.Arg(2)
		mbTrans1219 := thrift.NewTMemoryBufferLen(len(arg1218))
		defer mbTrans1219.Close()
		_, err1220 := mbTrans1219.WriteString(arg1218)
		if err1220 != nil {
			Usage()
			return
		}
		factory1221 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1222 := factory1221.GetProtocol(mbTrans1219)
		argvalue1 := mqbroker.NewEventHeader()
		err1223 := argvalue1.Read(jsProt1222)
		if err1223 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1224 := flag.Arg(3)
		mbTrans1225 := thrift.NewTMemoryBufferLen(len(arg1224))
		defer mbTrans1225.Close()
		_, err1226 := mbTrans1225.WriteString(arg1224)
		if err1226 != nil {
			Usage()
			return
		}
		factory1227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1228 := factory1227.GetProtocol(mbTrans1225)
		argvalue2 := mqbroker.NewDataPlusCommonEvent()
		err1229 := argvalue2.Read(jsProt1228)
		if err1229 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubDataPlusCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubCompassCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubCompassCommonEvent requires 3 args")
			flag.Usage()
		}
		arg1230 := flag.Arg(1)
		mbTrans1231 := thrift.NewTMemoryBufferLen(len(arg1230))
		defer mbTrans1231.Close()
		_, err1232 := mbTrans1231.WriteString(arg1230)
		if err1232 != nil {
			Usage()
			return
		}
		factory1233 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1234 := factory1233.GetProtocol(mbTrans1231)
		argvalue0 := mqbroker.NewRequestHeader()
		err1235 := argvalue0.Read(jsProt1234)
		if err1235 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1236 := flag.Arg(2)
		mbTrans1237 := thrift.NewTMemoryBufferLen(len(arg1236))
		defer mbTrans1237.Close()
		_, err1238 := mbTrans1237.WriteString(arg1236)
		if err1238 != nil {
			Usage()
			return
		}
		factory1239 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1240 := factory1239.GetProtocol(mbTrans1237)
		argvalue1 := mqbroker.NewEventHeader()
		err1241 := argvalue1.Read(jsProt1240)
		if err1241 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1242 := flag.Arg(3)
		mbTrans1243 := thrift.NewTMemoryBufferLen(len(arg1242))
		defer mbTrans1243.Close()
		_, err1244 := mbTrans1243.WriteString(arg1242)
		if err1244 != nil {
			Usage()
			return
		}
		factory1245 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1246 := factory1245.GetProtocol(mbTrans1243)
		argvalue2 := mqbroker.NewCompassCommonEvent()
		err1247 := argvalue2.Read(jsProt1246)
		if err1247 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubCompassCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubBidMasterCommonEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubBidMasterCommonEvent requires 3 args")
			flag.Usage()
		}
		arg1248 := flag.Arg(1)
		mbTrans1249 := thrift.NewTMemoryBufferLen(len(arg1248))
		defer mbTrans1249.Close()
		_, err1250 := mbTrans1249.WriteString(arg1248)
		if err1250 != nil {
			Usage()
			return
		}
		factory1251 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1252 := factory1251.GetProtocol(mbTrans1249)
		argvalue0 := mqbroker.NewRequestHeader()
		err1253 := argvalue0.Read(jsProt1252)
		if err1253 != nil {
			Usage()
			return
		}
		value0 := mqbroker.RequestHeader(argvalue0)
		arg1254 := flag.Arg(2)
		mbTrans1255 := thrift.NewTMemoryBufferLen(len(arg1254))
		defer mbTrans1255.Close()
		_, err1256 := mbTrans1255.WriteString(arg1254)
		if err1256 != nil {
			Usage()
			return
		}
		factory1257 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1258 := factory1257.GetProtocol(mbTrans1255)
		argvalue1 := mqbroker.NewEventHeader()
		err1259 := argvalue1.Read(jsProt1258)
		if err1259 != nil {
			Usage()
			return
		}
		value1 := mqbroker.EventHeader(argvalue1)
		arg1260 := flag.Arg(3)
		mbTrans1261 := thrift.NewTMemoryBufferLen(len(arg1260))
		defer mbTrans1261.Close()
		_, err1262 := mbTrans1261.WriteString(arg1260)
		if err1262 != nil {
			Usage()
			return
		}
		factory1263 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1264 := factory1263.GetProtocol(mbTrans1261)
		argvalue2 := mqbroker.NewBidMasterCommonEvent()
		err1265 := argvalue2.Read(jsProt1264)
		if err1265 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubBidMasterCommonEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pubBidMasterOperationEvent":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PubBidMasterOperationEvent requires 3 args")
			flag.Usage()
		}
		arg1266 := flag.Arg(1)
		mbTrans1267 := thrift.NewTMemoryBufferLen(len(arg1266))
		defer mbTrans1267.Close()
		_, err1268 := mbTrans1267.WriteString(arg1266)
		if err1268 != nil {
			Usage()
			return
		}
		factory1269 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1270 := factory1269.GetProtocol(mbTrans1267)
		argvalue0 := mqbroker.NewRequestHeader()
		err1271 := argvalue0.Read(jsProt1270)
		if err1271 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1272 := flag.Arg(2)
		mbTrans1273 := thrift.NewTMemoryBufferLen(len(arg1272))
		defer mbTrans1273.Close()
		_, err1274 := mbTrans1273.WriteString(arg1272)
		if err1274 != nil {
			Usage()
			return
		}
		factory1275 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1276 := factory1275.GetProtocol(mbTrans1273)
		argvalue1 := mqbroker.NewEventHeader()
		err1277 := argvalue1.Read(jsProt1276)
		if err1277 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg1278 := flag.Arg(3)
		mbTrans1279 := thrift.NewTMemoryBufferLen(len(arg1278))
		defer mbTrans1279.Close()
		_, err1280 := mbTrans1279.WriteString(arg1278)
		if err1280 != nil {
			Usage()
			return
		}
		factory1281 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1282 := factory1281.GetProtocol(mbTrans1279)
		argvalue2 := mqbroker.NewBidMasterOperationEvent()
		err1283 := argvalue2.Read(jsProt1282)
		if err1283 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.PubBidMasterOperationEvent(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
