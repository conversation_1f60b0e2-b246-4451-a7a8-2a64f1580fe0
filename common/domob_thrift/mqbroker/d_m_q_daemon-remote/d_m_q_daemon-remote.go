// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"mqbroker"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getLatestMessage()")
	fmt.Fprintln(os.Stderr, "  void skipMessage( subscriberList, string messageId)")
	fmt.Fprintln(os.<PERSON>r, "  void resetCounters()")
	fmt.Fprintln(os.<PERSON>der<PERSON>, "  i64 getMaxMessageId(TopicId topicId)")
	fmt.Fprintln(os.Stderr, "  i64 getOffset(TopicId topicId, string subscriberName)")
	fmt.Fprintln(os.Stderr, "  MQOperationStatus registerTopic(TopicId topicId)")
	fmt.Fprintln(os.Stderr, "  MQOperationStatus registerSubscriber(SubscriberInfo subscriber)")
	fmt.Fprintln(os.Stderr, "  MQOperationStatus deregisterTopic(TopicId topicId)")
	fmt.Fprintln(os.Stderr, "  MQOperationStatus deregisterSubscriber(string subscriberName)")
	fmt.Fprintln(os.Stderr, "  MQOperationStatus removeSubscribeRelation(TopicId topicId, string subscriberName)")
	fmt.Fprintln(os.Stderr, "   getTopicList()")
	fmt.Fprintln(os.Stderr, "  MQOperationStatus subscribe(TopicId topicId, string subscriberName)")
	fmt.Fprintln(os.Stderr, "   getSubscriberByTopic(TopicId topicId)")
	fmt.Fprintln(os.Stderr, "   getTopicBySubscriber(string subscriberName)")
	fmt.Fprintln(os.Stderr, "   getSubscriberList()")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := mqbroker.NewDMQDaemonClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getLatestMessage":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetLatestMessage requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetLatestMessage())
		fmt.Print("\n")
		break
	case "skipMessage":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SkipMessage requires 2 args")
			flag.Usage()
		}
		arg1352 := flag.Arg(1)
		mbTrans1353 := thrift.NewTMemoryBufferLen(len(arg1352))
		defer mbTrans1353.Close()
		_, err1354 := mbTrans1353.WriteString(arg1352)
		if err1354 != nil {
			Usage()
			return
		}
		factory1355 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1356 := factory1355.GetProtocol(mbTrans1353)
		containerStruct0 := mqbroker.NewSkipMessageArgs()
		err1357 := containerStruct0.ReadField1(jsProt1356)
		if err1357 != nil {
			Usage()
			return
		}
		argvalue0 := containerStruct0.SubscriberList
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SkipMessage(value0, value1))
		fmt.Print("\n")
		break
	case "resetCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "ResetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.ResetCounters())
		fmt.Print("\n")
		break
	case "getMaxMessageId":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetMaxMessageId requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		fmt.Print(client.GetMaxMessageId(value0))
		fmt.Print("\n")
		break
	case "getOffset":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOffset requires 2 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetOffset(value0, value1))
		fmt.Print("\n")
		break
	case "registerTopic":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "RegisterTopic requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		fmt.Print(client.RegisterTopic(value0))
		fmt.Print("\n")
		break
	case "registerSubscriber":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "RegisterSubscriber requires 1 args")
			flag.Usage()
		}
		arg1360 := flag.Arg(1)
		mbTrans1361 := thrift.NewTMemoryBufferLen(len(arg1360))
		defer mbTrans1361.Close()
		_, err1362 := mbTrans1361.WriteString(arg1360)
		if err1362 != nil {
			Usage()
			return
		}
		factory1363 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1364 := factory1363.GetProtocol(mbTrans1361)
		argvalue0 := mqbroker.NewSubscriberInfo()
		err1365 := argvalue0.Read(jsProt1364)
		if err1365 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.RegisterSubscriber(value0))
		fmt.Print("\n")
		break
	case "deregisterTopic":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DeregisterTopic requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		fmt.Print(client.DeregisterTopic(value0))
		fmt.Print("\n")
		break
	case "deregisterSubscriber":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DeregisterSubscriber requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.DeregisterSubscriber(value0))
		fmt.Print("\n")
		break
	case "removeSubscribeRelation":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RemoveSubscribeRelation requires 2 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.RemoveSubscribeRelation(value0, value1))
		fmt.Print("\n")
		break
	case "getTopicList":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetTopicList requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetTopicList())
		fmt.Print("\n")
		break
	case "subscribe":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "Subscribe requires 2 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.Subscribe(value0, value1))
		fmt.Print("\n")
		break
	case "getSubscriberByTopic":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetSubscriberByTopic requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := mqbroker.TopicId(tmp0)
		value0 := mqbroker.TopicId(argvalue0)
		fmt.Print(client.GetSubscriberByTopic(value0))
		fmt.Print("\n")
		break
	case "getTopicBySubscriber":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetTopicBySubscriber requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetTopicBySubscriber(value0))
		fmt.Print("\n")
		break
	case "getSubscriberList":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetSubscriberList requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetSubscriberList())
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err1374 := (strconv.Atoi(flag.Arg(1)))
		if err1374 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
