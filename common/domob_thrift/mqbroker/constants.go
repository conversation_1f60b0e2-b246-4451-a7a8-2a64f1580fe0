// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package mqbroker

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_event"
	"rtb_model_server/common/domob_thrift/appinfo_event"
	"rtb_model_server/common/domob_thrift/bidmaster_event"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/compass_event"
	"rtb_model_server/common/domob_thrift/data_plus_event"
	"rtb_model_server/common/domob_thrift/dbm_event"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/dmp_event"
	"rtb_model_server/common/domob_thrift/dos_event"
	"rtb_model_server/common/domob_thrift/dpm_event"
	"rtb_model_server/common/domob_thrift/dsp_event"
	"rtb_model_server/common/domob_thrift/event"
	"rtb_model_server/common/domob_thrift/finance_event"
	"rtb_model_server/common/domob_thrift/mediainfo_event"
	"rtb_model_server/common/domob_thrift/mqbroker_event"
	"rtb_model_server/common/domob_thrift/offerwall_types"
	"rtb_model_server/common/domob_thrift/passport_event"
	"rtb_model_server/common/domob_thrift/project_event"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_event"
	"rtb_model_server/common/domob_thrift/searchui_types"
	"rtb_model_server/common/domob_thrift/vico_event"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__
var _ = passport_event.GoUnusedProtection__
var _ = adinfo_event.GoUnusedProtection__
var _ = finance_event.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var _ = mediainfo_event.GoUnusedProtection__
var _ = offerwall_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appinfo_event.GoUnusedProtection__
var _ = rtb_adinfo_event.GoUnusedProtection__
var _ = project_event.GoUnusedProtection__
var _ = dbm_event.GoUnusedProtection__
var _ = dmp_event.GoUnusedProtection__
var _ = dpm_event.GoUnusedProtection__
var _ = dos_event.GoUnusedProtection__
var _ = dsp_event.GoUnusedProtection__
var _ = vico_event.GoUnusedProtection__
var _ = data_plus_event.GoUnusedProtection__
var _ = compass_event.GoUnusedProtection__
var _ = bidmaster_event.GoUnusedProtection__
var _ = mqbroker_event.GoUnusedProtection__
var PublishStatusText map[PublishStatusCode]string

func init() {
	PublishStatusText = map[PublishStatusCode]string{
		10100: "Success",
		10101: "Client exception.",
		10102: "Invalid event header.",
		10103: "Unknown event type.",
		10104: "Unknown event code.",
		10105: "Invalid event id.",
		10106: "Invalid argument list.",
		10200: "Server exception.",
		10201: "Server busy now.",
		10202: "Server error during persistance.",
		10203: "Fail to request EventID.",
		10107: "Event data invalid.",
	}

}
