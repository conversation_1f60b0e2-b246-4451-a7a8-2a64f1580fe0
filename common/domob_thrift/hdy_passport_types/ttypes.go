// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_passport_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//用户角色
type UserRole int64

const (
	UserRole_ROLE_UNKNOWN UserRole = 0
	UserRole_ROLE_SPONSOR UserRole = 1
	UserRole_ROLE_AGENT   UserRole = 2
)

func (p UserRole) String() string {
	switch p {
	case UserRole_ROLE_UNKNOWN:
		return "UserRole_ROLE_UNKNOWN"
	case UserRole_ROLE_SPONSOR:
		return "UserRole_ROLE_SPONSOR"
	case UserRole_ROLE_AGENT:
		return "UserRole_ROLE_AGENT"
	}
	return "<UNSET>"
}

func UserRoleFromString(s string) (UserRole, error) {
	switch s {
	case "UserRole_ROLE_UNKNOWN":
		return UserRole_ROLE_UNKNOWN, nil
	case "UserRole_ROLE_SPONSOR":
		return UserRole_ROLE_SPONSOR, nil
	case "UserRole_ROLE_AGENT":
		return UserRole_ROLE_AGENT, nil
	}
	return UserRole(math.MinInt32 - 1), fmt.Errorf("not a valid UserRole string")
}

//账户状态
type UserStatus int64

const (
	UserStatus_STATUS_NORMAL  UserStatus = 0
	UserStatus_STATUS_BANED   UserStatus = 1
	UserStatus_STATUS_DELETED UserStatus = 2
)

func (p UserStatus) String() string {
	switch p {
	case UserStatus_STATUS_NORMAL:
		return "UserStatus_STATUS_NORMAL"
	case UserStatus_STATUS_BANED:
		return "UserStatus_STATUS_BANED"
	case UserStatus_STATUS_DELETED:
		return "UserStatus_STATUS_DELETED"
	}
	return "<UNSET>"
}

func UserStatusFromString(s string) (UserStatus, error) {
	switch s {
	case "UserStatus_STATUS_NORMAL":
		return UserStatus_STATUS_NORMAL, nil
	case "UserStatus_STATUS_BANED":
		return UserStatus_STATUS_BANED, nil
	case "UserStatus_STATUS_DELETED":
		return UserStatus_STATUS_DELETED, nil
	}
	return UserStatus(math.MinInt32 - 1), fmt.Errorf("not a valid UserStatus string")
}

type UserRegInfo struct {
	Mobile   string `thrift:"mobile,1" json:"mobile"`
	Vcode    int32  `thrift:"vcode,2" json:"vcode"`
	Password string `thrift:"password,3" json:"password"`
}

func NewUserRegInfo() *UserRegInfo {
	return &UserRegInfo{}
}

func (p *UserRegInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserRegInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *UserRegInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Vcode = v
	}
	return nil
}

func (p *UserRegInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *UserRegInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserRegInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserRegInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mobile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mobile: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vcode", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:vcode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Vcode)); err != nil {
		return fmt.Errorf("%T.vcode (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:vcode: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:password: %s", p, err)
	}
	return err
}

func (p *UserRegInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserRegInfo(%+v)", *p)
}

type UserInfo struct {
	Id       int32    `thrift:"id,1" json:"id"`
	UserRole UserRole `thrift:"userRole,2" json:"userRole"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Mobile string `thrift:"mobile,10" json:"mobile"`
	Email  string `thrift:"email,11" json:"email"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CompanyName     string     `thrift:"companyName,20" json:"companyName"`
	LicenseNo       string     `thrift:"licenseNo,21" json:"licenseNo"`
	Address         string     `thrift:"address,22" json:"address"`
	BusinessLicense string     `thrift:"businessLicense,23" json:"businessLicense"`
	ContactName     string     `thrift:"contactName,24" json:"contactName"`
	ContactMobile   string     `thrift:"contactMobile,25" json:"contactMobile"`
	Status          UserStatus `thrift:"status,26" json:"status"`
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewUserInfo() *UserInfo {
	return &UserInfo{
		UserRole: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UserInfo) IsSetUserRole() bool {
	return int64(p.UserRole) != math.MinInt32-1
}

func (p *UserInfo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *UserInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *UserInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UserRole = UserRole(v)
	}
	return nil
}

func (p *UserInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Mobile = v
	}
	return nil
}

func (p *UserInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *UserInfo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *UserInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LicenseNo = v
	}
	return nil
}

func (p *UserInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Address = v
	}
	return nil
}

func (p *UserInfo) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.BusinessLicense = v
	}
	return nil
}

func (p *UserInfo) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.ContactName = v
	}
	return nil
}

func (p *UserInfo) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.ContactMobile = v
	}
	return nil
}

func (p *UserInfo) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Status = UserStatus(v)
	}
	return nil
}

func (p *UserInfo) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *UserInfo) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *UserInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserRole() {
		if err := oprot.WriteFieldBegin("userRole", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:userRole: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.UserRole)); err != nil {
			return fmt.Errorf("%T.userRole (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:userRole: %s", p, err)
		}
	}
	return err
}

func (p *UserInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:mobile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mobile)); err != nil {
		return fmt.Errorf("%T.mobile (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:mobile: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:email: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyName", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:companyName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CompanyName)); err != nil {
		return fmt.Errorf("%T.companyName (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:companyName: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("licenseNo", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:licenseNo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LicenseNo)); err != nil {
		return fmt.Errorf("%T.licenseNo (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:licenseNo: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("address", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:address: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Address)); err != nil {
		return fmt.Errorf("%T.address (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:address: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("businessLicense", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:businessLicense: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BusinessLicense)); err != nil {
		return fmt.Errorf("%T.businessLicense (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:businessLicense: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("contactName", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:contactName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ContactName)); err != nil {
		return fmt.Errorf("%T.contactName (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:contactName: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("contactMobile", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:contactMobile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ContactMobile)); err != nil {
		return fmt.Errorf("%T.contactMobile (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:contactMobile: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField26(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (26) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:status: %s", p, err)
		}
	}
	return err
}

func (p *UserInfo) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *UserInfo) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *UserInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserInfo(%+v)", *p)
}
