// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package duoyou_logs

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type LogAccess struct {
	Ip                string `thrift:"ip,1" json:"ip"`
	AccessTime        int32  `thrift:"access_time,2" json:"access_time"`
	Url               string `thrift:"url,3" json:"url"`
	ResponseTimeMs    int32  `thrift:"response_time_ms,4" json:"response_time_ms"`
	XUa               string `thrift:"x_ua,5" json:"x_ua"`
	XUid              string `thrift:"x_uid,6" json:"x_uid"`
	XScreen           string `thrift:"x_screen,7" json:"x_screen"`
	XPinfo            string `thrift:"x_pinfo,8" json:"x_pinfo"`
	ResponseBytes     int32  `thrift:"response_bytes,9" json:"response_bytes"`
	Channel           string `thrift:"channel,10" json:"channel"`
	DuoyouVersionCode int32  `thrift:"duoyou_version_code,11" json:"duoyou_version_code"`
	Network           string `thrift:"network,12" json:"network"`
	Carrier           string `thrift:"carrier,13" json:"carrier"`
	AndroidVersion    string `thrift:"android_version,14" json:"android_version"`
	DuoyouPackageName string `thrift:"duoyou_package_name,15" json:"duoyou_package_name"`
}

func NewLogAccess() *LogAccess {
	return &LogAccess{}
}

func (p *LogAccess) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LogAccess) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *LogAccess) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccessTime = v
	}
	return nil
}

func (p *LogAccess) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *LogAccess) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ResponseTimeMs = v
	}
	return nil
}

func (p *LogAccess) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.XUa = v
	}
	return nil
}

func (p *LogAccess) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.XUid = v
	}
	return nil
}

func (p *LogAccess) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.XScreen = v
	}
	return nil
}

func (p *LogAccess) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.XPinfo = v
	}
	return nil
}

func (p *LogAccess) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ResponseBytes = v
	}
	return nil
}

func (p *LogAccess) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *LogAccess) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DuoyouVersionCode = v
	}
	return nil
}

func (p *LogAccess) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *LogAccess) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *LogAccess) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AndroidVersion = v
	}
	return nil
}

func (p *LogAccess) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DuoyouPackageName = v
	}
	return nil
}

func (p *LogAccess) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LogAccess"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LogAccess) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ip: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_time", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:access_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessTime)); err != nil {
		return fmt.Errorf("%T.access_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:access_time: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:url: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("response_time_ms", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:response_time_ms: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ResponseTimeMs)); err != nil {
		return fmt.Errorf("%T.response_time_ms (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:response_time_ms: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_ua", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:x_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUa)); err != nil {
		return fmt.Errorf("%T.x_ua (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:x_ua: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_uid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:x_uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUid)); err != nil {
		return fmt.Errorf("%T.x_uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:x_uid: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_screen", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:x_screen: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XScreen)); err != nil {
		return fmt.Errorf("%T.x_screen (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:x_screen: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_pinfo", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:x_pinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XPinfo)); err != nil {
		return fmt.Errorf("%T.x_pinfo (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:x_pinfo: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("response_bytes", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:response_bytes: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ResponseBytes)); err != nil {
		return fmt.Errorf("%T.response_bytes (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:response_bytes: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:channel: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duoyou_version_code", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:duoyou_version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DuoyouVersionCode)); err != nil {
		return fmt.Errorf("%T.duoyou_version_code (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:duoyou_version_code: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:network: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:carrier: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_version", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:android_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidVersion)); err != nil {
		return fmt.Errorf("%T.android_version (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:android_version: %s", p, err)
	}
	return err
}

func (p *LogAccess) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duoyou_package_name", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:duoyou_package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DuoyouPackageName)); err != nil {
		return fmt.Errorf("%T.duoyou_package_name (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:duoyou_package_name: %s", p, err)
	}
	return err
}

func (p *LogAccess) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogAccess(%+v)", *p)
}

type LogReport struct {
	Id                int64  `thrift:"id,1" json:"id"`
	AccessTime        int32  `thrift:"access_time,2" json:"access_time"`
	Url               string `thrift:"url,3" json:"url"`
	ResponseTimeMs    int32  `thrift:"response_time_ms,4" json:"response_time_ms"`
	XUa               string `thrift:"x_ua,5" json:"x_ua"`
	XUid              string `thrift:"x_uid,6" json:"x_uid"`
	XScreen           string `thrift:"x_screen,7" json:"x_screen"`
	XPinfo            string `thrift:"x_pinfo,8" json:"x_pinfo"`
	Channel           string `thrift:"channel,9" json:"channel"`
	DuoyouVersionCode int32  `thrift:"duoyou_version_code,10" json:"duoyou_version_code"`
	DuoyouPackageName string `thrift:"duoyou_package_name,11" json:"duoyou_package_name"`
	Network           string `thrift:"network,12" json:"network"`
	Carrier           string `thrift:"carrier,13" json:"carrier"`
	AndroidVersion    string `thrift:"android_version,14" json:"android_version"`
	PhoneModel        string `thrift:"phone_model,15" json:"phone_model"`
	Ip                string `thrift:"ip,16" json:"ip"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Action           string `thrift:"action,20" json:"action"`
	Appid            int32  `thrift:"appid,21" json:"appid"`
	VersionCode      int32  `thrift:"version_code,22" json:"version_code"`
	AdUserId         int32  `thrift:"ad_user_id,23" json:"ad_user_id"`
	Refer            string `thrift:"refer,24" json:"refer"`
	DownloadFinishId int64  `thrift:"download_finish_id,25" json:"download_finish_id"`
	InstalledId      int64  `thrift:"installed_id,26" json:"installed_id"`
}

func NewLogReport() *LogReport {
	return &LogReport{}
}

func (p *LogReport) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LogReport) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LogReport) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccessTime = v
	}
	return nil
}

func (p *LogReport) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *LogReport) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ResponseTimeMs = v
	}
	return nil
}

func (p *LogReport) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.XUa = v
	}
	return nil
}

func (p *LogReport) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.XUid = v
	}
	return nil
}

func (p *LogReport) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.XScreen = v
	}
	return nil
}

func (p *LogReport) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.XPinfo = v
	}
	return nil
}

func (p *LogReport) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *LogReport) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.DuoyouVersionCode = v
	}
	return nil
}

func (p *LogReport) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DuoyouPackageName = v
	}
	return nil
}

func (p *LogReport) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *LogReport) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *LogReport) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AndroidVersion = v
	}
	return nil
}

func (p *LogReport) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PhoneModel = v
	}
	return nil
}

func (p *LogReport) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *LogReport) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *LogReport) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *LogReport) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *LogReport) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *LogReport) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *LogReport) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.DownloadFinishId = v
	}
	return nil
}

func (p *LogReport) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.InstalledId = v
	}
	return nil
}

func (p *LogReport) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LogReport"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LogReport) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_time", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:access_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessTime)); err != nil {
		return fmt.Errorf("%T.access_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:access_time: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:url: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("response_time_ms", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:response_time_ms: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ResponseTimeMs)); err != nil {
		return fmt.Errorf("%T.response_time_ms (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:response_time_ms: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_ua", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:x_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUa)); err != nil {
		return fmt.Errorf("%T.x_ua (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:x_ua: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_uid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:x_uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUid)); err != nil {
		return fmt.Errorf("%T.x_uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:x_uid: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_screen", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:x_screen: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XScreen)); err != nil {
		return fmt.Errorf("%T.x_screen (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:x_screen: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_pinfo", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:x_pinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XPinfo)); err != nil {
		return fmt.Errorf("%T.x_pinfo (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:x_pinfo: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:channel: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duoyou_version_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:duoyou_version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DuoyouVersionCode)); err != nil {
		return fmt.Errorf("%T.duoyou_version_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:duoyou_version_code: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duoyou_package_name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:duoyou_package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DuoyouPackageName)); err != nil {
		return fmt.Errorf("%T.duoyou_package_name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:duoyou_package_name: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:network: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:carrier: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_version", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:android_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidVersion)); err != nil {
		return fmt.Errorf("%T.android_version (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:android_version: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_model", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:phone_model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneModel)); err != nil {
		return fmt.Errorf("%T.phone_model (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:phone_model: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:ip: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:action: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Action)); err != nil {
		return fmt.Errorf("%T.action (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:action: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:appid: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version_code", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.version_code (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:version_code: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:ad_user_id: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:refer: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_finish_id", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:download_finish_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DownloadFinishId)); err != nil {
		return fmt.Errorf("%T.download_finish_id (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:download_finish_id: %s", p, err)
	}
	return err
}

func (p *LogReport) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("installed_id", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:installed_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.InstalledId)); err != nil {
		return fmt.Errorf("%T.installed_id (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:installed_id: %s", p, err)
	}
	return err
}

func (p *LogReport) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogReport(%+v)", *p)
}
