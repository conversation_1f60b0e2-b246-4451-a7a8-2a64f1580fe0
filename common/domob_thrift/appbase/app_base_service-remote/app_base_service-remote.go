// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"appbase"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "   getMediaInfoByIdList(RequestHeader header,  idList)")
	fmt.Fprintln(os.Stderr, "  MediaInfo getMediaInfoByPublishId(RequestHeader header, string publishId)")
	fmt.Fprintln(os.<PERSON>, "   getAppByPublisherId(RequestHeader header, IdInt publisherId)")
	fmt.Fprintln(os.<PERSON>r, "   getAppVersionByCreativeIdList(RequestHeader header,  creativeIdList)")
	fmt.Fprintln(os.Stderr, "   getAppVersionByIdList(RequestHeader header,  idList)")
	fmt.Fprintln(os.Stderr, "  AppVersion getAppVersionDetailByCreativeId(RequestHeader header, IdInt creativeId)")
	fmt.Fprintln(os.Stderr, "  AppVersion getAppVersionDetailById(RequestHeader header, IdInt id)")
	fmt.Fprintln(os.Stderr, "   getAppVersionDetailByIdList(RequestHeader header,  idList)")
	fmt.Fprintln(os.Stderr, "   getAppVersionDetailByCreativeIdList(RequestHeader header,  creativeIdList)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := appbase.NewAppBaseServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getMediaInfoByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaInfoByIdList requires 2 args")
			flag.Usage()
		}
		arg55 := flag.Arg(1)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue0 := appbase.NewRequestHeader()
		err60 := argvalue0.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg61 := flag.Arg(2)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		containerStruct1 := appbase.NewGetMediaInfoByIdListArgs()
		err66 := containerStruct1.ReadField2(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		fmt.Print(client.GetMediaInfoByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaInfoByPublishId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaInfoByPublishId requires 2 args")
			flag.Usage()
		}
		arg67 := flag.Arg(1)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue0 := appbase.NewRequestHeader()
		err72 := argvalue0.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetMediaInfoByPublishId(value0, value1))
		fmt.Print("\n")
		break
	case "getAppByPublisherId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppByPublisherId requires 2 args")
			flag.Usage()
		}
		arg74 := flag.Arg(1)
		mbTrans75 := thrift.NewTMemoryBufferLen(len(arg74))
		defer mbTrans75.Close()
		_, err76 := mbTrans75.WriteString(arg74)
		if err76 != nil {
			Usage()
			return
		}
		factory77 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt78 := factory77.GetProtocol(mbTrans75)
		argvalue0 := appbase.NewRequestHeader()
		err79 := argvalue0.Read(jsProt78)
		if err79 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err80 := (strconv.Atoi(flag.Arg(2)))
		if err80 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appbase.IdInt(argvalue1)
		fmt.Print(client.GetAppByPublisherId(value0, value1))
		fmt.Print("\n")
		break
	case "getAppVersionByCreativeIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppVersionByCreativeIdList requires 2 args")
			flag.Usage()
		}
		arg81 := flag.Arg(1)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue0 := appbase.NewRequestHeader()
		err86 := argvalue0.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg87 := flag.Arg(2)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		containerStruct1 := appbase.NewGetAppVersionByCreativeIdListArgs()
		err92 := containerStruct1.ReadField2(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CreativeIdList
		value1 := argvalue1
		fmt.Print(client.GetAppVersionByCreativeIdList(value0, value1))
		fmt.Print("\n")
		break
	case "getAppVersionByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppVersionByIdList requires 2 args")
			flag.Usage()
		}
		arg93 := flag.Arg(1)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue0 := appbase.NewRequestHeader()
		err98 := argvalue0.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg99 := flag.Arg(2)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		containerStruct1 := appbase.NewGetAppVersionByIdListArgs()
		err104 := containerStruct1.ReadField2(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		fmt.Print(client.GetAppVersionByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "getAppVersionDetailByCreativeId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppVersionDetailByCreativeId requires 2 args")
			flag.Usage()
		}
		arg105 := flag.Arg(1)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue0 := appbase.NewRequestHeader()
		err110 := argvalue0.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err111 := (strconv.Atoi(flag.Arg(2)))
		if err111 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appbase.IdInt(argvalue1)
		fmt.Print(client.GetAppVersionDetailByCreativeId(value0, value1))
		fmt.Print("\n")
		break
	case "getAppVersionDetailById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppVersionDetailById requires 2 args")
			flag.Usage()
		}
		arg112 := flag.Arg(1)
		mbTrans113 := thrift.NewTMemoryBufferLen(len(arg112))
		defer mbTrans113.Close()
		_, err114 := mbTrans113.WriteString(arg112)
		if err114 != nil {
			Usage()
			return
		}
		factory115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt116 := factory115.GetProtocol(mbTrans113)
		argvalue0 := appbase.NewRequestHeader()
		err117 := argvalue0.Read(jsProt116)
		if err117 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err118 := (strconv.Atoi(flag.Arg(2)))
		if err118 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appbase.IdInt(argvalue1)
		fmt.Print(client.GetAppVersionDetailById(value0, value1))
		fmt.Print("\n")
		break
	case "getAppVersionDetailByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppVersionDetailByIdList requires 2 args")
			flag.Usage()
		}
		arg119 := flag.Arg(1)
		mbTrans120 := thrift.NewTMemoryBufferLen(len(arg119))
		defer mbTrans120.Close()
		_, err121 := mbTrans120.WriteString(arg119)
		if err121 != nil {
			Usage()
			return
		}
		factory122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt123 := factory122.GetProtocol(mbTrans120)
		argvalue0 := appbase.NewRequestHeader()
		err124 := argvalue0.Read(jsProt123)
		if err124 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg125 := flag.Arg(2)
		mbTrans126 := thrift.NewTMemoryBufferLen(len(arg125))
		defer mbTrans126.Close()
		_, err127 := mbTrans126.WriteString(arg125)
		if err127 != nil {
			Usage()
			return
		}
		factory128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt129 := factory128.GetProtocol(mbTrans126)
		containerStruct1 := appbase.NewGetAppVersionDetailByIdListArgs()
		err130 := containerStruct1.ReadField2(jsProt129)
		if err130 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		fmt.Print(client.GetAppVersionDetailByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "getAppVersionDetailByCreativeIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppVersionDetailByCreativeIdList requires 2 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := appbase.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg137 := flag.Arg(2)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		containerStruct1 := appbase.NewGetAppVersionDetailByCreativeIdListArgs()
		err142 := containerStruct1.ReadField2(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CreativeIdList
		value1 := argvalue1
		fmt.Print(client.GetAppVersionDetailByCreativeIdList(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
