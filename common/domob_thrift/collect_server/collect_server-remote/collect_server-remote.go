// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"collect_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ResultSignal airportBeatHeart(RequestHeader requestHeader, string host, string runningVersion, string currentVersion, i32 startupTime, i64 runDirSize)")
	fmt.Fprintln(os.<PERSON>derr, "  ResultSignal terminalBeatHeart(RequestHeader requestHeader, string host, string account, string runningVersion, string currentVersion, i32 startupTime)")
	fmt.Fprintln(os.Stderr, "  ResultFlightInfo getFlightInfo(RequestHeader requestHeader, i32 flightId)")
	fmt.Fprintln(os.Stderr, "  ResultBool reportFlightFileStatus(RequestHeader requestHeader, TopicInfo topicInfo, i32 flightId, FlightFileStatus fileStatus, string extInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool canDeleteAirportFile(RequestHeader requestHeader, string host, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool isMyAirportFileFinished(RequestHeader requestHeader, string host, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool resetNotFinishFlightFile(RequestHeader requestHeader, string host)")
	fmt.Fprintln(os.Stderr, "  ResultBool addFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultI32List getFileFinishedBlockIndex(RequestHeader requestHeader, TopicInfo topicInfo,  needBlocks)")
	fmt.Fprintln(os.Stderr, "  ResultData getFileData(RequestHeader requestHeader, TopicInfo topicInfo, i32 blockIndex)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := collect_server.NewCollectServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "airportBeatHeart":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "AirportBeatHeart requires 6 args")
			flag.Usage()
		}
		arg33 := flag.Arg(1)
		mbTrans34 := thrift.NewTMemoryBufferLen(len(arg33))
		defer mbTrans34.Close()
		_, err35 := mbTrans34.WriteString(arg33)
		if err35 != nil {
			Usage()
			return
		}
		factory36 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt37 := factory36.GetProtocol(mbTrans34)
		argvalue0 := collect_server.NewRequestHeader()
		err38 := argvalue0.Read(jsProt37)
		if err38 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err42 := (strconv.Atoi(flag.Arg(5)))
		if err42 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5, err43 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err43 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		fmt.Print(client.AirportBeatHeart(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "terminalBeatHeart":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "TerminalBeatHeart requires 6 args")
			flag.Usage()
		}
		arg44 := flag.Arg(1)
		mbTrans45 := thrift.NewTMemoryBufferLen(len(arg44))
		defer mbTrans45.Close()
		_, err46 := mbTrans45.WriteString(arg44)
		if err46 != nil {
			Usage()
			return
		}
		factory47 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt48 := factory47.GetProtocol(mbTrans45)
		argvalue0 := collect_server.NewRequestHeader()
		err49 := argvalue0.Read(jsProt48)
		if err49 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		tmp5, err54 := (strconv.Atoi(flag.Arg(6)))
		if err54 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.TerminalBeatHeart(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getFlightInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFlightInfo requires 2 args")
			flag.Usage()
		}
		arg55 := flag.Arg(1)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue0 := collect_server.NewRequestHeader()
		err60 := argvalue0.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err61 := (strconv.Atoi(flag.Arg(2)))
		if err61 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetFlightInfo(value0, value1))
		fmt.Print("\n")
		break
	case "reportFlightFileStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ReportFlightFileStatus requires 5 args")
			flag.Usage()
		}
		arg62 := flag.Arg(1)
		mbTrans63 := thrift.NewTMemoryBufferLen(len(arg62))
		defer mbTrans63.Close()
		_, err64 := mbTrans63.WriteString(arg62)
		if err64 != nil {
			Usage()
			return
		}
		factory65 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt66 := factory65.GetProtocol(mbTrans63)
		argvalue0 := collect_server.NewRequestHeader()
		err67 := argvalue0.Read(jsProt66)
		if err67 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg68 := flag.Arg(2)
		mbTrans69 := thrift.NewTMemoryBufferLen(len(arg68))
		defer mbTrans69.Close()
		_, err70 := mbTrans69.WriteString(arg68)
		if err70 != nil {
			Usage()
			return
		}
		factory71 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt72 := factory71.GetProtocol(mbTrans69)
		argvalue1 := collect_server.NewTopicInfo()
		err73 := argvalue1.Read(jsProt72)
		if err73 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err74 := (strconv.Atoi(flag.Arg(3)))
		if err74 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := collect_server.FlightFileStatus(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.ReportFlightFileStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "canDeleteAirportFile":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CanDeleteAirportFile requires 3 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := collect_server.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg83 := flag.Arg(3)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		argvalue2 := collect_server.NewTopicInfo()
		err88 := argvalue2.Read(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.CanDeleteAirportFile(value0, value1, value2))
		fmt.Print("\n")
		break
	case "isMyAirportFileFinished":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "IsMyAirportFileFinished requires 3 args")
			flag.Usage()
		}
		arg89 := flag.Arg(1)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		argvalue0 := collect_server.NewRequestHeader()
		err94 := argvalue0.Read(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg96 := flag.Arg(3)
		mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
		defer mbTrans97.Close()
		_, err98 := mbTrans97.WriteString(arg96)
		if err98 != nil {
			Usage()
			return
		}
		factory99 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt100 := factory99.GetProtocol(mbTrans97)
		argvalue2 := collect_server.NewTopicInfo()
		err101 := argvalue2.Read(jsProt100)
		if err101 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.IsMyAirportFileFinished(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resetNotFinishFlightFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResetNotFinishFlightFile requires 2 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := collect_server.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.ResetNotFinishFlightFile(value0, value1))
		fmt.Print("\n")
		break
	case "addFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddFile requires 2 args")
			flag.Usage()
		}
		arg109 := flag.Arg(1)
		mbTrans110 := thrift.NewTMemoryBufferLen(len(arg109))
		defer mbTrans110.Close()
		_, err111 := mbTrans110.WriteString(arg109)
		if err111 != nil {
			Usage()
			return
		}
		factory112 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt113 := factory112.GetProtocol(mbTrans110)
		argvalue0 := collect_server.NewRequestHeader()
		err114 := argvalue0.Read(jsProt113)
		if err114 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg115 := flag.Arg(2)
		mbTrans116 := thrift.NewTMemoryBufferLen(len(arg115))
		defer mbTrans116.Close()
		_, err117 := mbTrans116.WriteString(arg115)
		if err117 != nil {
			Usage()
			return
		}
		factory118 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt119 := factory118.GetProtocol(mbTrans116)
		argvalue1 := collect_server.NewTopicInfo()
		err120 := argvalue1.Read(jsProt119)
		if err120 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddFile(value0, value1))
		fmt.Print("\n")
		break
	case "getFileFinishedBlockIndex":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileFinishedBlockIndex requires 3 args")
			flag.Usage()
		}
		arg121 := flag.Arg(1)
		mbTrans122 := thrift.NewTMemoryBufferLen(len(arg121))
		defer mbTrans122.Close()
		_, err123 := mbTrans122.WriteString(arg121)
		if err123 != nil {
			Usage()
			return
		}
		factory124 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt125 := factory124.GetProtocol(mbTrans122)
		argvalue0 := collect_server.NewRequestHeader()
		err126 := argvalue0.Read(jsProt125)
		if err126 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg127 := flag.Arg(2)
		mbTrans128 := thrift.NewTMemoryBufferLen(len(arg127))
		defer mbTrans128.Close()
		_, err129 := mbTrans128.WriteString(arg127)
		if err129 != nil {
			Usage()
			return
		}
		factory130 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt131 := factory130.GetProtocol(mbTrans128)
		argvalue1 := collect_server.NewTopicInfo()
		err132 := argvalue1.Read(jsProt131)
		if err132 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg133 := flag.Arg(3)
		mbTrans134 := thrift.NewTMemoryBufferLen(len(arg133))
		defer mbTrans134.Close()
		_, err135 := mbTrans134.WriteString(arg133)
		if err135 != nil {
			Usage()
			return
		}
		factory136 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt137 := factory136.GetProtocol(mbTrans134)
		containerStruct2 := collect_server.NewGetFileFinishedBlockIndexArgs()
		err138 := containerStruct2.ReadField3(jsProt137)
		if err138 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.NeedBlocks
		value2 := argvalue2
		fmt.Print(client.GetFileFinishedBlockIndex(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFileData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFileData requires 3 args")
			flag.Usage()
		}
		arg139 := flag.Arg(1)
		mbTrans140 := thrift.NewTMemoryBufferLen(len(arg139))
		defer mbTrans140.Close()
		_, err141 := mbTrans140.WriteString(arg139)
		if err141 != nil {
			Usage()
			return
		}
		factory142 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt143 := factory142.GetProtocol(mbTrans140)
		argvalue0 := collect_server.NewRequestHeader()
		err144 := argvalue0.Read(jsProt143)
		if err144 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg145 := flag.Arg(2)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		argvalue1 := collect_server.NewTopicInfo()
		err150 := argvalue1.Read(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err151 := (strconv.Atoi(flag.Arg(3)))
		if err151 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetFileData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
